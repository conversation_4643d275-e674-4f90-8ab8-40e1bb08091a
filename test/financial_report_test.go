package test

import (
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestFinancialReport(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	resp, err := repo.DefaultFinancialRepository.FinancialReport(ctx, &model.RepoCondition{
		PartnerId:             100,
		Start:                 ParseTime("2024-06-17"),
		End:                   ParseTime("2024-06-23"),
		TagType:               "DETAIL",
		RegionSearchType:      "STORE",
		RegionSearchIds:       []int64{4660705853316300800},
		RegionGroupType:       "STORE",
		PeriodGroupType:       "DAY",
		Limit:                 10,
		IncludeRealAmountZero: true,
		//StoreTags: []string{"1"},
	})
	if err != nil {
		t.<PERSON>rror(err)
	}
	bs, _ := json.Marshal(resp)
	t.Log(string(bs))
}
