package test

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
	"time"
)

func TestPosPromotion(t *testing.T) {
	InitTest()
	condition := &model.RepoCondition{}
	condition.PartnerId = 726
	// 增加数据校验
	condition.RegionSearchType = "STORE"
	condition.RegionSearchIds = []int64{4845219859958693888}
	condition.StoreIDs = condition.RegionSearchIds
	condition.Start, _ = time.Parse(config.DateFormat, "2024-06-06")
	condition.End = condition.Start
	resp, err := repo.DefaultSalesRepository.PosPromotion(context.Background(), condition)
	if err != nil {
		t.Error(err)
	}
	t.Log(resp)
	modelResp := resp
	if err != nil {
		t.Error(err)
	}
	marshal, err := json.Marshal(modelResp)
	var grpcResp = &sales_report.PosPromotionResponse{}
	err = json.Unmarshal(marshal, grpcResp)
	if err != nil {
		logger.Pre().Error("grpc结构体解析失败：", err)
	}
	fmt.Printf("%v", grpcResp)
}
