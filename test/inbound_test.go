package test

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestInboundReport(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	cr := &model.CommonRequest{
		Start:            ParseTime("2024-05-22"),
		End:              ParseTime("2024-05-27"),
		RegionSearchType: "STORE",
		RegionGroupType:  "STORE",
		ProductIds:       []string{"4700252215825203200"},
		RegionSearchIds:  []string{"4558877826094235648"},
		PeriodGroupType:  "DAY",
		Limit:            10,
	}
	rc := cr.ToRepoCondition([]string{})
	rc.PartnerId = 1016
	resp, err := repo.DefaultInboundQueryDB.Inbound(ctx, rc)
	if err != nil {
		t.Error(err)
	}
	bs, _ := json.Marshal(resp)
	fmt.Println(string(bs))
}

func TestPosInboundReport(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	cr := &model.CommonRequest{
		Start:            ParseTime("2024-05-22"),
		End:              ParseTime("2024-05-27"),
		RegionSearchType: "STORE",
		RegionGroupType:  "STORE",
		ProductIds:       []string{"4700252215825203200"},
		RegionSearchIds:  []string{"4558877826094235648"},
		PeriodGroupType:  "DAY",
		Search: 		  "香辣大1肠堡",
		Limit:            10,
	}
	rc := cr.ToRepoCondition([]string{})
	rc.PartnerId = 1016
	rc.StoreIDs = []int64{cast.ToInt64(cr.RegionSearchIds[0])}
	resp, err := repo.DefaultInboundQueryDB.PosInbound(ctx, rc)
	if err != nil {
		t.Error(err)
	}
	bs, _ := json.Marshal(resp)
	fmt.Println(string(bs))
}
