package test

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/db"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"time"
)

func InitTest() {
	logger.InitLogger("debug", "dev")
	config.DefaultConfig.DB.Pg = config.DBConfig {
		Url:         "postgresql://omni_pos_test:<EMAIL>:1921/saas_qa_pos_sales_report",
		MaxOpenConn: 1,
		MaxIdleConn: 1,
	}
	uuid.InitDefaultRollBackIdGenerator(&uuid.RollBackOption{
		RemoteAddr: "localhost:6171",
	})
	db.Init()
}

func CreateTestContext() context.Context {
	ctx := context.Background()
	ctx = context.WithValue(ctx, oauth.PartnerID, uint64(1016))
	ctx = context.WithValue(ctx, oauth.UserID, uint64(1))
	return ctx
}

// 2024-03-26
func ParseTime(t string) time.Time {
	ti, err := time.Parse("2006-01-02", t)
	if err != nil {
		panic(err)
	}
	return ti
}
