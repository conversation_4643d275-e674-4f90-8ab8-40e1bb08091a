package test

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestUpdateStoreOpenDate(t *testing.T) {
	InitTest()
	ctx := context.Background()
	partnerIds := []int64{1016}
	g, err := queryStoreOpenDate(ctx, partnerIds)
	if err != nil {
		logger.Pre().Error("查询门店开业时间数据出错:", err)
	}

	if err := repo.DefaultMetadataCache.UpdateStoreOpenDate(ctx, g); err != nil {
		logger.Pre().Error("更新门店开业时间数据出错:", err)
	}
}

func queryStoreOpenDate(ctx context.Context, partnerIds []int64) (map[string][]int64, error) {
	// 查询租户下没有开业时间的门店
	resp, err := repo.DefaultSalesRepository.StoreOpenDate(ctx, partnerIds)
	if err != nil {
		return nil, err
	}
	if len(resp.Rows) == 0 {
		return nil, nil
	}
	g := make(map[string][]int64)
	for _, row := range resp.Rows {
		if _, ok := g[row.OpenDate]; !ok {
			g[row.OpenDate] = make([]int64, 0)
		}
		g[row.OpenDate] = append(g[row.OpenDate], row.StoreId)
	}
	return g, nil
}
