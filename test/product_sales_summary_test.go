package test

import (
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestQueryProductSummary(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	cr := &model.CommonRequest{
		Start: ParseTime("2024-05-22"),
		End: ParseTime("2024-05-22"),
		RegionSearchType: "STORE",
		RegionGroupType: "STORE",
		RegionSearchIds: []string{"4881429946909622272"},
		PeriodGroupType: "DAY",
		Limit: 10,
	}
	rc := cr.ToRepoCondition([]string{})
	rc.PartnerId = 1372
	config.SpicyCode = "202403253"
	resp, err := repo.DefaultSalesRepository.ProductSalesComboSummary(ctx, rc)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}
