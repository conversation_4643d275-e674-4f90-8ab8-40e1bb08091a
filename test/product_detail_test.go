package test

import (
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestAAA(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	cr := &model.CommonRequest{
		Start: ParseTime("2024-05-24"),
		End: ParseTime("2024-05-24"),
		RegionSearchType: "STORE",
		RegionGroupType: "STORE",
		//RegionSearchIds: []string{"4881429946909622272"},
		PeriodGroupType: "DAY",
		IncludeRealAmountZero: true,
		Limit: 10,
	}
	rc := cr.ToRepoCondition([]string{})
	rc.PartnerId = 726
	config.SpicyCode = "01"
	resp, err := repo.DefaultSalesRepository.ProductSalesDetail(ctx, rc)
	if err != nil {
		t.Error(err)
	}
	t.Log(resp)
	//report.PackagingForProductSalesSummary(ctx, cr, resp)
}
