package test

import (
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"testing"
)

func TestQueryStorePerformanceAnalysis(t *testing.T) {
	InitTest()
	ctx := CreateTestContext()
	resp, err := repo.DefaultSalesRepository.StorePerformanceAnalysis(ctx, &model.RepoCondition{
		PartnerId: 726,
		Start: ParseTime("2024-03-26"),
		End: ParseTime("2024-05-26"),
		RegionSearchType: "STORE",
		RegionGroupType: "STORE",
		PeriodGroupType: "DAY",
		Limit: 10,
	})
	if err != nil {
		t.Error(err)
	}
	bs, _ := json.Marshal(resp)
	t.Log(string(bs))
}
