package omnichannel

type ProductListRequest struct {
	StoreId string `json:"storeId"`
	Channel string `json:"channel"`
}

type ProductListResponse struct {
	StatusCode int       `json:"status_code"`
	Payload    []Payload `json:"payload"`
	Success    bool      `json:"success"`
	Message    string    `json:"message"`
}

type Payload struct {
	MenuId     string      `json:"menuId"`
	Name       string      `json:"name"`
	Category   []*Category `json:"categoryList"`
	UpdateTime string      `json:"updateTime"`
	Version    int         `json:"version"`
}

type Category struct {
	Created    interface{} `json:"created"`
	CreatedBy  string      `json:"created_by"`
	Updated    interface{} `json:"updated"`
	UpdatedBy  string      `json:"updated_by"`
	CategoryId string      `json:"categoryId" storm:"id"`
	//MenuCategoryId string      `json:"menuCategoryId"`
	CategoryName string    `json:"categoryName"`
	Sort         string    `json:"sort"`
	ProductList  []Product `json:"productList"`
}

type Product struct {
	Created                    string      `json:"created"`
	CreatedBy                  string      `json:"created_by"`
	Updated                    string      `json:"updated"`
	UpdatedBy                  string      `json:"updated_by"`
	Name                       string      `json:"name"`
	MnemonicCode               []string    `json:"mnemonic_code"`
	DealId                     []string    `json:"deal_id"`
	FirstPinyin                []string    `json:"first_pinyin"`
	ProductCategory            []string    `json:"product_category"`
	ProductId                  string      `json:"product_id"`
	Upc                        []string    `json:"upc"`
	SellingPrice               interface{} `json:"selling_price"`
	OriginalPrice              interface{} `json:"original_price"`
	Code                       string      `json:"code"`
	PriceType                  interface{} `json:"price_type"`
	SaleType                   string      `json:"sale_type"`
	Unit                       interface{} `json:"unit"`
	IsJoinQueue                bool        `json:"is_join_queue,omitempty"`
	Relation                   *Relation   `json:"relation"`
	ChannelAvailablePeriod     string      `json:"channel_available_period"`
	ChannelAvailablePeriodType string      `json:"channel_available_period_type"`
	ChannelDescription         string      `json:"channel_description"`
	AppChannelDescription      string      `json:"app_channel_description"`
	ChannelRemark              string      `json:"channel_remark"`
	Picture                    string      `json:"picture"`
	SetType                    interface{} `json:"set_type"`
	ExtCode                    string      `json:"extCode,omitempty"`
	Status                     string      `json:"status"`
	ChannelTag                 interface{} `json:"channel_tag"`
	ChannelPackageFixedFee     float64     `json:"channel_package_fixed_fee"`
	ChannelPicture             interface{} `json:"channel_picture"`
	ChannelSimpleImage         interface{} `json:"channel_simple_image"`
	ChannelHasCupLabelNotice   bool        `json:"channel_has_cup_label_notice"`
	ChannelCupLabelNotice      string      `json:"channel_cup_label_notice"`
	MakeTime                   string      `json:"make_time"`
	ChannelNoCupCounted        bool        `json:"channel_no_cup_counted"`
	SellSpecification          *struct {
		Data []SellSpecification `json:"data"`
	} `json:"sell_specification"`
	IsAdditionalAttribute bool        `json:"is_additional_attribute"`
	IsAddition            bool        `json:"is_addition"`
	ProductShelfStatus    bool        `json:"productShelfStatus"`
	WeightProduct         bool        `json:"weightProduct"`
	Stock                 interface{} `json:"stock"`
	StockOperate          bool        `json:"stockOperate"`
	ProductPrintName      string      `json:"product_print_name"`
	Sort                  string      `json:"sort"`
	ServingSize           string      `json:"serving_size,omitempty"`
	IsCupNotice           bool        `json:"is_cup_notice,omitempty"`
}

type SellSpecification struct {
	Id       string                   `json:"id"`
	Code     string                   `json:"code"`
	AttrName string                   `json:"attrName"`
	Remark   string                   `json:"remark"`
	Values   []SellSpecificationValue `json:"values"`
}

type SellSpecificationValue struct {
	Code    string `json:"code"`
	Label   string `json:"label"`
	Value   string `json:"value"`
	Checked bool   `json:"checked"`
}
type Sku struct {
	Id string `json:"id"`
	//ProductId       string      `json:"product_id"`
	ShelfStatus     bool          `json:"shelfStatus"`
	Code            string        `json:"code"`
	SellingPrice    float64       `json:"selling_price"`
	OriginalPrice   *float64      `json:"original_price"`
	Currency        string        `json:"currency"`
	Status          interface{}   `json:"status"`
	Upc             []string      `json:"upc"`
	AttrSingleIndex string        `json:"attr_single_index"`
	AttrNames       interface{}   `json:"attr_names"`
	AttrValues      interface{}   `json:"attr_values"`
	Stock           int           `json:"stock"`
	StockOperate    bool          `json:"stockOperate"`
	SkuMutexAttr    []interface{} `json:"sku_mutex_attr"`
	SaleName        string        `json:"sale_name"`
}

type Addition struct {
	Name       string  `json:"name"`
	Code       string  `json:"code"`
	Id         string  `json:"id"`
	RaisePrice float64 `json:"raise_price"`
	Currency   string  `json:"currency"`
	// 一下的数据应该是废弃的
	GroupCode              string `json:"groupCode,omitempty"`
	GroupName              string `json:"groupName,omitempty"`
	SplitCharging          bool   `json:"splitCharging,omitempty"`
	GroupMaxCount          int    `json:"groupMaxCount,omitempty"`
	GroupMinCount          int    `json:"groupMinCount,omitempty"`
	NotRepeatSelectInGroup bool   `json:"notRepeatSelectInGroup"`
	FeedLabel              string `json:"feedLabel"`
	Picture                string `json:"picture"`
}

type Set struct {
	Name                     string   `json:"name"`
	Code                     string   `json:"code"`
	Price                    float64  `json:"price"`
	Currency                 string   `json:"currency"`
	KindName                 string   `json:"kind_name"`
	Mandatory                bool     `json:"mandatory"`
	ProductCount             int      `json:"product_count"`
	CountLimit               int      `json:"count_limit"`
	MinCountLimit            int      `json:"min_count_limit"`
	LabelName                string   `json:"label_name"`
	ProductId                string   `json:"product_id"`
	SubItemSpu               *Product `json:"subItemSpu"`
	ChannelRemark            string   `json:"channel_remark"`
	ChannelHasCupLabelNotice bool     `json:"channel_has_cup_label_notice"`
	ChannelCupLabelNotice    string   `json:"channel_cup_label_notice"`
	ChannelNoCupCounted      bool     `json:"channel_no_cup_counted"`
	ProductPrintName         string   `json:"product_print_name"`
	RaisePrice               float64  `json:"raisePrice"`
	DefaultFreeFeed          string   `json:"default_free_feed"`
	OptionalFeed             string   `json:"optional_feed"`
	MaxOptionalCount         int      `json:"max_optional_count"`
	MinOptionalCount         int      `json:"min_optional_count"`
	NotRepeatSelectInGroup   bool     `json:"not_repeat_select_in_group"`
}

type AttributeValues struct {
	Code                   string  `json:"code"`                   //编码
	DefaultSelectionStatus bool    `json:"defaultSelectionStatus"` //是否默认选择
	EnableStatus           bool    `json:"enableStatus"`           //开启状态
	ExtCode                string  `json:"extCode"`                //外卖编码(字母加数字4位字符不可重复)
	Id                     string  `json:"id"`
	Name                   string  `json:"name"`          //  属性值
	Price                  float64 `json:"price"`         //属性价格
	PrintName              string  `json:"printName"`     //打印值名称
	PrintOrder             int     `json:"printOrder"`    //打印显示值顺序
	ReadyStatus            bool    `json:"readyStatus"`   //是否是现制品
	SaleStatus             bool    `json:"saleStatus"`    //
	SelfHelpOrder          int     `json:"selfHelpOrder"` //自取显示值顺序
	ShowName               string  `json:"showName"`      //显示值名称
	TakeoutOrder           int     `json:"takeoutOrder"`  //外卖显示值顺序
	Stock                  *int    `json:"stock"`
	StockOperate           bool    `json:"stockOperate"`
	ShelfStatus            bool    `json:"shelfStatus"` //上下架状态
}

type AttributeLabels struct {
	Id         string `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Type       string `json:"type"`
	Color      string `json:"color"`
	BgColor    string `json:"bgColor"`
	StartTime  string `json:"startTime"`
	EndTime    string `json:"endTime"`
	OpenStatus string `json:"openStatus"`
}

type AdditionalAttribute struct {
	ShelfStatus        bool              `json:"shelfStatus"`      //上下架状态
	AttributeName      string            `json:"attribute_name"`   //  附加属性名称
	Code               string            `json:"code"`             //属性CODE
	Id                 string            `json:"id"`               //  属性ID
	PrintName          string            `json:"printName"`        //  打印名称
	Order              int               `json:"order"`            //排序
	SelectedMaxCount   int               `json:"selectedMaxCount"` //选择上限
	SelectedStatus     bool              `json:"selectedStatus"`   //是否必须
	SingleOrMult       bool              `json:"singleOrMult"`     //单选或者多选
	PrintOrder         int               `json:"printOrder"`       //属性组排序
	AttributeValues    []AttributeValues `json:"attribute_values"`
	AttributeCondition []interface{}     `json:"attribute_condition"`
	AttributeLabels    []AttributeLabels `json:"attributeLabels"`
	Remark             string            `json:"remark"`
}

type Relation struct {
	ProductLabel        []ProductLabel        `json:"productLabel,omitempty"`
	ProductCategory     string                `json:"product_category"`
	Sku                 []Sku                 `json:"sku"`
	Addition            []Addition            `json:"addition"`
	Set                 []Set                 `json:"set"`
	Unit                string                `json:"unit"`
	AdditionalAttribute []AdditionalAttribute `json:"additional_attribute"`
}

type ProductLabel struct {
	Id         string `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Color      string `json:"color"`
	BgColor    string `json:"bgColor"`
	OpenStatus string `json:"openStatus"`
}
