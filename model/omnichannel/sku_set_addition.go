package omnichannel

// SetSkuAddition Set/Sku/Addition的公共字段
type SetSkuAddition struct {
	Id              string   `json:"id"`
	Upc             []string `json:"upc"`
	SellingPrice    float64  `json:"selling_price"`
	Stock           int      `json:"stock"`
	AttrSingleIndex string   `json:"attr_single_index"`
	// addition
	RaisePrice float64 `json:"raise_price"`
	// 一下是Set的定义
	Name                     string  `json:"name"`
	Code                     string  `json:"code"`
	Price                    float64 `json:"price"`
	Currency                 string  `json:"currency"`
	KindName                 string  `json:"kind_name"`
	Mandatory                bool    `json:"mandatory"`
	ProductCount             int     `json:"product_count"`
	CountLimit               int     `json:"count_limit"`
	ProductId                string  `json:"product_id"`
	ChannelRemark            string  `json:"channel_remark"`
	ChannelHasCupLabelNotice bool    `json:"channel_has_cup_label_notice"`
	ChannelCupLabelNotice    string  `json:"channel_cup_label_notice"`
	ChannelNoCupCounted      bool    `json:"channel_no_cup_counted"`
	ProductPrintName         string  `json:"product_print_name"`
}

func FormSet(set Set) SetSkuAddition {
	return SetSkuAddition{
		Name:                     set.Name,
		Code:                     set.Code,
		Price:                    set.Price,
		Currency:                 set.Currency,
		KindName:                 set.KindName,
		Mandatory:                set.Mandatory,
		ProductCount:             set.ProductCount,
		CountLimit:               set.CountLimit,
		ProductId:                set.ProductId,
		ChannelRemark:            set.ChannelRemark,
		ChannelHasCupLabelNotice: set.ChannelHasCupLabelNotice,
		ChannelCupLabelNotice:    set.ChannelCupLabelNotice,
		ChannelNoCupCounted:      set.ChannelNoCupCounted,
		ProductPrintName:         set.ProductPrintName,
	}
}

func FromSKu(set Sku) SetSkuAddition {
	return SetSkuAddition{
		AttrSingleIndex: set.AttrSingleIndex,
		Stock:           set.Stock,
		SellingPrice:    set.SellingPrice,
		Upc:             set.Upc,
		Id:              set.Id,
		Code:            set.Code,
		Currency:        set.Currency,
	}
}

func FormAddition(set Addition) SetSkuAddition {
	return SetSkuAddition{
		Id:         set.Id,
		Name:       set.Name,
		Code:       set.Code,
		RaisePrice: set.RaisePrice,
		Currency:   set.Currency,
	}
}
