package model

import (
	"github.com/shopspring/decimal"
	"math"
)

// 分摊
func allocToProduct(skuList []*TicketProduct, total float64, setValueFunc func(sku *TicketProduct, value decimal.Decimal, isAdd bool)) {
	if total == 0 {
		return
	}
	totalDecimal := decimal.NewFromFloat(0)
	for _, product := range skuList {
		totalDecimal = totalDecimal.Add(decimal.NewFromFloat(product.Amount))
	}

	if totalDecimal.Equals(decimal.NewFromFloat(0)) {
		return
	}

	notLastSkuDiscountAmount := decimal.NewFromFloat(total)
	maxIndex := -1
	maxAmount := float64(0)
	for index, product := range skuList {
		rate := decimal.NewFromFloat(0)
		if len(skuList) == 1 {
			rate = decimal.NewFromFloat(1)
		} else {
			rate = decimal.NewFromFloat(product.Amount).Div(totalDecimal)
		}

		rateDecimal := decimal.NewFromFloat(total).Mul(rate).Round(2)

		// 设置商品的金额字段
		setValueFunc(product, rateDecimal, false)

		// 剩余的未分配的
		notLastSkuDiscountAmount = notLastSkuDiscountAmount.Sub(rateDecimal).Round(2)

		if index == 0 || math.Abs(product.Amount) > math.Abs(maxAmount) {
			maxIndex = index
			maxAmount = product.Amount
		}
	}

	if !notLastSkuDiscountAmount.Equals(decimal.NewFromFloat(0)) {
		setValueFunc(skuList[maxIndex], notLastSkuDiscountAmount, true)
	}
}
