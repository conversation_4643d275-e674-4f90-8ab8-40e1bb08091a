package model

import (
	"github.com/lib/pq"
	"github.com/shopspring/decimal"
	"time"
)

// 排序字段
type Resolve interface {
	SortFields() []string
}

// 门店报表模型
type SalesTicketAmount struct {
	Id                         int64        `gorm:"primaryKey;not null" json:"id,omitempty"`                                // ID
	PartnerId                  int64        `gorm:"not null" json:"partner_id,omitempty"`                                   // 租户ID
	ScopeId                    int64        `gorm:"not null" json:"scope_id,omitempty"`                                     // 分区ID
	BusDate                    time.Time    `gorm:"type:date;not null" json:"bus_date,omitempty"`                           // 营业日期
	BusDateWeek                time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-周
	BusDateMonth               time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-月
	BusDateYear                time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-年
	StoreId                    int64        `gorm:"not null" json:"store_id,omitempty"`                                     // 门店ID
	EticketId                  int64        `gorm:"unique;not null" json:"eticket_id,omitempty"`                            // 电子小票ID
	ChannelId                  int64        `gorm:"not null" json:"channel_id,omitempty"`                                   // 渠道ID
	ChannelName                string       `gorm:"type:varchar(50);not null" json:"channel_name,omitempty"`                // 渠道名称
	OrderType                  string       `gorm:"type:varchar(15);not null" json:"order_type,omitempty"`                  // 订单类型
	OrderTypeName              string       `gorm:"type:varchar(15);not null" json:"order_type_name,omitempty"`             // 订单类型名称
	OrderTime                  time.Time    `gorm:"type:timestamp;not null" json:"order_time,omitempty"`                    // 订单时间
	Refunded                   bool         `gorm:"not null" json:"refunded,omitempty"`                                     // 是否退款
	GrossAmount                float64      `gorm:"type:numeric(38,16);not null" json:"gross_amount,omitempty"`             // 交易毛额
	NetAmount                  float64      `gorm:"type:numeric(38,16);not null" json:"net_amount,omitempty"`               // 交易净额
	DiscountAmount             float64      `gorm:"type:numeric(38,16);not null" json:"discount_amount,omitempty"`          // 折扣金额
	Tip                        float64      `gorm:"type:numeric(38,16);not null" json:"tip,omitempty"`                      // 小费
	PackageFee                 float64      `gorm:"type:numeric(38,16);not null" json:"package_fee,omitempty"`              // 打包费
	DeliveryFee                float64      `gorm:"type:numeric(38,16);not null" json:"delivery_fee,omitempty"`             // 配送费
	ServiceFee                 float64      `gorm:"type:numeric(38,16);not null" json:"service_fee,omitempty"`              // 服务费
	TaxFee                     float64      `gorm:"type:numeric(38,16);not null" json:"tax_fee,omitempty"`                  // 税额
	OtherFee                   float64      `gorm:"type:numeric(38,16);not null" json:"other_fee,omitempty"`                // 其他费用
	PayAmount                  float64      `gorm:"type:numeric(38,16);not null" json:"pay_amount,omitempty"`               // 实付金额
	Rounding                   float64      `gorm:"type:numeric(38,16);not null" json:"rounding,omitempty"`                 // 抹零
	OverflowAmount             float64      `gorm:"type:numeric(38,16);not null" json:"overflow_amount,omitempty"`          // 溢收
	ChangeAmount               float64      `gorm:"type:numeric(38,16);not null" json:"change_amount,omitempty"`            // 找零
	ProductCount               int          `gorm:"not null" json:"product_count,omitempty"`                                // 商品数量
	AccessoryCount             int          `gorm:"not null" json:"accessory_count,omitempty"`                              // 加料数量
	EticketCount               int          `gorm:"not null" json:"eticket_count,omitempty"`                                // 单数
	Created                    time.Time    `gorm:"not null" json:"created,omitempty"`                                      // 创建时间
	Commission                 float64      `gorm:"type:numeric(38,16);not null" json:"commission,omitempty"`               // 佣金
	PaymentTransferAmount      float64      `gorm:"type:numeric(38,16)" json:"payment_transfer_amount,omitempty"`           // 支付转折扣
	DiscountTransferAmount     float64      `gorm:"type:numeric(38,16)" json:"discount_transfer_amount,omitempty"`          // 折扣转支付
	RefundCode                 string       `gorm:"type:varchar(15)" json:"refund_code,omitempty"`                          // 退单原因code
	RefundSide                 string       `gorm:"type:varchar(15)" json:"refund_side,omitempty"`                          // 退单方
	RefundReason               string       `gorm:"type:varchar(255)" json:"refund_reason,omitempty"`                       // 退单原因
	Amount0                    float64      `gorm:"column:amount_0;type:numeric(38,16);not null" json:"amount_0,omitempty"` // 营业额
	Amount1                    float64      `gorm:"column:amount_1;type:numeric(38,16);not null" json:"amount_1,omitempty"` // 支出
	Amount2                    float64      `gorm:"column:amount_2;type:numeric(38,16);not null" json:"amount_2,omitempty"` // 实收金额(转换后)
	Amount3                    float64      `gorm:"column:amount_3;type:numeric(38,16);not null" json:"amount_3,omitempty"` // 实收金额
	Amount4                    float64      `gorm:"column:amount_4;type:numeric(38,16);not null" json:"amount_4,omitempty"` // 应付金额
	MerchantDiscountAmount     float64      `gorm:"type:numeric(38,16);not null" json:"merchant_discount_amount,omitempty"` // 商家优惠承担
	PlatformDiscountAmount     float64      `gorm:"type:numeric(38,16);not null" json:"platform_discount_amount,omitempty"` // 平台优惠承担
	OrderStatus                TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"`                // 正单，退单，部分退单
	Member                     bool         `gorm:"not null" json:"member"`                                                 // 是否会员
	MerchantSendFee            float64      `gorm:"type:numeric(38,16)" json:"merchant_send_fee,omitempty"`                 // 商家承担配送费
	StoreDiscountAmount        float64      `gorm:"type:numeric(38,16)" json:"store_discount_amount,omitempty"`             // 门店折扣金额
	DiscountMerchantContribute float64      `gorm:"type:numeric(38,16)" json:"discount_merchant_contribute,omitempty"`      // 活动商家出资
	DiscountPlatformContribute float64      `gorm:"type:numeric(38,16)" json:"discount_platform_contribute,omitempty"`      // 活动平台出资
	DiscountBuyerContribute    float64      `gorm:"type:numeric(38,16)" json:"discount_buyer_contribute,omitempty"`         // 活动用户出资
	DiscountOtherContribute    float64      `gorm:"type:numeric(38,16)" json:"discount_other_contribute,omitempty"`         // 活动第三方出资
	PayMerchantContribute      float64      `gorm:"type:numeric(38,16)" json:"pay_merchant_contribute,omitempty"`           // 实付商家出资
	PayPlatformContribute      float64      `gorm:"type:numeric(38,16)" json:"pay_platform_contribute,omitempty"`           // 实付平台出资
	PayBuyerContribute         float64      `gorm:"type:numeric(38,16)" json:"pay_buyer_contribute,omitempty"`              // 实付用户出资
	PayOtherContribute         float64      `gorm:"type:numeric(38,16)" json:"pay_other_contribute,omitempty"`              // 实付第三方出资
	SendFee                    float64      `gorm:"type:numeric(38,16)" json:"send_fee,omitempty"`                          // 用户实际配送费
	SendFeeForPlatform         float64      `gorm:"type:numeric(38,16)" json:"send_fee_for_platform,omitempty"`             // 用户支付给平台配送费
	SendFeeForMerchant         float64      `gorm:"type:numeric(38,16)" json:"send_fee_for_merchant,omitempty"`             // 用户支付给商家配送费
	PlatformSendFee            float64      `gorm:"type:numeric(38,16)" json:"platform_send_fee,omitempty"`                 // 平台承担配送费
	SurchargeAmount            float64      `gorm:"type:numeric(38,16)" json:"surcharge_amount,omitempty"`                  // 附加费

	DeliveryFeeForMerchant float64 `gorm:"type:numeric(38,16)" json:"delivery_fee_for_merchant,omitempty"` // 累计付给商家配送费（自配送场景）

	NaturalDate    time.Time `gorm:"type:date;not null" json:"natural_date"`      // 自然日
	SendFeeRate    float64   `gorm:"type:numeric(38,16)" json:"send_fee_rate"`    // 配送费税率
	PackageFeeRate float64   `gorm:"type:numeric(38,16)" json:"package_fee_rate"` // 包装费税率

	MealSegmentName   string         `gorm:"type:varchar(255)" json:"meal_segment_name,omitempty"`   // 餐段
	People            float64        `gorm:"type:numeric(38,16)" json:"people,omitempty"`            // 用餐人数
	StoreTags         pq.StringArray `gorm:"type:json" json:"store_tags,omitempty"`                  // 门店标签
	StoreTagNames     pq.StringArray `gorm:"type:json" json:"store_tag_names,omitempty"`             // 门店标签
	CouponChannelCode string         `gorm:"type:varchar(255)" json:"coupon_channel_code,omitempty"` // 卡券渠道
	CouponChannelName string         `gorm:"type:varchar(255)" json:"coupon_channel_name,omitempty"` // 卡券渠道
	ZoneName          string         `gorm:"type:varchar(255)" json:"zone_name,omitempty"`           // 区域名称
	TableNo           string         `gorm:"type:varchar(255)" json:"table_no,omitempty"`            // 桌号
	OpenTableBy       string         `gorm:"type:varchar(255)" json:"open_table_by,omitempty"`       // 点餐人
	OperatorName      string         `gorm:"type:varchar(255)" json:"operator_name,omitempty"`       // 收银员
	OperatorCode      string         `gorm:"type:varchar(255)" json:"operator_code,omitempty"`       // 收银员编号
	OpenTableAt       time.Time      `gorm:"type:timestamp" json:"open_table_at,omitempty"`          // 开台时间
	EndTime           time.Time      `gorm:"type:timestamp" json:"end_time,omitempty"`               // 结账时间
	TicketNo          string         `gorm:"type:varchar(255)" json:"ticket_no,omitempty"`           // 交易号，根据业务规则生成
	BowlNum           float64        `json:"bowl_num,omitempty"`                                     // 碗数
	ConsumptionTime   float64        `json:"consumption_time"`                                       // 消费时长
	RealTicketNo      string         `json:"real_ticket_no,omitempty"`                               // 原始电子小票中的取餐号ticket_no,元字段已经被占用
	IsNonSales        bool           `json:"is_non_sales,omitempty"`                                 // 非销售
	PosDeviceCode     string         `gorm:"type:varchar(255)" json:"pos_device_code,omitempty"`     // pos机器编码
	//ref_ticket_no
	RefTicketNo string `gorm:"type:varchar(255)" json:"ref_ticket_no"` // 原单交易号
	PlateNo     string `json:"plate_no,omitempty"`                     // 飞盘号（外送号码）
	PhoneNo     string `json:"phone_no"`                               // 帐单号码

}

func (rta *SalesTicketAmount) SortFields() []string {
	return []string{"BUS_DATE"}
}

// 商品销售模型
type SalesProductAmount struct {
	Id             int64        `gorm:"not null"`                                                // ID
	PartnerId      int64        `gorm:"not null"`                                                // 租户ID
	ScopeId        int64        `gorm:"not null"`                                                // 分区ID
	BusDate        time.Time    `gorm:"type:date;not null" json:"bus_date,omitempty"`            // 营业日期
	BusDateWeek    time.Time    `gorm:"type:date;not null" json:"-"`                             // 营业日期-周
	BusDateMonth   time.Time    `gorm:"type:date;not null" json:"-"`                             // 营业日期-月
	BusDateYear    time.Time    `gorm:"type:date;not null" json:"-"`                             // 营业日期-年
	StoreId        int64        `gorm:"not null"`                                                // 门店ID
	EticketId      int64        `gorm:"not null"`                                                // 电子小票ID
	ChannelId      int64        `gorm:"not null"`                                                // 渠道ID
	ChannelName    string       `gorm:"type:varchar(50);not null"`                               // 渠道名称
	OrderType      string       `gorm:"type:varchar(15);not null"`                               // 订单类型
	OrderTypeName  string       `gorm:"type:varchar(15);not null"`                               // 订单类型名称
	OrderTime      time.Time    `gorm:"type:timestamp;not null"`                                 // 订单时间
	Refunded       bool         `gorm:"not null"`                                                // 是否退款
	ProductId      int64        `gorm:"not null"`                                                // 商品ID
	ProductCode    string       `gorm:"type:varchar(50);not null"`                               // 商品编码
	ProductName    string       `gorm:"type:varchar(55);not null"`                               // 商品名称
	SaleType       string       `gorm:"type:varchar(20);not null"`                               // 商品类型
	IsAccessory    bool         `gorm:"not null"`                                                // 是否加料商品
	CategoryId     int64        `gorm:"not null"`                                                // 商品分类ID
	Qty            int          `gorm:"not null"`                                                // 数量
	GrossAmount    float64      `gorm:"type:numeric(38,16);not null"`                            // 交易毛额
	NetAmount      float64      `gorm:"type:numeric(38,16);not null"`                            // 交易净额
	DiscountAmount float64      `gorm:"type:numeric(38,16);not null"`                            // 折扣金额
	Created        time.Time    `gorm:"not null"`                                                // 创建时间
	EticketCount   int          `gorm:"not null"`                                                // 使用该商品的订单数
	TaxFee         float64      `gorm:"type:numeric(38,16);not null"`                            // products里面的taxAmount
	OrderStatus    TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"` // 正单，退单，部分退单
	Price          float64      `gorm:"type:numeric(38,16);not null" json:"price,omitempty"`     // 商品单价
	PromotionName  string       `json:"promotion_name,omitempty"`                                // 促销名称
	PromotionCode  string       `json:"promotion_code,omitempty"`                                // 促销code
	StartTime      time.Time    `gorm:"type:timestamp;not null"`                                 // 下单时间
	IsCombo        bool         `gorm:"not null"`                                                // 是否套餐
	ComboType      int          `gorm:"not null" json:"combo_type"`                              // 商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品
	Accessories    string       `gorm:"type:varchar(255);not null" json:"accessories"`           // 加料
	SkuRemark      string       `gorm:"type:varchar(255);not null" json:"sku_remark"`            // 商品sku属性键值对的集合
	Flavor         string       `gorm:"type:varchar(255);not null" json:"flavor"`                // 口味分布：由所有属性值和所有加料组成

	Weight    float64 `gorm:"type:numeric(38,16);not null"` // 重量/份量
	HasWeight bool    `gorm:"not null"`                     // 是否是称重商品
	Unit      string  `gorm:"type:varchar(20)" json:"unit"` // 单位

	// 增加新字段
	TransferAmount         float64        `gorm:"type:numeric(38,16);not null" json:"transfer_amount"`          // 支付转折扣
	InvoiceAmount          float64        `gorm:"type:numeric(38,16);not null" json:"invoice_amount"`           // 开票金额
	NaturalDate            time.Time      `gorm:"type:date;not null" json:"natural_date"`                       // 自然日
	ProductTax             float64        `gorm:"type:numeric(38,16);not null" json:"product_tax"`              // 商品税率
	FinanceRealAmount      float64        `gorm:"type:numeric(38,16);not null" json:"finance_real_amount"`      // 财务实收
	DiscountTransferAmount float64        `gorm:"type:numeric(38,16);not null" json:"discount_transfer_amount"` // 折扣转支付
	CreateTime             time.Time      `gorm:"type:timestamp;not null" json:"create_time"`                   // 创建时间
	BowlNum                int32          `json:"bowl_num"`
	StoreTags              pq.StringArray `gorm:"type:json" json:"store_tags,omitempty"`                    // 门店标签
	StoreTagNames          pq.StringArray `gorm:"type:json" json:"store_tag_names,omitempty"`               // 门店标签
	MealSegmentName        string         `gorm:"type:varchar(255)" json:"meal_segment_name,omitempty"`     // 餐段
	CouponChannelCode      string         `gorm:"type:varchar(255)" json:"coupon_channel_code,omitempty"`   // 卡券渠道
	CouponChannelName      string         `gorm:"type:varchar(255)" json:"coupon_channel_name,omitempty"`   // 卡券渠道
	ZoneName               string         `gorm:"type:varchar(255)" json:"zone_name,omitempty"`             // 区域名称
	TableNo                string         `gorm:"type:varchar(255)" json:"table_no,omitempty"`              // 桌号
	OpenTableBy            string         `gorm:"type:varchar(255)" json:"open_table_by,omitempty"`         // 点餐人
	OperatorName           string         `gorm:"type:varchar(255)" json:"operator_name,omitempty"`         // 收银员
	OpenTableAt            time.Time      `gorm:"type:timestamp" json:"open_table_at,omitempty"`            // 开台时间
	EndTime                time.Time      `gorm:"type:timestamp" json:"end_time,omitempty"`                 // 结账时间
	TicketNo               string         `gorm:"type:varchar(255)" json:"ticket_no,omitempty"`             // 交易号，根据业务规则生成
	Remark                 string         `gorm:"type:varchar(255);not null" json:"remark"`                 // remark
	ComboProductId         int64          `gorm:"not null" json:"combo_product_id"`                         // 套餐商品ID
	ProductsRealAmount     float64        `gorm:"type:numeric(38,16);not null" json:"products_real_amount"` // 商品实收金额
	AccCount               float64        `gorm:"type:numeric(38,16);not null" json:"acc_count"`            // 加料数量
	ProductAccCount        float64        `gorm:"type:numeric(38,16);not null" json:"product_acc_count"`    // 加料数量

	MerchantDiscountAmount float64 `gorm:"type:numeric(38,16)" json:"merchant_discount_amount,omitempty"` // 商家折扣承担 - 分摊

	GrossAmount2               float64 `gorm:"type:numeric(38,16)" json:"gross_amount2"`
	PackageAmount              float64 `gorm:"type:numeric(38,16)" json:"package_amount,omitempty"`               // 分摊
	ServiceFee                 float64 `gorm:"type:numeric(38,16)" json:"service_fee,omitempty"`                  // 分摊
	Tip                        float64 `gorm:"type:numeric(38,16)" json:"tip,omitempty"`                          // 分摊
	OtherFee                   float64 `gorm:"type:numeric(38,16)" json:"other_fee,omitempty"`                    // 分摊
	Rounding                   float64 `gorm:"type:numeric(38,16)" json:"rounding,omitempty"`                     // 分摊
	OverflowAmount             float64 `gorm:"type:numeric(38,16)" json:"overflow_amount,omitempty"`              // 分摊
	Commission                 float64 `gorm:"type:numeric(38,16)" json:"commission,omitempty"`                   // 分摊
	MerchantSendFee            float64 `gorm:"type:numeric(38,16)" json:"merchant_send_fee,omitempty"`            // 分摊
	SendFeeForMerchant         float64 `gorm:"type:numeric(38,16)" json:"send_fee_for_merchant,omitempty"`        // 分摊
	SendFeeForPlatform         float64 `gorm:"type:numeric(38,16)" json:"send_fee_for_platform,omitempty"`        // 分摊
	PayCost                    float64 `gorm:"type:numeric(38,16)" json:"pay_cost,omitempty"`                     // 分摊
	PayTpAllowance             float64 `gorm:"type:numeric(38,16)" json:"pay_tp_allowance,omitempty"`             // 分摊
	PayMerchantAllowance       float64 `gorm:"type:numeric(38,16)" json:"pay_merchant_allowance,omitempty"`       // 分摊
	PayPlatformAllowance       float64 `gorm:"type:numeric(38,16)" json:"pay_platform_allowance,omitempty"`       // 分摊
	PromotionCost              float64 `gorm:"type:numeric(38,16)" json:"promotion_cost,omitempty"`               // 分摊
	PromotionTpAllowance       float64 `gorm:"type:numeric(38,16)" json:"promotion_tp_allowance,omitempty"`       // 分摊
	PromotionMerchantAllowance float64 `gorm:"type:numeric(38,16)" json:"promotion_merchant_allowance,omitempty"` // 分摊
	PromotionPlatformAllowance float64 `gorm:"type:numeric(38,16)" json:"promotion_platform_allowance,omitempty"` // 分摊
	IsNonSales                 bool    `json:"is_non_sales,omitempty"`                                            // 非销售
}

func (rpa *SalesProductAmount) SortFields() []string {
	return []string{"BUS_DATE"}
}

type SalesProductSkuRemark struct {
	Id         int64     `gorm:"primaryKey;not null" json:"id,omitempty"`       // ID
	EticketId  int64     `gorm:"not null" json:"eticket_id,omitempty"`          // 电子小票id
	BusDate    time.Time `gorm:"type:date;not null" json:"bus_date,omitempty"`  // 营业日期
	PartnerId  int64     `gorm:"not null" json:"partner_id,omitempty"`          // 租户ID
	StoreId    int64     `gorm:"not null" json:"store_id,omitempty"`            // 门店ID
	Pid        int64     `gorm:"not null" json:"pid,omitempty"`                 // sales_product_amounts.id
	ProductId  int64     `gorm:"not null" json:"product_id,omitempty"`          // sku_id
	Code       string    `gorm:"type:varchar(100)" json:"code,omitempty"`       // 属性编号
	Name       string    `gorm:"type:varchar(100)" json:"name,omitempty"`       // 属性名称
	ValueCode  string    `gorm:"type:varchar(100)" json:"value_code,omitempty"` // 属性值编号
	ValueName  string    `gorm:"type:varchar(100)" json:"value_name,omitempty"` // 属性值名称
	ValuePrice float64   `json:"value_price,omitempty"`                         // 属性加价
	Qty        int       `json:"qty,omitempty"`                                 // 对应的商品数量
	ComboType  int       `gorm:"default:0" json:"combo_type,omitempty"`         // 商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品
	Created    time.Time `gorm:"not null" json:"created,omitempty"`             // 创建时间
	Updated    time.Time `json:"updated,omitempty"`                             // 更新时间
	UpdatedBy  int64     `json:"updated_by,omitempty"`                          // 更新人
	Deleted    int       `json:"deleted,omitempty"`                             // 删除标记
}

func (rpa *SalesProductSkuRemark) TableName() string {
	return "sales_product_sku_remark"
}

func (rpa *SalesProductSkuRemark) SortFields() []string {
	return []string{"BUS_DATE"}
}

// 支付统计模型
type SalesPaymentAmount struct {
	Id                   int64          `gorm:"type:bigint;not null" json:"id"`                          // ID
	PartnerId            int64          `gorm:"type:bigint;not null" json:"partner_id"`                  // 租户ID
	ScopeId              int64          `gorm:"type:bigint;not null" json:"scope_id"`                    // 分区ID
	BusDate              time.Time      `gorm:"type:date;not null" json:"bus_date"`                      // 营业日期
	BusDateWeek          time.Time      `gorm:"type:date;not null" json:"bus_date_week"`                 // 营业日期（周）
	BusDateMonth         time.Time      `gorm:"type:date;not null" json:"bus_date_month"`                // 营业日期（月）
	BusDateYear          time.Time      `gorm:"type:date;not null" json:"bus_date_year"`                 // 营业日期（年）
	StoreId              int64          `gorm:"type:bigint;not null" json:"store_id"`                    // 门店ID
	EticketId            int64          `gorm:"type:bigint;not null" json:"eticket_id"`                  // 电子小票ID
	ChannelId            string         `gorm:"type:varchar(50);not null" json:"channel_id"`             // 渠道id
	OrderTime            time.Time      `gorm:"type:timestamp;not null" json:"order_time"`               // 订单时间
	Refunded             bool           `gorm:"type:boolean;not null" json:"refunded"`                   // 是否退款
	PaymentId            int64          `gorm:"type:bigint;not null" json:"payment_id"`                  // 支付ID
	PaymentCode          string         `gorm:"type:varchar(50);not null" json:"payment_code"`           // 支付编码
	PaymentName          string         `gorm:"type:varchar(50);not null" json:"payment_name"`           // 支付名称
	Receivable           float64        `gorm:"type:numeric(38,16);not null" json:"receivable"`          // 应收金额
	PayAmount            float64        `gorm:"type:numeric(38,16);not null" json:"pay_amount"`          // 实付金额
	Rounding             float64        `gorm:"type:numeric(38,16);not null" json:"rounding"`            // 抹零
	OverflowAmount       float64        `gorm:"type:numeric(38,16);not null" json:"overflow_amount"`     // 溢收
	ChangeAmount         float64        `gorm:"type:numeric(38,16);not null" json:"change_amount"`       // 找零
	Created              time.Time      `gorm:"not null" json:"created"`                                 // 创建时间
	Qty                  int64          `gorm:"type:bigint;not null" json:"qty"`                         // 数量
	EticketCount         int64          `gorm:"type:bigint;not null" json:"eticket_count"`               // 单数
	OrderStatus          TicketStatus   `gorm:"type:varchar(20);not null" json:"order_status,omitempty"` // 正单，退单，部分退款单
	TpAllowance          float64        `gorm:"type:numeric(38,16);not null" json:"tp_allowance"`        // 第三方补贴金额
	TransferAmount       float64        `gorm:"type:numeric(38,16);not null" json:"transfer_amount"`     // 支付转折扣
	RealAmount           float64        `gorm:"type:numeric(38,16);not null" json:"real_amount"`         // 实收金额
	Cost                 float64        `gorm:"type:numeric(38,16);not null" json:"cost"`                // 用户实际购买金额
	MerchantAllowance    float64        `gorm:"type:numeric(38,16);not null" json:"merchant_allowance"`  // 商家补贴金额
	FinancePayAmount     float64        `gorm:"type:numeric(38,16);not null" json:"finance_pay_amount"`  // 财务实付
	FinancePayAmountUsed bool           `gorm:"type:boolean:not null" json:"finance_pay_amount_used"`    // 是否使用财务实付
	StoreTags            pq.StringArray `gorm:"type:text[]" json:"store_tags,omitempty"`                 // 门店标签
	StoreTagNames        pq.StringArray `gorm:"type:text[]" json:"store_tag_names,omitempty"`
	// 卡号
	CardNo string `gorm:"type:varchar(50);not null" json:"card_no,omitempty"`
	// 服务费
	SurChargeAmount float64 `gorm:"type:numeric(38,16);not null" json:"sur_charge_amount"` // 服务费
	// 支付时间
	PayTime    time.Time `gorm:"type:timestamp;not null" json:"pay_time,omitempty"`
	IsNonSales bool      `json:"is_non_sales,omitempty"` // 非销售
}

func (rpa *SalesPaymentAmount) SortFields() []string {
	return []string{"BUS_DATE"}
}

// 折扣统计模型
type SalesDiscountAmount struct {
	Id            int64        `gorm:"type:bigint;not null" json:"id,omitempty"`                  //ID
	PartnerId     int64        `gorm:"type:bigint;not null" json:"partner_id,omitempty"`          //租户ID
	ScopeId       int64        `gorm:"type:bigint;not null" json:"scope_id,omitempty"`            //分区ID
	BusDate       time.Time    `gorm:"type:date;not null" json:"bus_date,omitempty"`              //营业日期
	BusDateWeek   time.Time    `gorm:"type:date;not null" json:"bus_date_week,omitempty"`         //营业日期（周）
	BusDateMonth  time.Time    `gorm:"type:date;not null" json:"bus_date_month,omitempty"`        //营业日期（月）
	BusDateYear   time.Time    `gorm:"type:date;not null" json:"bus_date_year,omitempty"`         //营业日期（年）
	StoreId       int64        `gorm:"type:bigint;not null" json:"store_id,omitempty"`            //门店ID
	EticketId     int64        `gorm:"type:bigint;not null" json:"eticket_id,omitempty"`          //电子小票ID
	OrderTime     time.Time    `gorm:"type:timestamp;not null" json:"order_time,omitempty"`       //订单时间
	Refunded      bool         `gorm:"type:bool;not null" json:"refunded,omitempty"`              //是否退款
	PromotionId   int64        `gorm:"type:bigint;not null" json:"promotion_id,omitempty"`        //促销ID
	PromotionCode string       `gorm:"type:varchar(50);not null" json:"promotion_code,omitempty"` //促销编码
	PromotionName string       `gorm:"type:varchar(50);not null" json:"promotion_name,omitempty"` //促销名称
	Amount        float64      `gorm:"type:numeric(38,16);not null" json:"amount,omitempty"`      //促销金额
	Created       time.Time    `gorm:"not null" json:"created,omitempty"`                         //创建时间
	Qty           int          `gorm:"type:bigint;not null" json:"qty,omitempty"`                 //数量
	EticketCount  int          `gorm:"type:bigint;not null" json:"eticket_count,omitempty"`       //单数
	OrderStatus   TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"`   // 正单，退单，部分退款单
	RealAmount    float64      `gorm:"type:numeric(38,16);not null" json:"real_amount,omitempty"` // 折扣优惠实收金额
	ProductId     int64        `gorm:"type:bigint;not null" json:"product_id,omitempty"`
	ProductCode   string       `gorm:"type:varchar(50);not null" json:"product_code,omitempty"`
	//CategoryId             int64        `gorm:"type:bigint;not null" json:"category_id,omitempty"`
	GrossAmount               float64        `gorm:"type:numeric(38,16);not null" json:"gross_amount,omitempty"`
	NetAmount                 float64        `gorm:"type:numeric(38,16);not null" json:"net_amount,omitempty"`
	DiscountAmount            float64        `gorm:"type:numeric(38,16);not null" json:"discount_amount,omitempty"` // 商品分摊到的折扣金额
	MerchantDiscountAmount    float64        `gorm:"type:numeric(38,16);not null" json:"merchant_discount_amount,omitempty"`
	DiscountTransferAmount    float64        `gorm:"type:numeric(38,16);not null" json:"discount_transfer_amount,omitempty"` // 商品分摊到的折扣转支付
	ChannelId                 int64          `gorm:"type:bigint;not null" json:"channel_id,omitempty"`
	ChannelName               string         `gorm:"type:varchar(50);not null" json:"channel_name,omitempty"`
	OrderType                 string         `gorm:"type:varchar(15);not null" json:"order_type,omitempty"`
	Cost                      float64        `gorm:"type,numeric(38,16);not null" json:"cost,omitempty"`                  // 商品分摊到的(财务)用户实际购买金额
	TpAllowance               float64        `gorm:"type,numeric(38,16);not null" json:"tp_allowance,omitempty"`          // 商品分摊到的(财务)第三方补贴金额
	MerchantAllowance         float64        `gorm:"type,numeric(38,16);not null" json:"merchant_allowance,omitempty"`    // 商品分摊到的(财务)商家补贴金额
	PlatformAllowance         float64        `gorm:"type,numeric(38,16);not null" json:"platform_allowance,omitempty"`    // 商品分摊到的(财务)平台补贴金额
	ProductCount              int            `gorm:"type:int;not null" json:"product_count"`                              // 商品数量
	StoreDiscountAmount       float64        `gorm:"type:numeric(38,16);not null" json:"store_discount_amount,omitempty"` // 门店折扣承担
	SumDiscountAmount         float64        `gorm:"type:numeric(38,16);not null" json:"sum_discount_amount"`             // 折扣金额合计
	SumCost                   float64        `gorm:"type:numeric(38,16);not null" json:"sum_cost"`                        // (财务)用户实际购买金额
	SumMerchantAllowance      float64        `gorm:"type:numeric(38,16);not null" json:"sum_merchant_allowance"`          // (财务)商家补贴金额
	SumTpAllowance            float64        `gorm:"type:numeric(38,16);not null" json:"sum_tp_allowance"`                // (财务)第三方补贴金额
	SumPlatformAllowance      float64        `gorm:"type:numeric(38,16);not null" json:"sum_platform_allowance"`          // (财务)平台补贴金额
	SumDiscountTransferAmount float64        `gorm:"type:numeric(38,16);not null" json:"sum_discount_transfer_amount"`    // 折扣转支付
	IsAccessory               bool           `gorm:"not null" json:"is_accessory"`                                        // 是否加料商品
	ComboType                 int            `gorm:"not null" json:"combo_type"`                                          // 商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品
	Weight                    float64        `gorm:"type:numeric(38,16);not null"`                                        // 重量/份量
	HasWeight                 bool           `gorm:"not null"`                                                            // 是否是称重商品
	Unit                      string         `gorm:"type:varchar(20)" json:"unit"`                                        // 单位
	PromotionCateId           string         `gorm:"type:varchar(100)" json:"promotion_cate_id,omitempty"`                // 促销分类ID
	PromotionCateName         string         `gorm:"type:varchar(100)" json:"promotion_cate_name,omitempty"`              // 促销分类名称
	StoreTags                 pq.StringArray `gorm:"type:json" json:"store_tags,omitempty"`                               // 门店标签
	StoreTagNames             pq.StringArray `gorm:"type:json" json:"store_tag_names,omitempty"`                          // 门店标签
	CouponChannelCode         string         `gorm:"type:varchar(255)" json:"coupon_channel_code,omitempty"`              // 卡券渠道
	CouponChannelName         string         `gorm:"type:varchar(255)" json:"coupon_channel_name,omitempty"`              // 卡券渠道
}

func (rpa *SalesDiscountAmount) SortFields() []string {
	return []string{"BUS_DATE"}
}

type SalesItemPaymentsAmounts struct {
	ID                int64     `gorm:"primaryKey;not null" json:"id,omitempty"`                            // ID
	PartnerId         int64     `gorm:"not null" json:"partner_id,omitempty"`                               // 租户ID
	StoreID           int64     `gorm:"not null" json:"store_id,omitempty"`                                 // 门店id
	StoreCode         string    `gorm:"type:varchar(100);not null" json:"store_code,omitempty"`             // 门店编码
	StoreName         string    `gorm:"type:varchar(100);not null" json:"store_name,omitempty"`             // 门店名称
	TicketID          string    `gorm:"type:varchar(64);not null" json:"ticket_id,omitempty"`               // 小票id
	EticketId         int64     `gorm:"type:bigint;not null" json:"eticket_id"`                             // 电子小票ID
	TicketUno         string    `gorm:"type:varchar(100);not null" json:"ticket_uno,omitempty"`             // 小票唯一编号
	TicketStatus      string    `gorm:"type:varchar(50);not null" json:"ticket_status,omitempty"`           // 小票状态：正单SALE 退单REFUND etc.
	TakeMealNo        string    `gorm:"type:varchar(100);not null" json:"take_meal_no,omitempty"`           // 小票取餐号
	MainSeq           uint64    `gorm:"not null" json:"main_seq,omitempty"`                                 // 主商品序号
	Seq               uint64    `gorm:"not null" json:"seq,omitempty"`                                      // item序号
	ItemID            int64     `gorm:"not null" json:"item_id,omitempty"`                                  // item ID
	ItemCode          string    `gorm:"type:varchar(100);not null" json:"item_code,omitempty"`              // item编码
	ItemName          string    `gorm:"type:varchar(100);not null" json:"item_name,omitempty"`              // item名称
	ItemType          string    `gorm:"type:varchar(50);not null" json:"item_type,omitempty"`               // item类型：NORMAL(普通商品),COMBO(套餐商品),COMBO_ITEM(套餐子项商品),ATTR(属性),ACCESSORY(加料)
	ParentItemID      int64     `gorm:"not null" json:"parent_item_id,omitempty"`                           // item ID
	Price             float64   `gorm:"type:numeric(38,16);not null" json:"price,omitempty"`                // 单价
	Qty               int64     `gorm:"not null" json:"qty,omitempty"`                                      // 数量
	Amount            float64   `gorm:"type:numeric(38,16);not null" json:"amount,omitempty"`               // 金额
	OrderPaidTotal    float64   `gorm:"type:numeric(38,16);not null" json:"order_paid_total,omitempty"`     // 整单商品支付金额
	SalesPayAmount    float64   `gorm:"type:numeric(38,16);not null" json:"sales_pay_amount,omitempty"`     // 支付项合计销售金额
	NonSalesPayAmount float64   `gorm:"type:numeric(38,16);not null" json:"non_sales_pay_amount,omitempty"` // 支付项合计非销售金额
	TransID           int64     `gorm:"not null" json:"trans_id,omitempty"`                                 // transID：*******************
	TransCode         string    `gorm:"type:varchar(50);not null" json:"trans_code,omitempty"`              // trans编码：110897
	TransName         string    `gorm:"type:varchar(100);not null" json:"trans_name,omitempty"`             // trans名称：员工餐、Visa支付
	TransAttr         string    `gorm:"type:varchar(50);not null" json:"trans_attr,omitempty"`              // trans类型：NON_SALES(非销售）、SALES（销售）
	TransAmount       float64   `gorm:"type:numeric(38,16)" json:"trans_amount,omitempty"`                  // trans总金额: 10.23
	BusDate           time.Time `gorm:"type:date;not null" json:"bus_date,omitempty"`                       // 营业日期
	StartTime         time.Time `gorm:"type:timestamp;not null"`                                            // 下单时间
	EndTime           time.Time `gorm:"type:timestamp;not null"`                                            // 结账时间
	Created           time.Time `gorm:"type:timestamptz;not null" json:"created,omitempty"`                 // 时间戳
}

func (rpa *SalesItemPaymentsAmounts) SortFields() []string {
	return []string{"BUS_DATE"}
}

// ItemPayment 自定义结构，用于暂存计算中的支付项
type ItemPayment struct {
	Id          string          `json:"id"`         // 支付id或者支付渠道id
	TransCode   string          `json:"trans_code"` // 支付方式编码
	TransName   string          `json:"trans_name"` // 支付方式名称，如存储卡券名称
	TransAttr   string          `json:"trans_attr"` // 支付方式所属交易类型：SALES,NON_SALES
	TransAmount decimal.Decimal `json:"pay_amount"` // 分摊到item的支付金额

	ApplyShoppingId string `json:"applyShoppingId"` // 支付中配置的那条商品id（tji_coupon支付才有的适用商品）
}

// ItemPromotion 自定义结构，用于暂存计算中的折扣项
type ItemPromotion struct {
	Id          string          `json:"id"`           // 折扣id
	TransCode   string          `json:"trans_code"`   // 折扣编码
	TransName   string          `json:"trans_name"`   // 折扣名称，如存储折扣券名称
	TransAttr   string          `json:"trans_attr"`   // 折扣所属交易类型：SALES,NON_SALES
	TransAmount decimal.Decimal `json:"promo_amount"` // 分摊到item的折扣金额
}
