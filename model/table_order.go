package model

import (
	"time"
)

type TableCache struct {
	Id        int64  `json:"id,omitempty"`
	PartnerId int64  `json:"partner_id,omitempty"`
	StoreId   int64  `json:"store_id,omitempty"`
	ZoneId    int64  `json:"zone_id,omitempty"`
	ZoneName  string `json:"zone_name,omitempty"`
	TableName string `json:"table_name,omitempty"`
	TableSeat int32  `json:"table_seat,omitempty"`
	TableMode int32  `json:"table_mode,omitempty"`
}

type SalesTableInfo struct {
	Id                int64        `json:"id"`
	PartnerId         int64        `json:"partner_id"`
	BusDate           time.Time    `gorm:"type:date" json:"bus_date,omitempty"`
	StoreId           int64        `json:"store_id"`
	TicketId          string       `json:"ticket_id"`
	EticketId         int64        `json:"eticket_id"`
	TableOrderId      int64        `json:"table_order_id"`
	MealTimeId        int64        `json:"meal_time_id"`
	MealName          string	   `json:"meal_name"`
	ChannelId         int64        `json:"channel_id"`
	ChannelName       string       `json:"channel_name"`
	OrderTime         time.Time    `gorm:"type:timestamp" json:"order_time,omitempty"`
	CostTime          int32        `json:"cost_time"`
	OrderStatus       TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"` // 正单，退单，部分退单
	EticketCount      int          `json:"eticket_count"`
	TableId           int64        `json:"table_id"`
	ZoneId            int64        `json:"zone_id"`
	People            float64      `json:"people"`
	BusinessAmount    float64      `json:"business_amount"`
	FinanceRealAmount float64      `json:"finance_real_amount"`
	GrossAmount       float64      `json:"gross_amount"`
	ServiceFee        float64      `json:"service_fee"`
	DiscountAmount    float64      `json:"discount_amount"`
	PayAmount         float64      `json:"pay_amount"`
	TablePayAmount    float64      `json:"table_pay_amount"`
	Created           time.Time    `gorm:"<-:create" json:"created,omitempty"`
	Updated           time.Time    `json:"updated,omitempty"`
	UpdatedBy         int64        `json:"updated_by"`
}

type SalesTableRefundProduct struct {
	Id           int64     `json:"id"`
	PartnerId    int64     `json:"partner_id"`
	BusDate      time.Time `gorm:"type:date" json:"bus_date,omitempty"`
	StoreId      int64     `json:"store_id"`
	TableOrderId int64     `json:"table_order_id"`
	TableId      int64     `json:"table_id"`
	ZoneId       int64     `json:"zone_id"`
	ProductId    int64     `json:"product_id"`
	RefundTime   time.Time `gorm:"type:timestamp" json:"refund_time,omitempty"`
	RefundName   string    `json:"refund_name"`
	Price        float64   `json:"price"`
	Qty          int32     `json:"qty"`
	GrossAmount  float64   `json:"gross_amount"`
	Created      time.Time `gorm:"<-:create" json:"created,omitempty"`
	Updated      time.Time `json:"updated,omitempty"`
	UpdatedBy    int64     `json:"updated_by"`
}
