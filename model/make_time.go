package model

import "time"

type SalesProductMakeTime struct {
    Id          int64     `gorm:"type:bigint;not null" json:"id"`
    PartnerId   int64     `gorm:"type:bigint;not null" json:"partner_id"`
    TicketId    string    `gorm:"type:varchar(64);not null" json:"ticket_id"`
    StoreId     int64     `gorm:"type:bigint;not null" json:"store_id"`
    ChannelCode string    `gorm:"type:varchar(50)" json:"channel_code"`
    ChannelName string    `gorm:"type:varchar(50)" json:"channel_name"`
    BusDate     time.Time `gorm:"type:date;not null" json:"bus_date"`
    OrderTime   time.Time `gorm:"type:timestamp;not null" json:"order_time"`
    OrderHour   int32     `gorm:"type:integer;not null" json:"order_hour"`
    ProductId   int64     `gorm:"type:bigint;not null" json:"product_id"`
    MakeTime    int32     `gorm:"type:integer;not null" json:"make_time"`
    FinishTime  time.Time `gorm:"type:timestamp;not null" json:"finish_time"`
    ProductDate time.Time `gorm:"type:date;not null" json:"product_date"`
    MakeHour    int32     `gorm:"type:integer;not null" json:"make_hour"`
    Deleted     int32     `gorm:"type:integer;not null" json:"deleted"`
    Created     time.Time `json:"created"`
    Updated     time.Time `json:"updated"`
    UpdatedBy   int64     `gorm:"type:bigint" json:"updated_by"`
}

func (m *SalesProductMakeTime) TableName() string {
    return "sales_product_make_time"
}
