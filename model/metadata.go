package model

import (
	"github.com/lib/pq"
	"time"
)

type StoreCache struct {
	Id          int64          `json:"id,omitempty"`
	Geos        []int64        `gorm:"type:bigint[]" json:"geos,omitempty"`
	Geo0        int64          `json:"geo_0,omitempty"`
	Geo1        int64          `json:"geo_1,omitempty"`
	Geo2        int64          `json:"geo_2,omitempty"`
	Branchs     []int64        `gorm:"type:bigint[]" json:"branchs,omitempty"`
	Branch0     int64          `json:"branch_0,omitempty"`
	Branch1     int64          `json:"branch_1,omitempty"`
	Branch2     int64          `json:"branch_2,omitempty"`
	Franchisees []int64        `gorm:"type:bigint[]" json:"franchisees,omitempty"` // 加盟商区域
	Franchisee0 int64          `json:"franchisee_0,omitempty"`
	Franchisee1 int64          `json:"franchisee_1,omitempty"`
	Franchisee2 int64          `json:"franchisee_2,omitempty"`
	Regions     pq.StringArray `json:"regions,omitempty"`
	Region0     string         `json:"region_0,omitempty"`
	Region1     string         `json:"region_1,omitempty"`
	Region2     string         `json:"region_2,omitempty"`
	StoreType   string         `json:"store_type,omitempty"`
	OpenStatus  string         `json:"open_status,omitempty"`
	PartnerId   int64          `json:"partner_id,omitempty"`
	StoreName   string         `json:"store_name,omitempty"`
	CompanyInfo int64          `json:"company_info,omitempty"` // 门店所属公司
	Currency    string         `json:"currency,omitempty"`     // 币种
	OpenDate    time.Time      `json:"open_date,omitempty"`    // 开业时间
	StoreCode   string         `json:"store_code,omitempty"`   // 门店编码
	TimeZone    string         `json:"time_zone,omitempty"`    // 时区
}

type ProductCache struct {
	Id         int64   `json:"id,omitempty"`
	Categories []int64 `gorm:"type:bigint[]" json:"categories,omitempty"`
	Category0  int64   `json:"category_0,omitempty"`
	Category1  int64   `json:"category_1,omitempty"`
	Category2  int64   `json:"category_2,omitempty"`
	PartnerId  int64   `json:"partner_id,omitempty"`
	Category   int64   `json:"category,omitempty"`
	HasCup     bool    `json:"has_cup,omitempty"`
}

type Id2IdCacheType int8

const (
	Geo Id2IdCacheType = iota
	Branch
	Franchisee
)

type PaymentCache struct {
	Id   int64  `gorm:"primaryKey;column:id" json:"id"`
	Code string `json:"code,omitempty"`
	Name string `json:"name,omitempty"`
	// cooperation_id 供应商id
	CooperationId string `json:"cooperation_id"`
	// cooperation_code 供应商编码
	CooperationCode string `json:"cooperation_code"`
	//transaction_types  交易类型
	TransactionTypes pq.StringArray ` gorm:"type:text[]" json:"transaction_types,omitempty"`
	// PAYMENT_CHANNEL_CATEGORY
	//支付类型 id
	PaymentCategoryId   int64  `json:"payment_category_id"`
	PaymentCategoryName string `json:"payment_category_name"`
	PaymentCategoryCode string `json:"payment_channel_category_code"`
	// 消费者支付方式 payment_method
	PaymentMethod string `json:"payment_method"`
}

func (p *PaymentCache) TableName() string {
	return "payment_caches"
}
