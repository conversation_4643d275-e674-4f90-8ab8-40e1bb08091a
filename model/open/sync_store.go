package open

import "time"

type SyncStoreReportReq struct {
	PartnerId int64  `json:"partner_id"` // 租户id
	StartDate string `json:"start_date"` // 开始日期
	EndDate   string `json:"end_date"`   // 结束日期
	StoreId   int64  `json:"store_id"`   // 门店id
}

type SyncStoreReportRow struct {
	BusDate          string  `json:"bus_date"`           // 营业日
	StoreId          int64   `json:"store_id"`           // 门店id
	ChannelId        int64   `json:"channel_id"`         // 订单渠道
	BusinessAmount   float64 `json:"business_amount"`    // 营业额
	DiscountAmount   float64 `json:"discount_amount"`    // 优惠金额
	ValidTicketCount int32   `json:"valid_ticket_count"` // 有效订单数
	Currency         string  `json:"currency"`           // 币种
}

type SyncStoreReportResp struct {
	Rows []*SyncStoreReportRow `json:"rows"` // 门店数据
}

func (m *SyncStoreReportData) TableName() string {
	return "sync_store"
}

type SyncStoreReportData struct {
	Id               int64     `json:"id"`                              // ID
	BusDate          string    `json:"bus_date"`                        // 营业日
	StoreId          int64     `json:"store_id"`                        // 门店id
	StoreCode        string    `json:"store_code"`                      // 门店编号
	StoreName        string    `json:"store_name"`                      // 门店名称
	ChannelId        int64     `json:"channel_id"`                      // 订单渠道
	OrderChannel     string    `json:"order_channel"`                   // 订单渠道
	BusinessAmount   float64   `json:"business_amount"`                 // 营业额
	RealAmount       float64   `json:"real_amount"`                     // 实收金额
	DiscountAmount   float64   `json:"discount_amount"`                 // 优惠金额
	ValidTicketCount int32     `json:"valid_ticket_count"`              // 有效订单数
	Currency         string    `json:"currency"`                        // 币种
	CreatedAt        time.Time `gorm:"type:datetime" json:"created_at"` // 创建时间
	UpdatedAt        time.Time `gorm:"type:datetime" json:"updated_at"` // 更新时间
}
