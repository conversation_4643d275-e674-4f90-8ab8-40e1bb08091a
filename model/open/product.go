package open

// 商品报表请求
type ProductReportReq struct {
	StartDate string `json:"start_date"` // 开始日期
	EndDate   string `json:"end_date"`   // 结束日期
	StoreId   int64  `json:"store_id"`   // 门店id
	ProductId int64  `json:"product_id"` // 商品skuid
	ChannelId int64  `json:"channel_id"` // 渠道id
	Limit     int32  `json:"limit"`      // 限制数量
	Offset    int32  `json:"offset"`     // 偏移量
	Total     bool   `json:"total"`      // 是否返回总数
}

type ProductReportRow struct {
	Id          int64   `json:"id"`           // 记录流水id
	BusDate     string  `json:"bus_date"`     // 营业日
	StoreId     int64   `json:"store_id"`     // 门店id
	StoreCode   string  `json:"store_code"`   // 门店编号
	StoreName   string  `json:"store_name"`   // 门店名称
	ChannelId   int64   `json:"channel_id"`   // 渠道id
	ChannelCode string  `json:"channel_code"` // 渠道编码
	ChannelName string  `json:"channel_name"` // 渠道名字
	ProductId   int64   `json:"product_id"`   // 商品skuid
	ProductCode string  `json:"product_code"` // 商品skucode
	ProductName string  `json:"product_name"` // 商品名称
	GrossAmount float64 `json:"gross_amount"` // 商品流水
	NetAmount   float64 `json:"net_amount"`   // 商品实收
	Qty         int32   `json:"qty"`          // 商品数量
	SkuRemark   string  `json:"sku_remark"`   // 做法
	Created     string  `json:"created"`      // 创建时间
	Updated     string  `json:"updated"`      // 更新时间
}

// 商品报表响应
type ProductReportResp struct {
	Rows  []*ProductReportRow `json:"rows"`  // 数据
	Total int32               `json:"total"` // 总数
}
