package open

// 收银汇总表请求
type FinancialReportReq struct {
    StartDate string `json:"start_date"` // 开始日期
    EndDate   string `json:"end_date"`   // 结束日期
    Summary   bool   `json:"summary"`    // 是否汇总门店数据到一行
}

// OpenFinancialReportStore 门店单天数据
type FinancialReportStore struct {
    BusDate          string  `json:"bus_date"`           // 营业日
    StoreId          int64   `json:"store_id"`           // 门店id
    StoreCode        string  `json:"store_code"`         // 门店编号
    StoreName        string  `json:"store_name"`         // 门店名称
    StoreCity        string  `json:"store_city"`         // 所在城市
    StoreType        string  `json:"store_type"`         // 门店经营类型id
    StoreTypeName    string  `json:"store_type_name"`    // 门店经营类型
    BusinessDays     int32   `json:"business_days"`      // 营业天数
    BusinessAmount   float64 `json:"business_amount"`    // 总营业额
    DiscountAmount   float64 `json:"discount_amount"`    // 总优惠组成
    ValidTicketCount int32   `json:"valid_ticket_count"` // 有效订单数
}

// 收银汇总表响应
type FinancialReportResp struct {
    Rows []*FinancialReportStore `json:"rows"` // 门店数据
}
