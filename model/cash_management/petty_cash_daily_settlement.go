package cash_management

type PettyCashDailySettlement struct {
	PartnerId         int64  `json:"partner_id"`
	BusinessDate      string `json:"business_date"`
	StoreId           int64  `json:"store_id"`
	StoreCode         string `json:"store_code"`
	StoreName         string `json:"store_name"`
	StartBalance      int64  `json:"start_balance"`
	CollectCashAmount int64  `json:"collect_cash_amount"`
	OtherAmount       int64  `json:"other_amount"`
	PayoutAmount      int64  `json:"payout_amount"`
	EndBalance        int64  `json:"end_balance"`
	PayoutDetail      string `json:"payout_detail"`
}

type PettyCashDailySettlementReportResp struct {
	Rows    []*PettyCashDailySettlement      `json:"rows"`
	Total   int64                            `json:"total"`
	Summary *PettyCashDailySettlementSummary `json:"summary"`
}

type PettyCashDailySettlementSummary struct {
	PayoutDetailSummary string  `json:"payout_detail_summary"`
	PayoutAmountSummary float64 `json:"payout_amount_summary"`
}
