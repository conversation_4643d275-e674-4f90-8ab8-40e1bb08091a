package model

import "time"

type StoreGoal struct {
	Id        int64     `json:"id"`
	PartnerId int64     `json:"partner_id"`
	StoreId   int64     `json:"store_id"`
	Goal      float64   `json:"goal"`
	GoalDate  time.Time `json:"goal_date"`
	Deleted   bool      `json:"deleted"`
	CreateAt  time.Time `json:"create_at"`
	UpdateAt  time.Time `json:"update_at"`
}

func (m *StoreGoal) TableName() string {
	return "store_goal"
}
