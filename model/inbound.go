package model

import (
	"encoding/json"
	"time"
)

type InboundInfo struct {
	StoreID      string            `json:"store_id"`      // 门店ID
	InboundTime  string            `json:"inbound_time"`  // 入库时间 yyyy-MM-dd HH:mm:ss
	Products     []*InboundProduct `json:"products"`      // 商品
	OperatorName string            `json:"operator_name"` //操作人
}

type InboundProduct struct {
	Id          string              `json:"id"`          // 后台商品productId
	SpuId       string              `json:"spu_id"`      // 后台商品productId
	Name        string              `json:"name"`        // 后台商品名字
	Code        string              `json:"code"`        // 后台商品编码
	Price       float64             `json:"price"`       // 商品单价
	Amount      float64             `json:"amount"`      // 商品总价
	Qty         int32               `json:"qty"`         // 数量
	Type        string              `json:"type"`        // 商品类型
	Accessories []*InboundProduct   `json:"accessories"` // 加料
	ComboItems  []*InboundProduct   `json:"combo_items"` // 套餐子项
	SkuRemark   []*InboundSkuRemark `json:"sku_remark"`  // sku属性(后台下发)
	Unit        InboundProductUnit  `json:"unit"`        // 单位
}

type InboundProductUnit struct {
	Id   int    `json:"id"`   // 单位id
	Code string `json:"code"` // 单位编号
	Name string `json:"name"` // 单位名称
}

type InboundSkuRemark struct {
	Name   TicketSkuName  `json:"name"`   // sku名称
	Values TicketSkuValue `json:"values"` // sku值
}

type InboundSkuName struct {
	Id   string `json:"id"`   // sku属性id
	Code string `json:"code"` // sku属性code
	Name string `json:"name"` // sku属性名称
}

type InboundSkuValue struct {
	Code  string  `json:"code"`  // sku属性值code
	Name  string  `json:"name"`  // sku属性值名称
	Price float64 `json:"price"` // sku属性值价格
}

/// table schema

type SalesInboundContent struct {
	Id           int64            `gorm:"type:bigint;not null" json:"id,omitempty"` // ID
	InboundId    int64            `gorm:"type:bigint;not null" json:"inbound_id,omitempty"`
	PartnerId    int64            `gorm:"type:bigint;not null" json:"partner_id"` // 租户ID
	StoreId      int64            `json:"store_id,omitempty"`                     // 门店ID
	InboundTime  time.Time        `gorm:"type:timestamp;not null" json:"inbound_time"`
	ProductNames string           `gorm:"type:varchar(100);not null" json:"product_names,omitempty"`
	ProductQty   int32            `json:"product_qty,omitempty"`
	OperatorName string           `gorm:"type:varchar(100);not null" json:"operator_name,omitempty"`
	Content      *json.RawMessage `gorm:"<-:create;type:json" json:"content,omitempty"` // 原始数据
	Created      time.Time        `gorm:"<-:create" json:"created,omitempty"`           // 创建时间
	Updated      time.Time        `json:"updated,omitempty"`
	UpdatedBy    int64            `json:"updated_by,omitempty"` // 更新人
	Deleted      int32            `gorm:"type:integer;not null" json:"deleted"`
}

func (m *SalesInboundContent) TableName() string {
	return "sales_inbound_content"
}

type SalesInboundProduct struct {
	Id           int64     `gorm:"type:bigint;not null" json:"id"`
	InboundId    int64     `gorm:"type:bigint;not null" json:"inbound_id,omitempty"`
	PartnerId    int64     `gorm:"type:bigint;not null" json:"partner_id"`
	StoreId      int64     `gorm:"type:bigint;not null" json:"store_id"`
	SkuId        int64     `gorm:"type:bigint;not null" json:"sku_id"`
	SpuId        int64     `gorm:"type:bigint;not null" json:"spu_id"`
	SkuCode      string    `gorm:"type:varchar(100);not null" json:"sku_code"`
	SkuName      string    `gorm:"type:varchar(100);not null" json:"sku_name"`
	Price        float64   `json:"price"`
	AccPrice     float64   `json:"acc_price"`
	Qty          float64   `json:"qty"`
	InboundTime  time.Time `gorm:"type:timestamp;not null" json:"inbound_time"`
	OperatorName string    `gorm:"type:varchar(100);not null" json:"operator_name"`
	Created      time.Time `json:"created"`
	Updated      time.Time `json:"updated"`
	UpdatedBy    int64     `gorm:"type:bigint" json:"updated_by"`
	Deleted      int32     `gorm:"type:integer;not null" json:"deleted"`
}

func (m *SalesInboundProduct) TableName() string {
	return "sales_inbound_product"
}
