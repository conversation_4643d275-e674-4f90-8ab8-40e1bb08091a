package model

import (
	"time"
)

// 门店报表模型
type SalesTicketAmountAll struct {
	Id                         int64        `gorm:"primaryKey;not null" json:"id,omitempty"`                                // ID
	PartnerId                  int64        `gorm:"not null" json:"partner_id,omitempty"`                                   // 租户ID
	ScopeId                    int64        `gorm:"not null" json:"scope_id,omitempty"`                                     // 分区ID
	BusDate                    time.Time    `gorm:"type:date;not null" json:"bus_date,omitempty"`                           // 营业日期
	BusDateWeek                time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-周
	BusDateMonth               time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-月
	BusDateYear                time.Time    `gorm:"type:date;not null" json:"-"`                                            // 营业日期-年
	StoreId                    int64        `gorm:"not null" json:"store_id,omitempty"`                                     // 门店ID
	EticketId                  int64        `gorm:"unique;not null" json:"eticket_id,omitempty"`                            // 电子小票ID
	ChannelId                  int64        `gorm:"not null" json:"channel_id,omitempty"`                                   // 渠道ID
	ChannelName                string       `gorm:"type:varchar(50);not null" json:"channel_name,omitempty"`                // 渠道名称
	OrderType                  string       `gorm:"type:varchar(15);not null" json:"order_type,omitempty"`                  // 订单类型
	OrderTypeName              string       `gorm:"type:varchar(15);not null" json:"order_type_name,omitempty"`             // 订单类型名称
	OrderTime                  time.Time    `gorm:"type:timestamp;not null" json:"order_time,omitempty"`                    // 订单时间
	Refunded                   bool         `gorm:"not null" json:"refunded,omitempty"`                                     // 是否退款
	GrossAmount                float64      `gorm:"type:numeric(38,16);not null" json:"gross_amount,omitempty"`             // 交易毛额
	NetAmount                  float64      `gorm:"type:numeric(38,16);not null" json:"net_amount,omitempty"`               // 交易净额
	DiscountAmount             float64      `gorm:"type:numeric(38,16);not null" json:"discount_amount,omitempty"`          // 折扣金额
	Tip                        float64      `gorm:"type:numeric(38,16);not null" json:"tip,omitempty"`                      // 小费
	PackageFee                 float64      `gorm:"type:numeric(38,16);not null" json:"package_fee,omitempty"`              // 打包费
	DeliveryFee                float64      `gorm:"type:numeric(38,16);not null" json:"delivery_fee,omitempty"`             // 配送费
	ServiceFee                 float64      `gorm:"type:numeric(38,16);not null" json:"service_fee,omitempty"`              // 服务费
	TaxFee                     float64      `gorm:"type:numeric(38,16);not null" json:"tax_fee,omitempty"`                  // 税额
	OtherFee                   float64      `gorm:"type:numeric(38,16);not null" json:"other_fee,omitempty"`                // 其他费用
	PayAmount                  float64      `gorm:"type:numeric(38,16);not null" json:"pay_amount,omitempty"`               // 实付金额
	Rounding                   float64      `gorm:"type:numeric(38,16);not null" json:"rounding,omitempty"`                 // 抹零
	OverflowAmount             float64      `gorm:"type:numeric(38,16);not null" json:"overflow_amount,omitempty"`          // 溢收
	ChangeAmount               float64      `gorm:"type:numeric(38,16);not null" json:"change_amount,omitempty"`            // 找零
	ProductCount               int          `gorm:"not null" json:"product_count,omitempty"`                                // 商品数量
	AccessoryCount             int          `gorm:"not null" json:"accessory_count,omitempty"`                              // 加料数量
	EticketCount               int          `gorm:"not null" json:"eticket_count,omitempty"`                                // 单数
	Created                    time.Time    `gorm:"not null" json:"created,omitempty"`                                      // 创建时间
	Commission                 float64      `gorm:"type:numeric(38,16);not null" json:"commission,omitempty"`               // 佣金
	PaymentTransferAmount      float64      `gorm:"type:numeric(38,16)" json:"payment_transfer_amount,omitempty"`           // 支付转折扣
	DiscountTransferAmount     float64      `gorm:"type:numeric(38,16)" json:"discount_transfer_amount,omitempty"`          // 折扣转支付
	RefundCode                 string       `gorm:"type:varchar(15)" json:"refund_code,omitempty"`                          // 退单原因code
	RefundSide                 string       `gorm:"type:varchar(15)" json:"refund_side,omitempty"`                          // 退单方
	RefundReason               string       `gorm:"type:varchar(255)" json:"refund_reason,omitempty"`                       // 退单原因
	Amount0                    float64      `gorm:"column:amount_0;type:numeric(38,16);not null" json:"amount_0,omitempty"` // 营业额
	Amount1                    float64      `gorm:"column:amount_1;type:numeric(38,16);not null" json:"amount_1,omitempty"` // 支出
	Amount2                    float64      `gorm:"column:amount_2;type:numeric(38,16);not null" json:"amount_2,omitempty"` // 实收金额(转换后)
	Amount3                    float64      `gorm:"column:amount_3;type:numeric(38,16);not null" json:"amount_3,omitempty"` // 实收金额
	Amount4                    float64      `gorm:"column:amount_4;type:numeric(38,16);not null" json:"amount_4,omitempty"` // 应付金额
	MerchantDiscountAmount     float64      `gorm:"type:numeric(38,16);not null" json:"merchant_discount_amount,omitempty"` // 商家优惠承担
	PlatformDiscountAmount     float64      `gorm:"type:numeric(38,16);not null" json:"platform_discount_amount,omitempty"` // 平台优惠承担
	OrderStatus                TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"`                // 正单，退单，部分退单
	Member                     bool         `gorm:"not null" json:"member"`                                                 // 是否会员
	MerchantSendFee            float64      `gorm:"type:numeric(38,16)" json:"merchant_send_fee,omitempty"`                 // 商家承担配送费
	StoreDiscountAmount        float64      `gorm:"type:numeric(38,16)" json:"store_discount_amount,omitempty"`             // 门店折扣金额
	DiscountMerchantContribute float64      `gorm:"type:numeric(38,16)" json:"discount_merchant_contribute,omitempty"`      // 活动商家出资
	DiscountPlatformContribute float64      `gorm:"type:numeric(38,16)" json:"discount_platform_contribute,omitempty"`      // 活动平台出资
	DiscountBuyerContribute    float64      `gorm:"type:numeric(38,16)" json:"discount_buyer_contribute,omitempty"`         // 活动用户出资
	DiscountOtherContribute    float64      `gorm:"type:numeric(38,16)" json:"discount_other_contribute,omitempty"`         // 活动第三方出资
	PayMerchantContribute      float64      `gorm:"type:numeric(38,16)" json:"pay_merchant_contribute,omitempty"`           // 实付商家出资
	PayPlatformContribute      float64      `gorm:"type:numeric(38,16)" json:"pay_platform_contribute,omitempty"`           // 实付平台出资
	PayBuyerContribute         float64      `gorm:"type:numeric(38,16)" json:"pay_buyer_contribute,omitempty"`              // 实付用户出资
	PayOtherContribute         float64      `gorm:"type:numeric(38,16)" json:"pay_other_contribute,omitempty"`              // 实付第三方出资
	SendFee                    float64      `gorm:"type:numeric(38,16)" json:"send_fee,omitempty"`                          // 用户实际配送费
	SendFeeForPlatform         float64      `gorm:"type:numeric(38,16)" json:"send_fee_for_platform,omitempty"`             // 用户支付给平台配送费
	SendFeeForMerchant         float64      `gorm:"type:numeric(38,16)" json:"send_fee_for_merchant,omitempty"`             // 用户支付给商家配送费
	PlatformSendFee            float64      `gorm:"type:numeric(38,16)" json:"platform_send_fee,omitempty"`                 // 平台承担配送费
	DeliveryFeeForMerchant     float64      `gorm:"type:numeric(38,16)" json:"delivery_fee_for_merchant,omitempty"`         // 累计付给商家配送费（自配送场景）
	NaturalDate                time.Time    `gorm:"type:date;not null" json:"natural_date"`                                 // 自然日
	SendFeeRate                float64      `gorm:"type:numeric(38,16)" json:"send_fee_rate"`                               // 配送费税率
	PackageFeeRate             float64      `gorm:"type:numeric(38,16)" json:"package_fee_rate"`                            // 包装费税率
	TicketType                 int32        `gorm:"type:integer;not null" json:"ticket_type,omitempty"`                     // 小票类型, 0 正常, 1 异常
	IsNonSales                 bool         `gorm:"type:boolean;not null" json:"is_non_sales,omitempty"`                    // 非销售
	PlateNo                    string       `json:"plate_no,omitempty"`                                                     // 飞盘号（外送号码）
	PhoneNo                    string       `json:"phone_no"`                                                               // 帐单号码

}

func (src *SalesTicketAmount) ToAll(t *Ticket, ticketType int32) *SalesTicketAmountAll {
	return &SalesTicketAmountAll{
		Id:                         src.Id,
		PartnerId:                  src.PartnerId,
		ScopeId:                    src.ScopeId,
		BusDate:                    src.BusDate,
		BusDateWeek:                src.BusDateWeek,
		BusDateMonth:               src.BusDateMonth,
		BusDateYear:                src.BusDateYear,
		StoreId:                    src.StoreId,
		EticketId:                  src.EticketId,
		ChannelId:                  src.ChannelId,
		ChannelName:                src.ChannelName,
		OrderType:                  src.OrderType,
		OrderTypeName:              src.OrderTypeName,
		OrderTime:                  src.OrderTime,
		Refunded:                   src.Refunded,
		GrossAmount:                src.GrossAmount,
		NetAmount:                  src.NetAmount,
		DiscountAmount:             src.DiscountAmount,
		Tip:                        src.Tip,
		PackageFee:                 src.PackageFee,
		DeliveryFee:                src.DeliveryFee,
		ServiceFee:                 src.ServiceFee,
		TaxFee:                     src.TaxFee,
		OtherFee:                   src.OtherFee,
		PayAmount:                  src.PayAmount,
		Rounding:                   src.Rounding,
		OverflowAmount:             src.OverflowAmount,
		ChangeAmount:               src.ChangeAmount,
		ProductCount:               src.ProductCount,
		AccessoryCount:             src.AccessoryCount,
		EticketCount:               src.EticketCount,
		Created:                    src.Created,
		Commission:                 src.Commission,
		PaymentTransferAmount:      src.PaymentTransferAmount,
		DiscountTransferAmount:     src.DiscountTransferAmount,
		RefundCode:                 src.RefundCode,
		RefundSide:                 src.RefundSide,
		RefundReason:               src.RefundReason,
		Amount0:                    src.Amount0,
		Amount1:                    src.Amount1,
		Amount2:                    src.Amount2,
		Amount3:                    src.Amount3,
		Amount4:                    src.Amount4,
		MerchantDiscountAmount:     src.MerchantDiscountAmount,
		PlatformDiscountAmount:     src.PlatformDiscountAmount,
		OrderStatus:                src.OrderStatus,
		Member:                     src.Member,
		MerchantSendFee:            src.MerchantSendFee,
		StoreDiscountAmount:        src.StoreDiscountAmount,
		DiscountMerchantContribute: src.DiscountMerchantContribute,
		DiscountPlatformContribute: src.DiscountPlatformContribute,
		DiscountBuyerContribute:    src.DiscountBuyerContribute,
		DiscountOtherContribute:    src.DiscountOtherContribute,
		PayMerchantContribute:      src.PayMerchantContribute,
		PayPlatformContribute:      src.PayPlatformContribute,
		PayBuyerContribute:         src.PayBuyerContribute,
		PayOtherContribute:         src.PayOtherContribute,
		SendFee:                    src.SendFee,
		SendFeeForPlatform:         src.SendFeeForPlatform,
		SendFeeForMerchant:         src.SendFeeForMerchant,
		PlatformSendFee:            src.PlatformSendFee,
		DeliveryFeeForMerchant:     src.DeliveryFeeForMerchant,
		NaturalDate:                GetNaturalDate(t),
		PhoneNo:                    src.PhoneNo,
		PlateNo:                    src.PlateNo,
		//SendFeeRate: src.SendFeeRate,
		//PackageFeeRate: src.PackageFeeRate,
		TicketType: ticketType,
		IsNonSales: src.IsNonSales,
	}
}

func (rta *SalesTicketAmountAll) SortFields() []string {
	return []string{"BUS_DATE"}
}

func (m *SalesTicketAmountAll) TableName() string {
	return "sales_ticket_amounts_all"
}

// 支付统计模型
type SalesPaymentAmountAll struct {
	Id                   int64        `gorm:"type:bigint;not null" json:"id"`                          // ID
	PartnerId            int64        `gorm:"type:bigint;not null" json:"partner_id"`                  // 租户ID
	ScopeId              int64        `gorm:"type:bigint;not null" json:"scope_id"`                    // 分区ID
	BusDate              time.Time    `gorm:"type:date;not null" json:"bus_date"`                      // 营业日期
	BusDateWeek          time.Time    `gorm:"type:date;not null" json:"bus_date_week"`                 // 营业日期（周）
	BusDateMonth         time.Time    `gorm:"type:date;not null" json:"bus_date_month"`                // 营业日期（月）
	BusDateYear          time.Time    `gorm:"type:date;not null" json:"bus_date_year"`                 // 营业日期（年）
	StoreId              int64        `gorm:"type:bigint;not null" json:"store_id"`                    // 门店ID
	EticketId            int64        `gorm:"type:bigint;not null" json:"eticket_id"`                  // 电子小票ID
	ChannelId            string       `gorm:"type:varchar(50);not null" json:"channel_id"`             // 渠道id
	OrderTime            time.Time    `gorm:"type:timestamp;not null" json:"order_time"`               // 订单时间
	Refunded             bool         `gorm:"type:boolean;not null" json:"refunded"`                   // 是否退款
	PaymentId            int64        `gorm:"type:bigint;not null" json:"payment_id"`                  // 支付ID
	PaymentCode          string       `gorm:"type:varchar(50);not null" json:"payment_code"`           // 支付编码
	PaymentName          string       `gorm:"type:varchar(50);not null" json:"payment_name"`           // 支付名称
	Receivable           float64      `gorm:"type:numeric(38,16);not null" json:"receivable"`          // 应收金额
	PayAmount            float64      `gorm:"type:numeric(38,16);not null" json:"pay_amount"`          // 实付金额
	Rounding             float64      `gorm:"type:numeric(38,16);not null" json:"rounding"`            // 抹零
	OverflowAmount       float64      `gorm:"type:numeric(38,16);not null" json:"overflow_amount"`     // 溢收
	ChangeAmount         float64      `gorm:"type:numeric(38,16);not null" json:"change_amount"`       // 找零
	Created              time.Time    `gorm:"not null" json:"created"`                                 // 创建时间
	Qty                  int64        `gorm:"type:bigint;not null" json:"qty"`                         // 数量
	EticketCount         int64        `gorm:"type:bigint;not null" json:"eticket_count"`               // 单数
	OrderStatus          TicketStatus `gorm:"type:varchar(20);not null" json:"order_status,omitempty"` // 正单，退单，部分退款单
	TpAllowance          float64      `gorm:"type:numeric(38,16);not null" json:"tp_allowance"`        // 第三方补贴金额
	TransferAmount       float64      `gorm:"type:numeric(38,16);not null" json:"transfer_amount"`     // 支付转折扣
	RealAmount           float64      `gorm:"type:numeric(38,16);not null" json:"real_amount"`         // 实收金额
	Cost                 float64      `gorm:"type:numeric(38,16);not null" json:"cost"`                // 用户实际购买金额
	MerchantAllowance    float64      `gorm:"type:numeric(38,16);not null" json:"merchant_allowance"`  // 商家补贴金额
	FinancePayAmount     float64      `gorm:"type:numeric(38,16);not null" json:"finance_pay_amount"`  // 财务实付
	FinancePayAmountUsed bool         `gorm:"type:boolean:not null" json:"finance_pay_amount_used"`    // 是否使用财务实付
	NaturalDate          time.Time    `gorm:"type:date;not null" json:"natural_date"`                  // 自然日
	TicketType           int32        `gorm:"type:integer;not null" json:"ticket_type,omitempty"`      // 小票类型, 0 正常, 1 异常
	IsNonSales           bool         `json:"is_non_sales,omitempty"`                                  // 非销售
}

func (src *SalesPaymentAmount) ToAll(t *Ticket, ticketType int32) *SalesPaymentAmountAll {
	return &SalesPaymentAmountAll{
		Id:                   src.Id,
		PartnerId:            src.PartnerId,
		ScopeId:              src.ScopeId,
		BusDate:              src.BusDate,
		BusDateWeek:          src.BusDateWeek,
		BusDateMonth:         src.BusDateMonth,
		BusDateYear:          src.BusDateYear,
		StoreId:              src.StoreId,
		EticketId:            src.EticketId,
		ChannelId:            toChannelId(src.ChannelId),
		OrderTime:            src.OrderTime,
		Refunded:             src.Refunded,
		PaymentId:            src.PaymentId,
		PaymentCode:          src.PaymentCode,
		PaymentName:          src.PaymentName,
		Receivable:           src.Receivable,
		PayAmount:            src.PayAmount,
		Rounding:             src.Rounding,
		OverflowAmount:       src.OverflowAmount,
		ChangeAmount:         src.ChangeAmount,
		Created:              src.Created,
		Qty:                  src.Qty,
		EticketCount:         src.EticketCount,
		OrderStatus:          src.OrderStatus,
		TpAllowance:          src.TpAllowance,
		TransferAmount:       src.TransferAmount,
		RealAmount:           src.RealAmount,
		Cost:                 src.Cost,
		MerchantAllowance:    src.MerchantAllowance,
		FinancePayAmount:     src.FinancePayAmount,
		FinancePayAmountUsed: src.FinancePayAmountUsed,
		NaturalDate:          GetNaturalDate(t),
		TicketType:           ticketType,
		IsNonSales:           src.IsNonSales,
	}
}

func (rpa *SalesPaymentAmountAll) SortFields() []string {
	return []string{"BUS_DATE"}
}

func (m *SalesPaymentAmountAll) TableName() string {
	return "sales_payment_amounts_all"
}

// sql中使用到cast channel_id as bigint, 防止执行报错
func toChannelId(s string) string {
	if len(s) == 0 {
		return "0"
	}
	return s
}
