package model

import (
	"encoding/json"
	"math"
	"time"
)

type FrmLoss struct {
	Id             string           `json:"id" storm:"id"`
	StoreID        string           `json:"storeID" storm:"index"`
	BusDate        string           `json:"bus_date" storm:"index"`
	CreatedTime    string           `json:"created_time"`
	RequestId      string           `json:"request_id"`
	FrmLossProduct []FrmLossProduct `json:"frm_loss_product"`
	Remark         string           `json:"remark"`   //备注
	Code           string           `json:"code"`     //操作人code
	FrmCode        string           `json:"frm_code"` //报损原因code
	ReasonName     string           `json:"reason_name"`
	OperatorName   string           `json:"operator_name"` //操作人
	ShiftNumber    string           `json:"shiftNumber" storm:"index"`
}

type FrmLossProduct struct {
	ProductId    string            `json:"product_id" storm:"index"`
	Price        float64           `json:"price"`
	LinkSpuId    string            `json:"link_spu_id"` //spu id同于本地库存的扣减
	ProductCode  string            `json:"product_code"`
	ProductName  string            `json:"product_name"`
	Quantity     float64           `json:"quantity"`
	UnitID       string            `json:"unit_id"`     //单位ID
	UnitName     string            `json:"unit_name"`   //单位名称
	Accesssories []*FrmLossProduct `json:"accessories"` // 加料
	ComboItems   []*FrmLossProduct `json:"combo_items"` // 套餐子项
	SkuRemark    []SkuRemark       `json:"skuRemark"`   //sku属性
}

type Sku struct {
	ID    uint64  `json:"id"`
	Code  string  `json:"code"`
	Name  string  `json:"name"`
	Price float64 `json:"price"`
}

type SkuRemark struct {
	Name   Sku `json:"name"`
	Values Sku `json:"values"`
}

type SalesAdjustContent struct {
	Id          int64     `gorm:"type:bigint;not null" json:"id,omitempty"` // ID
	AdjustId    string    `gorm:"<-:create;type:varchar(100);uniqueIndex:only" json:"adjust_id,omitempty"`
	BusDate     time.Time `gorm:"type:date;not null" json:"bus_date,omitempty"` // 营业日期
	PartnerId   int64     `gorm:"type:bigint;not null" json:"partner_id"`       // 租户ID
	StoreId     int64     `json:"store_id,omitempty"`                           // 门店ID
	OperateTime time.Time `gorm:"type:timestamp;not null" json:"operate_time"`

	Content   *json.RawMessage `gorm:"<-:create;type:json" json:"content,omitempty"` // 原始数据
	Created   time.Time        `gorm:"<-:create" json:"created,omitempty"`           // 创建时间
	Updated   time.Time        `json:"updated,omitempty"`
	UpdatedBy int64            `json:"updated_by,omitempty"` // 更新人
	Deleted   int32            `gorm:"type:integer;not null" json:"deleted"`
}

func (m *SalesAdjustContent) TableName() string {
	return "sales_adjust_content"
}

type SalesAdjustProduct struct {
	Id           int64            `gorm:"type:bigint;not null" json:"id"`
	AdjustId     string           `gorm:"<-:create;type:varchar(100);uniqueIndex:only" json:"adjust_id,omitempty"`
	BusDate      time.Time        `gorm:"type:date;not null" json:"bus_date,omitempty"` // 营业日期
	PartnerId    int64            `gorm:"type:bigint;not null" json:"partner_id"`
	StoreId      int64            `gorm:"type:bigint;not null" json:"store_id"`
	SkuId        int64            `gorm:"type:bigint;not null" json:"sku_id"`
	SpuId        int64            `gorm:"type:bigint;not null" json:"spu_id"`
	SkuCode      string           `json:"sku_code"`
	SkuName      string           `json:"sku_name"`
	Price        float64          `json:"price"`
	AccPrice     float64          `json:"acc_price"`
	Qty          float64          `json:"qty"`
	Unit         string           `gorm:"type:varchar(20)" json:"unit"` // 单位
	ReasonCode   string           `gorm:"type:varchar(100);not null" json:"reason_code"`
	ReasonName   string           `gorm:"type:varchar(100);not null" json:"reason_name"`
	OperateTime  time.Time        `gorm:"type:timestamp;not null" json:"operate_time"`
	OperatorName string           `gorm:"type:varchar(100);not null" json:"operator_name"`
	SkuRemark    *json.RawMessage `gorm:"type:json;not null" json:"sku_remark"`
	Accessories  *json.RawMessage `gorm:"type:json;not null" json:"accessories"`
	Remark       string           `gorm:"type:varchar(100);not null" json:"remark"`
	Created      time.Time        `json:"created"`
	Updated      time.Time        `json:"updated"`
	UpdatedBy    int64            `gorm:"type:bigint" json:"updated_by"`
	Deleted      int32            `gorm:"type:integer;not null" json:"deleted"`
}

func (m *SalesAdjustProduct) TableName() string {
	return "sales_adjust_product"
}

func (p FrmLossProduct) Flip() FrmLossProduct {
	p.Quantity = math.Round(- p.Quantity * 100) / 100
	accs := make([]*FrmLossProduct, 0, len(p.Accesssories))
	for _, a := range p.Accesssories {
		newA := a.Flip()
		accs = append(accs, &newA)
	}
	p.Accesssories = accs
	comboItems := make([]*FrmLossProduct, 0, len(p.ComboItems))
	for _, c := range p.ComboItems {
		newC := c.Flip()
		comboItems = append(comboItems, &newC)
	}
	p.ComboItems = comboItems
	return p
}

func (r FrmLoss) Flip() FrmLoss {
	r.FrmLossProduct = make([]FrmLossProduct, 0, len(r.FrmLossProduct))
	for _, p := range r.FrmLossProduct {
		newP := p.Flip()
		r.FrmLossProduct = append(r.FrmLossProduct, newP)
	}
	return r
}
