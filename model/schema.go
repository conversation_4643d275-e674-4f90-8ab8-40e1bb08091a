// code from https://gitlab.hexcloud.cn/hicloud/hipos-hub/-/wikis/ticket#73558c27
package model

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
)

// 电子小票模型
type Ticket struct {
	Id                   int64             `json:"id"`                    // 电子小票记录 id
	TicketId             string            `json:"ticket_id"`             // 小票id，全市场唯一
	TicketNo             string            `json:"ticket_no"`             // 交易号，根据业务规则生成
	TicketUno            string            `json:"ticketUno"`             // 订单号
	StartTime            string            `json:"start_time"`            // 开单时间， YYYY-MM-DD HH:MM:SS
	EndTime              string            `json:"end_time"`              // 订单结束时间, YYYY-MM-DD HH:MM:SS
	BusDate              string            `json:"bus_date"`              // 门店营业日期, YYYY-MM-DD
	Pos                  TicketPos         `json:"pos"`                   // pos信息
	Operator             TicketOperator    `json:"operator"`              // 收银员
	Amounts              TicketAmounts     `json:"amounts"`               // 订单金额信息
	TakemealNumber       string            `json:"takemealNumber"`        // 取餐号，根据业务规则生成
	Qty                  int               `json:"qty"`                   // 商品总数
	Status               TicketStatus      `json:"status"`                // 订单状态(正单，退单，或者是退单的原单)
	RefundInfo           TicketRefundInfo  `json:"refundInfo"`            // 订单的退款信息
	Channel              TicketChannel     `json:"channel"`               // 订单的渠道信息
	Products             []*TicketProduct  `json:"products"`              // 小票中的商品
	Payments             []TicketPayment   `json:"payments"`              // 支付项
	Promotions           []TicketPromotion `json:"promotions"`            // 促销项
	Members              []TicketMember    `json:"members"`               // 会员信息
	Table                TicketTable       `json:"table"`                 // 桌位信息
	Coupons              []TicketCoupon    `json:"coupons"`               // 卡券
	People               int               `json:"people"`                // 用餐人数
	RoomNo               string            `json:"room_no"`               // 房间号
	Remark               string            `json:"remark"`                // 备注 如果是退单，展示 正单的单号
	HouseAc              bool              `json:"house_ac"`              // 是否自用（如家
	OrderTimeType        string            `json:"order_time_type"`       // 早中晚餐标志
	ShiftNumber          string            `json:"shiftNumber"`           // 班次号，根据业务
	TaxList              []TicketTax       `json:"taxList"`               // 税项
	TakeawayInfo         TicketTakeaway    `json:"takeaway_info"`         // 外卖附加信息
	Store                TicketStore       `json:"store"`                 // 门店信息
	Fees                 []TicketFee       `json:"fees"`                  // 费用信息
	FeesNoAccount        []TicketFee       `json:"fees_no_account"`       // 无需用户额外支付的费用信息
	TimeZone             string            `json:"timeZone"`              // 时区信息，例如Asia/Shanghai
	ErrorCodes           string            `json:"error_codes"`           // 错误信息
	DiscountProportioned bool              `json:"discount_proportioned"` // 是否已折扣分摊
	Efficiency           Efficiency        `json:"efficiency"`            // 订单履约效率
	SpellOrder           bool              `json:"spellOrder"`            // 是否是拼单订单
	Currency             Currency          `json:"currency"`
	Prior                Prior             `json:"prior"`
	Weight               float64           `json:"weight"`              // 称重商品总重量(kg)
	MealSegmentName      string            `json:"mealSegmentName"`     // 餐段
	BowlNum              int32             `json:"bowl_num"`            // 碗数
	ConsumptionTime      float64           `json:"consumption_time"`    // 消费时长
	OrderTime            int64             `json:"orderTime,omitempty"` // 第一次下单时间，毫秒时间戳
	PlateNo              string            `json:"plateNo,omitempty"`   // 飞盘号

}

// 履约效率
type Efficiency struct {
	ConfirmedTime string  `json:"confirmed_time"` // 订单确认时间, YYYY-MM-DD HH:MM:SS
	MadeTime      string  `json:"made_time"`      // 制作完成时间, YYYY-MM-DD HH:MM:SS
	AssignedTime  string  `json:"assigned_time"`  // 物流接单时间, YYYY-MM-DD HH:MM:SS
	ArrivedTime   string  `json:"arrived_time"`   // 骑手到店时间, YYYY-MM-DD HH:MM:SS
	FetchedTime   string  `json:"fetched_time"`   // 骑手取餐时间, YYYY-MM-DD HH:MM:SS
	DeliveredTime string  `json:"delivered_time"` // 骑手送达时间, YYYY-MM-DD HH:MM:SS
	MakeSpan      float64 `json:"make_span"`      // 制作时长, 单位秒
	AvgMakeSpan   float64 `json:"avg_make_span"`  // 平均每杯制作时长, 单位秒
	ArriveSpan    float64 `json:"arrive_span"`    // 取餐时长, 单位秒
	DeliverSpan   float64 `json:"deliver_span"`   // 配送时长, 单位秒
}

type Currency struct {
	Name   string `json:"name"`
	Symbol string `json:"symbol"`
}

// 插队
type Prior struct {
	Reason  string `json:"reason"`
	Code    string `json:"code"`
	IsPrior bool   `json:"isPrior"`
}

func (t *Ticket) GetTimeZone() *time.Location {
	var zone *time.Location
	var err error
	if nAo := strings.Split(t.TimeZone, ","); len(nAo) != 2 {
		zone = config.BeijinZone
	} else {
		var offset int
		if offset, err = strconv.Atoi(nAo[1]); err != nil {
			zone = config.BeijinZone
		} else {
			zone = time.FixedZone(nAo[1], offset)
		}
	}
	return zone
}

func (ticket *Ticket) GetOrderTime() time.Time {
	// 优先采用第一次下单时间
	if ticket.OrderTime > 0 {
		return time.Unix(ticket.OrderTime/1000, 0).In(ticket.GetTimeZone())
	}
	if et, err := time.Parse(config.DateTimeFormat, ticket.EndTime); err == nil {
		return et
	}

	if et, err := time.Parse(config.DateTimeFormat, ticket.StartTime); err == nil {
		return et
	}
	return time.Now().In(ticket.GetTimeZone())
}

// 金额结构体
type TicketAmounts struct {
	TaxAmount                  float64 `json:"taxAmount"`                    // 税额
	TaxIncluded                bool    `json:"taxIncluded"`                  // 商品价格是否为含税价格
	GrossAmount                float64 `json:"gross_amount"`                 // 售价毛额
	NetAmount                  float64 `json:"net_amount"`                   // 支付后计算过优惠结果，gross_amount - net_amount
	PayAmount                  float64 `json:"pay_amount"`                   // 实付金额，与net_amount 相等
	DiscountAmount             float64 `json:"discount_amount"`              // 最终折扣总额
	RemovezeroAmount           float64 `json:"removezero_amount"`            // 抹零
	Rounding                   float64 `json:"rounding"`                     // 甩尾
	OverflowAmount             float64 `json:"overflow_amount"`              // 溢收
	ChangeAmount               float64 `json:"changeAmount"`                 // 找零金额
	ServiceFee                 float64 `json:"serviceFee"`                   // 服务费
	Tip                        float64 `json:"tip"`                          // 小费
	PackageFee                 float64 `json:"package_fee"`                  // 打包费
	DeliveryFee                float64 `json:"delivery_fee"`                 // 累计配送费
	DeliveryFeeForPlatform     float64 `json:"delivery_fee_for_platform"`    // 累计付给平台配送费（平台签约配送场景）
	DeliveryFeeForMerchant     float64 `json:"delivery_fee_for_merchant"`    // 累计付给商家配送费（自配送场景）
	OtherFee                   float64 `json:"otherFee"`                     // 其他费用
	Commission                 float64 `json:"commission"`                   // 佣金
	MerchantDiscountAmount     float64 `json:"merchant_discount_amount"`     // 商家优惠承担
	PlatformDiscountAmount     float64 `json:"platform_discount_amount"`     // 平台优惠承担
	Receivable                 float64 `json:"receivable"`                   // 应付金额/应收金额
	StoreDiscountAmount        float64 `json:"store_discount_amount"`        // 门店优惠承担
	DiscountMerchantContribute float64 `json:"discount_merchant_contribute"` // 活动商家出资
	DiscountPlatformContribute float64 `json:"discount_platform_contribute"` // 活动平台出资
	DiscountBuyerContribute    float64 `json:"discount_buyer_contribute"`    // 活动用户出资
	DiscountOtherContribute    float64 `json:"discount_other_contribute"`    // 活动第三方出资
	PayMerchantContribute      float64 `json:"pay_merchant_contribute"`      // 实付商家出资
	PayPlatformContribute      float64 `json:"pay_platform_contribute"`      // 实付平台出资
	PayBuyerContribute         float64 `json:"pay_buyer_contribute"`         // 实付用户出资
	PayOtherContribute         float64 `json:"pay_other_contribute"`         // 实付第三方出资
	PaymentTransferAmount      float64 `json:"payment_transfer_amount"`      // 支付转折扣
	DiscountTransferAmount     float64 `json:"discount_transfer_amount"`     // 折扣转支付
	RealAmount                 float64 `json:"real_amount"`                  // 实收金额转换后
	BusinessAmount             float64 `json:"business_amount"`              // 营业额
	ExpendAmount               float64 `json:"expend_amount"`                // 支出
	ProjectedIncome            float64 `json:"projected_income"`             // 实收金额
	SurChargeAmount            float64 `json:"surChargeAmount"`              //支付手续费
	Amount0                    float64 `json:"amount_0"`                     // 扩展金额字段
	Amount1                    float64 `json:"amount_1"`                     // 扩展金额字段
	Amount2                    float64 `json:"amount_2"`                     // 扩展金额字段
	Amount3                    float64 `json:"amount_3"`                     // 扩展金额字段
	Amount4                    float64 `json:"amount_4"`                     // 扩展金额字段
}

type TicketStore struct {
	Id         string           `json:"id"`         // 门店id
	Code       string           `json:"code"`       // 门店编码
	Name       string           `json:"name"`       // 门店名称
	PartnerId  string           `json:"partnerId"`  // 租户id
	ScopeId    string           `json:"scopeId"`    // 用于类似连锁酒店独立运营
	SecondCode string           `json:"secondCode"` // 门店第二编码，目前只在喜茶环境有实际意义
	CompanyId  string           `json:"companyId"`  // 门店公司id, 目前只在喜茶环境有实际意义
	Tags       []TicketStoreTag `json:"tags"`       // 门店标签
}

type TicketStoreTag struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

type TicketPos struct {
	Id         string `json:"id"`          // pos id
	Code       string `json:"code"`        // pos编码
	DeviceId   string `json:"device_id"`   // 设备id
	DeviceCode string `json:"device_code"` // 设备标识编码
}

type TicketOperator struct {
	Id        string `json:"id"`         // 收银员id
	Name      string `json:"name"`       // 收银员姓名
	Code      string `json:"code"`       // 收银员编码
	LoginTime string `json:"login_time"` // 登陆时间(YYYY-MM-DD HH:MM:SS)
	LoginId   string `json:"loginId"`    // 登陆id
}

type TicketTable struct {
	Id          string `json:"id"`        // 桌位id
	ZoneId      string `json:"zone_id"`   // 区域id
	No          string `json:"no"`        // 桌位号
	ZoneNo      string `json:"zoneNo"`    // 桌位分区号
	People      int    `json:"people"`    // 用餐人数
	Temporary   bool   `json:"temporary"` // 是否是临时桌位
	ZoneName    string `json:"zoneName"`  // 区域名称
	OpenTableAt string `json:"openTableAt"`
	OpenTableBy string `json:"openTableBy"` // 点餐人

}

type TicketChannel struct {
	Source       string `json:"source"`       // 区分是POS系统产生或被推送进入
	DeviceType   string `json:"deviceType"`   // POS系统内部的设备类型，比如pad或者kiosk
	OrderType    string `json:"orderType"`    // 区分堂食、外带和外送
	DeliveryType string `json:"deliveryType"` // 区分是预约单还是实时单
	TpName       string `json:"tpName"`       // 第三方平台名称，比如小程序，美团和饿了么
	Code         string `json:"code"`         // 渠道编码,后台配置的业务编码
	Id           string `json:"id"`           // 渠道id
	MappingCode  string `json:"mapping_code"` // 第三方编码，pos内部有标准定义
}

type TicketRefundInfo struct {
	RefundId     string `json:"refund_id"`     // 退单负单的ticketId，该字段与ref_ticket_id字段不可能同时不为空
	RefundNo     string `json:"refund_no"`     // 退单负单的ticketNo, 该字段与ref_ticket_no字段不可能同时不为空
	RefTicketId  string `json:"ref_ticket_id"` // 退单正单的ticketId
	RefTicketNo  string `json:"ref_ticket_no"` // 退单正单的ticketNo
	RefundReason string `json:"refund_reason"` // 退单原因
	RefundSide   string `json:"refund_side"`   // 退单方
	RefundCode   string `json:"refund_code"`   // 退单原因code
}

type TicketProductSortByAmount []*TicketProduct

func (a TicketProductSortByAmount) Len() int           { return len(a) }
func (a TicketProductSortByAmount) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a TicketProductSortByAmount) Less(i, j int) bool { return a[i].Amount < a[j].Amount }

type TicketProduct struct {
	Id                string            `json:"id"`                  // 后台商品productId
	ParentId          string            `json:"parent_id"`           // 父级productId
	Name              string            `json:"name"`                // 后台商品名字
	Code              string            `json:"code"`                // 后台商品编码
	SeqId             int               `json:"seq_id"`              // 商品的顺序号
	Price             float64           `json:"price"`               // 商品单价
	Amount            float64           `json:"amount"`              // 商品总价
	Qty               int               `json:"qty"`                 // 数量
	TaxAmount         float64           `json:"taxAmount"`           // 税额
	DiscountAmount    float64           `json:"discount_amount"`     // 折扣金额
	NetAmount         float64           `json:"net_amount"`          // 净额
	SumAmount         float64           `json:"sum_amount"`          // 商品原价
	SumDiscountAmount float64           `json:"sum_discount_amount"` // 商品折扣
	SumNetAmount      float64           `json:"sum_net_amount"`      // 商品实收
	Type              string            `json:"type"`                // 商品类型
	Accessories       []*TicketProduct  `json:"accessories"`         // 加料
	ComboItems        []*TicketProduct  `json:"combo_items"`         // 套餐子项
	SkuRemark         []TicketSkuRemark `json:"skuRemark"`           // sku属性(后台下发)
	Remark            string            `json:"remark"`              // 商品备注
	HasMakeSpan       bool              `json:"has_make_span"`       // 是否有制作时长信息
	AvgMakeSpan       float64           `json:"avg_make_span"`       // 平均每杯制作时长, 单位秒
	SpellUsers        []SpellUser       `json:"spellUsers"`
	Blessing          Blessing          `json:"blessing"`
	Weight            float64           `json:"weight"`            // 重量(kg)
	HasWeight         bool              `json:"has_weight"`        // 是否称重商品
	BowlCount         int               `json:"bowl_count"`        //商品包含的碗数
	RadioBowOptions   string            `json:"radio_bow_options"` //是否记碗数

	Unit MetaProductUnit `json:"unit"` // 单位
	// 增加商品税率、支付转折扣、开票金额、自然日、财务实收
	TransferAmount         float64 `json:"transfer_amount"`          // 支付转折扣
	FinanceRealAmount      float64 `json:"finance_real_amount"`      // 财务实收
	InvoiceAmount          float64 `json:"invoice_amount"`           // 开票金额
	NaturalDate            string  `json:"natural_date"`             // 自然日
	Rate                   float64 `json:"rate"`                     // 商品税率
	DiscountTransferAmount float64 `json:"discount_transfer_amount"` // 折扣转支付
	CreateTime             string  `json:"create_time"`              // 创建时间
	BowlNum                int32   `json:"bowl_num"`
	ProductsRealAmount     float64 `json:"products_real_amount"` // 商品实收金额, 订单维度财务实收的分摊
	PackageFee             float64 `json:"package_fee,omitempty"`
	MerchantDiscountAmount float64 `json:"merchant_discount_amount,omitempty"` // 商家折扣承担 - 分摊

	SubAmount                  float64 `json:"sub_amount"`                             // 分摊后商品流水
	PackageAmount              float64 `json:"package_amount,omitempty"`               // 分摊
	ServiceFee                 float64 `json:"service_fee,omitempty"`                  // 分摊
	Tip                        float64 `json:"tip,omitempty"`                          // 分摊
	OtherFee                   float64 `json:"other_fee,omitempty"`                    // 分摊
	Rounding                   float64 `json:"rounding,omitempty"`                     // 分摊
	OverflowAmount             float64 `json:"overflow_amount,omitempty"`              // 分摊
	Commission                 float64 `json:"commission,omitempty"`                   // 分摊
	MerchantSendFee            float64 `json:"merchant_send_fee,omitempty"`            // 分摊
	SendFeeForMerchant         float64 `json:"send_fee_for_merchant,omitempty"`        // 分摊
	SendFeeForPlatform         float64 `json:"send_fee_for_platform,omitempty"`        // 分摊
	PayCost                    float64 `json:"pay_cost,omitempty"`                     // 分摊
	PayTpAllowance             float64 `json:"pay_tp_allowance,omitempty"`             // 分摊
	PayMerchantAllowance       float64 `json:"pay_merchant_allowance,omitempty"`       // 分摊
	PayPlatformAllowance       float64 `json:"pay_platform_allowance,omitempty"`       // 分摊
	PromotionCost              float64 `json:"promotion_cost,omitempty"`               // 分摊
	PromotionTpAllowance       float64 `json:"promotion_tp_allowance,omitempty"`       // 分摊
	PromotionMerchantAllowance float64 `json:"promotion_merchant_allowance,omitempty"` // 分摊
	PromotionPlatformAllowance float64 `json:"promotion_platform_allowance,omitempty"` // 分摊
	ShoppingId                 string  `json:"shoppingId,omitempty"`                   // 商品行在订单中唯一id
}

type MetaProductUnit struct {
	Id   int    `json:"id"`   // 单位id
	Code string `json:"code"` // 单位编号
	Name string `json:"name"` // 单位名称
}

type SpellUser struct {
	Name string `json:"name"`
	Qty  int    `json:"qty"`
}

type Blessing struct {
	TemplateId string `json:"templateId"` // 模板id
	From       string `json:"from"`       // 祝福人
	To         string `json:"to"`         // 被祝福人
	Content    string `json:"content"`    // 祝福内容
}

func (bls *Blessing) UnmarshalJSON(b []byte) error {
	if string(b) == "null" || string(b) == `""` {
		*bls = Blessing{}
		return nil
	}
	type blessing Blessing // use another type to avoid stack overflow
	inBls := (*blessing)(bls)
	return json.Unmarshal(b, inBls)
}

type TicketSkuName struct {
	Id   string `json:"id"`   // sku属性id
	Code string `json:"code"` // sku属性code
	Name string `json:"name"` // sku属性名称
}

type TicketSkuValue struct {
	Code  string  `json:"code"`  // sku属性值code
	Name  string  `json:"name"`  // sku属性值名称
	Price float64 `json:"price"` // sku属性值价格
}

type TicketSkuRemark struct {
	Name   TicketSkuName  `json:"name"`   // sku名称
	Values TicketSkuValue `json:"values"` // sku值
}

type TicketTax struct {
	Id       string  `json:"id"`
	Amount   float64 `json:"amount"`   // 税金额
	SubTotal float64 `json:"subTotal"` // 计税总额
	Code     string  `json:"code"`     // 税种编码
	Name     string  `json:"name"`     // 税种名称
	Rate     float64 `json:"rate"`     // 税率
}

// 电子小票中的支付项结构体
type TicketPayment struct {
	Id                string   `json:"id"`                 // 支付id或者支付渠道id
	SeqId             string   `json:"seq_id"`             // 支付项序号，每个支付项不同
	Receivable        float64  `json:"receivable"`         // 当前支付的应收金额
	PayAmount         float64  `json:"pay_amount"`         // 实际支付的金额
	Change            float64  `json:"change"`             // 找零
	Overflow          float64  `json:"overflow"`           // 溢收
	Rounding          float64  `json:"rounding"`           // 甩尾
	PayTime           string   `json:"pay_time"`           // 支付时间(YYYY-MM-DD HH:MM:SS)
	TransCode         string   `json:"trans_code"`         // 支付方式编码
	TransName         string   `json:"trans_name"`         // 支付方式名称，如存储卡券名称
	TpTransactionNo   string   `json:"tpTransactionNo"`    // 三方支付流水号
	TpAllowance       float64  `json:"tp_allowance"`       // 第三方补贴金额
	MerchantAllowance float64  `json:"merchant_allowance"` // 商家补贴金额
	PlatformAllowance float64  `json:"platform_allowance"` // 平台补贴金额
	Cost              float64  `json:"cost"`               // 用户实际购买金额
	RealAmount        float64  `json:"real_amount"`        // 商家实收
	TransferAmount    float64  `json:"transfer_amount"`    // 支付转折扣
	BeforeMoney       float64  `json:"before_money"`       // 使用汇率之前的金额
	Parities          float64  `json:"parities"`           // 汇率
	Price             float64  `json:"price"`              // 售价
	HasInvoiced       bool     `json:"has_invoiced"`       // 是否已开发票
	Erd               float64  `json:"erd"`                // 汇率差
	Name              string   `json:"name"`
	IsOnline          bool     `json:"is_online"`
	TransactionTypes  []string `json:"transaction_types"` // 支付方式类型
	RefundPathCode    string   `json:"refund_path_code"`  // 退款路径code
	RefundPathName    string   `json:"refund_path_name"`  // 退款路径name
	CardType          string   `json:"card_type"`
	CardNo            string   `json:"card_no"`         //卡号
	SurChargeAmount   float64  `json:"surChargeAmount"` // 三方支付手续费
	ApplyShoppingId   string   `json:"applyShoppingId"` // 支付中配置的那条商品id（tji_coupon支付才有的适用商品）
}

type TicketFee struct {
	Id     string  `json:"id"`     // 主档费用配置id
	Code   string  `json:"code"`   // 主档费用编码id
	Name   string  `json:"name"`   // 费用名称，比如保温袋包装费.
	Price  float64 `json:"price"`  // 费用单价
	Qty    int     `json:"qty"`    // 费用数量
	Amount float64 `json:"amount"` // 费用总额
	Type   string  `json:"type"`   // TIP(小费), PACKAGE_FEE(打包费), DELIVERY_FEE(配送费), SERVICE_FEE(服务费), OTHER_FEE(其他费用), COMMISSION(佣金)
}

type TicketPromotion struct {
	PromotionInfo TicketPromotionInfo      `json:"promotionInfo"` // 促销的主档信息
	Source        TicketPromotionSource    `json:"source"`        // 促销结果
	Products      []TicketPromotionProduct `json:"products"`      // 折扣换购商品或赠品
}

type TicketPromotionInfo struct {
	Type               string   `json:"type"`                          // 折扣类型名称
	DiscountType       string   `json:"discount_type"`                 // 折扣类型
	DiscountLevel      string   `json:"discount_level"`                // 折扣级别 1: 订单界别  2: 商品级别
	Name               string   `json:"name"`                          // 折扣名称
	PromotionId        string   `json:"promotion_id"`                  // 促销id
	PromotionCode      string   `json:"promotion_code"`                // 促销code
	PromotionType      string   `json:"promotion_type"`                // 促销类型(NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
	AllowOverlap       bool     `json:"allow_overlap"`                 // 是否允许折上折
	TriggerTimesCustom bool     `json:"trigger_times_custom"`          // 是否允许多次
	TicketDisplay      string   `json:"ticket_display"`                // 小票上显示的促销名称
	MaxDiscount        float64  `json:"max_discount"`                  // 该折扣的最大可折扣金额
	PromotionCateId    string   `json:"promotion_cate_id,omitempty"`   // 促销分类ID
	PromotionCateName  string   `json:"promotion_cate_name,omitempty"` // 促销分类名称
	CouponId           string   `json:"coupon_id"`
	TransactionTypes   []string `json:"transaction_types"` // 支付方式类型
}

type TicketPromotionSource struct {
	Trigger           int      `json:"trigger"`            // 折扣次数
	Discount          float64  `json:"discount"`           // 折扣金额
	MerchantDiscount  float64  `json:"merchant_discount"`  // 活动商家优惠承担
	PlatformDiscount  float64  `json:"platform_discount"`  // 活动平台优惠承担
	StoreDiscount     float64  `json:"store_discount"`     // 活动门店优惠承担
	Cost              float64  `json:"cost"`               // (财务)用户实际购买金额
	TpAllowance       float64  `json:"tp_allowance"`       // (财务)第三方补贴金额
	MerchantAllowance float64  `json:"merchant_allowance"` // (财务)商家补贴金额
	PlatformAllowance float64  `json:"platform_allowance"` // (财务)平台补贴金额
	Fired             []string `json:"fired"`              // 该折扣作用的商品id
	RealAmount        float64  `json:"real_amount"`        // (财务)商家实收
	TransferAmount    float64  `json:"transfer_amount"`    // (财务)折扣转支付
}

type TicketPromotionProduct struct {
	Price       float64                  `json:"price"`                // 商品价格
	Amt         float64                  `json:"amt"`                  // 商品总价,商品流水
	AccAmt      float64                  `json:"accAmt"`               // 加料总价
	Qty         int                      `json:"qty"`                  // 商品数量
	KeyId       string                   `json:"key_id"`               // 商品id
	Accies      []string                 `json:"accies"`               // 加料ids
	Accessories []TicketPromotionProduct `json:"accessories"`          // 加料详情
	Type        string                   `json:"type"`                 // 商品类型
	Discount    float64                  `json:"discount"`             // 换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
	FreeAmt     float64                  `json:"free_amt"`             // 优惠金额
	Method      string                   `json:"method"`               // 换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
	Weight      float64                  `json:"weight"`               // 重量(kg)
	HasWeight   bool                     `json:"has_weight"`           // 是否称重商品
	Unit        MetaProductUnit          `json:"unit"`                 // 单位
	ShoppingId  string                   `json:"shoppingId,omitempty"` // pos 传递的商品序列id, 唯一
}

type TicketCoupon struct {
	IsOnline          bool    `json:"isOnline"`           // 是否是线上券
	Id                string  `json:"id"`                 // 卡券唯一编码
	Name              string  `json:"name"`               // 卡券名称
	Type              int     `json:"type"`               // 是否是会员券
	Code              string  `json:"code"`               // 会员券类型
	ParValue          float64 `json:"par_value"`          // 卡券优惠金额
	Price             float64 `json:"price"`              // 售价
	RealAmount        float64 `json:"real_amount"`        // 卡券实收金额
	TransferAmount    float64 `json:"transfer_amount"`    // 折扣转支付
	PlatformAllowance float64 `json:"platform_allowance"` // 平台补贴金额
	TpAllowance       float64 `json:"tp_allowance"`       // 第三方补贴金额
	MerchantAllowance float64 `json:"merchant_allowance"` // 商家补贴金额
	Cost              float64 `json:"cost"`               // 用户实际购买金额
	ChannelCode       string  `json:"channel_code"`
	ChannelName       string  `json:"channel_name"`
}

type TicketMember struct {
	MemberCode    string `json:"member_code"`    // 会员号
	Mobile        string `json:"mobile"`         // 手机号
	Name          string `json:"name"`           // 会员名称
	GradeCode     string `json:"grade_code"`     // 会员等级编码
	GradeName     string `json:"grade_name"`     // 会员等级名称
	Greetings     string `json:"greetings"`      // 会员问候语，需要打印在杯贴上
	BalancePoints int64  `json:"balance_points"` // 会员积分账户现有的积分
	TotalPoints   int64  `json:"total_points"`   // 会员积分账户历史总积分
	OrderPoints   int64  `json:"order_points"`   // 本次订单获得的积分积分
}

type TicketTakeaway struct {
	OrderMethod         string   `json:"order_method"`          // 默认TAKEWAY，还可能会是INSTORE（店内点餐）、PREORDER（预约自提）
	TpOrderId           string   `json:"tp_order_id"`           // 外卖单的三方订单号
	OrderTime           string   `json:"order_time"`            // 下单时间(YYYY-MM-DD HH:MM:SS)
	DeliverTime         string   `json:"deliver_time"`          // 预计送达时间(YYYY-MM-DD HH:MM:SS)
	Description         string   `json:"description"`           // 备注
	Consignee           string   `json:"consignee"`             // 订餐人员
	PhoneList           []string `json:"phone_list"`            // 联系电话
	Tp                  string   `json:"tp"`                    // 第三方平台
	Source              string   `json:"source"`                // 外卖订单来源
	SourceOrderId       string   `json:"source_order_id"`       // 外卖订单来源id
	DaySeq              string   `json:"day_seq"`               // 外卖订单流水号
	DeliveryType        int      `json:"delivery_type"`         // 外卖订单类型
	DeliveryName        string   `json:"delivery_name"`         // 送餐人姓名
	DeliveryPhone       string   `json:"delivery_phone"`        // 送餐人电话
	InvoiceTitle        string   `json:"invoice_title"`         // 发票抬头
	InvoiceType         string   `json:"invoice_type"`          // 发票类型, PERSONAL/COMPANY
	InvoiceTaxPayerId   string   `json:"invoice_tax_payer_id"`  // 纳税人识别号
	InvoiceEmail        string   `json:"invoice_email"`         // 用户取发票邮箱
	WaitingTime         string   `json:"waiting_time"`          // 等待时间
	TablewareNum        int      `json:"tableware_num"`         // 餐具数量
	SendFee             float64  `json:"send_fee"`              // 配送费
	SendFeeForPlatform  float64  `json:"send_fee_for_platform"` // 用户支付给平台配送费
	SendFeeForMerchant  float64  `json:"send_fee_for_merchant"` // 用户支付给商家配送费
	MerchantSendFee     float64  `json:"merchant_send_fee"`     // 商家承担配送费
	PlatformSendFee     float64  `json:"platform_send_fee"`     // 用户承担配送费
	SelfDelivery        bool     `json:"selfDelivery"`          // 是否自配送, 商家自配送/平台签约配送
	PackageFee          float64  `json:"package_fee"`           // 餐盒费
	DeliveryTime        string   `json:"delivery_time"`         // 意义待明确
	TakeMealSn          string   `json:"take_meal_sn"`          // 第三方取餐号
	NeedInvoice         bool     `json:"needInvoice"`           // 是否打印开票二维码
	PartnerPlatformId   int      `json:"partnerPlatformId"`     // 合作方id，需要打印在喜茶杯贴条形码上
	PartnerPlatformName string   `json:"partnerPlatformName"`   // 合作方名称，就是美团等
	WxName              string   `json:"wxName"`                // 微信昵称，小程序订单有值，打印在喜茶杯贴上
	Remarks             string   `json:"remarks"`               // 订单备注, 非空时打印备注杯贴
	DeliveryPoiAddress  string   `json:"delivery_poi_address"`  // 外卖配送地址
	IsHighPriority      bool     `json:"isHighPriority"`        // 是否是优先券订单
	TakeoutType         string   `json:"takeoutType"`           // 外卖单类型，分为`NORMAL`和`PARTIAL`，分别为正常单>和部分退款.
	OriginalOrderNo     string   `json:"originalOrderNo"`       // 部分退款单时，原单号
	Blessing            Blessing `json:"blessing"`              // 祝福信息
	NickName            string   `json:"nickName"`              // 用户原始昵称
	DeliveryPlatform    string   `json:"delivery_platform"`     // 配送平台
	SendFeeRate         float64  `json:"send_fee_rate"`         // 配送费税率
	SendFeeTaxCode      string   `json:"send_fee_tax_code"`     // 配送费税种分类编号
	PackageFeeRate      float64  `json:"package_fee_rate"`      // 包装费税率
	PackageFeeTaxCode   string   `json:"package_fee_tax_code"`  // 包装费税种分类编号
}

func (t *Ticket) isPos() bool {
	return t.Channel.Source == "POS"
}

func CalcTicket(ticket *Ticket) {
	var noZeroProducts []*TicketProduct
	for _, product := range ticket.Products {
		if product.Price != 0 {
			noZeroProducts = append(noZeroProducts, product)
		} else {
			if ticket.Status != "SALE" {
				// 部分退负单，如果只退了 饿了么的 加料，需要计算
				// 饿了么加料在合阔这边算作加价属性
				noZeroProducts = append(noZeroProducts, product)
			}
		}
	}

	allSkus := step1Calc(noZeroProducts)

	// 处理单商品行的金额
	allSingleSkuList := step7Calc(ticket, allSkus)

	// 处理套餐子商品的金额
	for _, product := range allSingleSkuList {
		if len(product.ComboItems) > 0 {
			step7SubCalc(product, product.ComboItems)
		}
	}
}
