package model

import (
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"time"
)

func GetOrderDateTime(ticket *Ticket) time.Time {
	var dateTime time.Time
	if ticket.Channel.Source == "POS" { // POS取end_time
		dateTime, _ = time.Parse(config.DateTimeFormat, ticket.EndTime)
	} else { // 其他取start_time
		dateTime, _ = time.Parse(config.DateTimeFormat, ticket.StartTime)
	}
	return dateTime
}

func GetNaturalDate(ticket *Ticket) time.Time {
	var dateTime time.Time
	if ticket.Channel.DeliveryType == "BOOKING" {
		dateTime, _ = time.Parse(config.DateTimeFormat, ticket.TakeawayInfo.DeliverTime)
	} else {
		dateTime = GetOrderDateTime(ticket)
	}
	return time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), 0, 0, 0, 0, time.UTC)
}
