-- =============================================
-- Author: 杜森
-- Create date: 20200824
-- Description: 基础表结构
-- Modify [n]: 20201026
-- =============================================

-- 两张主档表,用于从主档缓存数据，提高查询速度
CREATE TABLE store_caches
(
    id          bigserial not null primary key,
    geos        bigint[],
    geo0        bigint,
    geo1        bigint,
    geo2        bigint,
    branchs     bigint[],
    branch0     bigint,
    branch1     bigint,
    branch2     bigint,
    store_type  bigint,
    open_status varchar(10)
);

CREATE TABLE product_caches
(
    id         bigserial not null primary key,
    categories bigint[],
    category0  bigint,
    category1  bigint,
    category2  bigint
);

CREATE TABLE id2id_caches
(
    id   bigint not null default 0,
    c_id bigint not null default 0,
    type int2   not null default 0,
    primary key (id, type, c_id)
);

-- 用于存储上传上来的电子小票
CREATE TABLE sales_ticket_contents
(
    id               bigserial   not null primary key,
    eticket_id       bigint,
    ticket_id        varchar(64),
    partner_id       bigint,
    scope_id         bigint,
    store_id         bigint,
    bus_date         date,
    order_time       timestamp,
    created          timestamp with time zone,
    updated          timestamp with time zone,
    updated_by       bigint,
    status           varchar(10),
    process_status   varchar(10),
    content          json,
    content_modified json,
    is_deleted       boolean,
    channel_id       bigint      not null,
    channel_name     varchar(50) not null,
    order_type       varchar(15) not null,
    order_type_name  varchar(15) not null
);

-- 四张基础表
-- 折扣报表
CREATE TABLE sales_discount_amounts
(
    id             bigint          not null primary key,
    partner_id     bigint          not null,
    scope_id       bigint          not null,
    bus_date       date            not null,
    bus_date_week  date            not null,
    bus_date_month date            not null,
    bus_date_year  date            not null,
    store_id       bigint          not null,
    eticket_id     bigint          not null,
    order_time     timestamp       not null,
    refunded       boolean         not null,
    promotion_id   bigint          not null,
    promotion_code varchar(50)     not null,
    amount         numeric(38, 16) not null,
    created        TIMESTAMP       not null,
    qty            bigint          not null,
    eticket_count  bigint          not null
);

ALTER TABLE sales_discount_amounts
    REPLICA IDENTITY FULL;

-- 支付报表
CREATE TABLE sales_payment_amounts
(
    id              bigint          not null primary key,
    partner_id      bigint          not null,
    scope_id        bigint          not null,
    bus_date        date            not null,
    bus_date_week   date            not null,
    bus_date_month  date            not null,
    bus_date_year   date            not null,
    store_id        bigint          not null,
    eticket_id      bigint          not null,
    order_time      timestamp       not null,
    refunded        boolean         not null,
    payment_id      bigint          not null,
    payment_code    varchar(50)     not null,
    receivable      numeric(38, 16) not null,
    pay_amount      numeric(38, 16) not null,
    rounding        numeric(38, 16) not null,
    overflow_amount numeric(38, 16) not null,
    change_amount   numeric(38, 16) not null,
    created         timestamp       not null,
    qty             bigint          not null,
    eticket_count   bigint          not null
);
ALTER TABLE sales_payment_amounts
    REPLICA IDENTITY FULL;

-- 门店报表：
-- amount_0:营业额business_amount；
-- amount_1:支出expend_amount；
-- amount_2:实收金额(转换后)real_amount
-- amount_3:实收金额 projected_income
CREATE TABLE sales_ticket_amounts
(
    id                       bigserial       not null primary key,
    partner_id               bigint          not null,
    scope_id                 bigint          not null,
    bus_date                 date            not null,
    bus_date_week            date            not null,
    bus_date_month           date            not null,
    bus_date_year            date            not null,
    store_id                 bigint          not null,
    eticket_id               bigint          not null unique,
    channel_id               bigint          not null,
    channel_name             varchar(50)     not null,
    order_type               varchar(15)     not null,
    order_type_name          varchar(15)     not null,
    order_time               timestamp       not null,
    refunded                 boolean         not null,
    gross_amount             numeric(38, 16) not null,
    net_amount               numeric(38, 16) not null,
    discount_amount          numeric(38, 16) not null,
    tip                      numeric(38, 16) not null,
    package_fee              numeric(38, 16) not null,
    delivery_fee             numeric(38, 16) not null,
    service_fee              numeric(38, 16) not null,
    tax_fee                  numeric(38, 16) not null,
    other_fee                numeric(38, 16) not null,
    pay_amount               numeric(38, 16) not null,
    rounding                 numeric(38, 16) not null,
    overflow_amount          numeric(38, 16) not null,
    change_amount            numeric(38, 16) not null,
    product_count            bigint          not null,
    accessory_count          bigint          not null,
    eticket_count            bigint          not null,
    created                  TIMESTAMP       not null,
    commission               numeric(38, 16) not null,
    amount_0                 numeric(38, 16) not null,
    amount_1                 numeric(38, 16) not null,
    amount_2                 numeric(38, 16) not null,
    amount_3                 numeric(38, 16) not null,
    amount_4                 numeric(38, 16) not null,
    merchant_discount_amount numeric(38, 16) not null,
    platform_discount_amount numeric(38, 16) not null,
    order_status             varchar(20)     not null,
    payment_transfer_amount          numeric(38, 16) not null,
    discount_transfer_amount          numeric(38, 16) not null,
    refund_side              varchar(5)      not null,
    refund_reason            varchar(50)     not null,
    refund_code              bigint          not null
);
ALTER TABLE sales_ticket_amounts
    REPLICA IDENTITY FULL;

-- 产品报表
CREATE TABLE sales_product_amounts
(
    id              bigserial       not null primary key,
    partner_id      bigint          not null,
    scope_id        bigint          not null,
    bus_date        date            not null,
    bus_date_week   date            not null,
    bus_date_month  date            not null,
    bus_date_year   date            not null,
    store_id        bigint          not null,
    eticket_id      bigint          not null,
    channel_id      bigint          not null,
    channel_name    varchar(50)     not null,
    order_type      varchar(15)     not null,
    order_type_name varchar(15)     not null,
    order_time      timestamp       not null,
    refunded        boolean         not null,
    product_id      bigint          not null,
    product_code    varchar(50)     not null,
    sale_type       varchar(20)     not null,
    is_accessory    boolean         not null,
    category_id     bigint          not null,
    qty             bigint          not null,
    gross_amount    numeric(38, 16) not null,
    net_amount      numeric(38, 16) not null,
    discount_amount numeric(38, 16) not null,
    created         TIMESTAMP       not null,
    eticket_count   bigint          not null,
    tax_fee         numeric(38, 16) not null
);
ALTER TABLE sales_product_amounts
    REPLICA IDENTITY FULL;

-- 新增业务需要修改的表结构
alter table sales_ticket_contents
    add "channel_id"      bigint,
    add "channel_name"    varchar(50),
    add "order_type"      varchar(15),
    add "order_type_name" varchar(15),
    alter column ticket_id type varchar(64) using ticket_id::varchar(64);

comment on column sales_ticket_contents.channel_id is '渠道id';
comment on column sales_ticket_contents.channel_name is '渠道名称';
comment on column sales_ticket_contents.order_type is '订单类型';
comment on column sales_ticket_contents.order_type_name is '订单类型名称';
comment on column sales_ticket_contents.ticket_id is '订单id';

-- 门店销售报表的表结构修改如下
alter table sales_ticket_amounts
    add "merchant_discount_amount" numeric(38, 16),
    add "platform_discount_amount" numeric(38, 16),
    add "payment_transfer_amount"  numeric(38, 16),
    add "discount_transfer_amount" numeric(38, 16),
    add "order_status"             varchar(20),
    add "refund_side"              varchar(15),
    add "refund_reason"            varchar(255),
    add "refund_code"              bigint;

comment on column sales_ticket_amounts.merchant_discount_amount is '商家补贴';
comment on column sales_ticket_amounts.platform_discount_amount is '平台补贴';
comment on column sales_ticket_amounts.payment_transfer_amount is '支付转折扣';
comment on column sales_ticket_amounts.discount_transfer_amount is '折扣转支付';
comment on column sales_ticket_amounts.order_status is '订单状态';
comment on column sales_ticket_amounts.refund_side is '退单方';
comment on column sales_ticket_amounts.refund_reason is '退单原因';
comment on column sales_ticket_amounts.refund_code is '退单code';

-- 门店缓存
alter table store_caches
    add store_type  varchar(50),
    add open_status varchar(50),
    add partner_id  bigint,
    add store_name  varchar(100);

comment on column store_caches.store_type is '门店类型';
comment on column store_caches.open_status is '开店状态';
comment on column store_caches.partner_id is '租户id';
comment on column store_caches.store_name is '门店名称';

-- 商品缓存
alter table product_caches
    add partner_id bigint;

comment on column product_caches.partner_id is '租户id';

-- 商品销售报表
alter table sales_product_amounts
    add order_status varchar(20),
    add price        numeric(38, 16),
    add promotion_name varchar(50),
    add promotion_code varchar(50);

comment on column sales_product_amounts.order_status is '订单状态';
comment on column sales_product_amounts.price is '商品价格';
comment on column sales_product_amounts.promotion_name is '促销名称';
comment on column sales_product_amounts.promotion_code is '促销编码';

-- 支付统计报表
alter table sales_payment_amounts
    add order_status varchar(20),
    add channel_id varchar(50),
    add tp_allowance numeric(38,16),
    add real_amount numeric(38,16),
    add transfer_amount numeric(38,16);

comment on column sales_payment_amounts.order_status is '订单状态';
comment on column sales_payment_amounts.channel_id is '渠道id';
comment on column sales_payment_amounts.tp_allowance is '第三方补贴金额';
comment on column sales_payment_amounts.real_amount is '实收金额';
comment on column sales_payment_amounts.transfer_amount is '支付转折扣';

--- 增加折扣销售报表里面需要的数据
alter table sales_discount_amounts
    add order_status varchar(20),
    add product_id bigint,
    add product_code varchar(50),
    add gross_amount numeric(38,16),
    add net_amount numeric(38,16),
    add discount_amount numeric(38,16),
    add merchant_discount_amount numeric(38,16),
    add discount_transfer_amount numeric(38,16),
    add real_amount numeric(38,16),
    add channel_id bigint,
    add channel_name varchar(50),
    add order_type varchar(50);


comment on column sales_discount_amounts.order_status is '订单状态';
comment on column sales_discount_amounts.product_id is '商品id';
comment on column sales_discount_amounts.product_code is '商品code';
comment on column sales_discount_amounts.gross_amount is '商品毛额';
comment on column sales_discount_amounts.net_amount is '实收金额';
comment on column sales_discount_amounts.discount_amount is '折扣金额';
comment on column sales_discount_amounts.merchant_discount_amount is '商家补贴金额';
comment on column sales_discount_amounts.discount_transfer_amount is '折扣转支付';
comment on column sales_discount_amounts.real_amount is '实收金额';
comment on column sales_discount_amounts.channel_id is '渠道id';
comment on column sales_discount_amounts.channel_name is '渠道名称';
comment on column sales_discount_amounts.order_type is '订单类型';

-- 异常电子小票异常原因分析
create table sales_ticket_errmsgs
(
    id           bigserial    not null
        constraint sales_report_ticket_err_pk
            primary key,
    eticket_id   bigint       not null,
    error_msg    varchar(50)  not null,
    channel_id   varchar(50)  not null,
    channel_name varchar(15)  not null,
    store_id     bigint       not null,
    error_info   varchar(255) not null,
    ticket_id    varchar(64)  not null
);

comment on column sales_ticket_errmsgs.id is '异常电子小票异常信息id';
comment on column sales_ticket_errmsgs.eticket_id is 'eticketId';
comment on column sales_ticket_errmsgs.channel_id is '渠道id';
comment on column sales_ticket_errmsgs.channel_name is '渠道名称';
comment on column sales_ticket_errmsgs.store_id is '门店id';
comment on column sales_ticket_errmsgs.error_info is '异常原因详细信息';
comment on column sales_ticket_errmsgs.error_msg is '异常原因';
comment on column sales_ticket_errmsgs.ticket_id is '订单id';

-- 是否是会员订单，true是会员，否则不是
alter table sales_ticket_amounts
    add member bool;

comment on column sales_ticket_amounts.member is '会员标识';

-- 新增字段 2021-07-07 --
alter table sales_ticket_amounts
add discount_platform_contribute numeric(38, 16) default 0,
add discount_buyer_contribute numeric(38, 16) default 0,
add discount_other_contribute numeric(38, 16) default 0,
add pay_platform_contribute numeric(38, 16) default 0,
add pay_buyer_contribute numeric(38, 16) default 0,
add pay_other_contribute numeric(38, 16) default 0,
add send_fee numeric(38, 16) default 0,
add send_fee_for_platform numeric(38, 16) default 0,
add platform_send_fee numeric(38, 16) default 0;

-- 新增字段 2021-07-08 --
alter table sales_payment_amounts
add cost numeric(38,16) default 0;

-- 新增字段 2021-07-09 --
alter table sales_payment_amounts
add merchant_allowance numeric(38,16) default 0;

-- 新增字段 2021-07-12 --
alter table sales_payment_amounts
add finance_pay_amount numeric(38,16) default 0,
add finance_pay_amount_used bool default false

alter table sales_discount_amounts
add cost numeric(38,16) default 0,--(财务)用户实际购买金额
add tp_allowance numeric(38,16) default 0,--(财务)第三方补贴金额
add merchant_allowance numeric(38,16) default 0,--(财务)商家补贴金额
add platform_allowance numeric(38,16) default 0;--(财务)平台补贴金额；

-- 增加下单时间 2021-09-01----
alter table sales_product_amounts
add column start_time timestamp ;
comment on column sales_product_amounts.start_time is '下单时间';

-- 增加商品套餐类型 2021-09-01------
alter table sales_product_amounts
add column combo_type bigint default 0;
comment on column sales_product_amounts.combo_type is '商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品';

--增加字段统计加料、属性等 2021-09-15---
alter table sales_product_amounts
add column accessories varchar(255),
add column sku_remark varchar(255),
add column flavor varchar(255);
comment on column sales_product_amounts.accessories is '加料商品';
comment on column sales_product_amounts.sku_remark is '统计商品所有属性及其属性值';
comment on column sales_product_amounts.flavor is '商品的口味:由所有属性值和所有加料组成';

--增加字段统计折扣金额等 wangheng 2021-10-26
alter table sales_discount_amounts
    add column sum_discount_amount numeric default 0,
add column sum_cost numeric default 0,
add column sum_merchant_allowance numeric default 0,
add column sum_tp_allowance numeric default 0,
add column sum_platform_allowance numeric default 0,
add column sum_discount_transfer_amount numeric default 0,
add column is_accessory bool default false,
add column combo_type int default 0,
add column product_count int default 0;
comment on column sales_discount_amounts.sum_discount_amount is '折扣金额合计';
comment on column sales_discount_amounts.sum_cost is '用户实际购买金额合计';
comment on column sales_discount_amounts.sum_merchant_allowance is '商家补贴购买金额合计';
comment on column sales_discount_amounts.sum_tp_allowance is '第三方补贴金额合计';
comment on column sales_discount_amounts.sum_platform_allowance is '平台补贴金额合计';
comment on column sales_discount_amounts.sum_discount_transfer_amount is '折扣转支付金额合计';
comment on column sales_discount_amounts.is_accessory is '是否是加料商品';
comment on column sales_discount_amounts.combo_type is '商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品';
comment on column sales_discount_amounts.product_count is '商品数量';

--增加promotion_name、payment_name
alter table sales_discount_amounts
    add column promotion_name varchar;
comment on column sales_discount_amounts.promotion_name is '促销名称';
alter table sales_payment_amounts
    add column payment_name varchar;
comment on column sales_payment_amounts.payment_name is '支付名称';



--增加商品单位、重量/份量、是否是称重商品
alter table sales_discount_amounts
    add column unit varchar,
add column weight numeric default 0,
add column has_weight bool default false;
comment on column sales_discount_amounts.unit is '商品单位';
comment on column sales_discount_amounts.weight is '商品重量/份量:has_weight为true表示重量,has_weight为false表示份量';
comment on column sales_discount_amounts.has_weight is '是否是称重商品';

--增加累计付给商家配送费（自配送场景）
alter table sales_ticket_amounts
add column delivery_fee_for_merchant numeric default 0;
comment on column sales_ticket_amounts.delivery_fee_for_merchant is '累计付给商家配送费（自配送场景）';

-- 428-446是计税表sql改动
-- 增加商品税率、支付转折扣、开票金额
alter table sales_product_amounts
    add column transfer_amount numeric default 0,
    add column discount_transfer_amount numeric default 0,
add column product_tax numeric default 0,
add column invoice_amount numeric default 0,
add column natural_date date;
comment on column sales_product_amounts.discount_transfer_amount is '折扣转支付';
comment on column sales_product_amounts.transfer_amount is '支付转折扣';
comment on column sales_product_amounts.product_tax is '商品税率';
comment on column sales_product_amounts.invoice_amount is '开票金额';
comment on column sales_product_amounts.natural_date is '自然日';

--增加配送费税率、包装费税率、自然日
alter table sales_ticket_amounts
    add column natural_date date,
add column send_fee_rate numeric default 0,
add column package_fee_rate numeric default 0;
comment on column sales_ticket_amounts.send_fee_rate is '配送费税率';
comment on column sales_ticket_amounts.package_fee_rate is '包装费税率';
comment on column sales_ticket_amounts.natural_date is '自然日';

ALTER TABLE "public"."sales_product_amounts"
    ADD COLUMN "finance_real_amount" numeric DEFAULT 0;

COMMENT ON COLUMN "public"."sales_product_amounts"."finance_real_amount" IS '财务实收金额';

ALTER TABLE "public"."sales_product_amounts"
    ADD COLUMN "transfer_amount" numeric DEFAULT 0;

COMMENT ON COLUMN "public"."sales_product_amounts"."transfer_amount" IS '支付转折扣';

ALTER TABLE "public"."sales_product_amounts"
    ADD COLUMN "invoice_amount" numeric DEFAULT 0;

COMMENT ON COLUMN "public"."sales_product_amounts"."invoice_amount" IS '开票金额';
>>>>>>> features/2022_03_15_for_xicha_product_tax
