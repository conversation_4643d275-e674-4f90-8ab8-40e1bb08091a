-- =============================================
-- Author: 杜森
-- Create date: 20200903
-- Description: 预计算表结构
-- Modify [n]: 20201026
-- =============================================

-- 门店交易报表
CREATE TABLE store_sales
(
    bus_date date,
    store_id bigint,
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    gross_amount numeric(38, 16),
    net_amount numeric(38, 16),
    discount_amount numeric(38, 16),
    tip numeric(38, 16),
    package_fee numeric(38, 16),
    delivery_fee numeric(38, 16),
    service_fee numeric(38, 16),
    tax_fee numeric(38, 16),
    other_fee numeric(38, 16),
    pay_amount numeric(38, 16),
    rounding numeric(38, 16),
    overflow_amount numeric(38, 16),
    change_amount numeric(38, 16),
    order_count bigint,
    product_count bigint,
    accessory_count bigint,
    gross_amount_returned numeric(38, 16),
    net_amount_returned numeric(38, 16),
    discount_amount_returned numeric(38, 16),
    tip_returned numeric(38, 16),
    package_fee_returned numeric(38, 16),
    delivery_fee_returned numeric(38, 16),
    service_fee_returned numeric(38, 16),
    tax_fee_returned numeric(38, 16),
    other_fee_returned numeric(38, 16),
    pay_amount_returned numeric(38, 16),
    rounding_returned numeric(38, 16),
    overflow_amount_returned numeric(38, 16),
    change_amount_returned numeric(38, 16),
    order_count_returned bigint,
    commission numeric(38, 16),
    amount_0 numeric(38, 16),
    amount_1 numeric(38, 16),
    amount_2 numeric(38, 16),
    amount_3 numeric(38, 16),
    amount_4 numeric(38, 16),
    commission_returned numeric(38, 16),
    amount_0_returned numeric(38, 16),
    amount_1_returned numeric(38, 16),
    amount_2_returned numeric(38, 16),
    amount_3_returned numeric(38, 16),
    amount_4_returned numeric(38, 16),
    PRIMARY KEY (bus_date, store_id, partner_id, scope_id)
);

-- 门店渠道报表
CREATE TABLE store_channel_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    channel_id BIGINT,
    order_type varchar(15),
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    gross_amount DECIMAL(38, 16),
    net_amount DECIMAL(38, 16),
    discount_amount DECIMAL(38, 16),
    tip DECIMAL(38, 16),
    package_fee DECIMAL(38, 16),
    delivery_fee DECIMAL(38, 16),
    service_fee DECIMAL(38, 16),
    tax_fee DECIMAL(38, 16),
    other_fee DECIMAL(38, 16),
    pay_amount DECIMAL(38, 16),
    rounding DECIMAL(38, 16),
    overflow_amount DECIMAL(38, 16),
    change_amount DECIMAL(38, 16),
    order_count BIGINT,
    gross_amount_returned DECIMAL(38, 16),
    net_amount_returned DECIMAL(38, 16),
    discount_amount_returned DECIMAL(38, 16),
    tip_returned DECIMAL(38, 16),
    package_fee_returned DECIMAL(38, 16),
    delivery_fee_returned DECIMAL(38, 16),
    service_fee_returned DECIMAL(38, 16),
    tax_fee_returned DECIMAL(38, 16),
    other_fee_returned DECIMAL(38, 16),
    pay_amount_returned DECIMAL(38, 16),
    rounding_returned DECIMAL(38, 16),
    overflow_amount_returned DECIMAL(38, 16),
    change_amount_returned DECIMAL(38, 16),
    order_count_returned BIGINT,
    commission numeric(38, 16),
    amount_0 numeric(38, 16),
    amount_1 numeric(38, 16),
    amount_2 numeric(38, 16),
    amount_3 numeric(38, 16),
    amount_4 numeric(38, 16),
    commission_returned numeric(38, 16),
    amount_0_returned numeric(38, 16),
    amount_1_returned numeric(38, 16),
    amount_2_returned numeric(38, 16),
    amount_3_returned numeric(38, 16),
    amount_4_returned numeric(38, 16),
    PRIMARY KEY (bus_date, store_id, channel_id, order_type, partner_id, scope_id)
);

-- 门店时段报表
CREATE TABLE store_period_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    net_amount_00 DECIMAL(38, 16),
    net_amount_01 DECIMAL(38, 16),
    net_amount_02 DECIMAL(38, 16),
    net_amount_03 DECIMAL(38, 16),
    net_amount_04 DECIMAL(38, 16),
    net_amount_05 DECIMAL(38, 16),
    net_amount_06 DECIMAL(38, 16),
    net_amount_07 DECIMAL(38, 16),
    net_amount_08 DECIMAL(38, 16),
    net_amount_09 DECIMAL(38, 16),
    net_amount_10 DECIMAL(38, 16),
    net_amount_11 DECIMAL(38, 16),
    net_amount_12 DECIMAL(38, 16),
    net_amount_13 DECIMAL(38, 16),
    net_amount_14 DECIMAL(38, 16),
    net_amount_15 DECIMAL(38, 16),
    net_amount_16 DECIMAL(38, 16),
    net_amount_17 DECIMAL(38, 16),
    net_amount_18 DECIMAL(38, 16),
    net_amount_19 DECIMAL(38, 16),
    net_amount_20 DECIMAL(38, 16),
    net_amount_21 DECIMAL(38, 16),
    net_amount_22 DECIMAL(38, 16),
    net_amount_23 DECIMAL(38, 16),
    order_count_00 BIGINT,
    order_count_01 BIGINT,
    order_count_02 BIGINT,
    order_count_03 BIGINT,
    order_count_04 BIGINT,
    order_count_05 BIGINT,
    order_count_06 BIGINT,
    order_count_07 BIGINT,
    order_count_08 BIGINT,
    order_count_09 BIGINT,
    order_count_10 BIGINT,
    order_count_11 BIGINT,
    order_count_12 BIGINT,
    order_count_13 BIGINT,
    order_count_14 BIGINT,
    order_count_15 BIGINT,
    order_count_16 BIGINT,
    order_count_17 BIGINT,
    order_count_18 BIGINT,
    order_count_19 BIGINT,
    order_count_20 BIGINT,
    order_count_21 BIGINT,
    order_count_22 BIGINT,
    order_count_23 BIGINT,
    PRIMARY KEY (bus_date, store_id, partner_id, scope_id)
);

-- 单品交易报表
CREATE TABLE product_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    product_id BIGINT,
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    gross_amount DECIMAL(38, 16), 
    gross_price DECIMAL(38, 16), 
    net_amount DECIMAL(38, 16), 
    net_price DECIMAL(38, 16), 
    discount_amount DECIMAL(38, 16), 
    item_count BIGINT, 
    gross_amount_returned DECIMAL(38, 16), 
    net_amount_returned DECIMAL(38, 16), 
    discount_amount_returned DECIMAL(38, 16), 
    item_count_returned BIGINT,
    tax_fee DECIMAL(38, 16), 
    tax_fee_returned DECIMAL(38, 16), 
    PRIMARY KEY (bus_date, store_id, product_id, partner_id, scope_id)
);

-- 单品渠道报表
CREATE TABLE product_channel_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    product_id BIGINT, 
    channel_id BIGINT,
    order_type varchar(15),
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    gross_amount DECIMAL(38, 16), 
    gross_price DECIMAL(38, 16), 
    net_amount DECIMAL(38, 16), 
    net_price DECIMAL(38, 16),
    discount_amount DECIMAL(38, 16),
    item_count BIGINT,
    gross_amount_returned DECIMAL(38, 16),
    net_amount_returned DECIMAL(38, 16),
    discount_amount_returned DECIMAL(38, 16),
    item_count_returned BIGINT,
    tax_fee DECIMAL(38, 16), 
    tax_fee_returned DECIMAL(38, 16), 
    PRIMARY KEY (bus_date, store_id, product_id, channel_id, order_type, partner_id, scope_id)
);

-- 单品时段报表
CREATE TABLE product_period_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    product_id BIGINT, 
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    net_amount_00 DECIMAL(38, 16),
    net_amount_01 DECIMAL(38, 16),
    net_amount_02 DECIMAL(38, 16),
    net_amount_03 DECIMAL(38, 16),
    net_amount_04 DECIMAL(38, 16),
    net_amount_05 DECIMAL(38, 16),
    net_amount_06 DECIMAL(38, 16),
    net_amount_07 DECIMAL(38, 16),
    net_amount_08 DECIMAL(38, 16),
    net_amount_09 DECIMAL(38, 16),
    net_amount_10 DECIMAL(38, 16),
    net_amount_11 DECIMAL(38, 16),
    net_amount_12 DECIMAL(38, 16),
    net_amount_13 DECIMAL(38, 16),
    net_amount_14 DECIMAL(38, 16),
    net_amount_15 DECIMAL(38, 16),
    net_amount_16 DECIMAL(38, 16),
    net_amount_17 DECIMAL(38, 16),
    net_amount_18 DECIMAL(38, 16),
    net_amount_19 DECIMAL(38, 16),
    net_amount_20 DECIMAL(38, 16),
    net_amount_21 DECIMAL(38, 16),
    net_amount_22 DECIMAL(38, 16),
    net_amount_23 DECIMAL(38, 16),
    order_count_00 BIGINT,
    order_count_01 BIGINT,
    order_count_02 BIGINT,
    order_count_03 BIGINT,
    order_count_04 BIGINT,
    order_count_05 BIGINT,
    order_count_06 BIGINT,
    order_count_07 BIGINT,
    order_count_08 BIGINT,
    order_count_09 BIGINT,
    order_count_10 BIGINT,
    order_count_11 BIGINT,
    order_count_12 BIGINT,
    order_count_13 BIGINT,
    order_count_14 BIGINT,
    order_count_15 BIGINT,
    order_count_16 BIGINT,
    order_count_17 BIGINT,
    order_count_18 BIGINT,
    order_count_19 BIGINT,
    order_count_20 BIGINT,
    order_count_21 BIGINT,
    order_count_22 BIGINT,
    order_count_23 BIGINT,
    PRIMARY KEY (bus_date, store_id, product_id, partner_id, scope_id)
);

-- 折扣交易报表
CREATE TABLE discount_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    promotion_id BIGINT,
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    
    amount DECIMAL(38, 16),
    amount_returned DECIMAL(38, 16),
    item_count BIGINT,
    item_count_returned BIGINT,
    eticket_count BIGINT,
    eticket_count_returned BIGINT,

    PRIMARY KEY (bus_date, store_id, promotion_id, partner_id, scope_id)
);

-- 支付统计报表
CREATE TABLE payment_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    payment_id BIGINT,
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    receivable DECIMAL(38, 16),
    pay_amount DECIMAL(38, 16),
    rounding DECIMAL(38, 16),
    overflow_amount DECIMAL(38, 16),
    change_amount DECIMAL(38, 16),
    net_amount DECIMAL(38, 16),
    receivable_returned DECIMAL(38, 16),
    pay_amount_returned DECIMAL(38, 16),
    rounding_returned DECIMAL(38, 16),
    overflow_amount_returned DECIMAL(38, 16),
    change_amount_returned DECIMAL(38, 16),
    net_amount_returned DECIMAL(38, 16),
    item_count BIGINT,
    item_count_returned BIGINT,
    eticket_count BIGINT,
    eticket_count_returned BIGINT,
    PRIMARY KEY (bus_date, store_id, payment_id, partner_id, scope_id)
);

-- 支付时段报表
CREATE TABLE payment_period_sales
(
    bus_date DATE, 
    store_id BIGINT, 
    payment_id BIGINT,
    partner_id bigint,
    scope_id   bigint,
    bus_date_week  date,
    bus_date_month date,
    bus_date_year  date,
    net_amount_00 DECIMAL(38, 16),
    net_amount_01 DECIMAL(38, 16),
    net_amount_02 DECIMAL(38, 16),
    net_amount_03 DECIMAL(38, 16),
    net_amount_04 DECIMAL(38, 16),
    net_amount_05 DECIMAL(38, 16),
    net_amount_06 DECIMAL(38, 16),
    net_amount_07 DECIMAL(38, 16),
    net_amount_08 DECIMAL(38, 16),
    net_amount_09 DECIMAL(38, 16),
    net_amount_10 DECIMAL(38, 16),
    net_amount_11 DECIMAL(38, 16),
    net_amount_12 DECIMAL(38, 16),
    net_amount_13 DECIMAL(38, 16),
    net_amount_14 DECIMAL(38, 16),
    net_amount_15 DECIMAL(38, 16),
    net_amount_16 DECIMAL(38, 16),
    net_amount_17 DECIMAL(38, 16),
    net_amount_18 DECIMAL(38, 16),
    net_amount_19 DECIMAL(38, 16),
    net_amount_20 DECIMAL(38, 16),
    net_amount_21 DECIMAL(38, 16),
    net_amount_22 DECIMAL(38, 16),
    net_amount_23 DECIMAL(38, 16),
    order_count_00 BIGINT,
    order_count_01 BIGINT,
    order_count_02 BIGINT,
    order_count_03 BIGINT,
    order_count_04 BIGINT,
    order_count_05 BIGINT,
    order_count_06 BIGINT,
    order_count_07 BIGINT,
    order_count_08 BIGINT,
    order_count_09 BIGINT,
    order_count_10 BIGINT,
    order_count_11 BIGINT,
    order_count_12 BIGINT,
    order_count_13 BIGINT,
    order_count_14 BIGINT,
    order_count_15 BIGINT,
    order_count_16 BIGINT,
    order_count_17 BIGINT,
    order_count_18 BIGINT,
    order_count_19 BIGINT,
    order_count_20 BIGINT,
    order_count_21 BIGINT,
    order_count_22 BIGINT,
    order_count_23 BIGINT,
    PRIMARY KEY (bus_date, store_id, payment_id, partner_id, scope_id)
);