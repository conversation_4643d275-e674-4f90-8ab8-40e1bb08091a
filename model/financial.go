package model

type Financial struct {
	BusDate              string  `json:"bus_date"`
	RegionId             int64   `json:"region_id"`               // 门店id
	BranchRegionId       int64   `json:"branch_region_id"`        // 管理区域
	BranchRegion         string  `json:"branch_region"`           // 管理区域
	RegionCode           string  `json:"region_code"`             // 门店code
	RegionName           string  `json:"region_name"`             // --店铺名称,
	RegionAddress        string  `json:"region_address"`          // 门店所在城市
	RegionAddress1       string  `json:"region_address1"`         // 门店所在城市
	RegionAddress2       string  `json:"region_address2"`         // 门店所在城市
	RegionAddress3       string  `json:"region_address3"`         // 门店所在城市
	RegionAlias          string  `json:"region_alias"`            // 门店别名
	BusinessAmount       float64 `json:"business_amount"`         // --流水金额（营业流水）
	DiscountAmount       float64 `json:"discount_amount"`         // --优惠金额（优惠组成）
	RealAmount           float64 `json:"real_amount"`             //--实收金额（实收组成）
	RealAmountWithoutGST float64 `json:"real_amount_without_gst"` //--实收不含税
	ValidOrderCount      int64   `json:"valid_order_count"`       // --账单数
	CustomerPrice        float64 `json:"customer_price"`          // --单均消费

	CustomerCount          float64 `json:"customer_count"`         // 客流
	BowlCount              float64 `json:"bowl_count"`             // 碗数
	Tip                    float64 `json:"tip"`                    // 小费
	TaxFee                 float64 `json:"tax_fee"`                // 税费
	RealAmountPerCapita    float64 `json:"real_amount_per_capita"` // 人均消费: 实收金额/客流
	CustomerCountPerDay    float64 `json:"customer_count_per_day"` // 日均客流: 客流/营业天数
	RealAmountPerDay       float64 `json:"real_amount_per_day"`    // 日均实收: 实收金额/营业天数
	RealAmountPerBowl      float64 `json:"real_amount_per_bowl"`   //
	CompositionOfBillTypes []struct {
		OrderType   string  `json:"order_type"`
		OrderName   string  `json:"order_name"`
		GrossAmount float64 `json:"gross_amount"`
		OrderCount  int64   `json:"order_count"`
		RealAmount  float64 `json:"real_amount"`
	} `json:"composition_of_bill_types"` // --账单类型组成
	CompositionOfTakeAway []struct {
		ChannelId   int64   `json:"channel_id"`
		ChannelName string  `json:"channel_name"`
		GrossAmount float64 `json:"gross_amount"`
		OrderCount  int64   `json:"order_count"`
		RealAmount  float64 `json:"real_amount"`
	} `json:"composition_of_take_away"` // --外卖渠道组成
	CompositionOfPaidIn []struct {
		ChannelId       int64   `json:"channel_id"`
		ChannelName     string  `json:"channel_name"`
		Commission      float64 `json:"commission"`
		SurChargeAmount float64 `json:"surcharge_amount"`
		Discounts       []struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"discounts"`
		Payments []struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"payments"`
		TransferRealAmountTotalPayments  float64 `json:"transfer_real_amount_total_payments"`
		TransferRealAmountTotalDiscounts float64 `json:"transfer_real_amount_total_discounts"`
	} `json:"composition_of_paid_in"` // --实收组成
	CompositionOfPaidInTotal float64 `json:"composition_of_paid_in_total"` // --实收组成小计
	CompositionOfDiscount    []struct {
		ChannelId          int64   `json:"channel_id"`
		ChannelName        string  `json:"channel_name"`
		Commission         float64 `json:"commission"`
		SendFeeForMerchant float64 `json:"send_fee_for_merchant"`
		OtherFee           float64 `json:"other_fee"`
		Payments           []struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"payments"`
		Discounts []struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"discounts"`
	} `json:"composition_of_discount"` // --优惠组成
	CompositionOfDiscountTotal float64 `json:"composition_of_discount_total"` // --优惠组成小计

	// 餐段组成
	MealSegments []struct {
		MealSegmentName string  `json:"meal_segment_name"`
		GrossAmount     float64 `json:"gross_amount"`
		RealAmount      float64 `json:"real_amount"`
		ValidOrderCount float64 `json:"valid_order_count"`
	} `json:"meal_segments"`

	Total int    `json:"total,omitempty"` //summary时的汇总条数
	Tag   string `json:"tag"`             // 区分详细还是汇总信息
}

type FinancialResponse struct {
	Summary *Financial   `json:"summary"` //汇总
	Rows    []*Financial `json:"rows"`    //返回记录
}
