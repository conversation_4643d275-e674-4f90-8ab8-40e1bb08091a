package model

import "time"

type ExchangeRate struct {
	Id           int64     `json:"id"`
	PartnerId    int64     `json:"partner_id"`
	FromCurrency string    `json:"from_currency"`
	ToCurrency   string    `json:"to_currency"`
	Rate         float64   `json:"rate"`
	StartDate    time.Time `json:"start_date"`
	EndDate      time.Time `json:"end_date"`
	Deleted      bool      `json:"deleted"`
	CreateAt     time.Time `json:"create_at"`
	UpdateAt     time.Time `json:"update_at"`
}

func (m *ExchangeRate) TableName() string {
	return "exchange_rate"
}
