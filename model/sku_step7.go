package model

import (
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func step1Calc(products []*TicketProduct) (skuList []*TicketProduct) {
	for _, product := range products {
		// 商品 和 加料 各算作一个sku
		skuList = append(skuList, product)
		for _, accessory := range product.Accessories {
			skuList = append(skuList, accessory)
		}

		for _, comboItem := range product.ComboItems {
			for _, accessory := range comboItem.Accessories {
				// 子项商品的加料
				skuList = append(skuList, accessory)
			}
		}
	}
	return skuList
}

// 商品分摊金额
func step7Calc(ticket *Ticket, stepSkuList []*TicketProduct) []*TicketProduct {
	// 整单折扣分摊
	var merchantDiscountAmount float64
	productsDiscountMap := map[string]float64{}
	for _, pro := range ticket.Promotions {
		if len(pro.Products) == 0 {
			merchantDiscountAmount += pro.Source.MerchantDiscount
		} else {
			for _, product := range pro.Products {
				_, ok := productsDiscountMap[product.KeyId]
				if ok {
					productsDiscountMap[product.KeyId] = productsDiscountMap[product.KeyId] + product.Discount
				} else {
					productsDiscountMap[product.KeyId] = product.Discount
				}
			}
		}
	}
	allocToProduct(stepSkuList, merchantDiscountAmount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.MerchantDiscountAmount, _ = decimal.NewFromFloat(sku.MerchantDiscountAmount).Add(amountVal).Round(2).Float64()
			sku.SumDiscountAmount, _ = decimal.NewFromFloat(sku.SumDiscountAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.MerchantDiscountAmount, _ = amountVal.Round(2).Float64()
			sku.SumDiscountAmount, _ = amountVal.Round(2).Float64()
		}
	})
	//单商品分摊 todo
	for i, product := range stepSkuList {
		discount, ok := productsDiscountMap[cast.ToString(product.Id)]
		if ok {
			stepSkuList[i].MerchantDiscountAmount, _ = decimal.NewFromFloat(discount).Round(2).Float64()
			stepSkuList[i].SumDiscountAmount, _ = decimal.NewFromFloat(discount).Round(2).Float64()
			//setValueFunc(product, discount, false)
		} else {
			stepSkuList[i].MerchantDiscountAmount, _ = decimal.NewFromFloat(product.DiscountAmount).Round(2).Float64()
			stepSkuList[i].SumDiscountAmount, _ = decimal.NewFromFloat(product.DiscountAmount).Round(2).Float64()
		}
	}
	logger.Pre().Infoln("productsDiscountMap：", productsDiscountMap)
	logger.Pre().Infoln("stepSkuList：", stepSkuList)

	// pos订单使用商品本身携带的包装费
	if !ticket.isPos() {
		allocToProduct(stepSkuList, ticket.TakeawayInfo.PackageFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
			if isAdd {
				sku.PackageAmount, _ = decimal.NewFromFloat(sku.PackageAmount).Add(amountVal).Round(2).Float64()
			} else {
				sku.PackageAmount, _ = amountVal.Round(2).Float64()
			}
		})
	}

	allocToProduct(stepSkuList, ticket.Amounts.ServiceFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.ServiceFee, _ = decimal.NewFromFloat(sku.ServiceFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.ServiceFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.Amounts.Tip, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.Tip, _ = decimal.NewFromFloat(sku.Tip).Add(amountVal).Round(2).Float64()
		} else {
			sku.Tip, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.Amounts.OtherFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.OtherFee, _ = decimal.NewFromFloat(sku.OtherFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.OtherFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.Amounts.Rounding, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.Rounding, _ = decimal.NewFromFloat(sku.Rounding).Add(amountVal).Round(2).Float64()
		} else {
			sku.Rounding, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.Amounts.OverflowAmount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.OverflowAmount, _ = decimal.NewFromFloat(sku.OverflowAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.OverflowAmount, _ = amountVal.Round(2).Float64()
		}
	})
	//
	//allocToProduct(stepSkuList, ticket.Amounts.Commission, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
	//	if isAdd {
	//		sku.Commission, _ = decimal.NewFromFloat(sku.Commission).Add(amountVal).Round(2).Float64()
	//	} else {
	//		sku.Commission, _ = amountVal.Round(2).Float64()
	//	}
	//})

	allocToProduct(stepSkuList, ticket.TakeawayInfo.MerchantSendFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.MerchantSendFee, _ = decimal.NewFromFloat(sku.MerchantSendFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.MerchantSendFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.TakeawayInfo.SendFeeForMerchant, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.SendFeeForMerchant, _ = decimal.NewFromFloat(sku.SendFeeForMerchant).Add(amountVal).Round(2).Float64()
		} else {
			sku.SendFeeForMerchant, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, ticket.TakeawayInfo.SendFeeForPlatform, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.SendFeeForPlatform, _ = decimal.NewFromFloat(sku.SendFeeForPlatform).Add(amountVal).Round(2).Float64()
		} else {
			sku.SendFeeForPlatform, _ = amountVal.Round(2).Float64()
		}
	})

	totalPayCost := float64(0)
	totalPayTpAllowance := float64(0)
	totalPayMerchantAllowance := float64(0)
	totalPayPlatformAllowance := float64(0)
	for _, item := range ticket.Payments {
		totalPayCost += item.Cost
		totalPayTpAllowance += item.TpAllowance
		totalPayMerchantAllowance += item.MerchantAllowance
		totalPayPlatformAllowance += item.PlatformAllowance
	}

	totalPromotionCost := float64(0)
	totalPromotionTpAllowance := float64(0)
	totalPromotionMerchantAllowance := float64(0)
	totalPromotionPlatformAllowance := float64(0)
	for _, item := range ticket.Promotions {
		totalPromotionCost += item.Source.Cost
		totalPromotionTpAllowance += item.Source.TpAllowance
		totalPromotionMerchantAllowance += item.Source.MerchantAllowance
		totalPromotionPlatformAllowance += item.Source.PlatformAllowance
	}

	allocToProduct(stepSkuList, totalPayCost, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayCost, _ = decimal.NewFromFloat(sku.PayCost).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayCost, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPayTpAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayTpAllowance, _ = decimal.NewFromFloat(sku.PayTpAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayTpAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPayMerchantAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayMerchantAllowance, _ = decimal.NewFromFloat(sku.PayMerchantAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayMerchantAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPayPlatformAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayPlatformAllowance, _ = decimal.NewFromFloat(sku.PayPlatformAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayPlatformAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPromotionCost, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionCost, _ = decimal.NewFromFloat(sku.PromotionCost).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionCost, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPromotionTpAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionTpAllowance, _ = decimal.NewFromFloat(sku.PromotionTpAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionTpAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPromotionMerchantAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionMerchantAllowance, _ = decimal.NewFromFloat(sku.PromotionMerchantAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionMerchantAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, totalPromotionPlatformAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionPlatformAllowance, _ = decimal.NewFromFloat(sku.PromotionPlatformAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionPlatformAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	return stepSkuList
}

// 套餐子商品
func step7SubCalc(parentSku *TicketProduct, stepSkuList []*TicketProduct) []*TicketProduct {

	allocToProduct(stepSkuList, parentSku.MerchantDiscountAmount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.MerchantDiscountAmount, _ = decimal.NewFromFloat(sku.MerchantDiscountAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.MerchantDiscountAmount, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.Amount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.SubAmount, _ = decimal.NewFromFloat(sku.SubAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.SubAmount, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PackageAmount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PackageAmount, _ = decimal.NewFromFloat(sku.PackageAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.PackageAmount, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.ServiceFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.ServiceFee, _ = decimal.NewFromFloat(sku.ServiceFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.ServiceFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.Tip, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.Tip, _ = decimal.NewFromFloat(sku.Tip).Add(amountVal).Round(2).Float64()
		} else {
			sku.Tip, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.OtherFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.OtherFee, _ = decimal.NewFromFloat(sku.OtherFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.OtherFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.Rounding, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.Rounding, _ = decimal.NewFromFloat(sku.Rounding).Add(amountVal).Round(2).Float64()
		} else {
			sku.Rounding, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.OverflowAmount, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.OverflowAmount, _ = decimal.NewFromFloat(sku.OverflowAmount).Add(amountVal).Round(2).Float64()
		} else {
			sku.OverflowAmount, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.Commission, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.Commission, _ = decimal.NewFromFloat(sku.Commission).Add(amountVal).Round(2).Float64()
		} else {
			sku.Commission, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.MerchantSendFee, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.MerchantSendFee, _ = decimal.NewFromFloat(sku.MerchantSendFee).Add(amountVal).Round(2).Float64()
		} else {
			sku.MerchantSendFee, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.SendFeeForMerchant, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.SendFeeForMerchant, _ = decimal.NewFromFloat(sku.SendFeeForMerchant).Add(amountVal).Round(2).Float64()
		} else {
			sku.SendFeeForMerchant, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.SendFeeForPlatform, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.SendFeeForPlatform, _ = decimal.NewFromFloat(sku.SendFeeForPlatform).Add(amountVal).Round(2).Float64()
		} else {
			sku.SendFeeForPlatform, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PayCost, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayCost, _ = decimal.NewFromFloat(sku.PayCost).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayCost, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PayTpAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayTpAllowance, _ = decimal.NewFromFloat(sku.PayTpAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayTpAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PayMerchantAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayMerchantAllowance, _ = decimal.NewFromFloat(sku.PayMerchantAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayMerchantAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PayPlatformAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PayPlatformAllowance, _ = decimal.NewFromFloat(sku.PayPlatformAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PayPlatformAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PromotionCost, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionCost, _ = decimal.NewFromFloat(sku.PromotionCost).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionCost, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PromotionTpAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionTpAllowance, _ = decimal.NewFromFloat(sku.PromotionTpAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionTpAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PromotionMerchantAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionMerchantAllowance, _ = decimal.NewFromFloat(sku.PromotionMerchantAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionMerchantAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	allocToProduct(stepSkuList, parentSku.PromotionPlatformAllowance, func(sku *TicketProduct, amountVal decimal.Decimal, isAdd bool) {
		if isAdd {
			sku.PromotionPlatformAllowance, _ = decimal.NewFromFloat(sku.PromotionPlatformAllowance).Add(amountVal).Round(2).Float64()
		} else {
			sku.PromotionPlatformAllowance, _ = amountVal.Round(2).Float64()
		}
	})

	return stepSkuList
}
