package report_v2

import "github.com/spf13/cast"

type ProductChannelSalesResponseV2 struct {
	Summary *ProductChannelSalesV2
	Rows    []*ProductChannelSalesV2
	Total   int64
}

type ProductChannelSalesV2 struct {
	BusDate                string  `json:"bus_date"`
	GeoId                  int64   `json:"geo_id"`
	GeoCode                string  `json:"geo_code"`
	GeoName                string  `json:"geo_name"`
	BranchId               int64   `json:"branch_id"`
	BranchCode             string  `json:"branch_code"`
	BranchName             string  `json:"branch_name"`
	CompanyId              int64   `json:"company_id"`
	CompanyCode            string  `json:"company_code"`
	CompanyName            string  `json:"company_name"`
	RegionId               int64   `json:"region_id"`
	RegionCode             string  `json:"region_code"`
	RegionName             string  `json:"region_name"`
	RegionAddress          string  `json:"region_address"`
	RegionAlias            string  `json:"region_alias"`
	StoreType              string  `json:"store_type"`
	StoreTypeName          string  `json:"store_type_name"`
	BusinessDays           int64   `json:"business_days"`
	ProductCategoryId      int64   `json:"product_category_id"`
	ProductCategoryId1     int64   `json:"product_category_id1"`
	ProductCategoryId2     int64   `json:"product_category_id2"`
	ProductCategoryId3     int64   `json:"product_category_id3"`
	ProductCategoryId4     int64   `json:"product_category_id4"`
	ProductCategoryCode    string  `json:"product_category_code"`
	ProductCategoryName    string  `json:"product_category_name"`
	ProductCategoryCode1   string  `json:"product_category_code1"`
	ProductCategoryName1   string  `json:"product_category_name1"`
	ProductCategoryCode2   string  `json:"product_category_code2"`
	ProductCategoryName2   string  `json:"product_category_name2"`
	ProductCategoryCode3   string  `json:"product_category_code3"`
	ProductCategoryName3   string  `json:"product_category_name3"`
	ProductCategoryCode4   string  `json:"product_category_code4"`
	ProductCategoryName4   string  `json:"product_category_name4"`
	ProductId              int64   `json:"product_id"`
	ProductCode            string  `json:"product_code"`
	ProductName            string  `json:"product_name"`
	ChannelId              int64   `json:"channel_id"`
	ChannelCode            string  `json:"channel_code"`
	ChannelName            string  `json:"channel_name"`
	OrderType              string  `json:"order_type"`
	WeightCount            string  `json:"weight_count"`
	GrossAmount            float64 `json:"gross_amount"`
	NetAmount              float64 `json:"net_amount"`
	DiscountAmount         float64 `json:"discount_amount"`
	ItemCount              int64   `json:"item_count"`
	ProductAveragePrice    float64 `json:"product_average_price"`
	WeightCountReturned    string  `json:"weight_count_returned"`
	GrossAmountReturned    float64 `json:"gross_amount_returned"`
	NetAmountReturned      float64 `json:"net_amount_returned"`
	DiscountAmountReturned float64 `json:"discount_amount_returned"`
	ItemCountReturned      int64   `json:"item_count_returned"`
	Total                  int64   `json:"total"`
}

func (p *ProductChannelSalesV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                 p.BusDate,
		"geo_id":                   cast.ToString(p.GeoId),
		"geo_code":                 p.GeoCode,
		"geo_name":                 p.GeoName,
		"branch_id":                cast.ToString(p.BranchId),
		"branch_code":              p.BranchCode,
		"branch_name":              p.BranchName,
		"company_id":               cast.ToString(p.CompanyId),
		"company_code":             p.CompanyCode,
		"company_name":             p.CompanyName,
		"region_id":                cast.ToString(p.RegionId),
		"region_code":              p.RegionCode,
		"region_name":              p.RegionName,
		"region_address":           p.RegionAddress,
		"region_alias":             p.RegionAlias,
		"store_type":               p.StoreType,
		"store_type_name":          p.StoreTypeName,
		"business_days":            p.BusinessDays,
		"product_id":               cast.ToString(p.ProductId),
		"product_code":             p.ProductCode,
		"product_name":             p.ProductName,
		"product_category_id":      cast.ToString(p.ProductCategoryId),
		"product_category_code":    p.ProductCategoryCode,
		"product_category_name":    p.ProductCategoryName,
		"product_category_id1":     cast.ToString(p.ProductCategoryId1),
		"product_category_code1":   p.ProductCategoryCode1,
		"product_category_name1":   p.ProductCategoryName1,
		"product_category_id2":     cast.ToString(p.ProductCategoryId2),
		"product_category_code2":   p.ProductCategoryCode2,
		"product_category_name2":   p.ProductCategoryName2,
		"product_category_id3":     cast.ToString(p.ProductCategoryId3),
		"product_category_code3":   p.ProductCategoryCode3,
		"product_category_name3":   p.ProductCategoryName3,
		"product_category_id4":     cast.ToString(p.ProductCategoryId4),
		"product_category_code4":   p.ProductCategoryCode4,
		"product_category_name4":   p.ProductCategoryName4,
		"channel_id":               cast.ToString(p.ChannelId),
		"channel_code":             p.ChannelCode,
		"channel_name":             p.ChannelName,
		"order_type":               p.OrderType,
		"weight_count":             p.WeightCount,
		"weight_count_returned":    p.WeightCountReturned,
		"gross_amount":             SaveTwoEffectiveDigits(p.GrossAmount),
		"net_amount":               SaveTwoEffectiveDigits(p.NetAmount),
		"discount_amount":          SaveTwoEffectiveDigits(p.DiscountAmount),
		"item_count":               p.ItemCount,
		"product_average_price":    SaveTwoEffectiveDigits(p.ProductAveragePrice),
		"gross_amount_returned":    SaveTwoEffectiveDigits(p.GrossAmountReturned),
		"net_amount_returned":      SaveTwoEffectiveDigits(p.NetAmountReturned),
		"discount_amount_returned": SaveTwoEffectiveDigits(p.DiscountAmountReturned),
		"item_count_returned":      p.ItemCountReturned,
		"total":                    p.Total,
	}
}
