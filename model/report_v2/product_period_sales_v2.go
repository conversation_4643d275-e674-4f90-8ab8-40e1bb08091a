package report_v2

import "github.com/spf13/cast"

type ProductPeriodSalesResponseV2 struct {
	Summary *ProductPeriodSalesV2
	Rows    []*ProductPeriodSalesV2
	Total   int64
}

type ProductPeriodSalesV2 struct {
	BusDate              string  `json:"bus_date"`
	GeoId                int64   `json:"geo_id"`
	GeoCode              string  `json:"geo_code"`
	GeoName              string  `json:"geo_name"`
	BranchId             int64   `json:"branch_id"`
	BranchCode           string  `json:"branch_code"`
	BranchName           string  `json:"branch_name"`
	CompanyId            int64   `json:"company_id"`
	CompanyCode          string  `json:"company_code"`
	CompanyName          string  `json:"company_name"`
	RegionId             int64   `json:"region_id"`
	RegionCode           string  `json:"region_code"`
	RegionName           string  `json:"region_name"`
	RegionAddress        string  `json:"region_address"`
	RegionAlias          string  `json:"region_alias"`
	StoreType            string  `json:"store_type"`
	StoreTypeName        string  `json:"store_type_name"`
	BusinessDays         int64   `json:"business_days"`
	ProductCategoryId    int64   `json:"product_category_id"`
	ProductCategoryCode  string  `json:"product_category_code"`
	ProductCategoryName  string  `json:"product_category_name"`
	ProductCategoryId1   int64   `json:"product_category_id1"`
	ProductCategoryCode1 string  `json:"product_category_code1"`
	ProductCategoryName1 string  `json:"product_category_name1"`
	ProductCategoryId2   int64   `json:"product_category_id2"`
	ProductCategoryCode2 string  `json:"product_category_code2"`
	ProductCategoryName2 string  `json:"product_category_name2"`
	ProductCategoryId3   int64   `json:"product_category_id3"`
	ProductCategoryCode3 string  `json:"product_category_code3"`
	ProductCategoryName3 string  `json:"product_category_name3"`
	ProductCategoryId4   int64   `json:"product_category_id4"`
	ProductCategoryCode4 string  `json:"product_category_code4"`
	ProductCategoryName4 string  `json:"product_category_name4"`
	ProductId            int64   `json:"product_id"`
	ProductCode          string  `json:"product_code"`
	ProductName          string  `json:"product_name"`
	GrossAmount          float64 `json:"gross_amount"`
	NetAmount            float64 `json:"net_amount"`
	ItemCount            int64   `json:"item_count"`

	Data struct {
		H00 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h00"`
		H01 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h01"`
		H02 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h02"`
		H03 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h03"`
		H04 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h04"`
		H05 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h05"`
		H06 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h06"`
		H07 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h07"`
		H08 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h08"`
		H09 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h09"`
		H10 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h10"`
		H11 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h11"`
		H12 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h12"`
		H13 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h13"`
		H14 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h14"`
		H15 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h15"`
		H16 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h16"`
		H17 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h17"`
		H18 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h18"`
		H19 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h19"`
		H20 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h20"`
		H21 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h21"`
		H22 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h22"`
		H23 struct {
			WeightCount string  `json:"weight_count"`
			GrossAmount float64 `json:"gross_amount"`
			NetAmount   float64 `json:"net_amount"`
			ItemCount   int64   `json:"item_count"`
		} `json:"h23"`
	} `json:"data"`
}

func (p *ProductPeriodSalesV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":               p.BusDate,
		"geo_id":                 cast.ToString(p.GeoId),
		"geo_code":               p.GeoCode,
		"geo_name":               p.GeoName,
		"branch_id":              cast.ToString(p.BranchId),
		"branch_code":            p.BranchCode,
		"branch_name":            p.BranchName,
		"company_id":             cast.ToString(p.CompanyId),
		"company_code":           p.CompanyCode,
		"company_name":           p.CompanyName,
		"region_id":              cast.ToString(p.RegionId),
		"region_code":            p.RegionCode,
		"region_name":            p.RegionName,
		"region_address":         p.RegionAddress,
		"region_alias":           p.RegionAlias,
		"store_type":             p.StoreType,
		"store_type_name":        p.StoreTypeName,
		"business_days":          p.BusinessDays,
		"product_id":             cast.ToString(p.ProductId),
		"product_code":           p.ProductCode,
		"product_name":           p.ProductName,
		"product_category_id":    cast.ToString(p.ProductCategoryId),
		"product_category_code":  p.ProductCategoryCode,
		"product_category_name":  p.ProductCategoryName,
		"product_category_id1":   cast.ToString(p.ProductCategoryId1),
		"product_category_code1": p.ProductCategoryCode1,
		"product_category_name1": p.ProductCategoryName1,
		"product_category_id2":   cast.ToString(p.ProductCategoryId2),
		"product_category_code2": p.ProductCategoryCode2,
		"product_category_name2": p.ProductCategoryName2,
		"product_category_id3":   cast.ToString(p.ProductCategoryId3),
		"product_category_code3": p.ProductCategoryCode3,
		"product_category_name3": p.ProductCategoryName3,
		"product_category_id4":   cast.ToString(p.ProductCategoryId4),
		"product_category_code4": p.ProductCategoryCode4,
		"product_category_name4": p.ProductCategoryName4,
		"gross_amount":           SaveTwoEffectiveDigits(p.GrossAmount),
		"net_amount":             SaveTwoEffectiveDigits(p.NetAmount),
		"item_count":             p.ItemCount,

		"weight_count_00": p.Data.H00.WeightCount,
		"gross_amount_00": SaveTwoEffectiveDigits(p.Data.H00.GrossAmount),
		"net_amount_00":   SaveTwoEffectiveDigits(p.Data.H00.NetAmount),
		"item_count_00":   p.Data.H00.ItemCount,

		"weight_count_01": p.Data.H01.WeightCount,
		"gross_amount_01": SaveTwoEffectiveDigits(p.Data.H01.GrossAmount),
		"net_amount_01":   SaveTwoEffectiveDigits(p.Data.H01.NetAmount),
		"item_count_01":   p.Data.H01.ItemCount,

		"weight_count_02": p.Data.H02.WeightCount,
		"gross_amount_02": SaveTwoEffectiveDigits(p.Data.H02.GrossAmount),
		"net_amount_02":   SaveTwoEffectiveDigits(p.Data.H02.NetAmount),
		"item_count_02":   p.Data.H02.ItemCount,

		"weight_count_03": p.Data.H03.WeightCount,
		"gross_amount_03": SaveTwoEffectiveDigits(p.Data.H03.GrossAmount),
		"net_amount_03":   SaveTwoEffectiveDigits(p.Data.H03.NetAmount),
		"item_count_03":   p.Data.H03.ItemCount,

		"weight_count_04": p.Data.H04.WeightCount,
		"gross_amount_04": SaveTwoEffectiveDigits(p.Data.H04.GrossAmount),
		"net_amount_04":   SaveTwoEffectiveDigits(p.Data.H04.NetAmount),
		"item_count_04":   p.Data.H04.ItemCount,

		"weight_count_05": p.Data.H05.WeightCount,
		"gross_amount_05": SaveTwoEffectiveDigits(p.Data.H05.GrossAmount),
		"net_amount_05":   SaveTwoEffectiveDigits(p.Data.H05.NetAmount),
		"item_count_05":   p.Data.H05.ItemCount,

		"weight_count_06": p.Data.H06.WeightCount,
		"gross_amount_06": SaveTwoEffectiveDigits(p.Data.H06.GrossAmount),
		"net_amount_06":   SaveTwoEffectiveDigits(p.Data.H06.NetAmount),
		"item_count_06":   p.Data.H06.ItemCount,

		"weight_count_07": p.Data.H07.WeightCount,
		"gross_amount_07": SaveTwoEffectiveDigits(p.Data.H07.GrossAmount),
		"net_amount_07":   SaveTwoEffectiveDigits(p.Data.H07.NetAmount),
		"item_count_07":   p.Data.H07.ItemCount,

		"weight_count_08": p.Data.H08.WeightCount,
		"gross_amount_08": SaveTwoEffectiveDigits(p.Data.H08.GrossAmount),
		"net_amount_08":   SaveTwoEffectiveDigits(p.Data.H08.NetAmount),
		"item_count_08":   p.Data.H08.ItemCount,

		"weight_count_09": p.Data.H09.WeightCount,
		"gross_amount_09": SaveTwoEffectiveDigits(p.Data.H09.GrossAmount),
		"net_amount_09":   SaveTwoEffectiveDigits(p.Data.H09.NetAmount),
		"item_count_09":   p.Data.H09.ItemCount,

		"weight_count_10": p.Data.H10.WeightCount,
		"gross_amount_10": SaveTwoEffectiveDigits(p.Data.H10.GrossAmount),
		"net_amount_10":   SaveTwoEffectiveDigits(p.Data.H10.NetAmount),
		"item_count_10":   p.Data.H10.ItemCount,

		"weight_count_11": p.Data.H11.WeightCount,
		"gross_amount_11": SaveTwoEffectiveDigits(p.Data.H11.GrossAmount),
		"net_amount_11":   SaveTwoEffectiveDigits(p.Data.H11.NetAmount),
		"item_count_11":   p.Data.H11.ItemCount,

		"weight_count_12": p.Data.H12.WeightCount,
		"gross_amount_12": SaveTwoEffectiveDigits(p.Data.H12.GrossAmount),
		"net_amount_12":   SaveTwoEffectiveDigits(p.Data.H12.NetAmount),
		"item_count_12":   p.Data.H12.ItemCount,

		"weight_count_13": p.Data.H13.WeightCount,
		"gross_amount_13": SaveTwoEffectiveDigits(p.Data.H13.GrossAmount),
		"net_amount_13":   SaveTwoEffectiveDigits(p.Data.H13.NetAmount),
		"item_count_13":   p.Data.H13.ItemCount,

		"weight_count_14": p.Data.H14.WeightCount,
		"gross_amount_14": SaveTwoEffectiveDigits(p.Data.H14.GrossAmount),
		"net_amount_14":   SaveTwoEffectiveDigits(p.Data.H14.NetAmount),
		"item_count_14":   p.Data.H14.ItemCount,

		"weight_count_15": p.Data.H15.WeightCount,
		"gross_amount_15": SaveTwoEffectiveDigits(p.Data.H15.GrossAmount),
		"net_amount_15":   SaveTwoEffectiveDigits(p.Data.H15.NetAmount),
		"item_count_15":   p.Data.H15.ItemCount,

		"weight_count_16": p.Data.H16.WeightCount,
		"gross_amount_16": SaveTwoEffectiveDigits(p.Data.H16.GrossAmount),
		"net_amount_16":   SaveTwoEffectiveDigits(p.Data.H16.NetAmount),
		"item_count_16":   p.Data.H16.ItemCount,

		"weight_count_17": p.Data.H17.WeightCount,
		"gross_amount_17": SaveTwoEffectiveDigits(p.Data.H17.GrossAmount),
		"net_amount_17":   SaveTwoEffectiveDigits(p.Data.H17.NetAmount),
		"item_count_17":   p.Data.H17.ItemCount,

		"weight_count_18": p.Data.H18.WeightCount,
		"gross_amount_18": SaveTwoEffectiveDigits(p.Data.H18.GrossAmount),
		"net_amount_18":   SaveTwoEffectiveDigits(p.Data.H18.NetAmount),
		"item_count_18":   p.Data.H18.ItemCount,

		"weight_count_19": p.Data.H19.WeightCount,
		"gross_amount_19": SaveTwoEffectiveDigits(p.Data.H19.GrossAmount),
		"net_amount_19":   SaveTwoEffectiveDigits(p.Data.H19.NetAmount),
		"item_count_19":   p.Data.H19.ItemCount,

		"weight_count_20": p.Data.H20.WeightCount,
		"gross_amount_20": SaveTwoEffectiveDigits(p.Data.H20.GrossAmount),
		"net_amount_20":   SaveTwoEffectiveDigits(p.Data.H20.NetAmount),
		"item_count_20":   p.Data.H20.ItemCount,

		"weight_count_21": p.Data.H21.WeightCount,
		"gross_amount_21": SaveTwoEffectiveDigits(p.Data.H21.GrossAmount),
		"net_amount_21":   SaveTwoEffectiveDigits(p.Data.H21.NetAmount),
		"item_count_21":   p.Data.H21.ItemCount,

		"weight_count_22": p.Data.H22.WeightCount,
		"gross_amount_22": SaveTwoEffectiveDigits(p.Data.H22.GrossAmount),
		"net_amount_22":   SaveTwoEffectiveDigits(p.Data.H22.NetAmount),
		"item_count_22":   p.Data.H22.ItemCount,

		"weight_count_23": p.Data.H23.WeightCount,
		"gross_amount_23": SaveTwoEffectiveDigits(p.Data.H23.GrossAmount),
		"net_amount_23":   SaveTwoEffectiveDigits(p.Data.H23.NetAmount),
		"item_count_23":   p.Data.H23.ItemCount,
	}
}
