package report_v2

import "github.com/spf13/cast"

type RefundAnalysisResponseV2 struct {
	Summary *RefundAnalysisV2
	Rows    []*RefundAnalysisV2
	Total   int64
}

type RefundAnalysisV2 struct {
	BusDate                   string  `json:"bus_date"`
	GeoId                     int64   `json:"geo_id"`
	GeoCode                   string  `json:"geo_code"`
	GeoName                   string  `json:"geo_name"`
	BranchId                  int64   `json:"branch_id"`
	BranchCode                string  `json:"branch_code"`
	BranchName                string  `json:"branch_name"`
	CompanyId                 int64   `json:"company_id"`
	CompanyCode               string  `json:"company_code"`
	CompanyName               string  `json:"company_name"`
	RegionId                  int64   `json:"region_id"`
	RegionCode                string  `json:"region_code"`
	RegionName                string  `json:"region_name"`
	RegionAddress             string  `json:"region_address"`
	RegionAlias               string  `json:"region_alias"`
	RegionStatus              string  `json:"region_status"`
	StoreType                 string  `json:"store_type"`
	StoreTypeName             string  `json:"store_type_name"`
	BusinessDays              int64   `json:"business_days"`
	RefundSide                string  `json:"refund_side"`
	ChannelId                 int64   `json:"channel_id"`
	ChannelCode               string  `json:"channel_code"`
	ChannelName               string  `json:"channel_name"`
	RefundCode                string  `json:"refund_code"`
	RefundReason              string  `json:"refund_reason"`
	GrossAmountReturned       float64 `json:"gross_amount_returned"`
	OrderCountReturned        int64   `json:"order_count_returned"`
	NetAmountReturned         float64 `json:"net_amount_returned"`
	ReceivableReturned        float64 `json:"receivable_returned"`
	PayAmountReturned         float64 `json:"pay_amount_returned"`
	DiscountAmountReturned    float64 `json:"discount_amount_returned"`
	MerchantAllowanceReturned float64 `json:"merchant_allowance_returned"`
	CommissionReturned        float64 `json:"commission_returned"`
	DeliveryFeeReturned       float64 `json:"delivery_fee_returned"`
	PackageFeeReturned        float64 `json:"package_fee_returned"`
	TipReturned               float64 `json:"tip_returned"`
	ServiceFeeReturned        float64 `json:"service_fee_returned"`
	TaxFeeReturned            float64 `json:"tax_fee_returned"`
	RoundingReturned          float64 `json:"rounding_returned"`
	OverflowAmountReturned    float64 `json:"overflow_amount_returned"`
	Total                     int64   `json:"total"`
}

func (r *RefundAnalysisV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                    r.BusDate,
		"geo_id":                      cast.ToString(r.GeoId),
		"geo_code":                    r.GeoCode,
		"geo_name":                    r.GeoName,
		"branch_id":                   cast.ToString(r.BranchId),
		"branch_code":                 r.BranchCode,
		"branch_name":                 r.BranchName,
		"company_id":                  cast.ToString(r.CompanyId),
		"company_code":                r.CompanyCode,
		"company_name":                r.CompanyName,
		"region_id":                   cast.ToString(r.RegionId),
		"region_code":                 r.RegionCode,
		"region_name":                 r.RegionName,
		"region_address":              r.RegionAddress,
		"region_alias":                r.RegionAlias,
		"region_status":               r.RegionStatus,
		"store_type":                  r.StoreType,
		"store_type_name":             r.StoreTypeName, // 门店经营类型
		"business_days":               cast.ToString(r.BusinessDays),
		"refund_side":                 r.RefundSide,
		"channel_id":                  cast.ToString(r.ChannelId),
		"channel_code":                r.ChannelCode,
		"channel_name":                r.ChannelName,
		"refund_code":                 r.RefundCode,
		"refund_reason":               r.RefundReason,
		"gross_amount_returned":       SaveTwoEffectiveDigits(r.GrossAmountReturned),
		"order_count_returned":        r.OrderCountReturned,
		"net_amount_returned":         SaveTwoEffectiveDigits(r.NetAmountReturned),
		"receivable_returned":         SaveTwoEffectiveDigits(r.ReceivableReturned),
		"pay_amount_returned":         SaveTwoEffectiveDigits(r.PayAmountReturned),
		"discount_amount_returned":    SaveTwoEffectiveDigits(r.DiscountAmountReturned),
		"merchant_allowance_returned": SaveTwoEffectiveDigits(r.MerchantAllowanceReturned),
		"commission_returned":         SaveTwoEffectiveDigits(r.CommissionReturned),
		"delivery_fee_returned":       SaveTwoEffectiveDigits(r.DeliveryFeeReturned),
		"package_fee_returned":        SaveTwoEffectiveDigits(r.PackageFeeReturned),
		"tip_returned":                SaveTwoEffectiveDigits(r.TipReturned),
		"service_fee_returned":        SaveTwoEffectiveDigits(r.ServiceFeeReturned),
		"tax_fee_returned":            SaveTwoEffectiveDigits(r.TaxFeeReturned),
		"rounding_returned":           SaveTwoEffectiveDigits(r.RoundingReturned),
		"overflow_amount_returned":    SaveTwoEffectiveDigits(r.OverflowAmountReturned),
		"total":                       r.Total,
	}
}
