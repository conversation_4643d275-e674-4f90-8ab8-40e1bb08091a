package report_v2

import "github.com/spf13/cast"

type StoreChannelSalesResponseV2 struct {
	Summary *StoreChannelV2
	Rows    []*StoreChannelV2
	Total   int64
}

type StoreChannelV2 struct {
	BusDate                    string  `json:"bus_date"`
	GeoId                      int64   `json:"geo_id"`
	GeoCode                    string  `json:"geo_code"`
	GeoName                    string  `json:"geo_name"`
	BranchId                   int64   `json:"branch_id"`
	BranchCode                 string  `json:"branch_code"`
	BranchName                 string  `json:"branch_name"`
	CompanyId                  int64   `json:"company_id"`
	CompanyCode                string  `json:"company_code"`
	CompanyName                string  `json:"company_name"`
	RegionId                   int64   `json:"region_id"`
	RegionCode                 string  `json:"region_code"`
	RegionName                 string  `json:"region_name"`
	RegionAddress              string  `json:"region_address"`
	RegionAlias                string  `json:"region_alias"`
	RegionStatus               string  `json:"region_status"`
	StoreType                  string  `json:"store_type"`
	StoreTypeName              string  `json:"store_type_name"`
	BusinessDays               int64   `json:"business_days"`
	ChannelId                  int64   `json:"channel_id"`
	ChannelCode                string  `json:"channel_code"`
	ChannelName                string  `json:"channel_name"`
	OrderType                  string  `json:"order_type"`
	BusinessAmount             float64 `json:"business_amount"`
	RealAmount                 float64 `json:"real_amount"`
	ExpendAmount               float64 `json:"expend_amount"`
	CustomerPrice              float64 `json:"customer_price"`
	ValidOrderCount            int64   `json:"valid_order_count"`
	GrossAmount                float64 `json:"gross_amount"`
	NetAmount                  float64 `json:"net_amount"`
	DiscountAmount             float64 `json:"discount_amount"`
	MerchantAllowance          float64 `json:"merchant_allowance"`
	PackageFee                 float64 `json:"package_fee"`
	DeliveryFee                float64 `json:"delivery_fee"`
	SendFee                    float64 `json:"send_fee"`
	MerchantSendFee            float64 `json:"merchant_send_fee"`
	PlatformSendFee            float64 `json:"platform_send_fee"`
	PlatformAllowance          float64 `json:"platform_allowance"`
	Commission                 float64 `json:"commission"`
	ServiceFee                 float64 `json:"service_fee"`
	Tip                        float64 `json:"tip"`
	OtherFee                   float64 `json:"other_fee"`
	Receivable                 float64 `json:"receivable"`
	PayAmount                  float64 `json:"pay_amount"`
	Rounding                   float64 `json:"rounding"`
	OverflowAmount             float64 `json:"overflow_amount"`
	ChangeAmount               float64 `json:"change_amount"`
	TransferRealAmount         float64 `json:"transfer_real_amount"`
	DiscountContribute         float64 `json:"discount_contribute"`
	DiscountMerchantContribute float64 `json:"discount_merchant_contribute"`
	PayMerchantContribute      float64 `json:"pay_merchant_contribute"`
	RealAmountDiscount         float64 `json:"real_amount_discount"`
	RealAmountPayment          float64 `json:"real_amount_payment"`
	Total                      int64   `json:"total"`
}

func (s *StoreChannelV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                     s.BusDate,
		"geo_id":                       cast.ToString(s.GeoId),
		"geo_code":                     s.GeoCode,
		"geo_name":                     s.GeoName,
		"branch_id":                    cast.ToString(s.BranchId),
		"branch_code":                  s.BranchCode,
		"branch_name":                  s.BranchName,
		"company_id":                   cast.ToString(s.CompanyId),
		"company_code":                 s.CompanyCode,
		"company_name":                 s.CompanyName,
		"region_id":                    cast.ToString(s.RegionId),
		"region_code":                  s.RegionCode,
		"region_name":                  s.RegionName,
		"region_address":               s.RegionAddress,
		"region_alias":                 s.RegionAlias,
		"region_status":                s.RegionStatus,
		"store_type":                   s.StoreType,
		"store_type_name":              s.StoreTypeName,
		"business_days":                s.BusinessDays,
		"channel_id":                   cast.ToString(s.ChannelId),
		"channel_code":                 s.ChannelCode,
		"channel_name":                 s.ChannelName,
		"order_type":                   s.OrderType,
		"business_amount":              SaveTwoEffectiveDigits(s.BusinessAmount),
		"real_amount":                  SaveTwoEffectiveDigits(s.RealAmount),
		"expend_amount":                SaveTwoEffectiveDigits(s.ExpendAmount),
		"customer_price":               SaveTwoEffectiveDigits(s.CustomerPrice),
		"valid_order_count":            s.ValidOrderCount,
		"gross_amount":                 SaveTwoEffectiveDigits(s.GrossAmount),
		"net_amount":                   SaveTwoEffectiveDigits(s.NetAmount),
		"discount_amount":              SaveTwoEffectiveDigits(s.DiscountAmount),
		"merchant_allowance":           SaveTwoEffectiveDigits(s.MerchantAllowance),
		"package_fee":                  SaveTwoEffectiveDigits(s.PackageFee),
		"delivery_fee":                 SaveTwoEffectiveDigits(s.DeliveryFee),
		"send_fee":                     SaveTwoEffectiveDigits(s.SendFee),
		"merchant_send_fee":            SaveTwoEffectiveDigits(s.MerchantSendFee),
		"platform_send_fee":            SaveTwoEffectiveDigits(s.PlatformSendFee),
		"platform_allowance":           SaveTwoEffectiveDigits(s.PlatformAllowance),
		"commission":                   SaveTwoEffectiveDigits(s.Commission),
		"service_fee":                  SaveTwoEffectiveDigits(s.ServiceFee),
		"tip":                          SaveTwoEffectiveDigits(s.Tip),
		"other_fee":                    SaveTwoEffectiveDigits(s.OtherFee),
		"receivable":                   SaveTwoEffectiveDigits(s.Receivable),
		"pay_amount":                   SaveTwoEffectiveDigits(s.PayAmount),
		"rounding":                     SaveTwoEffectiveDigits(s.Rounding),
		"overflow_amount":              SaveTwoEffectiveDigits(s.OverflowAmount),
		"change_amount":                SaveTwoEffectiveDigits(s.ChangeAmount),
		"transfer_real_amount":         SaveTwoEffectiveDigits(s.TransferRealAmount),
		"discount_contribute":          SaveTwoEffectiveDigits(s.DiscountContribute),
		"discount_merchant_contribute": SaveTwoEffectiveDigits(s.DiscountMerchantContribute),
		"pay_merchant_contribute":      SaveTwoEffectiveDigits(s.PayMerchantContribute),
		"real_amount_discount":         SaveTwoEffectiveDigits(s.RealAmountDiscount),
		"real_amount_payment":          SaveTwoEffectiveDigits(s.RealAmountPayment),
		"total":                        s.Total,
	}
}
