package report_v2

import (
	"github.com/spf13/cast"
)

type PaymentStatisticsResponseV2 struct {
	Summary *PaymentStatisticsV2
	Rows    []*PaymentStatisticsV2
	Total   int64
}

type PaymentStatisticsV2 struct {
	BusDate               string  `json:"bus_date"`
	GeoId                 int64   `json:"geo_id"`
	GeoCode               string  `json:"geo_code"`
	GeoName               string  `json:"geo_name"`
	BranchId              int64   `json:"branch_id"`
	BranchCode            string  `json:"branch_code"`
	BranchName            string  `json:"branch_name"`
	CompanyId             int64   `json:"company_id"`
	CompanyCode           string  `json:"company_code"`
	CompanyName           string  `json:"company_name"`
	RegionId              int64   `json:"region_id"`
	RegionCode            string  `json:"region_code"`
	RegionName            string  `json:"region_name"`
	RegionAddress         string  `json:"region_address"`
	RegionAlias           string  `json:"region_alias"`
	RegionStatus          string  `json:"region_status"`
	StoreType             string  `json:"store_type"`
	StoreTypeName         string  `json:"store_type_name"`
	BusinessDays          int64   `json:"business_days"`
	ChannelId             string  `json:"channel_id"`
	ChannelCode           string  `json:"channel_code"`
	ChannelName           string  `json:"channel_name"`
	PaymentId             int64   `json:"payment_id"`
	PaymentCode           string  `json:"payment_code"`
	PaymentName           string  `json:"payment_name"`
	PaymentType           string  `json:"payment_type"`
	Receivable            float64 `json:"receivable"`
	PayAmount             float64 `json:"pay_amount"`
	TransferRealAmount    float64 `json:"transfer_real_amount"`
	PaymentTransferAmount float64 `json:"payment_transfer_amount"`
	Cost                  float64 `json:"cost"`
	TpAllowance           float64 `json:"tp_allowance"`
	Rounding              float64 `json:"rounding"`
	OverflowAmount        float64 `json:"overflow_amount"`
	ChangeAmount          float64 `json:"change_amount"`
	ItemCount             int64   `json:"item_count"`
	ItemCountReturned     int64   `json:"item_count_returned"`
}

func (p *PaymentStatisticsV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                p.BusDate,
		"geo_id":                  cast.ToString(p.GeoId),
		"geo_code":                p.GeoCode,
		"geo_name":                p.GeoName,
		"branch_id":               cast.ToString(p.BranchId),
		"branch_code":             p.BranchCode,
		"branch_name":             p.BranchName,
		"company_id":              cast.ToString(p.CompanyId),
		"company_code":            p.CompanyCode,
		"company_name":            p.CompanyName,
		"region_id":               cast.ToString(p.RegionId),
		"region_code":             p.RegionCode,
		"region_name":             p.RegionName,
		"region_address":          p.RegionAddress,
		"region_alias":            p.RegionAlias,
		"region_status":           p.RegionStatus,
		"store_type":              p.StoreType,
		"store_type_name":         p.StoreTypeName,
		"business_days":           p.BusinessDays,
		"channel_id":              p.ChannelId,
		"channel_code":            p.ChannelCode,
		"channel_name":            p.ChannelName,
		"payment_id":              cast.ToString(p.PaymentId),
		"payment_code":            p.PaymentCode,
		"payment_name":            p.PaymentName,
		"payment_type":            p.PaymentType,
		"receivable":              SaveTwoEffectiveDigits(p.Receivable),
		"pay_amount":              SaveTwoEffectiveDigits(p.PayAmount),
		"transfer_real_amount":    SaveTwoEffectiveDigits(p.TransferRealAmount),
		"payment_transfer_amount": SaveTwoEffectiveDigits(p.PaymentTransferAmount),
		"cost":                    SaveTwoEffectiveDigits(p.Cost),
		"tp_allowance":            SaveTwoEffectiveDigits(p.TpAllowance),
		"rounding":                SaveTwoEffectiveDigits(p.Rounding),
		"overflow_amount":         SaveTwoEffectiveDigits(p.OverflowAmount),
		"change_amount":           SaveTwoEffectiveDigits(p.ChangeAmount),
		"item_count":              p.ItemCount,
		"item_count_returned":     p.ItemCountReturned,
	}
}
