package report_v2

import "github.com/spf13/cast"

type ProductSalesResponseV2 struct {
	Summary *ProductSalesV2
	Rows    []*ProductSalesV2
	Total   int64
}

type ProductSalesV2 struct {
	BusDate                string  `json:"bus_date"`
	GeoId                  int64   `json:"geo_id"`
	GeoCode                string  `json:"geo_code"`
	GeoName                string  `json:"geo_name"`
	BranchId               int64   `json:"branch_id"`
	BranchCode             string  `json:"branch_code"`
	BranchName             string  `json:"branch_name"`
	CompanyId              int64   `json:"company_id"`
	CompanyCode            string  `json:"company_code"`
	CompanyName            string  `json:"company_name"`
	RegionId               int64   `json:"region_id"`
	RegionCode             string  `json:"region_code"`
	RegionName             string  `json:"region_name"`
	RegionAddress          string  `json:"region_address"`
	RegionAlias            string  `json:"region_alias"`
	StoreType              string  `json:"store_type"`
	StoreTypeName          string  `json:"store_type_name"`
	BusinessDays           int64   `json:"business_days"`
	ProductId              int64   `json:"product_id"`
	ProductCode            string  `json:"product_code"`
	ProductName            string  `json:"product_name"`
	ProductNameNoSale      string  `json:"product_name_no_sale"`
	ProductSaleName        string  `json:"product_sale_name"`
	ProductCategoryId      int64   `json:"product_category_id"`
	ProductCategoryCode    string  `json:"product_category_code"`
	ProductCategoryName    string  `json:"product_category_name"`
	WeightCount            string  `json:"weight_count"`
	WeightCountReturned    string  `json:"weight_count_returned"`
	GrossAmount            float64 `json:"gross_amount"`
	GrossAmountReturned    float64 `json:"gross_amount_returned"`
	NetAmount              float64 `json:"net_amount"`
	NetAmountReturned      float64 `json:"net_amount_returned"`
	DiscountAmount         float64 `json:"discount_amount"`
	DiscountAmountReturned float64 `json:"discount_amount_returned"`
	ItemCount              int64   `json:"item_count"`
	ItemCountReturned      int64   `json:"item_count_returned"`
	Total                  int64   `json:"total"`
}

func (p *ProductSalesV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                 p.BusDate,
		"geo_id":                   cast.ToString(p.GeoId),
		"geo_code":                 p.GeoCode,
		"geo_name":                 p.GeoName,
		"branch_id":                cast.ToString(p.BranchId),
		"branch_code":              p.BranchCode,
		"branch_name":              p.BranchName,
		"company_id":               cast.ToString(p.CompanyId),
		"company_code":             p.CompanyCode,
		"company_name":             p.CompanyName,
		"region_id":                cast.ToString(p.RegionId),
		"region_code":              p.RegionCode,
		"region_name":              p.RegionName,
		"region_address":           p.RegionAddress,
		"region_alias":             p.RegionAlias,
		"store_type":               p.StoreType,
		"store_type_name":          p.StoreTypeName,
		"business_days":            p.BusinessDays,
		"product_id":               cast.ToString(p.ProductId),
		"product_code":             p.ProductCode,
		"product_name":             p.ProductName,
		"product_name_no_sale":     p.ProductNameNoSale,
		"product_sale_name":        p.ProductSaleName,
		"product_category_id":      cast.ToString(p.ProductCategoryId),
		"product_category_code":    p.ProductCategoryCode,
		"product_category_name":    p.ProductCategoryName,
		"weight_count":             p.WeightCount,
		"weight_count_returned":    p.WeightCountReturned,
		"gross_amount":             SaveTwoEffectiveDigits(p.GrossAmount),
		"gross_amount_returned":    SaveTwoEffectiveDigits(p.GrossAmountReturned),
		"net_amount":               SaveTwoEffectiveDigits(p.NetAmount),
		"net_amount_returned":      SaveTwoEffectiveDigits(p.NetAmountReturned),
		"discount_amount":          SaveTwoEffectiveDigits(p.DiscountAmount),
		"discount_amount_returned": SaveTwoEffectiveDigits(p.DiscountAmountReturned),
		"item_count":               p.ItemCount,
		"item_count_returned":      p.ItemCountReturned,
		"total":                    p.Total,
	}
}
