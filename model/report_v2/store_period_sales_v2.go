package report_v2

import (
	"fmt"
	"github.com/SmallTianTian/go-tools/slice"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
)

type StorePeriodSalesResponseV2 struct {
	Rows    []*StorePeriodV2 `json:"rows"`
	Summary *StorePeriodV2   `json:"summary"`
	Total   int64            `json:"total"`
}

type StorePeriodV2 struct {
	BusDate            string  `json:"bus_date"`
	GeoId              int64   `json:"geo_id"`
	GeoCode            string  `json:"geo_code"`
	GeoName            string  `json:"geo_name"`
	BranchId           int64   `json:"branch_id"`
	BranchCode         string  `json:"branch_code"`
	BranchName         string  `json:"branch_name"`
	CompanyId          int64   `json:"company_id"`
	CompanyCode        string  `json:"company_code"`
	CompanyName        string  `json:"company_name"`
	RegionId           int64   `json:"region_id"`
	RegionCode         string  `json:"region_code"`
	RegionName         string  `json:"region_name"`
	RegionAddress      string  `json:"region_address"`
	RegionAlias        string  `json:"region_alias"`
	RegionStatus       string  `json:"region_status"`
	StoreType          string  `json:"store_type"`
	StoreTypeName      string  `json:"store_type_name"`
	BusinessDays       int64   `json:"business_days"`
	BusinessAmount     float64 `json:"business_amount"`
	RealAmount         float64 `json:"real_amount"`
	ExpendAmount       float64 `json:"expend_amount"`
	ValidOrderCount    int64   `json:"valid_order_count"`
	DiscountContribute float64 `json:"discount_contribute"`
	TransferRealAmount float64 `json:"transfer_real_amount"`
	Data               *struct {
		H00 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h00"`
		H01 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h01"`
		H02 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h02"`
		H03 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h03"`
		H04 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h04"`
		H05 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h05"`
		H06 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h06"`
		H07 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h07"`
		H08 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h08"`
		H09 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h09"`
		H10 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h10"`
		H11 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h11"`
		H12 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h12"`
		H13 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h13"`
		H14 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h14"`
		H15 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h15"`
		H16 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h16"`
		H17 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h17"`
		H18 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h18"`
		H19 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h19"`
		H20 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h20"`
		H21 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h21"`
		H22 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h22"`
		H23 struct {
			BusinessAmount     float64 `json:"business_amount"`
			RealAmount         float64 `json:"real_amount"`
			ExpendAmount       float64 `json:"expend_amount"`
			ValidOrderCount    int64   `json:"valid_order_count"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		} `json:"h23"`
	} `json:"data"`
	DetailData []map[string]interface{} `json:"detail_data"`
}

func (s *StorePeriodV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":             s.BusDate,
		"geo_id":               cast.ToString(s.GeoId),
		"geo_code":             s.GeoCode,
		"geo_name":             s.GeoName,
		"branch_id":            cast.ToString(s.BranchId),
		"branch_code":          s.BranchCode,
		"branch_name":          s.BranchName,
		"company_id":           cast.ToString(s.CompanyId),
		"company_code":         s.CompanyCode,
		"company_name":         s.CompanyName,
		"region_id":            cast.ToString(s.RegionId),
		"region_code":          s.RegionCode,
		"region_name":          s.RegionName,
		"region_address":       s.RegionAddress,
		"region_alias":         s.RegionAlias,
		"region_status":        s.RegionStatus,
		"store_type":           s.StoreType,
		"store_type_name":      s.StoreTypeName, // 门店经营类型
		"business_days":        s.BusinessDays,
		"business_amount":      SaveTwoEffectiveDigits(s.BusinessAmount),
		"real_amount":          SaveTwoEffectiveDigits(s.RealAmount),
		"expend_amount":        SaveTwoEffectiveDigits(s.ExpendAmount),
		"valid_order_count":    s.ValidOrderCount,
		"discount_contribute":  SaveTwoEffectiveDigits(s.DiscountContribute),
		"transfer_real_amount": SaveTwoEffectiveDigits(s.TransferRealAmount),

		"business_amount_00":      SaveTwoEffectiveDigits(s.Data.H00.BusinessAmount),
		"real_amount_00":          SaveTwoEffectiveDigits(s.Data.H00.RealAmount),
		"expend_amount_00":        SaveTwoEffectiveDigits(s.Data.H00.ExpendAmount),
		"valid_order_count_00":    s.Data.H00.ValidOrderCount,
		"discount_contribute_00":  SaveTwoEffectiveDigits(s.Data.H00.DiscountContribute),
		"transfer_real_amount_00": SaveTwoEffectiveDigits(s.Data.H00.TransferRealAmount),

		"business_amount_01":      SaveTwoEffectiveDigits(s.Data.H01.BusinessAmount),
		"real_amount_01":          SaveTwoEffectiveDigits(s.Data.H01.RealAmount),
		"expend_amount_01":        SaveTwoEffectiveDigits(s.Data.H01.ExpendAmount),
		"valid_order_count_01":    s.Data.H01.ValidOrderCount,
		"discount_contribute_01":  SaveTwoEffectiveDigits(s.Data.H01.DiscountContribute),
		"transfer_real_amount_01": SaveTwoEffectiveDigits(s.Data.H01.TransferRealAmount),

		"business_amount_02":      SaveTwoEffectiveDigits(s.Data.H02.BusinessAmount),
		"real_amount_02":          SaveTwoEffectiveDigits(s.Data.H02.RealAmount),
		"expend_amount_02":        SaveTwoEffectiveDigits(s.Data.H02.ExpendAmount),
		"valid_order_count_02":    s.Data.H02.ValidOrderCount,
		"discount_contribute_02":  SaveTwoEffectiveDigits(s.Data.H02.DiscountContribute),
		"transfer_real_amount_02": SaveTwoEffectiveDigits(s.Data.H02.TransferRealAmount),

		"business_amount_03":      SaveTwoEffectiveDigits(s.Data.H03.BusinessAmount),
		"real_amount_03":          SaveTwoEffectiveDigits(s.Data.H03.RealAmount),
		"expend_amount_03":        SaveTwoEffectiveDigits(s.Data.H03.ExpendAmount),
		"valid_order_count_03":    s.Data.H03.ValidOrderCount,
		"discount_contribute_03":  SaveTwoEffectiveDigits(s.Data.H03.DiscountContribute),
		"transfer_real_amount_03": SaveTwoEffectiveDigits(s.Data.H03.TransferRealAmount),

		"business_amount_04":      SaveTwoEffectiveDigits(s.Data.H04.BusinessAmount),
		"real_amount_04":          SaveTwoEffectiveDigits(s.Data.H04.RealAmount),
		"expend_amount_04":        SaveTwoEffectiveDigits(s.Data.H04.ExpendAmount),
		"valid_order_count_04":    s.Data.H04.ValidOrderCount,
		"discount_contribute_04":  SaveTwoEffectiveDigits(s.Data.H04.DiscountContribute),
		"transfer_real_amount_04": SaveTwoEffectiveDigits(s.Data.H04.TransferRealAmount),

		"business_amount_05":      SaveTwoEffectiveDigits(s.Data.H05.BusinessAmount),
		"real_amount_05":          SaveTwoEffectiveDigits(s.Data.H05.RealAmount),
		"expend_amount_05":        SaveTwoEffectiveDigits(s.Data.H05.ExpendAmount),
		"valid_order_count_05":    s.Data.H05.ValidOrderCount,
		"discount_contribute_05":  SaveTwoEffectiveDigits(s.Data.H05.DiscountContribute),
		"transfer_real_amount_05": SaveTwoEffectiveDigits(s.Data.H05.TransferRealAmount),

		"business_amount_06":      SaveTwoEffectiveDigits(s.Data.H06.BusinessAmount),
		"real_amount_06":          SaveTwoEffectiveDigits(s.Data.H06.RealAmount),
		"expend_amount_06":        SaveTwoEffectiveDigits(s.Data.H06.ExpendAmount),
		"valid_order_count_06":    s.Data.H06.ValidOrderCount,
		"discount_contribute_06":  SaveTwoEffectiveDigits(s.Data.H06.DiscountContribute),
		"transfer_real_amount_06": SaveTwoEffectiveDigits(s.Data.H06.TransferRealAmount),

		"business_amount_07":      SaveTwoEffectiveDigits(s.Data.H07.BusinessAmount),
		"real_amount_07":          SaveTwoEffectiveDigits(s.Data.H07.RealAmount),
		"expend_amount_07":        SaveTwoEffectiveDigits(s.Data.H07.ExpendAmount),
		"valid_order_count_07":    s.Data.H07.ValidOrderCount,
		"discount_contribute_07":  SaveTwoEffectiveDigits(s.Data.H07.DiscountContribute),
		"transfer_real_amount_07": SaveTwoEffectiveDigits(s.Data.H07.TransferRealAmount),

		"business_amount_08":      SaveTwoEffectiveDigits(s.Data.H08.BusinessAmount),
		"real_amount_08":          SaveTwoEffectiveDigits(s.Data.H08.RealAmount),
		"expend_amount_08":        SaveTwoEffectiveDigits(s.Data.H08.ExpendAmount),
		"valid_order_count_08":    s.Data.H08.ValidOrderCount,
		"discount_contribute_08":  SaveTwoEffectiveDigits(s.Data.H08.DiscountContribute),
		"transfer_real_amount_08": SaveTwoEffectiveDigits(s.Data.H08.TransferRealAmount),

		"business_amount_09":      SaveTwoEffectiveDigits(s.Data.H09.BusinessAmount),
		"real_amount_09":          SaveTwoEffectiveDigits(s.Data.H09.RealAmount),
		"expend_amount_09":        SaveTwoEffectiveDigits(s.Data.H09.ExpendAmount),
		"valid_order_count_09":    s.Data.H09.ValidOrderCount,
		"discount_contribute_09":  SaveTwoEffectiveDigits(s.Data.H09.DiscountContribute),
		"transfer_real_amount_09": SaveTwoEffectiveDigits(s.Data.H09.TransferRealAmount),

		"business_amount_10":      SaveTwoEffectiveDigits(s.Data.H10.BusinessAmount),
		"real_amount_10":          SaveTwoEffectiveDigits(s.Data.H10.RealAmount),
		"expend_amount_10":        SaveTwoEffectiveDigits(s.Data.H10.ExpendAmount),
		"valid_order_count_10":    s.Data.H10.ValidOrderCount,
		"discount_contribute_10":  SaveTwoEffectiveDigits(s.Data.H10.DiscountContribute),
		"transfer_real_amount_10": SaveTwoEffectiveDigits(s.Data.H10.TransferRealAmount),

		"business_amount_11":      SaveTwoEffectiveDigits(s.Data.H11.BusinessAmount),
		"real_amount_11":          SaveTwoEffectiveDigits(s.Data.H11.RealAmount),
		"expend_amount_11":        SaveTwoEffectiveDigits(s.Data.H11.ExpendAmount),
		"valid_order_count_11":    s.Data.H11.ValidOrderCount,
		"discount_contribute_11":  SaveTwoEffectiveDigits(s.Data.H11.DiscountContribute),
		"transfer_real_amount_11": SaveTwoEffectiveDigits(s.Data.H11.TransferRealAmount),

		"business_amount_12":      SaveTwoEffectiveDigits(s.Data.H12.BusinessAmount),
		"real_amount_12":          SaveTwoEffectiveDigits(s.Data.H12.RealAmount),
		"expend_amount_12":        SaveTwoEffectiveDigits(s.Data.H12.ExpendAmount),
		"valid_order_count_12":    s.Data.H12.ValidOrderCount,
		"discount_contribute_12":  SaveTwoEffectiveDigits(s.Data.H12.DiscountContribute),
		"transfer_real_amount_12": SaveTwoEffectiveDigits(s.Data.H12.TransferRealAmount),

		"business_amount_13":      SaveTwoEffectiveDigits(s.Data.H13.BusinessAmount),
		"real_amount_13":          SaveTwoEffectiveDigits(s.Data.H13.RealAmount),
		"expend_amount_13":        SaveTwoEffectiveDigits(s.Data.H13.ExpendAmount),
		"valid_order_count_13":    s.Data.H13.ValidOrderCount,
		"discount_contribute_13":  SaveTwoEffectiveDigits(s.Data.H13.DiscountContribute),
		"transfer_real_amount_13": SaveTwoEffectiveDigits(s.Data.H13.TransferRealAmount),

		"business_amount_14":      SaveTwoEffectiveDigits(s.Data.H14.BusinessAmount),
		"real_amount_14":          SaveTwoEffectiveDigits(s.Data.H14.RealAmount),
		"expend_amount_14":        SaveTwoEffectiveDigits(s.Data.H14.ExpendAmount),
		"valid_order_count_14":    s.Data.H14.ValidOrderCount,
		"discount_contribute_14":  SaveTwoEffectiveDigits(s.Data.H14.DiscountContribute),
		"transfer_real_amount_14": SaveTwoEffectiveDigits(s.Data.H14.TransferRealAmount),

		"business_amount_15":      SaveTwoEffectiveDigits(s.Data.H15.BusinessAmount),
		"real_amount_15":          SaveTwoEffectiveDigits(s.Data.H15.RealAmount),
		"expend_amount_15":        SaveTwoEffectiveDigits(s.Data.H15.ExpendAmount),
		"valid_order_count_15":    s.Data.H15.ValidOrderCount,
		"discount_contribute_15":  SaveTwoEffectiveDigits(s.Data.H15.DiscountContribute),
		"transfer_real_amount_15": SaveTwoEffectiveDigits(s.Data.H15.TransferRealAmount),

		"business_amount_16":      SaveTwoEffectiveDigits(s.Data.H16.BusinessAmount),
		"real_amount_16":          SaveTwoEffectiveDigits(s.Data.H16.RealAmount),
		"expend_amount_16":        SaveTwoEffectiveDigits(s.Data.H16.ExpendAmount),
		"valid_order_count_16":    s.Data.H16.ValidOrderCount,
		"discount_contribute_16":  SaveTwoEffectiveDigits(s.Data.H16.DiscountContribute),
		"transfer_real_amount_16": SaveTwoEffectiveDigits(s.Data.H16.TransferRealAmount),

		"business_amount_17":      SaveTwoEffectiveDigits(s.Data.H17.BusinessAmount),
		"real_amount_17":          SaveTwoEffectiveDigits(s.Data.H17.RealAmount),
		"expend_amount_17":        SaveTwoEffectiveDigits(s.Data.H17.ExpendAmount),
		"valid_order_count_17":    s.Data.H17.ValidOrderCount,
		"discount_contribute_17":  SaveTwoEffectiveDigits(s.Data.H17.DiscountContribute),
		"transfer_real_amount_17": SaveTwoEffectiveDigits(s.Data.H17.TransferRealAmount),

		"business_amount_18":      SaveTwoEffectiveDigits(s.Data.H18.BusinessAmount),
		"real_amount_18":          SaveTwoEffectiveDigits(s.Data.H18.RealAmount),
		"expend_amount_18":        SaveTwoEffectiveDigits(s.Data.H18.ExpendAmount),
		"valid_order_count_18":    s.Data.H18.ValidOrderCount,
		"discount_contribute_18":  SaveTwoEffectiveDigits(s.Data.H18.DiscountContribute),
		"transfer_real_amount_18": SaveTwoEffectiveDigits(s.Data.H18.TransferRealAmount),

		"business_amount_19":      SaveTwoEffectiveDigits(s.Data.H19.BusinessAmount),
		"real_amount_19":          SaveTwoEffectiveDigits(s.Data.H19.RealAmount),
		"expend_amount_19":        SaveTwoEffectiveDigits(s.Data.H19.ExpendAmount),
		"valid_order_count_19":    s.Data.H19.ValidOrderCount,
		"discount_contribute_19":  SaveTwoEffectiveDigits(s.Data.H19.DiscountContribute),
		"transfer_real_amount_19": SaveTwoEffectiveDigits(s.Data.H19.TransferRealAmount),

		"business_amount_20":      SaveTwoEffectiveDigits(s.Data.H20.BusinessAmount),
		"real_amount_20":          SaveTwoEffectiveDigits(s.Data.H20.RealAmount),
		"expend_amount_20":        SaveTwoEffectiveDigits(s.Data.H20.ExpendAmount),
		"valid_order_count_20":    s.Data.H20.ValidOrderCount,
		"discount_contribute_20":  SaveTwoEffectiveDigits(s.Data.H20.DiscountContribute),
		"transfer_real_amount_20": SaveTwoEffectiveDigits(s.Data.H20.TransferRealAmount),

		"business_amount_21":      SaveTwoEffectiveDigits(s.Data.H21.BusinessAmount),
		"real_amount_21":          SaveTwoEffectiveDigits(s.Data.H21.RealAmount),
		"expend_amount_21":        SaveTwoEffectiveDigits(s.Data.H21.ExpendAmount),
		"valid_order_count_21":    s.Data.H21.ValidOrderCount,
		"discount_contribute_21":  SaveTwoEffectiveDigits(s.Data.H21.DiscountContribute),
		"transfer_real_amount_21": SaveTwoEffectiveDigits(s.Data.H21.TransferRealAmount),

		"business_amount_22":      SaveTwoEffectiveDigits(s.Data.H22.BusinessAmount),
		"real_amount_22":          SaveTwoEffectiveDigits(s.Data.H22.RealAmount),
		"expend_amount_22":        SaveTwoEffectiveDigits(s.Data.H22.ExpendAmount),
		"valid_order_count_22":    s.Data.H22.ValidOrderCount,
		"discount_contribute_22":  SaveTwoEffectiveDigits(s.Data.H22.DiscountContribute),
		"transfer_real_amount_22": SaveTwoEffectiveDigits(s.Data.H22.TransferRealAmount),

		"business_amount_23":      SaveTwoEffectiveDigits(s.Data.H23.BusinessAmount),
		"real_amount_23":          SaveTwoEffectiveDigits(s.Data.H23.RealAmount),
		"expend_amount_23":        SaveTwoEffectiveDigits(s.Data.H23.ExpendAmount),
		"valid_order_count_23":    s.Data.H23.ValidOrderCount,
		"discount_contribute_23":  SaveTwoEffectiveDigits(s.Data.H23.DiscountContribute),
		"transfer_real_amount_23": SaveTwoEffectiveDigits(s.Data.H23.TransferRealAmount),
	}
}

func (s *StorePeriodV2) HourDataColumn2Row(hours []int64) []map[string]interface{} {
	result := make([]map[string]interface{}, 0, 24)
	m := s.ToMap()
	// 24个小时
	for i := 0; i < 24; i++ {
		transHour := fmt.Sprintf("%02d:00-%02d:00", i, i+1)
		businessAmountKey := fmt.Sprintf("business_amount_%02d", i)
		realAmountKey := fmt.Sprintf("real_amount_%02d", i)
		expendAmountKey := fmt.Sprintf("expend_amount_%02d", i)
		validOrderCountKey := fmt.Sprintf("valid_order_count_%02d", i)
		discountContributeKey := fmt.Sprintf("discount_contribute_%02d", i)
		transferRealAmountKey := fmt.Sprintf("transfer_real_amount_%02d", i)
		if cast.ToFloat64(m[businessAmountKey]) == 0 && !slice.Int64InSlice(hours, cast.ToInt64(i)) {
			continue
		}
		realAmountRate := decimal.NewFromFloat(cast.ToFloat64(m[realAmountKey])).DivRound(decimal.NewFromFloat(s.RealAmount), 4)
		row := map[string]interface{}{
			"bus_date":                  s.BusDate,
			"geo_id":                    cast.ToString(s.GeoId),
			"geo_code":                  s.GeoCode,
			"geo_name":                  s.GeoName,
			"branch_id":                 cast.ToString(s.BranchId),
			"branch_code":               s.BranchCode,
			"branch_name":               s.BranchName,
			"company_id":                cast.ToString(s.CompanyId),
			"company_code":              s.CompanyCode,
			"company_name":              s.CompanyName,
			"region_id":                 cast.ToString(s.RegionId),
			"region_code":               s.RegionCode,
			"region_name":               s.RegionName,
			"region_address":            s.RegionAddress,
			"region_alias":              s.RegionAlias,
			"region_status":             s.RegionStatus,
			"store_type":                s.StoreType,
			"store_type_name":           s.StoreTypeName, // 门店经营类型
			"business_days":             s.BusinessDays,
			"trans_hour":                transHour,
			"business_amount":           m[businessAmountKey],
			"real_amount":               m[realAmountKey],
			"expend_amount":             m[expendAmountKey],
			"valid_order_count":         m[validOrderCountKey],
			"discount_contribute":       m[discountContributeKey],
			"transfer_real_amount":      m[transferRealAmountKey],
			"transfer_real_amount_rate": realAmountRate,
		}
		result = append(result, row)
	}
	return result
}
