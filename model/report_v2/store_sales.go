package report_v2

import (
	"github.com/spf13/cast"
)

type StoreSalesResponseV2 struct {
	Summary *StoreSalesV2
	Rows    []*StoreSalesV2
	Total   int64
}

type StoreSalesV2 struct {
	BusDate                string  `json:"bus_date"`
	GeoId                  int64   `json:"geo_id"`
	GeoCode                string  `json:"geo_code"`
	GeoName                string  `json:"geo_name"`
	BranchId               int64   `json:"branch_id"`
	BranchCode             string  `json:"branch_code"`
	BranchName             string  `json:"branch_name"`
	CompanyId              int64   `json:"company_id"`
	CompanyCode            string  `json:"company_code"`
	CompanyName            string  `json:"company_name"`
	RegionId               int64   `json:"region_id"`
	RegionCode             string  `json:"region_code"`
	RegionName             string  `json:"region_name"`
	RegionAddress          string  `json:"region_address"`
	RegionAlias            string  `json:"region_alias"`
	RegionStatus           string  `json:"region_status"`
	StoreType              string  `json:"store_type"`
	StoreTypeName          string  `json:"store_type_name"`
	BusinessDays           int64   `json:"business_days"`
	BusinessAmount         float64 `json:"business_amount"`
	ExpendAmount           float64 `json:"expend_amount"`
	RealAmount             float64 `json:"real_amount"`
	GrossAmount            float64 `json:"gross_amount"`
	PayAmount              float64 `json:"pay_amount"`
	CustomerPrice          float64 `json:"customer_price"`
	ValidOrderCount        int64   `json:"valid_order_count"`
	DiscountAmount         float64 `json:"discount_amount"`
	MerchantAllowance      float64 `json:"merchant_allowance"`
	PlatformAllowance      float64 `json:"platform_allowance"`
	MerchantSendFee        float64 `json:"merchant_send_fee"`
	PaymentTransferAmount  float64 `json:"payment_transfer_amount"`
	DiscountTransferAmount float64 `json:"discount_transfer_amount"`
	Commission             float64 `json:"commission"`
	OrderCountRefund       int64   `json:"order_count_refund"`
	GrossAmountReturned    float64 `json:"gross_amount_returned"`
	NetAmountReturned      float64 `json:"net_amount_returned"`
	OtherFee               float64 `json:"other_fee"`
	ChangeAmount           float64 `json:"change_amount"`
	Rounding               float64 `json:"rounding"`
	OverflowAmount         float64 `json:"overflow_amount"`
	DiscountContribute     float64 `json:"discount_contribute"`
	TransferRealAmount     float64 `json:"transfer_real_amount"`
	Total                  int64   `json:"total"`
}

func (s *StoreSalesV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                 s.BusDate,
		"geo_id":                   cast.ToString(s.GeoId),
		"geo_code":                 s.GeoCode,
		"geo_name":                 s.GeoName,
		"branch_id":                cast.ToString(s.BranchId),
		"branch_code":              s.BranchCode,
		"branch_name":              s.BranchName,
		"company_id":               cast.ToString(s.CompanyId),
		"company_code":             s.CompanyCode,
		"company_name":             s.CompanyName,
		"region_id":                cast.ToString(s.RegionId),
		"region_code":              s.RegionCode,
		"region_name":              s.RegionName,
		"region_address":           s.RegionAddress,
		"region_alias":             s.RegionAlias,
		"region_status":            s.RegionStatus,
		"store_type":               s.StoreType,
		"store_type_name":          s.StoreTypeName, // 门店经营类型
		"business_days":            s.BusinessDays,
		"business_amount":          SaveTwoEffectiveDigits(s.BusinessAmount),
		"expend_amount":            SaveTwoEffectiveDigits(s.ExpendAmount),
		"real_amount":              SaveTwoEffectiveDigits(s.RealAmount),
		"gross_amount":             SaveTwoEffectiveDigits(s.GrossAmount),
		"pay_amount":               SaveTwoEffectiveDigits(s.PayAmount),
		"customer_price":           SaveTwoEffectiveDigits(s.CustomerPrice),
		"valid_order_count":        s.ValidOrderCount,
		"discount_amount":          SaveTwoEffectiveDigits(s.DiscountAmount),
		"merchant_allowance":       SaveTwoEffectiveDigits(s.MerchantAllowance),
		"platform_allowance":       SaveTwoEffectiveDigits(s.PlatformAllowance),
		"merchant_send_fee":        SaveTwoEffectiveDigits(s.MerchantSendFee),
		"payment_transfer_amount":  SaveTwoEffectiveDigits(s.PaymentTransferAmount),
		"discount_transfer_amount": SaveTwoEffectiveDigits(s.DiscountTransferAmount),
		"commission":               SaveTwoEffectiveDigits(s.Commission),
		"order_count_refund":       s.OrderCountRefund,
		"gross_amount_returned":    SaveTwoEffectiveDigits(s.GrossAmountReturned),
		"net_amount_returned":      SaveTwoEffectiveDigits(s.NetAmountReturned),
		"other_fee":                SaveTwoEffectiveDigits(s.OtherFee),
		"change_amount":            SaveTwoEffectiveDigits(s.ChangeAmount),
		"rounding":                 SaveTwoEffectiveDigits(s.Rounding),
		"overflow_amount":          SaveTwoEffectiveDigits(s.OverflowAmount),
		"discount_contribute":      SaveTwoEffectiveDigits(s.DiscountContribute),
		"transfer_real_amount":     SaveTwoEffectiveDigits(s.TransferRealAmount),
		"total":                    s.Total,
	}
}
