package report_v2

import (
	"github.com/spf13/cast"
)

type PaymentPeriodResponseV2 struct {
	Rows    []*PaymentPeriodV2 `json:"rows"`
	Summary *PaymentPeriodV2   `json:"summary"`
	Total   int64              `json:"total"`
}

type PaymentPeriodV2 struct {
	BusDate               string  `json:"bus_date"`
	GeoId                 int64   `json:"geo_id"`
	GeoCode               string  `json:"geo_code"`
	GeoName               string  `json:"geo_name"`
	BranchId              int64   `json:"branch_id"`
	BranchCode            string  `json:"branch_code"`
	BranchName            string  `json:"branch_name"`
	CompanyId             int64   `json:"company_id"`
	CompanyCode           string  `json:"company_code"`
	CompanyName           string  `json:"company_name"`
	RegionId              int64   `json:"region_id"`
	RegionCode            string  `json:"region_code"`
	RegionName            string  `json:"region_name"`
	RegionAddress         string  `json:"region_address"`
	RegionAlias           string  `json:"region_alias"`
	RegionStatus          string  `json:"region_status"`
	StoreType             string  `json:"store_type"`
	StoreTypeName         string  `json:"store_type_name"`
	BusinessDays          int64   `json:"business_days"`
	PaymentId             int64   `json:"payment_id"`
	PaymentCode           string  `json:"payment_code"`
	PaymentName           string  `json:"payment_name"`
	PaymentType           string  `json:"payment_type"`
	Receivable            float64 `json:"receivable"`
	PayAmount             float64 `json:"pay_amount"`
	TransferRealAmount    float64 `json:"transfer_real_amount"`
	PaymentTransferAmount float64 `json:"payment_transfer_amount"`
	ItemCount             int64   `json:"item_count"`
	ItemCountReturned     int64   `json:"item_count_returned"`
	Rounding              float64 `json:"rounding"`
	OverflowAmount        float64 `json:"overflow_amount"`
	ChangeAmount          float64 `json:"change_amount"`
	Data                  struct {
		H00 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h00"`
		H01 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h01"`
		H02 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h02"`
		H03 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h03"`
		H04 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h04"`
		H05 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h05"`
		H06 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h06"`
		H07 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h07"`
		H08 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h08"`
		H09 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h09"`
		H10 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h10"`
		H11 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h11"`
		H12 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h12"`
		H13 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h13"`
		H14 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h14"`
		H15 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h15"`
		H16 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h16"`
		H17 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h17"`
		H18 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h18"`
		H19 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h19"`
		H20 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h20"`
		H21 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h21"`
		H22 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h22"`
		H23 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
		} `json:"h23"`
	} `json:"data"`
}

func (p *PaymentPeriodV2) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":                p.BusDate,
		"geo_id":                  cast.ToString(p.GeoId),
		"geo_code":                p.GeoCode,
		"geo_name":                p.GeoName,
		"branch_id":               cast.ToString(p.BranchId),
		"branch_code":             p.BranchCode,
		"branch_name":             p.BranchName,
		"company_id":              cast.ToString(p.CompanyId),
		"company_code":            p.CompanyCode,
		"company_name":            p.CompanyName,
		"region_id":               cast.ToString(p.RegionId),
		"region_code":             p.RegionCode,
		"region_name":             p.RegionName,
		"region_address":          p.RegionAddress,
		"region_alias":            p.RegionAlias,
		"region_status":           p.RegionStatus,
		"store_type":              p.StoreType,
		"store_type_name":         p.StoreTypeName,
		"business_days":           p.BusinessDays,
		"payment_id":              cast.ToString(p.PaymentId),
		"payment_code":            p.PaymentCode,
		"payment_name":            p.PaymentName,
		"payment_type":            p.PaymentType,
		"receivable":              SaveTwoEffectiveDigits(p.Receivable),
		"pay_amount":              SaveTwoEffectiveDigits(p.PayAmount),
		"transfer_real_amount":    SaveTwoEffectiveDigits(p.TransferRealAmount),
		"payment_transfer_amount": SaveTwoEffectiveDigits(p.PaymentTransferAmount),
		"item_count":              p.ItemCount,
		"item_count_returned":     p.ItemCountReturned,
		"rounding":                SaveTwoEffectiveDigits(p.Rounding),
		"overflow_amount":         SaveTwoEffectiveDigits(p.OverflowAmount),
		"change_amount":           SaveTwoEffectiveDigits(p.ChangeAmount),

		"receivable_00":              SaveTwoEffectiveDigits(p.Data.H00.Receivable),
		"pay_amount_00":              SaveTwoEffectiveDigits(p.Data.H00.PayAmount),
		"transfer_real_amount_00":    SaveTwoEffectiveDigits(p.Data.H00.TransferRealAmount),
		"payment_transfer_amount_00": SaveTwoEffectiveDigits(p.Data.H00.PaymentTransferAmount),
		"item_count_00":              p.Data.H00.ItemCount,
		"item_count_returned_00":     p.Data.H00.ItemCountReturned,
		"rounding_00":                SaveTwoEffectiveDigits(p.Data.H00.Rounding),
		"overflow_amount_00":         SaveTwoEffectiveDigits(p.Data.H00.OverflowAmount),
		"change_amount_00":           SaveTwoEffectiveDigits(p.Data.H00.ChangeAmount),

		"receivable_01":              SaveTwoEffectiveDigits(p.Data.H01.Receivable),
		"pay_amount_01":              SaveTwoEffectiveDigits(p.Data.H01.PayAmount),
		"transfer_real_amount_01":    SaveTwoEffectiveDigits(p.Data.H01.TransferRealAmount),
		"payment_transfer_amount_01": SaveTwoEffectiveDigits(p.Data.H01.PaymentTransferAmount),
		"item_count_01":              p.Data.H01.ItemCount,
		"item_count_returned_01":     p.Data.H01.ItemCountReturned,
		"rounding_01":                SaveTwoEffectiveDigits(p.Data.H01.Rounding),
		"overflow_amount_01":         SaveTwoEffectiveDigits(p.Data.H01.OverflowAmount),
		"change_amount_01":           SaveTwoEffectiveDigits(p.Data.H01.ChangeAmount),

		"receivable_02":              SaveTwoEffectiveDigits(p.Data.H02.Receivable),
		"pay_amount_02":              SaveTwoEffectiveDigits(p.Data.H02.PayAmount),
		"transfer_real_amount_02":    SaveTwoEffectiveDigits(p.Data.H02.TransferRealAmount),
		"payment_transfer_amount_02": SaveTwoEffectiveDigits(p.Data.H02.PaymentTransferAmount),
		"item_count_02":              p.Data.H02.ItemCount,
		"item_count_returned_02":     p.Data.H02.ItemCountReturned,
		"rounding_02":                SaveTwoEffectiveDigits(p.Data.H02.Rounding),
		"overflow_amount_02":         SaveTwoEffectiveDigits(p.Data.H02.OverflowAmount),
		"change_amount_02":           SaveTwoEffectiveDigits(p.Data.H02.ChangeAmount),

		"receivable_03":              SaveTwoEffectiveDigits(p.Data.H03.Receivable),
		"pay_amount_03":              SaveTwoEffectiveDigits(p.Data.H03.PayAmount),
		"transfer_real_amount_03":    SaveTwoEffectiveDigits(p.Data.H03.TransferRealAmount),
		"payment_transfer_amount_03": SaveTwoEffectiveDigits(p.Data.H03.PaymentTransferAmount),
		"item_count_03":              p.Data.H03.ItemCount,
		"item_count_returned_03":     p.Data.H03.ItemCountReturned,
		"rounding_03":                SaveTwoEffectiveDigits(p.Data.H03.Rounding),
		"overflow_amount_03":         SaveTwoEffectiveDigits(p.Data.H03.OverflowAmount),
		"change_amount_03":           SaveTwoEffectiveDigits(p.Data.H03.ChangeAmount),

		"receivable_04":              SaveTwoEffectiveDigits(p.Data.H04.Receivable),
		"pay_amount_04":              SaveTwoEffectiveDigits(p.Data.H04.PayAmount),
		"transfer_real_amount_04":    SaveTwoEffectiveDigits(p.Data.H04.TransferRealAmount),
		"payment_transfer_amount_04": SaveTwoEffectiveDigits(p.Data.H04.PaymentTransferAmount),
		"item_count_04":              p.Data.H04.ItemCount,
		"item_count_returned_04":     p.Data.H04.ItemCountReturned,
		"rounding_04":                SaveTwoEffectiveDigits(p.Data.H04.Rounding),
		"overflow_amount_04":         SaveTwoEffectiveDigits(p.Data.H04.OverflowAmount),
		"change_amount_04":           SaveTwoEffectiveDigits(p.Data.H04.ChangeAmount),

		"receivable_05":              SaveTwoEffectiveDigits(p.Data.H05.Receivable),
		"pay_amount_05":              SaveTwoEffectiveDigits(p.Data.H05.PayAmount),
		"transfer_real_amount_05":    SaveTwoEffectiveDigits(p.Data.H05.TransferRealAmount),
		"payment_transfer_amount_05": SaveTwoEffectiveDigits(p.Data.H05.PaymentTransferAmount),
		"item_count_05":              p.Data.H05.ItemCount,
		"item_count_returned_05":     p.Data.H05.ItemCountReturned,
		"rounding_05":                SaveTwoEffectiveDigits(p.Data.H05.Rounding),
		"overflow_amount_05":         SaveTwoEffectiveDigits(p.Data.H05.OverflowAmount),
		"change_amount_05":           SaveTwoEffectiveDigits(p.Data.H05.ChangeAmount),

		"receivable_06":              SaveTwoEffectiveDigits(p.Data.H06.Receivable),
		"pay_amount_06":              SaveTwoEffectiveDigits(p.Data.H06.PayAmount),
		"transfer_real_amount_06":    SaveTwoEffectiveDigits(p.Data.H06.TransferRealAmount),
		"payment_transfer_amount_06": SaveTwoEffectiveDigits(p.Data.H06.PaymentTransferAmount),
		"item_count_06":              p.Data.H06.ItemCount,
		"item_count_returned_06":     p.Data.H06.ItemCountReturned,
		"rounding_06":                SaveTwoEffectiveDigits(p.Data.H06.Rounding),
		"overflow_amount_06":         SaveTwoEffectiveDigits(p.Data.H06.OverflowAmount),
		"change_amount_06":           SaveTwoEffectiveDigits(p.Data.H06.ChangeAmount),

		"receivable_07":              SaveTwoEffectiveDigits(p.Data.H07.Receivable),
		"pay_amount_07":              SaveTwoEffectiveDigits(p.Data.H07.PayAmount),
		"transfer_real_amount_07":    SaveTwoEffectiveDigits(p.Data.H07.TransferRealAmount),
		"payment_transfer_amount_07": SaveTwoEffectiveDigits(p.Data.H07.PaymentTransferAmount),
		"item_count_07":              p.Data.H07.ItemCount,
		"item_count_returned_07":     p.Data.H07.ItemCountReturned,
		"rounding_07":                SaveTwoEffectiveDigits(p.Data.H07.Rounding),
		"overflow_amount_07":         SaveTwoEffectiveDigits(p.Data.H07.OverflowAmount),
		"change_amount_07":           SaveTwoEffectiveDigits(p.Data.H07.ChangeAmount),

		"receivable_08":              SaveTwoEffectiveDigits(p.Data.H08.Receivable),
		"pay_amount_08":              SaveTwoEffectiveDigits(p.Data.H08.PayAmount),
		"transfer_real_amount_08":    SaveTwoEffectiveDigits(p.Data.H08.TransferRealAmount),
		"payment_transfer_amount_08": SaveTwoEffectiveDigits(p.Data.H08.PaymentTransferAmount),
		"item_count_08":              p.Data.H08.ItemCount,
		"item_count_returned_08":     p.Data.H08.ItemCountReturned,
		"rounding_08":                SaveTwoEffectiveDigits(p.Data.H08.Rounding),
		"overflow_amount_08":         SaveTwoEffectiveDigits(p.Data.H08.OverflowAmount),
		"change_amount_08":           SaveTwoEffectiveDigits(p.Data.H08.ChangeAmount),

		"receivable_09":              SaveTwoEffectiveDigits(p.Data.H09.Receivable),
		"pay_amount_09":              SaveTwoEffectiveDigits(p.Data.H09.PayAmount),
		"transfer_real_amount_09":    SaveTwoEffectiveDigits(p.Data.H09.TransferRealAmount),
		"payment_transfer_amount_09": SaveTwoEffectiveDigits(p.Data.H09.PaymentTransferAmount),
		"item_count_09":              p.Data.H09.ItemCount,
		"item_count_returned_09":     p.Data.H09.ItemCountReturned,
		"rounding_09":                SaveTwoEffectiveDigits(p.Data.H09.Rounding),
		"overflow_amount_09":         SaveTwoEffectiveDigits(p.Data.H09.OverflowAmount),
		"change_amount_09":           SaveTwoEffectiveDigits(p.Data.H09.ChangeAmount),

		"receivable_10":              SaveTwoEffectiveDigits(p.Data.H10.Receivable),
		"pay_amount_10":              SaveTwoEffectiveDigits(p.Data.H10.PayAmount),
		"transfer_real_amount_10":    SaveTwoEffectiveDigits(p.Data.H10.TransferRealAmount),
		"payment_transfer_amount_10": SaveTwoEffectiveDigits(p.Data.H10.PaymentTransferAmount),
		"item_count_10":              p.Data.H10.ItemCount,
		"item_count_returned_10":     p.Data.H10.ItemCountReturned,
		"rounding_10":                SaveTwoEffectiveDigits(p.Data.H10.Rounding),
		"overflow_amount_10":         SaveTwoEffectiveDigits(p.Data.H10.OverflowAmount),
		"change_amount_10":           SaveTwoEffectiveDigits(p.Data.H10.ChangeAmount),

		"receivable_11":              SaveTwoEffectiveDigits(p.Data.H11.Receivable),
		"pay_amount_11":              SaveTwoEffectiveDigits(p.Data.H11.PayAmount),
		"transfer_real_amount_11":    SaveTwoEffectiveDigits(p.Data.H11.TransferRealAmount),
		"payment_transfer_amount_11": SaveTwoEffectiveDigits(p.Data.H11.PaymentTransferAmount),
		"item_count_11":              p.Data.H11.ItemCount,
		"item_count_returned_11":     p.Data.H11.ItemCountReturned,
		"rounding_11":                SaveTwoEffectiveDigits(p.Data.H11.Rounding),
		"overflow_amount_11":         SaveTwoEffectiveDigits(p.Data.H11.OverflowAmount),
		"change_amount_11":           SaveTwoEffectiveDigits(p.Data.H11.ChangeAmount),

		"receivable_12":              SaveTwoEffectiveDigits(p.Data.H12.Receivable),
		"pay_amount_12":              SaveTwoEffectiveDigits(p.Data.H12.PayAmount),
		"transfer_real_amount_12":    SaveTwoEffectiveDigits(p.Data.H12.TransferRealAmount),
		"payment_transfer_amount_12": SaveTwoEffectiveDigits(p.Data.H12.PaymentTransferAmount),
		"item_count_12":              p.Data.H12.ItemCount,
		"item_count_returned_12":     p.Data.H12.ItemCountReturned,
		"rounding_12":                SaveTwoEffectiveDigits(p.Data.H12.Rounding),
		"overflow_amount_12":         SaveTwoEffectiveDigits(p.Data.H12.OverflowAmount),
		"change_amount_12":           SaveTwoEffectiveDigits(p.Data.H12.ChangeAmount),

		"receivable_13":              SaveTwoEffectiveDigits(p.Data.H13.Receivable),
		"pay_amount_13":              SaveTwoEffectiveDigits(p.Data.H13.PayAmount),
		"transfer_real_amount_13":    SaveTwoEffectiveDigits(p.Data.H13.TransferRealAmount),
		"payment_transfer_amount_13": SaveTwoEffectiveDigits(p.Data.H13.PaymentTransferAmount),
		"item_count_13":              p.Data.H13.ItemCount,
		"item_count_returned_13":     p.Data.H13.ItemCountReturned,
		"rounding_13":                SaveTwoEffectiveDigits(p.Data.H13.Rounding),
		"overflow_amount_13":         SaveTwoEffectiveDigits(p.Data.H13.OverflowAmount),
		"change_amount_13":           SaveTwoEffectiveDigits(p.Data.H13.ChangeAmount),

		"receivable_14":              SaveTwoEffectiveDigits(p.Data.H14.Receivable),
		"pay_amount_14":              SaveTwoEffectiveDigits(p.Data.H14.PayAmount),
		"transfer_real_amount_14":    SaveTwoEffectiveDigits(p.Data.H14.TransferRealAmount),
		"payment_transfer_amount_14": SaveTwoEffectiveDigits(p.Data.H14.PaymentTransferAmount),
		"item_count_14":              p.Data.H14.ItemCount,
		"item_count_returned_14":     p.Data.H14.ItemCountReturned,
		"rounding_14":                SaveTwoEffectiveDigits(p.Data.H14.Rounding),
		"overflow_amount_14":         SaveTwoEffectiveDigits(p.Data.H14.OverflowAmount),
		"change_amount_14":           SaveTwoEffectiveDigits(p.Data.H14.ChangeAmount),

		"receivable_15":              SaveTwoEffectiveDigits(p.Data.H15.Receivable),
		"pay_amount_15":              SaveTwoEffectiveDigits(p.Data.H15.PayAmount),
		"transfer_real_amount_15":    SaveTwoEffectiveDigits(p.Data.H15.TransferRealAmount),
		"payment_transfer_amount_15": SaveTwoEffectiveDigits(p.Data.H15.PaymentTransferAmount),
		"item_count_15":              p.Data.H15.ItemCount,
		"item_count_returned_15":     p.Data.H15.ItemCountReturned,
		"rounding_15":                SaveTwoEffectiveDigits(p.Data.H15.Rounding),
		"overflow_amount_15":         SaveTwoEffectiveDigits(p.Data.H15.OverflowAmount),
		"change_amount_15":           SaveTwoEffectiveDigits(p.Data.H15.ChangeAmount),

		"receivable_16":              SaveTwoEffectiveDigits(p.Data.H16.Receivable),
		"pay_amount_16":              SaveTwoEffectiveDigits(p.Data.H16.PayAmount),
		"transfer_real_amount_16":    SaveTwoEffectiveDigits(p.Data.H16.TransferRealAmount),
		"payment_transfer_amount_16": SaveTwoEffectiveDigits(p.Data.H16.PaymentTransferAmount),
		"item_count_16":              p.Data.H16.ItemCount,
		"item_count_returned_16":     p.Data.H16.ItemCountReturned,
		"rounding_16":                SaveTwoEffectiveDigits(p.Data.H16.Rounding),
		"overflow_amount_16":         SaveTwoEffectiveDigits(p.Data.H16.OverflowAmount),
		"change_amount_16":           SaveTwoEffectiveDigits(p.Data.H16.ChangeAmount),

		"receivable_17":              SaveTwoEffectiveDigits(p.Data.H17.Receivable),
		"pay_amount_17":              SaveTwoEffectiveDigits(p.Data.H17.PayAmount),
		"transfer_real_amount_17":    SaveTwoEffectiveDigits(p.Data.H17.TransferRealAmount),
		"payment_transfer_amount_17": SaveTwoEffectiveDigits(p.Data.H17.PaymentTransferAmount),
		"item_count_17":              p.Data.H17.ItemCount,
		"item_count_returned_17":     p.Data.H17.ItemCountReturned,
		"rounding_17":                SaveTwoEffectiveDigits(p.Data.H17.Rounding),
		"overflow_amount_17":         SaveTwoEffectiveDigits(p.Data.H17.OverflowAmount),
		"change_amount_17":           SaveTwoEffectiveDigits(p.Data.H17.ChangeAmount),

		"receivable_18":              SaveTwoEffectiveDigits(p.Data.H18.Receivable),
		"pay_amount_18":              SaveTwoEffectiveDigits(p.Data.H18.PayAmount),
		"transfer_real_amount_18":    SaveTwoEffectiveDigits(p.Data.H18.TransferRealAmount),
		"payment_transfer_amount_18": SaveTwoEffectiveDigits(p.Data.H18.PaymentTransferAmount),
		"item_count_18":              p.Data.H18.ItemCount,
		"item_count_returned_18":     p.Data.H18.ItemCountReturned,
		"rounding_18":                SaveTwoEffectiveDigits(p.Data.H18.Rounding),
		"overflow_amount_18":         SaveTwoEffectiveDigits(p.Data.H18.OverflowAmount),
		"change_amount_18":           SaveTwoEffectiveDigits(p.Data.H18.ChangeAmount),

		"receivable_19":              SaveTwoEffectiveDigits(p.Data.H19.Receivable),
		"pay_amount_19":              SaveTwoEffectiveDigits(p.Data.H19.PayAmount),
		"transfer_real_amount_19":    SaveTwoEffectiveDigits(p.Data.H19.TransferRealAmount),
		"payment_transfer_amount_19": SaveTwoEffectiveDigits(p.Data.H19.PaymentTransferAmount),
		"item_count_19":              p.Data.H19.ItemCount,
		"item_count_returned_19":     p.Data.H19.ItemCountReturned,
		"rounding_19":                SaveTwoEffectiveDigits(p.Data.H19.Rounding),
		"overflow_amount_19":         SaveTwoEffectiveDigits(p.Data.H19.OverflowAmount),
		"change_amount_19":           SaveTwoEffectiveDigits(p.Data.H19.ChangeAmount),

		"receivable_20":              SaveTwoEffectiveDigits(p.Data.H20.Receivable),
		"pay_amount_20":              SaveTwoEffectiveDigits(p.Data.H20.PayAmount),
		"transfer_real_amount_20":    SaveTwoEffectiveDigits(p.Data.H20.TransferRealAmount),
		"payment_transfer_amount_20": SaveTwoEffectiveDigits(p.Data.H20.PaymentTransferAmount),
		"item_count_20":              p.Data.H20.ItemCount,
		"item_count_returned_20":     p.Data.H20.ItemCountReturned,
		"rounding_20":                SaveTwoEffectiveDigits(p.Data.H20.Rounding),
		"overflow_amount_20":         SaveTwoEffectiveDigits(p.Data.H20.OverflowAmount),
		"change_amount_20":           SaveTwoEffectiveDigits(p.Data.H20.ChangeAmount),

		"receivable_21":              SaveTwoEffectiveDigits(p.Data.H21.Receivable),
		"pay_amount_21":              SaveTwoEffectiveDigits(p.Data.H21.PayAmount),
		"transfer_real_amount_21":    SaveTwoEffectiveDigits(p.Data.H21.TransferRealAmount),
		"payment_transfer_amount_21": SaveTwoEffectiveDigits(p.Data.H21.PaymentTransferAmount),
		"item_count_21":              p.Data.H21.ItemCount,
		"item_count_returned_21":     p.Data.H21.ItemCountReturned,
		"rounding_21":                SaveTwoEffectiveDigits(p.Data.H21.Rounding),
		"overflow_amount_21":         SaveTwoEffectiveDigits(p.Data.H21.OverflowAmount),
		"change_amount_21":           SaveTwoEffectiveDigits(p.Data.H21.ChangeAmount),

		"receivable_22":              SaveTwoEffectiveDigits(p.Data.H22.Receivable),
		"pay_amount_22":              SaveTwoEffectiveDigits(p.Data.H22.PayAmount),
		"transfer_real_amount_22":    SaveTwoEffectiveDigits(p.Data.H22.TransferRealAmount),
		"payment_transfer_amount_22": SaveTwoEffectiveDigits(p.Data.H22.PaymentTransferAmount),
		"item_count_22":              p.Data.H22.ItemCount,
		"item_count_returned_22":     p.Data.H22.ItemCountReturned,
		"rounding_22":                SaveTwoEffectiveDigits(p.Data.H22.Rounding),
		"overflow_amount_22":         SaveTwoEffectiveDigits(p.Data.H22.OverflowAmount),
		"change_amount_22":           SaveTwoEffectiveDigits(p.Data.H22.ChangeAmount),

		"receivable_23":              SaveTwoEffectiveDigits(p.Data.H23.Receivable),
		"pay_amount_23":              SaveTwoEffectiveDigits(p.Data.H23.PayAmount),
		"transfer_real_amount_23":    SaveTwoEffectiveDigits(p.Data.H23.TransferRealAmount),
		"payment_transfer_amount_23": SaveTwoEffectiveDigits(p.Data.H23.PaymentTransferAmount),
		"item_count_23":              p.Data.H23.ItemCount,
		"item_count_returned_23":     p.Data.H23.ItemCountReturned,
		"rounding_23":                SaveTwoEffectiveDigits(p.Data.H23.Rounding),
		"overflow_amount_23":         SaveTwoEffectiveDigits(p.Data.H23.OverflowAmount),
		"change_amount_23":           SaveTwoEffectiveDigits(p.Data.H23.ChangeAmount),
	}
}
