package model

import (
	"github.com/spf13/cast"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/SmallTianTian/go-tools/slice"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

type DashboardRequest struct {
	IsToday         bool      `json:"is_today,omitempty"`          // 是否只查当日
	IsPre           bool      `json:"is_pre,omitempty"`            // 是否走预计算
	Start           time.Time `json:"start,omitempty"`             // 开始时间，无时区概念，对应bus_date
	End             time.Time `json:"end,omitempty"`               // 结束时间，无时区概念，对应bus_date
	RegionSearchIds []string  `json:"region_search_ids,omitempty"` // 门店id列表/地理区域id列表/管理区域id列表
	Panel           []string  `json:"panel,omitempty"`             // 订单类型(INEIN/TAKEAWAY/TAKEOUT/SELFHELP等
	Lan             string    `json:"lan,omitempty"`               // 语言
}

// 异常电子小票
type ErrorTicketRequest struct {
	StoreId      int64           `json:"store_id,omitempty"`
	Status       SalesInfoStatus `json:"status,omitempty"`
	BusDate      string          `json:"bus_date,omitempty"`
	OrderDate    string          `json:"order_date,omitempty"`
	CreateDate   string          `json:"create_date,omitempty"`
	UpdateDate   string          `json:"update_date,omitempty"`
	Limit        int             `json:"limit,omitempty"`  // 默认10, 0则转为10，-1则无限制
	Offset       int             `json:"offset,omitempty"` // 默认0
	IncludeTotal string          `json:"include_total,omitempty"`
	Lan          string          `json:"lan,omitempty"` // 语言
	ErrMsg       string          `json:"err_msg,omitempty"`
	ChannelId    string          `json:"channel_id,omitempty"`
}

// sql公共请求查询
type CommonRequest struct {
	Limit              int       `json:"limit,omitempty"`                 // 默认10, 0则转为10，-1则无限制
	Offset             int       `json:"offset,omitempty"`                // 默认0
	Sort               string    `json:"sort,omitempty"`                  // 排序字段, 默认BUS_DATE(暂时只支持这个字段)
	Order              string    `json:"order,omitempty"`                 // ASC(默认)/DESC
	IncludeTotal       bool      `json:"include_total,omitempty"`         // 默认false, 是否返回总行数
	IncludeSummary     bool      `json:"include_summary,omitempty"`       // 默认false, 是否返回汇总行
	IsToday            bool      `json:"is_today,omitempty"`              // 是否只查当日
	IsPre              bool      `json:"is_pre,omitempty"`                // 是否走预计算
	PeriodGroupType    string    `json:"period_group_type,omitempty"`     // 周期汇总类型, DAY(按天，默认)/WEEK(按周)/MONTH(按月)
	Start              time.Time `json:"start,omitempty"`                 // 开始时间，无时区概念，对应bus_date
	End                time.Time `json:"end,omitempty"`                   // 结束时间，无时区概念，对应bus_date
	CompareStart       string    `json:"compare_start,omitempty"`         // 环比开始时间
	CompareEnd         string    `json:"compare_end,omitempty"`           // 环比结束时间
	CompareSameStart   string    `json:"compare_same_start"`              // 同比开始时间
	CompareSameEnd     string    `json:"compare_same_end"`                // 同比结束时间
	RegionGroupType    string    `json:"region_group_type,omitempty"`     // 区域汇总类型，STORE(门店)/GEO_REGION(地理区域)/BRANCH_REGION(管理区域)
	RegionGroupLevel   int       `json:"region_group_level,omitempty"`    // 区域汇总层级, 0(第一级)/1(第二级)/2(第三级)
	RegionSearchType   string    `json:"region_search_type,omitempty"`    // 区域查询类型，STORE(门店)/GEO_REGION(地理区域)/BRANCH_REGION(管理区域)
	RegionSearchIds    []string  `json:"region_search_ids,omitempty"`     // 门店id列表/地理区域id列表/管理区域id列表
	ProductCategoryIds []string  `json:"product_category_ids,omitempty"`  // 商品类别IDS
	ProductCategory0Id string    `json:"product_category_0_id,omitempty"` // 商品一级类别
	ProductCategory1Id string    `json:"product_category_1_id,omitempty"` // 商品二级类别
	ProductCategory2Id string    `json:"product_category_2_id,omitempty"` // 商品三级类别
	ProductIds         []string  `json:"product_ids,omitempty"`           // 商品IDS
	PaymentIds         []string  `json:"payment_ids,omitempty"`           // 支付方式IDS
	DiscountIds        []string  `json:"discount_ids,omitempty"`          // 折扣方式IDS
	ChannelIds         []string  `json:"channel_ids,omitempty"`           // 渠道IDS
	ChannelId          string    `json:"channel_id,omitempty"`            // 渠道id
	Channels           []string  `json:"channels,omitempty"`              // channel_code
	OrderType          string    `json:"order_type,omitempty"`            // 订单类型(INEIN/TAKEAWAY/TAKEOUT/SELFHELP等
	Lan                string    `json:"lan,omitempty"`                   // 语言
	StoreType          string    `json:"store_type,omitempty"`            // 门店类型
	StoreTypes         []string  `json:"store_types,omitempty"`           // 门店类型数组
	OpenStatus         string    `json:"open_status,omitempty"`           // 开店类型
	RefundCode         string    `json:"refund_code,omitempty"`           // 退单原因code
	RefundSide         int64     `json:"refund_side,omitempty"`           // 退单方
	TagType            string    `json:"tag_type,omitempty"`              // tag标签类型
	Logistic           string    `json:"logistic,omitempty"`              // tag标签类型
	PriceScope         string    `json:"price_scope,omitempty"`
	Period             []int64   `json:"period,omitempty"` // 时段
	IsCombo            bool      `json:"is_combo"`         // 是否统计套餐商品
	TagTypes           string    `json:"tag_types"`        // 仅用户商品属性表,区分是口味：TASTE;属性:ATTRIBUTE;加料:FEED

	IsNatural bool      `json:"is_natural"` // 营业日、自然日
	Taxes     []float64 `json:"tax"`        // 商品税率

	OrderStatus string `json:"order_status"`         // 订单状态SALE/REFUND
	ProductId   string `json:"product_id,omitempty"` // 商品id
	Code        string `json:"code,omitempty"`       // code

	StoreTags             []string `json:"store_tags,omitempty"` // 门店标签
	IncludeRealAmountZero bool     `json:"include_real_amount_zero,omitempty"`
	RegionCodes           []string `json:"region_codes,omitempty"`      // 省市区
	CouponChannels        []string `json:"coupon_channels,omitempty"`   // 省市区
	ComboProductIds       []string `json:"combo_product_ids,omitempty"` // 套餐商品id
	Search                string   `json:"search"`
	TicketNo              string   `json:"ticket_no"`
	Timezone              string   `json:"timezone"`
	ProductAttributeCodes []string `json:"product_attribute_codes,omitempty"`
}

func (cr *DashboardRequest) ToRepoCondition() *RepoDashboardCondition {
	ids := make([][]int64, 6)
	for i, item := range [][]string{cr.RegionSearchIds} {
		for _, id := range item {
			ids[i] = append(ids[i], utils.StringMust2Int(id))
		}
	}

	condition := &RepoDashboardCondition{
		//PartnerId:       int64(ui.PartnerId),
		//ScopeId:         int64(ui.ScopeId),
		//UserId:          int64(ui.UserId),
		IsPre:           cr.IsPre,
		RegionSearchIds: ids[0],
		Panel:           cr.Panel,
	}

	if condition.Start, condition.End = cr.Start, cr.End; cr.IsToday {
		condition.End = condition.Start
	}
	return condition
}

const pattern = "^[0-9]+\\.?[0-9]*-[0-9]+\\.?[0-9]*$" // 正则表达式匹配形如："10.0-22.11"的字符串

// 生成查询条件的struct
func (cr *CommonRequest) ToRepoCondition(tableSortField []string) *RepoCondition {
	// RegionSearchIds
	// ProductCategoryIds
	// ProductIds
	// PaymentIds
	// DiscountIds
	ids := make([][]int64, 8)
	for i, item := range [][]string{cr.RegionSearchIds, cr.RegionSearchIds, cr.ProductCategoryIds, cr.ProductIds, cr.PaymentIds, cr.DiscountIds, cr.ChannelIds, cr.ComboProductIds} {
		for _, id := range item {
			ids[i] = append(ids[i], utils.StringMust2Int(id))
		}
	}
	refundSide := ""
	switch cr.RefundSide {
	case 0:
		refundSide = ""
	case 1:
		refundSide = "MERCHANT"
	case 2:
		refundSide = "CUSTOMER"
	}
	category0Id, _ := strconv.ParseInt(cr.ProductCategory0Id, 10, 64)
	category1Id, _ := strconv.ParseInt(cr.ProductCategory1Id, 10, 64)
	category2Id, _ := strconv.ParseInt(cr.ProductCategory2Id, 10, 64)
	condition := &RepoCondition{
		PartnerId:        0,
		ScopeId:          0,
		UserId:           0,
		Desc:             strings.ToUpper(cr.Order) == "DESC",
		IncludeTotal:     cr.IncludeTotal,
		IncludeSummary:   cr.IncludeSummary,
		IsPre:            cr.IsPre,
		PeriodGroupType:  NewPeriodGroupType(cr.PeriodGroupType),
		RegionGroupType:  cr.RegionGroupType,
		RegionGroupLevel: cr.RegionGroupLevel,
		RegionSearchType: cr.RegionSearchType,
		RegionSearchIds:  ids[0],
		//RegionIds:          ids[1],
		ProductCategoryIds: ids[2],
		ProductCategory0Id: category0Id,
		ProductCategory1Id: category1Id,
		ProductCategory2Id: category2Id,
		ProductIds:         ids[3],
		PaymentIds:         ids[4],
		DiscountIds:        ids[5],
		ChannelIds:         ids[6],
		ChannelId:          utils.StringMust2Int(cr.ChannelId),
		Channels:           cr.Channels,
		StoreType:          cr.StoreType,
		StoreTypes:         cr.StoreTypes,
		OpenStatus:         cr.OpenStatus,
		RefundSide:         refundSide,    // 退单方:商家/用户/全部
		RefundCode:         cr.RefundCode, // 退单原因code
		TagType:            cr.TagType,
		OrderType:          cr.OrderType,
		Logistic:           cr.Logistic,
		Period:             cr.Period,
		IsToday:            cr.IsToday,
		Start:              cr.Start,
		End:                cr.End,
		CompareStart:       cr.CompareStart,
		CompareEnd:         cr.CompareEnd,
		CompareSameStart:   cr.CompareSameStart,
		CompareSameEnd:     cr.CompareSameEnd,
		IsCombo:            cr.IsCombo, // 是否统计套餐商品
		TagTypes:           cr.TagTypes,

		Taxes:     cr.Taxes,
		IsNatural: cr.IsNatural,

		OrderStatus: cr.OrderStatus,
		ProductId:   cast.ToInt64(cr.ProductId),
		Code:        cr.Code,

		StoreTags:              cr.StoreTags,
		IncludeRealAmountZero:  cr.IncludeRealAmountZero,
		RegionCodes:            cr.RegionCodes,
		CouponChannels:         cr.CouponChannels,
		ComboProductIds:        ids[7],
		Search:                 cr.Search,
		TicketNo:               cr.TicketNo,
		ProductAttributesCodes: cr.ProductAttributeCodes,
	}
	// 是否要计算环比
	if condition.CompareStart != "" && condition.CompareEnd != "" {
		condition.IsCompare = true
	}
	// 是否要计算同比
	if condition.CompareSameStart != "" && condition.CompareSameEnd != "" {
		condition.IsCompareSame = true
	}
	if condition.Limit = int64(cr.Limit); condition.Limit == 0 {
		condition.Limit = 10
	}
	if condition.Offset = uint64(cr.Offset); cr.Offset < 0 {
		condition.Offset = 0
	}
	// 在传入不认识的字段的时候，这里必须要过滤掉
	if condition.Sort = cr.Sort; !slice.StringInSlice(tableSortField, condition.Sort) {
		condition.Sort = ""
	}
	arr := strings.Split(cr.PriceScope, ",")
	if len(arr) > 0 {
		priceScope := make([][]float64, 0, len(arr))
		for _, i := range arr {
			if result, ok := regexp.MatchString(pattern, i); result && ok == nil {
				prices := strings.Split(i, "-")
				priceLow := cast.ToFloat64(prices[0])
				priceHigh := cast.ToFloat64(prices[1])
				priceScope = append(priceScope, []float64{priceLow, priceHigh})
			} else {
				return condition
			}
		}
		condition.PriceScope = priceScope
	}
	return condition
}
