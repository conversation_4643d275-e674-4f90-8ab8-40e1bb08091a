package model

import "time"

const (
	// 首页字段排序
	DashboardOrderReportConfigType = "dashboard_order"
)

type ReportConfig struct {
	Id        int64     `gorm:"primary_key" json:"id"`
	PartnerId int64     `json:"partner_id"`
	UserId    int64     `json:"user_id"`
	Type      string    `json:"type"`
	Config    string    `json:"config"`
	Created   time.Time `gorm:"not null" json:"created"` // 创建时间
	Updated   time.Time `gorm:"not null" json:"updated"` // 更新时间
}

func (*ReportConfig) TableName() string {
	return "report_config"
}
