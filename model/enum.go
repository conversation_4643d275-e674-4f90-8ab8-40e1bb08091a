package model

type TicketStatus string

func (t TicketStatus) String() string {
	return string(t)
}

const (
	SALE            TicketStatus = "SALE"            // 销售单
	REFUND          TicketStatus = "REFUND"          // 退单
	REFUNDCOMPLETED TicketStatus = "REFUNDCOMPLETED" //	退单原单
	RESET           TicketStatus = "RESET"           //	重结单
	PARTIALREFUND   TicketStatus = "PARTIALREFUND"   //	部分退款单
)

type SalesInfoStatus string

func (s SalesInfoStatus) String() string {
	return string(s)
}

func NewSalesInfoStatus(v string) SalesInfoStatus {
	switch v {
	// 如果装饰是以下几种，那就返回这个状态的字符串
	case Init.String(), DataError.String(), SysError.String(), Finish.String():
		return SalesInfoStatus(v)
	default:
		return NilSalesInfoStatus
	}
}

const (
	NilSalesInfoStatus SalesInfoStatus = ""
	Init               SalesInfoStatus = "INIT"      // 新增
	DataError          SalesInfoStatus = "DATAERROR" // 数据异常
	SysError           SalesInfoStatus = "SYSERROR"  // 系统异常
	Finish             SalesInfoStatus = "FINISH"    // 完成
)

type SalesInfoProcessStatus string

func (s SalesInfoProcessStatus) String() string {
	return string(s)
}

const (
	Process_Init       SalesInfoProcessStatus = "INIT"       // 新增
	Process_Valid      SalesInfoProcessStatus = "VALID"      // 验证成功
	Process_Discounttr SalesInfoProcessStatus = "DISCOUNTTR" // 折扣转换成功
	Process_Discountsh SalesInfoProcessStatus = "DISCOUNTSH" // 折扣分摊成功
	Process_Finish     SalesInfoProcessStatus = "FINISH"     // 完成
	Process_ReInit     SalesInfoProcessStatus = "REINIT"     // 完成
)

type SalesTimeKey string

func (s SalesTimeKey) String() string {
	return string(s)
}

const (
	// value is database key
	BusDate    SalesTimeKey = "bus_date"
	OrderDate  SalesTimeKey = "order_time"
	CreateDate SalesTimeKey = "created"
	UpdateDate SalesTimeKey = "updated"
)

// 定义用餐方式排序用的顺序映射
var OrderMap = map[string]int{
	"DINEIN":   0,
	"SELFHELP": 1,
	"TAKEOUT":  2,
	"TAKEAWAY": 3,
}

type TicketProductType string

func (s TicketProductType) String() string {
	return string(s)
}

const (
	ProductTypeNORMAL   TicketProductType = "NORMAL"
	ProductTypeSET      TicketProductType = "SET"
	ProductTypeADDITION TicketProductType = "ADDITION"
	ProductTypeREMARK   TicketProductType = "REMARK"

	ProductTypeFLEXSET TicketProductType = "FLEX_SET" // tji上游加的，灵活套餐
)

type TicketPromotionDiscountLevel string

func (s TicketPromotionDiscountLevel) String() string {
	return string(s)
}

const (
	PromotionDiscountLevelOrder TicketPromotionDiscountLevel = "1"
	PromotionDiscountLevelSku   TicketPromotionDiscountLevel = "2"
)
