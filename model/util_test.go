package model

import (
	"fmt"
	"testing"
)

var ticket = &Ticket{
	StartTime: "2022-12-27 12:49:01",
	EndTime:   "2022-12-27 12:52:01",
	TakeawayInfo: TicketTakeaway{
		DeliverTime: "2022-12-28 12:01:02",
	},
	Channel: TicketChannel{
		//DeliveryType: "BOOKING",
		DeliveryType: "REALTIME",
		Source:       "POS",
	},
}

func Test_GetOrderDateTime(t *testing.T) {

	dateTime := GetOrderDateTime(ticket)
	fmt.Printf("dateTime: %v\n", dateTime)
}

func Test_GetNaturalDateTime(t *testing.T) {

	dateTime := GetNaturalDate(ticket)

	fmt.Printf("dateTime: %v\n", dateTime)
}
