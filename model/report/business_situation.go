package report

type BusinessSituationResponse struct {
	Rows            []*BusinessSituation `json:"rows"`
	CompareRows     []*BusinessSituation `json:"compare_rows"`      // 环比
	CompareSameRows []*BusinessSituation `json:"compare_same_rows"` // 同比
}

type BusinessSituation struct {
	StoreNumber            int64   `json:"store_number"`              // 门店数
	ProductCount           int64   `json:"product_count"`             // 商品销售数量
	CupCount               int64   `json:"cup_count"`                 // 商品杯数
	BusinessAmount         float64 `json:"business_amount"`           // 营业额流水
	ExpendAmount           float64 `json:"expend_amount"`             // 支出(交易)
	RealAmount             float64 `json:"real_amount"`               // 实收(交易)
	GrossAmount            float64 `json:"gross_amount"`              // 商品流水
	NetAmount              float64 `json:"net_amount"`                // 商品实收
	DiscountAmount         float64 `json:"discount_amount"`           // 折扣
	RefundOrderCount       int64   `json:"refund_order_count"`        // 退单数
	ValidOrderCount        int64   `json:"valid_order_count"`         // 有效订单数
	CustomerPrice          float64 `json:"customer_price"`            // 单均价
	MerchantAllowance      float64 `json:"merchant_allowance"`        // 商家活动补贴
	TransferRealAmount     float64 `json:"transfer_real_amount"`      // 实收金额(财务)
	DiscountContribute     float64 `json:"discount_contribute"`       // 优惠组成(财务)
	AverageBusinessAmount  float64 `json:"average_business_amount"`   // 店均营业额
	AverageValidOrderCount float64 `json:"average_valid_order_count"` // 店均订单数
	Child                  []struct {
		OrderType          string  `json:"order_type"`
		BusinessAmount     float64 `json:"business_amount"`
		TransferRealAmount float64 `json:"transfer_real_amount"`
	} `json:"child"`

	StoreBusinessAmount       float64 `json:"store_business_amount"`        // 门店流水
	TakeoutBusinessAmount     float64 `json:"takeout_business_amount"`      // 外卖流水
	StoreTransferRealAmount   float64 `json:"store_transfer_real_amount"`   // 门店财务实收
	TakeoutTransferRealAmount float64 `json:"takeout_transfer_real_amount"` // 外卖财务实收

	CompareBusinessAmount            float64 `json:"compare_business_amount"`              // 环比营业额流水
	CompareExpendAmount              float64 `json:"compare_expend_amount"`                // 环比支出交易
	CompareRealAmount                float64 `json:"compare_real_amount"`                  // 环比实收交易
	CompareMerchantAllowance         float64 `json:"compare_merchant_allowance"`           // 环比商家补贴
	CompareValidOrderCount           int64   `json:"compare_valid_order_count"`            // 环比有效订单数
	CompareRefundOrderCount          int64   `json:"compare_refund_order_count"`           // 环比退单数
	CompareCustomerPrice             float64 `json:"compare_customer_price"`               // 环比单均价
	CompareDiscountContribute        float64 `json:"compare_discount_contribute"`          // 环比优惠组成
	CompareAverageBusinessAmount     float64 `json:"compare_average_business_amount"`      // 环比店均营业额
	CompareTransferRealAmount        float64 `json:"compare_transfer_real_amount"`         // 环比财务实收
	CompareAverageValidOrderCount    float64 `json:"compare_average_valid_order_count"`    // 环比店均订单数
	CompareProductCount              int64   `json:"compare_product_count"`                // 环比商品数量
	CompareCupCount                  int64   `json:"compare_cup_count"`                    // 环比商品杯数
	CompareStoreBusinessAmount       float64 `json:"compare_store_business_amount"`        // 环比门店流水
	CompareTakeoutBusinessAmount     float64 `json:"compare_takeout_business_amount"`      // 环比外卖流水
	CompareStoreTransferRealAmount   float64 `json:"compare_store_transfer_real_amount"`   // 环比门店财务实收
	CompareTakeoutTransferRealAmount float64 `json:"compare_takeout_transfer_real_amount"` // 环比外卖财务实收

	CompareSameBusinessAmount            float64 `json:"compare_same_business_amount"`              // 同比营业额流水
	CompareSameExpendAmount              float64 `json:"compare_same_expend_amount"`                // 同比支出交易
	CompareSameRealAmount                float64 `json:"compare_same_real_amount"`                  // 同比实收交易
	CompareSameMerchantAllowance         float64 `json:"compare_same_merchant_allowance"`           // 同比商家补贴
	CompareSameValidOrderCount           int64   `json:"compare_same_valid_order_count"`            // 同比有效订单数
	CompareSameRefundOrderCount          int64   `json:"compare_same_refund_order_count"`           // 同比退单数
	CompareSameCustomerPrice             float64 `json:"compare_same_customer_price"`               // 同比单均价
	CompareSameDiscountContribute        float64 `json:"compare_same_discount_contribute"`          // 同比优惠组成
	CompareSameAverageBusinessAmount     float64 `json:"compare_same_average_business_amount"`      // 同比店均营业额
	CompareSameTransferRealAmount        float64 `json:"compare_same_transfer_real_amount"`         // 同比财务实收
	CompareSameAverageValidOrderCount    float64 `json:"compare_same_average_valid_order_count"`    // 同比店均订单数
	CompareSameProductCount              int64   `json:"compare_same_product_count"`                // 同比商品数量
	CompareSameCupCount                  int64   `json:"compare_same_cup_count"`                    // 同比商品杯数
	CompareSameStoreBusinessAmount       float64 `json:"compare_same_store_business_amount"`        // 同比门店流水
	CompareSameTakeoutBusinessAmount     float64 `json:"compare_same_takeout_business_amount"`      // 同比外卖流水
	CompareSameStoreTransferRealAmount   float64 `json:"compare_same_store_transfer_real_amount"`   // 同比门店财务实收
	CompareSameTakeoutTransferRealAmount float64 `json:"compare_same_takeout_transfer_real_amount"` // 同比外卖财务实收
}
