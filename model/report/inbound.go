package report

import (
	"encoding/json"
	protobuf_struct "github.com/golang/protobuf/ptypes/struct"
)

type InboundResponse struct {
	Rows  []*InboundRow `json:"rows"`
	Total int64         `json:"total"`
}

type InboundRow struct {
	StoreId       int64           `json:"store_id"`
	StoreName     string          `json:"store_name"`
	ProductId     int64           `json:"product_id"`
	ProductCode   string          `json:"product_code"`
	ProductName   string          `json:"product_name"`
	Price         float64         `json:"price"`
	Amount        float64         `json:"amount"`
	SkuRemarkName string          `json:"sku_remark"`
	Qty           float64         `json:"qty"`
	InboundTime   string          `json:"inbound_time"`
	OperatorName  string          `json:"operator_name"`
	Content       json.RawMessage `json:"content"`
}

type PosInboundResponse struct {
	Rows  []*PosInboundRow `json:"rows"`
	Total int64            `json:"total"`
}

type PosInboundRow struct {
	InboundTime  string  `json:"inbound_time"`
	OperatorName string  `json:"operator_name"`
	ProductNames string  `json:"product_names"`
	ProductQty   float64 `json:"product_qty"`
	Content      *protobuf_struct.Struct  `json:"content"`
}
