package report

import "fmt"

type CategorySalesResponse struct {
	Rows    []*CategorySales `json:"rows,omitempty"`
	Summary *CategorySales   `json:"summary,omitempty"`
	Total   int64            `json:"total,omitempty"`
}

type CategorySales struct {
	Total             int64   `json:"total,omitempty"`
	CategoryId        int64   `json:"product_id,omitempty"`          // 商品类目id
	CategoryName      string  `json:"category_name,omitempty"`       // 商品类目名称
	ProductCount      float64 `json:"product_count,omitempty"`       // 商品数量
	NetAmount         float64 `json:"net_amount,omitempty"`          // 商品类目实收
	PercentageOfSales float64 `json:"percentage_of_sales,omitempty"` // 商品类目收入占比
}

func (p *CategorySales) RowToMap() map[string]interface{} {
	return map[string]interface{}{
		"category_name":       p.CategoryName,
		"product_count":       p.ProductCount,
		"net_amount":          p.<PERSON><PERSON>,
		"percentage_of_sales": fmt.Sprintf("%.2f%%", p.PercentageOfSales*100),
	}
}
