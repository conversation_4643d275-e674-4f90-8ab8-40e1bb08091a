package report

type ProductSalesComboSummaryResponse struct {
	Rows    []*ProductSalesComboSummary `json:"rows,omitempty"`
	Summary *ProductSalesComboSummary   `json:"summary,omitempty"`
	Total   int64                       `json:"total,omitempty"`
}

type ProductSalesComboSummary struct {
	RowIdx               int32   `json:"row_idx"` // 行号
	Total                int64   `json:"total"`
	BusDate              string  `json:"bus_date,omitempty"`               // 营业日期
	RegionId             int64   `json:"region_id,omitempty"`              // 所在城市
	BranchRegionId       int64   `json:"branch_region_id,omitempty"`       // 所在城市
	BranchRegion         string  `json:"branch_region,omitempty"`          // 所在城市
	RegionAddress        string  `json:"region_address,omitempty"`         // 所在城市
	RegionAddress1       string  `json:"region_address1,omitempty"`        // 省
	RegionAddress2       string  `json:"region_address2,omitempty"`        // 市
	RegionAddress3       string  `json:"region_address3,omitempty"`        // 区
	RegionCode           string  `json:"region_code,omitempty"`            // 门店编号
	RegionName           string  `json:"region_name,omitempty"`            // 门店名称
	StoreType            string  `json:"store_type,omitempty"`             // 门店经营类型
	StoreTypeName        string  `json:"store_type_name,omitempty"`        // 门店经营类型
	GroupPurchaseChannel string  `json:"group_purchase_channel,omitempty"` // 团购渠道
	CategoryId           int64   `json:"category_id,omitempty"`            //
	CategoryName         string  `json:"category_name,omitempty"`          //
	ProductId            int64   `json:"product_id,omitempty"`             // 商品名称
	ProductName          string  `json:"product_name,omitempty"`           // 商品名称
	ProductCode          string  `json:"product_code,omitempty"`           // 商品编码
	Qty                  float64 `json:"qty,omitempty"`                    // 商品数量
	Price                float64 `json:"price,omitempty"`                  // 商品售价
	GrossAmount          float64 `json:"gross_amount,omitempty"`           // 商品流水
	DiscountAmount       float64 `json:"discount_amount,omitempty"`        // 折扣金额
	NetAmount            float64 `json:"net_amount,omitempty"`             // 商品实收
}
