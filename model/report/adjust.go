package report

type AdjustResponse struct {
	Rows    []*AdjustRow `json:"rows"`
	Total   int64        `json:"total"`
}

type AdjustRow struct {
	SkuId		  int64   `json:"sku_id"`
	OperateTime   string  `json:"operate_time"`
	StoreName     string  `json:"store_name"`
	OperatorName  string  `json:"operator_name"`
	ProductCode   string  `json:"product_code"`
	ProductName   string  `json:"product_name"`
	SkuRemarkName string  `json:"sku_remark_name"`
	ReasonCode    string  `json:"reason_code"`
	ReasonName    string  `json:"reason_name"`
	Price         float64 `json:"price"`
	Qty           float64 `json:"qty"`
	Amount        float64 `json:"amount"`
}
