package report

type ChannelDistributeResponse struct {
	Rows            []*ChannelDistribute `json:"rows"`
	CompareRows     []*ChannelDistribute `json:"compare_rows"`
	CompareSameRows []*ChannelDistribute `json:"compare_same_rows"`
	Summary         *ChannelDistribute   `json:"summary"`
}

// 小掌柜渠道分布
type ChannelDistribute struct {
	ChannelId                    int64   `json:"channel_id"`
	ChannelCode                  string  `json:"channel_code"`
	ChannelName                  string  `json:"channel_name"`
	BusinessAmount               float64 `json:"business_amount"`
	ProportionBusinessAmount     float64 `json:"proportion_business_amount"`
	RealAmount                   float64 `json:"real_amount"`
	ProportionRealAmount         float64 `json:"proportion_real_amount"`
	ValidOrderCount              int64   `json:"valid_order_count"`
	ProportionValidOrderCount    float64 `json:"proportion_valid_order_count"`
	RefundOrderCount             int64   `json:"refund_order_count"`
	ProportionRefundOrderCount   float64 `json:"proportion_refund_order_count"`
	DiscountContribute           float64 `json:"discount_contribute"`
	ProportionDiscountContribute float64 `json:"proportion_discount_contribute"`
	TransferRealAmount           float64 `json:"transfer_real_amount"`
	ProportionTransferRealAmount float64 `json:"proportion_transfer_real_amount"`
	DiscountAmount               float64 `json:"discount_amount"`
	GrossAmountReturned          float64 `json:"gross_amount_returned"`

	CompareBusinessAmount             float64 `json:"compare_business_amount"`
	CompareSameBusinessAmount         float64 `json:"compare_same_business_amount"`
	IncreaseCompareBusinessAmount     float64 `json:"increase_compare_business_amount"`
	IncreaseCompareSameBusinessAmount float64 `json:"increase_compare_same_business_amount"`
}
