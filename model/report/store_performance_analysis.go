package report

type StorePerformanceAnalysisResponse struct {
	Rows    []*StorePerformanceAnalysis `json:"rows"`
	Total   int64                       `json:"total"`
	Summary *StorePerformanceAnalysis   `json:"summary"`
}

type StorePerformanceAnalysis struct {
	StoreId              int64   `json:"store_id"`                // 门店id
	StoreName            string  `json:"store_name"`              // 门店名称
	SeatCount            int64   `json:"seat_count"`              // 座位数目
	BusinessAmount       float64 `json:"business_amount"`         // 营业额
	BowlCount            float64 `json:"bowl_count"`              // 碗数量
	SeatTurnoverRate     float64 `json:"seat_turnover_rate"`      // 座位周转率
	AverageDailySales    float64 `json:"average_daily_sales"`     // 平均日销售额
	AverageSalesPerOrder float64 `json:"average_sales_per_order"` // 单均销售额
	AverageBowlSpending  float64 `json:"average_bowl_spending"`   // Average bowl spending

	WeekdayAverageDailySales             float64                                         `json:"weekday_average_daily_sales"`               // 平日-平均日销售额
	WeekdayOrderTypeMealSegmentTurnovers []*StorePerformanceAnalysisOrderTypeMealSegment `json:"weekday_order_type_meal_segment_turnovers"` // 平日-餐段周转
	WeekdayMealSegmentTurnovers          []*StorePerformanceAnalysisMealSegment          `json:"weekday_meal_segment_turnovers"`            // 平日-餐段周转
	WeekdayChannelTurnovers              []*StorePerformanceAnalysisOrderType            `json:"weekday_channel_turnovers"`                 // 平日-渠道周转

	WeekendAverageDailySales             float64                                         `json:"weekend_average_daily_sales"`               // 周末-平均日销售额
	WeekendOrderTypeMealSegmentTurnovers []*StorePerformanceAnalysisOrderTypeMealSegment `json:"weekend_order_type_meal_segment_turnovers"` // 周末-餐段周转
	WeekendMealSegmentTurnovers          []*StorePerformanceAnalysisMealSegment          `json:"weekend_meal_segment_turnovers"`            // 周末-餐段周转
	WeekendChannelTurnovers              []*StorePerformanceAnalysisOrderType            `json:"weekend_channel_turnovers"`                 // 周末-渠道周转
}

type StorePerformanceAnalysisMealSegment struct {
	MealSegmentName string  `json:"meal_segment_name"` // 餐段名称
	BowlTurnover    float64 `json:"bowl_turnover"`     // 碗周转
}

type StorePerformanceAnalysisOrderType struct {
	OrderType     string  `json:"order_type"`      // 渠道名称
	OrderTypeName string  `json:"order_type_name"` // 渠道名称
	BowlTurnover  float64 `json:"bowl_turnover"`   // 碗周转
}

type StorePerformanceAnalysisOrderTypeMealSegment struct {
	OrderType       string  `json:"order_type"`        // 渠道名称
	OrderTypeName   string  `json:"order_type_name"`   // 渠道名称
	MealSegmentName string  `json:"meal_segment_name"` // 餐段名称
	Name            string  `json:"name"`              // 名称
	BowlTurnover    float64 `json:"bowl_turnover"`     // 碗周转
}
