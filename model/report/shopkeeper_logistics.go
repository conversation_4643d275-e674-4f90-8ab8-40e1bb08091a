package report

type LogisticsRankResponse struct {
	Rows            []*LogisticsRank `json:"rows"`
	CompareRows     []*LogisticsRank `json:"compare_rows"`
	CompareSameRows []*LogisticsRank `json:"compare_same_rows"`
	Summary         *LogisticsRank   `json:"summary"`
}

// 小掌柜渠道分布
type LogisticsRank struct {
	OrderType                 string  `json:"order_type"`
	OrderTypeName             string  `json:"order_type_name"`
	BusinessAmount            float64 `json:"business_amount"`
	ProportionBusinessAmount  float64 `json:"proportion_business_amount"`
	RealAmount                float64 `json:"real_amount"`
	ProportionRealAmount      float64 `json:"proportion_real_amount"`
	ValidOrderCount           int64   `json:"valid_order_count"`
	ProportionValidOrderCount float64 `json:"proportion_valid_order_count"`

	CompareBusinessAmount             float64 `json:"compare_business_amount"`
	CompareSameBusinessAmount         float64 `json:"compare_same_business_amount"`
	IncreaseCompareBusinessAmount     float64 `json:"increase_compare_business_amount"`
	IncreaseCompareSameBusinessAmount float64 `json:"increase_compare_same_business_amount"`
}
