package report

type RefundAnalysisResponse struct {
	Rows    []*RefundAnalysis
	Summary *RefundAnalysis
	Total   int64
}

type RefundAnalysis struct {
	BusDate            string  `json:"bus_date"`        //营业时间
	ChannelId          int64   `json:"channel_id"`      //渠道id
	ChannelCode        string  `json:"channel_code"`    //渠道code
	ChannelName        string  `json:"channel_name"`    //渠道名称
	PosDeviceCode      string  `json:"pos_device_code"` //POS编号
	TicketNo           string  `json:"ticket_no"`       // 交易号
	OrderCountReturned int64   `json:"order_count_returned"`
	PayAmountReturned  float64 `json:"pay_amount_returned"`
	RefundSide         string  `json:"refund_side"`                                   //退款方
	OperatorName       string  `json:"operator_name"`                                 //操作人
	RefundCode         string  `gorm:"type:varchar(15)" json:"refund_code,omitempty"` // 退单原因code
	RefundReason       string  `json:"refund_reason"`                                 //退款原因
	BranchId           int64   `json:"branch_id"`                                     // 管理区域ID
	BranchName         string  `json:"branch_name"`                                   // 管理区域名称
	BranchCode         string  `json:"branch_code"`                                   // 管理区域code
	RegionId           int64   `json:"region_id"`                                     // 门店id
	RegionCode         string  `json:"region_code"`                                   // 门店code
	RegionName         string  `json:"region_name"`                                   // 门店名称
	OriginRealTicketNo string  `json:"origin_real_ticket_no"`                         //取餐号(原单)
	PaymentId          int64   `json:"payment_id"`                                    // 原单支付方式id
	PaymentCode        string  `json:"payment_code"`                                  // 原单支付方式code
	PaymentName        string  `json:"payment_name"`                                  // 原单支付方式名称
	OriginOrderTime    string  `json:"origin_order_time"`                             // 原单交易时间
	OrderTime          string  `json:"order_time"`
}
