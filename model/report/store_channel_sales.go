package report

type StoreChannelSalesResponse struct {
	Rows    []*StoreChannelSales
	Summary *StoreChannelSales
	Total   int64
}

type StoreChannelSales struct {
	BusDate                    string  `json:"bus_date"`
	RegionId                   int64   `json:"region_id"`
	RegionCode                 string  `json:"region_code"`
	RegionName                 string  `json:"region_name"`
	RegionAddress              string  `json:"region_address"`
	RegionAlias                string  `json:"region_alias"`
	StoreType                  string  `json:"store_type"`
	StoreTypeName              string  `json:"store_type_name"`
	BusinessDays               int64   `json:"business_days"`
	BusinessAmount             float64 `json:"business_amount"`
	RealAmount                 float64 `json:"real_amount"`
	ExpendAmount               float64 `json:"expend_amount"`
	CustomerPrice              float64 `json:"customer_price"`
	ValidOrderCount            int64   `json:"valid_order_count"`
	GrossAmount                float64 `json:"gross_amount"`
	NetAmount                  float64 `json:"net_amount"`
	DiscountAmount             float64 `json:"discount_amount"`
	MerchantAllowance          float64 `json:"merchant_allowance"`
	PackageFee                 float64 `json:"package_fee"`
	DeliveryFee                float64 `json:"delivery_fee"`
	SendFee                    float64 `json:"send_fee"`
	MerchantSendFee            float64 `json:"merchant_send_fee"`
	PlatformSendFee            float64 `json:"platform_send_fee"`
	Commission                 float64 `json:"commission"`
	ServiceFee                 float64 `json:"service_fee"`
	Tip                        float64 `json:"tip"`
	Receivable                 float64 `json:"receivable"`
	PayAmount                  float64 `json:"pay_amount"`
	Rounding                   float64 `json:"rounding"`
	OverflowAmount             float64 `json:"overflow_amount"`
	ChangeAmount               float64 `json:"change_amount"`
	TransferRealAmount         float64 `json:"transfer_real_amount"`
	DiscountContribute         float64 `json:"discount_contribute"`
	DiscountMerchantContribute float64 `json:"discount_merchant_contribute"`
	PayMerchantContribute      float64 `json:"pay_merchant_contribute"`
	RealAmountDiscount         float64 `json:"real_amount_discount"`
	RealAmountPayment          float64 `json:"real_amount_payment"`
	Child                      []struct {
		ChannelId                  int64   `json:"channel_id"`
		ChannelCode                string  `json:"channel_code"`
		ChannelName                string  `json:"channel_name"`
		BusinessAmount             float64 `json:"business_amount"`
		RealAmount                 float64 `json:"real_amount"`
		ExpendAmount               float64 `json:"expend_amount"`
		CustomerPrice              float64 `json:"customer_price"`
		ValidOrderCount            int64   `json:"valid_order_count"`
		GrossAmount                float64 `json:"gross_amount"`
		NetAmount                  float64 `json:"net_amount"`
		DiscountAmount             float64 `json:"discount_amount"`
		MerchantAllowance          float64 `json:"merchant_allowance"`
		PackageFee                 float64 `json:"package_fee"`
		DeliveryFee                float64 `json:"delivery_fee"`
		SendFee                    float64 `json:"send_fee"`
		MerchantSendFee            float64 `json:"merchant_send_fee"`
		PlatformSendFee            float64 `json:"platform_send_fee"`
		Commission                 float64 `json:"commission"`
		ServiceFee                 float64 `json:"service_fee"`
		Tip                        float64 `json:"tip"`
		Receivable                 float64 `json:"receivable"`
		PayAmount                  float64 `json:"pay_amount"`
		Rounding                   float64 `json:"rounding"`
		OverflowAmount             float64 `json:"overflow_amount"`
		ChangeAmount               float64 `json:"change_amount"`
		TransferRealAmount         float64 `json:"transfer_real_amount"`
		DiscountContribute         float64 `json:"discount_contribute"`
		DiscountMerchantContribute float64 `json:"discount_merchant_contribute"`
		PayMerchantContribute      float64 `json:"pay_merchant_contribute"`
		RealAmountDiscount         float64 `json:"real_amount_discount"`
		RealAmountPayment          float64 `json:"real_amount_payment"`
		TaxFee                     float64 `json:"tax_fee"`
		Child                      []struct {
			OrderType                  string  `json:"order_type"`
			OrderTypeName              string  `json:"order_type_name"`
			BusinessAmount             float64 `json:"business_amount"`
			RealAmount                 float64 `json:"real_amount"`
			ExpendAmount               float64 `json:"expend_amount"`
			CustomerPrice              float64 `json:"customer_price"`
			ValidOrderCount            int64   `json:"valid_order_count"`
			GrossAmount                float64 `json:"gross_amount"`
			NetAmount                  float64 `json:"net_amount"`
			DiscountAmount             float64 `json:"discount_amount"`
			MerchantAllowance          float64 `json:"merchant_allowance"`
			PackageFee                 float64 `json:"package_fee"`
			DeliveryFee                float64 `json:"delivery_fee"`
			SendFee                    float64 `json:"send_fee"`
			MerchantSendFee            float64 `json:"merchant_send_fee"`
			PlatformSendFee            float64 `json:"platform_send_fee"`
			Commission                 float64 `json:"commission"`
			ServiceFee                 float64 `json:"service_fee"`
			Tip                        float64 `json:"tip"`
			Receivable                 float64 `json:"receivable"`
			PayAmount                  float64 `json:"pay_amount"`
			Rounding                   float64 `json:"rounding"`
			OverflowAmount             float64 `json:"overflow_amount"`
			ChangeAmount               float64 `json:"change_amount"`
			TransferRealAmount         float64 `json:"transfer_real_amount"`
			DiscountContribute         float64 `json:"discount_contribute"`
			DiscountMerchantContribute float64 `json:"discount_merchant_contribute"`
			PayMerchantContribute      float64 `json:"pay_merchant_contribute"`
			RealAmountDiscount         float64 `json:"real_amount_discount"`
			RealAmountPayment          float64 `json:"real_amount_payment"`
			TaxFee                     float64 `json:"tax_fee"`
		}
	}
	Total  int64   `json:"total"`
	TaxFee float64 `json:"tax_fee"`
}
