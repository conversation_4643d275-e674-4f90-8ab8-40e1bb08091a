package report

type DetailForDiscountAndRealAmountResponse struct {
	Rows []*Detail
}

type Detail struct {
	StoreId          int64   `json:"store_id"`
	StoreCode        string  `json:"store_code"`
	StoreName        string  `json:"store_name"`
	DiscountAmount   float64 `json:"discount_amount"`
	RealAmount       float64 `json:"real_amount"`
	CompositionOfPay []struct {
		ChannelId   int64  `json:"channel_id"`
		ChannelCode string `json:"channel_code"`
		ChannelName string `json:"channel_name"`
		Payments    []struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentCode        string  `json:"payment_code"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
		} `json:"payments"`
		Discounts []struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountCode       string  `json:"discount_code"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
		} `json:"discounts"`
		RealAmountTotalPayments  float64 `json:"real_amount_total_payments"`
		RealAmountTotalDiscounts float64 `json:"real_amount_total_discounts"`
	} `json:"composition_of_pay"`
	CompositionOfDiscount []struct {
		ChannelId          int64   `json:"channel_id"`
		ChannelCode        string  `json:"channel_code"`
		ChannelName        string  `json:"channel_name"`
		Commission         float64 `json:"commission"`
		SendFeeForMerchant float64 `json:"send_fee_for_merchant"`
		OtherFee           float64 `json:"other_fee"`
		Payments           []struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentCode        string  `json:"payment_code"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
		} `json:"payments"`
		Discounts []struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountCode       string  `json:"discount_code"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
		} `json:"discounts"`
		DiscountContributeTotalPayments  float64 `json:"discount_contribute_total_payments"`
		DiscountContributeTotalDiscounts float64 `json:"discount_contribute_total_discounts"`
	} `json:"composition_of_discount"`
}
