package report

type ShopkeeperProductPeriodResponse struct {
    Rows          []*ShopkeeperProductPeriodRow  `json:"rows"`
    PeriodSummary []*ShopkeeperProductPeriodItem `json:"period_summary"`
    Summary       *ShopkeeperProductPeriodItem   `json:"summary"`
    TotalRows     int64                          `json:"total_rows"`
}

type ShopkeeperProductPeriodItem struct {
    Unit        string  `json:"unit"`
    Weight      float64 `json:"weight"`
    WeightCount string  `json:"weight_count"`
    GrossAmount float64 `json:"gross_amount"`
    NetAmount   float64 `json:"net_amount"`
    ItemCount   int64   `json:"item_count"`
}

type ShopkeeperProductPeriodRow struct {
    ProductId   int64  `json:"product_id"`
    ProductCode string `json:"product_code"`
    ProductName string `json:"product_name"`

    Periods    []*ShopkeeperProductPeriodItem `json:"periods"`
    RowSummary ShopkeeperProductPeriodItem    `json:"row_summary"`
}

type ShopkeeperProductPeriodRawRow struct {
    ProductId   int64   `json:"product_id"`
    Unit        string  `json:"unit"`
    Weight      float64 `json:"weight"`
    Hour        int64   `json:"hour"`
    GrossAmount float64 `json:"gross_amount"`
    NetAmount   float64 `json:"net_amount"`
    ItemCount   int64   `json:"item_count"`
}
