package report

import (
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
)

type ProductFlavorSalesResponse struct {
	Rows  []*ProductFlavorSales `json:"rows"` // 返回记录
	Graph []*ProductFlavorSales `json:"graph"`
	Total int64                 `json:"total"`
}

type ProductFlavorSales struct {
	BusDate              string           `json:"bus_date"`        // 营业日期
	GeoId                int64            `json:"geo_id"`          // 地理区域id
	GeoCode              string           `json:"geo_code"`        // 地理区域code
	GeoName              string           `json:"geo_name"`        // 地理区域name
	BranchId             int64            `json:"branch_id"`       // 管理区域id
	BranchCode           string           `json:"branch_code"`     // 管理区域code
	BranchName           string           `json:"branch_name"`     // 管理区域name
	CompanyId            int64            `json:"company_id"`      // 所属公司id
	CompanyCode          string           `json:"company_code"`    // 所属公司code
	CompanyName          string           `json:"company_name"`    // 所属公司name
	RegionId             int64            `json:"region_id"`       // 门店id
	RegionCode           string           `json:"region_code"`     // 门店code
	RegionName           string           `json:"region_name"`     // 门店name
	RegionAddress        string           `json:"region_address"`  // 门店所在城市
	RegionAlias          string           `json:"region_alias"`    // 门店别名
	StoreType            string           `json:"store_type"`      // 门店经营类型
	StoreTypeName        string           `json:"store_type_name"` // 门店经营类型name
	ProductId            int64            `json:"product_id"`
	ProductCode          string           `json:"product_code"`
	ProductName          string           `json:"product_name"`
	ProductCategoryId1   int64            `json:"product_category_id1"`
	ProductCategoryCode1 string           `json:"product_category_code1"`
	ProductCategoryName1 string           `json:"product_category_name1"`
	ProductCategoryId2   int64            `json:"product_category_id2"`
	ProductCategoryCode2 string           `json:"product_category_code2"`
	ProductCategoryName2 string           `json:"product_category_name2"`
	ProductCategoryId3   int64            `json:"product_category_id3"`
	ProductCategoryCode3 string           `json:"product_category_code3"`
	ProductCategoryName3 string           `json:"product_category_name3"`
	ProductCategoryId4   int64            `json:"product_category_id4"`
	ProductCategoryCode4 string           `json:"product_category_code4"`
	ProductCategoryName4 string           `json:"product_category_name4"`
	Flavor               string           `json:"flavor"` // sku属性和加料详情
	SkuRemark            string           `json:"sku_remark"`
	Accessories          string           `json:"accessories"`
	AccessoryId          int64            `json:"accessory_id"`
	AccessoryName        string           `json:"accessory_name"`
	FlavorDistribute     string           `json:"flavor_distribute"`     // 口味分布情况
	AttributeNameCode    string           `json:"attribute_name_code"`   // 属性名code
	AttributeNameName    string           `json:"attribute_name_name"`   // 属性名name
	AttributeValueCode   string           `json:"attribute_value_code"`  // 属性值code
	AttributeValueName   string           `json:"attribute_value_name"`  // 属性值name
	ProductCount         int64            `json:"product_count"`         // 商品销量（正单）
	ProductCountTotal    int64            `json:"product_count_total"`   // 商品总数
	PercentProductCount  float64          `json:"percent_product_count"` // 占比
	AttributeNames       []*AttributeName `json:"attribute_names"`
	Feeds                []*Accessory     `json:"feeds"`
}

type AttributeName struct {
	AttributeNameCode   string            `json:"attribute_name_code"`
	AttributeNameName   string            `json:"attribute_name_name"`
	ProductCount        int64             `json:"product_count"`
	PercentProductCount float64           `json:"percent_product_count"`
	AttributeNameValues []*AttributeValue `json:"attribute_name_values"` // 该属性名下的属性值
}

type AttributeValue struct { // 属性值
	AttributeValueCode  string  `json:"attribute_value_code"`
	AttributeValueName  string  `json:"attribute_value_name"`
	ProductCount        int64   `json:"product_count"`
	PercentProductCount float64 `json:"percent_product_count"`
}

type Accessory struct {
	AccessoryName       string  `json:"accessory_name"`
	ProductCount        int64   `json:"product_count"`
	PercentProductCount float64 `json:"percent_product_count"`
}

func (p *ProductFlavorSales) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"bus_date":               p.BusDate,
		"geo_id":                 p.GeoId,
		"geo_code":               p.GeoCode,
		"geo_name":               p.GeoName,
		"branch_id":              p.BranchId,
		"branch_code":            p.BranchCode,
		"branch_name":            p.BranchName,
		"company_id":             p.CompanyId,
		"company_code":           p.CompanyCode,
		"company_name":           p.CompanyName,
		"region_id":              p.RegionId,
		"region_code":            p.RegionCode,
		"region_name":            p.RegionName,
		"region_address":         p.RegionAddress,
		"region_alias":           p.RegionAlias,
		"store_type":             p.StoreType,
		"store_type_name":        p.StoreTypeName,
		"product_id":             p.ProductId,
		"product_code":           p.ProductCode,
		"product_name":           p.ProductName,
		"product_category_id1":   cast.ToString(p.ProductCategoryId1),
		"product_category_code1": p.ProductCategoryCode1,
		"product_category_name1": p.ProductCategoryName1,
		"product_category_id2":   cast.ToString(p.ProductCategoryId2),
		"product_category_code2": p.ProductCategoryCode2,
		"product_category_name2": p.ProductCategoryName2,
		"product_category_id3":   cast.ToString(p.ProductCategoryId3),
		"product_category_code3": p.ProductCategoryCode3,
		"product_category_name3": p.ProductCategoryName3,
		"product_category_id4":   cast.ToString(p.ProductCategoryId4),
		"product_category_code4": p.ProductCategoryCode4,
		"product_category_name4": p.ProductCategoryName4,
		"flavor":                 p.Flavor,
		"flavor_distribute":      p.FlavorDistribute,
		"sku_remark":             p.SkuRemark,
		"accessories":            p.Accessories,
		"accessory_id":           p.AccessoryId,
		"accessory_name":         p.AccessoryName,
		"attribute_name_code":    p.AttributeNameCode,
		"attribute_name_name":    p.AttributeNameName,
		"attribute_value_code":   p.AttributeValueCode,
		"attribute_value_name":   p.AttributeValueName,
		"product_count":          p.ProductCount,
		"product_count_total":    p.ProductCountTotal,
		"percent_product_count":  cast.ToString(report_v2.SaveTwoEffectiveDigits(p.PercentProductCount*100)) + "%",
	}
}
