package report

type ProductStoreRankResponse struct {
	Rows []*ProductStoreRank `json:"rows"`
}

type ProductStoreRank struct {
	StoreId            int64   `json:"store_id"`
	StoreCode          string  `json:"store_code"`
	StoreName          string  `json:"store_name"`
	GrossAmount        float64 `json:"gross_amount"`
	GrossAmountPercent float64 `json:"gross_amount_percent"`
	NetAmount          float64 `json:"net_amount"`
	NetAmountPercent   float64 `json:"net_amount_percent"`
	ItemCount          int64   `json:"item_count"`
	ItemCountPercent   float64 `json:"item_count_percent"`
	WeightCount        float64 `json:"weight_count"`
	WeightCountPercent float64 `json:"weight_count_percent"`
	Unit               string  `json:"unit"`
}
