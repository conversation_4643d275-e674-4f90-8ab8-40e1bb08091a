package report

type ProductTopNRankResponse struct {
	Rows    []*ProductTopNRank `json:"rows"`
	Summary *ProductTopNRank   `json:"summary"`
}

type ProductTopNRank struct {
	ProductId          int64   `json:"product_id"`
	ProductCode        string  `json:"product_code"`
	ProductName        string  `json:"product_name"`
	GrossAmount        float64 `json:"gross_amount"`
	GrossAmountPercent float64 `json:"gross_amount_percent"`
	NetAmount          float64 `json:"net_amount"`
	NetAmountPercent   float64 `json:"net_amount_percent"`
	ItemCount          int64   `json:"item_count"`
	ItemCountPercent   float64 `json:"item_count_percent"`
	WeightCount        float64 `json:"weight_count"`
	Unit               string  `json:"unit"`
}
