package report

type StoreMakeTimeResponse struct {
    Rows  []*StoreMakeTimeItem `json:"rows"`
    Total int64                `json:"total"`
}

type StoreMakeTimeItem struct {
    RegionId      int64  `json:"region_id"`
    RegionCode    string `json:"region_code"`
    RegionName    string `json:"region_name"`
    RegionAddress string `json:"region_address"`
    RegionAlias   string `json:"region_alias"`
    StoreType     int64  `json:"store_type"`
    StoreTypeName string `json:"store_type_name"`
    MakeHour      int32  `json:"make_hour"`
    // 时段
    TimePeriod string `json:"time_period"`
    // 订单数
    OrderCount int32 `json:"order_count"`
    // 平均每杯制作时间(min)
    MakeTimeAvgPerOrder string `json:"make_time_avg_per_order"`
    // 杯数
    CupCount int32 `json:"cup_count"`
    // 平均每杯制作时间(min)
    MakeTimeAvgPerCup string `json:"make_time_avg_per_cup"`
}

func (d *StoreMakeTimeItem) ToMap() map[string]interface{} {
    return map[string]interface{}{
        "region_id":               d.RegionId,
        "region_code":             d.RegionCode,
        "region_name":             d.RegionName,
        "region_address":          d.RegionAddress,
        "region_alias":            d.RegionAlias,
        "store_type":              d.StoreType,
        "store_type_name":         d.StoreTypeName,
        "make_hour":               d.MakeHour,
        "time_period":             d.TimePeriod,
        "order_count":             d.OrderCount,
        "make_time_avg_per_order": d.MakeTimeAvgPerOrder,
        "cup_count":               d.CupCount,
        "make_time_avg_per_cup":   d.MakeTimeAvgPerCup,
    }
}
