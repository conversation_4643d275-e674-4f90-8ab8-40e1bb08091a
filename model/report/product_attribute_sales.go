package report

type ProductAttributeSalesResponse struct {
	Rows  []*ProductAttributeSalesRow `json:"rows"`  // 返回记录
	Total int64                       `json:"total"` // 行数
}

type ProductAttributeSalesRow struct {
	// 属性编码
	AttributeCode string `json:"attribute_code"`
	// 属性名称
	AttributeName string `json:"attribute_name"`
	// 属性组编码
	AttributeGroupCode string `json:"attribute_value_code"`
	// 属性组名称
	AttributeGroupName string `json:"attribute_group_name"`
	// 门店ID
	StoreId int64 `json:"store_id"`
	// 门店名称
	StoreName string `json:"store_name"`
	// 门店编码
	StoreCode string `json:"store_code"`
	// 营业日期
	BusDate string `json:"bus_date"`
	// 销量
	Qty float64 `json:"qty"`
	// 销量占比
	QtyRatio float64 `gorm:"type:float" json:"qty_ratio"`
	// Total
	Total int64 `json:"total"`
}
