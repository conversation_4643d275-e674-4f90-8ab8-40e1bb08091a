package report

import "fmt"

type ProductSalesSummaryResponse struct {
	Rows    []*ProductSalesSummary `json:"rows,omitempty"`
	Summary *ProductSalesSummary   `json:"summary,omitempty"`
	Total   int64                  `json:"total,omitempty"`
}

type ProductSalesSummary struct {
	Total             int64   `json:"total,omitempty"`
	ProductId         int64   `json:"product_id,omitempty"`          // 商品id
	ProductCode       string  `json:"product_code,omitempty"`        // 商品code
	ProductName       string  `json:"product_name,omitempty"`        // 商品名称
	CategoryId        int64   `json:"category_id,omitempty"`         // 商品分类id
	CategoryCode      string  `json:"category_code,omitempty"`       // 分类code
	CategoryName      string  `json:"category_name,omitempty"`       // 分类名称
	ProductCount      float64 `json:"product_count,omitempty"`       // 商品数量
	NetAmount         float64 `json:"net_amount,omitempty"`          // 商品实收
	PercentageOfSales float64 `json:"percentage_of_sales,omitempty"` // 商品收入占比
}

func (p *ProductSalesSummary) RowToMap() map[string]interface{} {
	return map[string]interface{}{
		"product_code":        p.ProductCode,
		"product_name":        p.ProductName,
		"category_code":       p.CategoryCode,
		"category_name":       p.CategoryName,
		"product_count":       p.ProductCount,
		"net_amount":          p.NetAmount,
		"percentage_of_sales": fmt.Sprintf("%.2f%%", p.PercentageOfSales*100),
	}
}
