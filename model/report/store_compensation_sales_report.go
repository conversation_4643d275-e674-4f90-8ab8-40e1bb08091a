package report

import "time"

// // 营业日期
// string bus_date = 1;
// // 门店ID
// uint64 store_id = 2;
// // 门店code
// string store_code = 3;
// // 门店名称
// string store_name = 4;
// // 账单号（取餐号码）
// string ticket_no = 5;
// // 结账时间
// string  end_time = 6;
// // 类别编码(支付方式/促销方式ID)
// uint64  compensation_category_id = 7;
// // 类别编码(支付方式/促销方式code)
// string compensation_category_code = 8;
// // 类别描述(支付方式/促销方式名称)
// string compensation_category_desc = 9;
// // 账单金额
// string  net_amount = 10;
// // 非销售金额
// string  non_sales_amount = 11;
// // 如果是汇总层，这里不给数据；如果是查询明细层，这里才有数据
// repeated StoreCompensationSalesProductDetail product_details = 12;
type StoreCompensationSalesReport struct {
	// 营业日
	BusDate string `json:"bus_date"`
	// 门店ID
	StoreId uint64 `json:"store_id"`
	// 门店code
	StoreCode string `json:"store_code"`
	// 门店名称
	StoreName string `json:"store_name"`
	// 账单号（取餐号码）
	TicketNo string `json:"ticket_no"`
	// 结账时间
	EndTime time.Time `json:"end_time"`
	// 类别编码(支付方式/促销方式ID)
	CompensationCategoryId uint64 `json:"compensation_category_id"`
	// 类别编码(支付方式/促销方式code)
	CompensationCategoryCode string `json:"compensation_category_code"`
	// 类别描述(支付方式/促销方式名称)
	CompensationCategoryDesc string `json:"compensation_category_desc"`
	// 账单金额
	NetAmount float64 `json:"net_amount"`
	// 非销售金额
	NonSalesAmount float64 `json:"non_sales_amount"`
	// 商品明细
	ProductDetails []*StoreCompensationSalesProduct `json:"product_details"`
}

type StoreCompensationSalesProduct struct {
	// 序号
	Seq uint64 `json:"seq"`
	// 序号
	ParentId uint64 `json:"parent_id"`
	// 商品ID
	ProductID uint64 `json:"product_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品编码
	ProductCode string `json:"product_code"`
	// 商品数量
	Qty float64 `json:"qty"`
	// 商品金额
	NetAmount float64 `json:"net_amount"`
	// 非销售金额
	NonSalesAmount float64 `json:"non_sales_amount"`
}

type StoreCompensationSalesItemReport struct {
	// 营业日
	BusDate string `json:"bus_date"`
	// 门店ID
	StoreId uint64 `json:"store_id"`
	// 门店code
	StoreCode string `json:"store_code"`
	// 门店名称
	StoreName string `json:"store_name"`
	// 账单号（取餐号码）
	TicketNo string `json:"ticket_no"`
	// 结账时间
	EndTime time.Time `json:"end_time"`
	// 类别编码(支付方式/促销方式ID)
	CompensationCategoryId uint64 `json:"compensation_category_id"`
	// 类别编码(支付方式/促销方式code)
	CompensationCategoryCode string `json:"compensation_category_code"`
	// 类别描述(支付方式/促销方式名称)
	CompensationCategoryDesc string `json:"compensation_category_desc"`

	// main序号
	MainSeq uint64 `json:"main_seq"`
	// 序号
	Seq uint64 `json:"seq"`
	// 序号
	ParentId uint64 `json:"parent_id"`
	// 商品ID
	ProductID uint64 `json:"product_id"`
	// 商品名称
	ProductName string `json:"product_name"`
	// 商品编码
	ProductCode string `json:"product_code"`
	// 商品数量
	Qty float64 `json:"qty"`
	// 商品金额（订单级别）
	NetAmount float64 `json:"net_amount"`
	// 非销售金额（订单级别）
	NonSalesAmount float64 `json:"non_sales_amount"`
	// item金额（item级别）
	ItemAmount float64 `json:"item_amount"`
}

type StoreCompensationSalesReportResp struct {
	SummaryRows  []*StoreCompensationSalesReport     `json:"summary_rows"`
	SummaryTotal int64                               `json:"summary_total"`
	Rows         []*StoreCompensationSalesItemReport `json:"rows"`
	Total        int64                               `json:"total"`
}
