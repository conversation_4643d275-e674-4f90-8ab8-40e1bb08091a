package report

type PaymentStatisticsResponse struct {
	Rows    []*PaymentStatistics `json:"rows"`
	Summary *PaymentStatistics   `json:"summary"`
	Total   int64                `json:"total"`
}

type PaymentStatistics struct {
	BusDate                    string  `json:"bus_date"`
	BranchId                   int64   `json:"branch_id"`
	BranchName                 string  `json:"branch_name"`
	BranchCode                 string  `json:"branch_code"`
	RegionId                   int64   `json:"region_id"`
	RegionCode                 string  `json:"region_code"`
	RegionName                 string  `json:"region_name"`
	ChannelId                  int64   `json:"channel_id"`
	ChannelName                string  `json:"channel_name"`
	ChannelCode                string  `json:"channel_code"`
	CooperationCode            string  `json:"cooperation_code"`      //支付供应商编码
	PaymentCategoryId          int64   `json:"payment_category_id"`   //支付类型 ID
	PaymentCategoryName        string  `json:"payment_category_name"` //支付类型名称
	PaymentCategoryCode        string  `json:"payment_category_code"` //支付类型编码
	PaymentId                  int64   `json:"payment_id"`
	PaymentCode                string  `json:"payment_code"`
	PaymentName                string  `json:"payment_name"`
	PayAmount                  float64 `json:"pay_amount"`
	TransferRealAmount         float64 `json:"transfer_real_amount"`
	ItemCount                  int64   `json:"item_count"`
	PayAmountReturned          float64 `json:"pay_amount_returned"`
	TransferRealAmountReturned float64 `json:"transfer_real_amount_returned"`
	ItemCountReturned          int64   `json:"item_count_returned"`
	DepositAmount              float64 `json:"deposit_amount"`
	DepositItemCount           int64   `json:"deposit_item_count"`
	AllItemCount               int64   `json:"all_item_count"`
	Total                      int64   `json:"total"`
}
