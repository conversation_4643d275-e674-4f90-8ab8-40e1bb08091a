package report

// 优惠报表请求
type PosPromotionRequest struct {
	// 开始营业日，格式2024-01-01
	Start string `json:"start"`
	// 结束营业日，格式2024-01-01
	End string `json:"end"`
}

// 优惠报表行
type PosPromotionRow struct {
	PromotionName     string  `json:"promotion_name"`     // 优惠名称
	PromotionCateName string  `json:"promotion_cate_name"`// 优惠类型名称
	Discount          float64 `json:"discount"`           // 优惠金额
	DiscountPercent   float64 `json:"discount_percent"`   // 优惠金额占比
	UsageCount        float64 `json:"usage_count"`        // 使用次数
}

// 优惠报表响应
type PosPromotionResponse struct {
	Rows    []*PosPromotionRow `json:"rows"`    // 返回记录
	Summary *PosPromotionRow   `json:"summary"` // 汇总
}
