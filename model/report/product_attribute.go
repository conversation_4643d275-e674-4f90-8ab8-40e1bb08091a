package report

type ProductAttributeResponse struct {
	Rows   []*ProductAttribute `json:"rows"`
	Graphs []*ProductAttribute `json:"graphs"`
}

type ProductAttribute struct {
	ProductId           int64   `json:"product_id"`
	ProductCode         string  `json:"product_code"`
	ProductName         string  `json:"product_name"`
	ProductCount        int64   `json:"product_count"`
	ProductCountTotal   int64   `json:"product_count_total"`
	PercentProductCount float64 `json:"percent_product_count"`
	Flavor              string  `json:"flavor"`

	SkuRemark   map[string]string `json:"sku_remark"`
	Accessories map[string]int64  `json:"accessories"`

	AttributeNames []*Attribute `json:"attribute_names"`
	Feeds          []*Feed      `json:"feeds"`
}

// 属性名
type Attribute struct {
	AttributeName       string                `json:"attribute_name"`        // 属性名称
	AttributeNameValues []*AttributeNameValue `json:"attribute_name_values"` // 该属性下的属性值
}

// 属性值
type AttributeNameValue struct {
	AttributeValueName  string  `json:"attribute_value_name"`  // 属性值名称
	ProductCount        int64   `json:"product_count"`         // 商品数量
	ProductCountPercent float64 `json:"product_count_percent"` // 商品数量占比
}

type Feed struct {
	FeedName         string  `json:"feed_name"`
	FeedCount        int64   `json:"feed_count"`
	FeedCountPercent float64 `json:"feed_count_percent"`
}

// 加料实现sort排序
type Feeds []*Feed

func (a Feeds) Len() int           { return len(a) }
func (a Feeds) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a Feeds) Less(i, j int) bool { return a[i].FeedCount > a[j].FeedCount }

// 属性值实现sort排序
type AttributeNameValues []*AttributeNameValue

func (a AttributeNameValues) Len() int           { return len(a) }
func (a AttributeNameValues) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a AttributeNameValues) Less(i, j int) bool { return a[i].ProductCount > a[j].ProductCount }
