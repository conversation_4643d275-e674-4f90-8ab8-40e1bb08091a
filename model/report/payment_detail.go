package report

type PaymentDetailResponse struct {
	Rows  []*PaymentDetail `json:"rows"`
	Total int64            `json:"total"`
}

// string bus_date = 1;
// // 门店ID
// uint64 store_id = 2;
// // 门店code
// string store_code = 3;
// // 门店名称
// string store_name = 4;
// // 支付方式ID
// int64 payment_id = 5;
// // 支付方式编码
// string payment_code = 6;
// // 支付方式名称
// string payment_name = 7;
// // 支付时间
//
//	// 支付时间
//	string pay_time = 8;
//
// // 实付金额
// string  pay_amount =9;
// // 支付转折扣
// string  payment_transfer_amount =10;
// // 服务费
// string  sur_charge_amount =11;
// // 卡号
// string  card_no =12;
// 外送号码（飞盘号）
// string  plate_no =13;
// 帐单号码
// string  phone_no =13;
type PaymentDetail struct {
	BusDate               string  `json:"bus_date"`
	StoreId               uint64  `json:"store_id"`
	StoreCode             string  `json:"store_code"`
	StoreName             string  `json:"store_name"`
	TicketNo              string  `json:"ticket_no"`
	PaymentId             int64   `json:"payment_id"`
	PaymentCode           string  `json:"payment_code"`
	PaymentName           string  `json:"payment_name"`
	PayTime               string  `json:"pay_time"`
	PayAmount             float64 `json:"pay_amount"`
	PaymentTransferAmount float64 `json:"payment_transfer_amount"`
	SurChargeAmount       float64 `json:"sur_charge_amount"`
	CardNo                string  `json:"card_no"`
	//real_amount
	RealAmount     float64 `json:"real_amount"`
	PlateNo        string  `json:"plate_no,omitempty"` // 飞盘号（外送号码）
	PhoneNo        string  `json:"phone_no"`
	OperatorCode   string  `json:"operator_code"` //操作人编号
	ServiceFee     float64 `json:"service_fee"`   // 服务费
	NonSalesAmount float64 `json:"non_sales_amount"`
}
