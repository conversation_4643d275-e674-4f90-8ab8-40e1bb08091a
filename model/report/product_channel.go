package report

type ProductChannelResponse struct {
	Summary *ProductChannel   `json:"summary"` // 汇总
	Rows    []*ProductChannel `json:"rows"`    // 返回记录
	Total   int64             `json:"total"`   // 行数
}

type ProductChannel struct {
	BusDate                string  `json:"bus_date"`
	RegionId               int64   `json:"region_id"`
	RegionCode             string  `json:"region_code"`
	RegionName             string  `json:"region_name"`
	RegionAddress          string  `json:"region_address"`
	RegionAlias            string  `json:"region_alias"`
	StoreType              string  `json:"store_type"`
	StoreTypeName          string  `json:"store_type_name"`
	BusinessDays           int64   `json:"business_days"`
	ProductAveragePrice    float64 `json:"product_average_price"`
	GrossAmount            float64 `json:"gross_amount"`
	GrossAmountReturned    float64 `json:"gross_amount_returned"`
	NetAmount              float64 `json:"net_amount"`
	NetAmountReturned      float64 `json:"net_amount_returned"`
	DiscountAmount         float64 `json:"discount_amount"`
	DiscountAmountReturned float64 `json:"discount_amount_returned"`
	ItemCount              int64   `json:"item_count"`
	ItemCountReturned      int64   `json:"item_count_returned"`
	Child                  []struct {
		ProductId              int64   `json:"product_id"`
		ProductCode            string  `json:"product_code"`
		ProductName            string  `json:"product_name"`
		ProductCategoryId      int64   `json:"product_category_id"`
		ProductCategoryCode    string  `json:"product_category_code"`
		ProductCategoryName    string  `json:"product_category_name"`
		ProductAveragePrice    float64 `json:"product_average_price"`
		Weight                 float64 `json:"weight"`       // 重量/份量
		WeightCount            string  `json:"weight_count"` // 重量/份量带单位
		WeightReturned         float64 `json:"weight_returned"`
		RefundWeightCount      string  `json:"refund_weight_count"`
		Unit                   string  `json:"unit"` // 单位
		GrossAmount            float64 `json:"gross_amount"`
		GrossAmountReturned    float64 `json:"gross_amount_returned"`
		NetAmount              float64 `json:"net_amount"`
		NetAmountReturned      float64 `json:"net_amount_returned"`
		DiscountAmount         float64 `json:"discount_amount"`
		DiscountAmountReturned float64 `json:"discount_amount_returned"`
		ItemCount              int64   `json:"item_count"`
		ItemCountReturned      int64   `json:"item_count_returned"`
		TaxFee                 float64 `json:"tax_fee"`
		Child                  []struct {
			ChannelId              int64   `json:"channel_id"`
			ChannelCode            string  `json:"channel_code"`
			ChannelName            string  `json:"channel_name"`
			Weight                 float64 `json:"weight"`       // 重量/份量
			WeightCount            string  `json:"weight_count"` // 重量/份量带单位
			WeightReturned         float64 `json:"weight_returned"`
			RefundWeightCount      string  `json:"refund_weight_count"`
			Unit                   string  `json:"unit"` // 单位
			ProductAveragePrice    float64 `json:"product_average_price"`
			GrossAmount            float64 `json:"gross_amount"`
			GrossAmountReturned    float64 `json:"gross_amount_returned"`
			NetAmount              float64 `json:"net_amount"`
			NetAmountReturned      float64 `json:"net_amount_returned"`
			DiscountAmount         float64 `json:"discount_amount"`
			DiscountAmountReturned float64 `json:"discount_amount_returned"`
			ItemCount              int64   `json:"item_count"`
			ItemCountReturned      int64   `json:"item_count_returned"`
			TaxFee                 float64 `json:"tax_fee"`
			Child                  []struct {
				OrderType              string  `json:"order_type"`
				OrderTypeName          string  `json:"order_type_name"`
				Weight                 float64 `json:"weight"`       // 重量/份量
				WeightCount            string  `json:"weight_count"` // 重量/份量带单位
				WeightReturned         float64 `json:"weight_returned"`
				RefundWeightCount      string  `json:"refund_weight_count"`
				Unit                   string  `json:"unit"` // 单位
				ProductAveragePrice    float64 `json:"product_average_price"`
				GrossAmount            float64 `json:"gross_amount"`
				GrossAmountReturned    float64 `json:"gross_amount_returned"`
				NetAmount              float64 `json:"net_amount"`
				NetAmountReturned      float64 `json:"net_amount_returned"`
				DiscountAmount         float64 `json:"discount_amount"`
				DiscountAmountReturned float64 `json:"discount_amount_returned"`
				ItemCount              int64   `json:"item_count"`
				ItemCountReturned      int64   `json:"item_count_returned"`
			}
		}
	}
	TaxFee float64 `json:"tax_fee"`
	Total  int64   `json:"total"`
}
