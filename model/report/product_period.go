package report

type ProductPeriodResponse struct {
	Rows    []*ProductPeriod `json:"rows"`
	Summary *ProductPeriod   `json:"summary"`
	Total   int64            `json:"total"`
}

type HourInfo struct {
	Weight      float64 `json:"weight"`
	WeightCount string  `json:"weight_count"`
	GrossAmount float64 `json:"gross_amount"`
	NetAmount   float64 `json:"net_amount"`
	ItemCount   int64   `json:"item_count"`
	TaxFee      float64 `json:"tax_fee"`
}

type ProductPeriod struct {
	BusDate       string  `json:"bus_date"`
	RegionId      int64   `json:"region_id"`
	RegionCode    string  `json:"region_code"`
	RegionName    string  `json:"region_name"`
	RegionAddress string  `json:"region_address"`
	RegionAlias   string  `json:"region_alias"`
	GrossAmount   float64 `json:"gross_amount"`
	NetAmount     float64 `json:"net_amount"`
	ItemCount     int64   `json:"item_count"`
	StoreType     string  `json:"store_type"`
	StoreTypeName string  `json:"store_type_name"`
	BusinessDays  int64   `json:"business_days"`
	Data          struct {
		H00 HourInfo `json:"h00"`
		H01 HourInfo `json:"h01"`
		H02 HourInfo `json:"h02"`
		H03 HourInfo `json:"h03"`
		H04 HourInfo `json:"h04"`
		H05 HourInfo `json:"h05"`
		H06 HourInfo `json:"h06"`
		H07 HourInfo `json:"h07"`
		H08 HourInfo `json:"h08"`
		H09 HourInfo `json:"h09"`
		H10 HourInfo `json:"h10"`
		H11 HourInfo `json:"h11"`
		H12 HourInfo `json:"h12"`
		H13 HourInfo `json:"h13"`
		H14 HourInfo `json:"h14"`
		H15 HourInfo `json:"h15"`
		H16 HourInfo `json:"h16"`
		H17 HourInfo `json:"h17"`
		H18 HourInfo `json:"h18"`
		H19 HourInfo `json:"h19"`
		H20 HourInfo `json:"h20"`
		H21 HourInfo `json:"h21"`
		H22 HourInfo `json:"h22"`
		H23 HourInfo `json:"h23"`
	} `json:"data"`

	Child []struct {
		ProductId           int64    `json:"product_id"`
		ProductCode         string   `json:"product_code"`
		ProductName         string   `json:"product_name"`
		ProductCategoryId   int64    `json:"product_category_id"`
		ProductCategoryCode string   `json:"product_category_code"`
		ProductCategoryName string   `json:"product_category_name"`
		H00                 HourInfo `json:"h00"`
		H01                 HourInfo `json:"h01"`
		H02                 HourInfo `json:"h02"`
		H03                 HourInfo `json:"h03"`
		H04                 HourInfo `json:"h04"`
		H05                 HourInfo `json:"h05"`
		H06                 HourInfo `json:"h06"`
		H07                 HourInfo `json:"h07"`
		H08                 HourInfo `json:"h08"`
		H09                 HourInfo `json:"h09"`
		H10                 HourInfo `json:"h10"`
		H11                 HourInfo `json:"h11"`
		H12                 HourInfo `json:"h12"`
		H13                 HourInfo `json:"h13"`
		H14                 HourInfo `json:"h14"`
		H15                 HourInfo `json:"h15"`
		H16                 HourInfo `json:"h16"`
		H17                 HourInfo `json:"h17"`
		H18                 HourInfo `json:"h18"`
		H19                 HourInfo `json:"h19"`
		H20                 HourInfo `json:"h20"`
		H21                 HourInfo `json:"h21"`
		H22                 HourInfo `json:"h22"`
		H23                 HourInfo `json:"h23"`
	} `json:"child"`
	TaxFee float64 `json:"tax_fee"`
	Total  int64   `json:"total"`
}
