package report

import (
	"fmt"
	"github.com/spf13/cast"
)

type ProductSalesResponse struct {
	Summary *ProductSales   `json:"summary"` // 汇总
	Rows    []*ProductSales `json:"rows"`    // 返回记录
	Total   int64           `json:"total"`   // 行数
}

type ProductSales struct {
	BusDate                string  `json:"bus_date"` //
	RegionId               int64   `json:"region_id"`
	RegionCode             string  `json:"region_code"`
	RegionName             string  `json:"region_name"`
	RegionAddress          string  `json:"region_address"`
	RegionAlias            string  `json:"region_alias"`
	StoreType              string  `json:"store_type"` // 门店经营类型
	StoreTypeName          string  `json:"store_type_name"`
	BusinessDays           int64   `json:"business_days"`
	GrossAmount            float64 `json:"gross_amount"`
	GrossAmountReturned    float64 `json:"gross_amount_returned"`
	NetAmount              float64 `json:"net_amount"`
	NetAmountReturned      float64 `json:"net_amount_returned"`
	DiscountAmount         float64 `json:"discount_amount"`
	DiscountAmountReturned float64 `json:"discount_amount_returned"`
	ItemCount              int64   `json:"item_count"`
	ItemCountReturned      int64   `json:"item_count_returned"`
	Data                   []struct {
		Category0              int64   `json:"category0"`
		Category0Code          string  `json:"category0_code"`
		Category0Name          string  `json:"category0_name"`
		GrossAmount            float64 `json:"gross_amount"`
		GrossAmountReturned    float64 `json:"gross_amount_returned"`
		NetAmount              float64 `json:"net_amount"`
		NetAmountReturned      float64 `json:"net_amount_returned"`
		DiscountAmount         float64 `json:"discount_amount"`
		DiscountAmountReturned float64 `json:"discount_amount_returned"`
		ItemCount              int64   `json:"item_count"`
		ItemCountReturned      int64   `json:"item_count_returned"`
		Data                   []struct {
			Category1              int64   `json:"category1"`
			Category1Code          string  `json:"category1_code"`
			Category1Name          string  `json:"category1_name"`
			GrossAmount            float64 `json:"gross_amount"`
			GrossAmountReturned    float64 `json:"gross_amount_returned"`
			NetAmount              float64 `json:"net_amount"`
			NetAmountReturned      float64 `json:"net_amount_returned"`
			DiscountAmount         float64 `json:"discount_amount"`
			DiscountAmountReturned float64 `json:"discount_amount_returned"`
			ItemCount              int64   `json:"item_count"`
			ItemCountReturned      int64   `json:"item_count_returned"`
			TaxFee                 float64 `json:"tax_fee"`
			Data                   []struct {
				Category2              int64   `json:"category2"`
				Category2Code          string  `json:"category2_code"`
				Category2Name          string  `json:"category2_name"`
				GrossAmount            float64 `json:"gross_amount"`
				GrossAmountReturned    float64 `json:"gross_amount_returned"`
				NetAmount              float64 `json:"net_amount"`
				NetAmountReturned      float64 `json:"net_amount_returned"`
				DiscountAmount         float64 `json:"discount_amount"`
				DiscountAmountReturned float64 `json:"discount_amount_returned"`
				ItemCount              int64   `json:"item_count"`
				ItemCountReturned      int64   `json:"item_count_returned"`
				TaxFee                 float64 `json:"tax_fee"`
				Data                   []struct {
					ProductId              int64   `json:"product_id"`
					ProductCode            string  `json:"product_code"`
					ProductName            string  `json:"product_name"`
					ProductSaleName        string  `json:"product_sale_name"` // 销售规格
					Weight                 float64 `json:"weight"`            // 重量/份量
					WeightCount            string  `json:"weight_count"`      // 重量/份量带单位
					WeightReturned         float64 `json:"weight_returned"`
					RefundWeightCount      string  `json:"refund_weight_count"`
					Unit                   string  `json:"unit"` // 单位
					GrossAmount            float64 `json:"gross_amount"`
					GrossAmountReturned    float64 `json:"gross_amount_returned"`
					NetAmount              float64 `json:"net_amount"`
					NetAmountReturned      float64 `json:"net_amount_returned"`
					DiscountAmount         float64 `json:"discount_amount"`
					DiscountAmountReturned float64 `json:"discount_amount_returned"`
					ItemCount              int64   `json:"item_count"`
					ItemCountReturned      int64   `json:"item_count_returned"`
					TaxFee                 float64 `json:"tax_fee"`
				}
			}
		}
	}
	TaxFee float64 `json:"tax_fee"`
	Total  int64   `json:"total,omitempty"`
}

func (p ProductSales) ToMap() map[string]string {
	return map[string]string{
		"bus_date":                 p.BusDate,
		"region_id":                cast.ToString(p.RegionId),
		"region_code":              p.RegionCode,
		"region_name":              p.RegionName,
		"region_address":           p.RegionAddress,
		"region_alias":             p.RegionAlias,
		"gross_amount":             fmt.Sprintf("%.2f", p.GrossAmount),
		"gross_amount_returned":    fmt.Sprintf("%.2f", p.GrossAmountReturned),
		"net_amount":               fmt.Sprintf("%.2f", p.NetAmount),
		"net_amount_returned":      fmt.Sprintf("%.2f", p.NetAmountReturned),
		"discount_amount":          fmt.Sprintf("%.2f", p.DiscountAmount),
		"discount_amount_returned": fmt.Sprintf("%.2f", p.DiscountAmountReturned),
		"item_count":               cast.ToString(p.ItemCount),
		"item_count_returned":      cast.ToString(p.ItemCountReturned),
		"total":                    cast.ToString(p.Total),
		"tax_fee":                  fmt.Sprintf("%.2f", p.TaxFee),
	}
}
