package report

type PaymentPeriodResponse struct {
	Rows    []*PaymentPeriod `json:"rows"`
	Summary *PaymentPeriod   `json:"summary"`
	Total   int64            `json:"total"`
}

type PaymentPeriod struct {
	BusDate               string  `json:"bus_date"`
	RegionId              int64   `json:"region_id"`
	RegionCode            string  `json:"region_code"`
	RegionName            string  `json:"region_name"`
	RegionAddress         string  `json:"region_address"`
	RegionAlias           string  `json:"region_alias"`
	StoreType             string  `json:"store_type"`
	StoreTypeName         string  `json:"store_type_name"`
	BusinessDays          int64   `json:"business_days"`
	Receivable            float64 `json:"receivable"`
	PayAmount             float64 `json:"pay_amount"`
	TransferRealAmount    float64 `json:"transfer_real_amount"`
	PaymentTransferAmount float64 `json:"payment_transfer_amount"`
	Cost                  float64 `json:"cost"`
	TpAllowance           float64 `json:"tp_allowance"`
	Rounding              float64 `json:"rounding"`
	OverflowAmount        float64 `json:"overflow_amount"`
	ChangeAmount          float64 `json:"change_amount"`
	ItemCount             int64   `json:"item_count"`
	ItemCountReturned     int64   `json:"item_count_returned"`
	Data                  struct {
		H00 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h00"`
		H01 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h01"`
		H02 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h02"`
		H03 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h03"`
		H04 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h04"`
		H05 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h05"`
		H06 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h06"`
		H07 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h07"`
		H08 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h08"`
		H09 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h09"`
		H10 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h10"`
		H11 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h11"`
		H12 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h12"`
		H13 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h13"`
		H14 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h14"`
		H15 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h15"`
		H16 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h16"`
		H17 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h17"`
		H18 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h18"`
		H19 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h19"`
		H20 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h20"`
		H21 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h21"`
		H22 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h22"`
		H23 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h23"`
	} `json:"data"`
	Child []struct {
		PaymentId   int64  `json:"payment_id"`
		PaymentCode string `json:"payment_code"`
		PaymentName string `json:"payment_name"`
		PaymentType string `json:"payment_type"`

		H00 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h00"`
		H01 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h01"`
		H02 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h02"`
		H03 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h03"`
		H04 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h04"`
		H05 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h05"`
		H06 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h06"`
		H07 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h07"`
		H08 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h08"`
		H09 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h09"`
		H10 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h10"`
		H11 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h11"`
		H12 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h12"`
		H13 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h13"`
		H14 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h14"`
		H15 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h15"`
		H16 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h16"`
		H17 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h17"`
		H18 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h18"`
		H19 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h19"`
		H20 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h20"`
		H21 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h21"`
		H22 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h22"`
		H23 struct {
			Receivable            float64 `json:"receivable"`
			PayAmount             float64 `json:"pay_amount"`
			TransferRealAmount    float64 `json:"transfer_real_amount"`
			PaymentTransferAmount float64 `json:"payment_transfer_amount"`
			Cost                  float64 `json:"cost"`
			TpAllowance           float64 `json:"tp_allowance"`
			Rounding              float64 `json:"rounding"`
			OverflowAmount        float64 `json:"overflow_amount"`
			ChangeAmount          float64 `json:"change_amount"`
			ItemCount             int64   `json:"item_count"`
			ItemCountReturned     int64   `json:"item_count_returned"`
		} `json:"h23"`
	} `json:"child"`
	Total int64 `json:"total"`
}
