package report

type PeriodNewResponse struct {
	Rows []*PeriodNewRow `json:"rows"`
}

type PeriodNewRow struct {
	PeriodName        string  `json:"period_name"`
	PeriodTime        string  `json:"period_time"`
	RealAmount        float64 `json:"real_amount"`
	FinanceRealAmount float64 `json:"finance_real_amount"`
	BusinessAmount    float64 `json:"business_amount"`
	ValidTicketCount  int64   `json:"valid_ticket_count"`
}
