package report

import "encoding/json"

type DiscountSalesResponse struct {
	Rows    []*DiscountSales
	Summary *DiscountSales
	Total   int64
}

type DiscountSales struct {
	BusDate            string  `json:"bus_date"`
	GeoId              int64   `json:"geo_id"`
	GeoCode            string  `json:"geo_code"`
	GeoName            string  `json:"geo_name"`
	BranchId           int64   `json:"branch_id"`
	BranchCode         string  `json:"branch_code"`
	BranchName         string  `json:"branch_name"`
	CompanyId          int64   `json:"company_id"`
	CompanyCode        string  `json:"company_code"`
	CompanyName        string  `json:"company_name"`
	RegionId           int64   `json:"region_id"`
	RegionCode         string  `json:"region_code"`
	RegionName         string  `json:"region_name"`
	RegionAddress      string  `json:"region_address"`
	RegionAlias        string  `json:"region_alias"`
	StoreType          string  `json:"store_type"`
	StoreTypeName      string  `json:"store_type_name"`
	BusinessDays       int64   `json:"business_days"`
	DiscountId         int64   `json:"discount_id"`
	DiscountCode       string  `json:"discount_code"`
	DiscountName       string  `json:"discount_name"`
	DiscountCount      int64   `json:"discount_count"`
	DiscountType       string  `json:"discount_type"`
	ChannelId          int64   `json:"channel_id"`
	ChannelCode        string  `json:"channel_code"`
	ChannelName        string  `json:"channel_name"`
	GrossAmount        float64 `json:"gross_amount"`
	ProductCount       int     `json:"product_count"`
	DiscountAmount     float64 `json:"discount_amount"`
	DiscountContribute float64 `json:"discount_contribute"`
	TransferRealAmount string  `json:"transfer_real_amount"`
	Total              int64   `json:"total"`
}

func (d *DiscountSales) ToMap() map[string]interface{} {
	results := make(map[string]interface{}, 0)
	bt, _ := json.Marshal(d)
	_ = json.Unmarshal(bt, &results)
	return results
}
