package report

type CustomerFormResponse struct {
	Rows            []*CustomerForm `json:"rows"`
	CompareRows     []*CustomerForm `json:"compare_rows"`
	CompareSameRows []*CustomerForm `json:"compare_same_rows"`
	Summary         *CustomerForm   `json:"summary"`
}

// 小掌柜渠道分布
type CustomerForm struct {
	Member                      bool    `json:"member"`
	MemberName                  string  `json:"member_name"`
	BusinessAmount              float64 `json:"business_amount"`
	ProportionBusinessAmount    float64 `json:"proportion_business_amount"`
	MerchantAllowance           float64 `json:"merchant_allowance"`
	ProportionMerchantAllowance float64 `json:"proportion_merchant_allowance"`
	ValidOrderCount             int64   `json:"valid_order_count"`
	ProportionValidOrderCount   float64 `json:"proportion_valid_order_count"`

	CompareBusinessAmount             float64 `json:"compare_business_amount"`
	CompareSameBusinessAmount         float64 `json:"compare_same_business_amount"`
	IncreaseCompareBusinessAmount     float64 `json:"increase_compare_business_amount"`
	IncreaseCompareSameBusinessAmount float64 `json:"increase_compare_same_business_amount"`
}
