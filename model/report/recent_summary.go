package report

type RecentSummaryResponse struct {
	Rows    []*RecentSummary `json:"rows"`
	Summary *RecentSummary   `json:"summary"`
	Total   int64            `json:"total"`
}

type RecentSummaryChild struct {
	ChannelId   int64  `json:"channel_id"`
	ChannelCode string `json:"channel_code"`
	ChannelName string `json:"channel_name"`

	BusinessAmount      float64 `json:"business_amount"`
	FinanceRealAmount   float64 `json:"finance_real_amount"`
	FinanceExpendAmount float64 `json:"finance_expend_amount"`
	ValidOrderCount     int64   `json:"valid_order_count"`
	PayAmount           float64 `json:"pay_amount"`
	TransferRealAmount  float64 `json:"transfer_real_amount"`
}

type RecentSummaryPayment struct {
	PaymentId          int64   `json:"payment_id"`
	PaymentName        string  `json:"payment_name"`
	PayAmount          float64 `json:"pay_amount"`
	TransferRealAmount float64 `json:"transfer_real_amount"`
}

type RecentSummary struct {
	BusDate       string `json:"bus_date"`
	RegionId      int64  `json:"region_id"`
	RegionCode    string `json:"region_code"`
	RegionName    string `json:"region_name"`
	RegionAddress string `json:"region_address"`
	RegionAlias   string `json:"region_alias"`
	StoreType     string `json:"store_type"`
	StoreTypeName string `json:"store_type_name"`
	BusinessDays  int64  `json:"business_days"`

	BusinessAmount             float64 `json:"business_amount"`
	CompareBusinessAmount      float64 `json:"compare_business_amount"`
	FinanceRealAmount          float64 `json:"finance_real_amount"`
	CompareFinanceRealAmount   float64 `json:"compare_finance_real_amount"`
	FinanceExpendAmount        float64 `json:"finance_expend_amount"`
	CompareFinanceExpendAmount float64 `json:"compare_finance_expend_amount"`
	ValidOrderCount            int64   `json:"valid_order_count"`
	CompareValidOrderCount     int64   `json:"compare_valid_order_count"`
	PayAmount                  float64 `json:"pay_amount"`
	ComparePayAmount           float64 `json:"compare_pay_amount"`
	TransferRealAmount         float64 `json:"transfer_real_amount"`
	CompareTransferRealAmount  float64 `json:"compare_transfer_real_amount"`

	Child []*RecentSummaryChild `json:"child"`

	Payments []*RecentSummaryPayment `json:"payments"`

	Total int64 `json:"total"`
}

func (rs *RecentSummary) ToMap() []*map[string]interface{} {
	res := make([]*map[string]interface{}, len(rs.Child))
	for i, ch := range rs.Child {
		res[i] = &map[string]interface{}{
			"bus_date": rs.BusDate,
			"region_id": rs.RegionId,
			"region_code": rs.RegionCode,
			"region_name": rs.RegionName,
			"region_address": rs.RegionAddress,
			"region_alias": rs.RegionAlias,
			"store_type": rs.StoreType,
			"store_type_name": rs.StoreTypeName,
			"business_days": rs.BusinessDays,
			"channel_id": ch.ChannelId,
			"channel_code": ch.ChannelCode,
			"channel_name": ch.ChannelName,
			"business_amount": ch.BusinessAmount,
			"finance_real_amount": ch.FinanceRealAmount,
			"finance_expend_amount": ch.FinanceExpendAmount,
			"valid_order_count": ch.ValidOrderCount,
			"pay_amount": ch.PayAmount,
			"transfer_real_amount": ch.TransferRealAmount,
		}
	}
	return res
}
