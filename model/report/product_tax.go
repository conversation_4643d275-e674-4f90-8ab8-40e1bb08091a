package report

type ProductTaxSalesResponse struct {
	Rows        []*ProductTaxSales `json:"rows"`
	SendFees    []*ProductTaxSales `json:"send_fees"`
	PackageFees []*ProductTaxSales `json:"package_fees"`
	Summary     *ProductTaxSales   `json:"summary"`
	Total       int64              `json:"total"`
}

type ProductTaxSales struct {
	StoreId                int64   `json:"store_id"`
	StoreCode              string  `json:"store_code"`
	StoreName              string  `json:"store_name"`
	StoreAddress           string  `json:"store_address"`
	StoreAlias             string  `json:"store_alias"`
	StoreStatus            string  `json:"store_status"`
	StoreType              string  `json:"store_type"`
	StoreTypeName          string  `json:"store_type_name"`
	CompanyId              int64   `json:"company_id"`
	CompanyCode            string  `json:"company_code"`
	CompanyName            string  `json:"company_name"`
	BusinessDays           int64   `json:"business_days"`
	CategoryId             int64   `json:"category_id"`
	CategoryCode           string  `json:"category_code"`
	CategoryName           string  `json:"category_name"`
	ProductId              int64   `json:"product_id"`
	ProductCode            string  `json:"product_code"`
	ProductName            string  `json:"product_name"`
	ChannelId              int64   `json:"channel_id"`
	ChannelCode            string  `json:"channel_code"`
	ChannelName            string  `json:"channel_name"`
	ProductCount           int64   `json:"product_count"`
	GrossAmount            float64 `json:"gross_amount"`
	DiscountAmount         float64 `json:"discount_amount"`
	FinanceRealAmount      float64 `json:"finance_real_amount"`
	TransferAmount         float64 `json:"transfer_amount"`
	DiscountTransferAmount float64 `json:"discount_transfer_amount"`
	ProductTax             float64 `json:"product_tax"`
	InvoiceAmount          float64 `json:"invoice_amount"`
	Total                  int64   `json:"total"`
}

func (p *ProductTaxSales) RowToMap() map[string]interface{} {
	return map[string]interface{}{
		"store_address":       p.StoreAddress,
		"store_code":          p.StoreCode,
		"store_name":          p.StoreName,
		"company_code":        p.CompanyCode,
		"company_name":        p.CompanyName,
		"store_type_name":     p.StoreTypeName,
		"business_days":       p.BusinessDays,
		"category_code":       p.CategoryCode,
		"category_name":       p.CategoryName,
		"product_code":        p.ProductCode,
		"product_name":        p.ProductName,
		"channel_code":        p.ChannelCode,
		"channel_name":        p.ChannelName,
		"product_count":       p.ProductCount,
		"gross_amount":        p.GrossAmount,
		"discount_amount":     p.DiscountAmount,
		"finance_real_amount": p.FinanceRealAmount,
		"transfer_amount":     p.TransferAmount,
		"product_tax":         p.ProductTax,
		"invoice_amount":      p.InvoiceAmount,
	}
}

func (p *ProductTaxSales) SendToMap() map[string]interface{} {
	return map[string]interface{}{
		"store_address":       p.StoreAddress,
		"store_code":          p.StoreCode,
		"store_name":          p.StoreName,
		"company_code":        p.CompanyCode,
		"company_name":        p.CompanyName,
		"store_type_name":     p.StoreTypeName,
		"business_days":       p.BusinessDays,
		"category_code":       "-",
		"category_name":       "-",
		"product_code":        "-",
		"product_name":        "餐饮服务费（配送费）",
		"channel_code":        p.ChannelCode,
		"channel_name":        p.ChannelName,
		"product_count":       "-",
		"gross_amount":        "-",
		"discount_amount":     "-",
		"finance_real_amount": "-",
		"transfer_amount":     "-",
		"product_tax":         p.ProductTax,
		"invoice_amount":      p.InvoiceAmount,
	}
}

func (p *ProductTaxSales) PackageToMap() map[string]interface{} {
	return map[string]interface{}{
		"store_address":       p.StoreAddress,
		"store_code":          p.StoreCode,
		"store_name":          p.StoreName,
		"company_code":        p.CompanyCode,
		"company_name":        p.CompanyName,
		"store_type_name":     p.StoreTypeName,
		"business_days":       p.BusinessDays,
		"category_code":       "-",
		"category_name":       "-",
		"product_code":        "-",
		"product_name":        "餐饮服务费（包装费）",
		"channel_code":        p.ChannelCode,
		"channel_name":        p.ChannelName,
		"product_count":       "-",
		"gross_amount":        "-",
		"discount_amount":     "-",
		"finance_real_amount": "-",
		"transfer_amount":     "-",
		"product_tax":         p.ProductTax,
		"invoice_amount":      p.InvoiceAmount,
	}
}
