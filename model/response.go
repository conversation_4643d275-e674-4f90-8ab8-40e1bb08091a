package model

// 响应结构体
type Response struct {
	Total   int64                  `json:"total,omitempty"`   //行数
	Summary map[string]interface{} `json:"summary,omitempty"` //汇总
	Rows    []interface{}          `json:"rows,omitempty"`    //返回记录
}

// 首页报表响应
type DashboardResponse struct {
	Summary     map[string]interface{} `json:"summary,omitempty"`      // 汇总
	LineChart   map[string]interface{} `json:"line_chart,omitempty"`   //
	StoreRank   []interface{}          `json:"store_rank,omitempty"`   // 门店排名
	ChannelRank []interface{}          `json:"channel_rank,omitempty"` // 渠道排名
	ProductRank []interface{}          `json:"product_rank,omitempty"` // 单品排名
}

type UpdateTimeResponse struct {
    Datetime string `json:"datetime,omitempty"`
}
