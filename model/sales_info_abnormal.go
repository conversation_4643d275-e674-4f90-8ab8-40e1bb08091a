package model

import (
	"encoding/json"
	"github.com/SmallTianTian/go-tools/slice"
	"time"
)

// 先存储起来的异常电子小票模型
type SalesTicketContentAbnormal struct {
	Id              int64                  `gorm:"<-:create" json:"id,omitempty"`                                          // ID
	EticketId       int64                  `gorm:"<-:create;" json:"eticket_id,omitempty"`                                 // 电子小票记录ID
	TicketId        string                 `gorm:"<-:create;type:varchar(64);uniqueIndex:only" json:"ticket_id,omitempty"` // 电子小票上传ID
	PartnerId       int64                  `gorm:"<-:create;uniqueIndex:only" json:"partner_id,omitempty"`                 // 租户ID
	ScopeId         int64                  `gorm:"<-:create" json:"scope_id,omitempty"`                                    // 分区ID
	StoreId         int64                  `json:"store_id,omitempty"`                                                     // 门店ID
	StoreName       string                 `gorm:"-" json:"store_name,omitempty"`                                          // 门店名称
	BusDate         time.Time              `gorm:"type:date;index" json:"bus_date,omitempty"`                              // 营业日期
	OrderTime       time.Time              `gorm:"type:timestamp" json:"order_time,omitempty"`                             // 订单时间
	Created         time.Time              `gorm:"<-:create" json:"created,omitempty"`                                     // 创建时间
	Updated         time.Time              `json:"updated,omitempty"`                                                      // 更新时间
	UpdatedBy       int64                  `json:"updated_by,omitempty"`                                                   // 更新人
	UpdatedName     string                 `gorm:"-" json:"updated_name,omitempty"`                                        // 更新人名称
	Status          SalesInfoStatus        `gorm:"type:varchar(10);index" json:"status,omitempty"`                         // 状态
	ProcessStatus   SalesInfoProcessStatus `gorm:"type:varchar(10);index" json:"process_status,omitempty"`                 // 执行状态
	Content         *json.RawMessage       `gorm:"<-:create;type:json" json:"content,omitempty"`                           // 原始数据
	ContentModified *json.RawMessage       `gorm:"type:json" json:"content_modified,omitempty"`                            // 修改后数据
	IsDeleted       bool                   `json:"is_deleted,omitempty"`                                                   // 是否被删除，仅用于数据库标示
	ChannelName     string                 `json:"channel_name,omitempty"`                                                 // 渠道名称
	ChannelId       int64                  `json:"channel_id,omitempty"`                                                   // 渠道订单类型
	OrderType       string                 `json:"order_type,omitempty"`                                                   // 订单类型
	OrderTypeName   string                 `json:"order_type_name,omitempty"`                                              // 订单类型名称 	// 错误信息
	ShiftNumber     string                 `json:"shift_number,omitempty"`                                                 // 班次号
}

func (m *SalesTicketContentAbnormal) TableName() string {
	return "sales_ticket_contents_abnormal"
}

func (si *SalesTicketContentAbnormal) ToTicket() (*Ticket, error) {
	var content *json.RawMessage
	if content = si.ContentModified; content == nil {
		content = si.Content
	}
	var ticket Ticket

	err := json.Unmarshal(*content, &ticket)

	var other float64
	feesM := make(map[string]float64)
	for _, item := range ticket.Fees {
		feesM[item.Type] = item.Amount
		if !slice.StringInSlice([]string{"TIP", "DELIVERY_FEE", "PACKAGE_FEE", "SERVICE_FEE"}, item.Type) {
			other += item.Amount
		}
	}

	// 配送费使用外卖信息下面的sendFee
	//ticket.Amounts.DeliveryFee = ticket.TakeawayInfo.SendFee
	// 打包费使用外卖信息下面的package_fee
	ticket.Amounts.PackageFee = ticket.TakeawayInfo.PackageFee

	//if ticket.Amounts.Tip == 0 && feesM["TIP"] != 0 {
	//	ticket.Amounts.Tip = feesM["TIP"]
	//}
	//if ticket.Amounts.DeliveryFee == 0 && feesM["DELIVERY_FEE"] != 0 {
	//	ticket.Amounts.DeliveryFee = feesM["DELIVERY_FEE"]
	//}
	//if ticket.Amounts.PackageFee == 0 && feesM["PACKAGE_FEE"] != 0 {
	//	ticket.Amounts.PackageFee = feesM["PACKAGE_FEE"]
	//}
	//if ticket.Amounts.ServiceFee == 0 && feesM["SERVICE_FEE"] != 0 {
	//	ticket.Amounts.ServiceFee = feesM["SERVICE_FEE"]
	//}
	//if ticket.Amounts.OtherFee == 0 && other != 0 {
	//	ticket.Amounts.OtherFee = other
	//}
	return &ticket, err
}
