package pos

type OrderTypePeriodReport struct {
	MealName       string            `json:"mealName"`       //餐段名称
	Period         []string          `json:"period"`         //餐段时间
	BowlNum        float64           `json:"bowlNum"`        // 碗数
	AverageBowlNum float64           `json:"averageBowlNum"` // 碗数/平均
	Amount         float64           `json:"amount"`         //营业净额
	TypePeriodList []OrderTypePeriod `json:"type_period_list"`
}

type OrderTypePeriod struct {
	OrderType      string  `json:"value"`
	BowlNum        float64 `json:"bowlNum"`        // 碗数
	Amount         float64 `json:"amount"`         //营业净额
	AverageBowlNum float64 `json:"averageBowlNum"` // 碗数/平均
	RevenueShare   float64 `json:"revenue_share"`  // 金额占比
}

type OrderTypeResponse struct {
	Total  *OrderTypePeriodReport   `json:"total"`
	Period []*OrderTypePeriodReport `json:"period"`
}
