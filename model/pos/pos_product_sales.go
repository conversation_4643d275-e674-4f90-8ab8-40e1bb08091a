package pos

type ProductSales struct {
	Id         int64   `json:"id"`         // id
	Name       string  `json:"name"`       // 名称
	Qty        int32   `json:"qty"`        // 数量
	Amount     float64 `json:"amount"`     // 实收金额
	Percentage float64 `json:"percentage"` // 实收金额占比
}

type ProductSalesReport struct {
	Summary          ProductSales    `json:"summary"`            // 总计
	ProductSalesList []*ProductSales `json:"product_sales_list"` // 明细列表
}
