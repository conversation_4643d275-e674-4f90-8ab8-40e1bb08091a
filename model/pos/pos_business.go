package pos

type BusinessChannel struct {
	BusinessAmount float64 `json:"business_amount"` //营业额
	RealAmount     float64 `json:"real_amount"`     //实收金额
	ExpendAmount   float64 `json:"expend_amount"`   //优惠金额
	Count          int     `json:"count"`           //有效单数
	TpName         string  `json:"tp_name"`
}

type BusinessReport struct {
	Channel BusinessChannels `json:"channel"`
	Sum     *BusinessChannel `json:"sum"`
}

type BusinessChannels []*BusinessChannel

func (b BusinessChannels) Len() int {
	return len(b)
}

func (b BusinessChannels) Less(i, j int) bool {
	return b[i].BusinessAmount < b[j].BusinessAmount
}

func (b BusinessChannels) Swap(i, j int) {
	b[i], b[j] = b[j], b[i]
}
