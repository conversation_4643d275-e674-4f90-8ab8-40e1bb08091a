package pos

// Period 时段报表
type Period struct {
	TimePeriod string  `json:"time_period"` // 時段
	BillCount  int     `json:"bill_count"`  // 賬單數
	BowlCount  float64 `json:"bowl_count"`  // 碗数
	Amount     float64 `json:"amount"`      // 金额
	Percentage float64 `json:"percentage"`  // 金额占比
}

type StorePeriodReportResponse struct {
	PeriodList []*Period `json:"period_list"` // 时段列表
	Summary    Period    `json:"summary"`     // 汇总
}
