package pos

type DepartmentBusiness struct {
	Id         int64   `json:"id"`         // id
	Name       string  `json:"name"`       // 名称
	Qty        int32   `json:"qty"`        // 数量
	Amount     float64 `json:"amount"`     // 实收金额
	Percentage float64 `json:"percentage"` // 实收金额占比
}

type DepartmentBusinessReport struct {
	Summary        DepartmentBusiness    `json:"summary"`         // 总计
	DepartmentList []*DepartmentBusiness `json:"department_list"` // 部门列表
}
