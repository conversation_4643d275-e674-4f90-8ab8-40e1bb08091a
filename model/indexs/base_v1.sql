-- =============================================
-- Author: 杜森
-- Create date: 20200804
-- Description: 针对销售拆解表(PG)的索引设计。在 3000万数据量下查询响应时间 500ms 左右。
-- Modify [n]: 20200804
-- =============================================

-- STORE_CACHES
CREATE INDEX CONCURRENTLY idx_store_caches_geo0 ON store_caches USING BTREE (geo0 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_store_caches_geo1 ON store_caches USING BTREE (geo1 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_store_caches_geo2 ON store_caches USING BTREE (geo2 NULLS LAST);

CREATE INDEX CONCURRENTLY idx_store_caches_branch0 ON store_caches USING BTREE (branch0 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_store_caches_branch1 ON store_caches USING BTREE (branch1 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_store_caches_branch2 ON store_caches USING BTREE (branch2 NULLS LAST);


-- PRODUCT_CACHES
CREATE INDEX CONCURRENTLY idx_product_caches_category0 ON product_caches USING BTREE (category0 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_product_caches_category1 ON product_caches USING BTREE (category1 NULLS LAST);
CREATE INDEX CONCURRENTLY idx_product_caches_category2 ON product_caches USING BTREE (category2 NULLS LAST);

-- ID2ID_CACHES
CREATE INDEX CONCURRENTLY idx_id2id_caches_cid ON id2id_caches USING BTREE (c_id NULLS LAST);


CREATE INDEX CONCURRENTLY idx_sales_ticket_contents_process_status ON sales_ticket_contents USING BTREE (process_status NULLS LAST);
CREATE INDEX CONCURRENTLY idx_sales_ticket_contents_status ON sales_ticket_contents USING BTREE (status NULLS LAST);
CREATE INDEX CONCURRENTLY idx_sales_ticket_contents_bus_date ON sales_ticket_contents USING BTREE (bus_date NULLS LAST);
CREATE INDEX CONCURRENTLY idx_sales_ticket_contents_ticket_id ON sales_ticket_contents USING BTREE (ticket_id NULLS LAST);
CREATE UNIQUE INDEX CONCURRENTLY idx_sales_ticket_contents_eticketid_partnerid ON sales_ticket_contents USING BTREE (
    eticket_id NULLS LAST, partner_id NULLS LAST
);


-- SALES_TICKET_AMOUNTS
CREATE INDEX CONCURRENTLY idx_ticket_amounts_date_store_scope_partner ON sales_ticket_amounts USING BTREE (
    bus_date DESC NULLS LAST, store_id, scope_id, partner_id
);
CREATE INDEX CONCURRENTLY idx_ticket_amounts_date_week ON sales_ticket_amounts USING BTREE (
    bus_date_week DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_ticket_amounts_date_month ON sales_ticket_amounts USING BTREE (
    bus_date_month DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_ticket_amounts_date_year ON sales_ticket_amounts USING BTREE (
    bus_date_year DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_ticket_amounts_channel_id_name ON sales_ticket_amounts USING BTREE (
    channel_id NULLS LAST, channel_name NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_ticket_amounts_order_type ON sales_ticket_amounts USING BTREE (
    order_type NULLS LAST
);
-- CREATE INDEX CONCURRENTLY idx_ticket_amounts_order_time_part_hours ON sales_ticket_amounts USING BTREE (
--     DATE_PART('hour', order_time) NULLS LAST
-- );


-- SALES_PRODUCT_AMOUNTS
CREATE INDEX CONCURRENTLY idx_product_amounts_date_store_scope_partner_product ON sales_product_amounts USING BTREE (
    bus_date DESC NULLS LAST, store_id, scope_id, partner_id, product_id
);
CREATE INDEX CONCURRENTLY idx_product_amounts_date_week ON sales_product_amounts USING BTREE (
    bus_date_week DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_product_amounts_date_month ON sales_product_amounts USING BTREE (
    bus_date_month DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_product_amounts_date_year ON sales_product_amounts USING BTREE (
    bus_date_year DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_product_amounts_channel_id_name ON sales_product_amounts USING BTREE (
    channel_id NULLS LAST, channel_name NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_product_amounts_order_type ON sales_product_amounts USING BTREE (
    order_type NULLS LAST
);
-- CREATE INDEX CONCURRENTLY idx_product_amounts_order_time_part_hours ON sales_product_amounts USING BTREE (
--     DATE_PART('hour', order_time) NULLS LAST
-- );


-- SALES_PAYMENT_AMOUNTS
CREATE INDEX CONCURRENTLY idx_payment_amounts_date_store_scope_partner ON sales_payment_amounts USING BTREE (
    bus_date DESC NULLS LAST, store_id, scope_id, partner_id
);
CREATE INDEX CONCURRENTLY idx_payment_amounts_date_week ON sales_payment_amounts USING BTREE (
    bus_date_week DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_payment_amounts_date_month ON sales_payment_amounts USING BTREE (
    bus_date_month DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_payment_amounts_date_year ON sales_payment_amounts USING BTREE (
    bus_date_year DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_payment_amounts_payment_id ON sales_payment_amounts USING BTREE (
    payment_id NULLS LAST
);
-- CREATE INDEX CONCURRENTLY idx_payment_amounts_order_time_part_hours ON sales_payment_amounts USING BTREE (
--     DATE_PART('hour', order_time) NULLS LAST
-- );


-- SALES_DISCOUNT_AMOUNTS
CREATE INDEX CONCURRENTLY idx_discount_amounts_date_store_scope_partner ON sales_discount_amounts USING BTREE (
    bus_date DESC NULLS LAST, store_id, scope_id, partner_id
);
CREATE INDEX CONCURRENTLY idx_discount_amounts_date_week ON sales_discount_amounts USING BTREE (
    bus_date_week DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_discount_amounts_date_month ON sales_discount_amounts USING BTREE (
    bus_date_month DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_discount_amounts_date_year ON sales_discount_amounts USING BTREE (
    bus_date_year DESC NULLS LAST
);
CREATE INDEX CONCURRENTLY idx_discount_amounts_promotion_id ON sales_discount_amounts USING BTREE (
    promotion_id NULLS LAST
);
