package model

import (
	"strings"
	"time"
)

type PeriodGroupType string

const (
	Day   PeriodGroupType = "DAY"
	Week                  = "WEEK"
	Month                 = "MONTH"
	Year                  = "YEAR"
)

func NewPeriodGroupType(s string) PeriodGroupType {
	switch true {
	case strings.ToLower(s) == "day":
		return Day
	case strings.ToLower(s) == "week":
		return Week
	case strings.ToLower(s) == "month":
		return Month
	case strings.ToLower(s) == "year":
		return Year
	default:
		return Day
	}
}

type RepoDashboardCondition struct {
	// ---- base condition
	PartnerId int64 `json:"partner_id,omitempty"`
	ScopeId   int64 `json:"scope_id,omitempty"`
	UserId    int64 `json:"user_id,omitempty"`
	Start     time.Time
	End       time.Time
	// ---- attrs
	RegionSearchIds []int64 `json:"region_search_ids,omitempty"`
	Panel           []string
	IsPre           bool
	Lan             string
}

type RepoCondition struct {
	// sort limit offset
	Limit       int64
	Offset      uint64
	Sort        string
	Desc        bool
	LimitByUnit bool // 分页时是否需要按照商品单位分组
	// ---- select
	IncludeTotal   bool
	IncludeSummary bool
	IsPre          bool
	IsToday        bool // 是否是今日
	IsCompare      bool // 是否计算环比
	IsCompareSame  bool // 是否计算同比
	IsCombo        bool // 是否统计套餐
	// ---- group
	PeriodGroupType  PeriodGroupType
	RegionGroupType  string `json:"region_group_type,omitempty"`
	RegionGroupLevel int    `json:"region_group_level,omitempty"`
	// ---- base condition
	PartnerId        int64 `json:"partner_id,omitempty"`
	ScopeId          int64 `json:"scope_id,omitempty"`
	UserId           int64 `json:"user_id,omitempty"`
	Start            time.Time
	End              time.Time
	CompareStart     string
	CompareEnd       string
	CompareSameStart string
	CompareSameEnd   string
	// ---- attrs
	RegionSearchType string  `json:"region_search_type,omitempty"`
	RegionSearchIds  []int64 `json:"region_search_ids,omitempty"`
	//RegionIds          []int64
	ProductCategoryIds []int64
	ProductCategory0Id int64 // 商品一级分类
	ProductCategory1Id int64 // 商品二级分类
	ProductCategory2Id int64 // 商品三级分类
	ProductIds         []int64
	PaymentIds         []int64
	DiscountIds        []int64
	ChannelIds         []int64
	ChannelId          int64
	Channels           []string
	StoreType          string
	StoreTypes         []string
	OpenStatus         string
	OrderType          string
	TagType            string // 详细/汇总
	Lan                string
	RefundCode         string //退单原因code
	RefundSide         string //退单方
	Logistic           string //物流
	PriceScope         [][]float64
	Period             []int64 // 时段
	StoreIDs           []int64 // 门店id
	TagTypes           string

	IsNatural bool
	Taxes     []float64

	OrderStatus string
	ProductId   int64
	Code        string //code

	StoreTags              []string
	IncludeRealAmountZero  bool
	RegionCodes            []string
	CouponChannels         []string
	ComboProductIds        []int64
	Search                 string
	TicketNo               string
	ProductAttributesCodes []string
}
