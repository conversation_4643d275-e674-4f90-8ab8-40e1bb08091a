package mq_task

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	"log"
	"strconv"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/mq"
	"gitlab.hexcloud.cn/histore/sales-report/services/ticket"
)

var DefaultMQ mq.MQ

func Init() {
	addr := config.DefaultConfig.ConnStr.Mq
	channel := config.DefaultConfig.Mq.Channel

	DefaultMQ = mq.NewNsqMQ(addr, channel)
	// 校验电子小票合不合规则
	if err := DefaultMQ.RegisterReceive(config.WaitCheckTopic, handleWaitCheckTicket); err != nil {
		log.Fatalf("Init mq receive for topic `%s` failed. Err: `%+v`", config.WaitCheckTopic, err)
	}
	// 处理折扣分摊
	if err := DefaultMQ.RegisterReceive(config.DiscountApportionTopic, handleDiscountApportion); err != nil {
		log.Fatalf("Init mq receive for topic `%s` failed. Err: `%+v`", config.DiscountApportionTopic, err)
	}
	// 将小票拆解为四张基础表
	if err := DefaultMQ.RegisterReceive(config.ResolveTicketTopic, handleResolveTicket); err != nil {
		log.Fatalf("Init mq receive for topic `%s` failed. Err: `%+v`", config.ResolveTicketTopic, err)
	}
	// 将异常小票拆解为2张基础表
	if err := DefaultMQ.RegisterReceive(config.ResolveAbnormalTicketTopic, handleResolveAbnormalTicket); err != nil {
		log.Fatalf("Init mq receive for topic `%s` failed. Err: `%+v`", config.ResolveAbnormalTicketTopic, err)
	}

}

// 校验电子小票合不合规则
func handleWaitCheckTicket(ctx context.Context, body []byte) error {
	id, err := strconv.Atoi(string(body))
	if err != nil {
		logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
		logger.Pre().Errorf("From mq, wait check ticket id `%d` is not a int.", id)
		return nil
	}
	ctx = context.WithValue(ctx, config.TrackId, id)
	var succ bool
	// 校验电子小票是否合法
	if succ, err = ticket.ValidateTicket(ctx, int64(id)); err == nil && succ {
		PublishMessage(ctx, config.DiscountApportionTopic, body)
	}
	return err
}

func handleDiscountApportion(ctx context.Context, body []byte) error {
	id, err := strconv.Atoi(string(body))
	if err != nil {
		logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
		return nil
	}
	ctx = context.WithValue(ctx, config.TrackId, id)
	if err = ticket.DiscountApportion(ctx, int64(id)); err == nil {
		PublishMessage(ctx, config.ResolveTicketTopic, body)
	}
	return err
}

func handleResolveTicket(ctx context.Context, body []byte) error {
	id, err := strconv.Atoi(string(body))
	if err != nil {
		logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
		return nil
	}
	ctx = context.WithValue(ctx, config.TrackId, id)
	err = ticket.ResolveTicket(ctx, int64(id))
	if err != nil {
		logger.Pre().Errorf("拆解数据存储异常:%v", err)
		return err
	}
	return nil
}

func handleResolveAbnormalTicket(ctx context.Context, body []byte) error {
	id, err := strconv.Atoi(string(body))
	if err != nil {
		logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
		return nil
	}
	ctx = context.WithValue(ctx, config.TrackId, id)
	err = ticket.ResolveTicketAbnormal(ctx, int64(id))
	if err != nil {
		logger.Pre().Errorf("拆解异常小票数据存储异常:%v", err)
		return err
	}
	return nil
}

func PublishMessage(ctx context.Context, topic string, message []byte) error {
	suffix := ""
	size := len(message)
	if size > 100 {
		size = 100
		suffix = "..."
	}

	if size != 0 {
		logger.Pre().Debugf("Send nsq message to `%s`, Msg: `%s`", topic, string(message[:size])+suffix)
	} else {
		logger.Pre().Debugf("Send nsq message to `%s`", topic)
	}

	return tracing.ModuleTracing(ctx, "Publish:"+topic, func(ctx2 context.Context) error {
		return DefaultMQ.Publish(topic, message)
	})
}
