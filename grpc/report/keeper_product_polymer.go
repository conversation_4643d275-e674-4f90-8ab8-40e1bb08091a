package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

func (t *SalesReportServer) ShopKeeperProductPolymer(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductPolymerResponse, error) {

	modelResp, err := shopkeeper.ProductPolymer(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("小掌柜商品聚合统计查询数据出错：", err)
		return nil, err
	}
	productResp := sales_report.ProductPolymerResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品聚合统计转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品集合统计grpc结构体解析失败：", err)
		return nil, err
	}
	return &productResp, err
}
