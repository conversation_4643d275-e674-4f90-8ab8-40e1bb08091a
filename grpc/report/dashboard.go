package report

import (
	"context"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) Dashboard(ctx context.Context, req *sales_report.DashboardRequest) (
	*sales_report.DashboardResponse, error) {
	modelResp, err := report.QueryDashboard(ctx, ToDashboardRequest(req))
	if err != nil {
		return nil, err
	}
	return &sales_report.DashboardResponse{
		Summary:     helpers.ToStruct(modelResp.Summary),
		LineChart:   helpers.ToStruct(modelResp.LineChart),
		StoreRank:   helpers.ToSliceStruct(modelResp.StoreRank),
		ChannelRank: helpers.ToSliceStruct(modelResp.ChannelRank),
		ProductRank: helpers.ToSliceStruct(modelResp.ProductRank),
	}, err
}
