package report

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	c_model "gitlab.hexcloud.cn/histore/sales-report/model/cash_management"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/cash_management"
)

// PettyCashDailySettlementReport 实现grpc中零用现金日结报表的接口
func (t *SalesReportServer) PettyCashDailySettlementReport(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.PettyCashDailySettlementReportResponse, error) {
	// 调用门店报表的服务
	comReq := ToCommonRequest(ctx, req) //
	// 将grpc请求转成sql条件
	modelResp, err := cash_management.QueryPettyCashDailySettlementReport(ctx, comReq)
	if err != nil {
		logger.Pre().Errorf("零用现金日结报表查询数据出错：%v", err)
		return nil, err
	}
	return t.buildResponseForPettyCashDailySettlementReport(modelResp, req), nil
}

// model转response,类型处理为string 回传 防止精度丢失
func (t *SalesReportServer) buildResponseForPettyCashDailySettlementReport(modelResp *c_model.PettyCashDailySettlementReportResp, req *sales_report.CommonRequest) *sales_report.PettyCashDailySettlementReportResponse {
	startTime, _ := RemoveTimeZoneAndParse(req.Start)
	endTime, _ := RemoveTimeZoneAndParse(req.End)
	periodStartDate := startTime.Format("20060102")
	periodEndDate := endTime.Format("20060102")
	periodDateText := fmt.Sprintf("%s to %s", periodStartDate, periodEndDate)
	resp := &sales_report.PettyCashDailySettlementReportResponse{Total: modelResp.Total, PeriodStartDate: periodStartDate, PeriodEndDate: periodEndDate, PeriodDateText: periodDateText}
	rows := make([]*sales_report.PettyCashDailySettlementReportResponse_PettyCashDailySettlementReportRow, 0, len(modelResp.Rows))

	summaryMap := make(map[string]interface{}, 0)
	err := json.Unmarshal([]byte(modelResp.Summary.PayoutDetailSummary), &summaryMap)
	if err != nil {
		logger.Pre().Errorf("零用现金日结报表 Unmarshal PayoutSummary出错：%v", err)
	}
	sum, err := utils.Map2Struct(summaryMap)
	if err != nil {
		logger.Pre().Errorf("零用现金日结报表marshal pb struct PayoutSummary出错：%v", err)
	}
	resp.PayoutDetailSummary = sum
	resp.PayoutAmountSummary = decimal.NewFromFloat(modelResp.Summary.PayoutAmountSummary).StringFixed(2)

	for _, r := range modelResp.Rows {
		row := &sales_report.PettyCashDailySettlementReportResponse_PettyCashDailySettlementReportRow{
			BusDate:           r.BusinessDate,
			StoreId:           cast.ToUint64(r.StoreId),
			StoreCode:         r.StoreCode,
			StoreName:         r.StoreName,
			StartBalance:      decimal.NewFromInt(r.StartBalance).Div(decimal.NewFromInt(100)).StringFixed(2),
			CollectCashAmount: decimal.NewFromInt(r.CollectCashAmount).Div(decimal.NewFromInt(100)).StringFixed(2),
			//OtherAmount:       decimal.NewFromInt(r.OtherAmount).Div(decimal.NewFromInt(100)).String(),
			PayoutAmount: decimal.NewFromInt(r.PayoutAmount).Div(decimal.NewFromInt(100)).Round(2).StringFixed(2),
			EndBalance:   decimal.NewFromInt(r.EndBalance).Div(decimal.NewFromInt(100)).Round(2).StringFixed(2),
		}
		detailMap := make(map[string]interface{}, 0)
		//r.PayoutDetail = `{"a":"1","b":"2"}`
		err := json.Unmarshal([]byte(r.PayoutDetail), &detailMap)
		if err != nil {
			logger.Pre().Errorf("零用现金日结报表Unmarshal支出明细出错：%v", err)
		}
		s, err := utils.Map2Struct(detailMap)
		if err != nil {
			logger.Pre().Errorf("零用现金日结报表marshal pb struct支出明细出错：%v", err)
		}
		row.PayoutDetail = s

		rows = append(rows, row)
	}
	resp.Rows = rows
	return resp
}
