package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) PaymentStatistics(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.PaymentStatisticsResponse, error) {
	modelResp, err := report.QueryPaymentStatistics(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("支付统计查询数据出错：", err)
		return nil, err
	}
	paymentResp := &sales_report.PaymentStatisticsResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("支付统计转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, paymentResp)
	if err != nil {
		logger.Pre().Error("支付统计grpc结构体解析失败：", err)
	}
	return paymentResp, err
}
