package report

import (
	"context"
	"encoding/json"
	mopen "gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/open"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
)

func (t *SalesReportServer) OpenFinancialReport(ctx context.Context, req *sales_report.OpenFinancialRequest) (
	*sales_report.OpenFinancialResponse, error) {
	modelResp, err := open.QueryOpenFinancialReport(ctx, &mopen.FinancialReportReq{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		Summary:   req.Summary,
	})

	if err != nil {
		logger.Pre().Errorf("Open收银汇总表查询数据出错：%v", err)
		return nil, err
	}
	financialResp := &sales_report.OpenFinancialResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("Open收银汇总表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, financialResp)
	if err != nil {
		logger.Pre().Error("Open收银汇总表grpc结构体解析失败：", err)
		return nil, err
	}
	return financialResp, err
}
