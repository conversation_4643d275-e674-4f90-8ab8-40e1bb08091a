package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) StoreChannelSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.StoreChannelSalesResponse, error) {
	modelResp, err := report.QueryStoreChannelSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Errorf("门店渠道查询数据出错：%v", err)
		return nil, err
	}
	storeChannelResp := &sales_report.StoreChannelSalesResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("门店渠道查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, storeChannelResp)
	if err != nil {
		logger.Pre().Error("门店渠道grpc结构体解析失败：", err)
		return nil, err
	}
	return storeChannelResp, err
}
