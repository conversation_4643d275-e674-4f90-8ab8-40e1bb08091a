package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductChannelSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductChannelResponse, error) {
	modelResp, err := report.QueryProductChannelSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("单品渠道查询数据出错：", err)
		return nil, err
	}
	productChannelResp := sales_report.ProductChannelResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("单品渠道查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productChannelResp)
	if err != nil {
		logger.Pre().Error("单品渠道grpc结构体解析失败：", err)
		return nil, err
	}
	return &productChannelResp, err
}
