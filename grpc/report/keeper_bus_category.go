package report

import (
	"context"
	"encoding/json"
	st "github.com/golang/protobuf/ptypes/struct"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
	"hexcloud.cn/histore/hexerror"
)

// 小掌柜.商品分类
func (t *SalesReportServer) ShopKeeperBusCategory(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.CommonResponse, error) {
	// 将grpc请求转成sql请求
	commonRequest := ToCommonRequest(ctx, req)
	if len(req.RegionSearchIds) == 0 {
		return nil, hexerror.InvalidData("RegionSearchIds is empty")
	}
	modelResp, err := shopkeeper.ProductCategory(ctx, commonRequest)
	if err != nil {
		return nil, err
	}
	commonResp := &sales_report.CommonResponse{Total: modelResp.Total}
	marshal, err := json.Marshal(modelResp.Rows)
	var commonRows = &[]*st.Struct{}
	err = json.Unmarshal(marshal, commonRows)
	if err != nil {
		logger.Pre().Error("grpc结构体解析失败：", err)
	}
	commonResp.Rows = *commonRows
	commonResp.Summary = helpers.ToStruct(modelResp.Summary)
	return commonResp, err
}
