package report

import (
	"context"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	r_model "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"time"
)

func (t *SalesReportServer) PaymentDetailReport(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.PaymentDetailReportResponse, error) {
	modelResp, err := report.QueryPaymentDetail(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("支付统计查询数据出错：", err)
		return nil, err
	}
	return t.buildResponseForPaymentDetailReport(modelResp, req)
}

func (t *SalesReportServer) buildResponseForPaymentDetailReport(modelResp *r_model.PaymentDetailResponse, req *sales_report.CommonRequest) (*sales_report.PaymentDetailReportResponse, error) {
	startTime, _ := RemoveTimeZoneAndParse(req.Start)
	endTime, _ := RemoveTimeZoneAndParse(req.Start)
	periodStartDate := startTime.Format("20060102")
	periodEndDate := endTime.Format("20060102")
	periodDateText := fmt.Sprintf("%s to %s", periodStartDate, periodEndDate)
	resp := &sales_report.PaymentDetailReportResponse{Total: modelResp.Total, PeriodStartDate: periodStartDate, PeriodEndDate: periodEndDate, PeriodDateText: periodDateText}
	rows := make([]*sales_report.PaymentDetailReportResponse_PaymentDetailReportRow, 0, len(modelResp.Rows))
	for _, r := range modelResp.Rows {
		payTime, err := time.Parse(time.RFC3339, r.PayTime)
		if err != nil {
			return nil, err
		}
		row := &sales_report.PaymentDetailReportResponse_PaymentDetailReportRow{
			BusDate:               r.BusDate,
			StoreId:               cast.ToUint64(r.StoreId),
			StoreCode:             r.StoreCode,
			StoreName:             r.StoreName,
			TicketNo:              r.TicketNo,
			PaymentId:             r.PaymentId,
			PaymentCode:           r.PaymentCode,
			PaymentName:           r.PaymentName,
			PayTime:               payTime.Format("15:04:05"),
			PayAmount:             decimal.NewFromFloat(r.PayAmount).StringFixed(2),
			PaymentTransferAmount: decimal.NewFromFloat(r.PaymentTransferAmount).StringFixed(2),
			SurChargeAmount:       decimal.NewFromFloat(r.SurChargeAmount).StringFixed(2),
			CardNo:                r.CardNo,
			PayDate:               payTime.Format("2006/01/02"),
			RealAmount:            decimal.NewFromFloat(r.RealAmount).StringFixed(2),
			PlateNo:               r.PlateNo,
			PhoneNo:               r.PhoneNo,
			OperatorCode:          r.OperatorCode,
			ServiceFee:            decimal.NewFromFloat(r.ServiceFee).StringFixed(2),
			NonSalesAmount:        decimal.NewFromFloat(r.NonSalesAmount).StringFixed(2),
		}
		rows = append(rows, row)
	}
	resp.Rows = rows
	return resp, nil
}
