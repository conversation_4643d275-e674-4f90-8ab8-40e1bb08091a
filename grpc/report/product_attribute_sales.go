package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductAttributeSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductAttributeSalesResponse, error) {
	modelResp, err := report.QueryProductAttributeSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Errorf("商品属性销售查询数据出错：%v", err)
		return nil, err
	}
	productAtrributeResp := sales_report.ProductAttributeSalesResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("商品属性销售查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productAtrributeResp)
	if err != nil {
		logger.Pre().Error("商品属性销售grpc结构体解析失败：", err)
		return nil, err
	}
	return &productAtrributeResp, err
}
