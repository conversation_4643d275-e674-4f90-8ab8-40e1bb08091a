package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 餐段
func (t *SalesReportServer) MealSegments(ctx context.Context, req *sales_report.CommonRequest) (*sales_report.MealSegmentsResponse, error) {
	modelResp, err := report.MealSegments(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("餐段查询数据出错：", err)
		return nil, err
	}
	resp := sales_report.MealSegmentsResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("餐段查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &resp)
	if err != nil {
		logger.Pre().Error("餐段grpc结构体解析失败：", err)
		return nil, err
	}
	return &resp, err
}
