package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

func (t *SalesReportServer) ShopkeeperPeriodNew(ctx context.Context, req *sales_report.CommonRequest) (*sales_report.PeriodNewResponse, error) {
	modelResp, err := shopkeeper.PeriodNew(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		return nil, err
	}
	marshal, err := json.Marshal(modelResp)
	var grpcResp = &sales_report.PeriodNewResponse{}
	err = json.Unmarshal(marshal, grpcResp)
	if err != nil {
		logger.Pre().Error("grpc结构体解析失败：", err)
	}
	return grpcResp, err
}
