package report

import (
	"context"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) UpdateTime(ctx context.Context, req *sales_report.UpdateTimeRequest) (
	*sales_report.UpdateTimeResponse, error) {

	modelResp, err := report.UpdateTime(ctx)
	if err != nil {
		return nil, err
	}
	return &sales_report.UpdateTimeResponse {
		Datetime:    modelResp.Datetime,
	}, err
}
