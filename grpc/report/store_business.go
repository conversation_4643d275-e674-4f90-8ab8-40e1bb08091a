package report

import (
	"context"
	"encoding/json"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	m "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) StoreBusiness(ctx context.Context, req *sales_report.StoreBusinessRequest) (
	*sales_report.StoreBusinessResponse, error) {
	reqModel := new(m.StoreBusinessRequest)
	bs, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(bs, reqModel)
	modelResp, err := report.StoreBusiness(ctx, reqModel)
	if err != nil {
		return nil, err
	}
	bs, err = json.Marshal(modelResp)
	if err != nil {
		return nil, err
	}
	resp := new(sales_report.StoreBusinessResponse)
	err = json.Unmarshal(bs, resp)

	return resp, err
}
