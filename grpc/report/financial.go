package report

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/services/financial"
)

// 实现grpc中门店销售报表的接口
func (t *SalesReportServer) FinancialReport(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.FinancialResponse, error) {
	// 调用门店报表的服务
	comReq := ToCommonRequest(ctx, req) // 将grpc请求转成sql条件
	modelResp, err := financial.QueryFinancialReport(ctx, comReq)
	if err != nil {
		logger.Pre().Errorf("收银汇总表查询数据出错：%v", err)
		return nil, err
	}
	financialResp := &sales_report.FinancialResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("收银汇总表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, financialResp)
	if err != nil {
		logger.Pre().Error("收银汇总表grpc结构体解析失败：", err)
		return nil, err
	}
	return financialResp, err
}
