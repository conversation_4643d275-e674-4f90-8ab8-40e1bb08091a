package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) PosPromotion(ctx context.Context, req *sales_report.CommonRequest) (*sales_report.PosPromotionResponse, error) {
	modelResp, err := report.PosPromotion(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		return nil, err
	}
	marshal, err := json.Marshal(modelResp)
	var grpcResp = &sales_report.PosPromotionResponse{}
	err = json.Unmarshal(marshal, grpcResp)
	if err != nil {
		logger.Pre().Error("grpc结构体解析失败：", err)
	}
	return grpcResp, err
}
