package report

import (
	"context"
	"encoding/base64"
	"fmt"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	_import "gitlab.hexcloud.cn/histore/sales-report/services/import"
)

func (t *SalesReportServer) ImportExchangeRates(ctx context.Context, req *sales_report.ImportRequest) (*sales_report.ImportResponse, error) {
	bs, err := base64.StdEncoding.DecodeString(req.File)
	if err != nil {
		fmt.Println("ImportExchangeRates 解码失败:", err)
		return nil, err
	}
	msg, err := _import.ImportExchangeRates(ctx, bs)
	return &sales_report.ImportResponse{Msg: msg}, err
}
