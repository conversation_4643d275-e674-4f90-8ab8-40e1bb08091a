package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

// 小掌柜.折扣分类
func (t *SalesReportServer) ShopKeeperDetail(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.DetailForDiscountAndRealAmountResponse, error) {
	// 将grpc请求转成sql请求
	modelResp, err := shopkeeper.Detail(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("小掌柜财务实收和优惠组成详细信息查询数据出错：", err)
		return nil, err
	}
	detailResp := sales_report.DetailForDiscountAndRealAmountResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜财务实收和优惠组成详细信息转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &detailResp)
	if err != nil {
		logger.Pre().Error("小掌柜财务实收和优惠组成详细信息grpc结构体解析失败：", err)
		return nil, err
	}
	return &detailResp, err
}
