package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

func (t *SalesReportServer) ShopKeeperProDistribute(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductDistributeResponse, error) {

	modelResp, err := shopkeeper.ProductDistribute(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("小掌柜商品分布查询数据出错：", err)
		return nil, err
	}
	productResp := sales_report.ProductDistributeResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品分布转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品分布grpc结构体解析失败：", err)
		return nil, err
	}
	return &productResp, err
}
