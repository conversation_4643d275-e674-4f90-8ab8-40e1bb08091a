package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

func (t *SalesReportServer) ShopKeeperProductAttribute(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductAttributeResponse, error) {

	modelResp, err := shopkeeper.ProductAttribute(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("小掌柜商品属性下钻查询数据出错：", err)
		return nil, err
	}
	productResp := sales_report.ProductAttributeResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品属性下钻转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productResp)
	if err != nil {
		logger.Pre().Error("小掌柜商品属性下钻grpc结构体解析失败：", err)
		return nil, err
	}
	return &productResp, err
}
