package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 实现grpc中门店销售报表的接口
func (t *SalesReportServer) RefundAnalysis(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.RefundAnalysisResponse, error) {
	// 调用门店报表的服务
	comReq := ToCommonRequest(ctx, req) // 将grpc请求转成sql条件
	modelResp, err := report.RefundAnalyse(ctx, comReq)
	if err != nil {
		logger.Pre().Errorf("退单原因查询数据出错:%v", err)
		return nil, err
	}
	refundResp := sales_report.RefundAnalysisResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("退单原因分析表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &refundResp)
	if err != nil {
		logger.Pre().Error("退单原因分析表grpc结构体解析失败：", err)
		return nil, err
	}
	return &refundResp, err
}
