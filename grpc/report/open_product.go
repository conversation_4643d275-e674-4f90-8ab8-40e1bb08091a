package report

import (
	"context"
	"encoding/json"
	mopen "gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/open"

	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
)

func (t *SalesReportServer) OpenProductReport(ctx context.Context, req *sales_report.OpenProductRequest) (
	*sales_report.OpenProductResponse, error) {
	bs, err := json.Marshal(req)
	if err != nil {
		logger.Pre().Error("Open商品流水查询条件转成byte数组出错：", err)
		return nil, err
	}
	modelReq := &mopen.ProductReportReq{}
	err = json.Unmarshal(bs, modelReq)
	if err != nil {
		logger.Pre().Error("Open商品流水grpc结构体解析失败：", err)
		return nil, err
	}

	modelResp, err := open.QueryOpenProductReport(ctx, modelReq)

	if err != nil {
		logger.Pre().Errorf("Open商品流水查询数据出错：%v", err)
		return nil, err
	}
	resp := &sales_report.OpenProductResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("Open商品流水查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, resp)
	if err != nil {
		logger.Pre().Error("Open商品流水grpc结构体解析失败：", err)
		return nil, err
	}
	return resp, err
}
