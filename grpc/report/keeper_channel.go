package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

// 小掌柜.渠道收入
func (t *SalesReportServer) ShopKeeperChaIncome(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ChannelDistributeResponse, error) {
	// 将grpc请求转成sql请求
	commonRequest := ToCommonRequest(ctx, req)
	modelResp, err := shopkeeper.Channel(ctx, commonRequest)
	if err != nil {
		logger.Pre().Errorf("小掌柜渠道收入查询数据出错：%v", err)
		return nil, err
	}
	channelResp := &sales_report.ChannelDistributeResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜渠道收入查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, channelResp)
	if err != nil {
		logger.Pre().Error("小掌柜渠道收入grpc结构体解析失败：", err)
		return nil, err
	}
	return channelResp, err
}
