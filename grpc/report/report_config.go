package report

import (
	"context"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

// 大掌柜报表配置查询
func (t *SalesReportServer) ShopkeeperQueryReportConfig(c context.Context, req *sales_report.QueryReportConfigRequest) (*sales_report.ReportConfigResponse, error) {
	result, err := repo.DefaultReportConfigRepo.QueryByTypeCurrentUser(c, req.Type)
	if err != nil {
		return nil, err
	}
	return &sales_report.ReportConfigResponse{
		Id:        result.Id,
		PartnerId: result.PartnerId,
		UserId:    result.UserId,
		Type:      result.Type,
		Config:    result.Config,
	}, nil
}

// 大掌柜报表配置保存
func (t *SalesReportServer) ShopkeeperSaveReportConfig(c context.Context, req *sales_report.SaveReportConfigRequest) (*sales_report.ReportConfigResponse, error) {
	result, err := repo.DefaultReportConfigRepo.SaveByTypeCurrentUser(c, req.Type, req.Config)
	if err != nil {
		return nil, err
	}
	return &sales_report.ReportConfigResponse{
		Id:        result.Id,
		PartnerId: result.PartnerId,
		UserId:    result.UserId,
		Type:      result.Type,
		Config:    result.Config,
	}, nil
}
