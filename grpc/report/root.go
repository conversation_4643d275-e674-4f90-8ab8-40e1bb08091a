package report

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"regexp"
	"time"

	"github.com/golang/protobuf/ptypes"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
)

// 继承了报表接口的结构体，具体的实现是在同级目录下的对应报表文件
// 不能给其他包里面的类型加方法
type SalesReportServer struct {
	// grpc接口
	sales_report.SalesReportServer
}

func ToDashboardRequest(req *sales_report.DashboardRequest) (q *model.DashboardRequest) {
	q = new(model.DashboardRequest)
	q.IsToday = req.IsToday
	q.IsPre = req.IsPre
	req.Start.Seconds = req.Start.Seconds + 8*60*60
	req.End.Seconds = req.End.Seconds + 8*60*60
	q.Start, _ = ptypes.Timestamp(req.Start)
	q.End, _ = ptypes.Timestamp(req.End)
	q.RegionSearchIds = req.RegionSearchIds
	q.Panel = req.Panel
	q.Lan = req.Lan
	return
}

// 将grpc请求转成sql查询公共请求
func ToCommonRequest(ctx context.Context, req *sales_report.CommonRequest) (q *model.CommonRequest) {
	q = new(model.CommonRequest)
	q.Limit = int(req.Limit)
	q.Offset = int(req.Offset)
	q.Sort = req.Sort
	q.Order = req.Order
	q.IncludeTotal = req.IncludeTotal
	q.IncludeSummary = req.IncludeSummary
	q.IsToday = req.IsToday
	q.IsPre = req.IsPre
	q.PeriodGroupType = req.PeriodGroupType
	q.Start, _ = RemoveTimeZoneAndParse(req.Start)
	q.End, _ = RemoveTimeZoneAndParse(req.End)
	q.CompareStart = req.CompareStart
	q.CompareEnd = req.CompareEnd
	q.RegionGroupType = req.RegionGroupType
	q.RegionSearchIds = req.RegionSearchIds
	q.RegionGroupLevel = cast.ToInt(req.RegionGroupLevel)
	q.ProductCategoryIds = req.ProductCategoryIds
	q.ProductIds = req.ProductIds
	q.PaymentIds = req.PaymentIds
	q.DiscountIds = req.DiscountIds
	q.ChannelIds = req.ChannelIds
	q.ChannelId = req.ChannelId
	q.Channels = req.Channels
	q.OrderType = req.OrderType
	q.Lan = req.Lan
	// 门店类型和开店类型
	q.StoreType = req.StoreType
	q.OpenStatus = req.OpenStatus
	q.TagType = req.TagType
	// 默认传过来什么就搜索什么
	q.RegionSearchIds = req.RegionSearchIds
	q.RegionSearchType = req.RegionSearchType
	// 退单原因
	q.RefundCode = req.RefundCode
	q.RefundSide = req.RefundSide
	q.Period = req.Period
	q.PriceScope = req.PriceScope
	q.IsCombo = req.IsCombo
	q.TagTypes = req.TagTypes
	q.StoreTypes = req.StoreTypes

	q.IsNatural = req.IsNatural
	q.Taxes = req.Taxes

	q.OrderStatus = req.OrderStatus
	q.ProductId = req.ProductId
	q.IsNatural = req.IsNatural
	q.Code = req.Code
	q.StoreTags = req.StoreTags
	q.IncludeRealAmountZero = req.IncludeRealAmountZero
	q.RegionCodes = req.RegionCodes
	q.CouponChannels = req.CouponChannels
	q.ComboProductIds = req.ComboProductIds
	q.Search = req.Search
	q.TicketNo = req.TicketNo
	q.Timezone = req.Timezone
	if q.Timezone == "" {
		q.Timezone = "Asia/Shanghai"
	}
	q.ProductAttributeCodes = req.ProductAttributeCodes

	var regionsStr, storeidsStr []string
	// 在ctx中要包含有用户info，否则报错
	if regions, storeids, err := entity.GetRegionAndStoreIdsByType(ctx, "branch_region"); err != nil || len(storeids) == 0 {
		logger.Pre().Warn("grpc GetRegionAndStoreIdsByType Error:", err, storeids)
		return
	} else {
		for _, item := range regions {
			regionsStr = append(regionsStr, cast.ToString(item))
		}
		for _, item := range storeids {
			storeidsStr = append(storeidsStr, cast.ToString(item))
		}
	}
	logger.Pre().Info("grpc GetRegionAndStoreIdsByType:", storeidsStr)
	// 如果传入 id 为空，给所有门店
	//if len(req.RegionSearchIds) == 0 {
	//	q.RegionSearchIds = storeidsStr
	//	return
	//}

	// 如果不是门店，需要看看权限是不是都在区域中，有一个不在，则转为门店继续判断
	//if req.RegionSearchType != "STORE" {
	//	allRegionIn := true
	//	for _, id := range req.RegionSearchIds {
	//		if !slice.StringInSlice(regionsStr, id) {
	//			allRegionIn = false
	//			break
	//		}
	//	}
	//	if allRegionIn {
	//		q.RegionSearchType = req.RegionSearchType
	//		q.RegionSearchIds = req.RegionSearchIds
	//		return
	//	}
	//	ids := make([]uint64, 0, len(req.RegionSearchIds))
	//	for _, id := range req.RegionSearchIds {
	//		ids = append(ids, cast.ToUint64(id))
	//	}
	//	req.RegionSearchIds = []string{}
	//	if storeids, err := repo.DefaultMetadataCache.GetStoresByRegionIds(ctx, ids...); err != nil || len(storeids) == 0 {
	//		return
	//	} else {
	//		for _, store := range storeids {
	//			req.RegionSearchIds = append(req.RegionSearchIds, cast.ToString(store))
	//		}
	//	}
	//}
	//
	//for _, id := range req.RegionSearchIds {
	//	if slice.StringInSlice(storeidsStr, id) {
	//		q.RegionSearchIds = append(q.RegionSearchIds, id)
	//	}
	//}

	// 如果没有查找到权限门店ID
	//if len(q.RegionSearchIds) == 0 {
	//	q.RegionSearchIds = []string{"-1"}
	//}
	return
}

// RemoveTimeZoneAndParse 将时间字符串中的时区部分去掉，并转换为 time.Time 格式
func RemoveTimeZoneAndParse(timestamp string) (time.Time, error) {
	// 使用正则表达式去掉时区部分
	re := regexp.MustCompile(`([+-]\d{2}:\d{2})$`)
	timestampNoTimeZone := re.ReplaceAllString(timestamp, "")

	// 定义时间格式
	const layout = "2006-01-02T15:04:05"

	// 解析时间字符串
	t, err := time.Parse(layout, timestampNoTimeZone)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}
