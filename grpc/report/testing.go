package report

import (
	"context"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"math"
)

func (t *SalesReportServer) Testing(ctx context.Context, req *sales_report.TestingRequest) (
	*sales_report.TestingResponse, error) {
	d, err := repo.DefaultDiffRepo.MaxCreateTimeDiff(ctx)
	var s int32
	if d.Seconds() > math.MaxInt32 {
		s = math.MaxInt32
	} else {
		s = int32(d.Seconds())
	}
	return &sales_report.TestingResponse {
		DelaySeconds:    s,
	}, err
}
