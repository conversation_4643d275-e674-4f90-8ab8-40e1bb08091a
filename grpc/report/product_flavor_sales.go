package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductFlavorSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductFlavorResponse, error) {
	modelResp, err := report.QueryProductFlavorSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Errorf("单品属性查询数据出错：%v", err)
		return nil, err
	}
	flavorResp := &sales_report.ProductFlavorResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("单品属性查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, flavorResp)
	if err != nil {
		logger.Pre().Error("单品属性grpc结构体解析失败：", err)
		return nil, err
	}
	return flavorResp, err
}
