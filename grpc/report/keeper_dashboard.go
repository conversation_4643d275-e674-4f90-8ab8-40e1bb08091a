package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

// 小掌柜.首页面板
func (t *SalesReportServer) ShopKeeperDashboard(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ShopKeeperDashBoardResponse, error) {
	// 将grpc请求转成sql请求
	commonRequest := ToCommonRequest(ctx, req)
	modelResp, err := shopkeeper.Dashboard(ctx, commonRequest)
	if err != nil {
		logger.Pre().Errorf("小掌柜首页面板查询数据出错：%v", err)
		return nil, err
	}
	dashboardResp := &sales_report.ShopKeeperDashBoardResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜首页面板查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, dashboardResp)
	if err != nil {
		logger.Pre().Error("小掌柜首页面板grpc结构体解析失败：", err)
		return nil, err
	}
	return dashboardResp, err
}
