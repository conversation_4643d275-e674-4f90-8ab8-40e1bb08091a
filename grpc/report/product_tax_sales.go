package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductTaxSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductTaxResponse, error) {
	modelResp, err := report.QueryProductTaxSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("商品计税表查询数据出错：", err)
		return nil, err
	}
	productTaxResp := sales_report.ProductTaxResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("商品计税表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productTaxResp)
	if err != nil {
		logger.Pre().Error("商品计税表grpc结构体解析失败：", err)
		return nil, err
	}
	return &productTaxResp, err
}
