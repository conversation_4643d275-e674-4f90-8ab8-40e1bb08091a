package report

import (
	"context"
	"encoding/json"
	st "github.com/golang/protobuf/ptypes/struct"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 实现grpc中门店销售报表的接口
func (t *SalesReportServer) StoreSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.CommonResponse, error) {
	// 调用门店报表的服务
	comReq := ToCommonRequest(ctx, req) // 将grpc请求转成sql条件
	modelResp, err := report.QueryStoreSales(ctx, comReq)
	if err != nil {
		return nil, err
	}
	// 组装公共响应结果
	commonResp := &sales_report.CommonResponse{Total: modelResp.Total}
	marshal, err := json.Marshal(modelResp.Rows)
	var commonRows = &[]*st.Struct{}
	err = json.Unmarshal(marshal, commonRows)
	if err != nil {
		logger.Pre().Error("grpc结构体解析失败：", err)
		return nil, err
	}
	commonResp.Rows = *commonRows
	commonResp.Summary = helpers.ToStruct(modelResp.Summary)
	return commonResp, nil
}
