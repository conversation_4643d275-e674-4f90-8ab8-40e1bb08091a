package report

import (
	"context"
	"github.com/golang/protobuf/ptypes"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	model_report "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (t *SalesReportServer) StoreCompensationSalesReport(ctx context.Context, req *sales_report.StoreCompensationSalesReportReq) (*sales_report.StoreCompensationSalesReportResp, error) {
	// 调用门店报表的服务
	// 特殊处理时间
	req.Start.Seconds = req.Start.Seconds - 8*60*60
	req.End.Seconds = req.End.Seconds - 8*60*60
	comReq := getCommonRequest(ctx, req)
	// 将grpc请求转成sql条件
	modelResp, err := report.QueryStoreCompensationSalesReport(ctx, comReq)
	if err != nil {
		logger.Pre().Errorf("非营运销售报表查询数据出错：%v", err)
		return nil, err
	}
	return t.buildStoreCompensationSalesReportResp(ctx, req, comReq, modelResp)
}

func getCommonRequest(ctx context.Context, req *sales_report.StoreCompensationSalesReportReq) (q *model.CommonRequest) {
	q = new(model.CommonRequest)
	q.Limit = int(req.Limit)
	q.Offset = int(req.Offset)
	q.Sort = req.Sort
	q.Order = req.Order
	req.Start.Seconds = req.Start.Seconds + 8*60*60
	req.End.Seconds = req.End.Seconds + 8*60*60
	q.Start, _ = ptypes.Timestamp(req.Start)
	q.End, _ = ptypes.Timestamp(req.End)
	q.RegionSearchIds = cast.ToStringSlice(req.RegionSearchIds)
	q.PaymentIds = cast.ToStringSlice(req.PaymentIds)
	q.RegionSearchType = req.RegionSearchType
	// 是否查合计，默认明细
	q.IncludeSummary = req.QueryType == sales_report.StoreCompensationSalesReportReq_query_type_header

	var regionsStr, storeidsStr []string
	// 在ctx中要包含有用户info，否则报错
	if regions, storeids, err := entity.GetRegionAndStoreIdsByType(ctx, "branch_region"); err != nil || len(storeids) == 0 {
		logger.Pre().Warn("grpc GetRegionAndStoreIdsByType Error:", err, storeids)
		return
	} else {
		for _, item := range regions {
			regionsStr = append(regionsStr, cast.ToString(item))
		}
		for _, item := range storeids {
			storeidsStr = append(storeidsStr, cast.ToString(item))
		}
	}
	logger.Pre().Info("grpc GetRegionAndStoreIdsByType:", storeidsStr)
	return
}

func (t *SalesReportServer) buildStoreCompensationSalesReportResp(ctx context.Context, req *sales_report.StoreCompensationSalesReportReq, query *model.CommonRequest, modelResp *model_report.StoreCompensationSalesReportResp) (*sales_report.StoreCompensationSalesReportResp, error) {
	resp := &sales_report.StoreCompensationSalesReportResp{
		SummaryTotal: modelResp.SummaryTotal,
		Total:        modelResp.Total,
	}
	summaryRows := make([]*sales_report.StoreCompensationSalesReportRow, 0)
	rows := make([]*sales_report.StoreCompensationSalesItemRow, 0)

	switch req.QueryType {
	default: // 默认查询纬度：summary，即订单+类别
		for _, r := range modelResp.SummaryRows {
			summaryRow := &sales_report.StoreCompensationSalesReportRow{
				BusDate:                  r.BusDate,
				StoreId:                  r.StoreId,
				StoreCode:                r.StoreCode,
				StoreName:                r.StoreName,
				TicketNo:                 r.TicketNo,
				EndTime:                  timestamppb.New(r.EndTime),
				CompensationCategoryDesc: r.CompensationCategoryDesc,
				CompensationCategoryCode: r.CompensationCategoryCode,
				CompensationCategoryId:   r.CompensationCategoryId,
				NetAmount:                cast.ToString(r.NetAmount),
				NonSalesAmount:           cast.ToString(r.NonSalesAmount),
				// 无item
			}
			summaryRows = append(summaryRows, summaryRow)
		}

	case sales_report.StoreCompensationSalesReportReq_query_type_detail: // item纬度：item
		for _, r := range modelResp.Rows {
			row := &sales_report.StoreCompensationSalesItemRow{
				BusDate:                  r.BusDate,
				StoreId:                  r.StoreId,
				StoreCode:                r.StoreCode,
				StoreName:                r.StoreName,
				TicketNo:                 r.TicketNo,
				EndTime:                  timestamppb.New(r.EndTime),
				CompensationCategoryDesc: r.CompensationCategoryDesc,
				CompensationCategoryCode: r.CompensationCategoryCode,
				CompensationCategoryId:   r.CompensationCategoryId,
				// item
				MainSeq:        r.MainSeq,
				Seq:            r.Seq,
				ParentId:       r.ParentId,
				ProductId:      r.ProductID,
				ProductCode:    r.ProductCode,
				ProductName:    r.ProductName,
				Qty:            cast.ToString(r.Qty),
				NetAmount:      cast.ToString(r.NetAmount),
				NonSalesAmount: cast.ToString(r.NonSalesAmount),
				ItemAmount:     cast.ToString(r.ItemAmount),
			}
			rows = append(rows, row)
		}
	}
	resp.SummaryRow = summaryRows
	resp.Rows = rows
	return resp, nil
}
