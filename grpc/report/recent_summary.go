package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) RecentSummary(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.RecentSummaryResponse, error) {
	modelResp, err := report.QueryRecentSummary(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("近期营业汇总查询数据出错：", err)
		return nil, err
	}
	resp := sales_report.RecentSummaryResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("近期营业汇总查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &resp)
	if err != nil {
		logger.Pre().Error("近期营业汇总grpc结构体解析失败：", err)
		return nil, err
	}
	return &resp, err
}
