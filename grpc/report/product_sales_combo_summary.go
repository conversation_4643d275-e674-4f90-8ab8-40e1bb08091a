package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductSalesComboSummary(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductSalesComboSummaryResponse, error) {
	modelResp, err := report.QueryProductSalesComboSummary(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Errorf("套餐商品销售汇总查询数据出错：%v", err)
		return nil, err
	}
	productResp := sales_report.ProductSalesComboSummaryResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("套餐商品销售汇总查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productResp)
	if err != nil {
		logger.Pre().Error("套餐商品销售汇总grpc结构体解析失败：", err)
		return nil, err
	}
	return &productResp, err
}
