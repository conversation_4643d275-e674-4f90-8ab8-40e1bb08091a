package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
)

// 小掌柜.营业额
func (t *SalesReportServer) ShopKeeperBusiness(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.BusinessSituationResponse, error) {
	// 将grpc请求转成sql请求
	commonRequest := ToCommonRequest(ctx, req)
	modelResp, err := shopkeeper.BusinessAmount(ctx, commonRequest)
	if err != nil {
		logger.Pre().Errorf("小掌柜营业概况查询数据出错：%v", err)
		return nil, err
	}
	businessResp := sales_report.BusinessSituationResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("小掌柜营业概况查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &businessResp)
	if err != nil {
		logger.Pre().Error("小掌柜营业概况grpc结构体解析失败：", err)
		return nil, err
	}
	return &businessResp, err
}
