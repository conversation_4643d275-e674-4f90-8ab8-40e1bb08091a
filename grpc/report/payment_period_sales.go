package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) PaymentPeriodSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.PaymentPeriodResponse, error) {
	modelResp, err := report.QueryPaymentPeriodSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("支付时段查询数据出错：", err)
		return nil, err
	}
	paymentPeriodResp := &sales_report.PaymentPeriodResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("支付时段查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &paymentPeriodResp)
	if err != nil {
		logger.Pre().Error("支付时段grpc结构体解析失败：", err)
		return nil, err
	}
	return paymentPeriodResp, err
}
