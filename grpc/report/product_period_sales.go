package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func (t *SalesReportServer) ProductPeriodSales(ctx context.Context, req *sales_report.CommonRequest) (
	*sales_report.ProductPeriodResponse, error) {
	modelResp, err := report.QueryProductPeriodSales(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("单品时段查询数据出错：", err)
		return nil, err
	}
	productPeriodResp := sales_report.ProductPeriodResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("单品时段查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &productPeriodResp)
	if err != nil {
		logger.Pre().Error("单品时段grpc结构体解析失败：", err)
		return nil, err
	}
	return &productPeriodResp, err
}
