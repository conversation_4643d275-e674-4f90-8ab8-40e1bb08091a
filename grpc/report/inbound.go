package report

import (
	"context"
	"encoding/json"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 报损报表
func (t *SalesReportServer) InboundReport(ctx context.Context, req *sales_report.CommonRequest) (*sales_report.InboundResponse, error) {
	modelResp, err := report.QueryInbound(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("入库报表查询数据出错：", err)
		return nil, err
	}
	resp := sales_report.InboundResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("入库报表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &resp)
	if err != nil {
		logger.Pre().Error("入库报表grpc结构体解析失败：", err)
		return nil, err
	}
	return &resp, err
}

// 报损报表
func (t *SalesReportServer) PosInboundReport(ctx context.Context, req *sales_report.CommonRequest) (*sales_report.PosInboundResponse, error) {
	modelResp, err := report.QueryPosInbound(ctx, ToCommonRequest(ctx, req))
	if err != nil {
		logger.Pre().Error("Pos入库报表查询数据出错：", err)
		return nil, err
	}
	resp := sales_report.PosInboundResponse{}
	marshal, err := json.Marshal(modelResp)
	if err != nil {
		logger.Pre().Error("Pos入库报表查询结果转成byte数组出错：", err)
		return nil, err
	}
	err = json.Unmarshal(marshal, &resp)
	if err != nil {
		logger.Pre().Error("Pos入库报表grpc结构体解析失败：", err)
		return nil, err
	}
	return &resp, err
}
