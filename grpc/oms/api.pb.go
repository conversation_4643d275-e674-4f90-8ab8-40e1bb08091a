// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.22.0
// 	protoc        v3.15.8
// source: grpc/oms/api.proto

package oms

import (
	context "context"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type RefundReasonsListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reasons map[string]*RefundReasonChannelList `protobuf:"bytes,1,rep,name=reasons,proto3" json:"reasons,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RefundReasonsListResp) Reset() {
	*x = RefundReasonsListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_oms_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundReasonsListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundReasonsListResp) ProtoMessage() {}

func (x *RefundReasonsListResp) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_oms_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundReasonsListResp.ProtoReflect.Descriptor instead.
func (*RefundReasonsListResp) Descriptor() ([]byte, []int) {
	return file_grpc_oms_api_proto_rawDescGZIP(), []int{0}
}

func (x *RefundReasonsListResp) GetReasons() map[string]*RefundReasonChannelList {
	if x != nil {
		return x.Reasons
	}
	return nil
}

type RefundReasonChannelList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rows map[string]string `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RefundReasonChannelList) Reset() {
	*x = RefundReasonChannelList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_oms_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefundReasonChannelList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundReasonChannelList) ProtoMessage() {}

func (x *RefundReasonChannelList) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_oms_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundReasonChannelList.ProtoReflect.Descriptor instead.
func (*RefundReasonChannelList) Descriptor() ([]byte, []int) {
	return file_grpc_oms_api_proto_rawDescGZIP(), []int{1}
}

func (x *RefundReasonChannelList) GetRows() map[string]string {
	if x != nil {
		return x.Rows
	}
	return nil
}

type CancelChannelRequ struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Channel string `protobuf:"bytes,1,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *CancelChannelRequ) Reset() {
	*x = CancelChannelRequ{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_oms_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelChannelRequ) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelChannelRequ) ProtoMessage() {}

func (x *CancelChannelRequ) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_oms_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelChannelRequ.ProtoReflect.Descriptor instead.
func (*CancelChannelRequ) Descriptor() ([]byte, []int) {
	return file_grpc_oms_api_proto_rawDescGZIP(), []int{2}
}

func (x *CancelChannelRequ) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

var File_grpc_oms_api_proto protoreflect.FileDescriptor

var file_grpc_oms_api_proto_rawDesc = []byte{
	0x0a, 0x12, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6f, 0x6d, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x03, 0x6f, 0x6d, 0x73, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb4, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x41, 0x0a, 0x07, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6f, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x1a, 0x58, 0x0a, 0x0c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x32, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x75,
	0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8e,
	0x01, 0x0a, 0x17, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x72, 0x6f,
	0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6f, 0x6d, 0x73, 0x2e, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x2e, 0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x04, 0x72, 0x6f, 0x77, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x52, 0x6f, 0x77, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x2d, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x32, 0x80,
	0x01, 0x0a, 0x0d, 0x4f, 0x6d, 0x73, 0x41, 0x70, 0x69, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x6f, 0x0a, 0x15, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x41, 0x50, 0x49, 0x12, 0x16, 0x2e, 0x6f, 0x6d, 0x73, 0x2e,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x1a, 0x1a, 0x2e, 0x6f, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x22, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x6f,
	0x6d, 0x73, 0x2f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x2f, 0x72, 0x65, 0x66, 0x75, 0x6e,
	0x64, 0x42, 0x10, 0x5a, 0x0e, 0x2e, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x6f, 0x6d, 0x73, 0x3b,
	0x6f, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_oms_api_proto_rawDescOnce sync.Once
	file_grpc_oms_api_proto_rawDescData = file_grpc_oms_api_proto_rawDesc
)

func file_grpc_oms_api_proto_rawDescGZIP() []byte {
	file_grpc_oms_api_proto_rawDescOnce.Do(func() {
		file_grpc_oms_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_oms_api_proto_rawDescData)
	})
	return file_grpc_oms_api_proto_rawDescData
}

var file_grpc_oms_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_grpc_oms_api_proto_goTypes = []interface{}{
	(*RefundReasonsListResp)(nil),   // 0: oms.RefundReasonsListResp
	(*RefundReasonChannelList)(nil), // 1: oms.RefundReasonChannelList
	(*CancelChannelRequ)(nil),       // 2: oms.CancelChannelRequ
	nil,                             // 3: oms.RefundReasonsListResp.ReasonsEntry
	nil,                             // 4: oms.RefundReasonChannelList.RowsEntry
}
var file_grpc_oms_api_proto_depIdxs = []int32{
	3, // 0: oms.RefundReasonsListResp.reasons:type_name -> oms.RefundReasonsListResp.ReasonsEntry
	4, // 1: oms.RefundReasonChannelList.rows:type_name -> oms.RefundReasonChannelList.RowsEntry
	1, // 2: oms.RefundReasonsListResp.ReasonsEntry.value:type_name -> oms.RefundReasonChannelList
	2, // 3: oms.OmsApiService.RefundOrderReasonsAPI:input_type -> oms.CancelChannelRequ
	0, // 4: oms.OmsApiService.RefundOrderReasonsAPI:output_type -> oms.RefundReasonsListResp
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_grpc_oms_api_proto_init() }
func file_grpc_oms_api_proto_init() {
	if File_grpc_oms_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_oms_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundReasonsListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_oms_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefundReasonChannelList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_oms_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelChannelRequ); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_oms_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_oms_api_proto_goTypes,
		DependencyIndexes: file_grpc_oms_api_proto_depIdxs,
		MessageInfos:      file_grpc_oms_api_proto_msgTypes,
	}.Build()
	File_grpc_oms_api_proto = out.File
	file_grpc_oms_api_proto_rawDesc = nil
	file_grpc_oms_api_proto_goTypes = nil
	file_grpc_oms_api_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// OmsApiServiceClient is the client API for OmsApiService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OmsApiServiceClient interface {
	//查询订单退款理由
	RefundOrderReasonsAPI(ctx context.Context, in *CancelChannelRequ, opts ...grpc.CallOption) (*RefundReasonsListResp, error)
}

type omsApiServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOmsApiServiceClient(cc grpc.ClientConnInterface) OmsApiServiceClient {
	return &omsApiServiceClient{cc}
}

func (c *omsApiServiceClient) RefundOrderReasonsAPI(ctx context.Context, in *CancelChannelRequ, opts ...grpc.CallOption) (*RefundReasonsListResp, error) {
	out := new(RefundReasonsListResp)
	err := c.cc.Invoke(ctx, "/oms.OmsApiService/RefundOrderReasonsAPI", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OmsApiServiceServer is the server API for OmsApiService service.
type OmsApiServiceServer interface {
	//查询订单退款理由
	RefundOrderReasonsAPI(context.Context, *CancelChannelRequ) (*RefundReasonsListResp, error)
}

// UnimplementedOmsApiServiceServer can be embedded to have forward compatible implementations.
type UnimplementedOmsApiServiceServer struct {
}

func (*UnimplementedOmsApiServiceServer) RefundOrderReasonsAPI(context.Context, *CancelChannelRequ) (*RefundReasonsListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundOrderReasonsAPI not implemented")
}

func RegisterOmsApiServiceServer(s *grpc.Server, srv OmsApiServiceServer) {
	s.RegisterService(&_OmsApiService_serviceDesc, srv)
}

func _OmsApiService_RefundOrderReasonsAPI_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelChannelRequ)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OmsApiServiceServer).RefundOrderReasonsAPI(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/oms.OmsApiService/RefundOrderReasonsAPI",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OmsApiServiceServer).RefundOrderReasonsAPI(ctx, req.(*CancelChannelRequ))
	}
	return interceptor(ctx, in, info, handler)
}

var _OmsApiService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "oms.OmsApiService",
	HandlerType: (*OmsApiServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RefundOrderReasonsAPI",
			Handler:    _OmsApiService_RefundOrderReasonsAPI_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "grpc/oms/api.proto",
}
