syntax = "proto3";

import "google/api/annotations.proto";

package oms;

// proto3需要增加的选项，(生成的go文件存储路径;生成的包名称)
option go_package = "./grpc/oms;oms";

service OmsApiService {
  //查询订单退款理由
  rpc RefundOrderReasonsAPI (CancelChannelRequ) returns (RefundReasonsListResp){
    option (google.api.http) = {
      get: "/api/v2/oms/reasons/refund"
    };
  }
}

message RefundReasonsListResp{
  map<string,RefundReasonChannelList> reasons = 1;
}

message RefundReasonChannelList{
  map<string,string> rows = 1;
}

message CancelChannelRequ{
  string channel = 1;
}

