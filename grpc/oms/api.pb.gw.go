// Code generated by protoc-gen-grpc-gateway. DO NOT EDIT.
// source: grpc/oms/api.proto

/*
Package oms is a reverse proxy.

It translates gRPC into RESTful JSON APIs.
*/
package oms

import (
	"context"
	"io"
	"net/http"

	"github.com/golang/protobuf/descriptor"
	"github.com/golang/protobuf/proto"
	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	"github.com/grpc-ecosystem/grpc-gateway/utilities"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

// Suppress "imported and not used" errors
var _ codes.Code
var _ io.Reader
var _ status.Status
var _ = runtime.String
var _ = utilities.NewDoubleArray
var _ = descriptor.ForMessage
var _ = metadata.Join

var (
	filter_OmsApiService_RefundOrderReasonsAPI_0 = &utilities.DoubleArray{Encoding: map[string]int{}, Base: []int(nil), Check: []int(nil)}
)

func request_OmsApiService_RefundOrderReasonsAPI_0(ctx context.Context, marshaler runtime.Marshaler, client OmsApiServiceClient, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq CancelChannelRequ
	var metadata runtime.ServerMetadata

	if err := req.ParseForm(); err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}
	if err := runtime.PopulateQueryParameters(&protoReq, req.Form, filter_OmsApiService_RefundOrderReasonsAPI_0); err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := client.RefundOrderReasonsAPI(ctx, &protoReq, grpc.Header(&metadata.HeaderMD), grpc.Trailer(&metadata.TrailerMD))
	return msg, metadata, err

}

func local_request_OmsApiService_RefundOrderReasonsAPI_0(ctx context.Context, marshaler runtime.Marshaler, server OmsApiServiceServer, req *http.Request, pathParams map[string]string) (proto.Message, runtime.ServerMetadata, error) {
	var protoReq CancelChannelRequ
	var metadata runtime.ServerMetadata

	if err := req.ParseForm(); err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}
	if err := runtime.PopulateQueryParameters(&protoReq, req.Form, filter_OmsApiService_RefundOrderReasonsAPI_0); err != nil {
		return nil, metadata, status.Errorf(codes.InvalidArgument, "%v", err)
	}

	msg, err := server.RefundOrderReasonsAPI(ctx, &protoReq)
	return msg, metadata, err

}

// RegisterOmsApiServiceHandlerServer registers the http handlers for service OmsApiService to "mux".
// UnaryRPC     :call OmsApiServiceServer directly.
// StreamingRPC :currently unsupported pending https://github.com/grpc/grpc-go/issues/906.
// Note that using this registration option will cause many gRPC library features to stop working. Consider using RegisterOmsApiServiceHandlerFromEndpoint instead.
func RegisterOmsApiServiceHandlerServer(ctx context.Context, mux *runtime.ServeMux, server OmsApiServiceServer) error {

	mux.Handle("GET", pattern_OmsApiService_RefundOrderReasonsAPI_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		var stream runtime.ServerTransportStream
		ctx = grpc.NewContextWithServerTransportStream(ctx, &stream)
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		rctx, err := runtime.AnnotateIncomingContext(ctx, mux, req)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := local_request_OmsApiService_RefundOrderReasonsAPI_0(rctx, inboundMarshaler, server, req, pathParams)
		md.HeaderMD, md.TrailerMD = metadata.Join(md.HeaderMD, stream.Header()), metadata.Join(md.TrailerMD, stream.Trailer())
		ctx = runtime.NewServerMetadataContext(ctx, md)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_OmsApiService_RefundOrderReasonsAPI_0(ctx, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

// RegisterOmsApiServiceHandlerFromEndpoint is same as RegisterOmsApiServiceHandler but
// automatically dials to "endpoint" and closes the connection when "ctx" gets done.
func RegisterOmsApiServiceHandlerFromEndpoint(ctx context.Context, mux *runtime.ServeMux, endpoint string, opts []grpc.DialOption) (err error) {
	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			if cerr := conn.Close(); cerr != nil {
				grpclog.Infof("Failed to close conn to %s: %v", endpoint, cerr)
			}
			return
		}
		go func() {
			<-ctx.Done()
			if cerr := conn.Close(); cerr != nil {
				grpclog.Infof("Failed to close conn to %s: %v", endpoint, cerr)
			}
		}()
	}()

	return RegisterOmsApiServiceHandler(ctx, mux, conn)
}

// RegisterOmsApiServiceHandler registers the http handlers for service OmsApiService to "mux".
// The handlers forward requests to the grpc endpoint over "conn".
func RegisterOmsApiServiceHandler(ctx context.Context, mux *runtime.ServeMux, conn *grpc.ClientConn) error {
	return RegisterOmsApiServiceHandlerClient(ctx, mux, NewOmsApiServiceClient(conn))
}

// RegisterOmsApiServiceHandlerClient registers the http handlers for service OmsApiService
// to "mux". The handlers forward requests to the grpc endpoint over the given implementation of "OmsApiServiceClient".
// Note: the gRPC framework executes interceptors within the gRPC handler. If the passed in "OmsApiServiceClient"
// doesn't go through the normal gRPC flow (creating a gRPC client etc.) then it will be up to the passed in
// "OmsApiServiceClient" to call the correct interceptors.
func RegisterOmsApiServiceHandlerClient(ctx context.Context, mux *runtime.ServeMux, client OmsApiServiceClient) error {

	mux.Handle("GET", pattern_OmsApiService_RefundOrderReasonsAPI_0, func(w http.ResponseWriter, req *http.Request, pathParams map[string]string) {
		ctx, cancel := context.WithCancel(req.Context())
		defer cancel()
		inboundMarshaler, outboundMarshaler := runtime.MarshalerForRequest(mux, req)
		rctx, err := runtime.AnnotateContext(ctx, mux, req)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}
		resp, md, err := request_OmsApiService_RefundOrderReasonsAPI_0(rctx, inboundMarshaler, client, req, pathParams)
		ctx = runtime.NewServerMetadataContext(ctx, md)
		if err != nil {
			runtime.HTTPError(ctx, mux, outboundMarshaler, w, req, err)
			return
		}

		forward_OmsApiService_RefundOrderReasonsAPI_0(ctx, mux, outboundMarshaler, w, req, resp, mux.GetForwardResponseOptions()...)

	})

	return nil
}

var (
	pattern_OmsApiService_RefundOrderReasonsAPI_0 = runtime.MustPattern(runtime.NewPattern(1, []int{2, 0, 2, 1, 2, 2, 2, 3, 2, 4}, []string{"api", "v2", "oms", "reasons", "refund"}, "", runtime.AssumeColonVerbOpt(true)))
)

var (
	forward_OmsApiService_RefundOrderReasonsAPI_0 = runtime.ForwardResponseMessage
)
