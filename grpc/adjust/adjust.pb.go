// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.19.4
// source: grpc/adjust/adjust.proto

package adjust

import (
	context "context"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Pong struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *Pong) Reset() {
	*x = Pong{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Pong) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pong) ProtoMessage() {}

func (x *Pong) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pong.ProtoReflect.Descriptor instead.
func (*Pong) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{0}
}

func (x *Pong) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type Adjust struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                     uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AdjustOrderNumber      uint64                 `protobuf:"varint,2,opt,name=adjust_order_number,json=adjustOrderNumber,proto3" json:"adjust_order_number,omitempty"`
	AdjustStore            uint64                 `protobuf:"varint,3,opt,name=adjust_store,json=adjustStore,proto3" json:"adjust_store,omitempty"`
	AdjustStoreSecondaryId string                 `protobuf:"bytes,4,opt,name=adjust_store_secondary_id,json=adjustStoreSecondaryId,proto3" json:"adjust_store_secondary_id,omitempty"`
	Code                   string                 `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`
	PartnerId              uint64                 `protobuf:"varint,6,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ProcessStatus          string                 `protobuf:"bytes,7,opt,name=process_status,json=processStatus,proto3" json:"process_status,omitempty"`
	ReasonType             string                 `protobuf:"bytes,8,opt,name=reason_type,json=reasonType,proto3" json:"reason_type,omitempty"`
	Remark                 string                 `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark,omitempty"`
	Status                 string                 `protobuf:"bytes,10,opt,name=status,proto3" json:"status,omitempty"`
	AdjustDate             *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=adjust_date,json=adjustDate,proto3" json:"adjust_date,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedBy              uint64                 `protobuf:"varint,14,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	UpdatedBy              uint64                 `protobuf:"varint,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	UserId                 uint64                 `protobuf:"varint,16,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BranchBatchId          uint64                 `protobuf:"varint,17,opt,name=branch_batch_id,json=branchBatchId,proto3" json:"branch_batch_id,omitempty"`
	ScheduleCode           string                 `protobuf:"bytes,18,opt,name=schedule_code,json=scheduleCode,proto3" json:"schedule_code,omitempty"`
	ScheduleId             uint64                 `protobuf:"varint,19,opt,name=schedule_id,json=scheduleId,proto3" json:"schedule_id,omitempty"`
	RequestId              uint64                 `protobuf:"varint,20,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CreatedName            string                 `protobuf:"bytes,21,opt,name=created_name,json=createdName,proto3" json:"created_name,omitempty"`
	UpdatedName            string                 `protobuf:"bytes,22,opt,name=updated_name,json=updatedName,proto3" json:"updated_name,omitempty"`
	ReceiveId              uint64                 `protobuf:"varint,25,opt,name=receive_id,json=receiveId,proto3" json:"receive_id,omitempty"`
	ReceiveCode            string                 `protobuf:"bytes,26,opt,name=receive_code,json=receiveCode,proto3" json:"receive_code,omitempty"`
	ScheduleName           string                 `protobuf:"bytes,28,opt,name=schedule_name,json=scheduleName,proto3" json:"schedule_name,omitempty"`
	BranchType             string                 `protobuf:"bytes,30,opt,name=branch_type,json=branchType,proto3" json:"branch_type,omitempty"` // STORE, WAREHOUSE
	// 附件
	Attachments []*Attachments `protobuf:"bytes,31,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// 单据来源
	// "POS_ADJUST"      # POS端报废
	// "MANUAL_CREATED"  # 手动新建
	// "PLAN_CREATED"    # 报废计划创建
	Source     string `protobuf:"bytes,32,opt,name=source,proto3" json:"source,omitempty"`
	ReasonName string `protobuf:"bytes,33,opt,name=reason_name,json=reasonName,proto3" json:"reason_name,omitempty"`
	// 驳回原因
	RejectReason     string  `protobuf:"bytes,36,opt,name=reject_reason,json=rejectReason,proto3" json:"reject_reason,omitempty"`
	TotalAmount      float64 `protobuf:"fixed64,38,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`                  // 含税金额总数
	TotalSalesAmount float64 `protobuf:"fixed64,39,opt,name=total_sales_amount,json=totalSalesAmount,proto3" json:"total_sales_amount,omitempty"` // 含税零售金额总数
}

func (x *Adjust) Reset() {
	*x = Adjust{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Adjust) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Adjust) ProtoMessage() {}

func (x *Adjust) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Adjust.ProtoReflect.Descriptor instead.
func (*Adjust) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{1}
}

func (x *Adjust) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Adjust) GetAdjustOrderNumber() uint64 {
	if x != nil {
		return x.AdjustOrderNumber
	}
	return 0
}

func (x *Adjust) GetAdjustStore() uint64 {
	if x != nil {
		return x.AdjustStore
	}
	return 0
}

func (x *Adjust) GetAdjustStoreSecondaryId() string {
	if x != nil {
		return x.AdjustStoreSecondaryId
	}
	return ""
}

func (x *Adjust) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Adjust) GetPartnerId() uint64 {
	if x != nil {
		return x.PartnerId
	}
	return 0
}

func (x *Adjust) GetProcessStatus() string {
	if x != nil {
		return x.ProcessStatus
	}
	return ""
}

func (x *Adjust) GetReasonType() string {
	if x != nil {
		return x.ReasonType
	}
	return ""
}

func (x *Adjust) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Adjust) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Adjust) GetAdjustDate() *timestamppb.Timestamp {
	if x != nil {
		return x.AdjustDate
	}
	return nil
}

func (x *Adjust) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Adjust) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Adjust) GetCreatedBy() uint64 {
	if x != nil {
		return x.CreatedBy
	}
	return 0
}

func (x *Adjust) GetUpdatedBy() uint64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *Adjust) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Adjust) GetBranchBatchId() uint64 {
	if x != nil {
		return x.BranchBatchId
	}
	return 0
}

func (x *Adjust) GetScheduleCode() string {
	if x != nil {
		return x.ScheduleCode
	}
	return ""
}

func (x *Adjust) GetScheduleId() uint64 {
	if x != nil {
		return x.ScheduleId
	}
	return 0
}

func (x *Adjust) GetRequestId() uint64 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *Adjust) GetCreatedName() string {
	if x != nil {
		return x.CreatedName
	}
	return ""
}

func (x *Adjust) GetUpdatedName() string {
	if x != nil {
		return x.UpdatedName
	}
	return ""
}

func (x *Adjust) GetReceiveId() uint64 {
	if x != nil {
		return x.ReceiveId
	}
	return 0
}

func (x *Adjust) GetReceiveCode() string {
	if x != nil {
		return x.ReceiveCode
	}
	return ""
}

func (x *Adjust) GetScheduleName() string {
	if x != nil {
		return x.ScheduleName
	}
	return ""
}

func (x *Adjust) GetBranchType() string {
	if x != nil {
		return x.BranchType
	}
	return ""
}

func (x *Adjust) GetAttachments() []*Attachments {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *Adjust) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Adjust) GetReasonName() string {
	if x != nil {
		return x.ReasonName
	}
	return ""
}

func (x *Adjust) GetRejectReason() string {
	if x != nil {
		return x.RejectReason
	}
	return ""
}

func (x *Adjust) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *Adjust) GetTotalSalesAmount() float64 {
	if x != nil {
		return x.TotalSalesAmount
	}
	return 0
}

type Attachments struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	Url  string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *Attachments) Reset() {
	*x = Attachments{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attachments) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachments) ProtoMessage() {}

func (x *Attachments) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachments.ProtoReflect.Descriptor instead.
func (*Attachments) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{2}
}

func (x *Attachments) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Attachments) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CreatedAdjustProductByCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ProductCode string       `protobuf:"bytes,2,opt,name=product_code,json=productCode,proto3" json:"product_code,omitempty"`
	UnitCode    string       `protobuf:"bytes,3,opt,name=unit_code,json=unitCode,proto3" json:"unit_code,omitempty"`
	Quantity    float64      `protobuf:"fixed64,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	ReasonType  string       `protobuf:"bytes,5,opt,name=reason_type,json=reasonType,proto3" json:"reason_type,omitempty"`
	SkuRemark   []*SkuRemark `protobuf:"bytes,6,rep,name=skuRemark,proto3" json:"skuRemark,omitempty"`
}

func (x *CreatedAdjustProductByCode) Reset() {
	*x = CreatedAdjustProductByCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatedAdjustProductByCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatedAdjustProductByCode) ProtoMessage() {}

func (x *CreatedAdjustProductByCode) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatedAdjustProductByCode.ProtoReflect.Descriptor instead.
func (*CreatedAdjustProductByCode) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{3}
}

func (x *CreatedAdjustProductByCode) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreatedAdjustProductByCode) GetProductCode() string {
	if x != nil {
		return x.ProductCode
	}
	return ""
}

func (x *CreatedAdjustProductByCode) GetUnitCode() string {
	if x != nil {
		return x.UnitCode
	}
	return ""
}

func (x *CreatedAdjustProductByCode) GetQuantity() float64 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CreatedAdjustProductByCode) GetReasonType() string {
	if x != nil {
		return x.ReasonType
	}
	return ""
}

func (x *CreatedAdjustProductByCode) GetSkuRemark() []*SkuRemark {
	if x != nil {
		return x.SkuRemark
	}
	return nil
}

type SkuRemark struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   *SkuRemark_Tag `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Values *SkuRemark_Tag `protobuf:"bytes,2,opt,name=values,proto3" json:"values,omitempty"`
}

func (x *SkuRemark) Reset() {
	*x = SkuRemark{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkuRemark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkuRemark) ProtoMessage() {}

func (x *SkuRemark) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkuRemark.ProtoReflect.Descriptor instead.
func (*SkuRemark) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{4}
}

func (x *SkuRemark) GetName() *SkuRemark_Tag {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *SkuRemark) GetValues() *SkuRemark_Tag {
	if x != nil {
		return x.Values
	}
	return nil
}

type CreatedAdjustByCodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AdjustStore string                        `protobuf:"bytes,1,opt,name=adjust_store,json=adjustStore,proto3" json:"adjust_store,omitempty"`
	Products    []*CreatedAdjustProductByCode `protobuf:"bytes,2,rep,name=products,proto3" json:"products,omitempty"`
	ReasonType  string                        `protobuf:"bytes,3,opt,name=reason_type,json=reasonType,proto3" json:"reason_type,omitempty"`
	Remark      string                        `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	RequestId   string                        `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	AdjustDate  string                        `protobuf:"bytes,6,opt,name=adjust_date,json=adjustDate,proto3" json:"adjust_date,omitempty"`
	Lan         string                        `protobuf:"bytes,7,opt,name=lan,proto3" json:"lan,omitempty"`
}

func (x *CreatedAdjustByCodeRequest) Reset() {
	*x = CreatedAdjustByCodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatedAdjustByCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatedAdjustByCodeRequest) ProtoMessage() {}

func (x *CreatedAdjustByCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatedAdjustByCodeRequest.ProtoReflect.Descriptor instead.
func (*CreatedAdjustByCodeRequest) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{5}
}

func (x *CreatedAdjustByCodeRequest) GetAdjustStore() string {
	if x != nil {
		return x.AdjustStore
	}
	return ""
}

func (x *CreatedAdjustByCodeRequest) GetProducts() []*CreatedAdjustProductByCode {
	if x != nil {
		return x.Products
	}
	return nil
}

func (x *CreatedAdjustByCodeRequest) GetReasonType() string {
	if x != nil {
		return x.ReasonType
	}
	return ""
}

func (x *CreatedAdjustByCodeRequest) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreatedAdjustByCodeRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *CreatedAdjustByCodeRequest) GetAdjustDate() string {
	if x != nil {
		return x.AdjustDate
	}
	return ""
}

func (x *CreatedAdjustByCodeRequest) GetLan() string {
	if x != nil {
		return x.Lan
	}
	return ""
}

type SkuRemark_Tag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *SkuRemark_Tag) Reset() {
	*x = SkuRemark_Tag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_adjust_adjust_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkuRemark_Tag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkuRemark_Tag) ProtoMessage() {}

func (x *SkuRemark_Tag) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_adjust_adjust_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkuRemark_Tag.ProtoReflect.Descriptor instead.
func (*SkuRemark_Tag) Descriptor() ([]byte, []int) {
	return file_grpc_adjust_adjust_proto_rawDescGZIP(), []int{4, 0}
}

func (x *SkuRemark_Tag) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SkuRemark_Tag) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *SkuRemark_Tag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_grpc_adjust_adjust_proto protoreflect.FileDescriptor

var file_grpc_adjust_adjust_proto_rawDesc = []byte{
	0x0a, 0x18, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x2f, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x18, 0x0a, 0x04, 0x50, 0x6f, 0x6e, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x9c, 0x09, 0x0a, 0x06,
	0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x11, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x39, 0x0a, 0x19, 0x61, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x61, 0x72, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61,
	0x72, 0x79, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x70, 0x61,
	0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3b, 0x0a, 0x0b, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x62, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x5f, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0d, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x72, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65,
	0x6e, 0x74, 0x73, 0x18, 0x1f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x64, 0x6a, 0x75,
	0x73, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x0b,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x61, 0x6c, 0x65, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x27, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x61, 0x6c, 0x65, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x33, 0x0a, 0x0b, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22,
	0xda, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x6e, 0x69, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x09, 0x73,
	0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x2e, 0x53, 0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72,
	0x6b, 0x52, 0x09, 0x73, 0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x22, 0xa4, 0x01, 0x0a,
	0x09, 0x53, 0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x29, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x2e, 0x53, 0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x2e, 0x53,
	0x6b, 0x75, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x2e, 0x54, 0x61, 0x67, 0x52, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x8a, 0x02, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x50, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x61, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6c, 0x61, 0x6e,
	0x32, 0x79, 0x0a, 0x06, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x12, 0x6f, 0x0a, 0x13, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x22, 0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x42, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0e, 0x2e, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x2e, 0x41,
	0x64, 0x6a, 0x75, 0x73, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x32, 0x2f, 0x73, 0x75, 0x70, 0x70, 0x6c, 0x79, 0x2f, 0x61, 0x64,
	0x6a, 0x75, 0x73, 0x74, 0x2f, 0x70, 0x6f, 0x73, 0x3a, 0x01, 0x2a, 0x42, 0x16, 0x5a, 0x14, 0x2e,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74, 0x3b, 0x61, 0x64, 0x6a,
	0x75, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_adjust_adjust_proto_rawDescOnce sync.Once
	file_grpc_adjust_adjust_proto_rawDescData = file_grpc_adjust_adjust_proto_rawDesc
)

func file_grpc_adjust_adjust_proto_rawDescGZIP() []byte {
	file_grpc_adjust_adjust_proto_rawDescOnce.Do(func() {
		file_grpc_adjust_adjust_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_adjust_adjust_proto_rawDescData)
	})
	return file_grpc_adjust_adjust_proto_rawDescData
}

var file_grpc_adjust_adjust_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_grpc_adjust_adjust_proto_goTypes = []interface{}{
	(*Pong)(nil),                       // 0: adjust.Pong
	(*Adjust)(nil),                     // 1: adjust.Adjust
	(*Attachments)(nil),                // 2: adjust.Attachments
	(*CreatedAdjustProductByCode)(nil), // 3: adjust.CreatedAdjustProductByCode
	(*SkuRemark)(nil),                  // 4: adjust.SkuRemark
	(*CreatedAdjustByCodeRequest)(nil), // 5: adjust.CreatedAdjustByCodeRequest
	(*SkuRemark_Tag)(nil),              // 6: adjust.SkuRemark.Tag
	(*timestamppb.Timestamp)(nil),      // 7: google.protobuf.Timestamp
}
var file_grpc_adjust_adjust_proto_depIdxs = []int32{
	7, // 0: adjust.Adjust.adjust_date:type_name -> google.protobuf.Timestamp
	7, // 1: adjust.Adjust.created_at:type_name -> google.protobuf.Timestamp
	7, // 2: adjust.Adjust.updated_at:type_name -> google.protobuf.Timestamp
	2, // 3: adjust.Adjust.attachments:type_name -> adjust.Attachments
	4, // 4: adjust.CreatedAdjustProductByCode.skuRemark:type_name -> adjust.SkuRemark
	6, // 5: adjust.SkuRemark.name:type_name -> adjust.SkuRemark.Tag
	6, // 6: adjust.SkuRemark.values:type_name -> adjust.SkuRemark.Tag
	3, // 7: adjust.CreatedAdjustByCodeRequest.products:type_name -> adjust.CreatedAdjustProductByCode
	5, // 8: adjust.adjust.CreatedAdjustByCode:input_type -> adjust.CreatedAdjustByCodeRequest
	1, // 9: adjust.adjust.CreatedAdjustByCode:output_type -> adjust.Adjust
	9, // [9:10] is the sub-list for method output_type
	8, // [8:9] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_grpc_adjust_adjust_proto_init() }
func file_grpc_adjust_adjust_proto_init() {
	if File_grpc_adjust_adjust_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_adjust_adjust_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Pong); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Adjust); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attachments); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatedAdjustProductByCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkuRemark); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatedAdjustByCodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_adjust_adjust_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkuRemark_Tag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_adjust_adjust_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_adjust_adjust_proto_goTypes,
		DependencyIndexes: file_grpc_adjust_adjust_proto_depIdxs,
		MessageInfos:      file_grpc_adjust_adjust_proto_msgTypes,
	}.Build()
	File_grpc_adjust_adjust_proto = out.File
	file_grpc_adjust_adjust_proto_rawDesc = nil
	file_grpc_adjust_adjust_proto_goTypes = nil
	file_grpc_adjust_adjust_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// AdjustClient is the client API for Adjust service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdjustClient interface {
	// 第三方根据code创建报废
	CreatedAdjustByCode(ctx context.Context, in *CreatedAdjustByCodeRequest, opts ...grpc.CallOption) (*Adjust, error)
}

type adjustClient struct {
	cc grpc.ClientConnInterface
}

func NewAdjustClient(cc grpc.ClientConnInterface) AdjustClient {
	return &adjustClient{cc}
}

func (c *adjustClient) CreatedAdjustByCode(ctx context.Context, in *CreatedAdjustByCodeRequest, opts ...grpc.CallOption) (*Adjust, error) {
	out := new(Adjust)
	err := c.cc.Invoke(ctx, "/adjust.adjust/CreatedAdjustByCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdjustServer is the server API for Adjust service.
type AdjustServer interface {
	// 第三方根据code创建报废
	CreatedAdjustByCode(context.Context, *CreatedAdjustByCodeRequest) (*Adjust, error)
}

// UnimplementedAdjustServer can be embedded to have forward compatible implementations.
type UnimplementedAdjustServer struct {
}

func (*UnimplementedAdjustServer) CreatedAdjustByCode(context.Context, *CreatedAdjustByCodeRequest) (*Adjust, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatedAdjustByCode not implemented")
}

func RegisterAdjustServer(s *grpc.Server, srv AdjustServer) {
	s.RegisterService(&_Adjust_serviceDesc, srv)
}

func _Adjust_CreatedAdjustByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatedAdjustByCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdjustServer).CreatedAdjustByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/adjust.adjust/CreatedAdjustByCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdjustServer).CreatedAdjustByCode(ctx, req.(*CreatedAdjustByCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Adjust_serviceDesc = grpc.ServiceDesc{
	ServiceName: "adjust.adjust",
	HandlerType: (*AdjustServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatedAdjustByCode",
			Handler:    _Adjust_CreatedAdjustByCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "grpc/adjust/adjust.proto",
}
