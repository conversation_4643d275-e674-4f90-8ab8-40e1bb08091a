syntax = "proto3";

package adjust;
option go_package = "./grpc/adjust;adjust";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

//Adjust 每日损耗服务
service adjust {
  // 第三方根据code创建报废
  rpc CreatedAdjustByCode (CreatedAdjustByCodeRequest) returns (Adjust) {
    option (google.api.http) = {post:"/api/v2/supply/adjust/pos" body: "*"};
  }
}
message Pong {
  string msg = 1;
}
message Adjust {
  uint64 id = 1;
  uint64 adjust_order_number = 2;
  uint64 adjust_store = 3;
  string adjust_store_secondary_id = 4;
  string code = 5;
  uint64 partner_id = 6;
  string process_status = 7;
  string reason_type = 8;
  string remark = 9;
  string status = 10;
  google.protobuf.Timestamp adjust_date = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  uint64 created_by = 14;
  uint64 updated_by = 15;
  uint64 user_id = 16;
  uint64 branch_batch_id = 17;
  string schedule_code = 18;
  uint64 schedule_id = 19;
  uint64 request_id = 20;
  string created_name = 21;
  string updated_name = 22;
  uint64 receive_id = 25;
  string receive_code = 26;
  string schedule_name = 28;
  string branch_type = 30; // STORE, WAREHOUSE
  // 附件
  repeated Attachments attachments = 31;
  // 单据来源
  // "POS_ADJUST"      # POS端报废
  // "MANUAL_CREATED"  # 手动新建
  // "PLAN_CREATED"    # 报废计划创建
  string source = 32;
  string reason_name = 33;
  // 驳回原因
  string reject_reason = 36;
  double total_amount = 38; // 含税金额总数
  double total_sales_amount = 39; // 含税零售金额总数
}
message Attachments {
  string type = 1;
  string url = 2;
}


message CreatedAdjustProductByCode {
  uint64 id = 1;
  string product_code = 2;
  string unit_code = 3;
  double quantity = 4;
  string reason_type = 5;
  repeated SkuRemark skuRemark = 6;
}
message SkuRemark {
  message Tag {
    uint64 id = 1;
    string code = 2;
    string name = 3;
  }
  Tag name = 1;
  Tag values = 2;
}
message CreatedAdjustByCodeRequest {
  string adjust_store = 1;
  repeated CreatedAdjustProductByCode products = 2;
  string reason_type = 3;
  string remark = 4;
  string request_id = 5;
  string adjust_date = 6;
  string lan = 7;
}
