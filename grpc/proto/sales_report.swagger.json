{"swagger": "2.0", "info": {"title": "grpc/proto/sales_report.proto", "version": "version not set"}, "tags": [{"name": "SalesReport"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/finance/report/business/status": {"post": {"summary": "财务报表.营业状况", "operationId": "SalesReport_FinanceBusinessStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/channel/form": {"post": {"summary": "财务报表.外卖渠道", "operationId": "SalesReport_FinanceChannelForm", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/discount": {"post": {"summary": "财务报表.优惠组成", "operationId": "SalesReport_FinanceDiscount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/logistics/form": {"post": {"summary": "财务报表.物流构成", "operationId": "SalesReport_FinanceLogisticsForm", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/net/receipts": {"post": {"summary": "财务报表.实收组成", "operationId": "SalesReport_FinanceNetReceipts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/petty-cash-daily-settlement": {"post": {"summary": "财务报表报表-零用现金日结报表", "operationId": "SalesReport_PettyCashDailySettlementReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPettyCashDailySettlementReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/finance/report/product": {"post": {"summary": "财务报表.菜品收入", "operationId": "SalesReport_FinanceProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/departmentBusinessReport": {"post": {"summary": "pos部门营业报表", "operationId": "SalesReport_PosDepartmentBusinessReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPosDepartmentBusinessResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getBusinessReport": {"post": {"summary": "pos营业报表", "operationId": "SalesReport_PosBusinessReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportBusinessReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getCategoryReport": {"post": {"summary": "pos类别报表", "operationId": "SalesReport_PosCategoryReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCategoryReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getDailyInfo": {"post": {"summary": "posr日结报表", "operationId": "SalesReport_PosDailyReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportDailyResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportDailyRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getOrderType": {"post": {"summary": "pos 就餐方式", "operationId": "SalesReport_PosOrderTypeReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportOrderTypeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportOrderTypeRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getPeriodReport": {"post": {"summary": "pos时段报表", "operationId": "SalesReport_PosHourReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportHourReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getProductReport": {"post": {"summary": "pos商品报表", "operationId": "SalesReport_PosProductReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/getShiftInfo": {"post": {"summary": "pos交班报表", "operationId": "SalesReport_PosShiftReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportShiftInfoResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportShiftReportRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/productSalesReport": {"post": {"summary": "pos商品销售报表(新)", "operationId": "SalesReport_PosProductSalesReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPosProductSalesReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/hub/report/storePeriodReport": {"post": {"summary": "pos时段报表(新)", "operationId": "SalesReport_PosStorePeriodReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPosStorePeriodReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportHubReportQuery"}}], "tags": ["SalesReport"]}}, "/api/v1/report/financial": {"post": {"summary": "财务报表", "operationId": "SalesReport_FinancialReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportFinancialResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/open/financial": {"post": {"summary": "收银汇总数据", "operationId": "SalesReport_OpenFinancialReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportOpenFinancialResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportOpenFinancialRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/open/product": {"post": {"summary": "收银汇总数据", "operationId": "SalesReport_OpenProductReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportOpenProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportOpenProductRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/adjust": {"post": {"summary": "报损报表", "operationId": "SalesReport_Adjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/category": {"post": {"summary": "类别销售报表", "operationId": "SalesReport_CategorySales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCategorySalesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/channel/product": {"post": {"summary": "商品渠道报表", "operationId": "SalesReport_ProductChannelSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductChannelResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/channel/store": {"post": {"summary": "门店渠道报表", "operationId": "SalesReport_StoreChannelSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportStoreChannelSalesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/dashboard": {"post": {"summary": "首页报表", "operationId": "SalesReport_Dashboard", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportDashboardResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportDashboardRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/discount": {"post": {"summary": "折扣销售报表", "operationId": "SalesReport_DiscountSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportDiscountSalesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/exchangerate/import": {"post": {"summary": "导入汇率", "operationId": "SalesReport_ImportExchangeRates", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportImportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportImportRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/maketime/product": {"post": {"summary": "单杯出杯时间", "operationId": "SalesReport_ProductMakeTime", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductMakeTimeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/maketime/store": {"post": {"summary": "订单完成时间", "operationId": "SalesReport_StoreMakeTime", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportStoreMakeTimeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/mealsegments": {"post": {"summary": "餐段接口", "operationId": "SalesReport_MealSegments", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportMealSegmentsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/payment": {"post": {"summary": "支付统计报表", "operationId": "SalesReport_PaymentStatistics", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPaymentStatisticsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/payment/detail": {"post": {"summary": "支付明细", "operationId": "SalesReport_PaymentDetailReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPaymentDetailReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/period/payment": {"post": {"summary": "支付时段报表", "operationId": "SalesReport_PaymentPeriodSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPaymentPeriodResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/period/product": {"post": {"summary": "商品时段报表", "operationId": "SalesReport_ProductPeriodSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductPeriodResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/period/store": {"post": {"summary": "门店时段报表", "operationId": "SalesReport_StorePeriodSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/pos/inbound": {"post": {"summary": "Pos 商品入库报表", "operationId": "SalesReport_PosInboundReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPosInboundResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/pos/promotion": {"post": {"summary": "Pos 促销报表", "operationId": "SalesReport_PosPromotion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPosPromotionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product": {"post": {"summary": "商品销售报表", "operationId": "SalesReport_ProductSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/attribute/sales": {"post": {"summary": "商品属性销量统计表", "operationId": "SalesReport_ProductAttributeSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductAttributeSalesResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/combo/detail": {"post": {"summary": "菜品销售明细表表", "operationId": "SalesReport_ProductSalesComboDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductSalesComboDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/combo/summary": {"post": {"summary": "菜品销售汇总表", "operationId": "SalesReport_ProductSalesComboSummary", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductSalesComboSummaryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/detail": {"post": {"summary": "菜品销售明细表表", "operationId": "SalesReport_ProductSalesDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductSalesDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/flavor": {"post": {"summary": "商品属性销售表", "operationId": "SalesReport_ProductFlavorSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductFlavorResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/inbound": {"post": {"summary": "Pos 商品入库报表", "operationId": "SalesReport_InboundReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportInboundResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/summary": {"post": {"summary": "菜品销售汇总表", "operationId": "SalesReport_ProductSalesSummary", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductSalesSummaryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/product/tax": {"post": {"summary": "商品计税表", "operationId": "SalesReport_ProductTaxSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductTaxResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/recent/summary": {"post": {"summary": "近期营业汇总表", "operationId": "SalesReport_RecentSummary", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportRecentSummaryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/refund/analy": {"post": {"summary": "退单原因分析表", "operationId": "SalesReport_RefundAnalysis", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportRefundAnalysisResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/store": {"post": {"summary": "门店销售报表", "operationId": "SalesReport_StoreSales", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/store-business": {"post": {"summary": "门店营业情况", "operationId": "SalesReport_StoreBusiness", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportStoreBusinessResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "partner_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "start_date", "description": "开始日期", "in": "query", "required": false, "type": "string"}, {"name": "end_date", "description": "结束日期", "in": "query", "required": false, "type": "string"}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/store/compensation": {"post": {"summary": "非营运销售报表", "operationId": "SalesReport_StoreCompensationSalesReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportStoreCompensationSalesReportResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportStoreCompensationSalesReportReq"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/store/performance": {"post": {"summary": "门店表现分析", "operationId": "SalesReport_StorePerformanceAnalysis", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportStorePerformanceAnalysisResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/storegoal/import": {"post": {"summary": "导入门店目标", "operationId": "SalesReport_ImportStoreGoals", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportImportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportImportRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/report/sales/testing": {"get": {"summary": "拨测", "operationId": "SalesReport_Testing", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportTestingResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["SalesReport"]}}, "/api/v1/report/sales/updatetime": {"post": {"summary": "报表数据更新时间", "operationId": "SalesReport_UpdateTime", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportUpdateTimeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportUpdateTimeRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/business": {"post": {"summary": "小掌柜.营业额", "operationId": "SalesReport_ShopKeeperBusiness", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportBusinessSituationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/business/category": {"post": {"summary": "小掌柜.实收金额.商品分类", "operationId": "SalesReport_ShopKeeperBusCategory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/business/category/product": {"post": {"summary": "小掌柜.实收金额.商品排行", "operationId": "SalesReport_ShopKeeperBusProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/business/top10/rank": {"post": {"summary": "小掌柜.门店营业额Top10排行", "operationId": "SalesReport_ShopKeeperBusinessRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/channel": {"post": {"summary": "小掌柜.渠道收入", "operationId": "SalesReport_ShopKeeperChaIncome", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportChannelDistributeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/channel/trend": {"post": {"summary": "小掌柜.渠道收入趋势", "operationId": "SalesReport_ShopKeeperChaTrend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/config/query": {"post": {"summary": "大掌柜报表配置查询", "operationId": "SalesReport_ShopkeeperQueryReportConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportReportConfigResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportQueryReportConfigRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/config/save": {"post": {"summary": "大掌柜报表配置保存", "operationId": "SalesReport_ShopkeeperSaveReportConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportReportConfigResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportSaveReportConfigRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/customer/form": {"post": {"summary": "小掌柜.客流构成", "operationId": "SalesReport_ShopKeeperCustomerForm", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCustomerFormResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/dashboard": {"post": {"summary": "小掌柜.首页面板", "operationId": "SalesReport_ShopKeeperDashboard", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportShopKeeperDashBoardResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/detail": {"post": {"summary": "小掌柜.财务实收和优惠组成下钻", "operationId": "SalesReport_ShopKeeperDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportDetailForDiscountAndRealAmountResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/discount/category": {"post": {"summary": "小掌柜.折扣分类", "operationId": "SalesReport_ShopKeeperDisCategory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/discount/channel": {"post": {"summary": "小掌柜.折扣渠道", "operationId": "SalesReport_ShopKeeperDisChannel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/discount/topn/rank": {"post": {"summary": "大掌柜折扣排行", "operationId": "SalesReport_ShopkeeperDiscountTopNRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportDiscountTopNRankResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/income": {"post": {"summary": "小掌柜.月收入趋势", "operationId": "SalesReport_ShopKeeperIncome", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/logistics/rank": {"post": {"summary": "小掌柜.物流占比/物流收入", "operationId": "SalesReport_ShopKeeperLogRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportLogRankResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/logistics/trend": {"post": {"summary": "小掌柜.物流收入趋势", "operationId": "SalesReport_ShopKeeperLogTrend", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/order/channel": {"post": {"summary": "小掌柜.有效订单和退单", "operationId": "SalesReport_ShopKeeperChaOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/payment": {"post": {"summary": "小掌柜.支付统计", "operationId": "SalesReport_ShopKeeperPaymentStatistics", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPaymentStatisticsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/period": {"post": {"summary": "小掌柜.营业时段", "operationId": "SalesReport_ShopKeeperPeriod", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/period-new": {"post": {"summary": "大掌柜新时段报表", "operationId": "SalesReport_ShopkeeperPeriodNew", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportPeriodNewResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/period/product": {"post": {"summary": "小掌柜.商品时段报表", "operationId": "SalesReport_ShopKeeperProductPeriod", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportShopkeeperProductPeriodResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/platform/discount": {"post": {"summary": "小掌柜.各平台补贴", "operationId": "SalesReport_ShopKeeperPlaDiscount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/attribute": {"post": {"summary": "小掌柜.商品属性下钻", "operationId": "SalesReport_ShopKeeperProductAttribute", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductAttributeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/distribute": {"post": {"summary": "小掌柜.商品分布", "operationId": "SalesReport_ShopKeeperProDistribute", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductDistributeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/polymer": {"post": {"summary": "小掌柜.统计渠道商品数据", "operationId": "SalesReport_ShopKeeperProductPolymer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductPolymerResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/rank": {"post": {"summary": "小掌柜.菜品收入", "operationId": "SalesReport_ShopKeeperProRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/store/rank": {"post": {"summary": "某商品各门店销售数据", "operationId": "SalesReport_ProductStoreRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductStoreRankResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/top20/rank": {"post": {"summary": "小掌柜.商品Top20排行", "operationId": "SalesReport_ShopKeeperProductTop20Rank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductTop20RankResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/product/topn/rank": {"post": {"summary": "5.5 商品排行", "operationId": "SalesReport_ProductTopNRank", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportProductTopNRankResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}, "/api/v1/shopkeeper/report/recent/summary": {"post": {"summary": "小掌柜.近期营业汇总表", "operationId": "SalesReport_ShopkeeperRecentSummary", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/sales_reportRecentSummaryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/sales_reportCommonRequest"}}], "tags": ["SalesReport"]}}}, "definitions": {"AdjustResponseAdjustRow": {"type": "object", "properties": {"operate_time": {"type": "string", "title": "操作时间"}, "store_name": {"type": "string", "title": "门店名称"}, "operator_name": {"type": "string", "title": "操作人"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "sku_remark_name": {"type": "string", "title": "商品规格"}, "reason_code": {"type": "string", "title": "报损原因编号"}, "reason_name": {"type": "string", "title": "报损原因名称"}, "price": {"type": "number", "format": "double", "title": "商品单价"}, "qty": {"type": "number", "format": "double", "title": "商品数量"}, "amount": {"type": "number", "format": "double", "title": "商品金额"}}}, "AttributeAttributeNameValue": {"type": "object", "properties": {"attribute_value_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "product_count_percent": {"type": "number", "format": "double"}}}, "BusinessSituationResponseBusinessSituation": {"type": "object", "properties": {"store_number": {"type": "string", "format": "int64"}, "product_count": {"type": "string", "format": "int64"}, "cup_count": {"type": "string", "format": "int64"}, "business_amount": {"type": "number", "format": "double"}, "expend_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "refund_order_count": {"type": "string", "format": "int64"}, "valid_order_count": {"type": "string", "format": "int64"}, "customer_price": {"type": "number", "format": "double"}, "merchant_allowance": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "average_business_amount": {"type": "number", "format": "double"}, "average_valid_order_count": {"type": "number", "format": "double"}, "child": {"type": "array", "items": {"$ref": "#/definitions/BusinessSituationResponseBusinessSituationChild"}}, "store_business_amount": {"type": "number", "format": "double"}, "takeout_business_amount": {"type": "number", "format": "double"}, "compare_business_amount": {"type": "number", "format": "double"}, "compare_expend_amount": {"type": "number", "format": "double"}, "compare_real_amount": {"type": "number", "format": "double"}, "compare_merchant_allowance": {"type": "number", "format": "double"}, "compare_valid_order_count": {"type": "string", "format": "int64"}, "compare_refund_order_count": {"type": "string", "format": "int64"}, "compare_customer_price": {"type": "number", "format": "double"}, "compare_discount_contribute": {"type": "number", "format": "double"}, "compare_average_business_amount": {"type": "number", "format": "double"}, "compare_transfer_real_amount": {"type": "number", "format": "double"}, "compare_average_valid_order_count": {"type": "number", "format": "double"}, "compare_product_count": {"type": "string", "format": "int64"}, "compare_cup_count": {"type": "string", "format": "int64"}, "compare_store_business_amount": {"type": "number", "format": "double"}, "compare_takeout_business_amount": {"type": "number", "format": "double"}, "compare_same_business_amount": {"type": "number", "format": "double"}, "compare_same_expend_amount": {"type": "number", "format": "double"}, "compare_same_real_amount": {"type": "number", "format": "double"}, "compare_same_merchant_allowance": {"type": "number", "format": "double"}, "compare_same_valid_order_count": {"type": "string", "format": "int64"}, "compare_same_refund_order_count": {"type": "string", "format": "int64"}, "compare_same_customer_price": {"type": "number", "format": "double"}, "compare_same_discount_contribute": {"type": "number", "format": "double"}, "compare_same_average_business_amount": {"type": "number", "format": "double"}, "compare_same_transfer_real_amount": {"type": "number", "format": "double"}, "compare_same_average_valid_order_count": {"type": "number", "format": "double"}, "compare_same_product_count": {"type": "string", "format": "int64"}, "compare_same_cup_count": {"type": "string", "format": "int64"}, "compare_same_store_business_amount": {"type": "number", "format": "double"}, "compare_same_takeout_business_amount": {"type": "number", "format": "double"}, "store_transfer_real_amount": {"type": "number", "format": "double"}, "takeout_transfer_real_amount": {"type": "number", "format": "double"}, "compare_store_transfer_real_amount": {"type": "number", "format": "double"}, "compare_takeout_transfer_real_amount": {"type": "number", "format": "double"}, "compare_same_store_transfer_real_amount": {"type": "number", "format": "double"}, "compare_same_takeout_transfer_real_amount": {"type": "number", "format": "double"}}}, "BusinessSituationResponseBusinessSituationChild": {"type": "object", "properties": {"business_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "order_type": {"type": "string"}}}, "CategorySalesResponseCategorySales": {"type": "object", "properties": {"category_name": {"type": "string", "title": "商品类目名称"}, "product_count": {"type": "number", "format": "double", "title": "商品数量"}, "net_amount": {"type": "number", "format": "double", "title": "商品类目实收"}, "percentage_of_sales": {"type": "number", "format": "double", "title": "商品类目收入占比"}}}, "ChannelDistributeResponseChannelDistribute": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "business_amount": {"type": "number", "format": "double"}, "proportion_business_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "proportion_real_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "proportion_valid_order_count": {"type": "number", "format": "double"}, "refund_order_count": {"type": "string", "format": "int64"}, "proportion_refund_order_count": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "proportion_discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "proportion_transfer_real_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "compare_business_amount": {"type": "number", "format": "double"}, "compare_same_business_amount": {"type": "number", "format": "double"}, "increase_compare_business_amount": {"type": "number", "format": "double"}, "increase_compare_same_business_amount": {"type": "number", "format": "double"}}}, "CustomerFormResponseCustomerForm": {"type": "object", "properties": {"member": {"type": "boolean"}, "member_name": {"type": "string"}, "business_amount": {"type": "number", "format": "double"}, "proportion_business_amount": {"type": "number", "format": "double"}, "merchant_allowance": {"type": "number", "format": "double"}, "proportion_merchant_allowance": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "proportion_valid_order_count": {"type": "number", "format": "double"}}}, "DetailCompositionOfDiscountDiscounts": {"type": "object", "properties": {"discount_id": {"type": "string", "format": "int64"}, "discount_code": {"type": "string"}, "discount_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}}}, "DetailCompositionOfDiscountPayments": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_code": {"type": "string"}, "payment_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}}}, "DetailCompositionOfPay": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/DetailCompositionOfPayPayment"}}, "discounts": {"type": "array", "items": {"$ref": "#/definitions/DetailCompositionOfPayDiscount"}}, "real_amount_total_payments": {"type": "number", "format": "double"}, "real_amount_total_discounts": {"type": "number", "format": "double"}}}, "DetailCompositionOfPayDiscount": {"type": "object", "properties": {"discount_id": {"type": "string", "format": "int64"}, "discount_code": {"type": "string"}, "discount_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}}}, "DetailCompositionOfPayPayment": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_code": {"type": "string"}, "payment_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}}}, "DetailForDiscountAndRealAmountResponseDetail": {"type": "object", "properties": {"store_id": {"type": "string", "format": "int64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "discount_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "composition_of_pay": {"type": "array", "items": {"$ref": "#/definitions/DetailCompositionOfPay"}}, "composition_of_discount": {"type": "array", "items": {"$ref": "#/definitions/DetailForDiscountAndRealAmountResponseDetailCompositionOfDiscount"}}}}, "DetailForDiscountAndRealAmountResponseDetailCompositionOfDiscount": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "commission": {"type": "number", "format": "double"}, "send_fee_for_merchant": {"type": "number", "format": "double"}, "other_fee": {"type": "number", "format": "double"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/DetailCompositionOfDiscountPayments"}}, "discounts": {"type": "array", "items": {"$ref": "#/definitions/DetailCompositionOfDiscountDiscounts"}}, "discount_contribute_total_payments": {"type": "number", "format": "double"}, "discount_contribute_total_discounts": {"type": "number", "format": "double"}}}, "DiscountSalesResponseDiscountSales": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "discount_id": {"type": "string", "format": "int64"}, "discount_code": {"type": "string"}, "discount_name": {"type": "string"}, "discount_type": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "product_count": {"type": "string", "format": "int64"}, "discount_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "string"}, "channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "discount_count": {"type": "string", "format": "int64"}, "branch_id": {"type": "string", "format": "int64"}, "branch_code": {"type": "string"}, "branch_name": {"type": "string"}}}, "DiscountTopNRankResponseDiscountTopNRank": {"type": "object", "properties": {"discount_name": {"type": "string", "title": "优惠名称"}, "discount_amount": {"type": "number", "format": "double", "title": "优惠金额"}, "discount_amount_percent": {"type": "number", "format": "double", "title": "优惠金额占比"}, "discount_count": {"type": "string", "format": "int64", "title": "优惠数量"}, "discount_count_percent": {"type": "number", "format": "double", "title": "优惠数量占比"}}}, "FinancialCompositionOfBillTypes": {"type": "object", "properties": {"order_type": {"type": "string"}, "order_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "order_count": {"type": "string", "format": "int64"}, "real_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfDiscountDiscount": {"type": "object", "properties": {"discount_id": {"type": "string", "format": "int64"}, "discount_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfDiscountPayment": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfPaidIn": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_name": {"type": "string"}, "discounts": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfPaidInDiscounts"}}, "payments": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfPaidInPayments"}}, "transfer_real_amount_total_payments": {"type": "number", "format": "double"}, "transfer_real_amount_total_discounts": {"type": "number", "format": "double"}, "commission": {"type": "number", "format": "double"}, "surcharge_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfPaidInDiscounts": {"type": "object", "properties": {"discount_id": {"type": "string", "format": "int64"}, "discount_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfPaidInPayments": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_name": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "FinancialCompositionOfTakeAway": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "order_count": {"type": "string", "format": "int64"}, "real_amount": {"type": "number", "format": "double"}}}, "FinancialGroupPurchase": {"type": "object", "properties": {"channel_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double", "title": "订单中含有xx渠道团购券的商品的商品流水"}, "real_amount": {"type": "number", "format": "double", "title": "订单中含有xx渠道团购券的商品的商品实收"}, "valid_order_count": {"type": "number", "format": "double", "title": "含有xx渠道团购券的订单数"}}}, "FinancialResponseFinancial": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "business_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "customer_price": {"type": "number", "format": "double"}, "composition_of_bill_types": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfBillTypes"}}, "composition_of_take_away": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfTakeAway"}}, "composition_of_paid_in": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfPaidIn"}}, "composition_of_paid_in_total": {"type": "number", "format": "double"}, "composition_of_discount": {"type": "array", "items": {"$ref": "#/definitions/FinancialResponseFinancialCompositionOfDiscount"}}, "composition_of_discount_total": {"type": "number", "format": "double"}, "package_fee": {"type": "number", "format": "double"}, "merchant_send_fee": {"type": "number", "format": "double"}, "sales_fee": {"type": "number", "format": "double"}, "dish_income_account_total": {"type": "number", "format": "double"}, "total": {"type": "integer", "format": "int32"}, "tag": {"type": "string"}, "meal_segments": {"type": "array", "items": {"$ref": "#/definitions/FinancialResponseFinancialMealSegment"}, "title": "餐段组成"}, "group_purchases": {"type": "array", "items": {"$ref": "#/definitions/FinancialGroupPurchase"}, "title": "团购渠道组成（订单中使用相关渠道团购券）"}, "store_tag": {"type": "string", "title": "门店标签"}, "customer_count": {"type": "number", "format": "double", "title": "客流"}, "bowl_count": {"type": "number", "format": "double", "title": "碗数"}, "tip": {"type": "number", "format": "double", "title": "小费"}, "real_amount_per_capita": {"type": "number", "format": "double", "title": "人均消费: 实收金额/客流"}, "customer_count_per_day": {"type": "number", "format": "double", "title": "日均客流: 客流/营业天数"}, "real_amount_per_day": {"type": "number", "format": "double", "title": "日均实收: 实收金额/营业天数"}, "real_amount_per_bowl": {"type": "number", "format": "double"}, "region_address1": {"type": "string", "title": "省"}, "region_address2": {"type": "string", "title": "市"}, "region_address3": {"type": "string", "title": "区"}, "branch_region": {"type": "string", "title": "管理区域"}, "tax_fee": {"type": "number", "format": "double", "title": "税费"}, "third_party_id": {"type": "string", "title": "三方ID"}, "real_amount_without_gst": {"type": "number", "format": "double"}}}, "FinancialResponseFinancialCompositionOfDiscount": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_name": {"type": "string"}, "commission": {"type": "number", "format": "double"}, "send_fee_for_merchant": {"type": "number", "format": "double"}, "other_fee": {"type": "number", "format": "double"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfDiscountPayment"}}, "discounts": {"type": "array", "items": {"$ref": "#/definitions/FinancialCompositionOfDiscountDiscount"}}}}, "FinancialResponseFinancialMealSegment": {"type": "object", "properties": {"meal_segment_name": {"type": "string", "title": "餐段名称"}, "gross_amount": {"type": "number", "format": "double", "title": "订单流水"}, "real_amount": {"type": "number", "format": "double", "title": "订单实收"}, "valid_order_count": {"type": "number", "format": "double", "title": "订单数"}}}, "InboundResponseInboundRow": {"type": "object", "properties": {"store_name": {"type": "string", "title": "门店名称"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "price": {"type": "number", "format": "double", "title": "商品价格"}, "amount": {"type": "number", "format": "double", "title": "商品价格"}, "sku_remark": {"type": "string", "title": "商品规格"}, "qty": {"type": "number", "format": "double", "title": "商品数量"}, "inbound_time": {"type": "string", "title": "入库时间"}, "operator_name": {"type": "string", "title": "入库人"}}}, "LogRankResponseLogRank": {"type": "object", "properties": {"order_type": {"type": "string"}, "order_type_name": {"type": "string"}, "business_amount": {"type": "number", "format": "double"}, "proportion_business_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "proportion_real_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "proportion_valid_order_count": {"type": "number", "format": "double"}, "compare_business_amount": {"type": "number", "format": "double"}, "compare_same_business_amount": {"type": "number", "format": "double"}, "increase_compare_business_amount": {"type": "number", "format": "double"}, "increase_compare_same_business_amount": {"type": "number", "format": "double"}}}, "OpenFinancialResponseOpenFinancialReportStore": {"type": "object", "properties": {"bus_date": {"type": "string"}, "store_id": {"type": "string", "format": "int64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "store_city": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "integer", "format": "int32"}, "business_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "valid_ticket_count": {"type": "integer", "format": "int32"}}}, "OpenProductResponseOpenProductReport": {"type": "object", "properties": {"bus_date": {"type": "string"}, "store_id": {"type": "string", "format": "int64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "qty": {"type": "integer", "format": "int32"}, "sku_remark": {"type": "string"}, "created": {"type": "string"}, "updated": {"type": "string"}}}, "PaymentDetailReportResponsePaymentDetailReportRow": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "ticket_no": {"type": "string", "title": "订单号"}, "payment_id": {"type": "string", "format": "int64", "title": "支付方式ID"}, "payment_code": {"type": "string", "title": "支付方式编码"}, "payment_name": {"type": "string", "title": "支付方式名称"}, "pay_time": {"type": "string", "title": "支付时间 HH24:MI:SS"}, "pay_amount": {"type": "string", "title": "实付金额"}, "payment_transfer_amount": {"type": "string", "title": "支付转折扣"}, "sur_charge_amount": {"type": "string", "title": "手续费"}, "card_no": {"type": "string", "title": "卡号"}, "pay_date": {"type": "string", "title": "支付日期 YYYY/MM/DD"}, "real_amount": {"type": "string", "title": "实收金额"}, "plate_no": {"type": "string"}, "phone_no": {"type": "string"}, "operator_code": {"type": "string"}, "service_fee": {"type": "string"}, "non_sales_amount": {"type": "string"}}}, "PaymentPeriodResponsePaymentPeriod": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "data": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodData"}, "child": {"type": "array", "items": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChild"}}}}, "PaymentPeriodResponsePaymentPeriodChild": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_code": {"type": "string"}, "payment_name": {"type": "string"}, "payment_type": {"type": "string"}, "h00": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH00"}, "h01": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH01"}, "h02": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH02"}, "h03": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH03"}, "h04": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH04"}, "h05": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH05"}, "h06": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH06"}, "h07": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH07"}, "h08": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH08"}, "h09": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH09"}, "h10": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH10"}, "h11": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH11"}, "h12": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH12"}, "h13": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH13"}, "h14": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH14"}, "h15": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH15"}, "h16": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH16"}, "h17": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH17"}, "h18": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH18"}, "h19": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH19"}, "h20": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH20"}, "h21": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH21"}, "h22": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH22"}, "h23": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodChildH23"}}}, "PaymentPeriodResponsePaymentPeriodChildH00": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH01": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH02": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH03": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH04": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH05": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH06": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH07": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH08": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH09": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH10": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH11": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH12": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH13": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH14": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH15": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH16": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH17": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH18": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH19": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH20": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH21": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH22": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodChildH23": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodData": {"type": "object", "properties": {"h00": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH00"}, "h01": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH01"}, "h02": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH02"}, "h03": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH03"}, "h04": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH04"}, "h05": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH05"}, "h06": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH06"}, "h07": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH07"}, "h08": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH08"}, "h09": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH09"}, "h10": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH10"}, "h11": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH11"}, "h12": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH12"}, "h13": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH13"}, "h14": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH14"}, "h15": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH15"}, "h16": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH16"}, "h17": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH17"}, "h18": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH18"}, "h19": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH19"}, "h20": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH20"}, "h21": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH21"}, "h22": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH22"}, "h23": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriodDataH23"}}}, "PaymentPeriodResponsePaymentPeriodDataH00": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH01": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH02": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH03": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH04": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH05": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH06": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH07": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH08": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH09": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH10": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH11": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH12": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH13": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH14": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH15": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH16": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH17": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH18": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH19": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH20": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH21": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH22": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentPeriodResponsePaymentPeriodDataH23": {"type": "object", "properties": {"receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "payment_transfer_amount": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "tp_allowance": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}}}, "PaymentStatisticsResponsePaymentStatistics": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业时间"}, "branch_id": {"type": "string", "format": "int64", "title": "管理区域"}, "branch_name": {"type": "string", "title": "管理区域名称"}, "branch_code": {"type": "string", "title": "管理区域 Code"}, "region_id": {"type": "string", "format": "int64", "title": "门店"}, "region_code": {"type": "string", "title": "门店 Code"}, "region_name": {"type": "string", "title": "门店名称"}, "cooperation_code": {"type": "string", "title": "支付供应商编码"}, "payment_category_id": {"type": "string", "format": "int64", "title": "支付类型 ID"}, "payment_category_name": {"type": "string", "title": "支付类型名称"}, "payment_category_code": {"type": "string", "title": "支付类型编码"}, "payment_id": {"type": "string", "format": "int64", "title": "支付方式 ID"}, "payment_code": {"type": "string", "title": "支付方式编码"}, "payment_name": {"type": "string", "title": "支付方式名称"}, "pay_amount": {"type": "number", "format": "double", "title": "支付金额"}, "transfer_real_amount": {"type": "number", "format": "double", "title": "财务实收金额(转换后)"}, "item_count": {"type": "string", "format": "int64", "title": "收款必数"}, "transfer_real_amount_returned": {"type": "number", "format": "double", "title": "财务实收金额(转换后)-退款"}, "item_count_returned": {"type": "string", "format": "int64", "title": "退款笔数"}, "deposit_amount": {"type": "number", "format": "double", "title": "预定金额"}, "deposit_item_count": {"type": "string", "format": "int64", "title": "预定笔数"}, "all_item_count": {"type": "string", "format": "int64", "title": "总笔数"}, "channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}}}, "PeriodNewResponsePeriodNewRow": {"type": "object", "properties": {"period_name": {"type": "string"}, "period_time": {"type": "string"}, "real_amount": {"type": "number", "format": "double"}, "finance_real_amount": {"type": "number", "format": "double"}, "business_amount": {"type": "number", "format": "double"}, "valid_ticket_count": {"type": "string", "format": "int64"}}}, "PettyCashDailySettlementReportResponsePettyCashDailySettlementReportRow": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "start_balance": {"type": "string", "title": "開機金額"}, "collect_cash_amount": {"type": "string", "title": "已收取現金"}, "payout_amount": {"type": "string", "title": "支出Total"}, "end_balance": {"type": "string", "title": "零用現金結餘"}, "startup_amount": {"type": "string", "title": "开店零用现金"}, "payout_detail": {"type": "object", "title": "支出明细"}}}, "PosDepartmentBusinessResponseDepartment": {"type": "object", "properties": {"name": {"type": "string"}, "qty": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "percentage": {"type": "number", "format": "double"}}}, "PosInboundResponsePosInboundRow": {"type": "object", "properties": {"inbound_time": {"type": "string", "title": "入库时间"}, "operator_name": {"type": "string", "title": "入库人"}, "product_names": {"type": "string", "title": "商品名称"}, "product_qty": {"type": "number", "format": "double", "title": "商品数量"}, "content": {"type": "object", "title": "内容"}}}, "PosProductSalesReportResponseProductSales": {"type": "object", "properties": {"name": {"type": "string"}, "qty": {"type": "integer", "format": "int32"}, "amount": {"type": "number", "format": "double"}, "percentage": {"type": "number", "format": "double"}}}, "PosStorePeriodReportResponsePeriod": {"type": "object", "properties": {"time_period": {"type": "string"}, "bill_count": {"type": "integer", "format": "int32"}, "bowl_count": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "percentage": {"type": "number", "format": "double"}}}, "ProductAttributeAttribute": {"type": "object", "properties": {"attribute_name": {"type": "string"}, "attribute_name_values": {"type": "array", "items": {"$ref": "#/definitions/AttributeAttributeNameValue"}}}, "title": "map<string, string> sku_remark = 8;\n   map<string, int64> accessories = 9;"}, "ProductAttributeFeed": {"type": "object", "properties": {"feed_name": {"type": "string"}, "feed_count": {"type": "string", "format": "int64"}, "feed_count_percent": {"type": "number", "format": "double"}}}, "ProductAttributeResponseProductAttribute": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "product_count_total": {"type": "string", "format": "int64"}, "percent_product_count": {"type": "number", "format": "double"}, "flavor": {"type": "string"}, "attribute_names": {"type": "array", "items": {"$ref": "#/definitions/ProductAttributeAttribute"}}, "feeds": {"type": "array", "items": {"$ref": "#/definitions/ProductAttributeFeed"}}}}, "ProductAttributeSalesResponseProductAttributeSalesRow": {"type": "object", "properties": {"attribute_code": {"type": "string", "title": "属性编码"}, "attribute_name": {"type": "string", "title": "属性名称"}, "attribute_group_code": {"type": "string", "title": "属性组编码"}, "attribute_group_name": {"type": "string", "title": "属性组名称"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_name": {"type": "string", "title": "门店名称"}, "store_code": {"type": "string", "title": "门店编码"}, "bus_date": {"type": "string", "title": "营业日期"}, "qty": {"type": "number", "format": "double", "title": "销量"}, "qty_ratio": {"type": "number", "format": "double", "title": "销量占比"}}}, "ProductChannelChildChildChild": {"type": "object", "properties": {"order_type": {"type": "string"}, "order_type_name": {"type": "string"}, "product_average_price": {"type": "number", "format": "double"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "weight_returned": {"type": "number", "format": "double"}, "refund_weight_count": {"type": "string"}, "unit": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductChannelResponseProductChannel": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "product_average_price": {"type": "number", "format": "double"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductChannelResponseProductChannelChild"}}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductChannelResponseProductChannelChild": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "product_category_id": {"type": "string", "format": "int64"}, "product_category_code": {"type": "string"}, "product_category_name": {"type": "string"}, "product_average_price": {"type": "number", "format": "double"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductChannelResponseProductChannelChildChild"}}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "weight_returned": {"type": "number", "format": "double"}, "refund_weight_count": {"type": "string"}, "unit": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductChannelResponseProductChannelChildChild": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "product_average_price": {"type": "number", "format": "double"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductChannelChildChildChild"}}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "weight_returned": {"type": "number", "format": "double"}, "refund_weight_count": {"type": "string"}, "unit": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductDataDataDataData": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "weight_returned": {"type": "number", "format": "double"}, "refund_weight_count": {"type": "string"}, "unit": {"type": "string"}, "product_sale_name": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductDistributeResponseProductDistribute": {"type": "object", "properties": {"category0": {"type": "string", "format": "int64"}, "category0_code": {"type": "string"}, "category0_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductDistributeResponseProductDistributeChild"}}, "gross_amount_percent": {"type": "number", "format": "double"}, "net_amount_percent": {"type": "number", "format": "double"}, "discount_amount_percent": {"type": "number", "format": "double"}, "item_count_percent": {"type": "number", "format": "double"}}}, "ProductDistributeResponseProductDistributeChild": {"type": "object", "properties": {"category1": {"type": "string", "format": "int64"}, "category1_code": {"type": "string"}, "category1_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "gross_amount_percent": {"type": "number", "format": "double"}, "net_amount_percent": {"type": "number", "format": "double"}, "discount_amount_percent": {"type": "number", "format": "double"}, "item_count_percent": {"type": "number", "format": "double"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductDistributeResponseProductDistributeChildChild"}}}}, "ProductDistributeResponseProductDistributeChildChild": {"type": "object", "properties": {"category2": {"type": "string", "format": "int64"}, "category2_code": {"type": "string"}, "category2_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "gross_amount_percent": {"type": "number", "format": "double"}, "net_amount_percent": {"type": "number", "format": "double"}, "discount_amount_percent": {"type": "number", "format": "double"}, "item_count_percent": {"type": "number", "format": "double"}}}, "ProductFlavorResponseProductFlavor": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "flavor": {"type": "string"}, "sku_remark": {"type": "string"}, "accessories": {"type": "string"}, "accessory_id": {"type": "string", "format": "int64"}, "accessory_name": {"type": "string"}, "flavor_distribute": {"type": "string"}, "attribute_name_code": {"type": "string"}, "attribute_name_name": {"type": "string"}, "attribute_value_code": {"type": "string"}, "attribute_value_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "product_count_total": {"type": "string", "format": "int64"}, "percent_product_count": {"type": "number", "format": "double"}, "attribute_names": {"type": "array", "items": {"$ref": "#/definitions/ProductFlavorattribute_name"}}, "feeds": {"type": "array", "items": {"$ref": "#/definitions/ProductFlavoraccessory"}}}}, "ProductFlavoraccessory": {"type": "object", "properties": {"accessory_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "percent_product_count": {"type": "number", "format": "double"}}}, "ProductFlavorattribute_name": {"type": "object", "properties": {"attribute_name_code": {"type": "string"}, "attribute_name_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "percent_product_count": {"type": "number", "format": "double"}, "attribute_name_values": {"type": "array", "items": {"$ref": "#/definitions/attribute_nameattribute_name_value"}}}}, "ProductMakeTimeResponseProductMakeTimeItem": {"type": "object", "properties": {"region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string", "format": "int64"}, "store_type_name": {"type": "string"}, "time_period": {"type": "string", "title": "时段"}, "product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "product_category_id": {"type": "string", "format": "int64", "title": "商品类目id"}, "product_category_code": {"type": "string"}, "product_category_name": {"type": "string", "title": "商品类目名称"}, "cup_count": {"type": "integer", "format": "int32", "title": "杯数"}, "make_time_avg_per_cup": {"type": "string", "title": "平均每杯制作时间(min)"}}}, "ProductPeriodResponseProductPeriod": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "data": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodData"}, "child": {"type": "array", "items": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChild"}}, "region_alias": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChild": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "product_category_id": {"type": "string", "format": "int64"}, "product_category_code": {"type": "string"}, "product_category_name": {"type": "string"}, "h00": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH00"}, "h01": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH01"}, "h02": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH02"}, "h03": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH03"}, "h04": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH04"}, "h05": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH05"}, "h06": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH06"}, "h07": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH07"}, "h08": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH08"}, "h09": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH09"}, "h10": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH10"}, "h11": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH11"}, "h12": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH12"}, "h13": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH13"}, "h14": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH14"}, "h15": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH15"}, "h16": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH16"}, "h17": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH17"}, "h18": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH18"}, "h19": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH19"}, "h20": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH20"}, "h21": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH21"}, "h22": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH22"}, "h23": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodChildH23"}}}, "ProductPeriodResponseProductPeriodChildH00": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH01": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH02": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH03": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH04": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH05": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH06": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH07": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH08": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH09": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH10": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH11": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH12": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH13": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH14": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH15": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH16": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH17": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH18": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH19": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH20": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH21": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH22": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodChildH23": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "weight_count": {"type": "string"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodData": {"type": "object", "properties": {"h00": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH00"}, "h01": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH01"}, "h02": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH02"}, "h03": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH03"}, "h04": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH04"}, "h05": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH05"}, "h06": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH06"}, "h07": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH07"}, "h08": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH08"}, "h09": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH09"}, "h10": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH10"}, "h11": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH11"}, "h12": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH12"}, "h13": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH13"}, "h14": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH14"}, "h15": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH15"}, "h16": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH16"}, "h17": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH17"}, "h18": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH18"}, "h19": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH19"}, "h20": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH20"}, "h21": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH21"}, "h22": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH22"}, "h23": {"$ref": "#/definitions/ProductPeriodResponseProductPeriodDataH23"}}}, "ProductPeriodResponseProductPeriodDataH00": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH01": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH02": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH03": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH04": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH05": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH06": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH07": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH08": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH09": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH10": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH11": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH12": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH13": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH14": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH15": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH16": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH17": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH18": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH19": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH20": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH21": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH22": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPeriodResponseProductPeriodDataH23": {"type": "object", "properties": {"gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "weight": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductPolymerResponseProductPolymer": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_name": {"type": "string"}, "item_count": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "weight_count": {"type": "number", "format": "double"}, "unit": {"type": "string"}}}, "ProductReportResponseSumProduct": {"type": "object", "properties": {"sum_amount": {"type": "number", "format": "double"}, "sum_net_amount": {"type": "number", "format": "double"}, "quantity": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double"}, "taxAmount": {"type": "number", "format": "double"}}}, "ProductResponseProductData": {"type": "object", "properties": {"category0": {"type": "string", "format": "int64"}, "category0_code": {"type": "string"}, "category0_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductResponseProductDataData"}}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductResponseProductDataData": {"type": "object", "properties": {"category1": {"type": "string", "format": "int64"}, "category1_code": {"type": "string"}, "category1_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductResponseProductDataDataData"}}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductResponseProductDataDataData": {"type": "object", "properties": {"category2": {"type": "string", "format": "int64"}, "category2_code": {"type": "string"}, "category2_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductDataDataDataData"}}, "tax_fee": {"type": "number", "format": "double"}}}, "ProductSalesComboDetailResponseProductSalesComboDetail": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "region_address": {"type": "string", "title": "所在城市"}, "region_address1": {"type": "string", "title": "省"}, "region_address2": {"type": "string", "title": "市"}, "region_address3": {"type": "string", "title": "区"}, "region_code": {"type": "string", "title": "门店编号"}, "region_name": {"type": "string", "title": "门店名称"}, "store_type_name": {"type": "string", "title": "门店经营类型"}, "group_purchase_channel": {"type": "string", "title": "团购渠道"}, "combo_product_name": {"type": "string", "title": "套餐名称"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "qty": {"type": "number", "format": "double", "title": "商品数量"}, "price": {"type": "number", "format": "double", "title": "商品售价"}, "gross_amount": {"type": "number", "format": "double", "title": "商品流水"}, "discount_amount": {"type": "number", "format": "double", "title": "折扣金额"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "add_time": {"type": "string", "title": "点菜时间"}, "ticket_no": {"type": "string", "title": "订单编号"}, "branch_region": {"type": "string"}, "unit": {"type": "string"}, "sku_remark": {"type": "string"}, "row_idx": {"type": "integer", "format": "int32"}, "category_name": {"type": "string"}, "order_type_name": {"type": "string"}, "combo_qty": {"type": "number", "format": "double", "title": "套餐商品数量"}}}, "ProductSalesComboSummaryResponseProductSalesComboSummary": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "region_address": {"type": "string", "title": "所在城市"}, "region_address1": {"type": "string", "title": "省"}, "region_address2": {"type": "string", "title": "市"}, "region_address3": {"type": "string", "title": "区"}, "region_code": {"type": "string", "title": "门店编号"}, "region_name": {"type": "string", "title": "门店名称"}, "store_type_name": {"type": "string", "title": "门店经营类型"}, "group_purchase_channel": {"type": "string", "title": "团购渠道"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "qty": {"type": "number", "format": "double", "title": "商品数量"}, "price": {"type": "number", "format": "double", "title": "商品售价"}, "gross_amount": {"type": "number", "format": "double", "title": "商品流水"}, "discount_amount": {"type": "number", "format": "double", "title": "折扣金额"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "branch_region": {"type": "string"}, "row_idx": {"type": "integer", "format": "int32"}, "category_name": {"type": "string"}}}, "ProductSalesDetailResponseProductSalesDetail": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "region_address": {"type": "string", "title": "省"}, "region_address1": {"type": "string", "title": "省"}, "region_address2": {"type": "string", "title": "市"}, "region_address3": {"type": "string", "title": "区"}, "branch_region": {"type": "string", "title": "管理区域"}, "store_type_name": {"type": "string", "title": "门店经营类型"}, "store_code": {"type": "string", "title": "门店编号"}, "store_name": {"type": "string", "title": "门店名称"}, "category_code": {"type": "string", "title": "类目编码"}, "category_name": {"type": "string", "title": "类目名称"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "channel_name": {"type": "string", "title": "下单渠道"}, "dining_method": {"type": "string", "title": "用餐方式"}, "sku_remark": {"type": "string", "title": "商品规格"}, "price": {"type": "number", "format": "double", "title": "商品售价"}, "product_count": {"type": "number", "format": "double", "title": "商品数量"}, "bowl_count": {"type": "number", "format": "double", "title": "碗数"}, "gross_amount": {"type": "number", "format": "double", "title": "商品流水"}, "discount_amount": {"type": "number", "format": "double", "title": "折扣金额"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "weight": {"type": "string", "title": "份量统计"}, "meal_segment_name": {"type": "string", "title": "餐段"}, "zone_name": {"type": "string", "title": "用餐区域名称"}, "table_no": {"type": "string", "title": "桌位号"}, "order_user": {"type": "string", "title": "点餐人"}, "open_time": {"type": "string", "title": "开台时间"}, "order_time": {"type": "string", "title": "点菜时间"}, "cashier_name": {"type": "string", "title": "收银员"}, "end_time": {"type": "string", "title": "结账时间"}, "order_type_name": {"type": "string", "title": "订单类型"}, "group_purchase_channel": {"type": "string", "title": "团购渠道"}, "ticket_no": {"type": "string", "title": "订单编号"}, "remark": {"type": "string", "title": "备注"}, "spicy": {"type": "string", "title": "辣度"}, "acc_count": {"type": "integer", "format": "int32", "title": "加料数量"}, "product_acc_count": {"type": "integer", "format": "int32", "title": "配料数量"}, "row_idx": {"type": "integer", "format": "int32"}, "unit_info": {"type": "string"}, "production_department_name": {"type": "string"}, "income_subject_name": {"type": "string"}, "discount_rate": {"type": "string"}, "flags": {"type": "string"}, "product_label": {"type": "string"}, "product_secondary_column": {"type": "string", "title": "商品名称辅助列"}}}, "ProductSalesSummaryResponseProductSalesSummary": {"type": "object", "properties": {"product_code": {"type": "string", "title": "商品code"}, "product_name": {"type": "string", "title": "商品名称"}, "product_count": {"type": "number", "format": "double", "title": "商品数量"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "percentage_of_sales": {"type": "number", "format": "double", "title": "商品收入占比"}, "category_code": {"type": "string", "title": "商品分类code"}, "category_name": {"type": "string", "title": "商品分类名称"}}}, "ProductStoreRankResponseProductStoreRank": {"type": "object", "properties": {"store_id": {"type": "string", "format": "int64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店编号"}, "store_name": {"type": "string", "title": "门店名称"}, "gross_amount": {"type": "number", "format": "double", "title": "商品流水"}, "gross_amount_percent": {"type": "number", "format": "double", "title": "商品流水占比"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "net_amount_percent": {"type": "number", "format": "double", "title": "商品实收占比"}, "item_count": {"type": "string", "format": "int64", "title": "商品数量"}, "item_count_percent": {"type": "number", "format": "double", "title": "商品数量占比"}, "weight_count": {"type": "number", "format": "double", "title": "商品重量"}, "weight_count_percent": {"type": "number", "format": "double", "title": "商品重量占比"}, "unit": {"type": "string", "title": "商品单位"}}}, "ProductTaxResponseProductTaxSales": {"type": "object", "properties": {"store_id": {"type": "string", "format": "int64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "store_address": {"type": "string"}, "store_alias": {"type": "string"}, "store_status": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "company_id": {"type": "string", "format": "int64"}, "company_code": {"type": "string"}, "company_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "category_id": {"type": "string", "format": "int64"}, "category_code": {"type": "string"}, "category_name": {"type": "string"}, "product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "finance_real_amount": {"type": "number", "format": "double"}, "transfer_amount": {"type": "number", "format": "double"}, "discount_transfer_amount": {"type": "number", "format": "double"}, "product_tax": {"type": "number", "format": "double"}, "invoice_amount": {"type": "number", "format": "double"}, "total": {"type": "string", "format": "int64"}}}, "ProductTop20RankResponseProductTop20Rank": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_percent": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_percent": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_percent": {"type": "number", "format": "double"}, "weight_count": {"type": "number", "format": "double"}, "unit": {"type": "string"}}}, "ProductTopNRankResponseProductTopNRank": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "gross_amount": {"type": "number", "format": "double", "title": "商品流水"}, "gross_amount_percent": {"type": "number", "format": "double", "title": "商品流水占比"}, "net_amount": {"type": "number", "format": "double", "title": "商品实收"}, "net_amount_percent": {"type": "number", "format": "double", "title": "商品实收占比"}, "item_count": {"type": "string", "format": "int64", "title": "商品数量"}, "item_count_percent": {"type": "number", "format": "double", "title": "商品数量占比"}, "weight_count": {"type": "number", "format": "double", "title": "商品重量"}, "unit": {"type": "string", "title": "商品单位"}}}, "RecentSummaryItemPaymentItem": {"type": "object", "properties": {"payment_id": {"type": "string", "format": "int64"}, "payment_name": {"type": "string"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "RecentSummaryResponseRecentSummaryItem": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string", "title": "门店编号"}, "region_name": {"type": "string", "title": "门店名称"}, "region_address": {"type": "string", "title": "所在城市"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string", "title": "门店经营类型"}, "business_days": {"type": "string", "format": "int64"}, "business_amount": {"type": "number", "format": "double", "title": "营业额"}, "compare_business_amount": {"type": "number", "format": "double"}, "finance_real_amount": {"type": "number", "format": "double", "title": "实收金额"}, "compare_finance_real_amount": {"type": "number", "format": "double"}, "finance_expend_amount": {"type": "number", "format": "double", "title": "优惠金额"}, "compare_finance_expend_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64", "title": "订单数"}, "compare_valid_order_count": {"type": "string", "format": "int64"}, "pay_amount": {"type": "number", "format": "double", "title": "消费者实付金额"}, "compare_pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "compare_transfer_real_amount": {"type": "number", "format": "double"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/RecentSummaryItemPaymentItem"}, "title": "各个支付方式具体支付金额"}, "child": {"type": "array", "items": {"$ref": "#/definitions/RecentSummaryResponseRecentSummaryItemChild"}, "title": "渠道"}}}, "RecentSummaryResponseRecentSummaryItemChild": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string", "title": "渠道名称"}, "business_amount": {"type": "number", "format": "double"}, "finance_real_amount": {"type": "number", "format": "double"}, "finance_expend_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "pay_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}}}, "RefundAnalysisResponseRefundAnalysis": {"type": "object", "properties": {"bus_date": {"type": "string"}, "channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "pos_device_code": {"type": "string"}, "ticket_no": {"type": "string"}, "order_count_returned": {"type": "string", "format": "int64"}, "pay_amount_returned": {"type": "number", "format": "double"}, "refund_side": {"type": "string"}, "operator_name": {"type": "string"}, "refund_code": {"type": "string"}, "refund_reason": {"type": "string"}, "branch_id": {"type": "string", "format": "int64"}, "branch_name": {"type": "string"}, "branch_code": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "origin_real_ticket_no": {"type": "string"}, "payment_id": {"type": "string", "format": "int64"}, "payment_code": {"type": "string"}, "payment_name": {"type": "string"}, "origin_order_time": {"type": "string"}, "order_time": {"type": "string"}}}, "ShopKeeperDashBoardResponseDashboard": {"type": "object", "properties": {"business_amount": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "customer_value": {"type": "number", "format": "double"}}}, "ShopkeeperProductPeriodResponseShopkeeperProductPeriodItem": {"type": "object", "properties": {"unit": {"type": "string"}, "weight": {"type": "number", "format": "float"}, "weight_count": {"type": "string"}, "gross_amount": {"type": "number", "format": "float"}, "net_amount": {"type": "number", "format": "float"}, "item_count": {"type": "string", "format": "int64"}}}, "ShopkeeperProductPeriodResponseShopkeeperProductPeriodRow": {"type": "object", "properties": {"product_id": {"type": "string", "format": "int64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "periods": {"type": "array", "items": {"$ref": "#/definitions/ShopkeeperProductPeriodResponseShopkeeperProductPeriodItem"}}, "row_summary": {"$ref": "#/definitions/ShopkeeperProductPeriodResponseShopkeeperProductPeriodItem"}}}, "StoreBusinessResponseStoreBusinessItem": {"type": "object", "properties": {"store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "business_amount": {"type": "number", "format": "double", "title": "营业额"}}}, "StoreChannelSalesResponseStoreChannelSales": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "business_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "expend_amount": {"type": "number", "format": "double"}, "customer_price": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "merchant_allowance": {"type": "number", "format": "double"}, "package_fee": {"type": "number", "format": "double"}, "delivery_fee": {"type": "number", "format": "double"}, "send_fee": {"type": "number", "format": "double"}, "merchant_send_fee": {"type": "number", "format": "double"}, "platform_send_fee": {"type": "number", "format": "double"}, "commission": {"type": "number", "format": "double"}, "service_fee": {"type": "number", "format": "double"}, "tip": {"type": "number", "format": "double"}, "receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "discount_merchant_contribute": {"type": "number", "format": "double"}, "pay_merchant_contribute": {"type": "number", "format": "double"}, "real_amount_discount": {"type": "number", "format": "double"}, "real_amount_payment": {"type": "number", "format": "double"}, "child": {"type": "array", "items": {"$ref": "#/definitions/StoreChannelSalesResponseStoreChannelSalesChild"}}, "tax_fee": {"type": "number", "format": "double"}}}, "StoreChannelSalesResponseStoreChannelSalesChild": {"type": "object", "properties": {"channel_id": {"type": "string", "format": "int64"}, "channel_code": {"type": "string"}, "channel_name": {"type": "string"}, "business_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "expend_amount": {"type": "number", "format": "double"}, "customer_price": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "merchant_allowance": {"type": "number", "format": "double"}, "package_fee": {"type": "number", "format": "double"}, "delivery_fee": {"type": "number", "format": "double"}, "send_fee": {"type": "number", "format": "double"}, "merchant_send_fee": {"type": "number", "format": "double"}, "platform_send_fee": {"type": "number", "format": "double"}, "commission": {"type": "number", "format": "double"}, "service_fee": {"type": "number", "format": "double"}, "tip": {"type": "number", "format": "double"}, "receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "discount_merchant_contribute": {"type": "number", "format": "double"}, "pay_merchant_contribute": {"type": "number", "format": "double"}, "real_amount_discount": {"type": "number", "format": "double"}, "real_amount_payment": {"type": "number", "format": "double"}, "child": {"type": "array", "items": {"$ref": "#/definitions/StoreChannelSalesResponseStoreChannelSalesChildChild"}}, "tax_fee": {"type": "number", "format": "double"}}}, "StoreChannelSalesResponseStoreChannelSalesChildChild": {"type": "object", "properties": {"order_type": {"type": "string"}, "order_type_name": {"type": "string"}, "business_amount": {"type": "number", "format": "double"}, "real_amount": {"type": "number", "format": "double"}, "expend_amount": {"type": "number", "format": "double"}, "customer_price": {"type": "number", "format": "double"}, "valid_order_count": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "merchant_allowance": {"type": "number", "format": "double"}, "package_fee": {"type": "number", "format": "double"}, "delivery_fee": {"type": "number", "format": "double"}, "send_fee": {"type": "number", "format": "double"}, "merchant_send_fee": {"type": "number", "format": "double"}, "platform_send_fee": {"type": "number", "format": "double"}, "commission": {"type": "number", "format": "double"}, "service_fee": {"type": "number", "format": "double"}, "tip": {"type": "number", "format": "double"}, "receivable": {"type": "number", "format": "double"}, "pay_amount": {"type": "number", "format": "double"}, "rounding": {"type": "number", "format": "double"}, "overflow_amount": {"type": "number", "format": "double"}, "change_amount": {"type": "number", "format": "double"}, "transfer_real_amount": {"type": "number", "format": "double"}, "discount_contribute": {"type": "number", "format": "double"}, "discount_merchant_contribute": {"type": "number", "format": "double"}, "pay_merchant_contribute": {"type": "number", "format": "double"}, "real_amount_discount": {"type": "number", "format": "double"}, "real_amount_payment": {"type": "number", "format": "double"}, "tax_fee": {"type": "number", "format": "double"}}}, "StoreCompensationSalesReportReqqueryType": {"type": "string", "enum": ["query_type_header", "query_type_detail"], "default": "query_type_header"}, "StoreMakeTimeResponseStoreMakeTimeItem": {"type": "object", "properties": {"region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "region_alias": {"type": "string"}, "store_type": {"type": "string", "format": "int64"}, "store_type_name": {"type": "string"}, "time_period": {"type": "string", "title": "时段"}, "order_count": {"type": "integer", "format": "int32", "title": "单数"}, "make_time_avg_per_order": {"type": "string", "title": "平均每单制作时间(min)"}, "cup_count": {"type": "integer", "format": "int32", "title": "杯数"}, "make_time_avg_per_cup": {"type": "string", "title": "平均每杯制作时间(min)"}}}, "StorePerformanceAnalysisOrderType": {"type": "object", "properties": {"order_type_name": {"type": "string"}, "bowl_turnover": {"type": "number", "format": "double"}}}, "StorePerformanceAnalysisOrderTypeMealSegment": {"type": "object", "properties": {"name": {"type": "string"}, "bowl_turnover": {"type": "number", "format": "double"}}}, "StorePerformanceAnalysisResponseStorePerformanceAnalysis": {"type": "object", "properties": {"store_name": {"type": "string"}, "seat_count": {"type": "string", "format": "int64"}, "business_amount": {"type": "number", "format": "double"}, "bowl_count": {"type": "number", "format": "double"}, "seat_turnover_rate": {"type": "number", "format": "double"}, "average_daily_sales": {"type": "number", "format": "double"}, "average_sales_per_order": {"type": "number", "format": "double"}, "average_bowl_spending": {"type": "number", "format": "double"}, "weekday_average_daily_sales": {"type": "number", "format": "double"}, "weekday_order_type_meal_segment_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisOrderTypeMealSegment"}}, "weekday_meal_segment_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisResponseStorePerformanceAnalysisMealSegment"}}, "weekday_channel_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisOrderType"}}, "weekend_average_daily_sales": {"type": "number", "format": "double"}, "weekend_order_type_meal_segment_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisOrderTypeMealSegment"}}, "weekend_meal_segment_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisResponseStorePerformanceAnalysisMealSegment"}}, "weekend_channel_turnovers": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisOrderType"}}}}, "StorePerformanceAnalysisResponseStorePerformanceAnalysisMealSegment": {"type": "object", "properties": {"meal_segment_name": {"type": "string"}, "bowl_turnover": {"type": "number", "format": "double"}}}, "attribute_nameattribute_name_value": {"type": "object", "properties": {"attribute_value_code": {"type": "string"}, "attribute_value_name": {"type": "string"}, "product_count": {"type": "string", "format": "int64"}, "percent_product_count": {"type": "number", "format": "double"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n    // or ...\n    if (any.isSameTypeAs(Foo.getDefaultInstance())) {\n      foo = any.unpack(Foo.getDefaultInstance());\n    }\n\nExample 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\nExample 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\nJSON\n\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "sales_reportAdjustResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/AdjustResponseAdjustRow"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportBusinessReportResponse": {"type": "object", "properties": {"extend": {"type": "string"}}}, "sales_reportBusinessSituationResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/BusinessSituationResponseBusinessSituation"}}, "compare_rows": {"type": "array", "items": {"$ref": "#/definitions/BusinessSituationResponseBusinessSituation"}}, "compare_same_rows": {"type": "array", "items": {"$ref": "#/definitions/BusinessSituationResponseBusinessSituation"}}}}, "sales_reportCategoryReportResponse": {"type": "object", "properties": {"extend": {"type": "string", "format": "byte"}}}, "sales_reportCategorySalesResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/CategorySalesResponseCategorySales"}}, "summary": {"$ref": "#/definitions/CategorySalesResponseCategorySales"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportChannelDistributeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ChannelDistributeResponseChannelDistribute"}}, "compare_rows": {"type": "array", "items": {"$ref": "#/definitions/ChannelDistributeResponseChannelDistribute"}}, "compare_same_rows": {"type": "array", "items": {"$ref": "#/definitions/ChannelDistributeResponseChannelDistribute"}}, "summary": {"$ref": "#/definitions/ChannelDistributeResponseChannelDistribute"}}}, "sales_reportCommonRequest": {"type": "object", "properties": {"limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "sort": {"type": "string"}, "order": {"type": "string"}, "include_total": {"type": "boolean"}, "include_summary": {"type": "boolean"}, "is_today": {"type": "boolean"}, "is_pre": {"type": "boolean"}, "period_group_type": {"type": "string"}, "start": {"type": "string"}, "end": {"type": "string"}, "region_group_type": {"type": "string"}, "region_group_level": {"type": "integer", "format": "int32"}, "region_search_type": {"type": "string"}, "region_search_ids": {"type": "array", "items": {"type": "string"}}, "product_category_ids": {"type": "array", "items": {"type": "string"}}, "product_ids": {"type": "array", "items": {"type": "string"}}, "payment_ids": {"type": "array", "items": {"type": "string"}}, "discount_ids": {"type": "array", "items": {"type": "string"}}, "channel_id": {"type": "string"}, "order_type": {"type": "string"}, "lan": {"type": "string"}, "oauth_region_type": {"type": "string"}, "store_type": {"type": "string"}, "open_status": {"type": "string"}, "tag_type": {"type": "string"}, "channels": {"type": "array", "items": {"type": "string"}}, "logistics": {"type": "array", "items": {"type": "string"}}, "category0": {"type": "string"}, "category1": {"type": "string"}, "category2": {"type": "string"}, "refund_code": {"type": "string"}, "refund_side": {"type": "string", "format": "int64"}, "period": {"type": "array", "items": {"type": "string", "format": "int64"}}, "price_scope": {"type": "string"}, "compare_start": {"type": "string"}, "compare_end": {"type": "string"}, "compare_same_start": {"type": "string"}, "compare_same_end": {"type": "string"}, "channel_ids": {"type": "array", "items": {"type": "string"}}, "is_combo": {"type": "boolean"}, "tag_types": {"type": "string"}, "store_types": {"type": "array", "items": {"type": "string"}}, "is_natural": {"type": "boolean"}, "taxes": {"type": "array", "items": {"type": "number", "format": "double"}}, "order_status": {"type": "string"}, "product_id": {"type": "string"}, "code": {"type": "string"}, "store_tags": {"type": "array", "items": {"type": "string"}}, "include_real_amount_zero": {"type": "boolean"}, "region_codes": {"type": "array", "items": {"type": "string"}}, "coupon_channels": {"type": "array", "items": {"type": "string"}}, "combo_product_ids": {"type": "array", "items": {"type": "string"}}, "search": {"type": "string"}, "ticket_no": {"type": "string"}, "timezone": {"type": "string"}, "product_attribute_codes": {"type": "array", "items": {"type": "string"}}}}, "sales_reportCommonResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"type": "object"}}, "summary": {"type": "object"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportCustomerFormResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/CustomerFormResponseCustomerForm"}}, "compare_rows": {"type": "array", "items": {"$ref": "#/definitions/CustomerFormResponseCustomerForm"}}, "compare_same_rows": {"type": "array", "items": {"$ref": "#/definitions/CustomerFormResponseCustomerForm"}}, "summary": {"$ref": "#/definitions/CustomerFormResponseCustomerForm"}}}, "sales_reportDailyRequest": {"type": "object", "properties": {"busDate": {"type": "string"}, "start_time": {"type": "string"}, "end_time": {"type": "string"}, "host": {"type": "string"}}}, "sales_reportDailyResponse": {"type": "object", "properties": {"extend": {"type": "string"}}}, "sales_reportDashboardRequest": {"type": "object", "properties": {"is_today": {"type": "boolean"}, "is_pre": {"type": "boolean"}, "start": {"type": "string", "format": "date-time"}, "end": {"type": "string", "format": "date-time"}, "region_search_ids": {"type": "array", "items": {"type": "string"}}, "panel": {"type": "array", "items": {"type": "string"}}, "lan": {"type": "string"}}}, "sales_reportDashboardResponse": {"type": "object", "properties": {"summary": {"type": "object"}, "line_chart": {"type": "object"}, "store_rank": {"type": "array", "items": {"type": "object"}}, "channel_rank": {"type": "array", "items": {"type": "object"}}, "product_rank": {"type": "array", "items": {"type": "object"}}}}, "sales_reportDetailForDiscountAndRealAmountResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/DetailForDiscountAndRealAmountResponseDetail"}}}}, "sales_reportDiscountSalesResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/DiscountSalesResponseDiscountSales"}}, "summary": {"$ref": "#/definitions/DiscountSalesResponseDiscountSales"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportDiscountTopNRankResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/DiscountTopNRankResponseDiscountTopNRank"}}, "summary": {"$ref": "#/definitions/DiscountTopNRankResponseDiscountTopNRank"}}}, "sales_reportFinancialResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/FinancialResponseFinancial"}}, "summary": {"$ref": "#/definitions/FinancialResponseFinancial"}}}, "sales_reportHourReportResponse": {"type": "object", "properties": {"extend": {"type": "string", "format": "byte"}}}, "sales_reportHubReportQuery": {"type": "object", "properties": {"start_time": {"type": "string"}, "end_time": {"type": "string"}, "operator": {"type": "string"}, "is_hour": {"type": "boolean"}, "is_meal": {"type": "boolean"}, "query": {"type": "array", "items": {"type": "string"}}, "channel": {"type": "array", "items": {"type": "string"}}, "host": {"type": "string"}, "bussinessDayStartTime": {"type": "string"}}}, "sales_reportImportRequest": {"type": "object", "properties": {"file": {"type": "string"}, "file_name": {"type": "string"}}}, "sales_reportImportResponse": {"type": "object", "properties": {"msg": {"type": "string"}}}, "sales_reportInboundResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/InboundResponseInboundRow"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportLogRankResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/LogRankResponseLogRank"}}, "compare_rows": {"type": "array", "items": {"$ref": "#/definitions/LogRankResponseLogRank"}}, "compare_same_rows": {"type": "array", "items": {"$ref": "#/definitions/LogRankResponseLogRank"}}, "summary": {"$ref": "#/definitions/LogRankResponseLogRank"}}}, "sales_reportMealSegmentsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"type": "string"}}}}, "sales_reportOpenFinancialRequest": {"type": "object", "properties": {"start_date": {"type": "string"}, "end_date": {"type": "string"}, "summary": {"type": "boolean"}}}, "sales_reportOpenFinancialResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/OpenFinancialResponseOpenFinancialReportStore"}}}}, "sales_reportOpenProductRequest": {"type": "object", "properties": {"start_date": {"type": "string"}, "end_date": {"type": "string"}, "store_id": {"type": "string", "format": "int64"}, "product_id": {"type": "string", "format": "int64"}, "channel_id": {"type": "string", "format": "int64"}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "total": {"type": "boolean"}}}, "sales_reportOpenProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/OpenProductResponseOpenProductReport"}}, "total": {"type": "integer", "format": "int32"}}}, "sales_reportOrderTypeRequest": {"type": "object", "properties": {"start_time": {"type": "string"}, "end_time": {"type": "string"}, "host": {"type": "string"}}}, "sales_reportOrderTypeResponse": {"type": "object", "properties": {"extend": {"type": "string"}}}, "sales_reportPaymentDetailReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PaymentDetailReportResponsePaymentDetailReportRow"}}, "total": {"type": "string", "format": "int64"}, "period_start_date": {"type": "string", "title": "导出专用//"}, "period_end_date": {"type": "string"}, "period_date_text": {"type": "string"}}}, "sales_reportPaymentPeriodResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriod"}}, "summary": {"$ref": "#/definitions/PaymentPeriodResponsePaymentPeriod"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportPaymentStatisticsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PaymentStatisticsResponsePaymentStatistics"}}, "summary": {"$ref": "#/definitions/PaymentStatisticsResponsePaymentStatistics"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportPeriodNewResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PeriodNewResponsePeriodNewRow"}}}}, "sales_reportPettyCashDailySettlementReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PettyCashDailySettlementReportResponsePettyCashDailySettlementReportRow"}}, "total": {"type": "string", "format": "int64"}, "period_start_date": {"type": "string", "title": "导出专用//"}, "period_end_date": {"type": "string"}, "period_date_text": {"type": "string"}, "payout_detail_summary": {"type": "object", "title": "支出明细汇总"}, "payout_amount_summary": {"type": "string"}}}, "sales_reportPosDepartmentBusinessResponse": {"type": "object", "properties": {"summary": {"$ref": "#/definitions/PosDepartmentBusinessResponseDepartment"}, "department_list": {"type": "array", "items": {"$ref": "#/definitions/PosDepartmentBusinessResponseDepartment"}}}}, "sales_reportPosInboundResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/PosInboundResponsePosInboundRow"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportPosProductSalesReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/sales_reportPosProductSalesReportResponseRow"}}}}, "sales_reportPosProductSalesReportResponseRow": {"type": "object", "properties": {"product_sales_list": {"type": "array", "items": {"$ref": "#/definitions/PosProductSalesReportResponseProductSales"}}, "summary": {"$ref": "#/definitions/PosProductSalesReportResponseProductSales"}}}, "sales_reportPosPromotionResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/sales_reportPosPromotionResponseRow"}}, "summary": {"$ref": "#/definitions/sales_reportPosPromotionResponseRow"}}}, "sales_reportPosPromotionResponseRow": {"type": "object", "properties": {"promotion_name": {"type": "string", "title": "促销名称"}, "promotion_cate_name": {"type": "string", "title": "促销类型名称"}, "discount": {"type": "number", "format": "double", "title": "优惠金额"}, "discount_percent": {"type": "number", "format": "double", "title": "优惠金额占比"}, "usage_count": {"type": "number", "format": "double", "title": "使用次数"}}}, "sales_reportPosStorePeriodReportResponse": {"type": "object", "properties": {"period_list": {"type": "array", "items": {"$ref": "#/definitions/PosStorePeriodReportResponsePeriod"}}, "summary": {"$ref": "#/definitions/PosStorePeriodReportResponsePeriod"}}}, "sales_reportProductAttributeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductAttributeResponseProductAttribute"}}, "graphs": {"type": "array", "items": {"$ref": "#/definitions/ProductAttributeResponseProductAttribute"}}}}, "sales_reportProductAttributeSalesResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductAttributeSalesResponseProductAttributeSalesRow"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductChannelResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductChannelResponseProductChannel"}}, "summary": {"$ref": "#/definitions/ProductChannelResponseProductChannel"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductDistributeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductDistributeResponseProductDistribute"}}, "summary": {"$ref": "#/definitions/ProductDistributeResponseProductDistribute"}}}, "sales_reportProductFlavorResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductFlavorResponseProductFlavor"}}, "graph": {"type": "array", "items": {"$ref": "#/definitions/ProductFlavorResponseProductFlavor"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductMakeTimeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductMakeTimeResponseProductMakeTimeItem"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductPeriodResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductPeriodResponseProductPeriod"}}, "summary": {"$ref": "#/definitions/ProductPeriodResponseProductPeriod"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductPolymerResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductPolymerResponseProductPolymer"}}, "summary": {"$ref": "#/definitions/ProductPolymerResponseProductPolymer"}}}, "sales_reportProductReportResponse": {"type": "object", "properties": {"id": {"type": "string"}, "channel": {"type": "array", "items": {"$ref": "#/definitions/sales_reportProductReportResponseProduct"}}, "sum": {"$ref": "#/definitions/ProductReportResponseSumProduct"}}}, "sales_reportProductReportResponseProduct": {"type": "object", "properties": {"name": {"type": "string"}, "sum_amount": {"type": "number", "format": "double"}, "sum_net_amount": {"type": "number", "format": "double"}, "quantity": {"type": "integer", "format": "int32"}, "has_weight": {"type": "boolean"}, "weight": {"type": "number", "format": "double"}, "tp_name": {"type": "string"}, "taxAmount": {"type": "number", "format": "double"}}}, "sales_reportProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/sales_reportProductResponseProduct"}}, "summary": {"$ref": "#/definitions/sales_reportProductResponseProduct"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductResponseProduct": {"type": "object", "properties": {"bus_date": {"type": "string"}, "region_id": {"type": "string", "format": "int64"}, "region_code": {"type": "string"}, "region_name": {"type": "string"}, "region_address": {"type": "string"}, "store_type": {"type": "string"}, "store_type_name": {"type": "string"}, "business_days": {"type": "string", "format": "int64"}, "gross_amount": {"type": "number", "format": "double"}, "gross_amount_returned": {"type": "number", "format": "double"}, "net_amount": {"type": "number", "format": "double"}, "net_amount_returned": {"type": "number", "format": "double"}, "discount_amount": {"type": "number", "format": "double"}, "discount_amount_returned": {"type": "number", "format": "double"}, "item_count": {"type": "string", "format": "int64"}, "item_count_returned": {"type": "string", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "region_alias": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductResponseProductData"}}, "tax_fee": {"type": "number", "format": "double"}}}, "sales_reportProductSalesComboDetailResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductSalesComboDetailResponseProductSalesComboDetail"}}, "summary": {"$ref": "#/definitions/ProductSalesComboDetailResponseProductSalesComboDetail"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductSalesComboSummaryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductSalesComboSummaryResponseProductSalesComboSummary"}}, "summary": {"$ref": "#/definitions/ProductSalesComboSummaryResponseProductSalesComboSummary"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductSalesDetailResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductSalesDetailResponseProductSalesDetail"}}, "summary": {"$ref": "#/definitions/ProductSalesDetailResponseProductSalesDetail"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductSalesSummaryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductSalesSummaryResponseProductSalesSummary"}}, "summary": {"$ref": "#/definitions/ProductSalesSummaryResponseProductSalesSummary"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportProductStoreRankResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductStoreRankResponseProductStoreRank"}}, "summary": {"$ref": "#/definitions/ProductStoreRankResponseProductStoreRank"}}}, "sales_reportProductTaxResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductTaxResponseProductTaxSales"}}, "summary": {"$ref": "#/definitions/ProductTaxResponseProductTaxSales"}, "total": {"type": "string", "format": "int64"}, "send_fees": {"type": "array", "items": {"$ref": "#/definitions/ProductTaxResponseProductTaxSales"}}, "package_fees": {"type": "array", "items": {"$ref": "#/definitions/ProductTaxResponseProductTaxSales"}}}}, "sales_reportProductTop20RankResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductTop20RankResponseProductTop20Rank"}}, "summary": {"$ref": "#/definitions/ProductTop20RankResponseProductTop20Rank"}}}, "sales_reportProductTopNRankResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ProductTopNRankResponseProductTopNRank"}}, "summary": {"$ref": "#/definitions/ProductTopNRankResponseProductTopNRank"}}}, "sales_reportQueryReportConfigRequest": {"type": "object", "properties": {"type": {"type": "string"}}}, "sales_reportRecentSummaryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/RecentSummaryResponseRecentSummaryItem"}}, "summary": {"$ref": "#/definitions/RecentSummaryResponseRecentSummaryItem"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportRefundAnalysisResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/RefundAnalysisResponseRefundAnalysis"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportReportConfigResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "partner_id": {"type": "string", "format": "int64"}, "user_id": {"type": "string", "format": "int64"}, "type": {"type": "string"}, "config": {"type": "string"}}}, "sales_reportSaveReportConfigRequest": {"type": "object", "properties": {"type": {"type": "string"}, "config": {"type": "string"}}}, "sales_reportShiftInfoResponse": {"type": "object", "properties": {"extend": {"type": "string"}}}, "sales_reportShiftReportRequest": {"type": "object", "properties": {"shiftNumber": {"type": "string"}}}, "sales_reportShopKeeperDashBoardResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ShopKeeperDashBoardResponseDashboard"}}}}, "sales_reportShopkeeperProductPeriodResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ShopkeeperProductPeriodResponseShopkeeperProductPeriodRow"}}, "period_summary": {"type": "array", "items": {"$ref": "#/definitions/ShopkeeperProductPeriodResponseShopkeeperProductPeriodItem"}}, "summary": {"$ref": "#/definitions/ShopkeeperProductPeriodResponseShopkeeperProductPeriodItem"}, "total_rows": {"type": "string", "format": "int64"}}}, "sales_reportStoreBusinessResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/StoreBusinessResponseStoreBusinessItem"}}}}, "sales_reportStoreChannelSalesResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/StoreChannelSalesResponseStoreChannelSales"}}, "summary": {"$ref": "#/definitions/StoreChannelSalesResponseStoreChannelSales"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportStoreCompensationSalesItemRow": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "ticket_no": {"type": "string", "title": "账单号（取餐号码）"}, "end_time": {"type": "string", "format": "date-time", "title": "结账时间"}, "compensation_category_id": {"type": "string", "format": "uint64", "title": "类别编码(支付方式/促销方式ID)"}, "compensation_category_code": {"type": "string", "title": "类别编码(支付方式/促销方式code)"}, "compensation_category_desc": {"type": "string", "title": "类别描述(支付方式/促销方式名称)"}, "main_seq": {"type": "string", "format": "uint64", "title": "item主商品排序"}, "seq": {"type": "string", "format": "uint64", "title": "item在一个主商品内的排序"}, "parent_id": {"type": "string", "format": "uint64", "title": "父级id,如果是0，代表是主商品"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "product_code": {"type": "string", "title": "商品编码"}, "qty": {"type": "string", "title": "商品数量"}, "net_amount": {"type": "string", "title": "账单金额(订单级别）"}, "non_sales_amount": {"type": "string", "title": "非销售金额(订单级别）"}, "item_amount": {"type": "string", "title": "item_amount（item级别）"}}}, "sales_reportStoreCompensationSalesReportReq": {"type": "object", "properties": {"start": {"type": "string", "format": "date-time", "title": "营业开始时间"}, "end": {"type": "string", "format": "date-time", "title": "营业结束时间"}, "payment_ids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "支付方式"}, "promotion_ids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "促销方式（预留）"}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "sort": {"type": "string"}, "order": {"type": "string"}, "query_type": {"$ref": "#/definitions/StoreCompensationSalesReportReqqueryType"}, "region_search_type": {"type": "string"}, "region_search_ids": {"type": "array", "items": {"type": "string", "format": "int64"}}}}, "sales_reportStoreCompensationSalesReportResp": {"type": "object", "properties": {"summary_row": {"type": "array", "items": {"$ref": "#/definitions/sales_reportStoreCompensationSalesReportRow"}}, "summary_total": {"type": "string", "format": "int64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/sales_reportStoreCompensationSalesItemRow"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportStoreCompensationSalesReportRow": {"type": "object", "properties": {"bus_date": {"type": "string", "title": "营业日期"}, "store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "ticket_no": {"type": "string", "title": "账单号（取餐号码）"}, "end_time": {"type": "string", "format": "date-time", "title": "结账时间"}, "compensation_category_id": {"type": "string", "format": "uint64", "title": "类别编码(支付方式/促销方式ID)"}, "compensation_category_code": {"type": "string", "title": "类别编码(支付方式/促销方式code)"}, "compensation_category_desc": {"type": "string", "title": "类别描述(支付方式/促销方式名称)"}, "net_amount": {"type": "string", "title": "账单金额"}, "non_sales_amount": {"type": "string", "title": "非销售金额"}}}, "sales_reportStoreMakeTimeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/StoreMakeTimeResponseStoreMakeTimeItem"}}, "total": {"type": "string", "format": "int64"}}}, "sales_reportStorePerformanceAnalysisResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/StorePerformanceAnalysisResponseStorePerformanceAnalysis"}}, "summary": {"$ref": "#/definitions/StorePerformanceAnalysisResponseStorePerformanceAnalysis"}, "total": {"type": "string", "format": "int64"}}}, "sales_reportTestingResponse": {"type": "object", "properties": {"delay_seconds": {"type": "integer", "format": "int32"}}}, "sales_reportUpdateTimeRequest": {"type": "object"}, "sales_reportUpdateTimeResponse": {"type": "object", "properties": {"datetime": {"type": "string"}}}}}