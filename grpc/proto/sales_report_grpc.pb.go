// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v4.22.4
// source: grpc/proto/sales_report.proto

package sales_report

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SalesReportClient is the client API for SalesReport service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SalesReportClient interface {
	// 拨测
	Testing(ctx context.Context, in *TestingRequest, opts ...grpc.CallOption) (*TestingResponse, error)
	// 门店营业情况
	StoreBusiness(ctx context.Context, in *StoreBusinessRequest, opts ...grpc.CallOption) (*StoreBusinessResponse, error)
	// 报表数据更新时间
	UpdateTime(ctx context.Context, in *UpdateTimeRequest, opts ...grpc.CallOption) (*UpdateTimeResponse, error)
	// 首页报表
	Dashboard(ctx context.Context, in *DashboardRequest, opts ...grpc.CallOption) (*DashboardResponse, error)
	// 门店销售报表
	StoreSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 商品销售报表
	ProductSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductResponse, error)
	// 退单原因分析表
	RefundAnalysis(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RefundAnalysisResponse, error)
	// 类别销售报表
	CategorySales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CategorySalesResponse, error)
	// 支付统计报表
	PaymentStatistics(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentStatisticsResponse, error)
	// 折扣销售报表
	DiscountSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DiscountSalesResponse, error)
	// 门店渠道报表
	StoreChannelSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StoreChannelSalesResponse, error)
	// 商品渠道报表
	ProductChannelSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductChannelResponse, error)
	// 商品计税表
	ProductTaxSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTaxResponse, error)
	// 门店时段报表
	StorePeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 商品时段报表
	ProductPeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductPeriodResponse, error)
	// 支付时段报表
	PaymentPeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentPeriodResponse, error)
	// 近期营业汇总表
	RecentSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RecentSummaryResponse, error)
	// 财务报表
	FinancialReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*FinancialResponse, error)
	// 收银汇总数据
	OpenFinancialReport(ctx context.Context, in *OpenFinancialRequest, opts ...grpc.CallOption) (*OpenFinancialResponse, error)
	// 收银汇总数据
	OpenProductReport(ctx context.Context, in *OpenProductRequest, opts ...grpc.CallOption) (*OpenProductResponse, error)
	// 商品属性销售表
	ProductFlavorSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductFlavorResponse, error)
	// 单杯出杯时间
	ProductMakeTime(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductMakeTimeResponse, error)
	// 订单完成时间
	StoreMakeTime(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StoreMakeTimeResponse, error)
	// 报损报表
	Adjust(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*AdjustResponse, error)
	// 财务报表.营业状况
	FinanceBusinessStatus(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 财务报表.物流构成
	FinanceLogisticsForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 财务报表.外卖渠道
	FinanceChannelForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 财务报表.实收组成
	FinanceNetReceipts(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 财务报表.优惠组成
	FinanceDiscount(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 财务报表.菜品收入
	FinanceProduct(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.首页面板
	ShopKeeperDashboard(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ShopKeeperDashBoardResponse, error)
	// 小掌柜.营业额
	ShopKeeperBusiness(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*BusinessSituationResponse, error)
	// 小掌柜.实收金额.商品分类
	ShopKeeperBusCategory(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.实收金额.商品排行
	ShopKeeperBusProduct(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.渠道收入趋势
	ShopKeeperChaTrend(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.渠道收入
	ShopKeeperChaIncome(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ChannelDistributeResponse, error)
	// 小掌柜.物流收入趋势
	ShopKeeperLogTrend(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.营业时段
	ShopKeeperPeriod(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.菜品收入
	ShopKeeperProRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.物流占比/物流收入
	ShopKeeperLogRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*LogRankResponse, error)
	// 小掌柜.各平台补贴
	ShopKeeperPlaDiscount(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.月收入趋势
	ShopKeeperIncome(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.折扣分类
	ShopKeeperDisCategory(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.折扣渠道
	ShopKeeperDisChannel(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.有效订单和退单
	ShopKeeperChaOrder(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.门店营业额Top10排行
	ShopKeeperBusinessRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error)
	// 小掌柜.商品分布
	ShopKeeperProDistribute(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductDistributeResponse, error)
	// 小掌柜.财务实收和优惠组成下钻
	ShopKeeperDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DetailForDiscountAndRealAmountResponse, error)
	// 小掌柜.商品Top20排行
	ShopKeeperProductTop20Rank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTop20RankResponse, error)
	// 小掌柜.商品属性下钻
	ShopKeeperProductAttribute(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductAttributeResponse, error)
	// 小掌柜.客流构成
	ShopKeeperCustomerForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CustomerFormResponse, error)
	// 小掌柜.支付统计
	ShopKeeperPaymentStatistics(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentStatisticsResponse, error)
	// 小掌柜.统计渠道商品数据
	ShopKeeperProductPolymer(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductPolymerResponse, error)
	// 小掌柜.商品时段报表
	ShopKeeperProductPeriod(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ShopkeeperProductPeriodResponse, error)
	// 小掌柜.近期营业汇总表
	ShopkeeperRecentSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RecentSummaryResponse, error)
	// 5.5 商品排行
	ProductTopNRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTopNRankResponse, error)
	// 某商品各门店销售数据
	ProductStoreRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductStoreRankResponse, error)
	// 大掌柜报表配置查询
	ShopkeeperQueryReportConfig(ctx context.Context, in *QueryReportConfigRequest, opts ...grpc.CallOption) (*ReportConfigResponse, error)
	// 大掌柜报表配置保存
	ShopkeeperSaveReportConfig(ctx context.Context, in *SaveReportConfigRequest, opts ...grpc.CallOption) (*ReportConfigResponse, error)
	// pos时段报表
	PosHourReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*HourReportResponse, error)
	// pos时段报表(新)
	PosStorePeriodReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosStorePeriodReportResponse, error)
	// pos类别报表
	PosCategoryReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*CategoryReportResponse, error)
	// pos商品报表
	PosProductReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*ProductReportResponse, error)
	// pos商品销售报表(新)
	PosProductSalesReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosProductSalesReportResponse, error)
	// pos营业报表
	PosBusinessReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*BusinessReportResponse, error)
	// pos交班报表
	PosShiftReport(ctx context.Context, in *ShiftReportRequest, opts ...grpc.CallOption) (*ShiftInfoResponse, error)
	// Pos 促销报表
	PosPromotion(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PosPromotionResponse, error)
	// posr日结报表
	PosDailyReport(ctx context.Context, in *DailyRequest, opts ...grpc.CallOption) (*DailyResponse, error)
	// pos 就餐方式
	PosOrderTypeReport(ctx context.Context, in *OrderTypeRequest, opts ...grpc.CallOption) (*OrderTypeResponse, error)
	// pos部门营业报表
	PosDepartmentBusinessReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosDepartmentBusinessResponse, error)
	// 菜品销售汇总表
	ProductSalesSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesSummaryResponse, error)
	// 菜品销售明细表表
	ProductSalesDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesDetailResponse, error)
	// 门店表现分析
	StorePerformanceAnalysis(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StorePerformanceAnalysisResponse, error)
	// 餐段接口
	MealSegments(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*MealSegmentsResponse, error)
	// 菜品销售汇总表
	ProductSalesComboSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesComboSummaryResponse, error)
	// 菜品销售明细表表
	ProductSalesComboDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesComboDetailResponse, error)
	// 导入门店目标
	ImportStoreGoals(ctx context.Context, in *ImportRequest, opts ...grpc.CallOption) (*ImportResponse, error)
	// 导入汇率
	ImportExchangeRates(ctx context.Context, in *ImportRequest, opts ...grpc.CallOption) (*ImportResponse, error)
	// Pos 商品入库报表
	PosInboundReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PosInboundResponse, error)
	// Pos 商品入库报表
	InboundReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*InboundResponse, error)
	// 大掌柜折扣排行
	ShopkeeperDiscountTopNRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DiscountTopNRankResponse, error)
	// 大掌柜新时段报表
	ShopkeeperPeriodNew(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PeriodNewResponse, error)
	// 财务报表报表-零用现金日结报表
	PettyCashDailySettlementReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PettyCashDailySettlementReportResponse, error)
	// 支付明细
	PaymentDetailReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentDetailReportResponse, error)
	// 商品属性销量统计表
	ProductAttributeSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductAttributeSalesResponse, error)
	// 非营运销售报表
	StoreCompensationSalesReport(ctx context.Context, in *StoreCompensationSalesReportReq, opts ...grpc.CallOption) (*StoreCompensationSalesReportResp, error)
}

type salesReportClient struct {
	cc grpc.ClientConnInterface
}

func NewSalesReportClient(cc grpc.ClientConnInterface) SalesReportClient {
	return &salesReportClient{cc}
}

func (c *salesReportClient) Testing(ctx context.Context, in *TestingRequest, opts ...grpc.CallOption) (*TestingResponse, error) {
	out := new(TestingResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/Testing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StoreBusiness(ctx context.Context, in *StoreBusinessRequest, opts ...grpc.CallOption) (*StoreBusinessResponse, error) {
	out := new(StoreBusinessResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StoreBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) UpdateTime(ctx context.Context, in *UpdateTimeRequest, opts ...grpc.CallOption) (*UpdateTimeResponse, error) {
	out := new(UpdateTimeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/UpdateTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) Dashboard(ctx context.Context, in *DashboardRequest, opts ...grpc.CallOption) (*DashboardResponse, error) {
	out := new(DashboardResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/Dashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StoreSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StoreSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductResponse, error) {
	out := new(ProductResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) RefundAnalysis(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RefundAnalysisResponse, error) {
	out := new(RefundAnalysisResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/RefundAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) CategorySales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CategorySalesResponse, error) {
	out := new(CategorySalesResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/CategorySales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PaymentStatistics(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentStatisticsResponse, error) {
	out := new(PaymentStatisticsResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PaymentStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) DiscountSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DiscountSalesResponse, error) {
	out := new(DiscountSalesResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/DiscountSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StoreChannelSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StoreChannelSalesResponse, error) {
	out := new(StoreChannelSalesResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StoreChannelSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductChannelSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductChannelResponse, error) {
	out := new(ProductChannelResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductChannelSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductTaxSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTaxResponse, error) {
	out := new(ProductTaxResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductTaxSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StorePeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StorePeriodSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductPeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductPeriodResponse, error) {
	out := new(ProductPeriodResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductPeriodSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PaymentPeriodSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentPeriodResponse, error) {
	out := new(PaymentPeriodResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PaymentPeriodSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) RecentSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RecentSummaryResponse, error) {
	out := new(RecentSummaryResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/RecentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinancialReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*FinancialResponse, error) {
	out := new(FinancialResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinancialReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) OpenFinancialReport(ctx context.Context, in *OpenFinancialRequest, opts ...grpc.CallOption) (*OpenFinancialResponse, error) {
	out := new(OpenFinancialResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/OpenFinancialReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) OpenProductReport(ctx context.Context, in *OpenProductRequest, opts ...grpc.CallOption) (*OpenProductResponse, error) {
	out := new(OpenProductResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/OpenProductReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductFlavorSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductFlavorResponse, error) {
	out := new(ProductFlavorResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductFlavorSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductMakeTime(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductMakeTimeResponse, error) {
	out := new(ProductMakeTimeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductMakeTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StoreMakeTime(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StoreMakeTimeResponse, error) {
	out := new(StoreMakeTimeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StoreMakeTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) Adjust(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*AdjustResponse, error) {
	out := new(AdjustResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/Adjust", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceBusinessStatus(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceBusinessStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceLogisticsForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceLogisticsForm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceChannelForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceChannelForm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceNetReceipts(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceNetReceipts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceDiscount(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) FinanceProduct(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/FinanceProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperDashboard(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ShopKeeperDashBoardResponse, error) {
	out := new(ShopKeeperDashBoardResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperDashboard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperBusiness(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*BusinessSituationResponse, error) {
	out := new(BusinessSituationResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperBusCategory(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperBusCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperBusProduct(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperBusProduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperChaTrend(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperChaTrend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperChaIncome(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ChannelDistributeResponse, error) {
	out := new(ChannelDistributeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperChaIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperLogTrend(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperLogTrend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperPeriod(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperPeriod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperLogRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*LogRankResponse, error) {
	out := new(LogRankResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperLogRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperPlaDiscount(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperPlaDiscount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperIncome(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperDisCategory(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperDisCategory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperDisChannel(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperDisChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperChaOrder(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperChaOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperBusinessRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CommonResponse, error) {
	out := new(CommonResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperBusinessRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProDistribute(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductDistributeResponse, error) {
	out := new(ProductDistributeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProDistribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DetailForDiscountAndRealAmountResponse, error) {
	out := new(DetailForDiscountAndRealAmountResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProductTop20Rank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTop20RankResponse, error) {
	out := new(ProductTop20RankResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProductTop20Rank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProductAttribute(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductAttributeResponse, error) {
	out := new(ProductAttributeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProductAttribute", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperCustomerForm(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*CustomerFormResponse, error) {
	out := new(CustomerFormResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperCustomerForm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperPaymentStatistics(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentStatisticsResponse, error) {
	out := new(PaymentStatisticsResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperPaymentStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProductPolymer(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductPolymerResponse, error) {
	out := new(ProductPolymerResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProductPolymer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopKeeperProductPeriod(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ShopkeeperProductPeriodResponse, error) {
	out := new(ShopkeeperProductPeriodResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopKeeperProductPeriod", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopkeeperRecentSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*RecentSummaryResponse, error) {
	out := new(RecentSummaryResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopkeeperRecentSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductTopNRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductTopNRankResponse, error) {
	out := new(ProductTopNRankResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductTopNRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductStoreRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductStoreRankResponse, error) {
	out := new(ProductStoreRankResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductStoreRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopkeeperQueryReportConfig(ctx context.Context, in *QueryReportConfigRequest, opts ...grpc.CallOption) (*ReportConfigResponse, error) {
	out := new(ReportConfigResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopkeeperQueryReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopkeeperSaveReportConfig(ctx context.Context, in *SaveReportConfigRequest, opts ...grpc.CallOption) (*ReportConfigResponse, error) {
	out := new(ReportConfigResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopkeeperSaveReportConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosHourReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*HourReportResponse, error) {
	out := new(HourReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosHourReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosStorePeriodReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosStorePeriodReportResponse, error) {
	out := new(PosStorePeriodReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosStorePeriodReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosCategoryReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*CategoryReportResponse, error) {
	out := new(CategoryReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosCategoryReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosProductReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*ProductReportResponse, error) {
	out := new(ProductReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosProductReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosProductSalesReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosProductSalesReportResponse, error) {
	out := new(PosProductSalesReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosProductSalesReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosBusinessReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*BusinessReportResponse, error) {
	out := new(BusinessReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosBusinessReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosShiftReport(ctx context.Context, in *ShiftReportRequest, opts ...grpc.CallOption) (*ShiftInfoResponse, error) {
	out := new(ShiftInfoResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosShiftReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosPromotion(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PosPromotionResponse, error) {
	out := new(PosPromotionResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosDailyReport(ctx context.Context, in *DailyRequest, opts ...grpc.CallOption) (*DailyResponse, error) {
	out := new(DailyResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosDailyReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosOrderTypeReport(ctx context.Context, in *OrderTypeRequest, opts ...grpc.CallOption) (*OrderTypeResponse, error) {
	out := new(OrderTypeResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosOrderTypeReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosDepartmentBusinessReport(ctx context.Context, in *HubReportQuery, opts ...grpc.CallOption) (*PosDepartmentBusinessResponse, error) {
	out := new(PosDepartmentBusinessResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosDepartmentBusinessReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductSalesSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesSummaryResponse, error) {
	out := new(ProductSalesSummaryResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductSalesSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductSalesDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesDetailResponse, error) {
	out := new(ProductSalesDetailResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductSalesDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StorePerformanceAnalysis(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*StorePerformanceAnalysisResponse, error) {
	out := new(StorePerformanceAnalysisResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StorePerformanceAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) MealSegments(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*MealSegmentsResponse, error) {
	out := new(MealSegmentsResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/MealSegments", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductSalesComboSummary(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesComboSummaryResponse, error) {
	out := new(ProductSalesComboSummaryResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductSalesComboSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductSalesComboDetail(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductSalesComboDetailResponse, error) {
	out := new(ProductSalesComboDetailResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductSalesComboDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ImportStoreGoals(ctx context.Context, in *ImportRequest, opts ...grpc.CallOption) (*ImportResponse, error) {
	out := new(ImportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ImportStoreGoals", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ImportExchangeRates(ctx context.Context, in *ImportRequest, opts ...grpc.CallOption) (*ImportResponse, error) {
	out := new(ImportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ImportExchangeRates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PosInboundReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PosInboundResponse, error) {
	out := new(PosInboundResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PosInboundReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) InboundReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*InboundResponse, error) {
	out := new(InboundResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/InboundReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopkeeperDiscountTopNRank(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*DiscountTopNRankResponse, error) {
	out := new(DiscountTopNRankResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopkeeperDiscountTopNRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ShopkeeperPeriodNew(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PeriodNewResponse, error) {
	out := new(PeriodNewResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ShopkeeperPeriodNew", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PettyCashDailySettlementReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PettyCashDailySettlementReportResponse, error) {
	out := new(PettyCashDailySettlementReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PettyCashDailySettlementReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) PaymentDetailReport(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*PaymentDetailReportResponse, error) {
	out := new(PaymentDetailReportResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/PaymentDetailReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) ProductAttributeSales(ctx context.Context, in *CommonRequest, opts ...grpc.CallOption) (*ProductAttributeSalesResponse, error) {
	out := new(ProductAttributeSalesResponse)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/ProductAttributeSales", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *salesReportClient) StoreCompensationSalesReport(ctx context.Context, in *StoreCompensationSalesReportReq, opts ...grpc.CallOption) (*StoreCompensationSalesReportResp, error) {
	out := new(StoreCompensationSalesReportResp)
	err := c.cc.Invoke(ctx, "/sales_report.SalesReport/StoreCompensationSalesReport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SalesReportServer is the server API for SalesReport service.
// All implementations must embed UnimplementedSalesReportServer
// for forward compatibility
type SalesReportServer interface {
	// 拨测
	Testing(context.Context, *TestingRequest) (*TestingResponse, error)
	// 门店营业情况
	StoreBusiness(context.Context, *StoreBusinessRequest) (*StoreBusinessResponse, error)
	// 报表数据更新时间
	UpdateTime(context.Context, *UpdateTimeRequest) (*UpdateTimeResponse, error)
	// 首页报表
	Dashboard(context.Context, *DashboardRequest) (*DashboardResponse, error)
	// 门店销售报表
	StoreSales(context.Context, *CommonRequest) (*CommonResponse, error)
	// 商品销售报表
	ProductSales(context.Context, *CommonRequest) (*ProductResponse, error)
	// 退单原因分析表
	RefundAnalysis(context.Context, *CommonRequest) (*RefundAnalysisResponse, error)
	// 类别销售报表
	CategorySales(context.Context, *CommonRequest) (*CategorySalesResponse, error)
	// 支付统计报表
	PaymentStatistics(context.Context, *CommonRequest) (*PaymentStatisticsResponse, error)
	// 折扣销售报表
	DiscountSales(context.Context, *CommonRequest) (*DiscountSalesResponse, error)
	// 门店渠道报表
	StoreChannelSales(context.Context, *CommonRequest) (*StoreChannelSalesResponse, error)
	// 商品渠道报表
	ProductChannelSales(context.Context, *CommonRequest) (*ProductChannelResponse, error)
	// 商品计税表
	ProductTaxSales(context.Context, *CommonRequest) (*ProductTaxResponse, error)
	// 门店时段报表
	StorePeriodSales(context.Context, *CommonRequest) (*CommonResponse, error)
	// 商品时段报表
	ProductPeriodSales(context.Context, *CommonRequest) (*ProductPeriodResponse, error)
	// 支付时段报表
	PaymentPeriodSales(context.Context, *CommonRequest) (*PaymentPeriodResponse, error)
	// 近期营业汇总表
	RecentSummary(context.Context, *CommonRequest) (*RecentSummaryResponse, error)
	// 财务报表
	FinancialReport(context.Context, *CommonRequest) (*FinancialResponse, error)
	// 收银汇总数据
	OpenFinancialReport(context.Context, *OpenFinancialRequest) (*OpenFinancialResponse, error)
	// 收银汇总数据
	OpenProductReport(context.Context, *OpenProductRequest) (*OpenProductResponse, error)
	// 商品属性销售表
	ProductFlavorSales(context.Context, *CommonRequest) (*ProductFlavorResponse, error)
	// 单杯出杯时间
	ProductMakeTime(context.Context, *CommonRequest) (*ProductMakeTimeResponse, error)
	// 订单完成时间
	StoreMakeTime(context.Context, *CommonRequest) (*StoreMakeTimeResponse, error)
	// 报损报表
	Adjust(context.Context, *CommonRequest) (*AdjustResponse, error)
	// 财务报表.营业状况
	FinanceBusinessStatus(context.Context, *CommonRequest) (*CommonResponse, error)
	// 财务报表.物流构成
	FinanceLogisticsForm(context.Context, *CommonRequest) (*CommonResponse, error)
	// 财务报表.外卖渠道
	FinanceChannelForm(context.Context, *CommonRequest) (*CommonResponse, error)
	// 财务报表.实收组成
	FinanceNetReceipts(context.Context, *CommonRequest) (*CommonResponse, error)
	// 财务报表.优惠组成
	FinanceDiscount(context.Context, *CommonRequest) (*CommonResponse, error)
	// 财务报表.菜品收入
	FinanceProduct(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.首页面板
	ShopKeeperDashboard(context.Context, *CommonRequest) (*ShopKeeperDashBoardResponse, error)
	// 小掌柜.营业额
	ShopKeeperBusiness(context.Context, *CommonRequest) (*BusinessSituationResponse, error)
	// 小掌柜.实收金额.商品分类
	ShopKeeperBusCategory(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.实收金额.商品排行
	ShopKeeperBusProduct(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.渠道收入趋势
	ShopKeeperChaTrend(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.渠道收入
	ShopKeeperChaIncome(context.Context, *CommonRequest) (*ChannelDistributeResponse, error)
	// 小掌柜.物流收入趋势
	ShopKeeperLogTrend(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.营业时段
	ShopKeeperPeriod(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.菜品收入
	ShopKeeperProRank(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.物流占比/物流收入
	ShopKeeperLogRank(context.Context, *CommonRequest) (*LogRankResponse, error)
	// 小掌柜.各平台补贴
	ShopKeeperPlaDiscount(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.月收入趋势
	ShopKeeperIncome(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.折扣分类
	ShopKeeperDisCategory(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.折扣渠道
	ShopKeeperDisChannel(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.有效订单和退单
	ShopKeeperChaOrder(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.门店营业额Top10排行
	ShopKeeperBusinessRank(context.Context, *CommonRequest) (*CommonResponse, error)
	// 小掌柜.商品分布
	ShopKeeperProDistribute(context.Context, *CommonRequest) (*ProductDistributeResponse, error)
	// 小掌柜.财务实收和优惠组成下钻
	ShopKeeperDetail(context.Context, *CommonRequest) (*DetailForDiscountAndRealAmountResponse, error)
	// 小掌柜.商品Top20排行
	ShopKeeperProductTop20Rank(context.Context, *CommonRequest) (*ProductTop20RankResponse, error)
	// 小掌柜.商品属性下钻
	ShopKeeperProductAttribute(context.Context, *CommonRequest) (*ProductAttributeResponse, error)
	// 小掌柜.客流构成
	ShopKeeperCustomerForm(context.Context, *CommonRequest) (*CustomerFormResponse, error)
	// 小掌柜.支付统计
	ShopKeeperPaymentStatistics(context.Context, *CommonRequest) (*PaymentStatisticsResponse, error)
	// 小掌柜.统计渠道商品数据
	ShopKeeperProductPolymer(context.Context, *CommonRequest) (*ProductPolymerResponse, error)
	// 小掌柜.商品时段报表
	ShopKeeperProductPeriod(context.Context, *CommonRequest) (*ShopkeeperProductPeriodResponse, error)
	// 小掌柜.近期营业汇总表
	ShopkeeperRecentSummary(context.Context, *CommonRequest) (*RecentSummaryResponse, error)
	// 5.5 商品排行
	ProductTopNRank(context.Context, *CommonRequest) (*ProductTopNRankResponse, error)
	// 某商品各门店销售数据
	ProductStoreRank(context.Context, *CommonRequest) (*ProductStoreRankResponse, error)
	// 大掌柜报表配置查询
	ShopkeeperQueryReportConfig(context.Context, *QueryReportConfigRequest) (*ReportConfigResponse, error)
	// 大掌柜报表配置保存
	ShopkeeperSaveReportConfig(context.Context, *SaveReportConfigRequest) (*ReportConfigResponse, error)
	// pos时段报表
	PosHourReport(context.Context, *HubReportQuery) (*HourReportResponse, error)
	// pos时段报表(新)
	PosStorePeriodReport(context.Context, *HubReportQuery) (*PosStorePeriodReportResponse, error)
	// pos类别报表
	PosCategoryReport(context.Context, *HubReportQuery) (*CategoryReportResponse, error)
	// pos商品报表
	PosProductReport(context.Context, *HubReportQuery) (*ProductReportResponse, error)
	// pos商品销售报表(新)
	PosProductSalesReport(context.Context, *HubReportQuery) (*PosProductSalesReportResponse, error)
	// pos营业报表
	PosBusinessReport(context.Context, *HubReportQuery) (*BusinessReportResponse, error)
	// pos交班报表
	PosShiftReport(context.Context, *ShiftReportRequest) (*ShiftInfoResponse, error)
	// Pos 促销报表
	PosPromotion(context.Context, *CommonRequest) (*PosPromotionResponse, error)
	// posr日结报表
	PosDailyReport(context.Context, *DailyRequest) (*DailyResponse, error)
	// pos 就餐方式
	PosOrderTypeReport(context.Context, *OrderTypeRequest) (*OrderTypeResponse, error)
	// pos部门营业报表
	PosDepartmentBusinessReport(context.Context, *HubReportQuery) (*PosDepartmentBusinessResponse, error)
	// 菜品销售汇总表
	ProductSalesSummary(context.Context, *CommonRequest) (*ProductSalesSummaryResponse, error)
	// 菜品销售明细表表
	ProductSalesDetail(context.Context, *CommonRequest) (*ProductSalesDetailResponse, error)
	// 门店表现分析
	StorePerformanceAnalysis(context.Context, *CommonRequest) (*StorePerformanceAnalysisResponse, error)
	// 餐段接口
	MealSegments(context.Context, *CommonRequest) (*MealSegmentsResponse, error)
	// 菜品销售汇总表
	ProductSalesComboSummary(context.Context, *CommonRequest) (*ProductSalesComboSummaryResponse, error)
	// 菜品销售明细表表
	ProductSalesComboDetail(context.Context, *CommonRequest) (*ProductSalesComboDetailResponse, error)
	// 导入门店目标
	ImportStoreGoals(context.Context, *ImportRequest) (*ImportResponse, error)
	// 导入汇率
	ImportExchangeRates(context.Context, *ImportRequest) (*ImportResponse, error)
	// Pos 商品入库报表
	PosInboundReport(context.Context, *CommonRequest) (*PosInboundResponse, error)
	// Pos 商品入库报表
	InboundReport(context.Context, *CommonRequest) (*InboundResponse, error)
	// 大掌柜折扣排行
	ShopkeeperDiscountTopNRank(context.Context, *CommonRequest) (*DiscountTopNRankResponse, error)
	// 大掌柜新时段报表
	ShopkeeperPeriodNew(context.Context, *CommonRequest) (*PeriodNewResponse, error)
	// 财务报表报表-零用现金日结报表
	PettyCashDailySettlementReport(context.Context, *CommonRequest) (*PettyCashDailySettlementReportResponse, error)
	// 支付明细
	PaymentDetailReport(context.Context, *CommonRequest) (*PaymentDetailReportResponse, error)
	// 商品属性销量统计表
	ProductAttributeSales(context.Context, *CommonRequest) (*ProductAttributeSalesResponse, error)
	// 非营运销售报表
	StoreCompensationSalesReport(context.Context, *StoreCompensationSalesReportReq) (*StoreCompensationSalesReportResp, error)
	mustEmbedUnimplementedSalesReportServer()
}

// UnimplementedSalesReportServer must be embedded to have forward compatible implementations.
type UnimplementedSalesReportServer struct {
}

func (UnimplementedSalesReportServer) Testing(context.Context, *TestingRequest) (*TestingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Testing not implemented")
}
func (UnimplementedSalesReportServer) StoreBusiness(context.Context, *StoreBusinessRequest) (*StoreBusinessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreBusiness not implemented")
}
func (UnimplementedSalesReportServer) UpdateTime(context.Context, *UpdateTimeRequest) (*UpdateTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTime not implemented")
}
func (UnimplementedSalesReportServer) Dashboard(context.Context, *DashboardRequest) (*DashboardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Dashboard not implemented")
}
func (UnimplementedSalesReportServer) StoreSales(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreSales not implemented")
}
func (UnimplementedSalesReportServer) ProductSales(context.Context, *CommonRequest) (*ProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductSales not implemented")
}
func (UnimplementedSalesReportServer) RefundAnalysis(context.Context, *CommonRequest) (*RefundAnalysisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundAnalysis not implemented")
}
func (UnimplementedSalesReportServer) CategorySales(context.Context, *CommonRequest) (*CategorySalesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategorySales not implemented")
}
func (UnimplementedSalesReportServer) PaymentStatistics(context.Context, *CommonRequest) (*PaymentStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaymentStatistics not implemented")
}
func (UnimplementedSalesReportServer) DiscountSales(context.Context, *CommonRequest) (*DiscountSalesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DiscountSales not implemented")
}
func (UnimplementedSalesReportServer) StoreChannelSales(context.Context, *CommonRequest) (*StoreChannelSalesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreChannelSales not implemented")
}
func (UnimplementedSalesReportServer) ProductChannelSales(context.Context, *CommonRequest) (*ProductChannelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductChannelSales not implemented")
}
func (UnimplementedSalesReportServer) ProductTaxSales(context.Context, *CommonRequest) (*ProductTaxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductTaxSales not implemented")
}
func (UnimplementedSalesReportServer) StorePeriodSales(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StorePeriodSales not implemented")
}
func (UnimplementedSalesReportServer) ProductPeriodSales(context.Context, *CommonRequest) (*ProductPeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductPeriodSales not implemented")
}
func (UnimplementedSalesReportServer) PaymentPeriodSales(context.Context, *CommonRequest) (*PaymentPeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaymentPeriodSales not implemented")
}
func (UnimplementedSalesReportServer) RecentSummary(context.Context, *CommonRequest) (*RecentSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecentSummary not implemented")
}
func (UnimplementedSalesReportServer) FinancialReport(context.Context, *CommonRequest) (*FinancialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinancialReport not implemented")
}
func (UnimplementedSalesReportServer) OpenFinancialReport(context.Context, *OpenFinancialRequest) (*OpenFinancialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenFinancialReport not implemented")
}
func (UnimplementedSalesReportServer) OpenProductReport(context.Context, *OpenProductRequest) (*OpenProductResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OpenProductReport not implemented")
}
func (UnimplementedSalesReportServer) ProductFlavorSales(context.Context, *CommonRequest) (*ProductFlavorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductFlavorSales not implemented")
}
func (UnimplementedSalesReportServer) ProductMakeTime(context.Context, *CommonRequest) (*ProductMakeTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductMakeTime not implemented")
}
func (UnimplementedSalesReportServer) StoreMakeTime(context.Context, *CommonRequest) (*StoreMakeTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreMakeTime not implemented")
}
func (UnimplementedSalesReportServer) Adjust(context.Context, *CommonRequest) (*AdjustResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Adjust not implemented")
}
func (UnimplementedSalesReportServer) FinanceBusinessStatus(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceBusinessStatus not implemented")
}
func (UnimplementedSalesReportServer) FinanceLogisticsForm(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceLogisticsForm not implemented")
}
func (UnimplementedSalesReportServer) FinanceChannelForm(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceChannelForm not implemented")
}
func (UnimplementedSalesReportServer) FinanceNetReceipts(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceNetReceipts not implemented")
}
func (UnimplementedSalesReportServer) FinanceDiscount(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceDiscount not implemented")
}
func (UnimplementedSalesReportServer) FinanceProduct(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FinanceProduct not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperDashboard(context.Context, *CommonRequest) (*ShopKeeperDashBoardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperDashboard not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperBusiness(context.Context, *CommonRequest) (*BusinessSituationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperBusiness not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperBusCategory(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperBusCategory not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperBusProduct(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperBusProduct not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperChaTrend(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperChaTrend not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperChaIncome(context.Context, *CommonRequest) (*ChannelDistributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperChaIncome not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperLogTrend(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperLogTrend not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperPeriod(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperPeriod not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProRank(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProRank not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperLogRank(context.Context, *CommonRequest) (*LogRankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperLogRank not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperPlaDiscount(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperPlaDiscount not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperIncome(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperIncome not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperDisCategory(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperDisCategory not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperDisChannel(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperDisChannel not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperChaOrder(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperChaOrder not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperBusinessRank(context.Context, *CommonRequest) (*CommonResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperBusinessRank not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProDistribute(context.Context, *CommonRequest) (*ProductDistributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProDistribute not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperDetail(context.Context, *CommonRequest) (*DetailForDiscountAndRealAmountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperDetail not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProductTop20Rank(context.Context, *CommonRequest) (*ProductTop20RankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProductTop20Rank not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProductAttribute(context.Context, *CommonRequest) (*ProductAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProductAttribute not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperCustomerForm(context.Context, *CommonRequest) (*CustomerFormResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperCustomerForm not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperPaymentStatistics(context.Context, *CommonRequest) (*PaymentStatisticsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperPaymentStatistics not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProductPolymer(context.Context, *CommonRequest) (*ProductPolymerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProductPolymer not implemented")
}
func (UnimplementedSalesReportServer) ShopKeeperProductPeriod(context.Context, *CommonRequest) (*ShopkeeperProductPeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopKeeperProductPeriod not implemented")
}
func (UnimplementedSalesReportServer) ShopkeeperRecentSummary(context.Context, *CommonRequest) (*RecentSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopkeeperRecentSummary not implemented")
}
func (UnimplementedSalesReportServer) ProductTopNRank(context.Context, *CommonRequest) (*ProductTopNRankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductTopNRank not implemented")
}
func (UnimplementedSalesReportServer) ProductStoreRank(context.Context, *CommonRequest) (*ProductStoreRankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductStoreRank not implemented")
}
func (UnimplementedSalesReportServer) ShopkeeperQueryReportConfig(context.Context, *QueryReportConfigRequest) (*ReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopkeeperQueryReportConfig not implemented")
}
func (UnimplementedSalesReportServer) ShopkeeperSaveReportConfig(context.Context, *SaveReportConfigRequest) (*ReportConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopkeeperSaveReportConfig not implemented")
}
func (UnimplementedSalesReportServer) PosHourReport(context.Context, *HubReportQuery) (*HourReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosHourReport not implemented")
}
func (UnimplementedSalesReportServer) PosStorePeriodReport(context.Context, *HubReportQuery) (*PosStorePeriodReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosStorePeriodReport not implemented")
}
func (UnimplementedSalesReportServer) PosCategoryReport(context.Context, *HubReportQuery) (*CategoryReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosCategoryReport not implemented")
}
func (UnimplementedSalesReportServer) PosProductReport(context.Context, *HubReportQuery) (*ProductReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosProductReport not implemented")
}
func (UnimplementedSalesReportServer) PosProductSalesReport(context.Context, *HubReportQuery) (*PosProductSalesReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosProductSalesReport not implemented")
}
func (UnimplementedSalesReportServer) PosBusinessReport(context.Context, *HubReportQuery) (*BusinessReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosBusinessReport not implemented")
}
func (UnimplementedSalesReportServer) PosShiftReport(context.Context, *ShiftReportRequest) (*ShiftInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosShiftReport not implemented")
}
func (UnimplementedSalesReportServer) PosPromotion(context.Context, *CommonRequest) (*PosPromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosPromotion not implemented")
}
func (UnimplementedSalesReportServer) PosDailyReport(context.Context, *DailyRequest) (*DailyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosDailyReport not implemented")
}
func (UnimplementedSalesReportServer) PosOrderTypeReport(context.Context, *OrderTypeRequest) (*OrderTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosOrderTypeReport not implemented")
}
func (UnimplementedSalesReportServer) PosDepartmentBusinessReport(context.Context, *HubReportQuery) (*PosDepartmentBusinessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosDepartmentBusinessReport not implemented")
}
func (UnimplementedSalesReportServer) ProductSalesSummary(context.Context, *CommonRequest) (*ProductSalesSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductSalesSummary not implemented")
}
func (UnimplementedSalesReportServer) ProductSalesDetail(context.Context, *CommonRequest) (*ProductSalesDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductSalesDetail not implemented")
}
func (UnimplementedSalesReportServer) StorePerformanceAnalysis(context.Context, *CommonRequest) (*StorePerformanceAnalysisResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StorePerformanceAnalysis not implemented")
}
func (UnimplementedSalesReportServer) MealSegments(context.Context, *CommonRequest) (*MealSegmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MealSegments not implemented")
}
func (UnimplementedSalesReportServer) ProductSalesComboSummary(context.Context, *CommonRequest) (*ProductSalesComboSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductSalesComboSummary not implemented")
}
func (UnimplementedSalesReportServer) ProductSalesComboDetail(context.Context, *CommonRequest) (*ProductSalesComboDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductSalesComboDetail not implemented")
}
func (UnimplementedSalesReportServer) ImportStoreGoals(context.Context, *ImportRequest) (*ImportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportStoreGoals not implemented")
}
func (UnimplementedSalesReportServer) ImportExchangeRates(context.Context, *ImportRequest) (*ImportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ImportExchangeRates not implemented")
}
func (UnimplementedSalesReportServer) PosInboundReport(context.Context, *CommonRequest) (*PosInboundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PosInboundReport not implemented")
}
func (UnimplementedSalesReportServer) InboundReport(context.Context, *CommonRequest) (*InboundResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InboundReport not implemented")
}
func (UnimplementedSalesReportServer) ShopkeeperDiscountTopNRank(context.Context, *CommonRequest) (*DiscountTopNRankResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopkeeperDiscountTopNRank not implemented")
}
func (UnimplementedSalesReportServer) ShopkeeperPeriodNew(context.Context, *CommonRequest) (*PeriodNewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShopkeeperPeriodNew not implemented")
}
func (UnimplementedSalesReportServer) PettyCashDailySettlementReport(context.Context, *CommonRequest) (*PettyCashDailySettlementReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PettyCashDailySettlementReport not implemented")
}
func (UnimplementedSalesReportServer) PaymentDetailReport(context.Context, *CommonRequest) (*PaymentDetailReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PaymentDetailReport not implemented")
}
func (UnimplementedSalesReportServer) ProductAttributeSales(context.Context, *CommonRequest) (*ProductAttributeSalesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProductAttributeSales not implemented")
}
func (UnimplementedSalesReportServer) StoreCompensationSalesReport(context.Context, *StoreCompensationSalesReportReq) (*StoreCompensationSalesReportResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreCompensationSalesReport not implemented")
}
func (UnimplementedSalesReportServer) mustEmbedUnimplementedSalesReportServer() {}

// UnsafeSalesReportServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SalesReportServer will
// result in compilation errors.
type UnsafeSalesReportServer interface {
	mustEmbedUnimplementedSalesReportServer()
}

func RegisterSalesReportServer(s grpc.ServiceRegistrar, srv SalesReportServer) {
	s.RegisterService(&SalesReport_ServiceDesc, srv)
}

func _SalesReport_Testing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).Testing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/Testing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).Testing(ctx, req.(*TestingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StoreBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreBusinessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StoreBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StoreBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StoreBusiness(ctx, req.(*StoreBusinessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_UpdateTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).UpdateTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/UpdateTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).UpdateTime(ctx, req.(*UpdateTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_Dashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DashboardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).Dashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/Dashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).Dashboard(ctx, req.(*DashboardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StoreSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StoreSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StoreSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StoreSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_RefundAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).RefundAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/RefundAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).RefundAnalysis(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_CategorySales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).CategorySales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/CategorySales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).CategorySales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PaymentStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PaymentStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PaymentStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PaymentStatistics(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_DiscountSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).DiscountSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/DiscountSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).DiscountSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StoreChannelSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StoreChannelSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StoreChannelSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StoreChannelSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductChannelSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductChannelSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductChannelSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductChannelSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductTaxSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductTaxSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductTaxSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductTaxSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StorePeriodSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StorePeriodSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StorePeriodSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StorePeriodSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductPeriodSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductPeriodSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductPeriodSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductPeriodSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PaymentPeriodSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PaymentPeriodSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PaymentPeriodSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PaymentPeriodSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_RecentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).RecentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/RecentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).RecentSummary(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinancialReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinancialReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinancialReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinancialReport(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_OpenFinancialReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenFinancialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).OpenFinancialReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/OpenFinancialReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).OpenFinancialReport(ctx, req.(*OpenFinancialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_OpenProductReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenProductRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).OpenProductReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/OpenProductReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).OpenProductReport(ctx, req.(*OpenProductRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductFlavorSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductFlavorSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductFlavorSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductFlavorSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductMakeTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductMakeTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductMakeTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductMakeTime(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StoreMakeTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StoreMakeTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StoreMakeTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StoreMakeTime(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_Adjust_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).Adjust(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/Adjust",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).Adjust(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceBusinessStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceBusinessStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceBusinessStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceBusinessStatus(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceLogisticsForm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceLogisticsForm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceLogisticsForm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceLogisticsForm(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceChannelForm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceChannelForm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceChannelForm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceChannelForm(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceNetReceipts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceNetReceipts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceNetReceipts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceNetReceipts(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceDiscount(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_FinanceProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).FinanceProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/FinanceProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).FinanceProduct(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperDashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperDashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperDashboard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperDashboard(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperBusiness(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperBusCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperBusCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperBusCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperBusCategory(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperBusProduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperBusProduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperBusProduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperBusProduct(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperChaTrend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperChaTrend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperChaTrend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperChaTrend(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperChaIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperChaIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperChaIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperChaIncome(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperLogTrend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperLogTrend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperLogTrend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperLogTrend(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperPeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperPeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperPeriod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperPeriod(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperLogRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperLogRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperLogRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperLogRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperPlaDiscount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperPlaDiscount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperPlaDiscount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperPlaDiscount(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperIncome(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperDisCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperDisCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperDisCategory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperDisCategory(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperDisChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperDisChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperDisChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperDisChannel(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperChaOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperChaOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperChaOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperChaOrder(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperBusinessRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperBusinessRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperBusinessRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperBusinessRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProDistribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProDistribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProDistribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProDistribute(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperDetail(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProductTop20Rank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProductTop20Rank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProductTop20Rank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProductTop20Rank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProductAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProductAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProductAttribute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProductAttribute(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperCustomerForm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperCustomerForm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperCustomerForm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperCustomerForm(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperPaymentStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperPaymentStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperPaymentStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperPaymentStatistics(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProductPolymer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProductPolymer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProductPolymer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProductPolymer(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopKeeperProductPeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopKeeperProductPeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopKeeperProductPeriod",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopKeeperProductPeriod(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopkeeperRecentSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopkeeperRecentSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopkeeperRecentSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopkeeperRecentSummary(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductTopNRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductTopNRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductTopNRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductTopNRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductStoreRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductStoreRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductStoreRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductStoreRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopkeeperQueryReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopkeeperQueryReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopkeeperQueryReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopkeeperQueryReportConfig(ctx, req.(*QueryReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopkeeperSaveReportConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveReportConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopkeeperSaveReportConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopkeeperSaveReportConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopkeeperSaveReportConfig(ctx, req.(*SaveReportConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosHourReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosHourReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosHourReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosHourReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosStorePeriodReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosStorePeriodReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosStorePeriodReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosStorePeriodReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosCategoryReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosCategoryReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosCategoryReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosCategoryReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosProductReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosProductReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosProductReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosProductReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosProductSalesReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosProductSalesReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosProductSalesReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosProductSalesReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosBusinessReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosBusinessReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosBusinessReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosBusinessReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosShiftReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShiftReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosShiftReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosShiftReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosShiftReport(ctx, req.(*ShiftReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosPromotion(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosDailyReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DailyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosDailyReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosDailyReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosDailyReport(ctx, req.(*DailyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosOrderTypeReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosOrderTypeReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosOrderTypeReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosOrderTypeReport(ctx, req.(*OrderTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosDepartmentBusinessReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HubReportQuery)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosDepartmentBusinessReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosDepartmentBusinessReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosDepartmentBusinessReport(ctx, req.(*HubReportQuery))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductSalesSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductSalesSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductSalesSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductSalesSummary(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductSalesDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductSalesDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductSalesDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductSalesDetail(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StorePerformanceAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StorePerformanceAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StorePerformanceAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StorePerformanceAnalysis(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_MealSegments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).MealSegments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/MealSegments",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).MealSegments(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductSalesComboSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductSalesComboSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductSalesComboSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductSalesComboSummary(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductSalesComboDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductSalesComboDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductSalesComboDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductSalesComboDetail(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ImportStoreGoals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ImportStoreGoals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ImportStoreGoals",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ImportStoreGoals(ctx, req.(*ImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ImportExchangeRates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ImportExchangeRates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ImportExchangeRates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ImportExchangeRates(ctx, req.(*ImportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PosInboundReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PosInboundReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PosInboundReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PosInboundReport(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_InboundReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).InboundReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/InboundReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).InboundReport(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopkeeperDiscountTopNRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopkeeperDiscountTopNRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopkeeperDiscountTopNRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopkeeperDiscountTopNRank(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ShopkeeperPeriodNew_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ShopkeeperPeriodNew(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ShopkeeperPeriodNew",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ShopkeeperPeriodNew(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PettyCashDailySettlementReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PettyCashDailySettlementReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PettyCashDailySettlementReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PettyCashDailySettlementReport(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_PaymentDetailReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).PaymentDetailReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/PaymentDetailReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).PaymentDetailReport(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_ProductAttributeSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).ProductAttributeSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/ProductAttributeSales",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).ProductAttributeSales(ctx, req.(*CommonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SalesReport_StoreCompensationSalesReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreCompensationSalesReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SalesReportServer).StoreCompensationSalesReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sales_report.SalesReport/StoreCompensationSalesReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SalesReportServer).StoreCompensationSalesReport(ctx, req.(*StoreCompensationSalesReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SalesReport_ServiceDesc is the grpc.ServiceDesc for SalesReport service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SalesReport_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sales_report.SalesReport",
	HandlerType: (*SalesReportServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Testing",
			Handler:    _SalesReport_Testing_Handler,
		},
		{
			MethodName: "StoreBusiness",
			Handler:    _SalesReport_StoreBusiness_Handler,
		},
		{
			MethodName: "UpdateTime",
			Handler:    _SalesReport_UpdateTime_Handler,
		},
		{
			MethodName: "Dashboard",
			Handler:    _SalesReport_Dashboard_Handler,
		},
		{
			MethodName: "StoreSales",
			Handler:    _SalesReport_StoreSales_Handler,
		},
		{
			MethodName: "ProductSales",
			Handler:    _SalesReport_ProductSales_Handler,
		},
		{
			MethodName: "RefundAnalysis",
			Handler:    _SalesReport_RefundAnalysis_Handler,
		},
		{
			MethodName: "CategorySales",
			Handler:    _SalesReport_CategorySales_Handler,
		},
		{
			MethodName: "PaymentStatistics",
			Handler:    _SalesReport_PaymentStatistics_Handler,
		},
		{
			MethodName: "DiscountSales",
			Handler:    _SalesReport_DiscountSales_Handler,
		},
		{
			MethodName: "StoreChannelSales",
			Handler:    _SalesReport_StoreChannelSales_Handler,
		},
		{
			MethodName: "ProductChannelSales",
			Handler:    _SalesReport_ProductChannelSales_Handler,
		},
		{
			MethodName: "ProductTaxSales",
			Handler:    _SalesReport_ProductTaxSales_Handler,
		},
		{
			MethodName: "StorePeriodSales",
			Handler:    _SalesReport_StorePeriodSales_Handler,
		},
		{
			MethodName: "ProductPeriodSales",
			Handler:    _SalesReport_ProductPeriodSales_Handler,
		},
		{
			MethodName: "PaymentPeriodSales",
			Handler:    _SalesReport_PaymentPeriodSales_Handler,
		},
		{
			MethodName: "RecentSummary",
			Handler:    _SalesReport_RecentSummary_Handler,
		},
		{
			MethodName: "FinancialReport",
			Handler:    _SalesReport_FinancialReport_Handler,
		},
		{
			MethodName: "OpenFinancialReport",
			Handler:    _SalesReport_OpenFinancialReport_Handler,
		},
		{
			MethodName: "OpenProductReport",
			Handler:    _SalesReport_OpenProductReport_Handler,
		},
		{
			MethodName: "ProductFlavorSales",
			Handler:    _SalesReport_ProductFlavorSales_Handler,
		},
		{
			MethodName: "ProductMakeTime",
			Handler:    _SalesReport_ProductMakeTime_Handler,
		},
		{
			MethodName: "StoreMakeTime",
			Handler:    _SalesReport_StoreMakeTime_Handler,
		},
		{
			MethodName: "Adjust",
			Handler:    _SalesReport_Adjust_Handler,
		},
		{
			MethodName: "FinanceBusinessStatus",
			Handler:    _SalesReport_FinanceBusinessStatus_Handler,
		},
		{
			MethodName: "FinanceLogisticsForm",
			Handler:    _SalesReport_FinanceLogisticsForm_Handler,
		},
		{
			MethodName: "FinanceChannelForm",
			Handler:    _SalesReport_FinanceChannelForm_Handler,
		},
		{
			MethodName: "FinanceNetReceipts",
			Handler:    _SalesReport_FinanceNetReceipts_Handler,
		},
		{
			MethodName: "FinanceDiscount",
			Handler:    _SalesReport_FinanceDiscount_Handler,
		},
		{
			MethodName: "FinanceProduct",
			Handler:    _SalesReport_FinanceProduct_Handler,
		},
		{
			MethodName: "ShopKeeperDashboard",
			Handler:    _SalesReport_ShopKeeperDashboard_Handler,
		},
		{
			MethodName: "ShopKeeperBusiness",
			Handler:    _SalesReport_ShopKeeperBusiness_Handler,
		},
		{
			MethodName: "ShopKeeperBusCategory",
			Handler:    _SalesReport_ShopKeeperBusCategory_Handler,
		},
		{
			MethodName: "ShopKeeperBusProduct",
			Handler:    _SalesReport_ShopKeeperBusProduct_Handler,
		},
		{
			MethodName: "ShopKeeperChaTrend",
			Handler:    _SalesReport_ShopKeeperChaTrend_Handler,
		},
		{
			MethodName: "ShopKeeperChaIncome",
			Handler:    _SalesReport_ShopKeeperChaIncome_Handler,
		},
		{
			MethodName: "ShopKeeperLogTrend",
			Handler:    _SalesReport_ShopKeeperLogTrend_Handler,
		},
		{
			MethodName: "ShopKeeperPeriod",
			Handler:    _SalesReport_ShopKeeperPeriod_Handler,
		},
		{
			MethodName: "ShopKeeperProRank",
			Handler:    _SalesReport_ShopKeeperProRank_Handler,
		},
		{
			MethodName: "ShopKeeperLogRank",
			Handler:    _SalesReport_ShopKeeperLogRank_Handler,
		},
		{
			MethodName: "ShopKeeperPlaDiscount",
			Handler:    _SalesReport_ShopKeeperPlaDiscount_Handler,
		},
		{
			MethodName: "ShopKeeperIncome",
			Handler:    _SalesReport_ShopKeeperIncome_Handler,
		},
		{
			MethodName: "ShopKeeperDisCategory",
			Handler:    _SalesReport_ShopKeeperDisCategory_Handler,
		},
		{
			MethodName: "ShopKeeperDisChannel",
			Handler:    _SalesReport_ShopKeeperDisChannel_Handler,
		},
		{
			MethodName: "ShopKeeperChaOrder",
			Handler:    _SalesReport_ShopKeeperChaOrder_Handler,
		},
		{
			MethodName: "ShopKeeperBusinessRank",
			Handler:    _SalesReport_ShopKeeperBusinessRank_Handler,
		},
		{
			MethodName: "ShopKeeperProDistribute",
			Handler:    _SalesReport_ShopKeeperProDistribute_Handler,
		},
		{
			MethodName: "ShopKeeperDetail",
			Handler:    _SalesReport_ShopKeeperDetail_Handler,
		},
		{
			MethodName: "ShopKeeperProductTop20Rank",
			Handler:    _SalesReport_ShopKeeperProductTop20Rank_Handler,
		},
		{
			MethodName: "ShopKeeperProductAttribute",
			Handler:    _SalesReport_ShopKeeperProductAttribute_Handler,
		},
		{
			MethodName: "ShopKeeperCustomerForm",
			Handler:    _SalesReport_ShopKeeperCustomerForm_Handler,
		},
		{
			MethodName: "ShopKeeperPaymentStatistics",
			Handler:    _SalesReport_ShopKeeperPaymentStatistics_Handler,
		},
		{
			MethodName: "ShopKeeperProductPolymer",
			Handler:    _SalesReport_ShopKeeperProductPolymer_Handler,
		},
		{
			MethodName: "ShopKeeperProductPeriod",
			Handler:    _SalesReport_ShopKeeperProductPeriod_Handler,
		},
		{
			MethodName: "ShopkeeperRecentSummary",
			Handler:    _SalesReport_ShopkeeperRecentSummary_Handler,
		},
		{
			MethodName: "ProductTopNRank",
			Handler:    _SalesReport_ProductTopNRank_Handler,
		},
		{
			MethodName: "ProductStoreRank",
			Handler:    _SalesReport_ProductStoreRank_Handler,
		},
		{
			MethodName: "ShopkeeperQueryReportConfig",
			Handler:    _SalesReport_ShopkeeperQueryReportConfig_Handler,
		},
		{
			MethodName: "ShopkeeperSaveReportConfig",
			Handler:    _SalesReport_ShopkeeperSaveReportConfig_Handler,
		},
		{
			MethodName: "PosHourReport",
			Handler:    _SalesReport_PosHourReport_Handler,
		},
		{
			MethodName: "PosStorePeriodReport",
			Handler:    _SalesReport_PosStorePeriodReport_Handler,
		},
		{
			MethodName: "PosCategoryReport",
			Handler:    _SalesReport_PosCategoryReport_Handler,
		},
		{
			MethodName: "PosProductReport",
			Handler:    _SalesReport_PosProductReport_Handler,
		},
		{
			MethodName: "PosProductSalesReport",
			Handler:    _SalesReport_PosProductSalesReport_Handler,
		},
		{
			MethodName: "PosBusinessReport",
			Handler:    _SalesReport_PosBusinessReport_Handler,
		},
		{
			MethodName: "PosShiftReport",
			Handler:    _SalesReport_PosShiftReport_Handler,
		},
		{
			MethodName: "PosPromotion",
			Handler:    _SalesReport_PosPromotion_Handler,
		},
		{
			MethodName: "PosDailyReport",
			Handler:    _SalesReport_PosDailyReport_Handler,
		},
		{
			MethodName: "PosOrderTypeReport",
			Handler:    _SalesReport_PosOrderTypeReport_Handler,
		},
		{
			MethodName: "PosDepartmentBusinessReport",
			Handler:    _SalesReport_PosDepartmentBusinessReport_Handler,
		},
		{
			MethodName: "ProductSalesSummary",
			Handler:    _SalesReport_ProductSalesSummary_Handler,
		},
		{
			MethodName: "ProductSalesDetail",
			Handler:    _SalesReport_ProductSalesDetail_Handler,
		},
		{
			MethodName: "StorePerformanceAnalysis",
			Handler:    _SalesReport_StorePerformanceAnalysis_Handler,
		},
		{
			MethodName: "MealSegments",
			Handler:    _SalesReport_MealSegments_Handler,
		},
		{
			MethodName: "ProductSalesComboSummary",
			Handler:    _SalesReport_ProductSalesComboSummary_Handler,
		},
		{
			MethodName: "ProductSalesComboDetail",
			Handler:    _SalesReport_ProductSalesComboDetail_Handler,
		},
		{
			MethodName: "ImportStoreGoals",
			Handler:    _SalesReport_ImportStoreGoals_Handler,
		},
		{
			MethodName: "ImportExchangeRates",
			Handler:    _SalesReport_ImportExchangeRates_Handler,
		},
		{
			MethodName: "PosInboundReport",
			Handler:    _SalesReport_PosInboundReport_Handler,
		},
		{
			MethodName: "InboundReport",
			Handler:    _SalesReport_InboundReport_Handler,
		},
		{
			MethodName: "ShopkeeperDiscountTopNRank",
			Handler:    _SalesReport_ShopkeeperDiscountTopNRank_Handler,
		},
		{
			MethodName: "ShopkeeperPeriodNew",
			Handler:    _SalesReport_ShopkeeperPeriodNew_Handler,
		},
		{
			MethodName: "PettyCashDailySettlementReport",
			Handler:    _SalesReport_PettyCashDailySettlementReport_Handler,
		},
		{
			MethodName: "PaymentDetailReport",
			Handler:    _SalesReport_PaymentDetailReport_Handler,
		},
		{
			MethodName: "ProductAttributeSales",
			Handler:    _SalesReport_ProductAttributeSales_Handler,
		},
		{
			MethodName: "StoreCompensationSalesReport",
			Handler:    _SalesReport_StoreCompensationSalesReport_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "grpc/proto/sales_report.proto",
}
