syntax = "proto3";

import "grpc/proto/google/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/any.proto";

package sales_report;

// proto3需要增加的选项，(生成的go文件存储路径;生成的包名称)
option go_package = "./grpc/proto;sales_report";

// GrpcPC端门店报表接口
service SalesReport {

  // 拨测
  rpc Testing(TestingRequest) returns (TestingResponse) {
    option (google.api.http) = {
      get:"/api/v1/report/sales/testing"
    };
  }

  // 门店营业情况
  rpc StoreBusiness(StoreBusinessRequest) returns (StoreBusinessResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/store-business"
    };
  }

  // 报表数据更新时间
  rpc UpdateTime(UpdateTimeRequest) returns (UpdateTimeResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/updatetime",
      body:"*"
    };
  }
  // 首页报表
  rpc Dashboard(DashboardRequest) returns (DashboardResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/dashboard",
      body:"*"
    };
  }
  // 门店销售报表
  rpc StoreSales(CommonRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/store",
      body:"*"
    };
  }
  // 商品销售报表
  rpc ProductSales(CommonRequest) returns (ProductResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/product",
      body:"*"
    };
  }
  // 退单原因分析表
  rpc RefundAnalysis(CommonRequest) returns (RefundAnalysisResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/refund/analy",
      body:"*"
    };
  }
  // 类别销售报表
  rpc CategorySales(CommonRequest) returns (CategorySalesResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/category",
      body:"*"
    };
  }
  // 支付统计报表
  rpc PaymentStatistics(CommonRequest) returns (PaymentStatisticsResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/payment",
      body:"*"
    };
  }
  // 折扣销售报表
  rpc DiscountSales(CommonRequest) returns (DiscountSalesResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/discount",
      body:"*"
    };
  }
  // 门店渠道报表
  rpc StoreChannelSales(CommonRequest) returns (StoreChannelSalesResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/channel/store",
      body:"*"
    };
  }
  // 商品渠道报表
  rpc ProductChannelSales(CommonRequest) returns (ProductChannelResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/channel/product",
      body:"*"
    };
  }

  // 商品计税表
  rpc ProductTaxSales(CommonRequest) returns (ProductTaxResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/tax",
      body:"*"
    };
  }

  // 门店时段报表
  rpc StorePeriodSales(CommonRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/period/store",
      body:"*"
    };
  }
  // 商品时段报表
  rpc ProductPeriodSales(CommonRequest) returns (ProductPeriodResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/period/product",
      body:"*"
    };
  }
  // 支付时段报表
  rpc PaymentPeriodSales(CommonRequest) returns (PaymentPeriodResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/period/payment",
      body:"*"
    };
  }

  // 近期营业汇总表
  rpc RecentSummary(CommonRequest) returns (RecentSummaryResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/recent/summary",
      body:"*"
    };
  }

  // 财务报表
  rpc FinancialReport(CommonRequest) returns (FinancialResponse){
    option (google.api.http) = {
      post:"/api/v1/report/financial",
      body:"*"
    };
  }

  // 收银汇总数据
  rpc OpenFinancialReport(OpenFinancialRequest) returns (OpenFinancialResponse){
    option (google.api.http) = {
      post:"/api/v1/report/open/financial",
      body:"*"
    };
  }

  // 收银汇总数据
  rpc OpenProductReport(OpenProductRequest) returns (OpenProductResponse){
    option (google.api.http) = {
      post:"/api/v1/report/open/product",
      body:"*"
    };
  }

  // 商品属性销售表
  rpc ProductFlavorSales(CommonRequest) returns (ProductFlavorResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/flavor",
      body:"*"
    };
  }

  // 单杯出杯时间
  rpc ProductMakeTime(CommonRequest) returns (ProductMakeTimeResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/maketime/product",
      body:"*"
    };
  }

  // 订单完成时间
  rpc StoreMakeTime(CommonRequest) returns (StoreMakeTimeResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/maketime/store",
      body:"*"
    };
  }

  // 报损报表
  rpc Adjust(CommonRequest) returns (AdjustResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/adjust",
      body:"*"
    };
  }

  // 财务报表.营业状况
  rpc FinanceBusinessStatus(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/business/status",
      body:"*"
    };
  }
  // 财务报表.物流构成
  rpc FinanceLogisticsForm(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/logistics/form",
      body:"*"
    };
  }
  // 财务报表.外卖渠道
  rpc FinanceChannelForm(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/channel/form",
      body:"*"
    };
  }
  // 财务报表.实收组成
  rpc FinanceNetReceipts(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/net/receipts",
      body:"*"
    };
  }
  // 财务报表.优惠组成
  rpc FinanceDiscount(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/discount",
      body:"*"
    };
  }
  // 财务报表.菜品收入
  rpc FinanceProduct(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/product",
      body:"*"
    };
  }

  // 小掌柜.首页面板
  rpc ShopKeeperDashboard(CommonRequest) returns (ShopKeeperDashBoardResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/dashboard",
      body:"*"
    };
  }
  // 小掌柜.营业额
  rpc ShopKeeperBusiness(CommonRequest) returns (BusinessSituationResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/business",
      body:"*"
    };
  }
  // 小掌柜.实收金额.商品分类
  rpc ShopKeeperBusCategory(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/business/category",
      body:"*"
    };
  }
  // 小掌柜.实收金额.商品排行
  rpc ShopKeeperBusProduct(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/business/category/product",
      body:"*"
    };
  }
  // 小掌柜.渠道收入趋势
  rpc ShopKeeperChaTrend(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/channel/trend",
      body:"*"
    };
  }
  // 小掌柜.渠道收入
  rpc ShopKeeperChaIncome(CommonRequest) returns (ChannelDistributeResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/channel",
      body:"*"
    };
  }
  // 小掌柜.物流收入趋势
  rpc ShopKeeperLogTrend(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/logistics/trend",
      body:"*"
    };
  }
  // 小掌柜.营业时段
  rpc ShopKeeperPeriod(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/period",
      body:"*"
    };
  }
  // 小掌柜.菜品收入
  rpc ShopKeeperProRank(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/rank",
      body:"*"
    };
  }
  // 小掌柜.物流占比/物流收入
  rpc ShopKeeperLogRank(CommonRequest) returns (LogRankResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/logistics/rank",
      body:"*"
    };
  }
  // 小掌柜.各平台补贴
  rpc ShopKeeperPlaDiscount(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/platform/discount",
      body:"*"
    };
  }
  // 小掌柜.月收入趋势
  rpc ShopKeeperIncome(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/income",
      body:"*"
    };
  }
  // 小掌柜.折扣分类
  rpc ShopKeeperDisCategory(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/discount/category",
      body:"*"
    };
  }
  // 小掌柜.折扣渠道
  rpc ShopKeeperDisChannel(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/discount/channel",
      body:"*"
    };
  }
  // 小掌柜.有效订单和退单
  rpc ShopKeeperChaOrder(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/order/channel",
      body:"*"
    };
  }

  // 小掌柜.门店营业额Top10排行
  rpc ShopKeeperBusinessRank(CommonRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/business/top10/rank",
      body:"*"
    };
  }

  // 小掌柜.商品分布
  rpc ShopKeeperProDistribute(CommonRequest) returns (ProductDistributeResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/distribute",
      body:"*"
    };
  }

  // 小掌柜.财务实收和优惠组成下钻
  rpc ShopKeeperDetail(CommonRequest) returns (DetailForDiscountAndRealAmountResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/detail",
      body:"*"
    };
  }

  // 小掌柜.商品Top20排行
  rpc ShopKeeperProductTop20Rank(CommonRequest) returns (ProductTop20RankResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/top20/rank",
      body:"*"
    };
  }

  // 小掌柜.商品属性下钻
  rpc ShopKeeperProductAttribute(CommonRequest) returns (ProductAttributeResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/attribute",
      body:"*"
    };
  }

  // 小掌柜.客流构成
  rpc ShopKeeperCustomerForm(CommonRequest) returns (CustomerFormResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/customer/form",
      body:"*"
    };
  }

  // 小掌柜.支付统计
  rpc ShopKeeperPaymentStatistics(CommonRequest) returns (PaymentStatisticsResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/payment",
      body:"*"
    };
  }

  // 小掌柜.统计渠道商品数据
  rpc ShopKeeperProductPolymer(CommonRequest) returns (ProductPolymerResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/polymer",
      body:"*"
    };
  }

  // 小掌柜.商品时段报表
  rpc ShopKeeperProductPeriod(CommonRequest) returns (ShopkeeperProductPeriodResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/period/product",
      body:"*"
    };
  }

  // 小掌柜.近期营业汇总表
  rpc ShopkeeperRecentSummary(CommonRequest) returns (RecentSummaryResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/recent/summary",
      body:"*"
    };
  }

  // 5.5 商品排行
  rpc ProductTopNRank(CommonRequest) returns (ProductTopNRankResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/topn/rank",
      body:"*"
    };
  }

  // 某商品各门店销售数据
  rpc ProductStoreRank(CommonRequest) returns (ProductStoreRankResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/product/store/rank",
      body:"*"
    };
  }

  // 大掌柜报表配置查询
  rpc ShopkeeperQueryReportConfig(QueryReportConfigRequest) returns (ReportConfigResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/config/query",
      body:"*"
    };
  }

  // 大掌柜报表配置保存
  rpc ShopkeeperSaveReportConfig(SaveReportConfigRequest) returns (ReportConfigResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/config/save",
      body:"*"
    };
  }
  // pos时段报表
  rpc PosHourReport(HubReportQuery) returns (HourReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getPeriodReport",
      body:"*"
    };
  }
  // pos时段报表(新)
  rpc PosStorePeriodReport(HubReportQuery) returns (PosStorePeriodReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/storePeriodReport",
      body:"*"
    };
  }
  // pos类别报表
  rpc PosCategoryReport(HubReportQuery) returns (CategoryReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getCategoryReport",
      body:"*"
    };
  }

  // pos商品报表
  rpc PosProductReport(HubReportQuery) returns (ProductReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getProductReport",
      body:"*"
    };
  }

  // pos商品销售报表(新)
  rpc PosProductSalesReport(HubReportQuery) returns (PosProductSalesReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/productSalesReport",
      body:"*"
    };
  }

  // pos营业报表
  rpc PosBusinessReport(HubReportQuery) returns (BusinessReportResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getBusinessReport",
      body:"*"
    };
  }

  // pos交班报表
  rpc PosShiftReport(ShiftReportRequest) returns (ShiftInfoResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getShiftInfo",
      body:"*"
    };
  }

  // Pos 促销报表
  rpc PosPromotion(CommonRequest) returns (PosPromotionResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/pos/promotion",
      body:"*"
    };
  }

  // posr日结报表
  rpc PosDailyReport(DailyRequest) returns (DailyResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getDailyInfo",
      body:"*"
    };
  }

  // pos 就餐方式
  rpc PosOrderTypeReport(OrderTypeRequest) returns (OrderTypeResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/getOrderType",
      body:"*"
    };
  }

  // pos部门营业报表
  rpc PosDepartmentBusinessReport(HubReportQuery) returns (PosDepartmentBusinessResponse){
    option (google.api.http) = {
      post:"/api/v1/hub/report/departmentBusinessReport",
      body:"*"
    };
  }

  // 菜品销售汇总表
  rpc ProductSalesSummary(CommonRequest) returns (ProductSalesSummaryResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/summary",
      body:"*"
    };
  }

  // 菜品销售明细表表
  rpc ProductSalesDetail(CommonRequest) returns (ProductSalesDetailResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/detail",
      body:"*"
    };
  }

  // 门店表现分析
  rpc StorePerformanceAnalysis(CommonRequest) returns (StorePerformanceAnalysisResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/store/performance",
      body:"*"
    };
  }

  // 餐段接口
  rpc MealSegments(CommonRequest) returns (MealSegmentsResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/mealsegments",
      body:"*"
    };
  }
  // 菜品销售汇总表
  rpc ProductSalesComboSummary(CommonRequest) returns (ProductSalesComboSummaryResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/combo/summary",
      body:"*"
    };
  }

  // 菜品销售明细表表
  rpc ProductSalesComboDetail(CommonRequest) returns (ProductSalesComboDetailResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/combo/detail",
      body:"*"
    };
  }

  // 导入门店目标
  rpc ImportStoreGoals(ImportRequest) returns (ImportResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/storegoal/import",
      body:"*"
    };
  }

  // 导入汇率
  rpc ImportExchangeRates(ImportRequest) returns (ImportResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/exchangerate/import",
      body:"*"
    };
  }

  // Pos 商品入库报表
  rpc PosInboundReport(CommonRequest) returns (PosInboundResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/pos/inbound",
      body:"*"
    };
  }

  // Pos 商品入库报表
  rpc InboundReport(CommonRequest) returns (InboundResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/inbound",
      body:"*"
    };
  }

  // 大掌柜折扣排行
  rpc ShopkeeperDiscountTopNRank(CommonRequest) returns (DiscountTopNRankResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/discount/topn/rank",
      body:"*"
    };
  }

  // 大掌柜新时段报表
  rpc ShopkeeperPeriodNew(CommonRequest) returns (PeriodNewResponse){
    option (google.api.http) = {
      post:"/api/v1/shopkeeper/report/period-new",
      body:"*"
    };
  }

  // 财务报表报表-零用现金日结报表
  rpc PettyCashDailySettlementReport(CommonRequest) returns (PettyCashDailySettlementReportResponse){
    option (google.api.http) = {
      post:"/api/v1/finance/report/petty-cash-daily-settlement",
      body:"*"
    };
  }
  // 支付明细
  rpc PaymentDetailReport(CommonRequest) returns (PaymentDetailReportResponse) {
    option (google.api.http) = {
      post:"/api/v1/report/sales/payment/detail",
      body:"*"
    };
  }
  // 商品属性销量统计表
  rpc ProductAttributeSales(CommonRequest) returns (ProductAttributeSalesResponse){
    option (google.api.http) = {
      post:"/api/v1/report/sales/product/attribute/sales",
      body:"*"
    };
  }

  // 非营运销售报表
  rpc StoreCompensationSalesReport(StoreCompensationSalesReportReq) returns (StoreCompensationSalesReportResp){
    option (google.api.http) = {
      post:"/api/v1/report/sales/store/compensation",
      body:"*"
    };
  }
}

message TestingRequest {}

message TestingResponse {
  int32 delay_seconds = 1;
}

message ImportRequest {
  string file = 1;
  string file_name = 2;
}

message ImportResponse {
  string msg = 1;
}

message StoreBusinessRequest {
  uint64 partner_id = 1;
  // 开始日期
  string start_date = 2;
  // 结束日期
  string end_date = 3;
}

message StoreBusinessResponse {
  message StoreBusinessItem {
    // 门店code
    string store_code = 1;
    // 门店名称
    string store_name = 2;
    // 营业额
    double business_amount = 3;
  }
  repeated StoreBusinessItem rows = 1;
}

message UpdateTimeRequest {}
message UpdateTimeResponse {
  string datetime = 1;
}


message DashboardRequest{
  bool is_today = 1;
  bool is_pre = 2;
  google.protobuf.Timestamp start = 3;
  google.protobuf.Timestamp end = 4;
  repeated string region_search_ids = 5;
  repeated string panel = 6;
  string lan = 7;
}

message DashboardResponse{
  google.protobuf.Struct summary = 1;
  google.protobuf.Struct line_chart = 2;
  repeated google.protobuf.Struct store_rank = 3;
  repeated google.protobuf.Struct channel_rank = 4;
  repeated google.protobuf.Struct product_rank = 5;
}

message CommonRequest{
  int32 limit = 1;
  int32 offset = 2;
  string sort = 3;
  string order = 4;
  bool include_total = 5;
  bool include_summary = 6;
  bool is_today = 7;
  bool is_pre = 8;
  string period_group_type = 9;
  string start = 10;
  string end = 11;
  string region_group_type = 12;
  int32 region_group_level = 13;
  string region_search_type = 14;
  repeated string region_search_ids = 15;
  repeated string product_category_ids = 16;
  repeated string product_ids = 17;
  repeated string payment_ids = 18;
  repeated string discount_ids = 19;
  string channel_id = 20;
  string order_type = 21;
  string lan = 22;
  string oauth_region_type = 23; // 权限基准区域
  string store_type = 24; // 门店类型
  string open_status = 25; // 开店类型
  string tag_type = 26; // 详细信息/汇总信息
  repeated string channels = 27; // 渠道列表
  repeated string logistics = 28; // 物流构成
  string category0 = 29; // 一级分类
  string category1 = 30; // 二级分类
  string category2 = 31; // 三级分类
  string refund_code = 32; // 原因编码
  int64 refund_side = 33; // 退单方
  repeated int64 period = 34; // 时段
  string price_scope = 35;
  string compare_start = 36; // 环比开始日期
  string compare_end = 37; // 环比结束日期
  string compare_same_start = 38; // 同比开始日期
  string compare_same_end = 39; // 同比结束日期
  repeated string channel_ids = 40;
  bool is_combo = 41; // 是否统计套餐
  string tag_types = 42;
  repeated string store_types = 43;

  bool is_natural = 44;
  repeated double taxes = 45;

  string order_status = 46;
  string product_id = 47;
  string code = 48;

  repeated string store_tags = 49;
  bool include_real_amount_zero = 50;
  repeated string region_codes = 51; // 省市区
  repeated string coupon_channels = 52; // 券渠道
  repeated string combo_product_ids = 53; // 套餐商品
  string search = 54;
  string ticket_no = 55; // 订单编号
  string timezone = 56; // 时区
  repeated string product_attribute_codes = 57;
}

message CommonResponse{
  repeated google.protobuf.Struct rows = 1;  // 这个才是最重要的，使用这个结构体
  google.protobuf.Struct summary = 2;
  int64 total = 3;
}

message FinancialResponse{
  message Financial{
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 business_days = 9;
    double business_amount = 10;
    double discount_amount = 11;
    double real_amount = 12;
    int64 valid_order_count = 13;
    double customer_price = 14;
    message CompositionOfBillTypes{
      string order_type = 1;
      string order_name = 2;
      double gross_amount = 3;
      int64 order_count = 4;
      double real_amount = 5;
    }
    repeated CompositionOfBillTypes composition_of_bill_types = 15;

    message CompositionOfTakeAway{
      int64 channel_id = 1;
      string channel_name = 2;
      double gross_amount = 3;
      int64 order_count = 4;
      double real_amount = 5;
    }
    repeated CompositionOfTakeAway composition_of_take_away = 16;

    message CompositionOfPaidIn{
      int64 channel_id = 1;
      string channel_name = 2;
      message Discounts{
        int64 discount_id = 1;
        string discount_name = 2;
        double real_amount = 3;
        double discount_contribute = 4;
        double transfer_real_amount = 5;
      }
      repeated Discounts discounts = 3;
      message Payments{
        int64 payment_id = 1;
        string payment_name = 2;
        double real_amount = 3;
        double discount_contribute = 4;
        double transfer_real_amount = 5;
      }
      repeated Payments payments = 4;
      double transfer_real_amount_total_payments = 5;
      double transfer_real_amount_total_discounts = 6;
      double commission = 7;
      double surcharge_amount = 8;
    }
    repeated CompositionOfPaidIn composition_of_paid_in = 17;

    double composition_of_paid_in_total = 18;

    message CompositionOfDiscount{
      int64 channel_id = 1;
      string channel_name = 2;
      double commission = 3;
      double send_fee_for_merchant = 4;
      double other_fee = 5;
      message Payment {
        int64 payment_id = 1;
        string payment_name = 2;
        double real_amount = 3;
        double discount_contribute = 4;
        double transfer_real_amount = 5;
      }
      repeated Payment payments = 6;
      message Discount {
        int64 discount_id = 1;
        string discount_name = 2;
        double real_amount = 3;
        double discount_contribute = 4;
        double transfer_real_amount = 5;
      }
      repeated Discount discounts = 7;
    }
    repeated CompositionOfDiscount composition_of_discount = 19;

    double composition_of_discount_total = 20;
    double package_fee = 21;
    double merchant_send_fee = 22;
    double sales_fee = 23;
    double dish_income_account_total = 24;
    int32 total = 25;
    string tag = 26;
    message MealSegment {
      // 餐段名称
      string meal_segment_name = 1;
      // 订单流水
      double gross_amount = 2;
      // 订单实收
      double real_amount = 3;
      // 订单数
      double valid_order_count = 4;
    }
    // 餐段组成
    repeated MealSegment meal_segments = 27;
    message GroupPurchase {
      string channel_name = 1;
      // 订单中含有xx渠道团购券的商品的商品流水
      double gross_amount = 2;
      // 订单中含有xx渠道团购券的商品的商品实收
      double real_amount = 3;
      // 含有xx渠道团购券的订单数
      double valid_order_count = 4;
    }
    // 团购渠道组成（订单中使用相关渠道团购券）
    repeated GroupPurchase group_purchases = 28;
    // 门店标签
    string store_tag = 29;
    // 客流
    double customer_count = 30;
    // 碗数
    double bowl_count = 31;
    // 小费
    double tip = 32;
    // 人均消费: 实收金额/客流
    double real_amount_per_capita = 33;
    // 日均客流: 客流/营业天数
    double customer_count_per_day = 35;
    // 日均实收: 实收金额/营业天数
    double real_amount_per_day = 36;
    double real_amount_per_bowl = 40;
    // 省
    string region_address1 = 37;
    // 市
    string region_address2 = 38;
    // 区
    string region_address3 = 39;
    // 管理区域
    string branch_region = 41;
    // 税费
    double tax_fee = 42;
    // 三方ID
    string third_party_id = 43;
    double real_amount_without_gst = 44;
  }

  repeated Financial rows = 1;
  Financial summary = 2;
}

message OpenFinancialRequest {
  string start_date = 1; // 开始日期
  string end_date = 2; // 结束日期
  bool summary = 3; // 是否汇总门店数据到一行
}

message OpenFinancialResponse {
  message OpenFinancialReportStore {
    string bus_date = 1;    // 营业日
    int64 store_id = 2;     // 门店id
    string store_code = 3;    // 门店编号
    string store_name = 4;    // 门店名称
    string store_city = 5;    // 所在城市
    string store_type = 6;    // 门店经营类型id
    string store_type_name = 7;     // 门店经营类型
    int32 business_days = 8;    // 营业天数
    double business_amount = 9;    // 总营业额
    double discount_amount = 10;    // 总优惠组成
    int32 valid_ticket_count = 11;     // 有效订单数
  }
  repeated OpenFinancialReportStore rows = 1;    // 门店数据
}

message OpenProductRequest {
  string start_date = 1; // 开始日期
  string end_date = 2; // 结束日期
  int64 store_id = 3; // 门店id
  int64 product_id = 4; // 商品skuid
  int64 channel_id = 5; // 渠道id
  int32 limit = 6; // 限制数量
  int32 offset = 7; // 偏移量
  bool total = 8; // 是否返回总数
}

message OpenProductResponse {
  message OpenProductReport {
    string bus_date = 1; // 营业日
    int64 store_id = 2; // 门店id
    string store_code = 3; // 门店编号
    string store_name = 4; // 门店名称
    int64 channel_id = 5; // 渠道id
    string channel_code = 6; // 渠道编码
    string channel_name = 7; // 渠道名字
    int64 product_id = 8; // 商品skuid
    string product_code = 9; // 商品skucode
    string product_name = 10; // 商品名称
    double gross_amount = 11; // 商品流水
    double net_amount = 12; // 商品实收
    int32 qty = 13; // 商品数量
    string sku_remark = 14; // 做法
    string created = 15; // 创建时间
    string updated = 16; // 更新时间
  }
  repeated OpenProductReport rows = 1; // 数据
  int32 total = 2; // 总数
}

message ProductResponse{
  message Product{
    string bus_date = 1;
    int64  region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string store_type = 6;
    string store_type_name = 7;
    int64 business_days = 8;
    double gross_amount = 9;
    double gross_amount_returned = 10;
    double net_amount = 11;
    double net_amount_returned = 12;
    double discount_amount = 13;
    double discount_amount_returned = 14;
    int64 item_count = 15;
    int64 item_count_returned = 16;
    message Data{
      int64 category0 = 1;
      string category0_code = 2;
      string category0_name = 3;
      double gross_amount = 4;
      double gross_amount_returned = 5;
      double net_amount = 6;
      double net_amount_returned = 7;
      double discount_amount = 8;
      double discount_amount_returned = 9;
      int64 item_count = 10;
      int64 item_count_returned = 11;
      message Data{
        int64 category1 = 1;
        string category1_code = 2;
        string category1_name = 3;
        double gross_amount = 4;
        double gross_amount_returned = 5;
        double net_amount = 6;
        double net_amount_returned = 7;
        double discount_amount = 8;
        double discount_amount_returned = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
        message Data{
          int64 category2 = 1;
          string category2_code = 2;
          string category2_name = 3;
          double gross_amount = 4;
          double gross_amount_returned = 5;
          double net_amount = 6;
          double net_amount_returned = 7;
          double discount_amount = 8;
          double discount_amount_returned = 9;
          int64 item_count = 10;
          int64 item_count_returned = 11;
          message Data{
            int64 product_id = 1;
            string product_code = 2;
            string product_name = 3;
            double gross_amount = 4;
            double gross_amount_returned = 5;
            double net_amount = 6;
            double net_amount_returned = 7;
            double discount_amount = 8;
            double discount_amount_returned = 9;
            int64 item_count = 10;
            int64 item_count_returned = 11;
            double weight = 12;
            string weight_count = 13;
            double weight_returned = 14;
            string refund_weight_count = 15;
            string unit = 16;
            string product_sale_name = 17;
            double tax_fee = 18;
          }
          repeated Data data = 12;
          double tax_fee = 13;
        }
        repeated Data data = 12;
        double tax_fee = 13;
      }
      repeated Data data = 12;
      double tax_fee = 13;
    }
    int64 total = 17;
    string region_alias = 18;
    repeated Data data = 19;
    double tax_fee = 20;
  }
  repeated Product rows = 1;
  Product summary = 2;
  int64 total = 3;
}

message ProductChannelResponse{
  message ProductChannel{
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 business_days = 9;
    double product_average_price = 10;
    double gross_amount = 11;
    double gross_amount_returned = 12;
    double net_amount = 13;
    double net_amount_returned = 14;
    double discount_amount = 15;
    double discount_amount_returned = 16;
    int64 item_count = 17;
    int64 item_count_returned = 18;
    message Child{
      int64 product_id = 1;
      string product_code = 2;
      string product_name = 3;
      int64 product_category_id = 4;
      string product_category_code = 5;
      string product_category_name = 6;
      double product_average_price = 7;
      double gross_amount = 8;
      double gross_amount_returned = 9;
      double net_amount = 10;
      double net_amount_returned = 11;
      double discount_amount = 12;
      double discount_amount_returned = 13;
      int64 item_count = 14;
      int64 item_count_returned = 15;
      message Child{
        int64 channel_id = 1;
        string channel_code = 2;
        string channel_name = 3;
        double product_average_price = 4;
        double gross_amount = 5;
        double gross_amount_returned = 6;
        double net_amount = 7;
        double net_amount_returned = 8;
        double discount_amount = 9;
        double discount_amount_returned = 10;
        int64 item_count = 11;
        int64 item_count_returned = 12;
        message Child{
          string order_type = 1;
          string order_type_name = 2;
          double product_average_price = 3;
          double gross_amount = 4;
          double gross_amount_returned = 5;
          double net_amount = 6;
          double net_amount_returned = 7;
          double discount_amount = 8;
          double discount_amount_returned = 9;
          int64 item_count = 10;
          int64 item_count_returned = 11;
          double weight = 12;
          string weight_count = 13;
          double weight_returned = 14;
          string refund_weight_count = 15;
          string unit = 16;
          double tax_fee = 17;
        }
        repeated Child child = 13;
        double weight = 14;
        string weight_count = 15;
        double weight_returned = 16;
        string refund_weight_count = 17;
        string unit = 18;
        double tax_fee = 19;
      }
      repeated Child child = 16;
      double weight = 17;
      string weight_count = 18;
      double weight_returned = 19;
      string refund_weight_count = 20;
      string unit = 21;
      double tax_fee = 22;
    }
    int64 total = 19;
    repeated Child child = 20;
    double tax_fee = 21;
  }
  repeated ProductChannel rows = 1;
  ProductChannel summary = 2;
  int64 total = 3;
}

message ProductDistributeResponse{
  message ProductDistribute{
    int64 category0 = 1;
    string category0_code = 2;
    string category0_name = 3;
    double gross_amount = 4;
    double gross_amount_returned = 5;
    double net_amount = 6;
    double net_amount_returned = 7;
    double discount_amount = 8;
    double discount_amount_returned = 9;
    int64 item_count = 10;
    int64 item_count_returned = 11;
    message Child{
      int64 category1 = 1;
      string category1_code = 2;
      string category1_name = 3;
      double gross_amount = 4;
      double gross_amount_returned = 5;
      double net_amount = 6;
      double net_amount_returned = 7;
      double discount_amount = 8;
      double discount_amount_returned = 9;
      int64 item_count = 10;
      int64 item_count_returned = 11;
      double gross_amount_percent = 12;
      double net_amount_percent = 13;
      double discount_amount_percent = 14;
      double item_count_percent = 15;
      message Child{
        int64 category2 = 1;
        string category2_code = 2;
        string category2_name = 3;
        double gross_amount = 4;
        double gross_amount_returned = 5;
        double net_amount = 6;
        double net_amount_returned = 7;
        double discount_amount = 8;
        double discount_amount_returned = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
        double gross_amount_percent = 12;
        double net_amount_percent = 13;
        double discount_amount_percent = 14;
        double item_count_percent = 15;
      }
      repeated Child child = 16;
    }
    repeated Child child = 12;
    double gross_amount_percent = 13;
    double net_amount_percent = 14;
    double discount_amount_percent = 15;
    double item_count_percent = 16;
  }
  repeated ProductDistribute rows = 1;
  ProductDistribute summary = 2;
}

message DiscountSalesResponse{
  message DiscountSales{
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 business_days = 9;
    int64 discount_id = 10;
    string discount_code = 11;
    string discount_name = 12;
    string discount_type = 13;
    double gross_amount = 14;
    int64 product_count = 15;
    double discount_amount = 16;
    double discount_contribute = 17;
    string transfer_real_amount = 18;
    int64 channel_id = 20;
    string channel_code = 21;
    string channel_name = 22;
    int64 discount_count = 23;
    int64  branch_id = 24;
    string branch_code = 25;
    string branch_name = 26;
  }
  repeated DiscountSales rows = 1;
  DiscountSales summary = 2;
  int64 total = 3;
}

message ProductPeriodResponse{
  message ProductPeriod{
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    double gross_amount = 6;
    double net_amount = 7;
    int64 item_count = 8;
    string store_type = 9;
    string store_type_name = 10;
    int64 business_days = 11;
    message Data{
      message H00{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H00 h00 = 1;
      message H01{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H01 h01 = 2;
      message H02{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H02 h02 = 3;
      message H03{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H03 h03 = 4;
      message H04{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H04 h04 = 5;
      message H05{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H05 h05 = 6;
      message H06{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H06 h06 = 7;
      message H07{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H07 h07 = 8;
      message H08{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H08 h08 = 9;
      message H09{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H09 h09 = 10;
      message H10{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H10 h10 = 11;
      message H11{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H11 h11 = 12;
      message H12{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H12 h12 = 13;
      message H13{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H13 h13 = 14;
      message H14{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H14 h14 = 15;
      message H15{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H15 h15 = 16;
      message H16{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H16 h16 = 17;
      message H17{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H17 h17 = 18;
      message H18{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H18 h18 = 19;
      message H19{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H19 h19 = 20;
      message H20{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H20 h20 = 21;
      message H21{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H21 h21 = 22;
      message H22{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H22 h22 = 23;
      message H23{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        double tax_fee = 5;
      }
      H23 h23 = 24;
    }
    Data data = 12;
    message Child{
      int64 product_id = 1;
      string product_code = 2;
      string product_name = 3;
      int64 product_category_id = 4;
      string product_category_code = 5;
      string product_category_name = 6;
      message H00{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;

      }
      H00 h00 = 7;
      message H01{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H01 h01 = 8;
      message H02{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H02 h02 = 9;
      message H03{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H03 h03 = 10;
      message H04{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H04 h04 = 11;
      message H05{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H05 h05 = 12;
      message H06{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H06 h06 = 13;
      message H07{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H07 h07 = 14;
      message H08{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H08 h08 = 15;
      message H09{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H09 h09 = 16;
      message H10{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H10 h10 = 17;
      message H11{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H11 h11 = 18;
      message H12{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H12 h12 = 19;
      message H13{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H13 h13 = 20;
      message H14{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H14 h14 = 21;
      message H15{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H15 h15 = 22;
      message H16{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H16 h16 = 23;
      message H17{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H17 h17 = 24;
      message H18{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H18 h18 = 25;
      message H19{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H19 h19 = 26;
      message H20{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H20 h20 = 27;
      message H21{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H21 h21 = 28;
      message H22{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H22 h22 = 29;
      message H23{
        double gross_amount = 1;
        double net_amount = 2;
        int64 item_count = 3;
        double weight = 4;
        string weight_count = 5;
        double tax_fee = 6;
      }
      H23 h23 = 30;
    }
    repeated Child child = 13;
    string region_alias = 14;
    double tax_fee = 15;
  }
  repeated ProductPeriod rows = 1;
  ProductPeriod summary = 2;
  int64 total = 3;
}

message DetailForDiscountAndRealAmountResponse{
  message Detail{
    int64 store_id = 1;
    string store_code = 2;
    string store_name = 3;
    double discount_amount = 4;
    double real_amount = 5;
    message CompositionOfPay{
      int64 channel_id = 1;
      string channel_code = 2;
      string channel_name = 3;
      message Payment {
        int64 payment_id = 1;
        string payment_code = 2;
        string payment_name = 3;
        double real_amount = 4;
        double discount_contribute = 5;
      }
      repeated Payment payments = 4;
      message Discount {
        int64 discount_id = 1;
        string discount_code = 2;
        string discount_name = 3;
        double real_amount = 4;
        double discount_contribute = 5;
      }
      repeated Discount discounts = 5;
      double real_amount_total_payments = 6;
      double real_amount_total_discounts = 7;
    }
    repeated CompositionOfPay composition_of_pay = 6;
    message CompositionOfDiscount{
      int64 channel_id = 1;
      string channel_code = 2;
      string channel_name = 3;
      double commission = 4;
      double send_fee_for_merchant = 5;
      double other_fee = 6;
      message Payments{
        int64 payment_id = 1;
        string payment_code = 2;
        string payment_name = 3;
        double real_amount = 4;
        double discount_contribute = 5;
      }
      repeated Payments payments = 7;
      message Discounts{
        int64 discount_id = 1;
        string discount_code = 2;
        string discount_name = 3;
        double real_amount = 4;
        double discount_contribute = 5;
      }
      repeated Discounts discounts = 8;
      double discount_contribute_total_payments = 9;
      double discount_contribute_total_discounts = 10;
    }
    repeated CompositionOfDiscount composition_of_discount = 7;
  }
  repeated Detail rows = 1;
}

message ProductTop20RankResponse{
  message ProductTop20Rank{
    int64 product_id = 1;
    string product_code = 2;
    string product_name = 3;
    double gross_amount = 4;
    double gross_amount_percent = 5;
    double net_amount = 6;
    double net_amount_percent = 7;
    int64 item_count = 8;
    double item_count_percent = 9;
    double weight_count = 10;
    string unit = 11;
  }
  repeated ProductTop20Rank rows = 1;
  ProductTop20Rank summary = 2;
}

message RefundAnalysisResponse {
  message RefundAnalysis {
    string bus_date = 1;                  // 营业时间
    int64 channel_id = 2;                 // 渠道ID
    string channel_code = 3;              // 渠道Code
    string channel_name = 4;              // 渠道名称
    string pos_device_code = 5;           // POS编号
    string ticket_no = 6;                 // 交易号
    int64 order_count_returned = 7;       // 退单数
    double pay_amount_returned = 8;       // 退款金额
    string refund_side = 9;               // 退款方
    string operator_name = 10;            // 操作人
    string refund_code = 11;              // 退单原因Code
    string refund_reason = 12;            // 退款原因
    int64 branch_id = 13;                 // 管理区域ID
    string branch_name = 14;              // 管理区域名称
    string branch_code = 15;              // 管理区域Code
    int64 region_id = 16;                 // 门店ID
    string region_code = 17;              // 门店Code
    string region_name = 18;              // 门店名称
    string origin_real_ticket_no = 19;    // 取餐号(原单)
    int64 payment_id = 20;         // 原单支付方式ID
    string payment_code = 21;      // 原单支付方式Code
    string payment_name = 22;      // 原单支付方式名称
    string origin_order_time = 23;        // 原单交易时间
    string order_time = 24;               // 订单时间
  }
  repeated RefundAnalysis rows = 2;
  int64 total = 3;
}

message BusinessSituationResponse {
  message BusinessSituation {
    int64 store_number = 1;               // 门店数
    int64 product_count = 2;              // 商品销售数量
    int64 cup_count = 54;                 // 商品杯数
    double business_amount = 3;            // 营业额流水
    double expend_amount = 4;             // 支出(交易)
    double real_amount = 5;               // 实收(交易)
    double gross_amount = 6;              // 商品流水
    double net_amount = 7;                // 商品实收
    double discount_amount = 8;           // 折扣
    int64 refund_order_count = 9;         // 退单数
    int64 valid_order_count = 10;         // 有效订单数
    double customer_price = 11;            // 单均价
    double merchant_allowance = 12;         // 商家活动补贴
    double transfer_real_amount = 13;      // 实收金额(财务)
    double discount_contribute = 14;       // 优惠组成(财务)
    double average_business_amount = 15;   // 店均营业额
    double average_valid_order_count = 16;  // 店均订单数
    message Child {
      double business_amount = 5;
      double transfer_real_amount = 6;
      string order_type = 7;
    }
    repeated Child child = 17;
    double store_business_amount = 18;
    double takeout_business_amount = 19;

    double compare_business_amount = 20;          // 环比营业额流水
    double compare_expend_amount = 21;              // 环比支出交易
    double compare_real_amount = 22;               // 环比实收交易
    double compare_merchant_allowance = 23;        // 环比商家补贴
    int64 compare_valid_order_count = 24;         // 环比有效订单数
    int64 compare_refund_order_count = 25;        // 环比退单数
    double compare_customer_price = 26;            // 环比单均价
    double compare_discount_contribute = 27;       // 环比优惠组成
    double compare_average_business_amount = 28;   // 环比店均营业额
    double compare_transfer_real_amount = 29;      // 环比财务实收
    double compare_average_valid_order_count = 30; // 环比店均订单数
    int64 compare_product_count = 31; // 环比商品数量
    int64 compare_cup_count = 55; // 环比商品杯数
    double compare_store_business_amount = 32; // 环比门店流水
    double compare_takeout_business_amount = 33; // 环比外卖流水

    double compare_same_business_amount = 34;          // 同比营业额流水
    double compare_same_expend_amount = 35;             // 同比支出交易
    double compare_same_real_amount = 36;               // 同比实收交易
    double compare_same_merchant_allowance = 37;        // 同比商家补贴
    int64 compare_same_valid_order_count = 38;         // 同比有效订单数
    int64 compare_same_refund_order_count = 39;        // 同比退单数
    double compare_same_customer_price = 40;         // 同比单均价
    double compare_same_discount_contribute = 41;       // 同比优惠组成
    double compare_same_average_business_amount = 42;   // 同比店均营业额
    double compare_same_transfer_real_amount = 43;      // 同比财务实收
    double compare_same_average_valid_order_count = 44; // 同比店均订单数
    int64 compare_same_product_count = 45; // 同比商品数量
    int64 compare_same_cup_count = 56; // 同比商品杯数
    double compare_same_store_business_amount = 46; // 同比门店流水
    double compare_same_takeout_business_amount = 47; // 同比外卖流水
    double store_transfer_real_amount = 48;
    double takeout_transfer_real_amount = 49;
    double compare_store_transfer_real_amount = 50;
    double compare_takeout_transfer_real_amount = 51;
    double compare_same_store_transfer_real_amount = 52;
    double compare_same_takeout_transfer_real_amount = 53;
  }
  repeated BusinessSituation rows = 1;
  repeated BusinessSituation compare_rows = 2;
  repeated BusinessSituation compare_same_rows = 3;
}

message PaymentStatisticsResponse {
  message PaymentStatistics {
    // 营业时间
    string bus_date = 1;
    // 管理区域
    int64 branch_id = 2;
    // 管理区域名称
    string branch_name = 3;
    // 管理区域 Code
    string branch_code = 4;
    // 门店
    int64 region_id = 5;
    // 门店 Code
    string region_code = 6;
    // 门店名称
    string region_name = 7;
    // 支付供应商编码
    string cooperation_code = 8;
    // 支付类型 ID
    int64 payment_category_id = 9;
    // 支付类型名称
    string payment_category_name = 10;
    // 支付类型编码
    string payment_category_code = 11;
    // 支付方式 ID
    int64 payment_id = 12;
    // 支付方式编码
    string payment_code = 13;
    // 支付方式名称
    string payment_name = 14;
    // 支付金额
    double pay_amount = 15;
    // 财务实收金额(转换后)
    double transfer_real_amount = 16;
    // 收款必数
    int64 item_count = 17;
    // 财务实收金额(转换后)-退款
    double transfer_real_amount_returned = 19;
    // 退款笔数
    int64 item_count_returned = 20;
    //  预定金额
    double deposit_amount = 21;
    // 预定笔数
    int64 deposit_item_count = 22;
    // 总笔数
    int64 all_item_count = 23;
    int64 channel_id = 24;
    string channel_code = 25;
    string channel_name = 26;
  }
  repeated PaymentStatistics rows = 1;
  PaymentStatistics summary = 2;
  int64 total = 3;
}

message StoreChannelSalesResponse {
  message StoreChannelSales{
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 business_days = 9;
    double business_amount = 10;
    double real_amount = 11;
    double expend_amount = 12;
    double customer_price = 13;
    int64 valid_order_count = 14;
    double gross_amount = 15;
    double net_amount = 16;
    double discount_amount = 17;
    double merchant_allowance = 18;
    double package_fee = 19;
    double delivery_fee = 20;
    double send_fee = 21;
    double merchant_send_fee = 22;
    double platform_send_fee = 23;
    double commission = 24;
    double service_fee = 25;
    double tip = 26;
    double receivable = 27;
    double pay_amount = 28;
    double rounding = 29;
    double overflow_amount = 30;
    double change_amount = 31;
    double transfer_real_amount = 32;
    double discount_contribute = 33;
    double discount_merchant_contribute = 34;
    double pay_merchant_contribute = 35;
    double real_amount_discount = 36;
    double real_amount_payment = 37;
    message Child {
      int64 channel_id = 1;
      string channel_code = 2;
      string channel_name = 3;
      double business_amount = 4;
      double real_amount = 5;
      double expend_amount = 6;
      double customer_price = 7;
      int64 valid_order_count = 8;
      double gross_amount = 9;
      double net_amount = 10;
      double discount_amount = 11;
      double merchant_allowance = 12;
      double package_fee = 13;
      double delivery_fee = 14;
      double send_fee = 15;
      double merchant_send_fee = 16;
      double platform_send_fee = 17;
      double commission = 18;
      double service_fee = 19;
      double tip = 20;
      double receivable = 21;
      double pay_amount = 22;
      double rounding = 23;
      double overflow_amount = 24;
      double change_amount = 25;
      double transfer_real_amount = 26;
      double discount_contribute = 27;
      double discount_merchant_contribute = 28;
      double pay_merchant_contribute = 29;
      double real_amount_discount = 30;
      double real_amount_payment = 31;
      message Child {
        string order_type = 1;
        string order_type_name = 2;
        double business_amount = 3;
        double real_amount = 4;
        double expend_amount = 5;
        double customer_price = 6;
        int64 valid_order_count = 7;
        double gross_amount = 8;
        double net_amount = 9;
        double discount_amount = 10;
        double merchant_allowance = 11;
        double package_fee = 12;
        double delivery_fee = 13;
        double send_fee = 14;
        double merchant_send_fee = 15;
        double platform_send_fee = 16;
        double commission = 17;
        double service_fee = 18;
        double tip = 19;
        double receivable = 20;
        double pay_amount = 21;
        double rounding = 22;
        double overflow_amount = 23;
        double change_amount = 24;
        double transfer_real_amount = 25;
        double discount_contribute = 26;
        double discount_merchant_contribute = 27;
        double pay_merchant_contribute = 28;
        double real_amount_discount = 29;
        double real_amount_payment = 30;
        double tax_fee = 31;
      }
      repeated Child child = 32;
      double tax_fee = 33;
    }
    repeated Child child = 38;
    double tax_fee = 39;
  }
  repeated StoreChannelSales rows = 1;
  StoreChannelSales summary = 2;
  int64 total = 3;
}

message PaymentPeriodResponse {
  message PaymentPeriod {
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 business_days = 9;
    double receivable = 10;
    double pay_amount = 11;
    double transfer_real_amount = 12;
    double payment_transfer_amount = 13;
    double cost = 14;
    double tp_allowance = 15;
    double rounding = 16;
    double overflow_amount = 17;
    double change_amount = 18;
    int64 item_count = 19;
    int64 item_count_returned = 20;
    message Data{
      message H00{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H00 h00 = 1;
      message H01{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H01 h01 = 2;
      message H02{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H02 h02 = 3;
      message H03{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H03 h03 = 4;
      message H04{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H04 h04 = 5;
      message H05{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H05 h05 = 6;
      message H06{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H06 h06 = 7;
      message H07{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H07 h07 = 8;
      message H08{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H08 h08 = 9;
      message H09{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H09 h09 = 10;
      message H10{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H10 h10 = 11;
      message H11{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H11 h11 = 12;
      message H12{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H12 h12 = 13;
      message H13{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H13 h13 = 14;
      message H14{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H14 h14 = 15;
      message H15{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H15 h15 = 16;
      message H16{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H16 h16 = 17;
      message H17{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H17 h17 = 18;
      message H18{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H18 h18 = 19;
      message H19{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H19 h19 = 20;
      message H20{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H20 h20 = 21;
      message H21{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H21 h21 = 22;
      message H22{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H22 h22 = 23;
      message H23{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H23 h23 = 24;
    }
    Data data = 21;
    message Child{
      int64 payment_id = 1;
      string payment_code = 2;
      string payment_name = 3;
      string payment_type = 4;
      message H00{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H00 h00 = 5;
      message H01{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H01 h01 = 6;
      message H02{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H02 h02 = 7;
      message H03{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H03 h03 = 8;
      message H04{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H04 h04 = 9;
      message H05{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H05 h05 = 10;
      message H06{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H06 h06 = 11;
      message H07{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H07 h07 = 12;
      message H08{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H08 h08 = 13;
      message H09{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H09 h09 = 14;
      message H10{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H10 h10 = 15;
      message H11{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H11 h11 = 16;
      message H12{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H12 h12 = 17;
      message H13{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H13 h13 = 18;
      message H14{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H14 h14 = 19;
      message H15{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H15 h15 = 20;
      message H16{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H16 h16 = 21;
      message H17{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H17 h17 = 22;
      message H18{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H18 h18 = 23;
      message H19{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H19 h19 = 24;
      message H20{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H20 h20 = 25;
      message H21{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H21 h21 = 26;
      message H22{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H22 h22 = 27;
      message H23{
        double receivable = 1;
        double pay_amount = 2;
        double transfer_real_amount = 3;
        double payment_transfer_amount = 4;
        double cost = 5;
        double tp_allowance = 6;
        double rounding = 7;
        double overflow_amount = 8;
        double change_amount = 9;
        int64 item_count = 10;
        int64 item_count_returned = 11;
      }
      H23 h23 = 28;
    }
    repeated Child child = 22;
  }
  repeated PaymentPeriod rows = 1;
  PaymentPeriod summary = 2;
  int64 total = 3;
}

message ShopKeeperDashBoardResponse {
  message Dashboard {
    double business_amount = 1;
    int64 valid_order_count = 2;
    double customer_value = 3;
  }
  repeated Dashboard rows = 1;
}

message ChannelDistributeResponse {
  message ChannelDistribute {
    int64 channel_id = 1;
    string channel_code = 2;
    string channel_name = 3;
    double business_amount = 4;
    double proportion_business_amount = 5;
    double real_amount = 6;
    double proportion_real_amount = 7;
    int64 valid_order_count = 8;
    double proportion_valid_order_count = 9;
    int64 refund_order_count = 10;
    double proportion_refund_order_count = 11;
    double discount_contribute = 12;
    double proportion_discount_contribute = 13;
    double transfer_real_amount = 14;
    double proportion_transfer_real_amount = 15;
    double discount_amount = 16;
    double gross_amount_returned = 17;
    double compare_business_amount = 18;
    double compare_same_business_amount = 19;
    double increase_compare_business_amount = 20;
    double increase_compare_same_business_amount = 21;
  }
  repeated ChannelDistribute rows = 1;
  repeated ChannelDistribute compare_rows = 2;
  repeated ChannelDistribute compare_same_rows = 3;
  ChannelDistribute summary = 4;
}

message LogRankResponse {
  message LogRank {
    string order_type = 1;
    string order_type_name = 2;
    double business_amount = 3;
    double proportion_business_amount = 4;
    double real_amount = 5;
    double proportion_real_amount = 6;
    int64 valid_order_count = 7;
    double proportion_valid_order_count = 8;
    double compare_business_amount = 9;
    double compare_same_business_amount = 10;
    double increase_compare_business_amount = 11;
    double increase_compare_same_business_amount = 12;
  }
  repeated LogRank rows = 1;
  repeated LogRank compare_rows = 2;
  repeated LogRank compare_same_rows = 3;
  LogRank summary = 4;
}

message CustomerFormResponse {
  message CustomerForm {
    bool member = 1;
    string member_name = 2;
    double business_amount = 3;
    double proportion_business_amount = 4;
    double merchant_allowance = 5;
    double proportion_merchant_allowance = 6;
    int64 valid_order_count = 7;
    double proportion_valid_order_count = 8;
  }
  repeated CustomerForm rows = 1;
  repeated CustomerForm compare_rows = 2;
  repeated CustomerForm compare_same_rows = 3;
  CustomerForm summary = 4;
}

message ProductFlavorResponse {
  message ProductFlavor {
    string bus_date = 1;
    int64 region_id = 2;
    string region_code = 3;
    string region_name = 4;
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 product_id = 9;
    string product_code = 10;
    string product_name = 11;
    string flavor = 12;
    string sku_remark = 13;
    string accessories = 14;
    int64 accessory_id = 15;
    string accessory_name = 16;
    string flavor_distribute = 17;
    string attribute_name_code = 18;
    string attribute_name_name = 19;
    string attribute_value_code = 20;
    string attribute_value_name = 21;
    int64 product_count = 22;
    int64 product_count_total = 23;
    double percent_product_count = 24;
    message attribute_name {
      string attribute_name_code = 1;
      string attribute_name_name = 2;
      int64 product_count = 3;
      double percent_product_count = 4;
      message attribute_name_value {
        string attribute_value_code = 1;
        string attribute_value_name = 2;
        int64 product_count = 3;
        double percent_product_count = 4;
      }
      repeated attribute_name_value attribute_name_values = 5;
    }
    repeated attribute_name attribute_names = 25;
    message accessory {
      string accessory_name = 1;
      int64 product_count = 2;
      double percent_product_count = 3;
    }
    repeated accessory feeds = 26;
  }
  repeated ProductFlavor rows = 1;
  repeated ProductFlavor graph = 2;
  int64 total = 3;
}

message ProductAttributeResponse {
  message ProductAttribute {
    int64 product_id = 1;
    string product_code = 2;
    string product_name = 3;
    int64 product_count = 4;
    int64 product_count_total = 5;
    double percent_product_count = 6;
    string flavor = 7;
    //    map<string, string> sku_remark = 8;
    //    map<string, int64> accessories = 9;
    message Attribute {
      string attribute_name = 1;
      message AttributeNameValue {
        string attribute_value_name = 1;
        int64 product_count = 2;
        double product_count_percent = 3;
      }
      repeated AttributeNameValue attribute_name_values = 2;
    }
    repeated Attribute attribute_names = 8;
    message Feed {
      string feed_name = 1;
      int64 feed_count = 2;
      double feed_count_percent = 3;
    }
    repeated Feed feeds = 9;
  }
  repeated ProductAttribute rows = 1;
  repeated ProductAttribute graphs = 2;
}

message ProductTaxResponse {
  message ProductTaxSales {
    int64 store_id = 1;
    string store_code = 2;
    string store_name = 3;
    string store_address = 4;
    string store_alias = 5;
    string store_status = 6;
    string store_type = 7;
    string store_type_name = 8;
    int64 company_id = 9;
    string company_code = 10;
    string company_name = 11;
    int64 business_days = 12;
    int64 category_id = 13;
    string category_code = 14;
    string category_name = 15;
    int64 product_id = 16;
    string product_code = 17;
    string product_name = 18;
    int64 channel_id = 19;
    string channel_code = 20;
    string channel_name = 21;
    int64 product_count = 22;
    double gross_amount = 23;
    double discount_amount = 24;
    double finance_real_amount = 25;
    double transfer_amount = 26;
    double discount_transfer_amount = 27;
    double product_tax = 28;
    double invoice_amount = 29;
    int64 total = 30;
  }
  repeated ProductTaxSales rows = 1;
  ProductTaxSales summary = 2;
  int64 total = 3;
  repeated ProductTaxSales send_fees = 5;
  repeated ProductTaxSales package_fees = 6;
}

message ProductPolymerResponse{
  message ProductPolymer{
    int64 channel_id = 1;
    string channel_name = 2;
    int64 item_count = 3;
    double gross_amount = 4;
    double net_amount = 5;
    double weight_count = 6;
    string unit = 7;
  }
  repeated ProductPolymer rows = 1;
  ProductPolymer summary = 2;
}

message ShopkeeperProductPeriodResponse {
  message ShopkeeperProductPeriodItem {
    string unit = 1;
    float weight = 2;
    string weight_count = 3;
    float gross_amount = 4;
    float net_amount = 5;
    int64 item_count = 6;
  }
  message ShopkeeperProductPeriodRow {
    int64 product_id = 1;
    string product_code = 2;
    string product_name = 3;
    repeated ShopkeeperProductPeriodItem periods = 4;
    ShopkeeperProductPeriodItem row_summary = 5;
  }
  repeated ShopkeeperProductPeriodRow rows = 1;
  repeated ShopkeeperProductPeriodItem period_summary = 2;
  ShopkeeperProductPeriodItem summary = 3;
  int64 total_rows = 4;
}

message ProductMakeTimeResponse{
  message ProductMakeTimeItem {
    int64 region_id = 1;
    string region_code = 2;
    string region_name = 3;
    string region_address = 4;
    string region_alias = 5;
    int64 store_type = 6;
    string store_type_name = 7;
    // 时段
    string time_period = 8;
    int64 product_id = 9;
    // 商品编号
    string product_code = 10;
    // 商品名称
    string product_name = 11;
    // 商品类目id
    int64 product_category_id = 12;
    string product_category_code = 13;
    // 商品类目名称
    string product_category_name = 14;
    // 杯数
    int32 cup_count = 15;
    // 平均每杯制作时间(min)
    string make_time_avg_per_cup = 16;
  }
  repeated ProductMakeTimeItem rows = 1;
  int64 total = 3;
}

message StoreMakeTimeResponse{
  message StoreMakeTimeItem {
    int64 region_id = 1;
    string region_code = 2;
    string region_name = 3;
    string region_address = 4;
    string region_alias = 5;
    int64 store_type = 6;
    string store_type_name = 7;
    // 时段
    string time_period = 8;
    // 单数
    int32 order_count = 9;
    // 平均每单制作时间(min)
    string make_time_avg_per_order = 10;
    // 杯数
    int32 cup_count = 11;
    // 平均每杯制作时间(min)
    string make_time_avg_per_cup = 12;
  }
  repeated StoreMakeTimeItem rows = 1;
  int64 total = 3;
}


message RecentSummaryResponse {
  message RecentSummaryItem {
    // 营业日期
    string bus_date = 1;
    int64 region_id = 2;
    // 门店编号
    string region_code = 3;
    // 门店名称
    string region_name = 4;
    // 所在城市
    string region_address = 5;
    string region_alias = 6;
    string store_type = 7;
    // 门店经营类型
    string store_type_name = 8;
    int64 business_days = 9;

    // 营业额
    double business_amount = 10;
    double compare_business_amount = 11;

    // 实收金额
    double finance_real_amount = 12;
    double compare_finance_real_amount = 13;

    // 优惠金额
    double finance_expend_amount = 14;
    double compare_finance_expend_amount = 15;

    // 订单数
    int64 valid_order_count = 16;
    int64 compare_valid_order_count = 17;

    // 消费者实付金额
    double pay_amount = 18;
    double compare_pay_amount = 19;

    double transfer_real_amount = 20;
    double compare_transfer_real_amount = 21;

    message PaymentItem {
      int64 payment_id = 1;
      string payment_name = 2;
      double pay_amount = 3;
      double transfer_real_amount = 4;
    }

    // 各个支付方式具体支付金额
    repeated PaymentItem payments = 22;

    message Child {
      int64 channel_id = 1;
      string channel_code = 2;
      // 渠道名称
      string channel_name = 3;

      double business_amount = 4;
      double finance_real_amount = 5;
      double finance_expend_amount = 6;
      int64 valid_order_count = 7;
      double pay_amount = 8;
      double transfer_real_amount = 9;
    }
    // 渠道
    repeated Child child = 23;
  }
  repeated RecentSummaryItem rows = 1;
  RecentSummaryItem summary = 2;
  int64 total = 3;
}

message ProductTopNRankResponse {
  message ProductTopNRank {
    // 商品id
    int64 product_id = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 商品流水
    double gross_amount = 4;
    // 商品流水占比
    double gross_amount_percent = 5;
    // 商品实收
    double net_amount = 6;
    // 商品实收占比
    double net_amount_percent = 7;
    // 商品数量
    int64 item_count = 8;
    // 商品数量占比
    double item_count_percent = 9;
    // 商品重量
    double weight_count = 10;
    // 商品单位
    string unit = 11;
  }
  repeated ProductTopNRank rows = 1;
  ProductTopNRank summary = 2;
}

message ProductStoreRankResponse {
  message ProductStoreRank {
    // 门店id
    int64 store_id = 1;
    // 门店编号
    string store_code = 2;
    // 门店名称
    string store_name = 3;
    // 商品流水
    double gross_amount = 4;
    // 商品流水占比
    double gross_amount_percent = 5;
    // 商品实收
    double net_amount = 6;
    // 商品实收占比
    double net_amount_percent = 7;
    // 商品数量
    int64 item_count = 8;
    // 商品数量占比
    double item_count_percent = 9;
    // 商品重量
    double weight_count = 10;
    // 商品重量占比
    double weight_count_percent = 11;
    // 商品单位
    string unit = 12;
  }
  repeated ProductStoreRank rows = 1;
  ProductStoreRank summary = 2;
}

message QueryReportConfigRequest {
  string type = 1;
}

message SaveReportConfigRequest {
  string type = 1;
  string config = 2;
}

message ReportConfigResponse {
  int64 id = 1;
  int64 partner_id = 2;
  int64 user_id = 3;
  string type = 4;
  string config = 5;
}

message DiscountTopNRankResponse {
  message DiscountTopNRank {
    // 优惠名称
    string discount_name = 1;
    // 优惠金额
    double discount_amount = 2;
    // 优惠金额占比
    double discount_amount_percent = 3;
    // 优惠数量
    int64 discount_count = 4;
    // 优惠数量占比
    double discount_count_percent = 5;
  }
  repeated DiscountTopNRank rows = 1;
  DiscountTopNRank summary = 2;
}

message PeriodNewResponse {
  message PeriodNewRow {
    string period_name = 1;
    string period_time = 2;
    double real_amount = 3;
    double finance_real_amount = 4;
    double business_amount = 5;
    int64 valid_ticket_count = 6;
  }
  repeated PeriodNewRow rows = 1;
}

message PosPromotionResponse {
  message Row {
    // 促销名称
    string promotion_name = 1;
    // 促销类型名称
    string promotion_cate_name = 2;
    // 优惠金额
    double discount = 3;
    // 优惠金额占比
    double discount_percent = 4;
    // 使用次数
    double usage_count = 5;
  }

  repeated Row rows = 1;
  Row summary = 2;
}

message AdjustResponse {
  message AdjustRow {
    // 操作时间
    string operate_time = 1;
    // 门店名称
    string store_name = 2;
    // 操作人
    string operator_name = 3;
    // 商品编号
    string product_code = 4;
    // 商品名称
    string product_name = 5;
    // 商品规格
    string sku_remark_name = 6;
    // 报损原因编号
    string reason_code = 7;
    // 报损原因名称
    string reason_name = 8;
    // 商品单价
    double price = 9;
    // 商品数量
    double qty = 10;
    // 商品金额
    double amount = 11;
  }

  repeated AdjustRow rows = 1;
  int64 total = 2;
}

message HubReportQuery {
  string start_time = 1;
  string end_time = 2;
  string operator = 3;
  bool is_hour = 4;
  bool  is_meal = 5;
  repeated string query = 6;
  repeated string channel = 7;
  string host = 8;
  string bussinessDayStartTime = 9;
}

message HourReportResponse {
  bytes extend = 1;
}

message CategoryReportResponse {
  bytes extend = 1;
}

message ProductReportResponse {
  string id = 1;
  message Product{
    string name = 1;  //产品名称
    double sum_amount = 2; //商品原价
    double sum_net_amount = 3;//商品实收
    int32 quantity = 4;// 商品数量
    bool has_weight = 5;// 是否称重
    double weight = 6;// 称重商品的重量
    string tp_name = 7;
    double taxAmount = 8;
  }
  repeated Product channel = 2;
  message SumProduct{
    double sum_amount = 1; //商品原价
    double sum_net_amount = 2;//商品实收
    int32 quantity = 3;// 商品数量
    double weight = 4;// 称重商品的重量
    double taxAmount = 5;
  }
  SumProduct sum = 3;
}

message BusinessReportResponse {
  string extend = 1;
}

message ShiftReportRequest{
  string shiftNumber = 1;
}

message storeDailyInfo{
  string id = 1;
  string code = 2;
  string name = 3;
  string address = 4;
  string saleTime = 5;
  string posId = 6;
  string posCode = 7;
  string busDate = 8;
}

message userInfo{
  string code = 1;
  string name = 2;
}

// 日结中的支付统计
message PayItem{
  double  pay_amount = 1; //支付金额
  int32  count = 2; // 支付笔数
  string payment_name = 3; // 支付方式名称
  int32  negativeCount = 4; // 退款笔数
  int32  sort = 5; // 排序
  double merchant_allowance = 6; // 商家让利金额
}

message PayioInfo{
  double  payin = 1;
  double  payout = 2;
  repeated google.protobuf.Struct payinNew = 3;
  repeated google.protobuf.Struct payoutNew = 4;
  double sum = 5;
}

// 日结中的促销统计
message PromotionItem{
  string type = 1;
  double discount = 2; // 优惠金额
  int32  count = 3; // 优惠次数
  int32  sort = 4;
}

// PromotionChannel  渠道统计
message PromotionChannel{
  string code = 1;
  string type = 2;
  string name = 3;
  string source = 4;
  bool is_product = 5;
  Item promotion_coupons = 6;
  Item promotion_product = 7;
}

message Item {
  double discount = 1; // 优惠金额
  double real_amount = 2; // 商家实收
  double sum = 3; // 总金额
  int32 count = 4;
}

message TaxItem{
  string code = 1;
  string name = 2;
  double totalAmount = 3;
  double taxAmount = 4;
  int32 count = 5;
  int32 negativeCount = 6;
}

// 收银员销售汇总
message UserSaleItem{
  string code = 1;
  double totalAmount = 2;
  string name = 3;
}

// 通用统计信息
message commonStatistical{
  double business_amount = 1; // 营业额
  double real_amount = 2; // 实收金额
  double expend_amount = 3; // 支出金额
  double receivable = 4; // 应付金额
  double gross_amount = 5; // 商品原价合计
  int32  count = 6; // 有效单数
  int32 trade = 7; // 交易单数
  int32 chargeback_count = 8; // 退单数
  double price_trade = 9;//单均价
  double price_quantity = 10;//单均价
  double amount = 11;//甩尾金额
  repeated string id = 12;
}

// 日结小票的统计信息
message TicketStatistic1 {
  string name = 1;
  double value = 2;
}

message ticketStatistic{
  string startTime = 1;
  string endTime = 2;
  string deviceIdentityCode = 3;
  repeated PayItem pays = 4;
  int32 pay_count = 5;
  repeated PayItem pay_overflow = 6;
  repeated PayItem onlinePays = 7;//线上实收汇总
  repeated PayItem offlinePays = 8;//线下实收汇总
  repeated PayItem change = 9;//线下找零汇总
  int32 count = 10;//小票单数
  double sumOverflow = 11;
  bool unupload = 12;
  PayioInfo payInfo = 13;
  repeated PromotionItem promotions = 14;
  repeated TaxItem taxs = 15;
  commonStatistical discountDealsStatistics = 16;//折扣统计
  commonStatistical allTicketStatistics = 17;//销售统计
  commonStatistical  removeZeroStatistics = 18;//抹零统计
  commonStatistical cancelTicketStatistics = 19;//取消订单统计
  commonStatistical roundingStatistics = 20;//甩尾统计
  commonStatistical  overflowStatistics = 21;//营收差异统计
  double diff = 22;//营收差异
  double netTrade = 23;//销售净额
  double  taxTotalAmount = 24;//税总计
  double paysAmount = 25;//支付小计
  double offlinePaysAmount = 26;//线下支付小计
  double onlinePaysAmount = 27;//线上支付小计
  double changeAmount = 28;//找零小计
  double promotionsAmount = 29;//促销金额小计
  double pay_merchant_allowance = 30;
  double overflow_amount = 31;
  double overflow_count = 32;
  double overflow_merchant_allowance = 33;
  double cash_refund_amount = 34;//非原单退款
  double cash_refund_count = 35;//非原单退款笔数
  double  frm_loss_amount = 36;//门店报损金额
  double frm_loss_count = 37;//门店报损数量
}


message ShiftInfoResponse{
  string extend = 1;
}

message DailyKnotInfo{
  storeDailyInfo store = 1;
  userInfo userInfo = 2;
  repeated PayItem pays = 3;
  repeated PayItem pay_overflow = 4;
  repeated PayItem onlinePays = 5;//线上实收汇总
  repeated PayItem offlinePays = 6;//线下实收汇总
  repeated PayItem change = 7;//线下找零汇总
  int32 count = 8;//小票总数
  repeated TicketStatistic1 tickets = 9;//小票统计信息
  double sumOverflow = 10;//溢出金额
  int32 unupload = 11;
  PayioInfo payInfo = 12;
  repeated PromotionItem promotions = 13;//促销汇总
  repeated PromotionItem pos_promotion = 14;//门店促销统计
  repeated PromotionChannel promotion_channel = 15;//促销渠道汇总
  repeated TaxItem taxs = 16;
  repeated UserSaleItem userItems = 17;
  commonStatistical discountDealsStatistics = 18; //折扣统计
  commonStatistical allTicketStatistics = 19; //销售统计
  commonStatistical removeZeroStatistics = 20; //抹零统计
  commonStatistical roundingStatistics = 21; //甩尾统计
  commonStatistical overflowStatistics = 22; //溢出统计
  commonStatistical cancelTicketStatistics = 23; //取消订单统计
  double  diff = 24; //营收差异
  double  netTrade = 25; //销售净额
  double taxTotalAmount = 26; //税总计
  double paysAmount = 27; //支付小记
  int32 pay_count = 28; //支付次数
  double pay_merchant_allowance = 29;
  double overflow_amount = 30; //溢出金额
  int32 overflow_count = 31; //溢出次数
  double overflow_merchant_allowance = 32; //溢出金额
  double offlinePaysAmount = 33; //线下实收汇总
  double onlinePaysAmount = 34; //线上实收汇总
  double changeAmount = 35; //找零汇总
  google.protobuf.Struct channels = 36;
  repeated google.protobuf.Any channels_new = 37;
  bool isToday = 38;
  bool is_summary = 39;
  double cash_refund_amount = 40;//非原单退款
  double cash_refund_count = 41;//非原单退款笔数
  double  frm_loss_amount = 42;//门店报损金额
  double frm_loss_count = 43;//门店报损数量
}

message DailyRequest{
  string busDate = 1;
  string start_time = 2;
  string end_time = 3;
  string host = 4;
}

message DailyResponse{
  string extend = 1;
}

message OrderTypeRequest{
  string start_time =2;
  string end_time = 3;
  string host = 4;
}

message OrderTypeResponse{
  string extend = 1;
}
message ProductSalesSummaryResponse {
  message ProductSalesSummary {
    // 商品code
    string product_code = 1;
    // 商品名称
    string product_name = 2;
    // 商品数量
    double product_count = 3;
    // 商品实收
    double net_amount = 4;
    // 商品收入占比
    double percentage_of_sales = 5;
    // 商品分类code
    string category_code = 6;
    // 商品分类名称
    string category_name = 7;
  }

  repeated ProductSalesSummary rows = 1;
  ProductSalesSummary summary = 2;
  int64 total = 3;
}


message ProductSalesDetailResponse {
  message ProductSalesDetail {
    // 营业日期
    string bus_date = 1;
    // 省
    string region_address = 41;
    // 省
    string region_address1 = 2;
    // 市
    string region_address2 = 3;
    // 区
    string region_address3 = 4;
    // 管理区域
    string branch_region = 5;
    // 门店经营类型
    string store_type_name = 6;
    // 门店编号
    string store_code = 7;
    // 门店名称
    string store_name = 8;
    // 类目编码
    string category_code = 10;
    // 类目名称
    string category_name = 11;
    // 商品编码
    string product_code = 12;
    // 商品名称
    string product_name = 13;
    // 下单渠道
    string channel_name = 14;
    // 用餐方式
    string dining_method = 15;
    // 商品规格
    string sku_remark = 16;
    // 商品售价
    double price = 17;
    // 商品数量
    double product_count = 18;
    // 碗数
    double bowl_count = 19;
    // 商品流水
    double gross_amount = 20;
    // 折扣金额
    double discount_amount = 21;
    // 商品实收
    double net_amount = 22;
    // 份量统计
    string weight = 23;
    // 餐段
    string meal_segment_name = 29;
    // 用餐区域名称
    string zone_name = 30;
    // 桌位号
    string table_no = 31;
    // 点餐人
    string order_user = 32;
    // 开台时间
    string open_time = 33;
    // 点菜时间
    string order_time = 34;
    // 收银员
    string cashier_name = 35;
    // 结账时间
    string end_time = 36;
    // 订单类型
    string order_type_name = 37;
    // 团购渠道
    string group_purchase_channel = 38;
    // 订单编号
    string ticket_no = 39;
    // 备注
    string remark = 40;
    // 辣度
    string spicy = 42;
    // 加料数量
    int32 acc_count = 43;
    // 配料数量
    int32 product_acc_count = 44;
    int32 row_idx = 45;

    string unit_info = 46;
    string production_department_name = 47;
    string income_subject_name = 48;
    string discount_rate = 49;
    string flags = 50;
    string product_label = 51;
    //商品名称辅助列
    string  product_secondary_column=52;
  }
  repeated ProductSalesDetail rows = 1;
  ProductSalesDetail summary = 2;
  int64 total = 3;
}

message StorePerformanceAnalysisResponse {
  message StorePerformanceAnalysis {
    string store_name = 1; // 门店名称
    int64 seat_count = 2; // 座位数目
    double business_amount = 3; // 营业额
    double bowl_count = 4; // 碗数量
    double seat_turnover_rate = 5; // 座位周转率
    double average_daily_sales = 6; // 平均日销售额
    double average_sales_per_order = 7; // 单均销售额
    double average_bowl_spending = 8; // Average bowl spending

    message MealSegment {
      string meal_segment_name = 1; // 餐段名称
      double bowl_turnover = 2; // 碗周转
    }
    message OrderType {
      string order_type_name = 1; // 渠道名称
      double bowl_turnover = 2;  // 碗周转
    }
    message OrderTypeMealSegment {
      string name = 1; // 名称
      double bowl_turnover = 2;  // 碗周转
    }

    double weekday_average_daily_sales = 9; // 平日-平均日销售额
    repeated OrderTypeMealSegment weekday_order_type_meal_segment_turnovers = 15; // 平日-餐段周转
    repeated MealSegment weekday_meal_segment_turnovers = 10; // 平日-餐段周转
    repeated OrderType weekday_channel_turnovers = 11; // 平日-渠道周转

    double weekend_average_daily_sales = 12; // 周末-平均日销售额
    repeated OrderTypeMealSegment weekend_order_type_meal_segment_turnovers = 16; // 周末-餐段周转
    repeated MealSegment weekend_meal_segment_turnovers = 13; // 周末-餐段周转
    repeated OrderType weekend_channel_turnovers = 14; // 周末-渠道周转
  }
  repeated StorePerformanceAnalysis rows = 1;
  StorePerformanceAnalysis summary = 2;
  int64 total = 3;
}

message MealSegmentsResponse {
  repeated string rows = 1;
}

message ProductSalesComboSummaryResponse {
  message ProductSalesComboSummary {
    // 营业日期
    string bus_date = 1;
    // 所在城市
    string region_address = 2;
    // 省
    string region_address1 = 3;
    // 市
    string region_address2 = 4;
    // 区
    string region_address3 = 5;
    // 门店编号
    string region_code = 6;
    // 门店名称
    string region_name = 7;
    // 门店经营类型
    string store_type_name = 8;
    // 团购渠道
    string group_purchase_channel = 9;
    // 商品名称
    string product_name = 10;
    // 商品编码
    string product_code = 11;
    // 商品数量
    double qty = 12;
    // 商品售价
    double price = 13;
    // 商品流水
    double gross_amount = 14;
    // 折扣金额
    double discount_amount = 15;
    // 商品实收
    double net_amount = 16;
    string branch_region = 17;
    int32 row_idx = 18;
    string category_name = 19;
  }
  repeated ProductSalesComboSummary rows = 1;
  ProductSalesComboSummary summary = 2;
  int64 total = 3;
}

message ProductSalesComboDetailResponse {
  message ProductSalesComboDetail {
    // 营业日期
    string bus_date = 1;
    // 所在城市
    string region_address = 2;
    // 省
    string region_address1 = 3;
    // 市
    string region_address2 = 4;
    // 区
    string region_address3 = 5;
    // 门店编号
    string region_code = 6;
    // 门店名称
    string region_name = 7;
    // 门店经营类型
    string store_type_name = 8;
    // 团购渠道
    string group_purchase_channel = 9;
    // 套餐名称
    string combo_product_name = 10;
    // 商品名称
    string product_name = 11;
    // 商品编码
    string product_code = 12;
    // 商品数量
    double qty = 13;
    // 商品售价
    double price = 14;
    // 商品流水
    double gross_amount = 15;
    // 折扣金额
    double discount_amount = 16;
    // 商品实收
    double net_amount = 17;
    // 点菜时间
    string add_time = 18;
    // 订单编号
    string ticket_no = 19;
    string branch_region = 20;
    string unit = 21;
    string sku_remark = 22;
    int32 row_idx = 23;
    string category_name = 24;
    string order_type_name = 25;
    // 套餐商品数量
    double combo_qty = 26;
  }
  repeated ProductSalesComboDetail rows = 1;
  ProductSalesComboDetail summary = 2;
  int64 total = 3;
}

message PosInboundResponse {
  message PosInboundRow {
    // 入库时间
    string inbound_time = 1;
    // 入库人
    string operator_name = 2;
    // 商品名称
    string product_names = 3;
    // 商品数量
    double product_qty = 4;
    // 内容
    google.protobuf.Struct content = 5;
  }

  repeated PosInboundRow rows = 1;
  int64 total = 2;
}

message InboundResponse {
  message InboundRow {
    // 门店名称
    string store_name = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 商品价格
    double price = 4;
    // 商品价格
    double amount = 5;
    // 商品规格
    string sku_remark = 6;
    // 商品数量
    double qty = 7;
    // 入库时间
    string inbound_time = 8;
    // 入库人
    string operator_name = 9;
  }

  repeated InboundRow rows = 1;
  int64 total = 2;
}

message PettyCashDailySettlementReportResponse{
  message PettyCashDailySettlementReportRow {
    // 营业日期
    string bus_date = 1;
    // 门店ID
    uint64 store_id = 2;
    // 门店code
    string store_code = 3;
    // 门店名称
    string store_name = 4;
    // 開機金額
    string  start_balance = 5;
    // 已收取現金
    string  collect_cash_amount = 6;
    // 支出Total
    string  payout_amount = 8;
    // 零用現金結餘
    string end_balance = 9;
    // 开店零用现金
    string  startup_amount = 10;
    // 支出明细
    google.protobuf.Struct   payout_detail = 11;
  }

    repeated PettyCashDailySettlementReportRow rows = 1;
    int64 total = 2;
    // 导出专用//
    string period_start_date = 3;
    string period_end_date = 4;
    string period_date_text = 5;
    // 支出明细汇总
    google.protobuf.Struct   payout_detail_summary=6 ;
    string payout_amount_summary =7;
}

message PaymentDetailReportResponse {
  message PaymentDetailReportRow {
    // 营业日期
    string bus_date = 1;
    // 门店ID
    uint64 store_id = 2;
    // 门店code
    string store_code = 3;
    // 门店名称
    string store_name = 4;
    // 订单号
    string ticket_no =5;
    // 支付方式ID
    int64 payment_id = 6;
    // 支付方式编码
    string payment_code = 7;
    // 支付方式名称
    string payment_name = 8;
    // 支付时间 HH24:MI:SS
    string pay_time = 9;
    // 实付金额
    string  pay_amount =10;
    // 支付转折扣
    string  payment_transfer_amount =11;
    // 手续费
    string  sur_charge_amount =12;
    // 卡号
    string  card_no =13;
    // 支付日期 YYYY/MM/DD
    string pay_date =14;
    // 实收金额
    string real_amount =15;
    string  plate_no = 16;                 // 飞盘号（外送号码）
    string phone_no=17;                    // 帐单号码
    string operator_code = 18;             // 员工号
    string service_fee =19;                // 服务费
    string  non_sales_amount  =20;         // 非销售金额
  }

  repeated PaymentDetailReportRow rows = 1;
  int64 total = 2;
  // 导出专用//
  string period_start_date = 3;
  string period_end_date = 4;
  string period_date_text = 5;
}


message ProductAttributeSalesResponse{
  message ProductAttributeSalesRow {
    //  属性编码
    string attribute_code = 1;
    //  属性名称
    string attribute_name = 2;
    //  属性组编码
    string attribute_group_code = 3;
    // 属性组名称
    string attribute_group_name = 4;
    // 门店ID
    uint64 store_id = 5;
    // 门店名称
    string store_name = 6;
    // 门店编码
    string store_code = 7;
    // 营业日期
    string bus_date = 8;
    // 销量
    double qty = 9;
    // 销量占比
    double qty_ratio = 10;
  }
  repeated  ProductAttributeSalesRow rows = 1;
  int64 total = 2;
}

message CategorySalesResponse {
  message CategorySales {
    // 商品类目名称
    string category_name = 1;
    // 商品数量
    double product_count = 2;
    // 商品类目实收
    double net_amount = 3;
    // 商品类目收入占比
    double percentage_of_sales = 4;
  }

  repeated CategorySales rows = 1;
  CategorySales summary = 2;
  int64 total = 3;
}

message StoreCompensationSalesReportReq {
  // 营业开始时间
  google.protobuf.Timestamp start = 1;
  // 营业结束时间
  google.protobuf.Timestamp end = 2;

  // 支付方式
  repeated int64 payment_ids = 3;
  // 促销方式（预留）
  repeated int64 promotion_ids = 4;
  int32 limit = 5;
  int32 offset = 6;
  string sort = 7;
  string order = 8;
  enum queryType {
    query_type_header = 0;
    query_type_detail = 1;
  }
  queryType query_type = 9;
  string region_search_type = 10;
  repeated int64 region_search_ids = 11;
}

message StoreCompensationSalesReportResp {
  repeated  StoreCompensationSalesReportRow summary_row = 1;// 订单级别
  int64 summary_total = 2;
  repeated  StoreCompensationSalesItemRow rows = 3;// item级别
  int64 total = 4;
}

message StoreCompensationSalesReportRow{
  // 营业日期
  string bus_date = 1;
  // 门店ID
  uint64 store_id = 2;
  // 门店code
  string store_code = 3;
  // 门店名称
  string store_name = 4;
  // 账单号（取餐号码）
  string ticket_no = 5;
  // 结账时间
  google.protobuf.Timestamp end_time = 6;
  // 类别编码(支付方式/促销方式ID)
  uint64  compensation_category_id = 7;
  // 类别编码(支付方式/促销方式code)
  string compensation_category_code = 8;
  // 类别描述(支付方式/促销方式名称)
  string compensation_category_desc = 9;
  // 账单金额
  string  net_amount = 10;
  // 非销售金额
  string  non_sales_amount = 11;
}

message StoreCompensationSalesItemRow{
  // 营业日期
  string bus_date = 1;
  // 门店ID
  uint64 store_id = 2;
  // 门店code
  string store_code = 3;
  // 门店名称
  string store_name = 4;
  // 账单号（取餐号码）
  string ticket_no = 5;
  // 结账时间
  google.protobuf.Timestamp  end_time = 6;
  // 类别编码(支付方式/促销方式ID)
  uint64  compensation_category_id = 7;
  // 类别编码(支付方式/促销方式code)
  string compensation_category_code = 8;
  // 类别描述(支付方式/促销方式名称)
  string compensation_category_desc = 9;

  // item主商品排序
  uint64 main_seq = 10;
  // item在一个主商品内的排序
  uint64 seq = 11;
  // 父级id,如果是0，代表是主商品
  uint64 parent_id = 12;
  // 商品id
  uint64 product_id = 13;
  // 商品名称
  string product_name = 14;
  // 商品编码
  string product_code = 15;
  // 商品数量
  string qty = 16;
  // 账单金额(订单级别）
  string  net_amount = 17;
  // 非销售金额(订单级别）
  string  non_sales_amount = 18;
  // item_amount（item级别）
  string  item_amount = 19;
}

message PosStorePeriodReportResponse {
  message Period {
    string time_period = 1; // 時段
    int32 bill_count = 2;  // 賬單數
    double bowl_count = 3;  // 碗数
    double amount = 4;      // 金额
    double percentage = 5;  // 金额占比
  }

  repeated Period period_list = 1;  // 时段列表
  Period summary = 2;               // 汇总
}

message PosDepartmentBusinessResponse {
  message Department {
    string name = 1; // 名称
    int32 qty = 2; // 数量
    double amount = 3; // 实收金额
    double percentage = 4; // 收入占比
  }

  Department summary = 1; // 总计
  repeated Department department_list = 2; // 明细列表
}

message PosProductSalesReportResponse {
  message ProductSales {
    string name = 1;          // 名称
    int32 qty = 2;            // 数量
    double amount = 3;        // 金额
    double percentage = 4;    // 金额占比
  }

  message Row{
    repeated ProductSales product_sales_list = 1;   // 商品销售列表
    ProductSales summary = 2;                       // 汇总
  }

  repeated Row rows = 1;
}
