syntax = "proto3";
option go_package = "./grpc/cam;cam";
option java_package = "cn.hexcloud.cam.common.service.facade.management";
option java_multiple_files = true;

package channel;

import "google/api/annotations.proto";

// 渠道管理服务
service ChannelManagement {

  // 渠道签约授权管理接口
  rpc channelAuthorization(ChannelAuthorizationRequest) returns (ChannelAuthorizationResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/auth/do"
      body: "*"
    };
  }

  // 渠道管理接口
  rpc channelManagement(ChannelManagementRequest) returns (ChannelManagementResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/do"
      body: "*"
    };
  }

  // 渠道脚本接口
  rpc channelScript(ChannelScriptRequest) returns (ChannelScriptResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/script/do"
      body: "*"
    };
  }

  // 渠道签约授权管理接口
  rpc channelAccess(ChannelAccessRequest) returns (ChannelAccessResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/access/do"
      body: "*"
    };
  }

  // 渠道实体映射关系管理接口
  rpc channelRelation(ChannelRelationRequest) returns (ChannelRelationResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/relation/do"
      body: "*"
    };
  }

  // 短信验证码接口
  rpc channelInfra(ChannelInfraRequest) returns (ChannelInfraResponse) {
    option (google.api.http) = {
      post: "/api/v1/cam/channel/infra/do"
      body: "*"
    };
  }
}


// 渠道脚本管理入参
message ChannelScriptRequest {
  string action = 1;
  ListScriptQuery list_script_query = 2;
  ChannelScriptSection channel_script_section = 3;
  DelScriptSection del_script_section = 4;
}

// 渠道脚本管理入参
message ChannelScriptResponse {
  string error_code = 1;
  string error_message = 2;
  repeated ChannelScriptItem channel_script_item = 3;
}

// 脚本列表
message ChannelScriptItem {
  int32 id = 1;
  string channel_code = 2;
  string script_key = 3;
  string script_text = 4;
  string version = 5;
  string create_time = 6;
}

// 保存/测试
message ChannelScriptSection {
  int32 id = 1;
  string channel_code = 2;
  string script_key = 3;
  string script_text = 4;
}

// 删除脚本
message DelScriptSection {
  int64 id = 1;
  string del_cache_only = 2;
}

message ListScriptQuery {
  string enabled = 1;
  string channel_code = 2;
  string script_key = 3;
  string version = 4;
}

// 渠道授权访问
message ChannelAccessRequest {
  // 业务操作
  string action = 1;
  // (可选)查询渠道列表
  ListAccessQuery list_access_query = 2;
  // 创建access
  repeated Access list_access = 3;
  // 编辑授权
  Access edit_access = 4;
  // 保存会员渠道授权
  AppletsAccessSection applets_access_section = 5;
}

// 门店id/公司id
message ApplyTarget {
  // 门店id
  string store_id = 1;
  // 公司id
  string company_id = 2;
}

// 小程序授权配置保存对象
message AppletsAccessSection {
  // 配置目标，租户id/公司id列表/门店id列表
  repeated ApplyTarget apply_target_item = 1;
  // 授权列表
  repeated AppletsAccessItem applets_access_item = 2;
}

// 小程序保存授权配置集合
message AppletsAccessItem {
  // 渠道名称
  string channel_code = 1;
  // 授权列表
  repeated Access props_item = 2;
}

// 渠道管理接口响应
message ChannelAccessResponse {
  //（必传）异常编码
  string error_code = 1;
  //（必传）异常信息
  string error_message = 2;
  // 授权列表
  AccessReportSection access_report_section = 3;
  // 查询/门店/公司/全局访问授权配置
  repeated Access access_item = 4;
  // 带分组结构的AccessList
  repeated AppletsAccessItem applets_access_item = 5;
}

message AccessReportSection {
  // （必传）当前页码
  int32 page_index = 1;
  // （必传）分页步长
  int32 page_size = 2;
  // （必传）总页数
  int32 page_count = 3;
  // （必传）总条数
  int64 total = 4;
  // （必传）渠道签约授权信息
  repeated AccessReportItem access_report_item = 5;
}

// 查询/门店/公司/全局访问授权配置
message AccessReportItem {
  // 门店id
  string store_id = 1;
  // 公司id
  string company_id = 2;
  // 配置等级
  int32  level = 5;
  // 渠道名称
  string channel_name_item = 6;
  // 更新时间
  string update_time = 7;
  // 租户id
  string partner_id = 8;
  // 门店/公司名称
  string name = 9;
  // 门店/公司编码
  string code = 10;
}

// 第三方访问授权配置
message Access {
  // 渠道代码
  string channel_code = 1;
  // 配置层级，0 租户；1 公司；2 门店
  int32 apply_to = 2;
  // 配置目标，租户id/公司id列表/门店id列表
  repeated ApplyTarget apply_targets = 3;
  // 渠道业务代码
  string business_code = 4;
  // 第三方商户PID
  string merchant_id = 5;
  // 第三方app id
  string app_id = 6;
  // 第三方app key
  string app_key = 7;
  // 第三方access key
  string access_key = 8;
  // 第三方cert
  string cert = 9;
  // 第三方private key
  string private_key = 10;
  // 第三方public key
  string public_key = 11;
  // 第三方网关URL
  string gateway_url = 12;
  // 支付名称集合
  string channelNameItems = 13;
  // 渠道访问授权配置id
  int64 access_id = 14;
  // 租户id
  string partner_id = 15;
  // 公司id
  string company_id = 16;
  // 门店id
  string store_id = 17;
  // 终端id
  string terminal_id = 18;
  // 第三方api版本
  string api_version = 19;
  // 子商户号
  string sub_merchant_id = 20;
}

message ListAccessQuery {
  // （必传）查询类型
  string channel_code = 1;
  // 租户id
  string partner_id = 2;
  // 公司id
  string company_id = 3;
  // 门店id
  string store_id = 4;
  // 业务id
  string business_code = 5;
  // 配置层级
  int32 level = 6;
  // 分页
  int32 page_index = 7;
  int32 page_size = 8;
  // 搜索字段
  string search = 9;
  // 搜索表字段,分隔
  string search_fields = 10;
}

// 渠道管理请求
message ChannelManagementRequest {
  string action = 1;
  ListChannelQuery list_channel_query = 2;
  Channel channel = 3;
}

// 渠道管理接口响应
message ChannelManagementResponse {
  string error_code = 1;
  string error_message = 2;
  repeated Channel channel_list = 3;
}

// 渠道列表查询
message ListChannelQuery {
  // （必传）查询类型
  string channel_category = 1;
  // 搜索名称
  string search_name = 2;
  // 渠道code
  string channel_code = 3;
  // 子分类
  string channel_sub_category = 4;
}

message Channel {
  // 渠道编码
  string channel_code = 1;
  // 渠道名称
  string channel_name = 2;
  // 渠道logo
  string channel_logo = 3;
  // 渠道性质，KA 品牌商；ISV 服务商
  string channel_type = 4;
  // 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
  string channel_category = 5;
  // 渠道描述
  string description = 6;
  // 渠道附加信息，JSON格式
  string channel_properties = 7;
  // 渠道主键
  int64 channel_id = 8;
  // 渠道标签
  string channel_labels = 9;
  // 渠道二级分类，AM 小程序&会员；COUPON 卡券
  string channel_sub_category = 10;
}

// 渠道签约授权请求信息
message ChannelAuthorizationRequest {
  /**
   *（必传）业务操作
   * listChannelBinding 查询渠道绑定关系
   * switchChannelBinding 绑定/解绑渠道
   * listSignupAuthorization 查询签约授权信息
   * signup 渠道签约
   * getSignupDetail 查询渠道签约详情
   * refreshSignupState 刷新渠道签约状态
   * getStoreGrantUrl 获取门店授权链接
   * getStoreCancelGrantUrl 获取门店取消授权链接
   * listStoreAuthorization 查询门店授权信息
   */
  string action = 1;
  // （可选）渠道绑定关系查询请求对象
  ListBindingSection list_binding_section = 2;
  // （可选）绑定/解绑渠道请求对象
  SwitchBindingSection switch_binding_section = 3;
  // （可选）渠道签约授权信息查询请求对象
  ListAuthorizationSection list_authorization_section = 4;
  // （可选）渠道签约请求对象
  SignupSection signup_section = 5;
  // （可选）渠道签约详情查询请求对象
  QuerySignupSection query_signup_section = 6;
  // （可选）渠道签约状态刷新请求对象
  RefreshSignupSection refresh_signup_section = 7;
  // （可选）渠道签约授权信息查询请求对象
  ListStoreAuthorizationSection list_store_authorization_section = 8;
  // （可选）渠道门店授权请求
  GetStoreGrantUrlSection get_store_grant_url_section = 9;
  // （可选）渠道门店取消授权请求
  GetStoreCancelGrantUrlSection get_store_cancel_grant_url_section = 10;
  // （可选）查询授权账户列表
  ListGrantAccountSection list_grant_account_section = 11;
  // （可选）渠道授权请求
  GetGrantUrlSection get_grant_url_section = 12;
  // （可选）渠道取消授权请求
  GetCancelGrantUrlSection get_cancel_grant_url_section = 13;
}

// 渠道签约授权响应信息
message ChannelAuthorizationResponse {
  // （必传）异常编码
  string error_code = 1;
  // （必传）异常信息
  string error_message = 2;
  // （可选）渠道绑定关系信息
  BindingListSection binding_list_section = 3;
  // （可选）渠道签约授权信息
  AuthorizationListSection authorization_list_section = 4;
  // （可选）渠道签约结果
  SignupResultSection signup_result_section = 5;
  // （可选）渠道签约详情
  SignupDetailSection signup_detail_section = 6;
  // （可选）渠道签约状态刷新结果
  RefreshSignupResultSection refresh_signup_result_section = 7;
  // （可选）授权URL信息
  GrantUrlSection grant_url_section = 8;
  // （可选）渠道签约授权信息查询请求对象
  StoreAuthorizationListSection store_authorization_list_section = 9;
  // （可选）授权账户列表
  GrantAccountListSection grant_account_list_section = 10;
}

// 渠道基础服务请求信息
message ChannelInfraRequest {
  // （必传）业务操作
  string action = 1;
  // （可选）短信验证码请求信息
  SendSMSCodeSection send_sms_code_section = 2;
}

// 渠道基础服务响应信息
message ChannelInfraResponse {
  // （必传）异常编码
  string error_code = 1;
  // （必传）异常信息
  string error_message = 2;
  // （可选）短信验证码响应信息
  SMSResultSection sms_result_section = 3;
}

// 渠道绑定关系查询对象
message ListBindingSection {
  // （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
  string channel_category = 1;
  // （可选）渠道是否启用
  string enabled = 2;
  // （可选）渠道标签，多个以","分割
  string channel_labels = 3;
  //（可选）渠道业务代码，多个以","分割
  string business_code = 4;
}

// 渠道绑定关系查询结果
message BindingListSection {
  // （必传）渠道绑定关系信息
  repeated BindingItem binding_item = 1;
}

// 渠道绑定关系信息
message BindingItem {
  //（必传）渠道代码
  string channel_code = 1;
  //（必传）渠道名称
  string channel_name = 2;
  //（必传）渠道业务代码，","分割
  string business_code = 3;
  //（必传）渠道性质，KA 品牌商；ISV 服务商
  string channel_type = 4;
  //（必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
  string channel_category = 5;
  //（可选）渠道二级分类，AM 小程序&会员；COUPON 卡券
  string channel_sub_category = 6;
  //（可选）渠道标签，逗号分割
  string channel_labels = 7;
  //（必传）是否启用
  string enabled = 8;
}

// 绑定/解绑渠道请求
message SwitchBindingSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （可选）是否启用，与business_code不能同时为空
  string enabled = 2;
  // （可选）启用的渠道业务代码，","分割，与enabled不能同时为空
  string business_code = 3;
}

// 渠道签约授权信息查询对象
message ListAuthorizationSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （可选）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
  string signup_state = 2;
  // （可选）授权状态，WAITING 待授权；COMPLETE 已授权
  string grant_state = 3;
  // （可选）复合查询条件，商户第三方商户账号、签约流水号
  string complex_criterion = 4;
  // （可选）当前页码
  int32 page_index = 5;
  // （可选）分页步长
  int32 page_size = 6;
}

// 渠道签约授权信息查询结果
message AuthorizationListSection {
  // （必传）当前页码
  int32 page_index = 1;
  // （必传）分页步长
  int32 page_size = 2;
  // （必传）总页数
  int32 page_count = 3;
  // （必传）总条数
  int64 total = 4;
  // （必传）渠道签约授权信息
  repeated AuthorizationItem authorization_item = 5;
}

// 渠道签约授权信息对象
message AuthorizationItem {
  // （必传）签约授权ID
  int32 auth_id = 1;
  // （必传）渠道代码
  string channel_code = 2;
  // （必传）第三方商户PID
  string merchant_id = 3;
  // （必传）签约单号
  string signup_no = 4;
  // （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
  string signup_state = 5;
  // （必传）授权状态，WAITING 待授权；COMPLETE 已授权
  string grant_state = 6;
}

// 门店授权请求
message GetStoreGrantUrlSection{
  // （必传）渠道编码
  string channel_code = 1;
  // （必传）门店ID
  string store_id = 2;
  // （可选）授权标记
  string grant_target_id = 3;
  // （可选）授权的渠道业务
  string grant_business = 4;
}

// 门店取消授权请求
message GetStoreCancelGrantUrlSection{
  // （必传）渠道编码
  string channel_code = 1;
  // （必传）门店ID
  string store_id = 2;
  // (必传) appAuthToken
  string token = 3;
  // （可选）取消授权的渠道业务
  string grant_business = 4;
}

// 授权请求
message GetGrantUrlSection{
  // （必传）渠道编码
  string channel_code = 1;
  // （可选）授权标记
  string grant_target_id = 2;
  // （可选）授权的渠道业务
  string grant_business = 3;
}

// 取消授权请求
message GetCancelGrantUrlSection{
  // （必传）渠道编码
  string channel_code = 1;
  // (必传) appAuthToken
  string token = 2;
  // （可选）取消授权的渠道业务
  string grant_business = 3;
}

// 授权URL信息
message GrantUrlSection{
  // 授权url
  string grant_url = 1;
  // 取消授权url
  string cancel_grant_url = 2;
}

// 授权账户列表
message ListGrantAccountSection{
  string channel_code = 1;
  string grant_target_id = 2;
  string state = 3;
}

// 授权账户列表
message GrantAccountListSection{
  repeated GrantAccountSection grant_account_section = 1;
}

message GrantAccountSection {
  string channel_code = 1;
  string grant_target_id = 2;
  string state = 3;
  string auth_id = 4;
}

// 渠道签约请求对象
message SignupSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）第三方商户PID
  string merchant_id = 2;
  // （必传）经营类目
  string business_category = 3;
  // （必传）服务费率
  double service_rate = 4;
  // （必传）签约主体
  string party_name = 5;
  // （可选）签约主体所在地
  Location party_location = 6;
  // （可选）联系人
  Contact contact = 7;
  // （可选）签约附件
  repeated Attachment attachments = 8;
  // （必传）短信id
  string sms_id = 9;
  // （必传）短信验证码
  string sms_code = 10;
}

// 渠道签约结果
message SignupResultSection {
  // （必传）签约流水号
  string signup_no = 1;
  // （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
  string signup_state = 2;
  // （可选）签约结果
  string signup_result = 3;
}

// 渠道签约详情查询对象
message QuerySignupSection {
  // （必传）渠道签约授权id
  int32 channel_auth_id = 1;
}

// 渠道签约详情查询结果
message SignupDetailSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）第三方商户PID
  string merchant_id = 2;
  // （必传）经营类目
  string business_category = 3;
  // （必传）签约主体
  string party_name = 4;
  // （可选）签约主体所在地
  Location party_location = 5;
  // （可选）联系人
  Contact contact = 6;
  // （可选）签约附件
  repeated Attachment attachments = 7;
}

// 渠道签约状态刷新请求对象
message RefreshSignupSection {
  // （必传）渠道签约授权id
  repeated int32 channel_auth_id = 1;
}

// 渠道签约状态刷新结果
message RefreshSignupResultSection {
  // （必传）渠道签约状态信息
  repeated SignupStateItem signup_state_item = 1;
}

// 渠道签约状态信息
message SignupStateItem {
  // （必传）渠道签约授权id
  int32 channel_auth_id = 1;
  // （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
  string signup_state = 2;
  // （可选）签约结果
  string signup_result = 3;
}

// 所在地信息
message Location {
  // （可选）国家代码
  string country_code = 1;
  // （可选）省份代码
  string province_code = 2;
  // （可选）城市代码
  string city_code = 3;
  // （可选）行政区代码
  string district_code = 4;
  // （可选）详细地址
  string address = 5;
}

// 联系人信息
message Contact {
  // （可选）联系人姓名
  string name = 1;
  // （可选）联系人Email
  string email = 2;
  // （可选）联系人手机号
  string cellphone_number = 3;
}

// 附件信息
message Attachment {
  /**
   * （必传）附件名称
   * 支付宝当面付待签约附件信息
   * 营业执照：BusinessLicensePic，店铺门头照片：ShopSignBoardPic，店铺内景照片：ShopScenePic
   */
  string name = 1;
  // （可选）附件文件名（需携带文件后缀名）
  string fileName = 2;
  // （必传）附件URL
  string url = 3;
  // （可选）附件内容
  bytes content = 4;
}

// 查询授权列表
message ListStoreAuthorizationSection{
  // （必传）门店ID列表
  repeated string store_id = 1;
  // （必传）渠道号
  string channel_code = 2;
  // （可选）取消授权的渠道业务
  string grant_business = 3;
}

message StoreAuthorizationListSection {
  repeated StoreAuthorizationItem authorization_item = 1;
}

message StoreAuthorizationItem {
  // （必传）门店ID
  string store_id = 1;
  // （必传）渠道代码
  string channel_code = 2;
  // （可选）授权状态，WAITING 待授权；COMPLETE 已授权
  string grant_state = 3;
  // （可选）第三方门店编号
  string third_store_id = 4;
  // （可选）第三方门店名称
  string third_store_name = 5;
  // （必传）授权token
  string auth_token = 6;
}

// 渠道实体映射关系请求对象
message ChannelRelationRequest {
  /**
   *（必传）业务操作
   * listRelation 查询渠道实体映射列表
   * produceRelationReport 查询渠道实体映射报表
   * createRelation 创建渠道实体映射关系（自动）
   * createCustomizedRelation 创建渠道实体映射关系（手动）
   * removeRelation 删除渠道实体映射关系
   */
  string action = 1;
  // （可选）渠道实体映射关系列表查询请求对象
  ListRelationSection list_relation_section = 2;
  // （可选）渠道实体映射关系报表查询请求对象
  ProduceRelationReportSection produce_relation_report_section = 3;
  // （可选）渠道实体映射关系（自动）请求对象
  CreateRelationSection create_relation_section = 4;
  // （可选）渠道实体映射关系（手动）请求对象
  CreateCustomizedRelationSection create_customized_relation_section = 5;
  // （可选）删除渠道实体映射关系请求对象
  RemoveRelationSection remove_relation_section = 6;
}

// 渠道实体映射关系应答对象
message ChannelRelationResponse {
  // 异常编码
  string error_code = 1;
  // 异常信息
  string error_message = 2;
  // 是否有警告信息
  bool warning = 3;
  //  警告信息
  string warning_message = 4;
  // （可选）渠道实体映射关系列表信息
  RelationListSection relation_list_section = 5;
  // （可选）渠道实体映射关系报表信息
  RelationReportSection relation_report_section = 6;
  // （可选）渠道实体映射关系创建结果
  CreateRelationResultSection create_relation_result_section = 7;
}

// 渠道实体映射关系查询请求对象
message ListRelationSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  // （可选）合阔实体id
  string hex_entity_id = 3;
  // （可选）第三方实体id
  string tp_entity_id = 4;
  // （可选）授权id
  string auth_id = 5;
}

// 渠道实体映射关系报表请求对象
message ProduceRelationReportSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  // （可选）查询条件（门店代码或门店名称，支持模糊查询）
  string search = 3;
  // （可选）查询条件对应的字段名
  string search_fields = 4;
  // （必传）当前页码
  int32 page_index = 5;
  // （必传）分页步长
  int32 page_size = 6;
}

// 创建渠道实体映射关系（自动）请求对象
message CreateRelationSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
}

// 创建渠道实体映射关系（手动）请求对象
message CreateCustomizedRelationSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  //（必传）渠道实体关系列表
  repeated CreateRelationItem create_relation_item = 3;
}

// 渠道实体映射关系列表查询应答对象
message RelationListSection {
  // （必传）渠道实体映射关系列表
  repeated RelationItem relation_item = 1;
}

// 渠道实体映射关系报表查询应答对象
message RelationReportSection {
  // （必传）当前页码
  int32 page_index = 1;
  // （必传）分页步长
  int32 page_size = 2;
  // （必传）总页数
  int32 page_count = 3;
  // （必传）总条数
  int64 total = 4;
  // （必传）渠道实体映射关系报表
  repeated RelationReportItem relation_report_item = 5;
}

// 渠道实体映射关系创建结果
message CreateRelationResultSection {
  // （必传）渠道实体映射关系列表
  repeated RelationItem relation_item = 1;
}

// 渠道实体映射关系信息
message RelationItem {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  // （必传）合阔实体id
  string hex_entity_id = 3;
  // （必传）第三方实体id
  string tp_entity_id = 4;
  // （必传）关系创建时间
  string create_time = 5;
}

// 渠道实体映射关系报表信息
message RelationReportItem {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  // （必传）合阔实体id
  string hex_entity_id = 3;
  // （可选）合阔实体名称
  string hex_entity_name = 4;
  // （必传）第三方实体id
  string tp_entity_id = 5;
  // （可选）第三方实体名称
  string tp_entity_name = 6;
  // （必传）关系创建时间
  string create_time = 7;
}

// 渠道实体映射关系信息
message CreateRelationItem {
  // （必传）合阔实体id
  string hex_entity_id = 1;
  // （必传）第三方实体id
  string tp_entity_id = 2;
}

// 删除渠道实体映射关系请求对象
message RemoveRelationSection {
  // （必传）渠道代码
  string channel_code = 1;
  // （必传）映射类型，STORE 门店映射
  string relation_type = 2;
  // （必传）合阔实体id
  string hex_entity_id = 3;
  // （可选）授权ID
  string auth_id = 4;
}

// 短信验证码请求信息
message SendSMSCodeSection {
  /**
   *（必传）短信验证码场景
   * 支付宝当面付ISV代签短信验证码：alipayISV_signup
   */
  string sms_scene = 1;
  // （必传）手机号
  string mobile_number = 2;
}

// 短信验证码响应信息
message SMSResultSection {
  // （必传）短信id
  string sms_id = 3;
}
