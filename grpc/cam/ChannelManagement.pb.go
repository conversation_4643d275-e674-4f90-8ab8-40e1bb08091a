// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.25.3
// source: grpc/cam/ChannelManagement.proto

package cam

import (
	context "context"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 渠道脚本管理入参
type ChannelScriptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action               string                `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	ListScriptQuery      *ListScriptQuery      `protobuf:"bytes,2,opt,name=list_script_query,json=listScriptQuery,proto3" json:"list_script_query,omitempty"`
	ChannelScriptSection *ChannelScriptSection `protobuf:"bytes,3,opt,name=channel_script_section,json=channelScriptSection,proto3" json:"channel_script_section,omitempty"`
	DelScriptSection     *DelScriptSection     `protobuf:"bytes,4,opt,name=del_script_section,json=delScriptSection,proto3" json:"del_script_section,omitempty"`
}

func (x *ChannelScriptRequest) Reset() {
	*x = ChannelScriptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelScriptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelScriptRequest) ProtoMessage() {}

func (x *ChannelScriptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelScriptRequest.ProtoReflect.Descriptor instead.
func (*ChannelScriptRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{0}
}

func (x *ChannelScriptRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelScriptRequest) GetListScriptQuery() *ListScriptQuery {
	if x != nil {
		return x.ListScriptQuery
	}
	return nil
}

func (x *ChannelScriptRequest) GetChannelScriptSection() *ChannelScriptSection {
	if x != nil {
		return x.ChannelScriptSection
	}
	return nil
}

func (x *ChannelScriptRequest) GetDelScriptSection() *DelScriptSection {
	if x != nil {
		return x.DelScriptSection
	}
	return nil
}

// 渠道脚本管理入参
type ChannelScriptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode         string               `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage      string               `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ChannelScriptItem []*ChannelScriptItem `protobuf:"bytes,3,rep,name=channel_script_item,json=channelScriptItem,proto3" json:"channel_script_item,omitempty"`
}

func (x *ChannelScriptResponse) Reset() {
	*x = ChannelScriptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelScriptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelScriptResponse) ProtoMessage() {}

func (x *ChannelScriptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelScriptResponse.ProtoReflect.Descriptor instead.
func (*ChannelScriptResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{1}
}

func (x *ChannelScriptResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelScriptResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelScriptResponse) GetChannelScriptItem() []*ChannelScriptItem {
	if x != nil {
		return x.ChannelScriptItem
	}
	return nil
}

// 脚本列表
type ChannelScriptItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	ScriptKey   string `protobuf:"bytes,3,opt,name=script_key,json=scriptKey,proto3" json:"script_key,omitempty"`
	ScriptText  string `protobuf:"bytes,4,opt,name=script_text,json=scriptText,proto3" json:"script_text,omitempty"`
	Version     string `protobuf:"bytes,5,opt,name=version,proto3" json:"version,omitempty"`
	CreateTime  string `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *ChannelScriptItem) Reset() {
	*x = ChannelScriptItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelScriptItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelScriptItem) ProtoMessage() {}

func (x *ChannelScriptItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelScriptItem.ProtoReflect.Descriptor instead.
func (*ChannelScriptItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{2}
}

func (x *ChannelScriptItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChannelScriptItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ChannelScriptItem) GetScriptKey() string {
	if x != nil {
		return x.ScriptKey
	}
	return ""
}

func (x *ChannelScriptItem) GetScriptText() string {
	if x != nil {
		return x.ScriptText
	}
	return ""
}

func (x *ChannelScriptItem) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ChannelScriptItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 保存/测试
type ChannelScriptSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	ScriptKey   string `protobuf:"bytes,3,opt,name=script_key,json=scriptKey,proto3" json:"script_key,omitempty"`
	ScriptText  string `protobuf:"bytes,4,opt,name=script_text,json=scriptText,proto3" json:"script_text,omitempty"`
}

func (x *ChannelScriptSection) Reset() {
	*x = ChannelScriptSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelScriptSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelScriptSection) ProtoMessage() {}

func (x *ChannelScriptSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelScriptSection.ProtoReflect.Descriptor instead.
func (*ChannelScriptSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{3}
}

func (x *ChannelScriptSection) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChannelScriptSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ChannelScriptSection) GetScriptKey() string {
	if x != nil {
		return x.ScriptKey
	}
	return ""
}

func (x *ChannelScriptSection) GetScriptText() string {
	if x != nil {
		return x.ScriptText
	}
	return ""
}

// 删除脚本
type DelScriptSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DelCacheOnly string `protobuf:"bytes,2,opt,name=del_cache_only,json=delCacheOnly,proto3" json:"del_cache_only,omitempty"`
}

func (x *DelScriptSection) Reset() {
	*x = DelScriptSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelScriptSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelScriptSection) ProtoMessage() {}

func (x *DelScriptSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelScriptSection.ProtoReflect.Descriptor instead.
func (*DelScriptSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{4}
}

func (x *DelScriptSection) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DelScriptSection) GetDelCacheOnly() string {
	if x != nil {
		return x.DelCacheOnly
	}
	return ""
}

type ListScriptQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled     string `protobuf:"bytes,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	ScriptKey   string `protobuf:"bytes,3,opt,name=script_key,json=scriptKey,proto3" json:"script_key,omitempty"`
	Version     string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *ListScriptQuery) Reset() {
	*x = ListScriptQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListScriptQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListScriptQuery) ProtoMessage() {}

func (x *ListScriptQuery) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListScriptQuery.ProtoReflect.Descriptor instead.
func (*ListScriptQuery) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{5}
}

func (x *ListScriptQuery) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

func (x *ListScriptQuery) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListScriptQuery) GetScriptKey() string {
	if x != nil {
		return x.ScriptKey
	}
	return ""
}

func (x *ListScriptQuery) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// 渠道授权访问
type ChannelAccessRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 业务操作
	Action string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	// (可选)查询渠道列表
	ListAccessQuery *ListAccessQuery `protobuf:"bytes,2,opt,name=list_access_query,json=listAccessQuery,proto3" json:"list_access_query,omitempty"`
	// 创建access
	ListAccess []*Access `protobuf:"bytes,3,rep,name=list_access,json=listAccess,proto3" json:"list_access,omitempty"`
	// 编辑授权
	EditAccess *Access `protobuf:"bytes,4,opt,name=edit_access,json=editAccess,proto3" json:"edit_access,omitempty"`
	// 保存会员渠道授权
	AppletsAccessSection *AppletsAccessSection `protobuf:"bytes,5,opt,name=applets_access_section,json=appletsAccessSection,proto3" json:"applets_access_section,omitempty"`
}

func (x *ChannelAccessRequest) Reset() {
	*x = ChannelAccessRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccessRequest) ProtoMessage() {}

func (x *ChannelAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccessRequest.ProtoReflect.Descriptor instead.
func (*ChannelAccessRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{6}
}

func (x *ChannelAccessRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelAccessRequest) GetListAccessQuery() *ListAccessQuery {
	if x != nil {
		return x.ListAccessQuery
	}
	return nil
}

func (x *ChannelAccessRequest) GetListAccess() []*Access {
	if x != nil {
		return x.ListAccess
	}
	return nil
}

func (x *ChannelAccessRequest) GetEditAccess() *Access {
	if x != nil {
		return x.EditAccess
	}
	return nil
}

func (x *ChannelAccessRequest) GetAppletsAccessSection() *AppletsAccessSection {
	if x != nil {
		return x.AppletsAccessSection
	}
	return nil
}

// 门店id/公司id
type ApplyTarget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 门店id
	StoreId string `protobuf:"bytes,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// 公司id
	CompanyId string `protobuf:"bytes,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
}

func (x *ApplyTarget) Reset() {
	*x = ApplyTarget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyTarget) ProtoMessage() {}

func (x *ApplyTarget) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyTarget.ProtoReflect.Descriptor instead.
func (*ApplyTarget) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{7}
}

func (x *ApplyTarget) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *ApplyTarget) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

// 小程序授权配置保存对象
type AppletsAccessSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 配置目标，租户id/公司id列表/门店id列表
	ApplyTargetItem []*ApplyTarget `protobuf:"bytes,1,rep,name=apply_target_item,json=applyTargetItem,proto3" json:"apply_target_item,omitempty"`
	// 授权列表
	AppletsAccessItem []*AppletsAccessItem `protobuf:"bytes,2,rep,name=applets_access_item,json=appletsAccessItem,proto3" json:"applets_access_item,omitempty"`
}

func (x *AppletsAccessSection) Reset() {
	*x = AppletsAccessSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppletsAccessSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppletsAccessSection) ProtoMessage() {}

func (x *AppletsAccessSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppletsAccessSection.ProtoReflect.Descriptor instead.
func (*AppletsAccessSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{8}
}

func (x *AppletsAccessSection) GetApplyTargetItem() []*ApplyTarget {
	if x != nil {
		return x.ApplyTargetItem
	}
	return nil
}

func (x *AppletsAccessSection) GetAppletsAccessItem() []*AppletsAccessItem {
	if x != nil {
		return x.AppletsAccessItem
	}
	return nil
}

// 小程序保存授权配置集合
type AppletsAccessItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道名称
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// 授权列表
	PropsItem []*Access `protobuf:"bytes,2,rep,name=props_item,json=propsItem,proto3" json:"props_item,omitempty"`
}

func (x *AppletsAccessItem) Reset() {
	*x = AppletsAccessItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppletsAccessItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppletsAccessItem) ProtoMessage() {}

func (x *AppletsAccessItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppletsAccessItem.ProtoReflect.Descriptor instead.
func (*AppletsAccessItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{9}
}

func (x *AppletsAccessItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *AppletsAccessItem) GetPropsItem() []*Access {
	if x != nil {
		return x.PropsItem
	}
	return nil
}

// 渠道管理接口响应
type ChannelAccessResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）异常编码
	ErrorCode string `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// （必传）异常信息
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 授权列表
	AccessReportSection *AccessReportSection `protobuf:"bytes,3,opt,name=access_report_section,json=accessReportSection,proto3" json:"access_report_section,omitempty"`
	// 查询/门店/公司/全局访问授权配置
	AccessItem []*Access `protobuf:"bytes,4,rep,name=access_item,json=accessItem,proto3" json:"access_item,omitempty"`
	// 带分组结构的AccessList
	AppletsAccessItem []*AppletsAccessItem `protobuf:"bytes,5,rep,name=applets_access_item,json=appletsAccessItem,proto3" json:"applets_access_item,omitempty"`
}

func (x *ChannelAccessResponse) Reset() {
	*x = ChannelAccessResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAccessResponse) ProtoMessage() {}

func (x *ChannelAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAccessResponse.ProtoReflect.Descriptor instead.
func (*ChannelAccessResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{10}
}

func (x *ChannelAccessResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelAccessResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelAccessResponse) GetAccessReportSection() *AccessReportSection {
	if x != nil {
		return x.AccessReportSection
	}
	return nil
}

func (x *ChannelAccessResponse) GetAccessItem() []*Access {
	if x != nil {
		return x.AccessItem
	}
	return nil
}

func (x *ChannelAccessResponse) GetAppletsAccessItem() []*AppletsAccessItem {
	if x != nil {
		return x.AppletsAccessItem
	}
	return nil
}

type AccessReportSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）当前页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// （必传）分页步长
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// （必传）总页数
	PageCount int32 `protobuf:"varint,3,opt,name=page_count,json=pageCount,proto3" json:"page_count,omitempty"`
	// （必传）总条数
	Total int64 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	// （必传）渠道签约授权信息
	AccessReportItem []*AccessReportItem `protobuf:"bytes,5,rep,name=access_report_item,json=accessReportItem,proto3" json:"access_report_item,omitempty"`
}

func (x *AccessReportSection) Reset() {
	*x = AccessReportSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessReportSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessReportSection) ProtoMessage() {}

func (x *AccessReportSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessReportSection.ProtoReflect.Descriptor instead.
func (*AccessReportSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{11}
}

func (x *AccessReportSection) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *AccessReportSection) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AccessReportSection) GetPageCount() int32 {
	if x != nil {
		return x.PageCount
	}
	return 0
}

func (x *AccessReportSection) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AccessReportSection) GetAccessReportItem() []*AccessReportItem {
	if x != nil {
		return x.AccessReportItem
	}
	return nil
}

// 查询/门店/公司/全局访问授权配置
type AccessReportItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 门店id
	StoreId string `protobuf:"bytes,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// 公司id
	CompanyId string `protobuf:"bytes,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 配置等级
	Level int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	// 渠道名称
	ChannelNameItem string `protobuf:"bytes,6,opt,name=channel_name_item,json=channelNameItem,proto3" json:"channel_name_item,omitempty"`
	// 更新时间
	UpdateTime string `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 租户id
	PartnerId string `protobuf:"bytes,8,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 门店/公司名称
	Name string `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	// 门店/公司编码
	Code string `protobuf:"bytes,10,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *AccessReportItem) Reset() {
	*x = AccessReportItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccessReportItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessReportItem) ProtoMessage() {}

func (x *AccessReportItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessReportItem.ProtoReflect.Descriptor instead.
func (*AccessReportItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{12}
}

func (x *AccessReportItem) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *AccessReportItem) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

func (x *AccessReportItem) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AccessReportItem) GetChannelNameItem() string {
	if x != nil {
		return x.ChannelNameItem
	}
	return ""
}

func (x *AccessReportItem) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *AccessReportItem) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *AccessReportItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccessReportItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// 第三方访问授权配置
type Access struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// 配置层级，0 租户；1 公司；2 门店
	ApplyTo int32 `protobuf:"varint,2,opt,name=apply_to,json=applyTo,proto3" json:"apply_to,omitempty"`
	// 配置目标，租户id/公司id列表/门店id列表
	ApplyTargets []*ApplyTarget `protobuf:"bytes,3,rep,name=apply_targets,json=applyTargets,proto3" json:"apply_targets,omitempty"`
	// 渠道业务代码
	BusinessCode string `protobuf:"bytes,4,opt,name=business_code,json=businessCode,proto3" json:"business_code,omitempty"`
	// 第三方商户PID
	MerchantId string `protobuf:"bytes,5,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// 第三方app id
	AppId string `protobuf:"bytes,6,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// 第三方app key
	AppKey string `protobuf:"bytes,7,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	// 第三方access key
	AccessKey string `protobuf:"bytes,8,opt,name=access_key,json=accessKey,proto3" json:"access_key,omitempty"`
	// 第三方cert
	Cert string `protobuf:"bytes,9,opt,name=cert,proto3" json:"cert,omitempty"`
	// 第三方private key
	PrivateKey string `protobuf:"bytes,10,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	// 第三方public key
	PublicKey string `protobuf:"bytes,11,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	// 第三方网关URL
	GatewayUrl string `protobuf:"bytes,12,opt,name=gateway_url,json=gatewayUrl,proto3" json:"gateway_url,omitempty"`
	// 支付名称集合
	ChannelNameItems string `protobuf:"bytes,13,opt,name=channelNameItems,proto3" json:"channelNameItems,omitempty"`
	// 渠道访问授权配置id
	AccessId int64 `protobuf:"varint,14,opt,name=access_id,json=accessId,proto3" json:"access_id,omitempty"`
	// 租户id
	PartnerId string `protobuf:"bytes,15,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 公司id
	CompanyId string `protobuf:"bytes,16,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店id
	StoreId string `protobuf:"bytes,17,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// 终端id
	TerminalId string `protobuf:"bytes,18,opt,name=terminal_id,json=terminalId,proto3" json:"terminal_id,omitempty"`
	// 第三方api版本
	ApiVersion string `protobuf:"bytes,19,opt,name=api_version,json=apiVersion,proto3" json:"api_version,omitempty"`
	// 子商户号
	SubMerchantId string `protobuf:"bytes,20,opt,name=sub_merchant_id,json=subMerchantId,proto3" json:"sub_merchant_id,omitempty"`
}

func (x *Access) Reset() {
	*x = Access{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Access) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Access) ProtoMessage() {}

func (x *Access) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Access.ProtoReflect.Descriptor instead.
func (*Access) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{13}
}

func (x *Access) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *Access) GetApplyTo() int32 {
	if x != nil {
		return x.ApplyTo
	}
	return 0
}

func (x *Access) GetApplyTargets() []*ApplyTarget {
	if x != nil {
		return x.ApplyTargets
	}
	return nil
}

func (x *Access) GetBusinessCode() string {
	if x != nil {
		return x.BusinessCode
	}
	return ""
}

func (x *Access) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *Access) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Access) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *Access) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *Access) GetCert() string {
	if x != nil {
		return x.Cert
	}
	return ""
}

func (x *Access) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *Access) GetPublicKey() string {
	if x != nil {
		return x.PublicKey
	}
	return ""
}

func (x *Access) GetGatewayUrl() string {
	if x != nil {
		return x.GatewayUrl
	}
	return ""
}

func (x *Access) GetChannelNameItems() string {
	if x != nil {
		return x.ChannelNameItems
	}
	return ""
}

func (x *Access) GetAccessId() int64 {
	if x != nil {
		return x.AccessId
	}
	return 0
}

func (x *Access) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *Access) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

func (x *Access) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *Access) GetTerminalId() string {
	if x != nil {
		return x.TerminalId
	}
	return ""
}

func (x *Access) GetApiVersion() string {
	if x != nil {
		return x.ApiVersion
	}
	return ""
}

func (x *Access) GetSubMerchantId() string {
	if x != nil {
		return x.SubMerchantId
	}
	return ""
}

type ListAccessQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）查询类型
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// 租户id
	PartnerId string `protobuf:"bytes,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// 公司id
	CompanyId string `protobuf:"bytes,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店id
	StoreId string `protobuf:"bytes,4,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// 业务id
	BusinessCode string `protobuf:"bytes,5,opt,name=business_code,json=businessCode,proto3" json:"business_code,omitempty"`
	// 配置层级
	Level int32 `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	// 分页
	PageIndex int32 `protobuf:"varint,7,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize  int32 `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 搜索字段
	Search string `protobuf:"bytes,9,opt,name=search,proto3" json:"search,omitempty"`
	// 搜索表字段,分隔
	SearchFields string `protobuf:"bytes,10,opt,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"`
}

func (x *ListAccessQuery) Reset() {
	*x = ListAccessQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAccessQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAccessQuery) ProtoMessage() {}

func (x *ListAccessQuery) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAccessQuery.ProtoReflect.Descriptor instead.
func (*ListAccessQuery) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{14}
}

func (x *ListAccessQuery) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListAccessQuery) GetPartnerId() string {
	if x != nil {
		return x.PartnerId
	}
	return ""
}

func (x *ListAccessQuery) GetCompanyId() string {
	if x != nil {
		return x.CompanyId
	}
	return ""
}

func (x *ListAccessQuery) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *ListAccessQuery) GetBusinessCode() string {
	if x != nil {
		return x.BusinessCode
	}
	return ""
}

func (x *ListAccessQuery) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ListAccessQuery) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ListAccessQuery) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAccessQuery) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ListAccessQuery) GetSearchFields() string {
	if x != nil {
		return x.SearchFields
	}
	return ""
}

// 渠道管理请求
type ChannelManagementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action           string            `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	ListChannelQuery *ListChannelQuery `protobuf:"bytes,2,opt,name=list_channel_query,json=listChannelQuery,proto3" json:"list_channel_query,omitempty"`
	Channel          *Channel          `protobuf:"bytes,3,opt,name=channel,proto3" json:"channel,omitempty"`
}

func (x *ChannelManagementRequest) Reset() {
	*x = ChannelManagementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelManagementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelManagementRequest) ProtoMessage() {}

func (x *ChannelManagementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelManagementRequest.ProtoReflect.Descriptor instead.
func (*ChannelManagementRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{15}
}

func (x *ChannelManagementRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelManagementRequest) GetListChannelQuery() *ListChannelQuery {
	if x != nil {
		return x.ListChannelQuery
	}
	return nil
}

func (x *ChannelManagementRequest) GetChannel() *Channel {
	if x != nil {
		return x.Channel
	}
	return nil
}

// 渠道管理接口响应
type ChannelManagementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode    string     `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage string     `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ChannelList  []*Channel `protobuf:"bytes,3,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
}

func (x *ChannelManagementResponse) Reset() {
	*x = ChannelManagementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelManagementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelManagementResponse) ProtoMessage() {}

func (x *ChannelManagementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelManagementResponse.ProtoReflect.Descriptor instead.
func (*ChannelManagementResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{16}
}

func (x *ChannelManagementResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelManagementResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelManagementResponse) GetChannelList() []*Channel {
	if x != nil {
		return x.ChannelList
	}
	return nil
}

// 渠道列表查询
type ListChannelQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）查询类型
	ChannelCategory string `protobuf:"bytes,1,opt,name=channel_category,json=channelCategory,proto3" json:"channel_category,omitempty"`
	// 搜索名称
	SearchName string `protobuf:"bytes,2,opt,name=search_name,json=searchName,proto3" json:"search_name,omitempty"`
	// 渠道code
	ChannelCode string `protobuf:"bytes,3,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// 子分类
	ChannelSubCategory string `protobuf:"bytes,4,opt,name=channel_sub_category,json=channelSubCategory,proto3" json:"channel_sub_category,omitempty"`
}

func (x *ListChannelQuery) Reset() {
	*x = ListChannelQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListChannelQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListChannelQuery) ProtoMessage() {}

func (x *ListChannelQuery) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListChannelQuery.ProtoReflect.Descriptor instead.
func (*ListChannelQuery) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{17}
}

func (x *ListChannelQuery) GetChannelCategory() string {
	if x != nil {
		return x.ChannelCategory
	}
	return ""
}

func (x *ListChannelQuery) GetSearchName() string {
	if x != nil {
		return x.SearchName
	}
	return ""
}

func (x *ListChannelQuery) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListChannelQuery) GetChannelSubCategory() string {
	if x != nil {
		return x.ChannelSubCategory
	}
	return ""
}

type Channel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 渠道编码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// 渠道名称
	ChannelName string `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	// 渠道logo
	ChannelLogo string `protobuf:"bytes,3,opt,name=channel_logo,json=channelLogo,proto3" json:"channel_logo,omitempty"`
	// 渠道性质，KA 品牌商；ISV 服务商
	ChannelType string `protobuf:"bytes,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
	ChannelCategory string `protobuf:"bytes,5,opt,name=channel_category,json=channelCategory,proto3" json:"channel_category,omitempty"`
	// 渠道描述
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// 渠道附加信息，JSON格式
	ChannelProperties string `protobuf:"bytes,7,opt,name=channel_properties,json=channelProperties,proto3" json:"channel_properties,omitempty"`
	// 渠道主键
	ChannelId int64 `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	// 渠道标签
	ChannelLabels string `protobuf:"bytes,9,opt,name=channel_labels,json=channelLabels,proto3" json:"channel_labels,omitempty"`
	// 渠道二级分类，AM 小程序&会员；COUPON 卡券
	ChannelSubCategory string `protobuf:"bytes,10,opt,name=channel_sub_category,json=channelSubCategory,proto3" json:"channel_sub_category,omitempty"`
}

func (x *Channel) Reset() {
	*x = Channel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Channel) ProtoMessage() {}

func (x *Channel) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Channel.ProtoReflect.Descriptor instead.
func (*Channel) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{18}
}

func (x *Channel) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *Channel) GetChannelName() string {
	if x != nil {
		return x.ChannelName
	}
	return ""
}

func (x *Channel) GetChannelLogo() string {
	if x != nil {
		return x.ChannelLogo
	}
	return ""
}

func (x *Channel) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *Channel) GetChannelCategory() string {
	if x != nil {
		return x.ChannelCategory
	}
	return ""
}

func (x *Channel) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Channel) GetChannelProperties() string {
	if x != nil {
		return x.ChannelProperties
	}
	return ""
}

func (x *Channel) GetChannelId() int64 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *Channel) GetChannelLabels() string {
	if x != nil {
		return x.ChannelLabels
	}
	return ""
}

func (x *Channel) GetChannelSubCategory() string {
	if x != nil {
		return x.ChannelSubCategory
	}
	return ""
}

// 渠道签约授权请求信息
type ChannelAuthorizationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// （必传）业务操作
	// listChannelBinding 查询渠道绑定关系
	// switchChannelBinding 绑定/解绑渠道
	// listSignupAuthorization 查询签约授权信息
	// signup 渠道签约
	// getSignupDetail 查询渠道签约详情
	// refreshSignupState 刷新渠道签约状态
	// getStoreGrantUrl 获取门店授权链接
	// getStoreCancelGrantUrl 获取门店取消授权链接
	// listStoreAuthorization 查询门店授权信息
	Action string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	// （可选）渠道绑定关系查询请求对象
	ListBindingSection *ListBindingSection `protobuf:"bytes,2,opt,name=list_binding_section,json=listBindingSection,proto3" json:"list_binding_section,omitempty"`
	// （可选）绑定/解绑渠道请求对象
	SwitchBindingSection *SwitchBindingSection `protobuf:"bytes,3,opt,name=switch_binding_section,json=switchBindingSection,proto3" json:"switch_binding_section,omitempty"`
	// （可选）渠道签约授权信息查询请求对象
	ListAuthorizationSection *ListAuthorizationSection `protobuf:"bytes,4,opt,name=list_authorization_section,json=listAuthorizationSection,proto3" json:"list_authorization_section,omitempty"`
	// （可选）渠道签约请求对象
	SignupSection *SignupSection `protobuf:"bytes,5,opt,name=signup_section,json=signupSection,proto3" json:"signup_section,omitempty"`
	// （可选）渠道签约详情查询请求对象
	QuerySignupSection *QuerySignupSection `protobuf:"bytes,6,opt,name=query_signup_section,json=querySignupSection,proto3" json:"query_signup_section,omitempty"`
	// （可选）渠道签约状态刷新请求对象
	RefreshSignupSection *RefreshSignupSection `protobuf:"bytes,7,opt,name=refresh_signup_section,json=refreshSignupSection,proto3" json:"refresh_signup_section,omitempty"`
	// （可选）渠道签约授权信息查询请求对象
	ListStoreAuthorizationSection *ListStoreAuthorizationSection `protobuf:"bytes,8,opt,name=list_store_authorization_section,json=listStoreAuthorizationSection,proto3" json:"list_store_authorization_section,omitempty"`
	// （可选）渠道门店授权请求
	GetStoreGrantUrlSection *GetStoreGrantUrlSection `protobuf:"bytes,9,opt,name=get_store_grant_url_section,json=getStoreGrantUrlSection,proto3" json:"get_store_grant_url_section,omitempty"`
	// （可选）渠道门店取消授权请求
	GetStoreCancelGrantUrlSection *GetStoreCancelGrantUrlSection `protobuf:"bytes,10,opt,name=get_store_cancel_grant_url_section,json=getStoreCancelGrantUrlSection,proto3" json:"get_store_cancel_grant_url_section,omitempty"`
	// （可选）查询授权账户列表
	ListGrantAccountSection *ListGrantAccountSection `protobuf:"bytes,11,opt,name=list_grant_account_section,json=listGrantAccountSection,proto3" json:"list_grant_account_section,omitempty"`
	// （可选）渠道授权请求
	GetGrantUrlSection *GetGrantUrlSection `protobuf:"bytes,12,opt,name=get_grant_url_section,json=getGrantUrlSection,proto3" json:"get_grant_url_section,omitempty"`
	// （可选）渠道取消授权请求
	GetCancelGrantUrlSection *GetCancelGrantUrlSection `protobuf:"bytes,13,opt,name=get_cancel_grant_url_section,json=getCancelGrantUrlSection,proto3" json:"get_cancel_grant_url_section,omitempty"`
}

func (x *ChannelAuthorizationRequest) Reset() {
	*x = ChannelAuthorizationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAuthorizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAuthorizationRequest) ProtoMessage() {}

func (x *ChannelAuthorizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAuthorizationRequest.ProtoReflect.Descriptor instead.
func (*ChannelAuthorizationRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{19}
}

func (x *ChannelAuthorizationRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelAuthorizationRequest) GetListBindingSection() *ListBindingSection {
	if x != nil {
		return x.ListBindingSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetSwitchBindingSection() *SwitchBindingSection {
	if x != nil {
		return x.SwitchBindingSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetListAuthorizationSection() *ListAuthorizationSection {
	if x != nil {
		return x.ListAuthorizationSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetSignupSection() *SignupSection {
	if x != nil {
		return x.SignupSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetQuerySignupSection() *QuerySignupSection {
	if x != nil {
		return x.QuerySignupSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetRefreshSignupSection() *RefreshSignupSection {
	if x != nil {
		return x.RefreshSignupSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetListStoreAuthorizationSection() *ListStoreAuthorizationSection {
	if x != nil {
		return x.ListStoreAuthorizationSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetGetStoreGrantUrlSection() *GetStoreGrantUrlSection {
	if x != nil {
		return x.GetStoreGrantUrlSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetGetStoreCancelGrantUrlSection() *GetStoreCancelGrantUrlSection {
	if x != nil {
		return x.GetStoreCancelGrantUrlSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetListGrantAccountSection() *ListGrantAccountSection {
	if x != nil {
		return x.ListGrantAccountSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetGetGrantUrlSection() *GetGrantUrlSection {
	if x != nil {
		return x.GetGrantUrlSection
	}
	return nil
}

func (x *ChannelAuthorizationRequest) GetGetCancelGrantUrlSection() *GetCancelGrantUrlSection {
	if x != nil {
		return x.GetCancelGrantUrlSection
	}
	return nil
}

// 渠道签约授权响应信息
type ChannelAuthorizationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）异常编码
	ErrorCode string `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// （必传）异常信息
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// （可选）渠道绑定关系信息
	BindingListSection *BindingListSection `protobuf:"bytes,3,opt,name=binding_list_section,json=bindingListSection,proto3" json:"binding_list_section,omitempty"`
	// （可选）渠道签约授权信息
	AuthorizationListSection *AuthorizationListSection `protobuf:"bytes,4,opt,name=authorization_list_section,json=authorizationListSection,proto3" json:"authorization_list_section,omitempty"`
	// （可选）渠道签约结果
	SignupResultSection *SignupResultSection `protobuf:"bytes,5,opt,name=signup_result_section,json=signupResultSection,proto3" json:"signup_result_section,omitempty"`
	// （可选）渠道签约详情
	SignupDetailSection *SignupDetailSection `protobuf:"bytes,6,opt,name=signup_detail_section,json=signupDetailSection,proto3" json:"signup_detail_section,omitempty"`
	// （可选）渠道签约状态刷新结果
	RefreshSignupResultSection *RefreshSignupResultSection `protobuf:"bytes,7,opt,name=refresh_signup_result_section,json=refreshSignupResultSection,proto3" json:"refresh_signup_result_section,omitempty"`
	// （可选）授权URL信息
	GrantUrlSection *GrantUrlSection `protobuf:"bytes,8,opt,name=grant_url_section,json=grantUrlSection,proto3" json:"grant_url_section,omitempty"`
	// （可选）渠道签约授权信息查询请求对象
	StoreAuthorizationListSection *StoreAuthorizationListSection `protobuf:"bytes,9,opt,name=store_authorization_list_section,json=storeAuthorizationListSection,proto3" json:"store_authorization_list_section,omitempty"`
	// （可选）授权账户列表
	GrantAccountListSection *GrantAccountListSection `protobuf:"bytes,10,opt,name=grant_account_list_section,json=grantAccountListSection,proto3" json:"grant_account_list_section,omitempty"`
}

func (x *ChannelAuthorizationResponse) Reset() {
	*x = ChannelAuthorizationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelAuthorizationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelAuthorizationResponse) ProtoMessage() {}

func (x *ChannelAuthorizationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelAuthorizationResponse.ProtoReflect.Descriptor instead.
func (*ChannelAuthorizationResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{20}
}

func (x *ChannelAuthorizationResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelAuthorizationResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelAuthorizationResponse) GetBindingListSection() *BindingListSection {
	if x != nil {
		return x.BindingListSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetAuthorizationListSection() *AuthorizationListSection {
	if x != nil {
		return x.AuthorizationListSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetSignupResultSection() *SignupResultSection {
	if x != nil {
		return x.SignupResultSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetSignupDetailSection() *SignupDetailSection {
	if x != nil {
		return x.SignupDetailSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetRefreshSignupResultSection() *RefreshSignupResultSection {
	if x != nil {
		return x.RefreshSignupResultSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetGrantUrlSection() *GrantUrlSection {
	if x != nil {
		return x.GrantUrlSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetStoreAuthorizationListSection() *StoreAuthorizationListSection {
	if x != nil {
		return x.StoreAuthorizationListSection
	}
	return nil
}

func (x *ChannelAuthorizationResponse) GetGrantAccountListSection() *GrantAccountListSection {
	if x != nil {
		return x.GrantAccountListSection
	}
	return nil
}

// 渠道基础服务请求信息
type ChannelInfraRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）业务操作
	Action string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	// （可选）短信验证码请求信息
	SendSmsCodeSection *SendSMSCodeSection `protobuf:"bytes,2,opt,name=send_sms_code_section,json=sendSmsCodeSection,proto3" json:"send_sms_code_section,omitempty"`
}

func (x *ChannelInfraRequest) Reset() {
	*x = ChannelInfraRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelInfraRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelInfraRequest) ProtoMessage() {}

func (x *ChannelInfraRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelInfraRequest.ProtoReflect.Descriptor instead.
func (*ChannelInfraRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{21}
}

func (x *ChannelInfraRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelInfraRequest) GetSendSmsCodeSection() *SendSMSCodeSection {
	if x != nil {
		return x.SendSmsCodeSection
	}
	return nil
}

// 渠道基础服务响应信息
type ChannelInfraResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）异常编码
	ErrorCode string `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// （必传）异常信息
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// （可选）短信验证码响应信息
	SmsResultSection *SMSResultSection `protobuf:"bytes,3,opt,name=sms_result_section,json=smsResultSection,proto3" json:"sms_result_section,omitempty"`
}

func (x *ChannelInfraResponse) Reset() {
	*x = ChannelInfraResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelInfraResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelInfraResponse) ProtoMessage() {}

func (x *ChannelInfraResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelInfraResponse.ProtoReflect.Descriptor instead.
func (*ChannelInfraResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{22}
}

func (x *ChannelInfraResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelInfraResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelInfraResponse) GetSmsResultSection() *SMSResultSection {
	if x != nil {
		return x.SmsResultSection
	}
	return nil
}

// 渠道绑定关系查询对象
type ListBindingSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
	ChannelCategory string `protobuf:"bytes,1,opt,name=channel_category,json=channelCategory,proto3" json:"channel_category,omitempty"`
	// （可选）渠道是否启用
	Enabled string `protobuf:"bytes,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// （可选）渠道标签，多个以","分割
	ChannelLabels string `protobuf:"bytes,3,opt,name=channel_labels,json=channelLabels,proto3" json:"channel_labels,omitempty"`
	// （可选）渠道业务代码，多个以","分割
	BusinessCode string `protobuf:"bytes,4,opt,name=business_code,json=businessCode,proto3" json:"business_code,omitempty"`
}

func (x *ListBindingSection) Reset() {
	*x = ListBindingSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBindingSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBindingSection) ProtoMessage() {}

func (x *ListBindingSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBindingSection.ProtoReflect.Descriptor instead.
func (*ListBindingSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{23}
}

func (x *ListBindingSection) GetChannelCategory() string {
	if x != nil {
		return x.ChannelCategory
	}
	return ""
}

func (x *ListBindingSection) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

func (x *ListBindingSection) GetChannelLabels() string {
	if x != nil {
		return x.ChannelLabels
	}
	return ""
}

func (x *ListBindingSection) GetBusinessCode() string {
	if x != nil {
		return x.BusinessCode
	}
	return ""
}

// 渠道绑定关系查询结果
type BindingListSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道绑定关系信息
	BindingItem []*BindingItem `protobuf:"bytes,1,rep,name=binding_item,json=bindingItem,proto3" json:"binding_item,omitempty"`
}

func (x *BindingListSection) Reset() {
	*x = BindingListSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingListSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingListSection) ProtoMessage() {}

func (x *BindingListSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingListSection.ProtoReflect.Descriptor instead.
func (*BindingListSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{24}
}

func (x *BindingListSection) GetBindingItem() []*BindingItem {
	if x != nil {
		return x.BindingItem
	}
	return nil
}

// 渠道绑定关系信息
type BindingItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）渠道名称
	ChannelName string `protobuf:"bytes,2,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	// （必传）渠道业务代码，","分割
	BusinessCode string `protobuf:"bytes,3,opt,name=business_code,json=businessCode,proto3" json:"business_code,omitempty"`
	// （必传）渠道性质，KA 品牌商；ISV 服务商
	ChannelType string `protobuf:"bytes,4,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// （必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
	ChannelCategory string `protobuf:"bytes,5,opt,name=channel_category,json=channelCategory,proto3" json:"channel_category,omitempty"`
	// （可选）渠道二级分类，AM 小程序&会员；COUPON 卡券
	ChannelSubCategory string `protobuf:"bytes,6,opt,name=channel_sub_category,json=channelSubCategory,proto3" json:"channel_sub_category,omitempty"`
	// （可选）渠道标签，逗号分割
	ChannelLabels string `protobuf:"bytes,7,opt,name=channel_labels,json=channelLabels,proto3" json:"channel_labels,omitempty"`
	// （必传）是否启用
	Enabled string `protobuf:"bytes,8,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *BindingItem) Reset() {
	*x = BindingItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingItem) ProtoMessage() {}

func (x *BindingItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingItem.ProtoReflect.Descriptor instead.
func (*BindingItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{25}
}

func (x *BindingItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *BindingItem) GetChannelName() string {
	if x != nil {
		return x.ChannelName
	}
	return ""
}

func (x *BindingItem) GetBusinessCode() string {
	if x != nil {
		return x.BusinessCode
	}
	return ""
}

func (x *BindingItem) GetChannelType() string {
	if x != nil {
		return x.ChannelType
	}
	return ""
}

func (x *BindingItem) GetChannelCategory() string {
	if x != nil {
		return x.ChannelCategory
	}
	return ""
}

func (x *BindingItem) GetChannelSubCategory() string {
	if x != nil {
		return x.ChannelSubCategory
	}
	return ""
}

func (x *BindingItem) GetChannelLabels() string {
	if x != nil {
		return x.ChannelLabels
	}
	return ""
}

func (x *BindingItem) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

// 绑定/解绑渠道请求
type SwitchBindingSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （可选）是否启用，与business_code不能同时为空
	Enabled string `protobuf:"bytes,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// （可选）启用的渠道业务代码，","分割，与enabled不能同时为空
	BusinessCode string `protobuf:"bytes,3,opt,name=business_code,json=businessCode,proto3" json:"business_code,omitempty"`
}

func (x *SwitchBindingSection) Reset() {
	*x = SwitchBindingSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchBindingSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchBindingSection) ProtoMessage() {}

func (x *SwitchBindingSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchBindingSection.ProtoReflect.Descriptor instead.
func (*SwitchBindingSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{26}
}

func (x *SwitchBindingSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *SwitchBindingSection) GetEnabled() string {
	if x != nil {
		return x.Enabled
	}
	return ""
}

func (x *SwitchBindingSection) GetBusinessCode() string {
	if x != nil {
		return x.BusinessCode
	}
	return ""
}

// 渠道签约授权信息查询对象
type ListAuthorizationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （可选）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
	SignupState string `protobuf:"bytes,2,opt,name=signup_state,json=signupState,proto3" json:"signup_state,omitempty"`
	// （可选）授权状态，WAITING 待授权；COMPLETE 已授权
	GrantState string `protobuf:"bytes,3,opt,name=grant_state,json=grantState,proto3" json:"grant_state,omitempty"`
	// （可选）复合查询条件，商户第三方商户账号、签约流水号
	ComplexCriterion string `protobuf:"bytes,4,opt,name=complex_criterion,json=complexCriterion,proto3" json:"complex_criterion,omitempty"`
	// （可选）当前页码
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// （可选）分页步长
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListAuthorizationSection) Reset() {
	*x = ListAuthorizationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListAuthorizationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuthorizationSection) ProtoMessage() {}

func (x *ListAuthorizationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuthorizationSection.ProtoReflect.Descriptor instead.
func (*ListAuthorizationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{27}
}

func (x *ListAuthorizationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListAuthorizationSection) GetSignupState() string {
	if x != nil {
		return x.SignupState
	}
	return ""
}

func (x *ListAuthorizationSection) GetGrantState() string {
	if x != nil {
		return x.GrantState
	}
	return ""
}

func (x *ListAuthorizationSection) GetComplexCriterion() string {
	if x != nil {
		return x.ComplexCriterion
	}
	return ""
}

func (x *ListAuthorizationSection) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ListAuthorizationSection) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 渠道签约授权信息查询结果
type AuthorizationListSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）当前页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// （必传）分页步长
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// （必传）总页数
	PageCount int32 `protobuf:"varint,3,opt,name=page_count,json=pageCount,proto3" json:"page_count,omitempty"`
	// （必传）总条数
	Total int64 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	// （必传）渠道签约授权信息
	AuthorizationItem []*AuthorizationItem `protobuf:"bytes,5,rep,name=authorization_item,json=authorizationItem,proto3" json:"authorization_item,omitempty"`
}

func (x *AuthorizationListSection) Reset() {
	*x = AuthorizationListSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorizationListSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizationListSection) ProtoMessage() {}

func (x *AuthorizationListSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizationListSection.ProtoReflect.Descriptor instead.
func (*AuthorizationListSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{28}
}

func (x *AuthorizationListSection) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *AuthorizationListSection) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AuthorizationListSection) GetPageCount() int32 {
	if x != nil {
		return x.PageCount
	}
	return 0
}

func (x *AuthorizationListSection) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AuthorizationListSection) GetAuthorizationItem() []*AuthorizationItem {
	if x != nil {
		return x.AuthorizationItem
	}
	return nil
}

// 渠道签约授权信息对象
type AuthorizationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）签约授权ID
	AuthId int32 `protobuf:"varint,1,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）第三方商户PID
	MerchantId string `protobuf:"bytes,3,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// （必传）签约单号
	SignupNo string `protobuf:"bytes,4,opt,name=signup_no,json=signupNo,proto3" json:"signup_no,omitempty"`
	// （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
	SignupState string `protobuf:"bytes,5,opt,name=signup_state,json=signupState,proto3" json:"signup_state,omitempty"`
	// （必传）授权状态，WAITING 待授权；COMPLETE 已授权
	GrantState string `protobuf:"bytes,6,opt,name=grant_state,json=grantState,proto3" json:"grant_state,omitempty"`
}

func (x *AuthorizationItem) Reset() {
	*x = AuthorizationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthorizationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthorizationItem) ProtoMessage() {}

func (x *AuthorizationItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthorizationItem.ProtoReflect.Descriptor instead.
func (*AuthorizationItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{29}
}

func (x *AuthorizationItem) GetAuthId() int32 {
	if x != nil {
		return x.AuthId
	}
	return 0
}

func (x *AuthorizationItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *AuthorizationItem) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *AuthorizationItem) GetSignupNo() string {
	if x != nil {
		return x.SignupNo
	}
	return ""
}

func (x *AuthorizationItem) GetSignupState() string {
	if x != nil {
		return x.SignupState
	}
	return ""
}

func (x *AuthorizationItem) GetGrantState() string {
	if x != nil {
		return x.GrantState
	}
	return ""
}

// 门店授权请求
type GetStoreGrantUrlSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道编码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）门店ID
	StoreId string `protobuf:"bytes,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// （可选）授权标记
	GrantTargetId string `protobuf:"bytes,3,opt,name=grant_target_id,json=grantTargetId,proto3" json:"grant_target_id,omitempty"`
	// （可选）授权的渠道业务
	GrantBusiness string `protobuf:"bytes,4,opt,name=grant_business,json=grantBusiness,proto3" json:"grant_business,omitempty"`
}

func (x *GetStoreGrantUrlSection) Reset() {
	*x = GetStoreGrantUrlSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStoreGrantUrlSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoreGrantUrlSection) ProtoMessage() {}

func (x *GetStoreGrantUrlSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoreGrantUrlSection.ProtoReflect.Descriptor instead.
func (*GetStoreGrantUrlSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{30}
}

func (x *GetStoreGrantUrlSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *GetStoreGrantUrlSection) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *GetStoreGrantUrlSection) GetGrantTargetId() string {
	if x != nil {
		return x.GrantTargetId
	}
	return ""
}

func (x *GetStoreGrantUrlSection) GetGrantBusiness() string {
	if x != nil {
		return x.GrantBusiness
	}
	return ""
}

// 门店取消授权请求
type GetStoreCancelGrantUrlSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道编码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）门店ID
	StoreId string `protobuf:"bytes,2,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// (必传) appAuthToken
	Token string `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	// （可选）取消授权的渠道业务
	GrantBusiness string `protobuf:"bytes,4,opt,name=grant_business,json=grantBusiness,proto3" json:"grant_business,omitempty"`
}

func (x *GetStoreCancelGrantUrlSection) Reset() {
	*x = GetStoreCancelGrantUrlSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStoreCancelGrantUrlSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoreCancelGrantUrlSection) ProtoMessage() {}

func (x *GetStoreCancelGrantUrlSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoreCancelGrantUrlSection.ProtoReflect.Descriptor instead.
func (*GetStoreCancelGrantUrlSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{31}
}

func (x *GetStoreCancelGrantUrlSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *GetStoreCancelGrantUrlSection) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *GetStoreCancelGrantUrlSection) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetStoreCancelGrantUrlSection) GetGrantBusiness() string {
	if x != nil {
		return x.GrantBusiness
	}
	return ""
}

// 授权请求
type GetGrantUrlSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道编码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （可选）授权标记
	GrantTargetId string `protobuf:"bytes,2,opt,name=grant_target_id,json=grantTargetId,proto3" json:"grant_target_id,omitempty"`
	// （可选）授权的渠道业务
	GrantBusiness string `protobuf:"bytes,3,opt,name=grant_business,json=grantBusiness,proto3" json:"grant_business,omitempty"`
}

func (x *GetGrantUrlSection) Reset() {
	*x = GetGrantUrlSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGrantUrlSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGrantUrlSection) ProtoMessage() {}

func (x *GetGrantUrlSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGrantUrlSection.ProtoReflect.Descriptor instead.
func (*GetGrantUrlSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{32}
}

func (x *GetGrantUrlSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *GetGrantUrlSection) GetGrantTargetId() string {
	if x != nil {
		return x.GrantTargetId
	}
	return ""
}

func (x *GetGrantUrlSection) GetGrantBusiness() string {
	if x != nil {
		return x.GrantBusiness
	}
	return ""
}

// 取消授权请求
type GetCancelGrantUrlSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道编码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// (必传) appAuthToken
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// （可选）取消授权的渠道业务
	GrantBusiness string `protobuf:"bytes,3,opt,name=grant_business,json=grantBusiness,proto3" json:"grant_business,omitempty"`
}

func (x *GetCancelGrantUrlSection) Reset() {
	*x = GetCancelGrantUrlSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCancelGrantUrlSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCancelGrantUrlSection) ProtoMessage() {}

func (x *GetCancelGrantUrlSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCancelGrantUrlSection.ProtoReflect.Descriptor instead.
func (*GetCancelGrantUrlSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{33}
}

func (x *GetCancelGrantUrlSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *GetCancelGrantUrlSection) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetCancelGrantUrlSection) GetGrantBusiness() string {
	if x != nil {
		return x.GrantBusiness
	}
	return ""
}

// 授权URL信息
type GrantUrlSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 授权url
	GrantUrl string `protobuf:"bytes,1,opt,name=grant_url,json=grantUrl,proto3" json:"grant_url,omitempty"`
	// 取消授权url
	CancelGrantUrl string `protobuf:"bytes,2,opt,name=cancel_grant_url,json=cancelGrantUrl,proto3" json:"cancel_grant_url,omitempty"`
}

func (x *GrantUrlSection) Reset() {
	*x = GrantUrlSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantUrlSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantUrlSection) ProtoMessage() {}

func (x *GrantUrlSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantUrlSection.ProtoReflect.Descriptor instead.
func (*GrantUrlSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{34}
}

func (x *GrantUrlSection) GetGrantUrl() string {
	if x != nil {
		return x.GrantUrl
	}
	return ""
}

func (x *GrantUrlSection) GetCancelGrantUrl() string {
	if x != nil {
		return x.CancelGrantUrl
	}
	return ""
}

// 授权账户列表
type ListGrantAccountSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelCode   string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	GrantTargetId string `protobuf:"bytes,2,opt,name=grant_target_id,json=grantTargetId,proto3" json:"grant_target_id,omitempty"`
	State         string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *ListGrantAccountSection) Reset() {
	*x = ListGrantAccountSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListGrantAccountSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGrantAccountSection) ProtoMessage() {}

func (x *ListGrantAccountSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGrantAccountSection.ProtoReflect.Descriptor instead.
func (*ListGrantAccountSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{35}
}

func (x *ListGrantAccountSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListGrantAccountSection) GetGrantTargetId() string {
	if x != nil {
		return x.GrantTargetId
	}
	return ""
}

func (x *ListGrantAccountSection) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

// 授权账户列表
type GrantAccountListSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GrantAccountSection []*GrantAccountSection `protobuf:"bytes,1,rep,name=grant_account_section,json=grantAccountSection,proto3" json:"grant_account_section,omitempty"`
}

func (x *GrantAccountListSection) Reset() {
	*x = GrantAccountListSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantAccountListSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantAccountListSection) ProtoMessage() {}

func (x *GrantAccountListSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantAccountListSection.ProtoReflect.Descriptor instead.
func (*GrantAccountListSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{36}
}

func (x *GrantAccountListSection) GetGrantAccountSection() []*GrantAccountSection {
	if x != nil {
		return x.GrantAccountSection
	}
	return nil
}

type GrantAccountSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChannelCode   string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	GrantTargetId string `protobuf:"bytes,2,opt,name=grant_target_id,json=grantTargetId,proto3" json:"grant_target_id,omitempty"`
	State         string `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	AuthId        string `protobuf:"bytes,4,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
}

func (x *GrantAccountSection) Reset() {
	*x = GrantAccountSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrantAccountSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrantAccountSection) ProtoMessage() {}

func (x *GrantAccountSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrantAccountSection.ProtoReflect.Descriptor instead.
func (*GrantAccountSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{37}
}

func (x *GrantAccountSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *GrantAccountSection) GetGrantTargetId() string {
	if x != nil {
		return x.GrantTargetId
	}
	return ""
}

func (x *GrantAccountSection) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *GrantAccountSection) GetAuthId() string {
	if x != nil {
		return x.AuthId
	}
	return ""
}

// 渠道签约请求对象
type SignupSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）第三方商户PID
	MerchantId string `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// （必传）经营类目
	BusinessCategory string `protobuf:"bytes,3,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	// （必传）服务费率
	ServiceRate float64 `protobuf:"fixed64,4,opt,name=service_rate,json=serviceRate,proto3" json:"service_rate,omitempty"`
	// （必传）签约主体
	PartyName string `protobuf:"bytes,5,opt,name=party_name,json=partyName,proto3" json:"party_name,omitempty"`
	// （可选）签约主体所在地
	PartyLocation *Location `protobuf:"bytes,6,opt,name=party_location,json=partyLocation,proto3" json:"party_location,omitempty"`
	// （可选）联系人
	Contact *Contact `protobuf:"bytes,7,opt,name=contact,proto3" json:"contact,omitempty"`
	// （可选）签约附件
	Attachments []*Attachment `protobuf:"bytes,8,rep,name=attachments,proto3" json:"attachments,omitempty"`
	// （必传）短信id
	SmsId string `protobuf:"bytes,9,opt,name=sms_id,json=smsId,proto3" json:"sms_id,omitempty"`
	// （必传）短信验证码
	SmsCode string `protobuf:"bytes,10,opt,name=sms_code,json=smsCode,proto3" json:"sms_code,omitempty"`
}

func (x *SignupSection) Reset() {
	*x = SignupSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignupSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupSection) ProtoMessage() {}

func (x *SignupSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupSection.ProtoReflect.Descriptor instead.
func (*SignupSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{38}
}

func (x *SignupSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *SignupSection) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *SignupSection) GetBusinessCategory() string {
	if x != nil {
		return x.BusinessCategory
	}
	return ""
}

func (x *SignupSection) GetServiceRate() float64 {
	if x != nil {
		return x.ServiceRate
	}
	return 0
}

func (x *SignupSection) GetPartyName() string {
	if x != nil {
		return x.PartyName
	}
	return ""
}

func (x *SignupSection) GetPartyLocation() *Location {
	if x != nil {
		return x.PartyLocation
	}
	return nil
}

func (x *SignupSection) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *SignupSection) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

func (x *SignupSection) GetSmsId() string {
	if x != nil {
		return x.SmsId
	}
	return ""
}

func (x *SignupSection) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

// 渠道签约结果
type SignupResultSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）签约流水号
	SignupNo string `protobuf:"bytes,1,opt,name=signup_no,json=signupNo,proto3" json:"signup_no,omitempty"`
	// （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
	SignupState string `protobuf:"bytes,2,opt,name=signup_state,json=signupState,proto3" json:"signup_state,omitempty"`
	// （可选）签约结果
	SignupResult string `protobuf:"bytes,3,opt,name=signup_result,json=signupResult,proto3" json:"signup_result,omitempty"`
}

func (x *SignupResultSection) Reset() {
	*x = SignupResultSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignupResultSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupResultSection) ProtoMessage() {}

func (x *SignupResultSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupResultSection.ProtoReflect.Descriptor instead.
func (*SignupResultSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{39}
}

func (x *SignupResultSection) GetSignupNo() string {
	if x != nil {
		return x.SignupNo
	}
	return ""
}

func (x *SignupResultSection) GetSignupState() string {
	if x != nil {
		return x.SignupState
	}
	return ""
}

func (x *SignupResultSection) GetSignupResult() string {
	if x != nil {
		return x.SignupResult
	}
	return ""
}

// 渠道签约详情查询对象
type QuerySignupSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道签约授权id
	ChannelAuthId int32 `protobuf:"varint,1,opt,name=channel_auth_id,json=channelAuthId,proto3" json:"channel_auth_id,omitempty"`
}

func (x *QuerySignupSection) Reset() {
	*x = QuerySignupSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySignupSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySignupSection) ProtoMessage() {}

func (x *QuerySignupSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySignupSection.ProtoReflect.Descriptor instead.
func (*QuerySignupSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{40}
}

func (x *QuerySignupSection) GetChannelAuthId() int32 {
	if x != nil {
		return x.ChannelAuthId
	}
	return 0
}

// 渠道签约详情查询结果
type SignupDetailSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）第三方商户PID
	MerchantId string `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	// （必传）经营类目
	BusinessCategory string `protobuf:"bytes,3,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	// （必传）签约主体
	PartyName string `protobuf:"bytes,4,opt,name=party_name,json=partyName,proto3" json:"party_name,omitempty"`
	// （可选）签约主体所在地
	PartyLocation *Location `protobuf:"bytes,5,opt,name=party_location,json=partyLocation,proto3" json:"party_location,omitempty"`
	// （可选）联系人
	Contact *Contact `protobuf:"bytes,6,opt,name=contact,proto3" json:"contact,omitempty"`
	// （可选）签约附件
	Attachments []*Attachment `protobuf:"bytes,7,rep,name=attachments,proto3" json:"attachments,omitempty"`
}

func (x *SignupDetailSection) Reset() {
	*x = SignupDetailSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignupDetailSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupDetailSection) ProtoMessage() {}

func (x *SignupDetailSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupDetailSection.ProtoReflect.Descriptor instead.
func (*SignupDetailSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{41}
}

func (x *SignupDetailSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *SignupDetailSection) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *SignupDetailSection) GetBusinessCategory() string {
	if x != nil {
		return x.BusinessCategory
	}
	return ""
}

func (x *SignupDetailSection) GetPartyName() string {
	if x != nil {
		return x.PartyName
	}
	return ""
}

func (x *SignupDetailSection) GetPartyLocation() *Location {
	if x != nil {
		return x.PartyLocation
	}
	return nil
}

func (x *SignupDetailSection) GetContact() *Contact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *SignupDetailSection) GetAttachments() []*Attachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

// 渠道签约状态刷新请求对象
type RefreshSignupSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道签约授权id
	ChannelAuthId []int32 `protobuf:"varint,1,rep,packed,name=channel_auth_id,json=channelAuthId,proto3" json:"channel_auth_id,omitempty"`
}

func (x *RefreshSignupSection) Reset() {
	*x = RefreshSignupSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshSignupSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshSignupSection) ProtoMessage() {}

func (x *RefreshSignupSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshSignupSection.ProtoReflect.Descriptor instead.
func (*RefreshSignupSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{42}
}

func (x *RefreshSignupSection) GetChannelAuthId() []int32 {
	if x != nil {
		return x.ChannelAuthId
	}
	return nil
}

// 渠道签约状态刷新结果
type RefreshSignupResultSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道签约状态信息
	SignupStateItem []*SignupStateItem `protobuf:"bytes,1,rep,name=signup_state_item,json=signupStateItem,proto3" json:"signup_state_item,omitempty"`
}

func (x *RefreshSignupResultSection) Reset() {
	*x = RefreshSignupResultSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshSignupResultSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshSignupResultSection) ProtoMessage() {}

func (x *RefreshSignupResultSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshSignupResultSection.ProtoReflect.Descriptor instead.
func (*RefreshSignupResultSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{43}
}

func (x *RefreshSignupResultSection) GetSignupStateItem() []*SignupStateItem {
	if x != nil {
		return x.SignupStateItem
	}
	return nil
}

// 渠道签约状态信息
type SignupStateItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道签约授权id
	ChannelAuthId int32 `protobuf:"varint,1,opt,name=channel_auth_id,json=channelAuthId,proto3" json:"channel_auth_id,omitempty"`
	// （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
	SignupState string `protobuf:"bytes,2,opt,name=signup_state,json=signupState,proto3" json:"signup_state,omitempty"`
	// （可选）签约结果
	SignupResult string `protobuf:"bytes,3,opt,name=signup_result,json=signupResult,proto3" json:"signup_result,omitempty"`
}

func (x *SignupStateItem) Reset() {
	*x = SignupStateItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignupStateItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignupStateItem) ProtoMessage() {}

func (x *SignupStateItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignupStateItem.ProtoReflect.Descriptor instead.
func (*SignupStateItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{44}
}

func (x *SignupStateItem) GetChannelAuthId() int32 {
	if x != nil {
		return x.ChannelAuthId
	}
	return 0
}

func (x *SignupStateItem) GetSignupState() string {
	if x != nil {
		return x.SignupState
	}
	return ""
}

func (x *SignupStateItem) GetSignupResult() string {
	if x != nil {
		return x.SignupResult
	}
	return ""
}

// 所在地信息
type Location struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （可选）国家代码
	CountryCode string `protobuf:"bytes,1,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	// （可选）省份代码
	ProvinceCode string `protobuf:"bytes,2,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	// （可选）城市代码
	CityCode string `protobuf:"bytes,3,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	// （可选）行政区代码
	DistrictCode string `protobuf:"bytes,4,opt,name=district_code,json=districtCode,proto3" json:"district_code,omitempty"`
	// （可选）详细地址
	Address string `protobuf:"bytes,5,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *Location) Reset() {
	*x = Location{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{45}
}

func (x *Location) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *Location) GetProvinceCode() string {
	if x != nil {
		return x.ProvinceCode
	}
	return ""
}

func (x *Location) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *Location) GetDistrictCode() string {
	if x != nil {
		return x.DistrictCode
	}
	return ""
}

func (x *Location) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

// 联系人信息
type Contact struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （可选）联系人姓名
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// （可选）联系人Email
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	// （可选）联系人手机号
	CellphoneNumber string `protobuf:"bytes,3,opt,name=cellphone_number,json=cellphoneNumber,proto3" json:"cellphone_number,omitempty"`
}

func (x *Contact) Reset() {
	*x = Contact{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Contact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Contact) ProtoMessage() {}

func (x *Contact) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Contact.ProtoReflect.Descriptor instead.
func (*Contact) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{46}
}

func (x *Contact) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Contact) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *Contact) GetCellphoneNumber() string {
	if x != nil {
		return x.CellphoneNumber
	}
	return ""
}

// 附件信息
type Attachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// （必传）附件名称
	// 支付宝当面付待签约附件信息
	// 营业执照：BusinessLicensePic，店铺门头照片：ShopSignBoardPic，店铺内景照片：ShopScenePic
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// （可选）附件文件名（需携带文件后缀名）
	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"`
	// （必传）附件URL
	Url string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	// （可选）附件内容
	Content []byte `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
}

func (x *Attachment) Reset() {
	*x = Attachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Attachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Attachment) ProtoMessage() {}

func (x *Attachment) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Attachment.ProtoReflect.Descriptor instead.
func (*Attachment) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{47}
}

func (x *Attachment) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Attachment) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *Attachment) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Attachment) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

// 查询授权列表
type ListStoreAuthorizationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）门店ID列表
	StoreId []string `protobuf:"bytes,1,rep,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// （必传）渠道号
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （可选）取消授权的渠道业务
	GrantBusiness string `protobuf:"bytes,3,opt,name=grant_business,json=grantBusiness,proto3" json:"grant_business,omitempty"`
}

func (x *ListStoreAuthorizationSection) Reset() {
	*x = ListStoreAuthorizationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListStoreAuthorizationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListStoreAuthorizationSection) ProtoMessage() {}

func (x *ListStoreAuthorizationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListStoreAuthorizationSection.ProtoReflect.Descriptor instead.
func (*ListStoreAuthorizationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{48}
}

func (x *ListStoreAuthorizationSection) GetStoreId() []string {
	if x != nil {
		return x.StoreId
	}
	return nil
}

func (x *ListStoreAuthorizationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListStoreAuthorizationSection) GetGrantBusiness() string {
	if x != nil {
		return x.GrantBusiness
	}
	return ""
}

type StoreAuthorizationListSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthorizationItem []*StoreAuthorizationItem `protobuf:"bytes,1,rep,name=authorization_item,json=authorizationItem,proto3" json:"authorization_item,omitempty"`
}

func (x *StoreAuthorizationListSection) Reset() {
	*x = StoreAuthorizationListSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreAuthorizationListSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreAuthorizationListSection) ProtoMessage() {}

func (x *StoreAuthorizationListSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreAuthorizationListSection.ProtoReflect.Descriptor instead.
func (*StoreAuthorizationListSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{49}
}

func (x *StoreAuthorizationListSection) GetAuthorizationItem() []*StoreAuthorizationItem {
	if x != nil {
		return x.AuthorizationItem
	}
	return nil
}

type StoreAuthorizationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）门店ID
	StoreId string `protobuf:"bytes,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,2,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （可选）授权状态，WAITING 待授权；COMPLETE 已授权
	GrantState string `protobuf:"bytes,3,opt,name=grant_state,json=grantState,proto3" json:"grant_state,omitempty"`
	// （可选）第三方门店编号
	ThirdStoreId string `protobuf:"bytes,4,opt,name=third_store_id,json=thirdStoreId,proto3" json:"third_store_id,omitempty"`
	// （可选）第三方门店名称
	ThirdStoreName string `protobuf:"bytes,5,opt,name=third_store_name,json=thirdStoreName,proto3" json:"third_store_name,omitempty"`
	// （必传）授权token
	AuthToken string `protobuf:"bytes,6,opt,name=auth_token,json=authToken,proto3" json:"auth_token,omitempty"`
}

func (x *StoreAuthorizationItem) Reset() {
	*x = StoreAuthorizationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreAuthorizationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreAuthorizationItem) ProtoMessage() {}

func (x *StoreAuthorizationItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreAuthorizationItem.ProtoReflect.Descriptor instead.
func (*StoreAuthorizationItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{50}
}

func (x *StoreAuthorizationItem) GetStoreId() string {
	if x != nil {
		return x.StoreId
	}
	return ""
}

func (x *StoreAuthorizationItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *StoreAuthorizationItem) GetGrantState() string {
	if x != nil {
		return x.GrantState
	}
	return ""
}

func (x *StoreAuthorizationItem) GetThirdStoreId() string {
	if x != nil {
		return x.ThirdStoreId
	}
	return ""
}

func (x *StoreAuthorizationItem) GetThirdStoreName() string {
	if x != nil {
		return x.ThirdStoreName
	}
	return ""
}

func (x *StoreAuthorizationItem) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

// 渠道实体映射关系请求对象
type ChannelRelationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// （必传）业务操作
	// listRelation 查询渠道实体映射列表
	// produceRelationReport 查询渠道实体映射报表
	// createRelation 创建渠道实体映射关系（自动）
	// createCustomizedRelation 创建渠道实体映射关系（手动）
	// removeRelation 删除渠道实体映射关系
	Action string `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	// （可选）渠道实体映射关系列表查询请求对象
	ListRelationSection *ListRelationSection `protobuf:"bytes,2,opt,name=list_relation_section,json=listRelationSection,proto3" json:"list_relation_section,omitempty"`
	// （可选）渠道实体映射关系报表查询请求对象
	ProduceRelationReportSection *ProduceRelationReportSection `protobuf:"bytes,3,opt,name=produce_relation_report_section,json=produceRelationReportSection,proto3" json:"produce_relation_report_section,omitempty"`
	// （可选）渠道实体映射关系（自动）请求对象
	CreateRelationSection *CreateRelationSection `protobuf:"bytes,4,opt,name=create_relation_section,json=createRelationSection,proto3" json:"create_relation_section,omitempty"`
	// （可选）渠道实体映射关系（手动）请求对象
	CreateCustomizedRelationSection *CreateCustomizedRelationSection `protobuf:"bytes,5,opt,name=create_customized_relation_section,json=createCustomizedRelationSection,proto3" json:"create_customized_relation_section,omitempty"`
	// （可选）删除渠道实体映射关系请求对象
	RemoveRelationSection *RemoveRelationSection `protobuf:"bytes,6,opt,name=remove_relation_section,json=removeRelationSection,proto3" json:"remove_relation_section,omitempty"`
}

func (x *ChannelRelationRequest) Reset() {
	*x = ChannelRelationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelRelationRequest) ProtoMessage() {}

func (x *ChannelRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelRelationRequest.ProtoReflect.Descriptor instead.
func (*ChannelRelationRequest) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{51}
}

func (x *ChannelRelationRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *ChannelRelationRequest) GetListRelationSection() *ListRelationSection {
	if x != nil {
		return x.ListRelationSection
	}
	return nil
}

func (x *ChannelRelationRequest) GetProduceRelationReportSection() *ProduceRelationReportSection {
	if x != nil {
		return x.ProduceRelationReportSection
	}
	return nil
}

func (x *ChannelRelationRequest) GetCreateRelationSection() *CreateRelationSection {
	if x != nil {
		return x.CreateRelationSection
	}
	return nil
}

func (x *ChannelRelationRequest) GetCreateCustomizedRelationSection() *CreateCustomizedRelationSection {
	if x != nil {
		return x.CreateCustomizedRelationSection
	}
	return nil
}

func (x *ChannelRelationRequest) GetRemoveRelationSection() *RemoveRelationSection {
	if x != nil {
		return x.RemoveRelationSection
	}
	return nil
}

// 渠道实体映射关系应答对象
type ChannelRelationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 异常编码
	ErrorCode string `protobuf:"bytes,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	// 异常信息
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 是否有警告信息
	Warning bool `protobuf:"varint,3,opt,name=warning,proto3" json:"warning,omitempty"`
	// 警告信息
	WarningMessage string `protobuf:"bytes,4,opt,name=warning_message,json=warningMessage,proto3" json:"warning_message,omitempty"`
	// （可选）渠道实体映射关系列表信息
	RelationListSection *RelationListSection `protobuf:"bytes,5,opt,name=relation_list_section,json=relationListSection,proto3" json:"relation_list_section,omitempty"`
	// （可选）渠道实体映射关系报表信息
	RelationReportSection *RelationReportSection `protobuf:"bytes,6,opt,name=relation_report_section,json=relationReportSection,proto3" json:"relation_report_section,omitempty"`
	// （可选）渠道实体映射关系创建结果
	CreateRelationResultSection *CreateRelationResultSection `protobuf:"bytes,7,opt,name=create_relation_result_section,json=createRelationResultSection,proto3" json:"create_relation_result_section,omitempty"`
}

func (x *ChannelRelationResponse) Reset() {
	*x = ChannelRelationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChannelRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelRelationResponse) ProtoMessage() {}

func (x *ChannelRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelRelationResponse.ProtoReflect.Descriptor instead.
func (*ChannelRelationResponse) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{52}
}

func (x *ChannelRelationResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ChannelRelationResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ChannelRelationResponse) GetWarning() bool {
	if x != nil {
		return x.Warning
	}
	return false
}

func (x *ChannelRelationResponse) GetWarningMessage() string {
	if x != nil {
		return x.WarningMessage
	}
	return ""
}

func (x *ChannelRelationResponse) GetRelationListSection() *RelationListSection {
	if x != nil {
		return x.RelationListSection
	}
	return nil
}

func (x *ChannelRelationResponse) GetRelationReportSection() *RelationReportSection {
	if x != nil {
		return x.RelationReportSection
	}
	return nil
}

func (x *ChannelRelationResponse) GetCreateRelationResultSection() *CreateRelationResultSection {
	if x != nil {
		return x.CreateRelationResultSection
	}
	return nil
}

// 渠道实体映射关系查询请求对象
type ListRelationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （可选）合阔实体id
	HexEntityId string `protobuf:"bytes,3,opt,name=hex_entity_id,json=hexEntityId,proto3" json:"hex_entity_id,omitempty"`
	// （可选）第三方实体id
	TpEntityId string `protobuf:"bytes,4,opt,name=tp_entity_id,json=tpEntityId,proto3" json:"tp_entity_id,omitempty"`
	// （可选）授权id
	AuthId string `protobuf:"bytes,5,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
}

func (x *ListRelationSection) Reset() {
	*x = ListRelationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRelationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRelationSection) ProtoMessage() {}

func (x *ListRelationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRelationSection.ProtoReflect.Descriptor instead.
func (*ListRelationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{53}
}

func (x *ListRelationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ListRelationSection) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *ListRelationSection) GetHexEntityId() string {
	if x != nil {
		return x.HexEntityId
	}
	return ""
}

func (x *ListRelationSection) GetTpEntityId() string {
	if x != nil {
		return x.TpEntityId
	}
	return ""
}

func (x *ListRelationSection) GetAuthId() string {
	if x != nil {
		return x.AuthId
	}
	return ""
}

// 渠道实体映射关系报表请求对象
type ProduceRelationReportSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （可选）查询条件（门店代码或门店名称，支持模糊查询）
	Search string `protobuf:"bytes,3,opt,name=search,proto3" json:"search,omitempty"`
	// （可选）查询条件对应的字段名
	SearchFields string `protobuf:"bytes,4,opt,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"`
	// （必传）当前页码
	PageIndex int32 `protobuf:"varint,5,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// （必传）分页步长
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ProduceRelationReportSection) Reset() {
	*x = ProduceRelationReportSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProduceRelationReportSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProduceRelationReportSection) ProtoMessage() {}

func (x *ProduceRelationReportSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProduceRelationReportSection.ProtoReflect.Descriptor instead.
func (*ProduceRelationReportSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{54}
}

func (x *ProduceRelationReportSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *ProduceRelationReportSection) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *ProduceRelationReportSection) GetSearch() string {
	if x != nil {
		return x.Search
	}
	return ""
}

func (x *ProduceRelationReportSection) GetSearchFields() string {
	if x != nil {
		return x.SearchFields
	}
	return ""
}

func (x *ProduceRelationReportSection) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *ProduceRelationReportSection) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 创建渠道实体映射关系（自动）请求对象
type CreateRelationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
}

func (x *CreateRelationSection) Reset() {
	*x = CreateRelationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRelationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRelationSection) ProtoMessage() {}

func (x *CreateRelationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRelationSection.ProtoReflect.Descriptor instead.
func (*CreateRelationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{55}
}

func (x *CreateRelationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *CreateRelationSection) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

// 创建渠道实体映射关系（手动）请求对象
type CreateCustomizedRelationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （必传）渠道实体关系列表
	CreateRelationItem []*CreateRelationItem `protobuf:"bytes,3,rep,name=create_relation_item,json=createRelationItem,proto3" json:"create_relation_item,omitempty"`
}

func (x *CreateCustomizedRelationSection) Reset() {
	*x = CreateCustomizedRelationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateCustomizedRelationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomizedRelationSection) ProtoMessage() {}

func (x *CreateCustomizedRelationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomizedRelationSection.ProtoReflect.Descriptor instead.
func (*CreateCustomizedRelationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{56}
}

func (x *CreateCustomizedRelationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *CreateCustomizedRelationSection) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *CreateCustomizedRelationSection) GetCreateRelationItem() []*CreateRelationItem {
	if x != nil {
		return x.CreateRelationItem
	}
	return nil
}

// 渠道实体映射关系列表查询应答对象
type RelationListSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道实体映射关系列表
	RelationItem []*RelationItem `protobuf:"bytes,1,rep,name=relation_item,json=relationItem,proto3" json:"relation_item,omitempty"`
}

func (x *RelationListSection) Reset() {
	*x = RelationListSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationListSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationListSection) ProtoMessage() {}

func (x *RelationListSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationListSection.ProtoReflect.Descriptor instead.
func (*RelationListSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{57}
}

func (x *RelationListSection) GetRelationItem() []*RelationItem {
	if x != nil {
		return x.RelationItem
	}
	return nil
}

// 渠道实体映射关系报表查询应答对象
type RelationReportSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）当前页码
	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	// （必传）分页步长
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// （必传）总页数
	PageCount int32 `protobuf:"varint,3,opt,name=page_count,json=pageCount,proto3" json:"page_count,omitempty"`
	// （必传）总条数
	Total int64 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	// （必传）渠道实体映射关系报表
	RelationReportItem []*RelationReportItem `protobuf:"bytes,5,rep,name=relation_report_item,json=relationReportItem,proto3" json:"relation_report_item,omitempty"`
}

func (x *RelationReportSection) Reset() {
	*x = RelationReportSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationReportSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationReportSection) ProtoMessage() {}

func (x *RelationReportSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationReportSection.ProtoReflect.Descriptor instead.
func (*RelationReportSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{58}
}

func (x *RelationReportSection) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *RelationReportSection) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RelationReportSection) GetPageCount() int32 {
	if x != nil {
		return x.PageCount
	}
	return 0
}

func (x *RelationReportSection) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RelationReportSection) GetRelationReportItem() []*RelationReportItem {
	if x != nil {
		return x.RelationReportItem
	}
	return nil
}

// 渠道实体映射关系创建结果
type CreateRelationResultSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道实体映射关系列表
	RelationItem []*RelationItem `protobuf:"bytes,1,rep,name=relation_item,json=relationItem,proto3" json:"relation_item,omitempty"`
}

func (x *CreateRelationResultSection) Reset() {
	*x = CreateRelationResultSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRelationResultSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRelationResultSection) ProtoMessage() {}

func (x *CreateRelationResultSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRelationResultSection.ProtoReflect.Descriptor instead.
func (*CreateRelationResultSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{59}
}

func (x *CreateRelationResultSection) GetRelationItem() []*RelationItem {
	if x != nil {
		return x.RelationItem
	}
	return nil
}

// 渠道实体映射关系信息
type RelationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （必传）合阔实体id
	HexEntityId string `protobuf:"bytes,3,opt,name=hex_entity_id,json=hexEntityId,proto3" json:"hex_entity_id,omitempty"`
	// （必传）第三方实体id
	TpEntityId string `protobuf:"bytes,4,opt,name=tp_entity_id,json=tpEntityId,proto3" json:"tp_entity_id,omitempty"`
	// （必传）关系创建时间
	CreateTime string `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *RelationItem) Reset() {
	*x = RelationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationItem) ProtoMessage() {}

func (x *RelationItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationItem.ProtoReflect.Descriptor instead.
func (*RelationItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{60}
}

func (x *RelationItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *RelationItem) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *RelationItem) GetHexEntityId() string {
	if x != nil {
		return x.HexEntityId
	}
	return ""
}

func (x *RelationItem) GetTpEntityId() string {
	if x != nil {
		return x.TpEntityId
	}
	return ""
}

func (x *RelationItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 渠道实体映射关系报表信息
type RelationReportItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （必传）合阔实体id
	HexEntityId string `protobuf:"bytes,3,opt,name=hex_entity_id,json=hexEntityId,proto3" json:"hex_entity_id,omitempty"`
	// （可选）合阔实体名称
	HexEntityName string `protobuf:"bytes,4,opt,name=hex_entity_name,json=hexEntityName,proto3" json:"hex_entity_name,omitempty"`
	// （必传）第三方实体id
	TpEntityId string `protobuf:"bytes,5,opt,name=tp_entity_id,json=tpEntityId,proto3" json:"tp_entity_id,omitempty"`
	// （可选）第三方实体名称
	TpEntityName string `protobuf:"bytes,6,opt,name=tp_entity_name,json=tpEntityName,proto3" json:"tp_entity_name,omitempty"`
	// （必传）关系创建时间
	CreateTime string `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *RelationReportItem) Reset() {
	*x = RelationReportItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelationReportItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelationReportItem) ProtoMessage() {}

func (x *RelationReportItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelationReportItem.ProtoReflect.Descriptor instead.
func (*RelationReportItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{61}
}

func (x *RelationReportItem) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *RelationReportItem) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *RelationReportItem) GetHexEntityId() string {
	if x != nil {
		return x.HexEntityId
	}
	return ""
}

func (x *RelationReportItem) GetHexEntityName() string {
	if x != nil {
		return x.HexEntityName
	}
	return ""
}

func (x *RelationReportItem) GetTpEntityId() string {
	if x != nil {
		return x.TpEntityId
	}
	return ""
}

func (x *RelationReportItem) GetTpEntityName() string {
	if x != nil {
		return x.TpEntityName
	}
	return ""
}

func (x *RelationReportItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 渠道实体映射关系信息
type CreateRelationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）合阔实体id
	HexEntityId string `protobuf:"bytes,1,opt,name=hex_entity_id,json=hexEntityId,proto3" json:"hex_entity_id,omitempty"`
	// （必传）第三方实体id
	TpEntityId string `protobuf:"bytes,2,opt,name=tp_entity_id,json=tpEntityId,proto3" json:"tp_entity_id,omitempty"`
}

func (x *CreateRelationItem) Reset() {
	*x = CreateRelationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRelationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRelationItem) ProtoMessage() {}

func (x *CreateRelationItem) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRelationItem.ProtoReflect.Descriptor instead.
func (*CreateRelationItem) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{62}
}

func (x *CreateRelationItem) GetHexEntityId() string {
	if x != nil {
		return x.HexEntityId
	}
	return ""
}

func (x *CreateRelationItem) GetTpEntityId() string {
	if x != nil {
		return x.TpEntityId
	}
	return ""
}

// 删除渠道实体映射关系请求对象
type RemoveRelationSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）渠道代码
	ChannelCode string `protobuf:"bytes,1,opt,name=channel_code,json=channelCode,proto3" json:"channel_code,omitempty"`
	// （必传）映射类型，STORE 门店映射
	RelationType string `protobuf:"bytes,2,opt,name=relation_type,json=relationType,proto3" json:"relation_type,omitempty"`
	// （必传）合阔实体id
	HexEntityId string `protobuf:"bytes,3,opt,name=hex_entity_id,json=hexEntityId,proto3" json:"hex_entity_id,omitempty"`
	// （可选）授权ID
	AuthId string `protobuf:"bytes,4,opt,name=auth_id,json=authId,proto3" json:"auth_id,omitempty"`
}

func (x *RemoveRelationSection) Reset() {
	*x = RemoveRelationSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoveRelationSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRelationSection) ProtoMessage() {}

func (x *RemoveRelationSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRelationSection.ProtoReflect.Descriptor instead.
func (*RemoveRelationSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{63}
}

func (x *RemoveRelationSection) GetChannelCode() string {
	if x != nil {
		return x.ChannelCode
	}
	return ""
}

func (x *RemoveRelationSection) GetRelationType() string {
	if x != nil {
		return x.RelationType
	}
	return ""
}

func (x *RemoveRelationSection) GetHexEntityId() string {
	if x != nil {
		return x.HexEntityId
	}
	return ""
}

func (x *RemoveRelationSection) GetAuthId() string {
	if x != nil {
		return x.AuthId
	}
	return ""
}

// 短信验证码请求信息
type SendSMSCodeSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// *
	// （必传）短信验证码场景
	// 支付宝当面付ISV代签短信验证码：alipayISV_signup
	SmsScene string `protobuf:"bytes,1,opt,name=sms_scene,json=smsScene,proto3" json:"sms_scene,omitempty"`
	// （必传）手机号
	MobileNumber string `protobuf:"bytes,2,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
}

func (x *SendSMSCodeSection) Reset() {
	*x = SendSMSCodeSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendSMSCodeSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSMSCodeSection) ProtoMessage() {}

func (x *SendSMSCodeSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSMSCodeSection.ProtoReflect.Descriptor instead.
func (*SendSMSCodeSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{64}
}

func (x *SendSMSCodeSection) GetSmsScene() string {
	if x != nil {
		return x.SmsScene
	}
	return ""
}

func (x *SendSMSCodeSection) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

// 短信验证码响应信息
type SMSResultSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// （必传）短信id
	SmsId string `protobuf:"bytes,3,opt,name=sms_id,json=smsId,proto3" json:"sms_id,omitempty"`
}

func (x *SMSResultSection) Reset() {
	*x = SMSResultSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SMSResultSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SMSResultSection) ProtoMessage() {}

func (x *SMSResultSection) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_cam_ChannelManagement_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SMSResultSection.ProtoReflect.Descriptor instead.
func (*SMSResultSection) Descriptor() ([]byte, []int) {
	return file_grpc_cam_ChannelManagement_proto_rawDescGZIP(), []int{65}
}

func (x *SMSResultSection) GetSmsId() string {
	if x != nil {
		return x.SmsId
	}
	return ""
}

var File_grpc_cam_ChannelManagement_proto protoreflect.FileDescriptor

var file_grpc_cam_ChannelManagement_proto_rawDesc = []byte{
	0x0a, 0x20, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x61, 0x6d, 0x2f, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x02, 0x0a, 0x14, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x11, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52,
	0x0f, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x53, 0x0a, 0x16, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x12, 0x64, 0x65, 0x6c, 0x5f, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x6c, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x64, 0x65,
	0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa7,
	0x01, 0x0a, 0x15, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4a, 0x0a, 0x13,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x22, 0xc1, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x89, 0x01, 0x0a,
	0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x54, 0x65, 0x78, 0x74, 0x22, 0x48, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x0e,
	0x64, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x63, 0x68, 0x65, 0x4f, 0x6e,
	0x6c, 0x79, 0x22, 0x87, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4b,
	0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xad, 0x02, 0x0a,
	0x14, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a,
	0x11, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x0f, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x30, 0x0a, 0x0b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x30, 0x0a, 0x0b, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x65, 0x64, 0x69,
	0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x53, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x65,
	0x74, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x47, 0x0a, 0x0b,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x22, 0xa4, 0x01, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74,
	0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40,
	0x0a, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52,
	0x0f, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x4a, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x65,
	0x74, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x66, 0x0a, 0x11,
	0x41, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x2e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x70, 0x73,
	0x49, 0x74, 0x65, 0x6d, 0x22, 0xab, 0x02, 0x0a, 0x15, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x50, 0x0a, 0x15, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x13, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x4a, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74,
	0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x70,
	0x70, 0x6c, 0x65, 0x74, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x11, 0x61, 0x70, 0x70, 0x6c, 0x65, 0x74, 0x73, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x74,
	0x65, 0x6d, 0x22, 0xcf, 0x01, 0x0a, 0x13, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x47, 0x0a, 0x12, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x10, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x22, 0xf6, 0x01, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f,
	0x72, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74,
	0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x97, 0x05,
	0x0a, 0x06, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x12, 0x39, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x70, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x65, 0x72, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x65, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x75, 0x62, 0x6c, 0x69, 0x63, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x10, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x65, 0x72,
	0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xc1, 0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22, 0xa7, 0x01, 0x0a, 0x18,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x47, 0x0a, 0x12, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x10, 0x6c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2a, 0x0a, 0x07, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x07, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0x94, 0x01, 0x0a, 0x19, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52,
	0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb3, 0x01, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x30, 0x0a, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x22, 0x89, 0x03, 0x0a, 0x07, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12, 0x21,
	0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x4c, 0x6f, 0x67, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xf3,
	0x08, 0x0a, 0x1b, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x14, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x62,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x12, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x16, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5f, 0x0a, 0x1a, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x18, 0x6c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0e, 0x73,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x69,
	0x67, 0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x73, 0x69, 0x67,
	0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4d, 0x0a, 0x14, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x71, 0x75, 0x65, 0x72, 0x79, 0x53, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x53, 0x0a, 0x16, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x75,
	0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f,
	0x0a, 0x20, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x1d, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x5e, 0x0a, 0x1b, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x67, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x71, 0x0a, 0x22, 0x67, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x67, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x1a, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x17, 0x6c, 0x69, 0x73, 0x74, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4e, 0x0a, 0x15, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72,
	0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x61, 0x0a, 0x1c, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74,
	0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x18, 0x67, 0x65, 0x74, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb4, 0x06, 0x0a, 0x1c, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x4d, 0x0a, 0x14, 0x62, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5f, 0x0a, 0x1a, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x18, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x15, 0x73, 0x69, 0x67,
	0x6e, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x15, 0x73,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x66, 0x0a,
	0x1d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1a, 0x72, 0x65, 0x66, 0x72, 0x65,
	0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x11, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74,
	0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x6f, 0x0a, 0x20, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x1a,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x72, 0x61, 0x6e, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x17, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x13, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x15, 0x73, 0x65,
	0x6e, 0x64, 0x5f, 0x73, 0x6d, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x4d, 0x53, 0x43, 0x6f, 0x64, 0x65, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x12, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x6d, 0x73, 0x43,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01, 0x0a, 0x14, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x12, 0x73, 0x6d, 0x73, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x4d,
	0x53, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10,
	0x73, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xa5, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x4d, 0x0a, 0x12, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x37,
	0x0a, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0b, 0x62, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x22, 0xb9, 0x02, 0x0a, 0x0b, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x30, 0x0a, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73, 0x75, 0x62, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x75, 0x62, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x22, 0x78, 0x0a, 0x14, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xea, 0x01,
	0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x78, 0x5f, 0x63, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f,
	0x6d, 0x70, 0x6c, 0x65, 0x78, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xd6, 0x01, 0x0a, 0x18, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x49, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x68,
	0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x22, 0xd1, 0x01, 0x0a, 0x11, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x75, 0x74,
	0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x75,
	0x70, 0x4e, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x75,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49,
	0x64, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e,
	0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x22, 0x9a, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x67, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x86, 0x01,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12,
	0x25, 0x0a, 0x0e, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x22, 0x7a, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x67,
	0x72, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x22, 0x58, 0x0a, 0x0f, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x55,
	0x72, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x5f, 0x67, 0x72, 0x61,
	0x6e, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x55, 0x72, 0x6c, 0x22, 0x7a, 0x0a, 0x17,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72,
	0x61, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x6b, 0x0a, 0x17, 0x47, 0x72, 0x61, 0x6e,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x15, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x47, 0x72, 0x61,
	0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x13, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8f, 0x01, 0x0a, 0x13, 0x47, 0x72, 0x61, 0x6e, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x75, 0x74, 0x68, 0x49, 0x64, 0x22, 0x91, 0x03, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65,
	0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0e,
	0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6d, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6d, 0x73, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x6d, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x6d, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x7a, 0x0a, 0x13, 0x53,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x6e, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x4e, 0x6f, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3c, 0x0a, 0x12, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a,
	0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x75, 0x74, 0x68, 0x49, 0x64, 0x22, 0xc2, 0x02, 0x0a, 0x13, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a,
	0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x11, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a,
	0x0e, 0x70, 0x61, 0x72, 0x74, 0x79, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x74, 0x79, 0x4c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x3e, 0x0a, 0x14, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x1a, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x11, 0x73, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x69,
	0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x73,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x81,
	0x01, 0x0a, 0x0f, 0x53, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x69,
	0x67, 0x6e, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xae, 0x01, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x76, 0x69,
	0x6e, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x5e, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x65, 0x6c, 0x6c,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x63, 0x65, 0x6c, 0x6c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x68, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x84, 0x01,
	0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x42, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x22, 0x6f, 0x0a, 0x1d, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x75, 0x74,
	0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x11, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x22, 0xe6, 0x01, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41,
	0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x67, 0x72, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x68, 0x69, 0x72, 0x64, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x74, 0x68, 0x69, 0x72, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x97,
	0x04, 0x0a, 0x16, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x50, 0x0a, 0x15, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x6c, 0x0a, 0x1f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x5f, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x1c, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x56, 0x0a, 0x17, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x15, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x75, 0x0a, 0x22, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x1f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65,
	0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x56, 0x0a, 0x17, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x15, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb5, 0x03, 0x0a, 0x17, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x61, 0x72, 0x6e,
	0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x77, 0x61, 0x72, 0x6e, 0x69,
	0x6e, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x61, 0x72,
	0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x50, 0x0a, 0x15, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x13, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x56, 0x0a,
	0x17, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x69, 0x0a, 0x1e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0xbc, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x22, 0x0a, 0x0d, 0x68, 0x65, 0x78, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x65, 0x78, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x70, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x70, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75, 0x74, 0x68, 0x49, 0x64, 0x22,
	0xdf, 0x01, 0x0a, 0x1c, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x5f, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4d,
	0x0a, 0x14, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x51, 0x0a,
	0x13, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x22, 0xd7, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x4d, 0x0a, 0x14, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x12, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x22, 0x59, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x74, 0x65, 0x6d, 0x22, 0xbd, 0x01, 0x0a, 0x0c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22,
	0x0a, 0x0d, 0x68, 0x65, 0x78, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x65, 0x78, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x70, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x70, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x91, 0x02, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x68, 0x65, 0x78, 0x5f, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x65, 0x78,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x68, 0x65, 0x78, 0x5f,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x68, 0x65, 0x78, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0c, 0x74, 0x70, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x70, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x70, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x70, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x5a, 0x0a, 0x12, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x22, 0x0a, 0x0d, 0x68, 0x65, 0x78, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x65, 0x78, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x74, 0x70, 0x5f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x70, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x9c, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x68, 0x65, 0x78, 0x5f, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x68, 0x65, 0x78, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x75,
	0x74, 0x68, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x4d, 0x53, 0x43,
	0x6f, 0x64, 0x65, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6d,
	0x73, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x6d, 0x73, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x29, 0x0a, 0x10,
	0x53, 0x4d, 0x53, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x6d, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x6d, 0x73, 0x49, 0x64, 0x32, 0x8d, 0x06, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x8b, 0x01,
	0x0a, 0x14, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x75,
	0x74, 0x68, 0x6f, 0x72, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x64, 0x6f, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x11, 0x63,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x21, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x22,
	0x16, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x2f, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x64, 0x6f, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x1d, 0x2e, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x22, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x2f,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x2f, 0x64,
	0x6f, 0x3a, 0x01, 0x2a, 0x12, 0x78, 0x0a, 0x0d, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x2f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2f, 0x64, 0x6f, 0x3a, 0x01, 0x2a, 0x12, 0x80,
	0x01, 0x0a, 0x0f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x61, 0x6d, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e,
	0x65, 0x6c, 0x2f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x64, 0x6f, 0x3a, 0x01,
	0x2a, 0x12, 0x74, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x72,
	0x61, 0x12, 0x1c, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e,
	0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x49, 0x6e, 0x66, 0x72, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x27,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x61, 0x6d, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x66, 0x72,
	0x61, 0x2f, 0x64, 0x6f, 0x3a, 0x01, 0x2a, 0x42, 0x44, 0x0a, 0x30, 0x63, 0x6e, 0x2e, 0x68, 0x65,
	0x78, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x63, 0x61, 0x6d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x66, 0x61, 0x63, 0x61, 0x64, 0x65,
	0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x01, 0x5a, 0x0e, 0x2e,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x63, 0x61, 0x6d, 0x3b, 0x63, 0x61, 0x6d, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpc_cam_ChannelManagement_proto_rawDescOnce sync.Once
	file_grpc_cam_ChannelManagement_proto_rawDescData = file_grpc_cam_ChannelManagement_proto_rawDesc
)

func file_grpc_cam_ChannelManagement_proto_rawDescGZIP() []byte {
	file_grpc_cam_ChannelManagement_proto_rawDescOnce.Do(func() {
		file_grpc_cam_ChannelManagement_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpc_cam_ChannelManagement_proto_rawDescData)
	})
	return file_grpc_cam_ChannelManagement_proto_rawDescData
}

var file_grpc_cam_ChannelManagement_proto_msgTypes = make([]protoimpl.MessageInfo, 66)
var file_grpc_cam_ChannelManagement_proto_goTypes = []interface{}{
	(*ChannelScriptRequest)(nil),            // 0: channel.ChannelScriptRequest
	(*ChannelScriptResponse)(nil),           // 1: channel.ChannelScriptResponse
	(*ChannelScriptItem)(nil),               // 2: channel.ChannelScriptItem
	(*ChannelScriptSection)(nil),            // 3: channel.ChannelScriptSection
	(*DelScriptSection)(nil),                // 4: channel.DelScriptSection
	(*ListScriptQuery)(nil),                 // 5: channel.ListScriptQuery
	(*ChannelAccessRequest)(nil),            // 6: channel.ChannelAccessRequest
	(*ApplyTarget)(nil),                     // 7: channel.ApplyTarget
	(*AppletsAccessSection)(nil),            // 8: channel.AppletsAccessSection
	(*AppletsAccessItem)(nil),               // 9: channel.AppletsAccessItem
	(*ChannelAccessResponse)(nil),           // 10: channel.ChannelAccessResponse
	(*AccessReportSection)(nil),             // 11: channel.AccessReportSection
	(*AccessReportItem)(nil),                // 12: channel.AccessReportItem
	(*Access)(nil),                          // 13: channel.Access
	(*ListAccessQuery)(nil),                 // 14: channel.ListAccessQuery
	(*ChannelManagementRequest)(nil),        // 15: channel.ChannelManagementRequest
	(*ChannelManagementResponse)(nil),       // 16: channel.ChannelManagementResponse
	(*ListChannelQuery)(nil),                // 17: channel.ListChannelQuery
	(*Channel)(nil),                         // 18: channel.Channel
	(*ChannelAuthorizationRequest)(nil),     // 19: channel.ChannelAuthorizationRequest
	(*ChannelAuthorizationResponse)(nil),    // 20: channel.ChannelAuthorizationResponse
	(*ChannelInfraRequest)(nil),             // 21: channel.ChannelInfraRequest
	(*ChannelInfraResponse)(nil),            // 22: channel.ChannelInfraResponse
	(*ListBindingSection)(nil),              // 23: channel.ListBindingSection
	(*BindingListSection)(nil),              // 24: channel.BindingListSection
	(*BindingItem)(nil),                     // 25: channel.BindingItem
	(*SwitchBindingSection)(nil),            // 26: channel.SwitchBindingSection
	(*ListAuthorizationSection)(nil),        // 27: channel.ListAuthorizationSection
	(*AuthorizationListSection)(nil),        // 28: channel.AuthorizationListSection
	(*AuthorizationItem)(nil),               // 29: channel.AuthorizationItem
	(*GetStoreGrantUrlSection)(nil),         // 30: channel.GetStoreGrantUrlSection
	(*GetStoreCancelGrantUrlSection)(nil),   // 31: channel.GetStoreCancelGrantUrlSection
	(*GetGrantUrlSection)(nil),              // 32: channel.GetGrantUrlSection
	(*GetCancelGrantUrlSection)(nil),        // 33: channel.GetCancelGrantUrlSection
	(*GrantUrlSection)(nil),                 // 34: channel.GrantUrlSection
	(*ListGrantAccountSection)(nil),         // 35: channel.ListGrantAccountSection
	(*GrantAccountListSection)(nil),         // 36: channel.GrantAccountListSection
	(*GrantAccountSection)(nil),             // 37: channel.GrantAccountSection
	(*SignupSection)(nil),                   // 38: channel.SignupSection
	(*SignupResultSection)(nil),             // 39: channel.SignupResultSection
	(*QuerySignupSection)(nil),              // 40: channel.QuerySignupSection
	(*SignupDetailSection)(nil),             // 41: channel.SignupDetailSection
	(*RefreshSignupSection)(nil),            // 42: channel.RefreshSignupSection
	(*RefreshSignupResultSection)(nil),      // 43: channel.RefreshSignupResultSection
	(*SignupStateItem)(nil),                 // 44: channel.SignupStateItem
	(*Location)(nil),                        // 45: channel.Location
	(*Contact)(nil),                         // 46: channel.Contact
	(*Attachment)(nil),                      // 47: channel.Attachment
	(*ListStoreAuthorizationSection)(nil),   // 48: channel.ListStoreAuthorizationSection
	(*StoreAuthorizationListSection)(nil),   // 49: channel.StoreAuthorizationListSection
	(*StoreAuthorizationItem)(nil),          // 50: channel.StoreAuthorizationItem
	(*ChannelRelationRequest)(nil),          // 51: channel.ChannelRelationRequest
	(*ChannelRelationResponse)(nil),         // 52: channel.ChannelRelationResponse
	(*ListRelationSection)(nil),             // 53: channel.ListRelationSection
	(*ProduceRelationReportSection)(nil),    // 54: channel.ProduceRelationReportSection
	(*CreateRelationSection)(nil),           // 55: channel.CreateRelationSection
	(*CreateCustomizedRelationSection)(nil), // 56: channel.CreateCustomizedRelationSection
	(*RelationListSection)(nil),             // 57: channel.RelationListSection
	(*RelationReportSection)(nil),           // 58: channel.RelationReportSection
	(*CreateRelationResultSection)(nil),     // 59: channel.CreateRelationResultSection
	(*RelationItem)(nil),                    // 60: channel.RelationItem
	(*RelationReportItem)(nil),              // 61: channel.RelationReportItem
	(*CreateRelationItem)(nil),              // 62: channel.CreateRelationItem
	(*RemoveRelationSection)(nil),           // 63: channel.RemoveRelationSection
	(*SendSMSCodeSection)(nil),              // 64: channel.SendSMSCodeSection
	(*SMSResultSection)(nil),                // 65: channel.SMSResultSection
}
var file_grpc_cam_ChannelManagement_proto_depIdxs = []int32{
	5,  // 0: channel.ChannelScriptRequest.list_script_query:type_name -> channel.ListScriptQuery
	3,  // 1: channel.ChannelScriptRequest.channel_script_section:type_name -> channel.ChannelScriptSection
	4,  // 2: channel.ChannelScriptRequest.del_script_section:type_name -> channel.DelScriptSection
	2,  // 3: channel.ChannelScriptResponse.channel_script_item:type_name -> channel.ChannelScriptItem
	14, // 4: channel.ChannelAccessRequest.list_access_query:type_name -> channel.ListAccessQuery
	13, // 5: channel.ChannelAccessRequest.list_access:type_name -> channel.Access
	13, // 6: channel.ChannelAccessRequest.edit_access:type_name -> channel.Access
	8,  // 7: channel.ChannelAccessRequest.applets_access_section:type_name -> channel.AppletsAccessSection
	7,  // 8: channel.AppletsAccessSection.apply_target_item:type_name -> channel.ApplyTarget
	9,  // 9: channel.AppletsAccessSection.applets_access_item:type_name -> channel.AppletsAccessItem
	13, // 10: channel.AppletsAccessItem.props_item:type_name -> channel.Access
	11, // 11: channel.ChannelAccessResponse.access_report_section:type_name -> channel.AccessReportSection
	13, // 12: channel.ChannelAccessResponse.access_item:type_name -> channel.Access
	9,  // 13: channel.ChannelAccessResponse.applets_access_item:type_name -> channel.AppletsAccessItem
	12, // 14: channel.AccessReportSection.access_report_item:type_name -> channel.AccessReportItem
	7,  // 15: channel.Access.apply_targets:type_name -> channel.ApplyTarget
	17, // 16: channel.ChannelManagementRequest.list_channel_query:type_name -> channel.ListChannelQuery
	18, // 17: channel.ChannelManagementRequest.channel:type_name -> channel.Channel
	18, // 18: channel.ChannelManagementResponse.channel_list:type_name -> channel.Channel
	23, // 19: channel.ChannelAuthorizationRequest.list_binding_section:type_name -> channel.ListBindingSection
	26, // 20: channel.ChannelAuthorizationRequest.switch_binding_section:type_name -> channel.SwitchBindingSection
	27, // 21: channel.ChannelAuthorizationRequest.list_authorization_section:type_name -> channel.ListAuthorizationSection
	38, // 22: channel.ChannelAuthorizationRequest.signup_section:type_name -> channel.SignupSection
	40, // 23: channel.ChannelAuthorizationRequest.query_signup_section:type_name -> channel.QuerySignupSection
	42, // 24: channel.ChannelAuthorizationRequest.refresh_signup_section:type_name -> channel.RefreshSignupSection
	48, // 25: channel.ChannelAuthorizationRequest.list_store_authorization_section:type_name -> channel.ListStoreAuthorizationSection
	30, // 26: channel.ChannelAuthorizationRequest.get_store_grant_url_section:type_name -> channel.GetStoreGrantUrlSection
	31, // 27: channel.ChannelAuthorizationRequest.get_store_cancel_grant_url_section:type_name -> channel.GetStoreCancelGrantUrlSection
	35, // 28: channel.ChannelAuthorizationRequest.list_grant_account_section:type_name -> channel.ListGrantAccountSection
	32, // 29: channel.ChannelAuthorizationRequest.get_grant_url_section:type_name -> channel.GetGrantUrlSection
	33, // 30: channel.ChannelAuthorizationRequest.get_cancel_grant_url_section:type_name -> channel.GetCancelGrantUrlSection
	24, // 31: channel.ChannelAuthorizationResponse.binding_list_section:type_name -> channel.BindingListSection
	28, // 32: channel.ChannelAuthorizationResponse.authorization_list_section:type_name -> channel.AuthorizationListSection
	39, // 33: channel.ChannelAuthorizationResponse.signup_result_section:type_name -> channel.SignupResultSection
	41, // 34: channel.ChannelAuthorizationResponse.signup_detail_section:type_name -> channel.SignupDetailSection
	43, // 35: channel.ChannelAuthorizationResponse.refresh_signup_result_section:type_name -> channel.RefreshSignupResultSection
	34, // 36: channel.ChannelAuthorizationResponse.grant_url_section:type_name -> channel.GrantUrlSection
	49, // 37: channel.ChannelAuthorizationResponse.store_authorization_list_section:type_name -> channel.StoreAuthorizationListSection
	36, // 38: channel.ChannelAuthorizationResponse.grant_account_list_section:type_name -> channel.GrantAccountListSection
	64, // 39: channel.ChannelInfraRequest.send_sms_code_section:type_name -> channel.SendSMSCodeSection
	65, // 40: channel.ChannelInfraResponse.sms_result_section:type_name -> channel.SMSResultSection
	25, // 41: channel.BindingListSection.binding_item:type_name -> channel.BindingItem
	29, // 42: channel.AuthorizationListSection.authorization_item:type_name -> channel.AuthorizationItem
	37, // 43: channel.GrantAccountListSection.grant_account_section:type_name -> channel.GrantAccountSection
	45, // 44: channel.SignupSection.party_location:type_name -> channel.Location
	46, // 45: channel.SignupSection.contact:type_name -> channel.Contact
	47, // 46: channel.SignupSection.attachments:type_name -> channel.Attachment
	45, // 47: channel.SignupDetailSection.party_location:type_name -> channel.Location
	46, // 48: channel.SignupDetailSection.contact:type_name -> channel.Contact
	47, // 49: channel.SignupDetailSection.attachments:type_name -> channel.Attachment
	44, // 50: channel.RefreshSignupResultSection.signup_state_item:type_name -> channel.SignupStateItem
	50, // 51: channel.StoreAuthorizationListSection.authorization_item:type_name -> channel.StoreAuthorizationItem
	53, // 52: channel.ChannelRelationRequest.list_relation_section:type_name -> channel.ListRelationSection
	54, // 53: channel.ChannelRelationRequest.produce_relation_report_section:type_name -> channel.ProduceRelationReportSection
	55, // 54: channel.ChannelRelationRequest.create_relation_section:type_name -> channel.CreateRelationSection
	56, // 55: channel.ChannelRelationRequest.create_customized_relation_section:type_name -> channel.CreateCustomizedRelationSection
	63, // 56: channel.ChannelRelationRequest.remove_relation_section:type_name -> channel.RemoveRelationSection
	57, // 57: channel.ChannelRelationResponse.relation_list_section:type_name -> channel.RelationListSection
	58, // 58: channel.ChannelRelationResponse.relation_report_section:type_name -> channel.RelationReportSection
	59, // 59: channel.ChannelRelationResponse.create_relation_result_section:type_name -> channel.CreateRelationResultSection
	62, // 60: channel.CreateCustomizedRelationSection.create_relation_item:type_name -> channel.CreateRelationItem
	60, // 61: channel.RelationListSection.relation_item:type_name -> channel.RelationItem
	61, // 62: channel.RelationReportSection.relation_report_item:type_name -> channel.RelationReportItem
	60, // 63: channel.CreateRelationResultSection.relation_item:type_name -> channel.RelationItem
	19, // 64: channel.ChannelManagement.channelAuthorization:input_type -> channel.ChannelAuthorizationRequest
	15, // 65: channel.ChannelManagement.channelManagement:input_type -> channel.ChannelManagementRequest
	0,  // 66: channel.ChannelManagement.channelScript:input_type -> channel.ChannelScriptRequest
	6,  // 67: channel.ChannelManagement.channelAccess:input_type -> channel.ChannelAccessRequest
	51, // 68: channel.ChannelManagement.channelRelation:input_type -> channel.ChannelRelationRequest
	21, // 69: channel.ChannelManagement.channelInfra:input_type -> channel.ChannelInfraRequest
	20, // 70: channel.ChannelManagement.channelAuthorization:output_type -> channel.ChannelAuthorizationResponse
	16, // 71: channel.ChannelManagement.channelManagement:output_type -> channel.ChannelManagementResponse
	1,  // 72: channel.ChannelManagement.channelScript:output_type -> channel.ChannelScriptResponse
	10, // 73: channel.ChannelManagement.channelAccess:output_type -> channel.ChannelAccessResponse
	52, // 74: channel.ChannelManagement.channelRelation:output_type -> channel.ChannelRelationResponse
	22, // 75: channel.ChannelManagement.channelInfra:output_type -> channel.ChannelInfraResponse
	70, // [70:76] is the sub-list for method output_type
	64, // [64:70] is the sub-list for method input_type
	64, // [64:64] is the sub-list for extension type_name
	64, // [64:64] is the sub-list for extension extendee
	0,  // [0:64] is the sub-list for field type_name
}

func init() { file_grpc_cam_ChannelManagement_proto_init() }
func file_grpc_cam_ChannelManagement_proto_init() {
	if File_grpc_cam_ChannelManagement_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpc_cam_ChannelManagement_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelScriptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelScriptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelScriptItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelScriptSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelScriptSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListScriptQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccessRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyTarget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppletsAccessSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppletsAccessItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAccessResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessReportSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccessReportItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Access); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAccessQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelManagementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelManagementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListChannelQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Channel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAuthorizationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelAuthorizationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelInfraRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelInfraResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBindingSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingListSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchBindingSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListAuthorizationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorizationListSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthorizationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStoreGrantUrlSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStoreCancelGrantUrlSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGrantUrlSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCancelGrantUrlSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantUrlSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListGrantAccountSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantAccountListSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrantAccountSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignupSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignupResultSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySignupSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignupDetailSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshSignupSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshSignupResultSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignupStateItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Location); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Contact); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Attachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListStoreAuthorizationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreAuthorizationListSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreAuthorizationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelRelationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChannelRelationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRelationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProduceRelationReportSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRelationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateCustomizedRelationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationListSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationReportSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRelationResultSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelationReportItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRelationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoveRelationSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendSMSCodeSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_grpc_cam_ChannelManagement_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SMSResultSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpc_cam_ChannelManagement_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   66,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_cam_ChannelManagement_proto_goTypes,
		DependencyIndexes: file_grpc_cam_ChannelManagement_proto_depIdxs,
		MessageInfos:      file_grpc_cam_ChannelManagement_proto_msgTypes,
	}.Build()
	File_grpc_cam_ChannelManagement_proto = out.File
	file_grpc_cam_ChannelManagement_proto_rawDesc = nil
	file_grpc_cam_ChannelManagement_proto_goTypes = nil
	file_grpc_cam_ChannelManagement_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ChannelManagementClient is the client API for ChannelManagement service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelManagementClient interface {
	// 渠道签约授权管理接口
	ChannelAuthorization(ctx context.Context, in *ChannelAuthorizationRequest, opts ...grpc.CallOption) (*ChannelAuthorizationResponse, error)
	// 渠道管理接口
	ChannelManagement(ctx context.Context, in *ChannelManagementRequest, opts ...grpc.CallOption) (*ChannelManagementResponse, error)
	// 渠道脚本接口
	ChannelScript(ctx context.Context, in *ChannelScriptRequest, opts ...grpc.CallOption) (*ChannelScriptResponse, error)
	// 渠道签约授权管理接口
	ChannelAccess(ctx context.Context, in *ChannelAccessRequest, opts ...grpc.CallOption) (*ChannelAccessResponse, error)
	// 渠道实体映射关系管理接口
	ChannelRelation(ctx context.Context, in *ChannelRelationRequest, opts ...grpc.CallOption) (*ChannelRelationResponse, error)
	// 短信验证码接口
	ChannelInfra(ctx context.Context, in *ChannelInfraRequest, opts ...grpc.CallOption) (*ChannelInfraResponse, error)
}

type channelManagementClient struct {
	cc grpc.ClientConnInterface
}

func NewChannelManagementClient(cc grpc.ClientConnInterface) ChannelManagementClient {
	return &channelManagementClient{cc}
}

func (c *channelManagementClient) ChannelAuthorization(ctx context.Context, in *ChannelAuthorizationRequest, opts ...grpc.CallOption) (*ChannelAuthorizationResponse, error) {
	out := new(ChannelAuthorizationResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelAuthorization", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelManagementClient) ChannelManagement(ctx context.Context, in *ChannelManagementRequest, opts ...grpc.CallOption) (*ChannelManagementResponse, error) {
	out := new(ChannelManagementResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelManagement", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelManagementClient) ChannelScript(ctx context.Context, in *ChannelScriptRequest, opts ...grpc.CallOption) (*ChannelScriptResponse, error) {
	out := new(ChannelScriptResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelScript", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelManagementClient) ChannelAccess(ctx context.Context, in *ChannelAccessRequest, opts ...grpc.CallOption) (*ChannelAccessResponse, error) {
	out := new(ChannelAccessResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelAccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelManagementClient) ChannelRelation(ctx context.Context, in *ChannelRelationRequest, opts ...grpc.CallOption) (*ChannelRelationResponse, error) {
	out := new(ChannelRelationResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelManagementClient) ChannelInfra(ctx context.Context, in *ChannelInfraRequest, opts ...grpc.CallOption) (*ChannelInfraResponse, error) {
	out := new(ChannelInfraResponse)
	err := c.cc.Invoke(ctx, "/channel.ChannelManagement/channelInfra", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelManagementServer is the server API for ChannelManagement service.
type ChannelManagementServer interface {
	// 渠道签约授权管理接口
	ChannelAuthorization(context.Context, *ChannelAuthorizationRequest) (*ChannelAuthorizationResponse, error)
	// 渠道管理接口
	ChannelManagement(context.Context, *ChannelManagementRequest) (*ChannelManagementResponse, error)
	// 渠道脚本接口
	ChannelScript(context.Context, *ChannelScriptRequest) (*ChannelScriptResponse, error)
	// 渠道签约授权管理接口
	ChannelAccess(context.Context, *ChannelAccessRequest) (*ChannelAccessResponse, error)
	// 渠道实体映射关系管理接口
	ChannelRelation(context.Context, *ChannelRelationRequest) (*ChannelRelationResponse, error)
	// 短信验证码接口
	ChannelInfra(context.Context, *ChannelInfraRequest) (*ChannelInfraResponse, error)
}

// UnimplementedChannelManagementServer can be embedded to have forward compatible implementations.
type UnimplementedChannelManagementServer struct {
}

func (*UnimplementedChannelManagementServer) ChannelAuthorization(context.Context, *ChannelAuthorizationRequest) (*ChannelAuthorizationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelAuthorization not implemented")
}
func (*UnimplementedChannelManagementServer) ChannelManagement(context.Context, *ChannelManagementRequest) (*ChannelManagementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelManagement not implemented")
}
func (*UnimplementedChannelManagementServer) ChannelScript(context.Context, *ChannelScriptRequest) (*ChannelScriptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelScript not implemented")
}
func (*UnimplementedChannelManagementServer) ChannelAccess(context.Context, *ChannelAccessRequest) (*ChannelAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelAccess not implemented")
}
func (*UnimplementedChannelManagementServer) ChannelRelation(context.Context, *ChannelRelationRequest) (*ChannelRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelRelation not implemented")
}
func (*UnimplementedChannelManagementServer) ChannelInfra(context.Context, *ChannelInfraRequest) (*ChannelInfraResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelInfra not implemented")
}

func RegisterChannelManagementServer(s *grpc.Server, srv ChannelManagementServer) {
	s.RegisterService(&_ChannelManagement_serviceDesc, srv)
}

func _ChannelManagement_ChannelAuthorization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelAuthorizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelAuthorization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelAuthorization",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelAuthorization(ctx, req.(*ChannelAuthorizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelManagement_ChannelManagement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelManagementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelManagement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelManagement",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelManagement(ctx, req.(*ChannelManagementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelManagement_ChannelScript_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelScriptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelScript(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelScript",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelScript(ctx, req.(*ChannelScriptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelManagement_ChannelAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelAccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelAccess(ctx, req.(*ChannelAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelManagement_ChannelRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelRelation(ctx, req.(*ChannelRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelManagement_ChannelInfra_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelInfraRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelManagementServer).ChannelInfra(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel.ChannelManagement/ChannelInfra",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelManagementServer).ChannelInfra(ctx, req.(*ChannelInfraRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelManagement_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel.ChannelManagement",
	HandlerType: (*ChannelManagementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "channelAuthorization",
			Handler:    _ChannelManagement_ChannelAuthorization_Handler,
		},
		{
			MethodName: "channelManagement",
			Handler:    _ChannelManagement_ChannelManagement_Handler,
		},
		{
			MethodName: "channelScript",
			Handler:    _ChannelManagement_ChannelScript_Handler,
		},
		{
			MethodName: "channelAccess",
			Handler:    _ChannelManagement_ChannelAccess_Handler,
		},
		{
			MethodName: "channelRelation",
			Handler:    _ChannelManagement_ChannelRelation_Handler,
		},
		{
			MethodName: "channelInfra",
			Handler:    _ChannelManagement_ChannelInfra_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "grpc/cam/ChannelManagement.proto",
}
