package http

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"hexcloud.cn/histore/hexerror"
	"io/ioutil"
)

func addAdjust(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	logger.Pre().Infof("addAdjust: %s\n", string(bodys))
	// 先存储起来
	id, e := services.SaveAdjustInfo(c, string(bodys))
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]string{"id": id}, nil
}

func rollbackAdjust(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	logger.Pre().Infof("rollbackAdjust: %s\n", string(bodys))
	var rb struct {
		Id string `json:"id"`
	}
	json.Unmarshal(bodys, &rb)
	// 先存储起来
	e := services.RollbackAdjust(c, rb.Id)
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]string{"id": rb.Id}, nil
}
