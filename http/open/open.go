package open

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	mopen "gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/open"
	"hexcloud.cn/histore/hexerror"
)

// 汇总表
func OpenFinancialReport(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req mopen.FinancialReportReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.report.OpenFinancialReport] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	resp, err := open.QueryOpenFinancialReport(c, &req)
	return resp, utils.WrapError(err)
}

// 商品
func OpenProductReport(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req mopen.ProductReportReq
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.report.OpenProductReport] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	resp, err := open.QueryOpenProductReport(c, &req)
	return resp, utils.WrapError(err)
}
