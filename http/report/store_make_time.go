package report

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"hexcloud.cn/histore/hexerror"
)

// 订单完成时间
func StoreMakeTime(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req model.CommonRequest
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.report.StoreMakeTime] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}

	resp, err := report.QueryStoreMakeTime(c, &req)
	return resp, utils.WrapError(err)
}
