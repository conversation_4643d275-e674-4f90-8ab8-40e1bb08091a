package http

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"hexcloud.cn/histore/hexerror"
	"io/ioutil"
)

// 商品入库
func inbound(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	logger.Pre().Infof("inbound: %s\n", string(bodys))
	// 先存储起来
	id, e := services.SaveInboundInfo(c, string(bodys))
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]string{"id": id}, nil
}
