package http

import (
	"encoding/json"
	"io/ioutil"

	"github.com/gin-gonic/gin"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"hexcloud.cn/histore/hexerror"
)

// 添加电子小票进来
func addTicket(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	// 先存储起来
	id, e := services.SaveSalesInfo(c, string(bodys))
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]string{"id": id}, nil
}

// 添加异常电子小票进来
func addAbnormalTicket(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	// 先存储起来
	id, e := services.SaveAbnormalSalesInfo(c, string(bodys))
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]string{"id": id}, nil
}


func reInitTicket(c *gin.Context) (interface{}, hexerror.HexError) {
	bodys, err := ioutil.ReadAll(c.Request.Body)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	var eticketIds []int64
	err = json.Unmarshal(bodys, &eticketIds)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	// 先存储起来
	cnt, e := services.ReInitTicket(c, eticketIds)
	if e != nil {
		return nil, utils.WrapError(e)
	}
	return map[string]int64{"cnt": cnt}, nil
}
