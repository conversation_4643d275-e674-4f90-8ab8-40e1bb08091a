package http

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/db"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/repo/diff"
	"testing"
)

func TestTesting(t *testing.T) {
	logger.InitLogger("debug", "prod")
	holo,_,_ := db.CreateDb(&config.DBConfig{
		Url:         "postgresql://BASIC$saas_report_dml_dev:saas$<EMAIL>:80/bi_saas_dev",
	})
	pg,_,_ := db.CreateDb(&config.DBConfig{
		Url:         "postgresql://omni_pos_test:<EMAIL>:1921/saas_qa_pos_sales_report",
	})
	repo.DefaultDiffRepo = &diff.DiffRepoImpl{PG: pg,HOLO: holo}
	d,_ := repo.DefaultDiffRepo.MaxCreateTimeDiff(context.Background())
	fmt.Println(d)
}
