package shopkeeper

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
	"hexcloud.cn/histore/hexerror"
)

// Channel 小掌柜-渠道收入
func Channel(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req model.CommonRequest
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.shopkeeper.Channel] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	//if len(req.RegionSearchIds) == 0 {
	//	return nil, hexerror.InvalidData("RegionSearchIds is empty")
	//}
	resp, err := shopkeeper.Channel(c, &req)
	return resp, utils.WrapError(err)
}
