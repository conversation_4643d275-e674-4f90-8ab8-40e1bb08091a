package shopkeeper

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
	"hexcloud.cn/histore/hexerror"
)

// IncomeTrend 每月收入趋势
func IncomeTrend(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req model.CommonRequest
	err := c.ShouldBind(&req)
	if len(req.RegionSearchIds) == 0 {
		return nil, hexerror.InvalidData("RegionSearchIds is empty")
	}
	if err != nil {
		logger.Pre().Warningf(
			"[%d] [http.shopkeeper.queryIncomeTrend] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	resp, err := shopkeeper.IncomeTrend(c, &req)
	return resp, utils.WrapError(err)
}
