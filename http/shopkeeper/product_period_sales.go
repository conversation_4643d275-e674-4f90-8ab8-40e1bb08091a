package shopkeeper

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/shopkeeper"
	"hexcloud.cn/histore/hexerror"
)

// 商品时段报表
func ProductPeriodSales(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req model.CommonRequest
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.shopkeeper.queryProductPeriodSales] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}

	resp, err := shopkeeper.QueryProductPeriodSales(c, &req)
	return resp, utils.WrapError(err)
}
