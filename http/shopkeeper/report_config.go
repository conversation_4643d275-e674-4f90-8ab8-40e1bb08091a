package shopkeeper

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hexerror"
)

func QueryReportConfig(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req struct {
		Type string `json:"type"`
	}
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.shopkeeper.QueryReportConfig] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	conf, err := repo.DefaultReportConfigRepo.QueryByTypeCurrentUser(c, req.Type)
	return conf, utils.WrapError(err)
}

func SaveReportConfig(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req struct {
		Type   string `json:"type"`
		Config string `json:"config"`
	}
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.shopkeeper.SaveReportConfig] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	conf, err := repo.DefaultReportConfigRepo.SaveByTypeCurrentUser(c, req.Type, req.Config)
	return conf, utils.WrapError(err)
}
