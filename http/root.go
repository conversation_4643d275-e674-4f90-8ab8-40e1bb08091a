package http

import (
	"gitlab.hexcloud.cn/histore/sales-report/http/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	"net/http"

	"gitlab.hexcloud.cn/histore/sales-report/http/financial"
	"gitlab.hexcloud.cn/histore/sales-report/http/shopkeeper"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	oauth2 "gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"

	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/http/report"
	oauth "hexcloud.cn/histore/hex-pkg/hex-oauth/http-oauth"
)

func GetServer() http.Handler {
	gin.EnableJsonDecoderUseNumber()
	root := gin.New()
	root.Use(tracing.TracerMiddle())
	enable := config.DefaultConfig.Oauth.Enable
	if enable {
		logger.Pre().Infoln("http using new oauth sever")
		oauthMiddle := oauth2.NewGinMiddleware("/"+config.DefaultConfig.Http.Prefix+"/api/v1/report/sales/testing",
			"/"+config.DefaultConfig.Http.Prefix+"/api/v1/report/sales/store-business")
		//oauthMiddle := oauth2.NewDebugGinMiddleware(100, 1)
		root.Use(oauthMiddle)
	} else {
		logger.Pre().Infoln("http using old oauth sever")
		auth := oauth.GinOauthMiddleware(config.DefaultConfig.ConnStr.OAuth)
		root.Use(auth)
	}
	router := root.Group(config.DefaultConfig.Http.Prefix)
	router.GET("/ping", func(c *gin.Context) { c.String(http.StatusOK, "pong") })
	router.GET("/ip", func(c *gin.Context) { c.String(http.StatusOK, c.ClientIP()) })
	// 设置TrackId的地方
	v1 := router.Group("/api/v1", trackId())
	needAuth := v1.Group("/")
	{
		// http电子小票接入接口
		needAuth.POST("/ticket", WrapAPI(addTicket))
		// http异常电子小票接入接口
		needAuth.POST("/abnormal_ticket", WrapAPI(addAbnormalTicket))
		needAuth.POST("/reinit", WrapAPI(reInitTicket))
		// 异常电子小票接口
		needAuth.GET("/tickets", WrapAPI(querySales))
		// 通过电子小票id查询电子小票
		needAuth.GET("/ticket", WrapAPI(queryTicketById))
		// 更新电子小票接口
		needAuth.PUT("/tickets", WrapAPI(updateSales))
		// 回滚电子小票接口
		needAuth.PUT("/tickets/rollback", WrapAPI(rollbackSales))
		needAuth.POST("/adjust", WrapAPI(addAdjust))
		needAuth.POST("/adjust/rollback", WrapAPI(rollbackAdjust))
		needAuth.GET("/regions", WrapAPI(regions))
		needAuth.POST("/inbound", WrapAPI(inbound))
		// PC报表接口
		sales := needAuth.Group("/report/sales")
		{
			// 拨测
			router.GET("/testing", WrapAPI(Testing))
			router.GET("/store-business", WrapAPI(report.StoreBusiness))
			// 报表更新时间
			sales.POST("/updatetime", WrapAPI(report.UpdateTime))
			// 首页报表
			sales.POST("/dashboard", WrapAPI(report.Dashboard))
			// 门店销售报表
			sales.POST("/store", WrapAPI(report.StoreSales)) // finished
			// 单品销售报表
			sales.POST("/product", WrapAPI(report.ProductSales)) // finished
			// 类别销售报表
			sales.POST("/category", WrapAPI(report.CategorySales))
			// 支付统计销售报表
			sales.POST("/payment", WrapAPI(report.PaymentStatistics)) // finished
			// 交易折扣报表
			sales.POST("/discount", WrapAPI(report.DiscountSales))
			// 门店渠道销售报表
			sales.POST("/channel/store", WrapAPI(report.StoreChannelSales)) // finished
			// 单品渠道销售报表
			sales.POST("/channel/product", WrapAPI(report.ProductChannelSales)) // finished
			// 门店时段销售报表
			sales.POST("/period/store", WrapAPI(report.StorePeriodSales)) // 门店时		段
			// 单品时段销售报表
			sales.POST("/period/product", WrapAPI(report.ProductPeriodSales)) // finished
			// 支付时段销售报表
			sales.POST("/period/payment", WrapAPI(report.PaymentPeriodSales)) // finished
			// 单杯出杯时间报表
			sales.POST("/maketime/product", WrapAPI(report.ProductMakeTime))
			// 订单完成时间报表
			sales.POST("/maketime/store", WrapAPI(report.StoreMakeTime))
			// 退单分析表
			sales.POST("/refund/analy", WrapAPI(report.RefundAnalysis)) // finished
			// 商品属性销售表
			sales.POST("/product/flavor", WrapAPI(report.ProductFlavorSales))
			// 商品计税表
			sales.POST("/product/tax", WrapAPI(report.ProductTaxSales))
			// 近期营业数据汇总
			sales.POST("/recent/summary", WrapAPI(report.RecentSummary))
			// 报损报表
			sales.POST("/adjust", WrapAPI(report.Adjust))
			sales.POST("/mealsegments", WrapAPI(report.MealSegments))
			// 促销报表
			pos := sales.Group("/pos")
			pos.POST("/promotion", WrapAPI(report.PosPromotion))
		}
		// 财务报表接口
		needAuth.POST("/report/financial", WrapAPI(financial.FinancialReport))
		// 收银汇总数据
		needAuth.POST("/report/open/financial", WrapAPI(open.OpenFinancialReport))
		// 商品销售流水
		needAuth.POST("/report/open/product", WrapAPI(open.OpenProductReport))
		finance := needAuth.Group("/finance/report")
		{
			// 财务报表：营业状况
			finance.POST("/business/status", WrapAPI(financial.BusinessStatus))
			// 财务报表：物流构成
			finance.POST("/logistics/form", WrapAPI(financial.LogisticsForm))
			// 财务报表：外卖渠道构成
			finance.POST("/channel/form", WrapAPI(financial.ChannelForm))
			// 财务报表：实收组成
			finance.POST("/net/receipts", WrapAPI(financial.NetReceipts))
			// 财务报表：优惠组成
			finance.POST("/discount", WrapAPI(financial.Discount))
			// 财务报表：菜品收入
			finance.POST("/product", WrapAPI(financial.Product))
		}
		// 小掌柜报表接口
		littleManager := needAuth.Group("/shopkeeper/report")
		{
			// 1首页面板
			littleManager.POST("/dashboard", WrapAPI(shopkeeper.Dashboard)) // finished
			// 1.1营业额
			littleManager.POST("/business", WrapAPI(shopkeeper.Business)) // finished
			// 2.1实收金额.商品类别
			littleManager.POST("/business/category", WrapAPI(shopkeeper.BusCategory)) // finished
			// 2.5实收金额.指定某个商品类别下的详细商品排行
			littleManager.POST("/business/category/product", WrapAPI(shopkeeper.BusCatProduct)) // finished
			// 2.2实收金额.时段,就是1.3接口，公用一个就行
			// 2.3实收金额.渠道，就是1.2接口，公用一个
			// 2.4实收金额.物流，就是1.5接口，公用一个
			// 2.6实收金额.渠道收入趋势
			littleManager.POST("/channel/trend", WrapAPI(shopkeeper.ChannelTrend)) // finished
			// 2.7实收金额.物流收入趋势
			littleManager.POST("/logistics/trend", WrapAPI(shopkeeper.LogisticsTrend)) // finished
			// 1.2小掌柜.渠道收入
			littleManager.POST("/channel", WrapAPI(shopkeeper.Channel)) // finished
			// 1.3小掌柜.营业时段
			littleManager.POST("/period", WrapAPI(shopkeeper.Period)) // finished
			// 1.4小掌柜.菜品收入
			littleManager.POST("/product/rank", WrapAPI(shopkeeper.ProductRank)) // finished
			// 1.5小掌柜.物流占比
			littleManager.POST("/logistics/rank", WrapAPI(shopkeeper.LogisticsRank)) // finished
			// 1.6小掌柜.各平台补贴
			littleManager.POST("/platform/discount", WrapAPI(shopkeeper.PlatformDiscount)) // finished
			// 1.7月收入趋势
			littleManager.POST("/income", WrapAPI(shopkeeper.IncomeTrend)) // finished
			// 3.1折扣分类
			littleManager.POST("/discount/category", WrapAPI(shopkeeper.DiscountCategory)) // finished
			// 3.2折扣渠道
			littleManager.POST("/discount/channel", WrapAPI(shopkeeper.DiscountChannel)) // finished
			// 3.3订单渠道
			// 3.4退单渠道共用一个
			littleManager.POST("/order/channel", WrapAPI(shopkeeper.OrderChannel)) // finished
			// 3.4退单渠道
			//littleManager.POST("/refund/channel", WrapAPI(shopkeeper.RefundChannel))
			// 3.5月收入趋势详细，与1.7共用一个接口
			//littleManager.POST("/income/detailed", WrapAPI(shopkeeper.IncomeDetailed))
			// 3.6折扣平台补贴,与1.6共用一个接口
			//littleManager.POST("/discount/platform", WrapAPI(shopkeeper.DiscountPlatform))
			// 3.7渠道收入/订单，与1.2渠道收入共用一个接口
			//littleManager.POST("/channel/order", WrapAPI(shopkeeper.ChannelOrder))
			// 4.1 客流构成
			littleManager.POST("/customer/form", WrapAPI(shopkeeper.CustomerForm))
			// 4.2 门店营业额Top10排行
			littleManager.POST("/business/top10/rank", WrapAPI(shopkeeper.StoreBusinessTop10Rank))
			// 4.3 商品分布
			littleManager.POST("/product/distribute", WrapAPI(shopkeeper.ProductDistribute))
			// 5.1 财务实收金额和财务优惠组成下钻
			littleManager.POST("/detail", WrapAPI(shopkeeper.Detail))
			// 5.5 商品Top20排行 已废弃
			littleManager.POST("/product/top20/rank", WrapAPI(shopkeeper.ProductTop20Rank))
			// 5.5 商品排行
			littleManager.POST("/product/topn/rank", WrapAPI(shopkeeper.ProductTopNRank))
			// 5.6 商品下钻.属性
			littleManager.POST("/product/attribute", WrapAPI(shopkeeper.ProductAttribute))
			// 某商品各门店销售数据
			littleManager.POST("/product/store/rank", WrapAPI(shopkeeper.ProductStoreRank))
			// 支付统计销售报表
			littleManager.POST("/payment", WrapAPI(shopkeeper.PaymentStatistics)) // finished
			// 统计渠道商品数据
			littleManager.POST("/product/polymer", WrapAPI(shopkeeper.ProductPolymer)) // finished
			//小掌柜单品时段销售报表
			littleManager.POST("/period/product", WrapAPI(shopkeeper.ProductPeriodSales))
			// 近期营业数据汇总
			littleManager.POST("/recent/summary", WrapAPI(shopkeeper.RecentSummary)) // finished
			// 大掌柜报表配置查询
			littleManager.POST("/config/query", WrapAPI(shopkeeper.QueryReportConfig)) // finished
			// 大掌柜报表配置保存
			littleManager.POST("/config/save", WrapAPI(shopkeeper.SaveReportConfig)) // finished
			// 折扣排行
			littleManager.POST("/discount/topn/rank", WrapAPI(shopkeeper.DiscountTopNRank))
			// 新营业时段
			littleManager.POST("/period-new", WrapAPI(shopkeeper.PeriodNew))
		}
	}
	return root
}
