package http

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hexerror"
	"math"
)

// Testing 拨测
func Testing(c *gin.Context) (interface{}, hexerror.HexError) {
	d, err := repo.DefaultDiffRepo.MaxCreateTimeDiff(c)
	var s int32
	if d.Seconds() > math.MaxInt32 {
		s = math.MaxInt32
	} else {
		s = int32(d.Seconds())
	}
	return map[string]interface{}{"delay_seconds": s}, utils.WrapError(err)
}
