package http

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"hexcloud.cn/histore/hexerror"
	"io/ioutil"
	"os"
	"path/filepath"
)

type Province struct {
	Value    string `json:"value"`
	Label    string `json:"label"`
	Children []City `json:"children"`
}

type City struct {
	Value    string `json:"value"`
	Label    string `json:"label"`
	Children []County `json:"children"`
}

type County struct {
	Value    string `json:"value"`
	Label    string `json:"label"`
	Ssqename string `json:"ssqename"`
}

var ps []Province

func regions(c *gin.Context) (interface{}, hexerror.HexError) {
	if len(ps) > 0 {
		return ps, nil
	}
	pathCurrent, _ := os.Getwd()
	regionPath := filepath.Join(pathCurrent, "pkg/regions/region.json")
	fileByte, err := ioutil.ReadFile(regionPath)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	err = json.Unmarshal(fileByte, &ps)
	if err != nil {
		return nil, utils.WrapError(err)
	}
	return ps, nil
}
