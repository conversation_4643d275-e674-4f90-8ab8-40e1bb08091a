package financial

import (
	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services/finance"
	"hexcloud.cn/histore/hexerror"
)

// 财务报表-物流构成
func LogisticsForm(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var req model.CommonRequest
	if err := c.ShouldBind(&req); err != nil {
		logger.Pre().Warningf(
			"[%d] [http.report.queryPaymentStatistics] Bind query failed, Err: `%+v`", trackId, err,
		)
		return nil, hexerror.InvalidData("Invalid request data.", err)
	}
	resp, err := finance.QueryLogisticsForm(c, &req)
	return resp, utils.WrapError(err)
}
