package http

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"hexcloud.cn/histore/hexerror"
)

// 异常电子小票
func querySales(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	var (
		limit, offset int
		err           error
	)
	if limit, err = strconv.Atoi(c.Query("limit")); err != nil {
		logger.Pre().Warningf("[%d] [http.querySales] Limit not a int number, will use 10.\nData:`%s`", trackId, c.Query("limit"))
		limit = 10
	}
	if offset, err = strconv.Atoi(c.Query("offset")); err != nil || offset < 0 {
		logger.Pre().Warningf("[%d] [http.querySales] Offset not a positive int number, will use 0.\nData:`%s`", trackId, c.Query("offset"))
		offset = 0
	}

	// 修改异常电子小票按多门店查询
	storeIds := make([]int64, 0)
	if v := c.Query("store_id"); v != "" {
		if strings.Contains(v, ",") {
			arr := strings.Split(v, ",")
			for _, s := range arr {
				storeId, _ := strconv.ParseInt(s, 10, 64)
				storeIds = append(storeIds, storeId)
			}
		} else {
			storeId, _ := strconv.ParseInt(v, 10, 64)
			storeIds = append(storeIds, storeId)
		}
	}
	ctx := context.WithValue(c, "lan", c.DefaultQuery("lan", ""))
	userInfo, _ := c.Get("user_info")
	ctx = context.WithValue(ctx, "user_info", userInfo)
	// 获取partner_id，并放到ctx里
	partnerID, exists := c.Get("partner_id")
	if exists {
		ctx = context.WithValue(ctx, "partner_id", partnerID)
	}
	results := make([]*model.SalesTicketContent, 0)
	var Total int64
	if len(storeIds) > 0 {
		for _, storeId := range storeIds {
			// 组装查询条件
			var errTicketReq = &model.ErrorTicketRequest{
				StoreId:      storeId,
				Status:       model.NewSalesInfoStatus(c.Query("status")),
				BusDate:      c.Query("bus_date"),
				OrderDate:    c.Query("order_date"),
				CreateDate:   c.Query("create_date"),
				UpdateDate:   c.Query("update_date"),
				Limit:        limit,
				Offset:       offset,
				IncludeTotal: c.Query("include_total"),
				Lan:          c.Query("lan"),
				ErrMsg:       c.Query("err_msg"),
				ChannelId:    c.Query("channel_id"),
			}
			result, total, err := services.QuerySalesInfo(ctx, errTicketReq)
			// 此处会返回无用户信息异常
			if err != nil {
				return nil, utils.WrapError(err)
			}
			results = append(results, result...)
			Total += total
		}
	} else {
		// 组装查询条件
		var errTicketReq = &model.ErrorTicketRequest{
			Status:       model.NewSalesInfoStatus(c.Query("status")),
			BusDate:      c.Query("bus_date"),
			OrderDate:    c.Query("order_date"),
			CreateDate:   c.Query("create_date"),
			UpdateDate:   c.Query("update_date"),
			Limit:        limit,
			Offset:       offset,
			IncludeTotal: c.Query("include_total"),
			Lan:          c.Query("lan"),
			ErrMsg:       c.Query("err_msg"),
			ChannelId:    c.Query("channel_id"),
		}
		result, total, err := services.QuerySalesInfo(ctx, errTicketReq)
		// 此处会返回无用户信息异常
		if err != nil {
			return nil, utils.WrapError(err)
		}
		results = append(results, result...)
		Total += total
	}

	return map[string]interface{}{
		"rows":  results,
		"total": Total,
	}, nil
}

func queryTicketById(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)
	fmt.Println(trackId)
	ticketId := c.Query("ticket_id")
	partnerId := cast.ToInt64(c.Query("partner_id"))
	logger.Pre().Infof("查询电子小票参数是ticket_id: %s, partner_id: %d", ticketId, partnerId)
	ticketContent, err := services.FindSalesTicket(c, ticketId, partnerId)
	if err != nil {
		logger.Pre().Errorf("[%d] [http.queryTicket] failed.\nTicketId: `%s`\nErr:`%+v`", trackId, ticketId, err)
		return nil, utils.WrapError(err)
	}
	return map[string]interface{}{
		"ticket_content": ticketContent,
		"total":          1,
	}, nil
}

func updateSales(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)

	template := make(map[string]interface{})
	if err := c.ShouldBind(&template); err != nil {
		logger.Pre().Errorf("[%d] [http.updateSales] Bind map failed.\nData: `%s`\nErr:`%+v`", trackId, template, err)
		return nil, utils.WrapError(err)
	}
	id, err := strconv.ParseInt(template["id"].(string), 10, 64)
	if err != nil {
		logger.Pre().Errorf("[%d] [http.updateSales] Parse int failed.\nData: `%s`\nErr:`%+v`", trackId, template["id"], err)
		return nil, utils.WrapError(err)
	}
	// 修改的是content_modified里面的内容，
	modify := template["content_modified"].(string)
	var tempTicket Ticket
	if err := json.Unmarshal([]byte(modify), &tempTicket); err != nil {
		return nil, utils.WrapError(err)
	}
	err = services.UpdateSalesInfo(c, id, template["init_status"].(bool), modify)
	return map[string]bool{
		"operate_status": err == nil,
	}, utils.WrapError(err)
}

func rollbackSales(c *gin.Context) (interface{}, hexerror.HexError) {
	trackId := c.Value(config.TrackId)

	template := make(map[string]string)
	if err := c.ShouldBind(&template); err != nil {
		logger.Pre().Errorf("[%d] [http.rollbackSales] Bind map failed.\nData: `%s`\nErr:`%+v`", trackId, template, err)
		return nil, utils.WrapError(err)
	}
	id, err := strconv.ParseInt(template["id"], 10, 64)
	if err != nil {
		logger.Pre().Errorf("[%d] [http.rollbackSales] Parse int failed.\nData: `%s`\nErr:`%+v`", trackId, template["id"], err)
		return nil, utils.WrapError(err)
	}

	err = services.RollbackSalesModified(c, id)
	return map[string]bool{
		"operate_status": err == nil,
	}, utils.WrapError(err)
}

// === 不需要阅读，过渡性代码
type Ticket struct {
	Id              string                  `json:"id,omitempty"`                                                                  // 电子小票记录 id
	TicketId        string                  `json:"ticket_id,omitempty"`                                                           // 小票id，全市场唯一
	TicketNo        string                  `json:"ticket_no,omitempty"`                                                           // 交易号，根据业务规则生成
	StartTime       string                  `json:"start_time,omitempty"`                                                          // 开单时间， YYYY-MM-DD HH:MM:SS
	EndTime         string                  `json:"end_time,omitempty"`                                                            // 订单结束时间, YYYY-MM-DD HH:MM:SS
	BusDate         string                  `json:"bus_date,omitempty"`                                                            // 门店营业日期, YYYY-MM-DD
	Pos             model.TicketPos         `json:"pos,omitempty"`                                                                 // pos信息
	Operator        model.TicketOperator    `json:"operator,omitempty"`                                                            // 收银员
	Amounts         model.TicketAmounts     `json:"amounts,omitempty"`                                                             // 订单金额信息
	TakemealNumber  string                  `json:"takemealNumber,omitempty"`                                                      // 取餐号，根据业务规则生成
	Qty             int                     `json:"qty,omitempty"`                                                                 // 商品总数
	Status          model.TicketStatus      `json:"status,omitempty"`                                                              // 订单状态(正单，退单，或者是退单的原单)
	RefundInfo      model.TicketRefundInfo  `json:"refundInfo,omitempty"`                                                          // 订单的退款信息
	Channel         model.TicketChannel     `json:"channel,omitempty"`                                                             // 订单的渠道信息
	Products        []*model.TicketProduct  `json:"products,omitempty"`                                                            // 小票中的商品
	Payments        []model.TicketPayment   `json:"payments,omitempty"`                                                            // 支付项
	Promotions      []model.TicketPromotion `json:"promotions,omitempty"`                                                          // 促销项
	Members         []model.TicketMember    `json:"members,omitempty"`                                                             // 会员信息
	Table           model.TicketTable       `json:"table,omitempty"`                                                               // 桌位信息
	Coupons         []model.TicketCoupon    `json:"coupons,omitempty"`                                                             // 卡券
	People          int                     `json:"people,omitempty"`                                                              // 用餐人数
	RoomNo          string                  `json:"room_no,omitempty"`                                                             // 房间号
	Remark          string                  `json:"remark,omitempty"`                                                              // 备注
	HouseAc         bool                    `json:"house_ac,omitempty"`                                                            // 是否自用（如家
	OrderTimeType   string                  `json:"order_time_type,omitempty"`                                                     // 早中晚餐标志
	ShiftNumber     string                  `json:"shiftNumber,omitempty"`                                                         // 班次号，根据业务
	TaxList         []model.TicketTax       `json:"taxList,omitempty"`                                                             // 税项
	TakeawayInfo    model.TicketTakeaway    `json:"takeaway_info,omitempty"`                                                       // 外卖附加信息
	Store           TicketStore             `json:"store,omitempty"`                                                               // 门店信息
	Fees            []model.TicketFee       `json:"fees,omitempty"`                                                                // 费用信息
	TimeZone        string                  `json:"timeZone,omitempty"`                                                            // 时区信息，例如Asia/Shanghai
	ErrorCodes      string                  `json:"error_codes,omitempty"`                                                         // 错误信息
	MealSegmentName string                  `protobuf:"bytes,48,opt,name=mealSegmentName,proto3" json:"mealSegmentName,omitempty"` // 餐段
}

type TicketStore struct {
	Id         string `json:"id,omitempty"`         // 门店id
	Code       string `json:"code,omitempty"`       // 门店编码
	PartnerId  string `json:"partner_id,omitempty"` // 租户id
	ScopeId    string `json:"scope_id,omitempty"`   //用于类似连锁酒店独立运营
	SecondCode string `json:"secondCode,omitempty"` // 门店第二编码，目前只在喜茶环境有实际意义
	CompanyId  string `json:"companyId,omitempty"`  // 门店公司id, 目前只在喜茶环境有实际意义
}

func (t *Ticket) toModelTicket() *model.Ticket {
	id, _ := strconv.ParseInt(t.Id, 10, 64)
	pid, _ := strconv.ParseInt(t.Store.PartnerId, 10, 64)
	sid, _ := strconv.ParseInt(t.Store.ScopeId, 10, 64)
	return &model.Ticket{
		Id:             id,
		TicketId:       t.TicketId,
		TicketNo:       t.TicketNo,
		StartTime:      t.StartTime,
		EndTime:        t.EndTime,
		BusDate:        t.BusDate,
		Pos:            t.Pos,
		Operator:       t.Operator,
		Amounts:        t.Amounts,
		TakemealNumber: t.TakemealNumber,
		Qty:            t.Qty,
		Status:         t.Status,
		RefundInfo:     t.RefundInfo,
		Channel:        t.Channel,
		Products:       t.Products,
		Payments:       t.Payments,
		Promotions:     t.Promotions,
		Members:        t.Members,
		Table:          t.Table,
		Coupons:        t.Coupons,
		People:         t.People,
		RoomNo:         t.RoomNo,
		Remark:         t.Remark,
		HouseAc:        t.HouseAc,
		OrderTimeType:  t.OrderTimeType,
		ShiftNumber:    t.ShiftNumber,
		TaxList:        t.TaxList,
		TakeawayInfo:   t.TakeawayInfo,
		Store: model.TicketStore{
			Id:         t.Store.Id,
			Code:       t.Store.Code,
			PartnerId:  cast.ToString(pid),
			ScopeId:    cast.ToString(sid),
			SecondCode: t.Store.SecondCode,
			CompanyId:  t.Store.CompanyId,
		},
		Fees:       t.Fees,
		TimeZone:   t.TimeZone,
		ErrorCodes: t.ErrorCodes,
	}
}
