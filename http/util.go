package http

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"hexcloud.cn/histore/hexerror"
)

func writeHexError2Resp(c *gin.Context, e hexerror.HexError) {
	c.J<PERSON>N(http.StatusOK, map[string]interface{}{
		"status_code": config.ErrorCode,
		"code":        e.Code(),
		"description": e.Description(),
		"detail":      e.Detail(),
	})
	c.Abort()
}

// 封装api，组装返回的结果数据
func WrapAPI(f func(*gin.Context) (interface{}, hexerror.HexError)) func(*gin.Context) {
	return func(c *gin.Context) {
		start := time.Now().UTC()
		resp, err := f(c)
		c.<PERSON><PERSON>("Duration", fmt.Sprintf("%dms", time.Since(start).Milliseconds()))
		if c.<PERSON>borted() {
			return
		}

		if err != nil {
			writeHexError2Resp(c, err)
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"status_code": config.SuccessCode,
			"payload":     resp,
		})
	}
}

// 设置TrackId
func trackId() gin.HandlerFunc {
	return func(c *gin.Context) {
		id := uuid.GetId()
		c.Set(config.TrackId, id)
		c.Header("Track-Id", strconv.FormatInt(id, 10))

		c.Next()
	}
}
