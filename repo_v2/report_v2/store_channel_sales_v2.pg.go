package report_v2

import (
	"context"
	"database/sql"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) StoreChannelSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.StoreChannelSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForStoreChannelSalesV2(trackId, condition)
	ch1 := make(chan []*report.StoreChannelV2, 1)
	ch2 := make(chan []*report.StoreChannelV2, 1)
	go queryDBWithChanForStoreChannelSalesV2(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForStoreChannelSalesV2(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.StoreChannelV2
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.StoreChannelSalesResponseV2{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForStoreChannelSalesV2(id int64, db *sql.DB, sql string, ch chan []*report.StoreChannelV2) {
	ch <- queryDBForStoreChannelSalesV2(id, db, sql)
}

func queryDBForStoreChannelSalesV2(trackId int64, db *sql.DB, s string) []*report.StoreChannelV2 {
	results := make([]*report.StoreChannelV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StoreChannelV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.ChannelId,
			&f.OrderType,
			&f.BusinessAmount,
			&f.RealAmount,
			&f.ExpendAmount,
			&f.CustomerPrice,
			&f.ValidOrderCount,
			&f.GrossAmount,
			&f.NetAmount,
			&f.DiscountAmount,
			&f.MerchantAllowance,
			&f.PackageFee,
			&f.DeliveryFee,
			&f.SendFee,
			&f.MerchantSendFee,
			&f.PlatformSendFee,
			&f.PlatformAllowance,
			&f.Commission,
			&f.ServiceFee,
			&f.Tip,
			&f.OtherFee,
			&f.Receivable,
			&f.PayAmount,
			&f.Rounding,
			&f.OverflowAmount,
			&f.ChangeAmount,
			&f.TransferRealAmount,
			&f.DiscountContribute,
			&f.DiscountMerchantContribute,
			&f.PayMerchantContribute,
			&f.RealAmountDiscount,
			&f.RealAmountPayment,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForStoreChannelSalesV2(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 行sql
		summarySQL string // 汇总sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						store_id AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,store_id
					ORDER BY bus_date DESC,store_id
					)T
				GROUP BY region_id
			) SELECT
					'' AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE(store_id, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days, 0) AS business_days,
					COALESCE(channel_id, 0) AS channel_id,
					COALESCE(order_type, '') AS order_type,
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(package_fee) AS package_fee, --包装费
					SUM(delivery_fee) AS delivery_fee, --配送费原价
					SUM(send_fee) AS send_fee, --用户实际配送费
					SUM(merchant_send_fee) AS merchant_send_fee, --商家承担配送费
					SUM(platform_send_fee) AS platform_send_fee, --平台承担配送费
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(commission) AS commission, --佣金
					SUM(service_fee) AS service_fee, --门店服务费
					SUM(tip) AS tip, -- 小费
					SUM(other_fee) AS other_fee, --其他
					SUM(amount_4) AS receivable, --应付金额
					SUM(pay_amount) AS pay_amount, --实付金额
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(change_amount) AS change_amount, --找零
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,--实收金额(财务)
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,--优惠组成(财务)
					SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣
					SUM(pay_merchant_contribute) AS pay_merchant_contribute,--优惠组成-支付
					SUM(discount_buyer_contribute+discount_other_contribute) AS real_amount_discount,--实收组成-折扣
					SUM(pay_buyer_contribute+pay_other_contribute) AS real_amount_payment,--实收组成-支付
					0 AS total
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON fact.store_id = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					store_caches.company_info, store_id,bd.days,channel_id, order_type
				ORDER BY store_id
				{LIMIT};
  		`

		summarySQL = `
			WITH base AS (
				SELECT
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(package_fee) AS package_fee, --包装费
					SUM(delivery_fee) AS delivery_fee, --配送费原价
					SUM(send_fee) AS send_fee, --用户实际配送费
					SUM(merchant_send_fee) AS merchant_send_fee, --商家承担配送费
					SUM(platform_send_fee) AS platform_send_fee, --平台承担配送费
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(commission) AS commission, --佣金
					SUM(service_fee) AS service_fee, --门店服务费
					SUM(tip) AS tip, -- 小费
					SUM(other_fee) AS other_fee, --其他
					SUM(amount_4) AS receivable, --应付金额
					SUM(pay_amount) AS pay_amount, --实付金额
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(change_amount) AS change_amount, --找零
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,--实收金额(财务)
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,--优惠组成(财务)
					SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣
					SUM(pay_merchant_contribute) AS pay_merchant_contribute,--优惠组成-支付
					SUM(discount_buyer_contribute+discount_other_contribute) AS real_amount_discount,--实收组成-折扣
					SUM(pay_buyer_contribute+pay_other_contribute) AS real_amount_payment--实收组成-支付
			
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY
					bus_date, store_id
				ORDER BY bus_date DESC, store_id
			)
			SELECT
					'' AS bus_date,
					0 AS geo_id,
					0 AS branch_id,
					0 AS company_id,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数,
					0 AS channel_id,
					'' AS order_type,
					COALESCE(SUM(business_amount),0) AS business_amount, --营业额
					COALESCE(SUM(real_amount), 0) AS real_amount, --实收金额(交易)
					COALESCE(SUM(expend_amount), 0)AS expend_amount, --支出(交易)
					CASE WHEN SUM(valid_order_count)=0 THEN 0 ELSE SUM(business_amount)/SUM(valid_order_count) END AS customer_price, --单均价
					COALESCE(SUM(valid_order_count), 0) AS valid_order_count, --有效订单数
					COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
					COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
					COALESCE(SUM(discount_amount),0) AS discount_amount, --折扣金额
					COALESCE(SUM(merchant_allowance), 0) AS merchant_allowance, --商家活动补贴
					COALESCE(SUM(package_fee), 0) AS package_fee, --包装费
					COALESCE(SUM(delivery_fee), 0) AS delivery_fee, --配送费原价
					COALESCE(SUM(send_fee), 0) AS send_fee, --配送费用户承担
					COALESCE(SUM(merchant_send_fee),0) AS merchant_send_fee, --配送费商家承担
					COALESCE(SUM(platform_send_fee),0) AS platform_send_fee, --配送费平台承担
					COALESCE(SUM(platform_allowance),0) AS platform_allowance, --平台活动补贴
					COALESCE(SUM(commission), 0) AS commission, --佣金
					COALESCE(SUM(service_fee), 0) AS service_fee, --门店服务费
					COALESCE(SUM(tip),0) AS tip, --门店小费
					COALESCE(SUM(other_fee), 0) AS other_fee, --其他
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(rounding), 0) AS rounding, --抹零
					COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(SUM(change_amount), 0) AS change_amount, --找零
					COALESCE(SUM(transfer_real_amount), 0) AS transfer_real_amount,--实收金额(财务)
					COALESCE(SUM(discount_contribute), 0) AS discount_contribute, --优惠组成(财务)
					COALESCE(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣(财务)
					COALESCE(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付(财务)
					COALESCE(SUM(real_amount_discount), 0) AS real_amount_discount, --实收组成-折扣(财务)
					COALESCE(SUM(real_amount_payment), 0) AS real_amount_payment, --实收组成-折扣(财务)
					COUNT(1) AS total
				FROM base
        `
	} else {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						store_id AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY store_id
					)T
				GROUP BY region_id
			) SELECT
					to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE(store_id, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days, 0) AS business_days,
					COALESCE(channel_id, 0) AS channel_id,
					order_type AS order_type,
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(package_fee) AS package_fee, --包装费
					SUM(delivery_fee) AS delivery_fee, --配送费原价
					SUM(send_fee) AS send_fee, --用户实际配送费
					SUM(merchant_send_fee) AS merchant_send_fee, --商家承担配送费
					SUM(platform_send_fee) AS platform_send_fee, --平台承担配送费
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(commission) AS commission, --佣金
					SUM(service_fee) AS service_fee, --门店服务费
					SUM(tip) AS tip, -- 小费
					SUM(other_fee) AS other_fee, --其他
					SUM(amount_4) AS receivable, --应付金额
					SUM(pay_amount) AS pay_amount, --实付金额
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(change_amount) AS change_amount, --找零
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,--实收金额(财务)
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,--优惠组成(财务)
					SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣
					SUM(pay_merchant_contribute) AS pay_merchant_contribute,--优惠组成-支付
					SUM(discount_buyer_contribute+discount_other_contribute) AS real_amount_discount,--实收组成-折扣
					SUM(pay_buyer_contribute+pay_other_contribute) AS real_amount_payment,--实收组成-支付
					0 AS total
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON fact.store_id = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,store_caches.company_info, store_id, bd.days,channel_id, order_type
				ORDER BY fact.bus_date DESC, store_id
				{LIMIT};
  		`
		summarySQL = `
			WITH base AS (
				SELECT
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(package_fee) AS package_fee, --包装费
					SUM(delivery_fee) AS delivery_fee, --配送费原价
					SUM(send_fee) AS send_fee, --用户实际配送费
					SUM(merchant_send_fee) AS merchant_send_fee, --商家承担配送费
					SUM(platform_send_fee) AS platform_send_fee, --平台承担配送费
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(commission) AS commission, --佣金
					SUM(service_fee) AS service_fee, --门店服务费
					SUM(tip) AS tip, -- 小费
					SUM(other_fee) AS other_fee, --其他
					SUM(amount_4) AS receivable, --应付金额
					SUM(pay_amount) AS pay_amount, --实付金额
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(change_amount) AS change_amount, --找零
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,--实收金额(财务)
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,--优惠组成(财务)
					SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣
					SUM(pay_merchant_contribute) AS pay_merchant_contribute,--优惠组成-支付
					SUM(discount_buyer_contribute+discount_other_contribute) AS real_amount_discount,--实收组成-折扣
					SUM(pay_buyer_contribute+pay_other_contribute) AS real_amount_payment--实收组成-支付
			
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY
					bus_date, store_id
				ORDER BY bus_date DESC, store_id
			)
			SELECT
					'' AS bus_date,
					0 AS geo_id,
					0 AS branch_id,
					0 AS company_id,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数,
					0 AS channel_id,
					'' AS order_type,
					COALESCE(SUM(business_amount),0) AS business_amount, --营业额
					COALESCE(SUM(real_amount), 0) AS real_amount, --实收金额(交易)
					COALESCE(SUM(expend_amount), 0)AS expend_amount, --支出(交易)
					CASE WHEN SUM(valid_order_count)=0 THEN 0 ELSE SUM(business_amount)/SUM(valid_order_count) END AS customer_price, --单均价
					COALESCE(SUM(valid_order_count), 0) AS valid_order_count, --有效订单数
					COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
					COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
					COALESCE(SUM(discount_amount),0) AS discount_amount, --折扣金额
					COALESCE(SUM(merchant_allowance), 0) AS merchant_allowance, --商家活动补贴
					COALESCE(SUM(package_fee), 0) AS package_fee, --包装费
					COALESCE(SUM(delivery_fee), 0) AS delivery_fee, --配送费原价
					COALESCE(SUM(send_fee), 0) AS send_fee, --配送费用户承担
					COALESCE(SUM(merchant_send_fee),0) AS merchant_send_fee, --配送费商家承担
					COALESCE(SUM(platform_send_fee),0) AS platform_send_fee, --配送费平台承担
					COALESCE(SUM(platform_allowance),0) AS platform_allowance, --平台活动补贴
					COALESCE(SUM(commission), 0) AS commission, --佣金
					COALESCE(SUM(service_fee), 0) AS service_fee, --门店服务费
					COALESCE(SUM(tip),0) AS tip, --门店小费
					COALESCE(SUM(other_fee), 0) AS other_fee, --其他
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(rounding), 0) AS rounding, --抹零
					COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(SUM(change_amount), 0) AS change_amount, --找零
					COALESCE(SUM(transfer_real_amount), 0) AS transfer_real_amount,--实收金额(财务)
					COALESCE(SUM(discount_contribute), 0) AS discount_contribute, --优惠组成(财务)
					COALESCE(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣(财务)
					COALESCE(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付(财务)
					COALESCE(SUM(real_amount_discount), 0) AS real_amount_discount, --实收组成-折扣(财务)
					COALESCE(SUM(real_amount_payment), 0) AS real_amount_payment, --实收组成-折扣(财务)
					COUNT(1) AS total
				FROM base
        `
	}

	whereSQL := getWhereSQLForStoreSalesV2(condition)
	//regionSQL := getRegionSQLForStoreSalesV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}
