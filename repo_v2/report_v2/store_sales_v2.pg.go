package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) StoreSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.StoreSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForStoreSalesV2(trackId, condition)
	ch1 := make(chan []*report.StoreSalesV2, 1)
	go queryDBWithChanForStoreSalesV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.StoreSalesResponseV2{
		Rows: rows,
	}, nil
}

func queryDBWithChanForStoreSalesV2(id int64, db *sql.DB, sql string, ch chan []*report.StoreSalesV2) {
	ch <- queryDBForStoreSalesV2(id, db, sql)
}

func queryDBForStoreSalesV2(trackId int64, db *sql.DB, s string) []*report.StoreSalesV2 {
	results := make([]*report.StoreSalesV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StoreSalesV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.BusinessAmount,
			&f.ExpendAmount,
			&f.RealAmount,
			&f.GrossAmount,
			&f.PayAmount,
			&f.CustomerPrice,
			&f.ValidOrderCount,
			&f.DiscountAmount,
			&f.MerchantAllowance,
			&f.PlatformAllowance,
			&f.MerchantSendFee,
			&f.PaymentTransferAmount,
			&f.DiscountTransferAmount,
			&f.Commission,
			&f.OrderCountRefund,
			&f.GrossAmountReturned,
			&f.NetAmountReturned,
			&f.OtherFee,
			&f.ChangeAmount,
			&f.Rounding,
			&f.OverflowAmount,
			&f.DiscountContribute,
			&f.TransferRealAmount,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForStoreSalesV2(trackId int64, condition *model.RepoCondition) string {
	var (
		rowSQL string // 行sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			--门店销售汇总信息导出rows
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,{REGION_ID}
					ORDER BY bus_date DESC,{REGION_ID}
					)T
				GROUP BY region_id
			) 	SELECT
					'' AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days,0) AS business_days,
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(pay_amount) AS pay_amount, --实付金额
					COALESCE((CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END),0) AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
					SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣金额
					SUM(discount_transfer_amount) AS discount_transfer_amount, --折扣转支付金额
					SUM(commission) AS commission, --佣金
					SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund, --退单数
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END ) AS gross_amount_returned, --退单商品流水
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END ) AS net_amount_returned, --退单商品实收
					SUM(other_fee) AS other_fee, --其他
					SUM(change_amount) AS change_amount, --找零
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute, --优惠组成(财务)
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount, --实收组成(财务)
					0 AS total
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON {REGION_ID} = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID},store_caches.company_info,bd.days
				ORDER BY {REGION_ID}
				{LIMIT}
        `
	} else {
		rowSQL = `
		--门店销售详细信息导出rows
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY {REGION_ID}
					)T
				GROUP BY region_id
			) 	SELECT
					to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days,0) AS business_days,
					SUM(amount_0) AS business_amount, --营业额
					SUM(amount_1+rounding) AS expend_amount, --支出(交易)
					SUM(amount_2-rounding) AS real_amount, --实收金额(交易)
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(pay_amount) AS pay_amount, --实付金额
					COALESCE((CASE WHEN SUM(eticket_count)=0 THEN 0 ELSE SUM(amount_0)/SUM(eticket_count) END), 0) AS customer_price, --单均价
					SUM(eticket_count) AS valid_order_count, --有效订单数
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance, --商家活动补贴
					SUM(platform_discount_amount) AS platform_allowance, --平台活动补贴
					SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
					SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣金额
					SUM(discount_transfer_amount) AS discount_transfer_amount, --折扣转支付金额
					SUM(commission) AS commission, --佣金
					SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund, --退单数
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END ) AS gross_amount_returned, --退单商品流水
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END ) AS net_amount_returned, --退单商品实收
					SUM(other_fee) AS other_fee, --其他
					SUM(change_amount) AS change_amount, --找零
					SUM(rounding) AS rounding, --抹零
					SUM(overflow_amount) AS overflow_amount, --溢收
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute, --优惠组成(财务)
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount, --实收组成(财务)
					0 AS total
				FROM sales_ticket_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON {REGION_ID} = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,{REGION_ID}, store_caches.company_info, bd.days
				ORDER BY fact.bus_date DESC, {REGION_ID}
				{LIMIT}
        `
	}

	whereSQL := getWhereSQLForStoreSalesV2(condition)
	regionSQL := getRegionSQLForStoreSalesV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	return rowSQL
}

func getLimitOffsetSQLForStoreSalesV2(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func getRegionSQLForStoreSalesV2(condition *model.RepoCondition) string {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func getWhereSQLForStoreSalesV2(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQLV2(condition)
	// 	渠道过滤条件
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = %d",
			condition.ChannelId,
		)
	}
	// 订单类型过滤条件
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	storeTypeSQL := ""
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	openStatusSQL := ""
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL, storeSQL, storeTypeSQL, openStatusSQL)
	return whereSQL
}
