package report_v2

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) StorePeriodSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.StorePeriodSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, totalSql := getSQLForStorePeriodSalesV2(trackId, condition)
	ch1 := make(chan []*report.StorePeriodV2, 1)
	ch2 := make(chan int64, 1)
	go queryDBWithChanForStorePeriodV2(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForStorePeriodTotalV2(trackId, rsmm.DB, totalSql, ch2)
	rows := <-ch1
	total := <-ch2
	close(ch1)
	close(ch2)
	return &report.StorePeriodSalesResponseV2{
		Rows:  rows,
		Total: total,
	}, nil
}

func queryDBWithChanForStorePeriodV2(id int64, db *sql.DB, sql string, ch chan []*report.StorePeriodV2) {
	ch <- queryDBForStorePeriodV2(id, db, sql)
}
func queryDBWithChanForStorePeriodTotalV2(id int64, db *sql.DB, sql string, ch chan int64) {
	ch <- queryDBForStorePeriodToyalV2(id, db, sql)
}

func queryDBForStorePeriodToyalV2(trackId int64, db *sql.DB, s string) int64 {
	r, err := db.Query(s)
	var total int64
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return total
	}

	defer r.Close()
	for r.Next() {
		if err := r.Scan(&total); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return total
		}
	}
	return total
}

func queryDBForStorePeriodV2(trackId int64, db *sql.DB, s string) []*report.StorePeriodV2 {
	results := make([]*report.StorePeriodV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StorePeriodV2)
		var value json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.BusinessAmount,
			&f.RealAmount,
			&f.ExpendAmount,
			&f.ValidOrderCount,
			&f.DiscountContribute,
			&f.TransferRealAmount,
			&value,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(value, &f.Data)
		if err != nil {
			logger.Pre().Error("门店时段data结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForStorePeriodSalesV2(trackId int64, condition *model.RepoCondition) (string, string) {
	var rowSQL, totalSql string // 行sql

	if condition.TagType == "SUMMARY" {
		rowSQL = `
		--门店时段报表summary
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date, {REGION_ID}
					ORDER BY bus_date DESC, {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
					AND is_non_sales = FALSE
				GROUP BY
					{REGION_ID}
				ORDER BY
					{REGION_ID}
				{LIMIT}
			),
			base_tickets AS (
				SELECT
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					EXTRACT(HOUR FROM fact.order_time AT TIME ZONE store_caches.time_zone)        AS hours,
				    COALESCE(SUM(amount_0), 0) AS business_amount, --营业额
                    COALESCE(SUM(amount_2-rounding), 0) AS real_amount, --实收金额(交易)
                    COALESCE(SUM(amount_1+rounding), 0) AS expend_amount, --支出(交易)
                    COALESCE(SUM(eticket_count), 0) AS valid_order_count, --有效订单数
                    COALESCE(SUM(amount_0+surcharge_amount-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                    COALESCE(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute --优惠组成(财务)

				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
                        INNER JOIN base_limit
                                    ON {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
					AND fact.is_non_sales = FALSE
				GROUP BY
					store_caches.company_info,{REGION_ID},EXTRACT(HOUR FROM fact.order_time AT TIME ZONE store_caches.time_zone)
				ORDER BY
					{REGION_ID}
			), base_tickets_for_period AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,

					SUM(business_amount) AS business_amount,
					SUM(real_amount) AS real_amount,
					SUM(expend_amount) AS expend_amount,
					SUM(valid_order_count) AS valid_order_count,
					SUM(discount_contribute) AS discount_contribute,
					SUM(transfer_real_amount) AS transfer_real_amount,

					SUM(CASE WHEN hours = 0 THEN business_amount ELSE 0 END) AS business_amount_00,
                    SUM(CASE WHEN hours = 1 THEN business_amount ELSE 0 END) AS business_amount_01,
                    SUM(CASE WHEN hours = 2 THEN business_amount ELSE 0 END) AS business_amount_02,
                    SUM(CASE WHEN hours = 3 THEN business_amount ELSE 0 END) AS business_amount_03,
                    SUM(CASE WHEN hours = 4 THEN business_amount ELSE 0 END) AS business_amount_04,
                    SUM(CASE WHEN hours = 5 THEN business_amount ELSE 0 END) AS business_amount_05,
                    SUM(CASE WHEN hours = 6 THEN business_amount ELSE 0 END) AS business_amount_06,
                    SUM(CASE WHEN hours = 7 THEN business_amount ELSE 0 END) AS business_amount_07,
                    SUM(CASE WHEN hours = 8 THEN business_amount ELSE 0 END) AS business_amount_08,
                    SUM(CASE WHEN hours = 9 THEN business_amount ELSE 0 END) AS business_amount_09,
                    SUM(CASE WHEN hours = 10 THEN business_amount ELSE 0 END) AS business_amount_10,
                    SUM(CASE WHEN hours = 11 THEN business_amount ELSE 0 END) AS business_amount_11,
                    SUM(CASE WHEN hours = 12 THEN business_amount ELSE 0 END) AS business_amount_12,
                    SUM(CASE WHEN hours = 13 THEN business_amount ELSE 0 END) AS business_amount_13,
                    SUM(CASE WHEN hours = 14 THEN business_amount ELSE 0 END) AS business_amount_14,
                    SUM(CASE WHEN hours = 15 THEN business_amount ELSE 0 END) AS business_amount_15,
                    SUM(CASE WHEN hours = 16 THEN business_amount ELSE 0 END) AS business_amount_16,
                    SUM(CASE WHEN hours = 17 THEN business_amount ELSE 0 END) AS business_amount_17,
                    SUM(CASE WHEN hours = 18 THEN business_amount ELSE 0 END) AS business_amount_18,
                    SUM(CASE WHEN hours = 19 THEN business_amount ELSE 0 END) AS business_amount_19,
                    SUM(CASE WHEN hours = 20 THEN business_amount ELSE 0 END) AS business_amount_20,
                    SUM(CASE WHEN hours = 21 THEN business_amount ELSE 0 END) AS business_amount_21,
                    SUM(CASE WHEN hours = 22 THEN business_amount ELSE 0 END) AS business_amount_22,
                    SUM(CASE WHEN hours = 23 THEN business_amount ELSE 0 END) AS business_amount_23,

                    SUM(CASE WHEN hours = 0 THEN real_amount ELSE 0 END) AS real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN real_amount ELSE 0 END) AS real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN real_amount ELSE 0 END) AS real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN real_amount ELSE 0 END) AS real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN real_amount ELSE 0 END) AS real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN real_amount ELSE 0 END) AS real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN real_amount ELSE 0 END) AS real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN real_amount ELSE 0 END) AS real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN real_amount ELSE 0 END) AS real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN real_amount ELSE 0 END) AS real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN real_amount ELSE 0 END) AS real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN real_amount ELSE 0 END) AS real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN real_amount ELSE 0 END) AS real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN real_amount ELSE 0 END) AS real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN real_amount ELSE 0 END) AS real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN real_amount ELSE 0 END) AS real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN real_amount ELSE 0 END) AS real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN real_amount ELSE 0 END) AS real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN real_amount ELSE 0 END) AS real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN real_amount ELSE 0 END) AS real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN real_amount ELSE 0 END) AS real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN real_amount ELSE 0 END) AS real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN real_amount ELSE 0 END) AS real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN real_amount ELSE 0 END) AS real_amount_23,

                    SUM(CASE WHEN hours = 0 THEN expend_amount ELSE 0 END) AS expend_amount_00,
                    SUM(CASE WHEN hours = 1 THEN expend_amount ELSE 0 END) AS expend_amount_01,
                    SUM(CASE WHEN hours = 2 THEN expend_amount ELSE 0 END) AS expend_amount_02,
                    SUM(CASE WHEN hours = 3 THEN expend_amount ELSE 0 END) AS expend_amount_03,
                    SUM(CASE WHEN hours = 4 THEN expend_amount ELSE 0 END) AS expend_amount_04,
                    SUM(CASE WHEN hours = 5 THEN expend_amount ELSE 0 END) AS expend_amount_05,
                    SUM(CASE WHEN hours = 6 THEN expend_amount ELSE 0 END) AS expend_amount_06,
                    SUM(CASE WHEN hours = 7 THEN expend_amount ELSE 0 END) AS expend_amount_07,
                    SUM(CASE WHEN hours = 8 THEN expend_amount ELSE 0 END) AS expend_amount_08,
                    SUM(CASE WHEN hours = 9 THEN expend_amount ELSE 0 END) AS expend_amount_09,
                    SUM(CASE WHEN hours = 10 THEN expend_amount ELSE 0 END) AS expend_amount_10,
                    SUM(CASE WHEN hours = 11 THEN expend_amount ELSE 0 END) AS expend_amount_11,
                    SUM(CASE WHEN hours = 12 THEN expend_amount ELSE 0 END) AS expend_amount_12,
                    SUM(CASE WHEN hours = 13 THEN expend_amount ELSE 0 END) AS expend_amount_13,
                    SUM(CASE WHEN hours = 14 THEN expend_amount ELSE 0 END) AS expend_amount_14,
                    SUM(CASE WHEN hours = 15 THEN expend_amount ELSE 0 END) AS expend_amount_15,
                    SUM(CASE WHEN hours = 16 THEN expend_amount ELSE 0 END) AS expend_amount_16,
                    SUM(CASE WHEN hours = 17 THEN expend_amount ELSE 0 END) AS expend_amount_17,
                    SUM(CASE WHEN hours = 18 THEN expend_amount ELSE 0 END) AS expend_amount_18,
                    SUM(CASE WHEN hours = 19 THEN expend_amount ELSE 0 END) AS expend_amount_19,
                    SUM(CASE WHEN hours = 20 THEN expend_amount ELSE 0 END) AS expend_amount_20,
                    SUM(CASE WHEN hours = 21 THEN expend_amount ELSE 0 END) AS expend_amount_21,
                    SUM(CASE WHEN hours = 22 THEN expend_amount ELSE 0 END) AS expend_amount_22,
                    SUM(CASE WHEN hours = 23 THEN expend_amount ELSE 0 END) AS expend_amount_23,

                    SUM(CASE WHEN hours = 0 THEN valid_order_count ELSE 0 END) AS valid_order_count_00,
                    SUM(CASE WHEN hours = 1 THEN valid_order_count ELSE 0 END) AS valid_order_count_01,
                    SUM(CASE WHEN hours = 2 THEN valid_order_count ELSE 0 END) AS valid_order_count_02,
                    SUM(CASE WHEN hours = 3 THEN valid_order_count ELSE 0 END) AS valid_order_count_03,
                    SUM(CASE WHEN hours = 4 THEN valid_order_count ELSE 0 END) AS valid_order_count_04,
                    SUM(CASE WHEN hours = 5 THEN valid_order_count ELSE 0 END) AS valid_order_count_05,
                    SUM(CASE WHEN hours = 6 THEN valid_order_count ELSE 0 END) AS valid_order_count_06,
                    SUM(CASE WHEN hours = 7 THEN valid_order_count ELSE 0 END) AS valid_order_count_07,
                    SUM(CASE WHEN hours = 8 THEN valid_order_count ELSE 0 END) AS valid_order_count_08,
                    SUM(CASE WHEN hours = 9 THEN valid_order_count ELSE 0 END) AS valid_order_count_09,
                    SUM(CASE WHEN hours = 10 THEN valid_order_count ELSE 0 END) AS valid_order_count_10,
                    SUM(CASE WHEN hours = 11 THEN valid_order_count ELSE 0 END) AS valid_order_count_11,
                    SUM(CASE WHEN hours = 12 THEN valid_order_count ELSE 0 END) AS valid_order_count_12,
                    SUM(CASE WHEN hours = 13 THEN valid_order_count ELSE 0 END) AS valid_order_count_13,
                    SUM(CASE WHEN hours = 14 THEN valid_order_count ELSE 0 END) AS valid_order_count_14,
                    SUM(CASE WHEN hours = 15 THEN valid_order_count ELSE 0 END) AS valid_order_count_15,
                    SUM(CASE WHEN hours = 16 THEN valid_order_count ELSE 0 END) AS valid_order_count_16,
                    SUM(CASE WHEN hours = 17 THEN valid_order_count ELSE 0 END) AS valid_order_count_17,
                    SUM(CASE WHEN hours = 18 THEN valid_order_count ELSE 0 END) AS valid_order_count_18,
                    SUM(CASE WHEN hours = 19 THEN valid_order_count ELSE 0 END) AS valid_order_count_19,
                    SUM(CASE WHEN hours = 20 THEN valid_order_count ELSE 0 END) AS valid_order_count_20,
                    SUM(CASE WHEN hours = 21 THEN valid_order_count ELSE 0 END) AS valid_order_count_21,
                    SUM(CASE WHEN hours = 22 THEN valid_order_count ELSE 0 END) AS valid_order_count_22,
                    SUM(CASE WHEN hours = 23 THEN valid_order_count ELSE 0 END) AS valid_order_count_23,

                    SUM(CASE WHEN hours = 0 THEN discount_contribute ELSE 0 END) AS discount_contribute_00,
                    SUM(CASE WHEN hours = 1 THEN discount_contribute ELSE 0 END) AS discount_contribute_01,
                    SUM(CASE WHEN hours = 2 THEN discount_contribute ELSE 0 END) AS discount_contribute_02,
                    SUM(CASE WHEN hours = 3 THEN discount_contribute ELSE 0 END) AS discount_contribute_03,
                    SUM(CASE WHEN hours = 4 THEN discount_contribute ELSE 0 END) AS discount_contribute_04,
                    SUM(CASE WHEN hours = 5 THEN discount_contribute ELSE 0 END) AS discount_contribute_05,
                    SUM(CASE WHEN hours = 6 THEN discount_contribute ELSE 0 END) AS discount_contribute_06,
                    SUM(CASE WHEN hours = 7 THEN discount_contribute ELSE 0 END) AS discount_contribute_07,
                    SUM(CASE WHEN hours = 8 THEN discount_contribute ELSE 0 END) AS discount_contribute_08,
                    SUM(CASE WHEN hours = 9 THEN discount_contribute ELSE 0 END) AS discount_contribute_09,
                    SUM(CASE WHEN hours = 10 THEN discount_contribute ELSE 0 END) AS discount_contribute_10,
                    SUM(CASE WHEN hours = 11 THEN discount_contribute ELSE 0 END) AS discount_contribute_11,
                    SUM(CASE WHEN hours = 12 THEN discount_contribute ELSE 0 END) AS discount_contribute_12,
                    SUM(CASE WHEN hours = 13 THEN discount_contribute ELSE 0 END) AS discount_contribute_13,
                    SUM(CASE WHEN hours = 14 THEN discount_contribute ELSE 0 END) AS discount_contribute_14,
                    SUM(CASE WHEN hours = 15 THEN discount_contribute ELSE 0 END) AS discount_contribute_15,
                    SUM(CASE WHEN hours = 16 THEN discount_contribute ELSE 0 END) AS discount_contribute_16,
                    SUM(CASE WHEN hours = 17 THEN discount_contribute ELSE 0 END) AS discount_contribute_17,
                    SUM(CASE WHEN hours = 18 THEN discount_contribute ELSE 0 END) AS discount_contribute_18,
                    SUM(CASE WHEN hours = 19 THEN discount_contribute ELSE 0 END) AS discount_contribute_19,
                    SUM(CASE WHEN hours = 20 THEN discount_contribute ELSE 0 END) AS discount_contribute_20,
                    SUM(CASE WHEN hours = 21 THEN discount_contribute ELSE 0 END) AS discount_contribute_21,
                    SUM(CASE WHEN hours = 22 THEN discount_contribute ELSE 0 END) AS discount_contribute_22,
                    SUM(CASE WHEN hours = 23 THEN discount_contribute ELSE 0 END) AS discount_contribute_23,

                    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23

				FROM base_tickets
				GROUP BY
				    geo_id, branch_id, company_id, region_id,store_type
			), base_tickets_by_date_and_region AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,

					SUM(business_amount) AS business_amount,
					SUM(real_amount) AS real_amount,
					SUM(expend_amount) AS expend_amount,
					SUM(valid_order_count) AS valid_order_count,
					SUM(discount_contribute) AS discount_contribute,
					SUM(transfer_real_amount) AS transfer_real_amount,

					JSON_BUILD_OBJECT(
                                                'h00', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_00),
                                                        'real_amount', SUM(real_amount_00),
                                                        'expend_amount', SUM(expend_amount_00),
                                                        'valid_order_count', SUM(valid_order_count_00),
                                                        'discount_contribute', SUM(discount_contribute_00),
                                                        'transfer_real_amount', SUM(transfer_real_amount_00)
                                                ),
                                                'h01', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_01),
                                                        'real_amount', SUM(real_amount_01),
                                                        'expend_amount', SUM(expend_amount_01),
                                                        'valid_order_count', SUM(valid_order_count_01),
                                                        'discount_contribute', SUM(discount_contribute_01),
                                                        'transfer_real_amount', SUM(transfer_real_amount_01)
                                                ),
                                                'h02', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_02),
                                                        'real_amount', SUM(real_amount_02),
                                                        'expend_amount', SUM(expend_amount_02),
                                                        'valid_order_count', SUM(valid_order_count_02),
                                                        'discount_contribute', SUM(discount_contribute_02),
                                                        'transfer_real_amount', SUM(transfer_real_amount_02)
                                                ),
                                                'h03', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_03),
                                                        'real_amount', SUM(real_amount_03),
                                                        'expend_amount', SUM(expend_amount_03),
                                                        'valid_order_count', SUM(valid_order_count_03),
                                                        'discount_contribute', SUM(discount_contribute_03),
                                                        'transfer_real_amount', SUM(transfer_real_amount_03)
                                                ),
                                                'h04', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_04),
                                                        'real_amount', SUM(real_amount_04),
                                                        'expend_amount', SUM(expend_amount_04),
                                                        'valid_order_count', SUM(valid_order_count_04),
                                                        'discount_contribute', SUM(discount_contribute_04),
                                                        'transfer_real_amount', SUM(transfer_real_amount_04)
                                                ),
                                                'h05', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_05),
                                                        'real_amount', SUM(real_amount_05),
                                                        'expend_amount', SUM(expend_amount_05),
                                                        'valid_order_count', SUM(valid_order_count_05),
                                                        'discount_contribute', SUM(discount_contribute_05),
                                                        'transfer_real_amount', SUM(transfer_real_amount_05)
                                                ),
                                                'h06', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_06),
                                                        'real_amount', SUM(real_amount_06),
                                                        'expend_amount', SUM(expend_amount_06),
                                                        'valid_order_count', SUM(valid_order_count_06),
                                                        'discount_contribute', SUM(discount_contribute_06),
                                                        'transfer_real_amount', SUM(transfer_real_amount_06)
                                                ),
                                                'h07', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_07),
                                                        'real_amount', SUM(real_amount_07),
                                                        'expend_amount', SUM(expend_amount_07),
                                                        'valid_order_count', SUM(valid_order_count_07),
                                                        'discount_contribute', SUM(discount_contribute_07),
                                                        'transfer_real_amount', SUM(transfer_real_amount_07)
                                                ),
                                                'h08', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_08),
                                                        'real_amount', SUM(real_amount_08),
                                                        'expend_amount', SUM(expend_amount_08),
                                                        'valid_order_count', SUM(valid_order_count_08),
                                                        'discount_contribute', SUM(discount_contribute_08),
                                                        'transfer_real_amount', SUM(transfer_real_amount_08)
                                                ),
                                                'h09', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_09),
                                                        'real_amount', SUM(real_amount_09),
                                                        'expend_amount', SUM(expend_amount_09),
                                                        'valid_order_count', SUM(valid_order_count_09),
                                                        'discount_contribute', SUM(discount_contribute_09),
                                                        'transfer_real_amount', SUM(transfer_real_amount_09)
                                                ),
                                                'h10', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_10),
                                                        'real_amount', SUM(real_amount_10),
                                                        'expend_amount', SUM(expend_amount_10),
                                                        'valid_order_count', SUM(valid_order_count_10),
                                                        'discount_contribute', SUM(discount_contribute_10),
                                                        'transfer_real_amount', SUM(transfer_real_amount_10)
                                                ),
                                                'h11', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_11),
                                                        'real_amount', SUM(real_amount_11),
                                                        'expend_amount', SUM(expend_amount_11),
                                                        'valid_order_count', SUM(valid_order_count_11),
                                                        'discount_contribute', SUM(discount_contribute_11),
                                                        'transfer_real_amount', SUM(transfer_real_amount_11)
                                                ),
                                                'h12', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_12),
                                                        'real_amount', SUM(real_amount_12),
                                                        'expend_amount', SUM(expend_amount_12),
                                                        'valid_order_count', SUM(valid_order_count_12),
                                                        'discount_contribute', SUM(discount_contribute_12),
                                                        'transfer_real_amount', SUM(transfer_real_amount_12)
                                                ),
                                                'h13', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_13),
                                                        'real_amount', SUM(real_amount_13),
                                                        'expend_amount', SUM(expend_amount_13),
                                                        'valid_order_count', SUM(valid_order_count_13),
                                                        'discount_contribute', SUM(discount_contribute_13),
                                                        'transfer_real_amount', SUM(transfer_real_amount_13)
                                                ),
                                                'h14', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_14),
                                                        'real_amount', SUM(real_amount_14),
                                                        'expend_amount', SUM(expend_amount_14),
                                                        'valid_order_count', SUM(valid_order_count_14),
                                                        'discount_contribute', SUM(discount_contribute_14),
                                                        'transfer_real_amount', SUM(transfer_real_amount_14)
                                                ),
                                                'h15', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_15),
                                                        'real_amount', SUM(real_amount_15),
                                                        'expend_amount', SUM(expend_amount_15),
                                                        'valid_order_count', SUM(valid_order_count_15),
                                                        'discount_contribute', SUM(discount_contribute_15),
                                                        'transfer_real_amount', SUM(transfer_real_amount_15)
                                                ),
                                                'h16', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_16),
                                                        'real_amount', SUM(real_amount_16),
                                                        'expend_amount', SUM(expend_amount_16),
                                                        'valid_order_count', SUM(valid_order_count_16),
                                                        'discount_contribute', SUM(discount_contribute_16),
                                                        'transfer_real_amount', SUM(transfer_real_amount_16)
                                                ),
                                                'h17', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_17),
                                                        'real_amount', SUM(real_amount_17),
                                                        'expend_amount', SUM(expend_amount_17),
                                                        'valid_order_count', SUM(valid_order_count_17),
                                                        'discount_contribute', SUM(discount_contribute_17),
                                                        'transfer_real_amount', SUM(transfer_real_amount_17)
                                                ),
                                                'h18', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_18),
                                                        'real_amount', SUM(real_amount_18),
                                                        'expend_amount', SUM(expend_amount_18),
                                                        'valid_order_count', SUM(valid_order_count_18),
                                                        'discount_contribute', SUM(discount_contribute_18),
                                                        'transfer_real_amount', SUM(transfer_real_amount_18)
                                                ),
                                                'h19', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_19),
                                                        'real_amount', SUM(real_amount_19),
                                                        'expend_amount', SUM(expend_amount_19),
                                                        'valid_order_count', SUM(valid_order_count_19),
                                                        'discount_contribute', SUM(discount_contribute_19),
                                                        'transfer_real_amount', SUM(transfer_real_amount_19)
                                                ),
                                                'h20', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_20),
                                                        'real_amount', SUM(real_amount_20),
                                                        'expend_amount', SUM(expend_amount_20),
                                                        'valid_order_count', SUM(valid_order_count_20),
                                                        'discount_contribute', SUM(discount_contribute_20),
                                                        'transfer_real_amount', SUM(transfer_real_amount_20)
                                                ),
                                                'h21', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_21),
                                                        'real_amount', SUM(real_amount_21),
                                                        'expend_amount', SUM(expend_amount_21),
                                                        'valid_order_count', SUM(valid_order_count_21),
                                                        'discount_contribute', SUM(discount_contribute_21),
                                                        'transfer_real_amount', SUM(transfer_real_amount_21)
                                                ),
                                                'h22', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_22),
                                                        'real_amount', SUM(real_amount_22),
                                                        'expend_amount', SUM(expend_amount_22),
                                                        'valid_order_count', SUM(valid_order_count_22),
                                                        'discount_contribute', SUM(discount_contribute_22),
                                                        'transfer_real_amount', SUM(transfer_real_amount_22)
                                                ),
                                                'h23', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_23),
                                                        'real_amount', SUM(real_amount_23),
                                                        'expend_amount', SUM(expend_amount_23),
                                                        'valid_order_count', SUM(valid_order_count_23),
                                                        'discount_contribute', SUM(discount_contribute_23),
                                                        'transfer_real_amount', SUM(transfer_real_amount_23)
                                                )
                                        ) AS "data"

				FROM base_tickets_for_period
				GROUP BY
					geo_id, branch_id, company_id, region_id,store_type
			)
			SELECT
				'' AS bus_date,
			    bt.geo_id AS geo_id,
			    bt.branch_id AS branch_id,
			    bt.company_id AS company_id,
				bt.region_id AS region_id,
				bt.store_type AS store_type,
			    bd.days AS business_days,
				bt.business_amount AS business_amount,
				bt.real_amount AS real_amount,
			    bt.expend_amount AS expend_amount,
			    bt.valid_order_count AS valid_order_count,
			    bt.discount_contribute AS discount_contribute,
			    bt.transfer_real_amount AS transfer_real_amount,

				COALESCE(bt.data, '{}'::json) AS data

			FROM
				base_tickets_by_date_and_region bt
					LEFT JOIN business_days bd
			            on bd.region_id = bt.region_id
            ORDER BY bt.region_id
		`
		totalSql = `
		With base_limit AS (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
					AND fact.is_non_sales = FALSE
				GROUP BY
					{REGION_ID}
			)
			select count(*) from base_limit;
`
	} else {
		rowSQL = `
		--门店时段报表rows
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					AND fact.is_non_sales = FALSE
					GROUP BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					fact.bus_date AS bus_date,
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
					AND is_non_sales = FALSE
				GROUP BY
					fact.bus_date,
					{REGION_ID}
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
				{LIMIT}
			),
			base_tickets AS (
				SELECT
					fact.bus_date AS bus_date,
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,

					EXTRACT(HOUR FROM fact.order_time AT TIME ZONE store_caches.time_zone)        AS hours,
				    COALESCE(SUM(amount_0), 0) AS business_amount, --营业额
                    COALESCE(SUM(amount_2-rounding), 0) AS real_amount, --实收金额(交易)
                    COALESCE(SUM(amount_1+rounding), 0) AS expend_amount, --支出(交易)
                    COALESCE(SUM(eticket_count), 0) AS valid_order_count, --有效订单数
                    COALESCE(SUM(amount_0+surcharge_amount-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                    COALESCE(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute --优惠组成(财务)

				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
                        INNER JOIN base_limit
                                    ON fact.bus_date = base_limit.bus_date
                                        AND {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
					AND fact.is_non_sales = FALSE
				GROUP BY
					fact.bus_date, store_caches.company_info, {REGION_ID}, EXTRACT(HOUR FROM fact.order_time AT TIME ZONE store_caches.time_zone)
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
			), base_tickets_for_period AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,

					SUM(business_amount) AS business_amount,
					SUM(real_amount) AS real_amount,
					SUM(expend_amount) AS expend_amount,
					SUM(valid_order_count) AS valid_order_count,
					SUM(discount_contribute) AS discount_contribute,
					SUM(transfer_real_amount) AS transfer_real_amount,

					SUM(CASE WHEN hours = 0 THEN business_amount ELSE 0 END) AS business_amount_00,
                    SUM(CASE WHEN hours = 1 THEN business_amount ELSE 0 END) AS business_amount_01,
                    SUM(CASE WHEN hours = 2 THEN business_amount ELSE 0 END) AS business_amount_02,
                    SUM(CASE WHEN hours = 3 THEN business_amount ELSE 0 END) AS business_amount_03,
                    SUM(CASE WHEN hours = 4 THEN business_amount ELSE 0 END) AS business_amount_04,
                    SUM(CASE WHEN hours = 5 THEN business_amount ELSE 0 END) AS business_amount_05,
                    SUM(CASE WHEN hours = 6 THEN business_amount ELSE 0 END) AS business_amount_06,
                    SUM(CASE WHEN hours = 7 THEN business_amount ELSE 0 END) AS business_amount_07,
                    SUM(CASE WHEN hours = 8 THEN business_amount ELSE 0 END) AS business_amount_08,
                    SUM(CASE WHEN hours = 9 THEN business_amount ELSE 0 END) AS business_amount_09,
                    SUM(CASE WHEN hours = 10 THEN business_amount ELSE 0 END) AS business_amount_10,
                    SUM(CASE WHEN hours = 11 THEN business_amount ELSE 0 END) AS business_amount_11,
                    SUM(CASE WHEN hours = 12 THEN business_amount ELSE 0 END) AS business_amount_12,
                    SUM(CASE WHEN hours = 13 THEN business_amount ELSE 0 END) AS business_amount_13,
                    SUM(CASE WHEN hours = 14 THEN business_amount ELSE 0 END) AS business_amount_14,
                    SUM(CASE WHEN hours = 15 THEN business_amount ELSE 0 END) AS business_amount_15,
                    SUM(CASE WHEN hours = 16 THEN business_amount ELSE 0 END) AS business_amount_16,
                    SUM(CASE WHEN hours = 17 THEN business_amount ELSE 0 END) AS business_amount_17,
                    SUM(CASE WHEN hours = 18 THEN business_amount ELSE 0 END) AS business_amount_18,
                    SUM(CASE WHEN hours = 19 THEN business_amount ELSE 0 END) AS business_amount_19,
                    SUM(CASE WHEN hours = 20 THEN business_amount ELSE 0 END) AS business_amount_20,
                    SUM(CASE WHEN hours = 21 THEN business_amount ELSE 0 END) AS business_amount_21,
                    SUM(CASE WHEN hours = 22 THEN business_amount ELSE 0 END) AS business_amount_22,
                    SUM(CASE WHEN hours = 23 THEN business_amount ELSE 0 END) AS business_amount_23,

                    SUM(CASE WHEN hours = 0 THEN real_amount ELSE 0 END) AS real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN real_amount ELSE 0 END) AS real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN real_amount ELSE 0 END) AS real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN real_amount ELSE 0 END) AS real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN real_amount ELSE 0 END) AS real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN real_amount ELSE 0 END) AS real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN real_amount ELSE 0 END) AS real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN real_amount ELSE 0 END) AS real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN real_amount ELSE 0 END) AS real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN real_amount ELSE 0 END) AS real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN real_amount ELSE 0 END) AS real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN real_amount ELSE 0 END) AS real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN real_amount ELSE 0 END) AS real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN real_amount ELSE 0 END) AS real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN real_amount ELSE 0 END) AS real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN real_amount ELSE 0 END) AS real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN real_amount ELSE 0 END) AS real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN real_amount ELSE 0 END) AS real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN real_amount ELSE 0 END) AS real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN real_amount ELSE 0 END) AS real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN real_amount ELSE 0 END) AS real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN real_amount ELSE 0 END) AS real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN real_amount ELSE 0 END) AS real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN real_amount ELSE 0 END) AS real_amount_23,

                    SUM(CASE WHEN hours = 0 THEN expend_amount ELSE 0 END) AS expend_amount_00,
                    SUM(CASE WHEN hours = 1 THEN expend_amount ELSE 0 END) AS expend_amount_01,
                    SUM(CASE WHEN hours = 2 THEN expend_amount ELSE 0 END) AS expend_amount_02,
                    SUM(CASE WHEN hours = 3 THEN expend_amount ELSE 0 END) AS expend_amount_03,
                    SUM(CASE WHEN hours = 4 THEN expend_amount ELSE 0 END) AS expend_amount_04,
                    SUM(CASE WHEN hours = 5 THEN expend_amount ELSE 0 END) AS expend_amount_05,
                    SUM(CASE WHEN hours = 6 THEN expend_amount ELSE 0 END) AS expend_amount_06,
                    SUM(CASE WHEN hours = 7 THEN expend_amount ELSE 0 END) AS expend_amount_07,
                    SUM(CASE WHEN hours = 8 THEN expend_amount ELSE 0 END) AS expend_amount_08,
                    SUM(CASE WHEN hours = 9 THEN expend_amount ELSE 0 END) AS expend_amount_09,
                    SUM(CASE WHEN hours = 10 THEN expend_amount ELSE 0 END) AS expend_amount_10,
                    SUM(CASE WHEN hours = 11 THEN expend_amount ELSE 0 END) AS expend_amount_11,
                    SUM(CASE WHEN hours = 12 THEN expend_amount ELSE 0 END) AS expend_amount_12,
                    SUM(CASE WHEN hours = 13 THEN expend_amount ELSE 0 END) AS expend_amount_13,
                    SUM(CASE WHEN hours = 14 THEN expend_amount ELSE 0 END) AS expend_amount_14,
                    SUM(CASE WHEN hours = 15 THEN expend_amount ELSE 0 END) AS expend_amount_15,
                    SUM(CASE WHEN hours = 16 THEN expend_amount ELSE 0 END) AS expend_amount_16,
                    SUM(CASE WHEN hours = 17 THEN expend_amount ELSE 0 END) AS expend_amount_17,
                    SUM(CASE WHEN hours = 18 THEN expend_amount ELSE 0 END) AS expend_amount_18,
                    SUM(CASE WHEN hours = 19 THEN expend_amount ELSE 0 END) AS expend_amount_19,
                    SUM(CASE WHEN hours = 20 THEN expend_amount ELSE 0 END) AS expend_amount_20,
                    SUM(CASE WHEN hours = 21 THEN expend_amount ELSE 0 END) AS expend_amount_21,
                    SUM(CASE WHEN hours = 22 THEN expend_amount ELSE 0 END) AS expend_amount_22,
                    SUM(CASE WHEN hours = 23 THEN expend_amount ELSE 0 END) AS expend_amount_23,

                    SUM(CASE WHEN hours = 0 THEN valid_order_count ELSE 0 END) AS valid_order_count_00,
                    SUM(CASE WHEN hours = 1 THEN valid_order_count ELSE 0 END) AS valid_order_count_01,
                    SUM(CASE WHEN hours = 2 THEN valid_order_count ELSE 0 END) AS valid_order_count_02,
                    SUM(CASE WHEN hours = 3 THEN valid_order_count ELSE 0 END) AS valid_order_count_03,
                    SUM(CASE WHEN hours = 4 THEN valid_order_count ELSE 0 END) AS valid_order_count_04,
                    SUM(CASE WHEN hours = 5 THEN valid_order_count ELSE 0 END) AS valid_order_count_05,
                    SUM(CASE WHEN hours = 6 THEN valid_order_count ELSE 0 END) AS valid_order_count_06,
                    SUM(CASE WHEN hours = 7 THEN valid_order_count ELSE 0 END) AS valid_order_count_07,
                    SUM(CASE WHEN hours = 8 THEN valid_order_count ELSE 0 END) AS valid_order_count_08,
                    SUM(CASE WHEN hours = 9 THEN valid_order_count ELSE 0 END) AS valid_order_count_09,
                    SUM(CASE WHEN hours = 10 THEN valid_order_count ELSE 0 END) AS valid_order_count_10,
                    SUM(CASE WHEN hours = 11 THEN valid_order_count ELSE 0 END) AS valid_order_count_11,
                    SUM(CASE WHEN hours = 12 THEN valid_order_count ELSE 0 END) AS valid_order_count_12,
                    SUM(CASE WHEN hours = 13 THEN valid_order_count ELSE 0 END) AS valid_order_count_13,
                    SUM(CASE WHEN hours = 14 THEN valid_order_count ELSE 0 END) AS valid_order_count_14,
                    SUM(CASE WHEN hours = 15 THEN valid_order_count ELSE 0 END) AS valid_order_count_15,
                    SUM(CASE WHEN hours = 16 THEN valid_order_count ELSE 0 END) AS valid_order_count_16,
                    SUM(CASE WHEN hours = 17 THEN valid_order_count ELSE 0 END) AS valid_order_count_17,
                    SUM(CASE WHEN hours = 18 THEN valid_order_count ELSE 0 END) AS valid_order_count_18,
                    SUM(CASE WHEN hours = 19 THEN valid_order_count ELSE 0 END) AS valid_order_count_19,
                    SUM(CASE WHEN hours = 20 THEN valid_order_count ELSE 0 END) AS valid_order_count_20,
                    SUM(CASE WHEN hours = 21 THEN valid_order_count ELSE 0 END) AS valid_order_count_21,
                    SUM(CASE WHEN hours = 22 THEN valid_order_count ELSE 0 END) AS valid_order_count_22,
                    SUM(CASE WHEN hours = 23 THEN valid_order_count ELSE 0 END) AS valid_order_count_23,

                    SUM(CASE WHEN hours = 0 THEN discount_contribute ELSE 0 END) AS discount_contribute_00,
                    SUM(CASE WHEN hours = 1 THEN discount_contribute ELSE 0 END) AS discount_contribute_01,
                    SUM(CASE WHEN hours = 2 THEN discount_contribute ELSE 0 END) AS discount_contribute_02,
                    SUM(CASE WHEN hours = 3 THEN discount_contribute ELSE 0 END) AS discount_contribute_03,
                    SUM(CASE WHEN hours = 4 THEN discount_contribute ELSE 0 END) AS discount_contribute_04,
                    SUM(CASE WHEN hours = 5 THEN discount_contribute ELSE 0 END) AS discount_contribute_05,
                    SUM(CASE WHEN hours = 6 THEN discount_contribute ELSE 0 END) AS discount_contribute_06,
                    SUM(CASE WHEN hours = 7 THEN discount_contribute ELSE 0 END) AS discount_contribute_07,
                    SUM(CASE WHEN hours = 8 THEN discount_contribute ELSE 0 END) AS discount_contribute_08,
                    SUM(CASE WHEN hours = 9 THEN discount_contribute ELSE 0 END) AS discount_contribute_09,
                    SUM(CASE WHEN hours = 10 THEN discount_contribute ELSE 0 END) AS discount_contribute_10,
                    SUM(CASE WHEN hours = 11 THEN discount_contribute ELSE 0 END) AS discount_contribute_11,
                    SUM(CASE WHEN hours = 12 THEN discount_contribute ELSE 0 END) AS discount_contribute_12,
                    SUM(CASE WHEN hours = 13 THEN discount_contribute ELSE 0 END) AS discount_contribute_13,
                    SUM(CASE WHEN hours = 14 THEN discount_contribute ELSE 0 END) AS discount_contribute_14,
                    SUM(CASE WHEN hours = 15 THEN discount_contribute ELSE 0 END) AS discount_contribute_15,
                    SUM(CASE WHEN hours = 16 THEN discount_contribute ELSE 0 END) AS discount_contribute_16,
                    SUM(CASE WHEN hours = 17 THEN discount_contribute ELSE 0 END) AS discount_contribute_17,
                    SUM(CASE WHEN hours = 18 THEN discount_contribute ELSE 0 END) AS discount_contribute_18,
                    SUM(CASE WHEN hours = 19 THEN discount_contribute ELSE 0 END) AS discount_contribute_19,
                    SUM(CASE WHEN hours = 20 THEN discount_contribute ELSE 0 END) AS discount_contribute_20,
                    SUM(CASE WHEN hours = 21 THEN discount_contribute ELSE 0 END) AS discount_contribute_21,
                    SUM(CASE WHEN hours = 22 THEN discount_contribute ELSE 0 END) AS discount_contribute_22,
                    SUM(CASE WHEN hours = 23 THEN discount_contribute ELSE 0 END) AS discount_contribute_23,

                    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23

				FROM base_tickets
				GROUP BY
				    bus_date, geo_id, branch_id, company_id, region_id, store_type
			), base_tickets_by_date_and_region AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,

					SUM(business_amount) AS business_amount,
					SUM(real_amount) AS real_amount,
					SUM(expend_amount) AS expend_amount,
					SUM(valid_order_count) AS valid_order_count,
					SUM(discount_contribute) AS discount_contribute,
					SUM(transfer_real_amount) AS transfer_real_amount,

					JSON_BUILD_OBJECT(
                                                'h00', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_00),
                                                        'real_amount', SUM(real_amount_00),
                                                        'expend_amount', SUM(expend_amount_00),
                                                        'valid_order_count', SUM(valid_order_count_00),
                                                        'discount_contribute', SUM(discount_contribute_00),
                                                        'transfer_real_amount', SUM(transfer_real_amount_00)
                                                ),
                                                'h01', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_01),
                                                        'real_amount', SUM(real_amount_01),
                                                        'expend_amount', SUM(expend_amount_01),
                                                        'valid_order_count', SUM(valid_order_count_01),
                                                        'discount_contribute', SUM(discount_contribute_01),
                                                        'transfer_real_amount', SUM(transfer_real_amount_01)
                                                ),
                                                'h02', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_02),
                                                        'real_amount', SUM(real_amount_02),
                                                        'expend_amount', SUM(expend_amount_02),
                                                        'valid_order_count', SUM(valid_order_count_02),
                                                        'discount_contribute', SUM(discount_contribute_02),
                                                        'transfer_real_amount', SUM(transfer_real_amount_02)
                                                ),
                                                'h03', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_03),
                                                        'real_amount', SUM(real_amount_03),
                                                        'expend_amount', SUM(expend_amount_03),
                                                        'valid_order_count', SUM(valid_order_count_03),
                                                        'discount_contribute', SUM(discount_contribute_03),
                                                        'transfer_real_amount', SUM(transfer_real_amount_03)
                                                ),
                                                'h04', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_04),
                                                        'real_amount', SUM(real_amount_04),
                                                        'expend_amount', SUM(expend_amount_04),
                                                        'valid_order_count', SUM(valid_order_count_04),
                                                        'discount_contribute', SUM(discount_contribute_04),
                                                        'transfer_real_amount', SUM(transfer_real_amount_04)
                                                ),
                                                'h05', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_05),
                                                        'real_amount', SUM(real_amount_05),
                                                        'expend_amount', SUM(expend_amount_05),
                                                        'valid_order_count', SUM(valid_order_count_05),
                                                        'discount_contribute', SUM(discount_contribute_05),
                                                        'transfer_real_amount', SUM(transfer_real_amount_05)
                                                ),
                                                'h06', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_06),
                                                        'real_amount', SUM(real_amount_06),
                                                        'expend_amount', SUM(expend_amount_06),
                                                        'valid_order_count', SUM(valid_order_count_06),
                                                        'discount_contribute', SUM(discount_contribute_06),
                                                        'transfer_real_amount', SUM(transfer_real_amount_06)
                                                ),
                                                'h07', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_07),
                                                        'real_amount', SUM(real_amount_07),
                                                        'expend_amount', SUM(expend_amount_07),
                                                        'valid_order_count', SUM(valid_order_count_07),
                                                        'discount_contribute', SUM(discount_contribute_07),
                                                        'transfer_real_amount', SUM(transfer_real_amount_07)
                                                ),
                                                'h08', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_08),
                                                        'real_amount', SUM(real_amount_08),
                                                        'expend_amount', SUM(expend_amount_08),
                                                        'valid_order_count', SUM(valid_order_count_08),
                                                        'discount_contribute', SUM(discount_contribute_08),
                                                        'transfer_real_amount', SUM(transfer_real_amount_08)
                                                ),
                                                'h09', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_09),
                                                        'real_amount', SUM(real_amount_09),
                                                        'expend_amount', SUM(expend_amount_09),
                                                        'valid_order_count', SUM(valid_order_count_09),
                                                        'discount_contribute', SUM(discount_contribute_09),
                                                        'transfer_real_amount', SUM(transfer_real_amount_09)
                                                ),
                                                'h10', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_10),
                                                        'real_amount', SUM(real_amount_10),
                                                        'expend_amount', SUM(expend_amount_10),
                                                        'valid_order_count', SUM(valid_order_count_10),
                                                        'discount_contribute', SUM(discount_contribute_10),
                                                        'transfer_real_amount', SUM(transfer_real_amount_10)
                                                ),
                                                'h11', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_11),
                                                        'real_amount', SUM(real_amount_11),
                                                        'expend_amount', SUM(expend_amount_11),
                                                        'valid_order_count', SUM(valid_order_count_11),
                                                        'discount_contribute', SUM(discount_contribute_11),
                                                        'transfer_real_amount', SUM(transfer_real_amount_11)
                                                ),
                                                'h12', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_12),
                                                        'real_amount', SUM(real_amount_12),
                                                        'expend_amount', SUM(expend_amount_12),
                                                        'valid_order_count', SUM(valid_order_count_12),
                                                        'discount_contribute', SUM(discount_contribute_12),
                                                        'transfer_real_amount', SUM(transfer_real_amount_12)
                                                ),
                                                'h13', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_13),
                                                        'real_amount', SUM(real_amount_13),
                                                        'expend_amount', SUM(expend_amount_13),
                                                        'valid_order_count', SUM(valid_order_count_13),
                                                        'discount_contribute', SUM(discount_contribute_13),
                                                        'transfer_real_amount', SUM(transfer_real_amount_13)
                                                ),
                                                'h14', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_14),
                                                        'real_amount', SUM(real_amount_14),
                                                        'expend_amount', SUM(expend_amount_14),
                                                        'valid_order_count', SUM(valid_order_count_14),
                                                        'discount_contribute', SUM(discount_contribute_14),
                                                        'transfer_real_amount', SUM(transfer_real_amount_14)
                                                ),
                                                'h15', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_15),
                                                        'real_amount', SUM(real_amount_15),
                                                        'expend_amount', SUM(expend_amount_15),
                                                        'valid_order_count', SUM(valid_order_count_15),
                                                        'discount_contribute', SUM(discount_contribute_15),
                                                        'transfer_real_amount', SUM(transfer_real_amount_15)
                                                ),
                                                'h16', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_16),
                                                        'real_amount', SUM(real_amount_16),
                                                        'expend_amount', SUM(expend_amount_16),
                                                        'valid_order_count', SUM(valid_order_count_16),
                                                        'discount_contribute', SUM(discount_contribute_16),
                                                        'transfer_real_amount', SUM(transfer_real_amount_16)
                                                ),
                                                'h17', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_17),
                                                        'real_amount', SUM(real_amount_17),
                                                        'expend_amount', SUM(expend_amount_17),
                                                        'valid_order_count', SUM(valid_order_count_17),
                                                        'discount_contribute', SUM(discount_contribute_17),
                                                        'transfer_real_amount', SUM(transfer_real_amount_17)
                                                ),
                                                'h18', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_18),
                                                        'real_amount', SUM(real_amount_18),
                                                        'expend_amount', SUM(expend_amount_18),
                                                        'valid_order_count', SUM(valid_order_count_18),
                                                        'discount_contribute', SUM(discount_contribute_18),
                                                        'transfer_real_amount', SUM(transfer_real_amount_18)
                                                ),
                                                'h19', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_19),
                                                        'real_amount', SUM(real_amount_19),
                                                        'expend_amount', SUM(expend_amount_19),
                                                        'valid_order_count', SUM(valid_order_count_19),
                                                        'discount_contribute', SUM(discount_contribute_19),
                                                        'transfer_real_amount', SUM(transfer_real_amount_19)
                                                ),
                                                'h20', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_20),
                                                        'real_amount', SUM(real_amount_20),
                                                        'expend_amount', SUM(expend_amount_20),
                                                        'valid_order_count', SUM(valid_order_count_20),
                                                        'discount_contribute', SUM(discount_contribute_20),
                                                        'transfer_real_amount', SUM(transfer_real_amount_20)
                                                ),
                                                'h21', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_21),
                                                        'real_amount', SUM(real_amount_21),
                                                        'expend_amount', SUM(expend_amount_21),
                                                        'valid_order_count', SUM(valid_order_count_21),
                                                        'discount_contribute', SUM(discount_contribute_21),
                                                        'transfer_real_amount', SUM(transfer_real_amount_21)
                                                ),
                                                'h22', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_22),
                                                        'real_amount', SUM(real_amount_22),
                                                        'expend_amount', SUM(expend_amount_22),
                                                        'valid_order_count', SUM(valid_order_count_22),
                                                        'discount_contribute', SUM(discount_contribute_22),
                                                        'transfer_real_amount', SUM(transfer_real_amount_22)
                                                ),
                                                'h23', JSON_BUILD_OBJECT(
                                                        'business_amount', SUM(business_amount_23),
                                                        'real_amount', SUM(real_amount_23),
                                                        'expend_amount', SUM(expend_amount_23),
                                                        'valid_order_count', SUM(valid_order_count_23),
                                                        'discount_contribute', SUM(discount_contribute_23),
                                                        'transfer_real_amount', SUM(transfer_real_amount_23)
                                                )
                                        ) AS "data"

				FROM base_tickets_for_period
				GROUP BY
					bus_date, geo_id, branch_id, company_id, region_id, store_type
			)
			SELECT
				to_char(bt.bus_date,'YYYY-MM-DD') AS bus_date,
			    bt.geo_id AS geo_id,
			    bt.branch_id AS branch_id,
			    bt.company_id AS company_id,
				bt.region_id AS region_id,
				bt.store_type AS store_type,
			    bd.days AS business_days,
				bt.business_amount AS business_amount,
				bt.real_amount AS real_amount,
			    bt.expend_amount AS expend_amount,
			    bt.valid_order_count AS valid_order_count,
			    bt.discount_contribute AS discount_contribute,
			    bt.transfer_real_amount AS transfer_real_amount,

				COALESCE(bt.data, '{}'::json) AS data

			FROM
				base_tickets_by_date_and_region bt
					LEFT JOIN business_days bd
			            on bd.region_id = bt.region_id
            ORDER BY bus_date DESC, bt.region_id
		`
		totalSql = `
		With base_limit AS (
				SELECT
					fact.bus_date AS bus_date,
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				AND fact.is_non_sales = FALSE
				GROUP BY
					fact.bus_date,
					{REGION_ID}
			)
			select count(*) from base_limit;

	`
	}

	whereSQL := getWhereSQLForStorePeriodSalesV2(condition)
	regionSQL := getRegionSQLForStoreSalesV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	totalSql = strings.ReplaceAll(totalSql, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	totalSql = strings.ReplaceAll(totalSql, "{REGION_ID}", regionSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	return rowSQL, totalSql
}

func getWhereSQLForStorePeriodSalesV2(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQLV2(condition)
	// 订单类型过滤条件
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	periodSQL := ""
	if len(condition.Period) != 0 {
		periodSQL = fmt.Sprintf(`
			 AND EXTRACT(HOUR FROM fact.order_time AT TIME ZONE store_caches.time_zone) IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	storeTypeSQL := ""
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	openStatusSQL := ""
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, periodSQL, orderTypeSQL, storeSQL, storeTypeSQL, openStatusSQL)
	return whereSQL
}
