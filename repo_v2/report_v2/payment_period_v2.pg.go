package report_v2

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) PaymentPeriodV2(ctx context.Context, condition *model.RepoCondition) (*report.PaymentPeriodResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForPaymentPeriodV2(trackId, condition)
	ch1 := make(chan []*report.PaymentPeriodV2, 1)
	go queryDBWithChanForPaymentPeriodV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	response := &report.PaymentPeriodResponseV2{
		Rows: rows,
	}
	return response, nil
}

func queryDBWithChanForPaymentPeriodV2(id int64, db *sql.DB, sql string, ch chan []*report.PaymentPeriodV2) {
	ch <- queryDBForPaymentPeriodV2(id, db, sql)
}

func queryDBForPaymentPeriodV2(trackId int64, db *sql.DB, s string) []*report.PaymentPeriodV2 {
	results := make([]*report.PaymentPeriodV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PaymentPeriodV2)
		var value json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.PaymentId,
			&f.PaymentCode,
			&f.PaymentName,
			&f.Receivable,
			&f.PayAmount,
			&f.TransferRealAmount,
			&f.PaymentTransferAmount,
			&f.ItemCount,
			&f.ItemCountReturned,
			&f.Rounding,
			&f.OverflowAmount,
			&f.ChangeAmount,
			&value,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(value, &f.Data)
		if err != nil {
			logger.Pre().Error("支付时段data结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForPaymentPeriodV2(trackId int64, condition *model.RepoCondition) string {
	var rowSQL string // 行sql

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			--支付时段报表 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						store_id AS region_id
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date, store_id
					ORDER BY bus_date DESC, store_id
				) T
				GROUP BY region_id
			), base as (
			    select
			        fact.bus_date AS bus_date,
					COALESCE(fact.store_id, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
					COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name
			    from sales_payment_amounts fact
			    left join store_caches on fact.store_id = store_caches.id
			    where
			        {WHERE}
			    group by fact.bus_date, fact.store_id, fact.payment_id
            ),
			base_payments AS (
				SELECT
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE(store_id,0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					base.payment_code AS payment_code,
					base.payment_name AS payment_name,

					extract(hour from fact.order_time) AS hours,
				    COALESCE(SUM(receivable), 0) AS receivable, --应付金额
                    COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
                    COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
                    COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
                    COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
                    COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned, --支付退单数
				    COALESCE(sum(rounding), 0) AS rounding, --抹零
                    COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
                    COALESCE(sum(change_amount), 0) AS change_amount --找零

				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
					left join base on base.region_id=fact.store_id and base.payment_id=fact.payment_id
				WHERE
					{WHERE}
				GROUP BY
					store_caches.company_info,store_id, fact.payment_id,base.payment_code,base.payment_name, extract(hour from fact.order_time)
				ORDER BY
					store_id
			), base_payments_for_period AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
					payment_id,
					payment_code,
				    payment_name,
					SUM(receivable) AS receivable,
					SUM(pay_amount) AS pay_amount,
					SUM(transfer_real_amount) AS transfer_real_amount,
					SUM(payment_transfer_amount) AS payment_transfer_amount,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(rounding) AS rounding,
					SUM(overflow_amount) AS overflow_amount,
					SUM(change_amount) AS change_amount,

					SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                    SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                    SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                    SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                    SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                    SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                    SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                    SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                    SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                    SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                    SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                    SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                    SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                    SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                    SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                    SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                    SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                    SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                    SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                    SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                    SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                    SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                    SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                    SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

					SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
					SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
					SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
					SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
					SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
					SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
					SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
					SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
					SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
					SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
					SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
					SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
					SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
					SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
					SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
					SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
					SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
					SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
					SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
					SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
					SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
					SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
					SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
					SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

				    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
					SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
					SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
					SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
					SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
					SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
					SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
					SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
					SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
					SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
					SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
					SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
					SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
					SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
					SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
					SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
					SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
					SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
					SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
					SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
					SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
					SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
					SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
					SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

				    SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
					SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
					SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
					SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
					SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
					SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
					SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
					SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
					SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
					SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
					SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
					SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
					SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
					SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
					SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
					SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
					SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
					SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
					SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
					SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
					SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
					SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
					SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
					SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

					SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
					SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
					SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
					SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
					SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
					SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
					SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
					SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
					SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
					SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
					SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
					SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
					SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
					SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
					SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
					SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
					SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
					SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
					SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
					SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
					SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
					SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
					SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
					SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

				    SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
					SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
					SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
					SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
					SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
					SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
					SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
					SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
					SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
					SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
					SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
					SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
					SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
					SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
					SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
					SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
					SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
					SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
					SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
					SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
					SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
					SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
					SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
					SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23,

				    SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
					SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
					SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
					SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
					SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
					SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
					SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
					SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
					SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
					SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
					SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
					SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
					SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
					SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
					SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
					SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
					SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
					SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
					SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
					SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
					SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
					SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
					SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
					SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

				    SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
					SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
					SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
					SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
					SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
					SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
					SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
					SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
					SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
					SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
					SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
					SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
					SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
					SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
					SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
					SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
					SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
					SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
					SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
					SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
					SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
					SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
					SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
					SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

				    SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
					SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
					SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
					SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
					SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
					SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
					SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
					SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
					SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
					SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
					SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
					SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
					SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
					SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
					SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
					SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
					SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
					SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
					SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
					SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
					SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
					SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
					SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
					SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23

				FROM base_payments
				GROUP BY
					geo_id, branch_id, company_id, region_id, store_type, payment_id, payment_code, payment_name
			), base_payments_by_date_and_region_for_payment AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
				    payment_id,
					payment_code,
					payment_name,

					SUM(receivable) AS receivable,
					SUM(pay_amount) AS pay_amount,
					SUM(transfer_real_amount) AS transfer_real_amount,
					SUM(payment_transfer_amount) AS payment_transfer_amount,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(rounding) AS rounding,
					SUM(overflow_amount) AS overflow_amount,
					SUM(change_amount) AS change_amount,

					JSON_BUILD_OBJECT(
						'h00', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_00),
							'pay_amount', SUM(pay_amount_00),
							'transfer_real_amount', SUM(transfer_real_amount_00),
							'payment_transfer_amount', SUM(payment_transfer_amount_00),
							'item_count', SUM(item_count_00),
							'item_count_returned', SUM(item_count_returned_00),
							'rounding', SUM(rounding_00),
							'overflow_amount', SUM(overflow_amount_00),
							'change_amount', SUM(change_amount_00)
						),
						'h01', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_01),
							'pay_amount', SUM(pay_amount_01),
							'transfer_real_amount', SUM(transfer_real_amount_01),
							'payment_transfer_amount', SUM(payment_transfer_amount_01),
							'item_count', SUM(item_count_01),
							'item_count_returned', SUM(item_count_returned_01),
							'rounding', SUM(rounding_01),
							'overflow_amount', SUM(overflow_amount_01),
							'change_amount', SUM(change_amount_01)
						),
						'h02', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_02),
							'pay_amount', SUM(pay_amount_02),
							'transfer_real_amount', SUM(transfer_real_amount_02),
							'payment_transfer_amount', SUM(payment_transfer_amount_02),
							'item_count', SUM(item_count_02),
							'item_count_returned', SUM(item_count_returned_02),
							'rounding', SUM(rounding_02),
							'overflow_amount', SUM(overflow_amount_02),
							'change_amount', SUM(change_amount_02)
						),
						'h03', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_03),
							'pay_amount', SUM(pay_amount_03),
							'transfer_real_amount', SUM(transfer_real_amount_03),
							'payment_transfer_amount', SUM(payment_transfer_amount_03),
							'item_count', SUM(item_count_03),
							'item_count_returned', SUM(item_count_returned_03),
							'rounding', SUM(rounding_03),
							'overflow_amount', SUM(overflow_amount_03),
							'change_amount', SUM(change_amount_03)
						),
						'h04', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_04),
							'pay_amount', SUM(pay_amount_04),
							'transfer_real_amount', SUM(transfer_real_amount_04),
							'payment_transfer_amount', SUM(payment_transfer_amount_04),
							'item_count', SUM(item_count_04),
							'item_count_returned', SUM(item_count_returned_04),
							'rounding', SUM(rounding_04),
							'overflow_amount', SUM(overflow_amount_04),
							'change_amount', SUM(change_amount_04)
						),
						'h05', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_05),
							'pay_amount', SUM(pay_amount_05),
							'transfer_real_amount', SUM(transfer_real_amount_05),
							'payment_transfer_amount', SUM(payment_transfer_amount_05),
							'item_count', SUM(item_count_05),
							'item_count_returned', SUM(item_count_returned_05),
							'rounding', SUM(rounding_05),
							'overflow_amount', SUM(overflow_amount_05),
							'change_amount', SUM(change_amount_05)
						),
						'h06', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_06),
							'pay_amount', SUM(pay_amount_06),
							'transfer_real_amount', SUM(transfer_real_amount_06),
							'payment_transfer_amount', SUM(payment_transfer_amount_06),
							'item_count', SUM(item_count_06),
							'item_count_returned', SUM(item_count_returned_06),
							'rounding', SUM(rounding_06),
							'overflow_amount', SUM(overflow_amount_06),
							'change_amount', SUM(change_amount_06)
						),
						'h07', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_07),
							'pay_amount', SUM(pay_amount_07),
							'transfer_real_amount', SUM(transfer_real_amount_07),
							'payment_transfer_amount', SUM(payment_transfer_amount_07),
							'item_count', SUM(item_count_07),
							'item_count_returned', SUM(item_count_returned_07),
							'rounding', SUM(rounding_07),
							'overflow_amount', SUM(overflow_amount_07),
							'change_amount', SUM(change_amount_07)
						),
						'h08', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_08),
							'pay_amount', SUM(pay_amount_08),
							'transfer_real_amount', SUM(transfer_real_amount_08),
							'payment_transfer_amount', SUM(payment_transfer_amount_08),
							'item_count', SUM(item_count_08),
							'item_count_returned', SUM(item_count_returned_08),
							'rounding', SUM(rounding_08),
							'overflow_amount', SUM(overflow_amount_08),
							'change_amount', SUM(change_amount_08)
						),
						'h09', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_09),
							'pay_amount', SUM(pay_amount_09),
							'transfer_real_amount', SUM(transfer_real_amount_09),
							'payment_transfer_amount', SUM(payment_transfer_amount_09),
							'item_count', SUM(item_count_09),
							'item_count_returned', SUM(item_count_returned_09),
							'rounding', SUM(rounding_09),
							'overflow_amount', SUM(overflow_amount_09),
							'change_amount', SUM(change_amount_09)
						),
						'h10', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_10),
							'pay_amount', SUM(pay_amount_10),
							'transfer_real_amount', SUM(transfer_real_amount_10),
							'payment_transfer_amount', SUM(payment_transfer_amount_10),
							'item_count', SUM(item_count_10),
							'item_count_returned', SUM(item_count_returned_10),
							'rounding', SUM(rounding_10),
							'overflow_amount', SUM(overflow_amount_10),
							'change_amount', SUM(change_amount_10)
						),
						'h11', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_11),
							'pay_amount', SUM(pay_amount_11),
							'transfer_real_amount', SUM(transfer_real_amount_11),
							'payment_transfer_amount', SUM(payment_transfer_amount_11),
							'item_count', SUM(item_count_11),
							'item_count_returned', SUM(item_count_returned_11),
							'rounding', SUM(rounding_11),
							'overflow_amount', SUM(overflow_amount_11),
							'change_amount', SUM(change_amount_11)
						),
						'h12', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_12),
							'pay_amount', SUM(pay_amount_12),
							'transfer_real_amount', SUM(transfer_real_amount_12),
							'payment_transfer_amount', SUM(payment_transfer_amount_12),
							'item_count', SUM(item_count_12),
							'item_count_returned', SUM(item_count_returned_12),
							'rounding', SUM(rounding_12),
							'overflow_amount', SUM(overflow_amount_12),
							'change_amount', SUM(change_amount_12)
						),
						'h13', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_13),
							'pay_amount', SUM(pay_amount_13),
							'transfer_real_amount', SUM(transfer_real_amount_13),
							'payment_transfer_amount', SUM(payment_transfer_amount_13),
							'item_count', SUM(item_count_13),
							'item_count_returned', SUM(item_count_returned_13),
							'rounding', SUM(rounding_13),
							'overflow_amount', SUM(overflow_amount_13),
							'change_amount', SUM(change_amount_13)
						),
						'h14', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_14),
							'pay_amount', SUM(pay_amount_14),
							'transfer_real_amount', SUM(transfer_real_amount_14),
							'payment_transfer_amount', SUM(payment_transfer_amount_14),
							'item_count', SUM(item_count_14),
							'item_count_returned', SUM(item_count_returned_14),
							'rounding', SUM(rounding_14),
							'overflow_amount', SUM(overflow_amount_14),
							'change_amount', SUM(change_amount_14)
						),
						'h15', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_15),
							'pay_amount', SUM(pay_amount_15),
							'transfer_real_amount', SUM(transfer_real_amount_15),
							'payment_transfer_amount', SUM(payment_transfer_amount_15),
							'item_count', SUM(item_count_15),
							'item_count_returned', SUM(item_count_returned_15),
							'rounding', SUM(rounding_15),
							'overflow_amount', SUM(overflow_amount_15),
							'change_amount', SUM(change_amount_15)
						),
						'h16', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_16),
							'pay_amount', SUM(pay_amount_16),
							'transfer_real_amount', SUM(transfer_real_amount_16),
							'payment_transfer_amount', SUM(payment_transfer_amount_16),
							'item_count', SUM(item_count_16),
							'item_count_returned', SUM(item_count_returned_16),
							'rounding', SUM(rounding_16),
							'overflow_amount', SUM(overflow_amount_16),
							'change_amount', SUM(change_amount_16)
						),
						'h17', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_17),
							'pay_amount', SUM(pay_amount_17),
							'transfer_real_amount', SUM(transfer_real_amount_17),
							'payment_transfer_amount', SUM(payment_transfer_amount_17),
							'item_count', SUM(item_count_17),
							'item_count_returned', SUM(item_count_returned_17),
							'rounding', SUM(rounding_17),
							'overflow_amount', SUM(overflow_amount_17),
							'change_amount', SUM(change_amount_17)
						),
						'h18', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_18),
							'pay_amount', SUM(pay_amount_18),
							'transfer_real_amount', SUM(transfer_real_amount_18),
							'payment_transfer_amount', SUM(payment_transfer_amount_18),
							'item_count', SUM(item_count_18),
							'item_count_returned', SUM(item_count_returned_18),
							'rounding', SUM(rounding_18),
							'overflow_amount', SUM(overflow_amount_18),
							'change_amount', SUM(change_amount_18)
						),
						'h19', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_19),
							'pay_amount', SUM(pay_amount_19),
							'transfer_real_amount', SUM(transfer_real_amount_19),
							'payment_transfer_amount', SUM(payment_transfer_amount_19),
							'item_count', SUM(item_count_19),
							'item_count_returned', SUM(item_count_returned_19),
							'rounding', SUM(rounding_19),
							'overflow_amount', SUM(overflow_amount_19),
							'change_amount', SUM(change_amount_19)
						),
						'h20', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_20),
							'pay_amount', SUM(pay_amount_20),
							'transfer_real_amount', SUM(transfer_real_amount_20),
							'payment_transfer_amount', SUM(payment_transfer_amount_20),
							'item_count', SUM(item_count_20),
							'item_count_returned', SUM(item_count_returned_20),
							'rounding', SUM(rounding_20),
							'overflow_amount', SUM(overflow_amount_20),
							'change_amount', SUM(change_amount_20)
						),
						'h21', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_21),
							'pay_amount', SUM(pay_amount_21),
							'transfer_real_amount', SUM(transfer_real_amount_21),
							'payment_transfer_amount', SUM(payment_transfer_amount_21),
							'item_count', SUM(item_count_21),
							'item_count_returned', SUM(item_count_returned_21),
							'rounding', SUM(rounding_21),
							'overflow_amount', SUM(overflow_amount_21),
							'change_amount', SUM(change_amount_21)
						),
						'h22', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_22),
							'pay_amount', SUM(pay_amount_22),
							'transfer_real_amount', SUM(transfer_real_amount_22),
							'payment_transfer_amount', SUM(payment_transfer_amount_22),
							'item_count', SUM(item_count_22),
							'item_count_returned', SUM(item_count_returned_22),
							'rounding', SUM(rounding_22),
							'overflow_amount', SUM(overflow_amount_22),
							'change_amount', SUM(change_amount_22)
						),
						'h23', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_23),
							'pay_amount', SUM(pay_amount_23),
							'transfer_real_amount', SUM(transfer_real_amount_23),
							'payment_transfer_amount', SUM(payment_transfer_amount_23),
							'item_count', SUM(item_count_23),
							'item_count_returned', SUM(item_count_returned_23),
							'rounding', SUM(rounding_23),
							'overflow_amount', SUM(overflow_amount_23),
							'change_amount', SUM(change_amount_23)
						)
					) AS "data"

				FROM base_payments_for_period
				GROUP BY
					geo_id, branch_id, company_id, region_id, store_type, payment_id, payment_code, payment_name
			)
			SELECT
				'' AS bus_date,
			    for_payment.geo_id AS geo_id,
			    for_payment.branch_id AS branch_id,
			    for_payment.company_id AS company_id,
				for_payment.region_id AS region_id,
				for_payment.store_type AS store_type,
			    bd.days AS business_days,
				for_payment.payment_id AS payment_id,
				for_payment.payment_code AS payment_code,
				for_payment.payment_name AS payment_name,
				for_payment.receivable AS receivable,
			    for_payment.pay_amount AS pay_amount,
			    for_payment.transfer_real_amount AS transfer_real_amount,
			    for_payment.payment_transfer_amount AS payment_transfer_amount,
			    for_payment.item_count AS item_count,
			    for_payment.item_count_returned AS item_count_returned,
			    for_payment.rounding AS rounding,
			    for_payment.overflow_amount AS overflow_amount,
			    for_payment.change_amount AS change_amount,

				COALESCE(for_payment.data, '{}'::json) AS data

			FROM
				base_payments_by_date_and_region_for_payment for_payment
					LEFT JOIN business_days bd
						ON for_payment.region_id = bd.region_id
            ORDER BY for_payment.region_id,for_payment.payment_id
		`
	} else {
		rowSQL = `
			--支付时段报表 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						store_id AS region_id
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY store_id
				) T
				GROUP BY region_id
			), base as (
			    select
			        fact.bus_date AS bus_date,
					COALESCE(fact.store_id, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
					COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name
			    from sales_payment_amounts fact
			    left join store_caches on fact.store_id = store_caches.id
			    where
			        {WHERE}
			    group by fact.bus_date, fact.store_id, fact.payment_id
            ),
			base_payments AS (
				SELECT
					fact.bus_date AS bus_date,
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE(store_id,0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					base.payment_code AS payment_code,
					base.payment_name AS payment_name,

					extract(hour from fact.order_time) AS hours,
				    COALESCE(SUM(receivable), 0) AS receivable, --应付金额
                    COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
                    COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
                    COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
                    COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
                    COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned, --支付退单数
				    COALESCE(sum(rounding), 0) AS rounding, --抹零
                    COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
                    COALESCE(sum(change_amount), 0) AS change_amount --找零

				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
					left join base on base.bus_date=fact.bus_date and base.region_id=fact.store_id and base.payment_id=fact.payment_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date, store_caches.company_info,store_id, fact.payment_id,base.payment_code,
				    base.payment_name, extract(hour from fact.order_time)
				ORDER BY
					fact.bus_date DESC,
					store_id

			), base_payments_for_period AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
					payment_id,
					payment_code,
					payment_name,

					SUM(receivable) AS receivable,
					SUM(pay_amount) AS pay_amount,
					SUM(transfer_real_amount) AS transfer_real_amount,
					SUM(payment_transfer_amount) AS payment_transfer_amount,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(rounding) AS rounding,
					SUM(overflow_amount) AS overflow_amount,
					SUM(change_amount) AS change_amount,

					SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                    SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                    SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                    SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                    SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                    SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                    SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                    SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                    SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                    SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                    SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                    SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                    SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                    SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                    SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                    SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                    SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                    SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                    SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                    SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                    SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                    SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                    SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                    SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

					SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
					SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
					SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
					SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
					SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
					SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
					SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
					SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
					SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
					SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
					SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
					SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
					SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
					SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
					SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
					SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
					SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
					SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
					SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
					SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
					SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
					SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
					SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
					SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

				    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
					SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
					SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
					SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
					SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
					SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
					SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
					SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
					SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
					SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
					SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
					SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
					SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
					SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
					SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
					SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
					SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
					SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
					SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
					SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
					SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
					SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
					SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
					SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

				    SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
					SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
					SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
					SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
					SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
					SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
					SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
					SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
					SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
					SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
					SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
					SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
					SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
					SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
					SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
					SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
					SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
					SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
					SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
					SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
					SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
					SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
					SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
					SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

					SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
					SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
					SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
					SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
					SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
					SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
					SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
					SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
					SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
					SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
					SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
					SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
					SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
					SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
					SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
					SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
					SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
					SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
					SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
					SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
					SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
					SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
					SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
					SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

				    SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
					SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
					SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
					SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
					SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
					SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
					SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
					SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
					SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
					SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
					SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
					SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
					SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
					SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
					SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
					SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
					SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
					SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
					SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
					SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
					SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
					SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
					SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
					SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23,

				    SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
					SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
					SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
					SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
					SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
					SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
					SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
					SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
					SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
					SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
					SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
					SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
					SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
					SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
					SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
					SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
					SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
					SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
					SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
					SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
					SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
					SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
					SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
					SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

				    SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
					SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
					SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
					SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
					SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
					SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
					SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
					SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
					SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
					SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
					SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
					SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
					SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
					SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
					SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
					SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
					SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
					SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
					SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
					SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
					SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
					SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
					SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
					SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

				    SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
					SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
					SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
					SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
					SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
					SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
					SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
					SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
					SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
					SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
					SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
					SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
					SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
					SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
					SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
					SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
					SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
					SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
					SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
					SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
					SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
					SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
					SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
					SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23

				FROM base_payments
				GROUP BY
					bus_date, geo_id, branch_id, company_id, region_id, store_type, payment_id, payment_code, payment_name
			), base_payments_by_date_and_region_for_payment AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
				    payment_id,
					payment_code,
					payment_name,

					SUM(receivable) AS receivable,
					SUM(pay_amount) AS pay_amount,
					SUM(transfer_real_amount) AS transfer_real_amount,
					SUM(payment_transfer_amount) AS payment_transfer_amount,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(rounding) AS rounding,
					SUM(overflow_amount) AS overflow_amount,
					SUM(change_amount) AS change_amount,

					JSON_BUILD_OBJECT(
						'h00', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_00),
							'pay_amount', SUM(pay_amount_00),
							'transfer_real_amount', SUM(transfer_real_amount_00),
							'payment_transfer_amount', SUM(payment_transfer_amount_00),
							'item_count', SUM(item_count_00),
							'item_count_returned', SUM(item_count_returned_00),
							'rounding', SUM(rounding_00),
							'overflow_amount', SUM(overflow_amount_00),
							'change_amount', SUM(change_amount_00)
						),
						'h01', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_01),
							'pay_amount', SUM(pay_amount_01),
							'transfer_real_amount', SUM(transfer_real_amount_01),
							'payment_transfer_amount', SUM(payment_transfer_amount_01),
							'item_count', SUM(item_count_01),
							'item_count_returned', SUM(item_count_returned_01),
							'rounding', SUM(rounding_01),
							'overflow_amount', SUM(overflow_amount_01),
							'change_amount', SUM(change_amount_01)
						),
						'h02', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_02),
							'pay_amount', SUM(pay_amount_02),
							'transfer_real_amount', SUM(transfer_real_amount_02),
							'payment_transfer_amount', SUM(payment_transfer_amount_02),
							'item_count', SUM(item_count_02),
							'item_count_returned', SUM(item_count_returned_02),
							'rounding', SUM(rounding_02),
							'overflow_amount', SUM(overflow_amount_02),
							'change_amount', SUM(change_amount_02)
						),
						'h03', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_03),
							'pay_amount', SUM(pay_amount_03),
							'transfer_real_amount', SUM(transfer_real_amount_03),
							'payment_transfer_amount', SUM(payment_transfer_amount_03),
							'item_count', SUM(item_count_03),
							'item_count_returned', SUM(item_count_returned_03),
							'rounding', SUM(rounding_03),
							'overflow_amount', SUM(overflow_amount_03),
							'change_amount', SUM(change_amount_03)
						),
						'h04', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_04),
							'pay_amount', SUM(pay_amount_04),
							'transfer_real_amount', SUM(transfer_real_amount_04),
							'payment_transfer_amount', SUM(payment_transfer_amount_04),
							'item_count', SUM(item_count_04),
							'item_count_returned', SUM(item_count_returned_04),
							'rounding', SUM(rounding_04),
							'overflow_amount', SUM(overflow_amount_04),
							'change_amount', SUM(change_amount_04)
						),
						'h05', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_05),
							'pay_amount', SUM(pay_amount_05),
							'transfer_real_amount', SUM(transfer_real_amount_05),
							'payment_transfer_amount', SUM(payment_transfer_amount_05),
							'item_count', SUM(item_count_05),
							'item_count_returned', SUM(item_count_returned_05),
							'rounding', SUM(rounding_05),
							'overflow_amount', SUM(overflow_amount_05),
							'change_amount', SUM(change_amount_05)
						),
						'h06', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_06),
							'pay_amount', SUM(pay_amount_06),
							'transfer_real_amount', SUM(transfer_real_amount_06),
							'payment_transfer_amount', SUM(payment_transfer_amount_06),
							'item_count', SUM(item_count_06),
							'item_count_returned', SUM(item_count_returned_06),
							'rounding', SUM(rounding_06),
							'overflow_amount', SUM(overflow_amount_06),
							'change_amount', SUM(change_amount_06)
						),
						'h07', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_07),
							'pay_amount', SUM(pay_amount_07),
							'transfer_real_amount', SUM(transfer_real_amount_07),
							'payment_transfer_amount', SUM(payment_transfer_amount_07),
							'item_count', SUM(item_count_07),
							'item_count_returned', SUM(item_count_returned_07),
							'rounding', SUM(rounding_07),
							'overflow_amount', SUM(overflow_amount_07),
							'change_amount', SUM(change_amount_07)
						),
						'h08', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_08),
							'pay_amount', SUM(pay_amount_08),
							'transfer_real_amount', SUM(transfer_real_amount_08),
							'payment_transfer_amount', SUM(payment_transfer_amount_08),
							'item_count', SUM(item_count_08),
							'item_count_returned', SUM(item_count_returned_08),
							'rounding', SUM(rounding_08),
							'overflow_amount', SUM(overflow_amount_08),
							'change_amount', SUM(change_amount_08)
						),
						'h09', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_09),
							'pay_amount', SUM(pay_amount_09),
							'transfer_real_amount', SUM(transfer_real_amount_09),
							'payment_transfer_amount', SUM(payment_transfer_amount_09),
							'item_count', SUM(item_count_09),
							'item_count_returned', SUM(item_count_returned_09),
							'rounding', SUM(rounding_09),
							'overflow_amount', SUM(overflow_amount_09),
							'change_amount', SUM(change_amount_09)
						),
						'h10', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_10),
							'pay_amount', SUM(pay_amount_10),
							'transfer_real_amount', SUM(transfer_real_amount_10),
							'payment_transfer_amount', SUM(payment_transfer_amount_10),
							'item_count', SUM(item_count_10),
							'item_count_returned', SUM(item_count_returned_10),
							'rounding', SUM(rounding_10),
							'overflow_amount', SUM(overflow_amount_10),
							'change_amount', SUM(change_amount_10)
						),
						'h11', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_11),
							'pay_amount', SUM(pay_amount_11),
							'transfer_real_amount', SUM(transfer_real_amount_11),
							'payment_transfer_amount', SUM(payment_transfer_amount_11),
							'item_count', SUM(item_count_11),
							'item_count_returned', SUM(item_count_returned_11),
							'rounding', SUM(rounding_11),
							'overflow_amount', SUM(overflow_amount_11),
							'change_amount', SUM(change_amount_11)
						),
						'h12', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_12),
							'pay_amount', SUM(pay_amount_12),
							'transfer_real_amount', SUM(transfer_real_amount_12),
							'payment_transfer_amount', SUM(payment_transfer_amount_12),
							'item_count', SUM(item_count_12),
							'item_count_returned', SUM(item_count_returned_12),
							'rounding', SUM(rounding_12),
							'overflow_amount', SUM(overflow_amount_12),
							'change_amount', SUM(change_amount_12)
						),
						'h13', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_13),
							'pay_amount', SUM(pay_amount_13),
							'transfer_real_amount', SUM(transfer_real_amount_13),
							'payment_transfer_amount', SUM(payment_transfer_amount_13),
							'item_count', SUM(item_count_13),
							'item_count_returned', SUM(item_count_returned_13),
							'rounding', SUM(rounding_13),
							'overflow_amount', SUM(overflow_amount_13),
							'change_amount', SUM(change_amount_13)
						),
						'h14', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_14),
							'pay_amount', SUM(pay_amount_14),
							'transfer_real_amount', SUM(transfer_real_amount_14),
							'payment_transfer_amount', SUM(payment_transfer_amount_14),
							'item_count', SUM(item_count_14),
							'item_count_returned', SUM(item_count_returned_14),
							'rounding', SUM(rounding_14),
							'overflow_amount', SUM(overflow_amount_14),
							'change_amount', SUM(change_amount_14)
						),
						'h15', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_15),
							'pay_amount', SUM(pay_amount_15),
							'transfer_real_amount', SUM(transfer_real_amount_15),
							'payment_transfer_amount', SUM(payment_transfer_amount_15),
							'item_count', SUM(item_count_15),
							'item_count_returned', SUM(item_count_returned_15),
							'rounding', SUM(rounding_15),
							'overflow_amount', SUM(overflow_amount_15),
							'change_amount', SUM(change_amount_15)
						),
						'h16', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_16),
							'pay_amount', SUM(pay_amount_16),
							'transfer_real_amount', SUM(transfer_real_amount_16),
							'payment_transfer_amount', SUM(payment_transfer_amount_16),
							'item_count', SUM(item_count_16),
							'item_count_returned', SUM(item_count_returned_16),
							'rounding', SUM(rounding_16),
							'overflow_amount', SUM(overflow_amount_16),
							'change_amount', SUM(change_amount_16)
						),
						'h17', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_17),
							'pay_amount', SUM(pay_amount_17),
							'transfer_real_amount', SUM(transfer_real_amount_17),
							'payment_transfer_amount', SUM(payment_transfer_amount_17),
							'item_count', SUM(item_count_17),
							'item_count_returned', SUM(item_count_returned_17),
							'rounding', SUM(rounding_17),
							'overflow_amount', SUM(overflow_amount_17),
							'change_amount', SUM(change_amount_17)
						),
						'h18', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_18),
							'pay_amount', SUM(pay_amount_18),
							'transfer_real_amount', SUM(transfer_real_amount_18),
							'payment_transfer_amount', SUM(payment_transfer_amount_18),
							'item_count', SUM(item_count_18),
							'item_count_returned', SUM(item_count_returned_18),
							'rounding', SUM(rounding_18),
							'overflow_amount', SUM(overflow_amount_18),
							'change_amount', SUM(change_amount_18)
						),
						'h19', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_19),
							'pay_amount', SUM(pay_amount_19),
							'transfer_real_amount', SUM(transfer_real_amount_19),
							'payment_transfer_amount', SUM(payment_transfer_amount_19),
							'item_count', SUM(item_count_19),
							'item_count_returned', SUM(item_count_returned_19),
							'rounding', SUM(rounding_19),
							'overflow_amount', SUM(overflow_amount_19),
							'change_amount', SUM(change_amount_19)
						),
						'h20', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_20),
							'pay_amount', SUM(pay_amount_20),
							'transfer_real_amount', SUM(transfer_real_amount_20),
							'payment_transfer_amount', SUM(payment_transfer_amount_20),
							'item_count', SUM(item_count_20),
							'item_count_returned', SUM(item_count_returned_20),
							'rounding', SUM(rounding_20),
							'overflow_amount', SUM(overflow_amount_20),
							'change_amount', SUM(change_amount_20)
						),
						'h21', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_21),
							'pay_amount', SUM(pay_amount_21),
							'transfer_real_amount', SUM(transfer_real_amount_21),
							'payment_transfer_amount', SUM(payment_transfer_amount_21),
							'item_count', SUM(item_count_21),
							'item_count_returned', SUM(item_count_returned_21),
							'rounding', SUM(rounding_21),
							'overflow_amount', SUM(overflow_amount_21),
							'change_amount', SUM(change_amount_21)
						),
						'h22', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_22),
							'pay_amount', SUM(pay_amount_22),
							'transfer_real_amount', SUM(transfer_real_amount_22),
							'payment_transfer_amount', SUM(payment_transfer_amount_22),
							'item_count', SUM(item_count_22),
							'item_count_returned', SUM(item_count_returned_22),
							'rounding', SUM(rounding_22),
							'overflow_amount', SUM(overflow_amount_22),
							'change_amount', SUM(change_amount_22)
						),
						'h23', JSON_BUILD_OBJECT(
							'receivable', SUM(receivable_23),
							'pay_amount', SUM(pay_amount_23),
							'transfer_real_amount', SUM(transfer_real_amount_23),
							'payment_transfer_amount', SUM(payment_transfer_amount_23),
							'item_count', SUM(item_count_23),
							'item_count_returned', SUM(item_count_returned_23),
							'rounding', SUM(rounding_23),
							'overflow_amount', SUM(overflow_amount_23),
							'change_amount', SUM(change_amount_23)
						)
					) AS "data"

				FROM base_payments_for_period
				GROUP BY
					bus_date, geo_id, branch_id, company_id, region_id, store_type, payment_id, payment_code, payment_name
			)
			SELECT
				to_char(for_payment.bus_date,'YYYY-MM-DD') AS bus_date,
			    for_payment.geo_id AS geo_id,
			    for_payment.branch_id AS branch_id,
			    for_payment.company_id AS company_id,
				for_payment.region_id AS region_id,
				for_payment.store_type AS store_type,
			    bd.days AS business_days,
				for_payment.payment_id AS payment_id,
				for_payment.payment_code AS payment_code,
				for_payment.payment_name AS payment_name,
				for_payment.receivable AS receivable,
			    for_payment.pay_amount AS pay_amount,
			    for_payment.transfer_real_amount AS transfer_real_amount,
			    for_payment.payment_transfer_amount AS payment_transfer_amount,
			    for_payment.item_count AS item_count,
			    for_payment.item_count_returned AS item_count_returned,
			    for_payment.rounding AS rounding,
			    for_payment.overflow_amount AS overflow_amount,
			    for_payment.change_amount AS change_amount,

				COALESCE(for_payment.data, '{}'::json) AS data

			FROM
				base_payments_by_date_and_region_for_payment for_payment
					LEFT JOIN business_days bd
						ON for_payment.region_id = bd.region_id
            ORDER BY for_payment.bus_date DESC, for_payment.region_id,for_payment.payment_id
		`
	}

	whereSQL := getWhereSQLForPaymentPeriodV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)
	// 暂时不用summary
	//logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL
}

func getWhereSQLForPaymentPeriodV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		paymentSQL    string // 支付方式筛选条件
		storeTypeSQL  string // 门店类型筛选条件
		openStatusSQL string // 开店状态筛选条件
	)
	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	if len(condition.PaymentIds) > 0 {
		paymentSQL = fmt.Sprintf(
			"AND payment_id IN (%s)",
			helpers.JoinInt64(condition.PaymentIds, ","),
		)
	}

	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentSQL, storeTypeSQL, openStatusSQL, storeSQL)
	return whereSQL
}
