package report_v2

import (
	"database/sql"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

type SalesRepositoryV2PG struct {
	DB *sql.DB
}

func GenerateRegionWhereSQLV2(condition *model.RepoCondition) string {
	var regionWhereSQL string
	// 如果查询门店id为空，则不加门店过滤语句
	if len(condition.RegionSearchIds) > 0 {
		switch condition.RegionSearchType {
		case "GEO_REGION": //按地理区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 0
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "BRANCH_REGION": //按管理区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 1
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "COMPANY": // 按公司查询
			regionWhereSQL = fmt.Sprintf(`
					AND store_caches.company_info IN (%s)
		 `, helpers.JoinInt64(condition.RegionSearchIds, ","))
		case "FRANCHISEE_REGION": //按加盟商区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 2
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		default:
			regionWhereSQL = fmt.Sprintf(
				"AND store_caches.id IN (%s)",
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		}
	}
	return regionWhereSQL
}
