package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) ProductChannelSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.ProductChannelSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	rowSQL := getSQLForProductChannelSalesV2(trackId, condition)
	ch1 := make(chan []*report.ProductChannelSalesV2, 1)
	go queryDBWithChanForProductChannelSalesV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.ProductChannelSalesResponseV2{
		Rows: rows,
	}, nil
}

func queryDBWithChanForProductChannelSalesV2(id int64, db *sql.DB, sql string, ch chan []*report.ProductChannelSalesV2) {
	ch <- queryDBForProductChannelSalesV2(id, db, sql)
}

func queryDBForProductChannelSalesV2(trackId int64, db *sql.DB, s string) []*report.ProductChannelSalesV2 {
	results := make([]*report.ProductChannelSalesV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductChannelSalesV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.ProductCategoryId,
			&f.ProductCategoryId1,
			&f.ProductCategoryId2,
			&f.ProductCategoryId3,
			&f.ProductCategoryId4,
			&f.ProductId,
			&f.ChannelId,
			&f.OrderType,
			&f.WeightCount,
			&f.GrossAmount,
			&f.NetAmount,
			&f.DiscountAmount,
			&f.ItemCount,
			&f.ProductAveragePrice,
			&f.WeightCountReturned,
			&f.GrossAmountReturned,
			&f.NetAmountReturned,
			&f.DiscountAmountReturned,
			&f.ItemCountReturned,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForProductChannelSalesV2(trackId int64, condition *model.RepoCondition) string {
	var (
		rowSQL string // 行sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						store_id AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,store_id
					ORDER BY bus_date DESC,store_id
					)T
				GROUP BY region_id
			) SELECT
					'' AS bus_date, --营业日期
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id, --地理区域id
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id, --管理区域id
					COALESCE(store_caches.company_info,0) AS company_id, --公司id
					COALESCE(store_id,0) AS region_id, --门店id
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days, 0) AS business_days, --营业天数
					COALESCE(product_caches.category, 0) AS product_category_id,--商品类别id
					COALESCE(product_caches.categories[1], 0) AS product_category_id1,--商品类别id1
					COALESCE(product_caches.categories[2], 0) AS product_category_id2,--商品类别id2
					COALESCE(product_caches.categories[3], 0) AS product_category_id3,--商品类别id3
					COALESCE(product_caches.categories[4], 0) AS product_category_id4,--商品类别id4
					COALESCE(fact.product_id, 0) AS product_id,--商品id
					COALESCE(fact.channel_id, 0) AS channel_id, --渠道id
					COALESCE(fact.order_type, '') AS order_type, --订单类型
					concat(sum(weight), COALESCE(unit, '')) as weight_count,
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(qty) AS item_count, --商品数量
					CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(gross_amount)/SUM(qty) END AS product_average_price, --商品均价
					concat(SUM(CASE WHEN refunded THEN weight ELSE 0 END), COALESCE(unit, '')) AS weight_count_returned, --退单商品份量
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					0 AS total
				FROM sales_product_amounts fact
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON fact.store_id = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					store_caches.company_info, store_id, bd.days, product_caches.category, product_category_id1,product_category_id2,product_category_id3,product_category_id4, fact.product_id, fact.channel_id, fact.order_type,fact.unit,fact.has_weight
				ORDER BY store_id
       `
	} else {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						store_id AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY store_id
					)T
				GROUP BY region_id
			) SELECT
					to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date, --营业日期
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id, --地理区域id
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id, --管理区域id
					COALESCE(store_caches.company_info,0) AS company_id, --公司id
					COALESCE(store_id, 0) AS region_id, --门店id
					COALESCE(max(store_caches.store_type), '') AS store_type, --门店经营类型
					bd.days AS business_days, --营业天数
					COALESCE(product_caches.category, 0) AS product_category_id,--商品类别id
					COALESCE(product_caches.categories[1], 0) AS product_category_id1,--商品类别id1
					COALESCE(product_caches.categories[2], 0) AS product_category_id2,--商品类别id2
					COALESCE(product_caches.categories[3], 0) AS product_category_id3,--商品类别id3
					COALESCE(product_caches.categories[4], 0) AS product_category_id4,--商品类别id4
					fact.product_id AS product_id,--商品id
					fact.channel_id AS channel_id, --渠道id
					fact.order_type AS order_type, --订单类型
					concat(sum(weight), COALESCE(unit, '')) as weight_count,
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(qty) AS item_count, --商品数量
					CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(gross_amount)/SUM(qty) END AS product_average_price, --商品均价
					concat(SUM(CASE WHEN refunded THEN weight ELSE 0 END), COALESCE(unit, '')) AS weight_count_returned, --退单商品份量
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					0 AS total
				FROM sales_product_amounts fact
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON fact.store_id = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date, store_caches.company_info, store_id, bd.days, product_caches.category, product_category_id1,product_category_id2,product_category_id3,product_category_id4, fact.product_id, fact.channel_id, fact.order_type,fact.unit,fact.has_weight
				ORDER BY fact.bus_date DESC, store_id
       `
	}

	whereSQL := getWhereSQLForProductChannelSalesV2(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getWhereSQLForProductChannelSalesV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		channelIdSQL          string // 渠道筛选条件
		orderTypeSQL          string // 订单类型筛选条件
		openStatusSQL         string // 开店类型筛选条件
		storeTypeSQL          string // 门店类型筛选条件
		comboSQL              string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = %d",
			condition.ChannelId,
		)
	}
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, channelIdSQL, orderTypeSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL)
	return whereSQL
}
