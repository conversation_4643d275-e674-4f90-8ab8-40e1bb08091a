package report_v2

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"strings"
)

var (
	storeMaps     map[int64]map[string]interface{}
	geoMaps       map[int64]map[string]interface{}
	branchMaps    map[int64]map[string]interface{}
	companyMaps   map[int64]map[string]interface{}
	storeTypeMaps map[int64]map[string]interface{}
	categoryMaps  map[int64]map[string]interface{}
	productMaps   map[int64]map[string]interface{}
)

func (rsmm *SalesRepositoryV2PG) ProductPeriodSalesV2(ctx context.Context, condition *model.RepoCondition, query *model.CommonRequest) []map[string]interface{} {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	storeMaps = entity.GetAllStoreMaps(ctx, query)
	categoryMaps = entity.GetAllMaps(ctx, query, "product_category")
	geoMaps = entity.GetAllMaps(ctx, query, "geo_region")
	branchMaps = entity.GetAllMaps(ctx, query, "branch_region")
	productMaps = entity.GetAllMaps(ctx, query, "sku")
	companyMaps = entity.GetAllMaps(ctx, query, "COMPANY_INFO")
	storeTypeMaps = entity.GetAllMaps(ctx, query, "store_type")
	logger.Pre().Infof("拉取主档结束")
	//打印时间
	logger.Pre().Infof("导出日期开始时间: %s", condition.Start)
	logger.Pre().Infof("导出日期结束时间: %s", condition.End)
	logger.Pre().Infof("数据--开始处理数据")
	rowSQL := getSQLForProductPeriodSalesV2(trackId, condition)
	rows := queryDBForProductPeriodSalesV2(trackId, rsmm.DB, rowSQL)
	logger.Pre().Infof("数据--结束处理数据 ")
	return rows
}

func queryDBWithChanForProductPeriodSalesV2(id int64, db *sql.DB, sql string, ch chan []map[string]interface{}) {
	ch <- queryDBForProductPeriodSalesV2(id, db, sql)
}

func queryDBForProductPeriodSalesV2(trackId int64, db *sql.DB, s string) []map[string]interface{} {
	results := make([]map[string]interface{}, 0, 200000)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductPeriodSalesV2)
		var val json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.ProductCategoryId,
			&f.ProductCategoryId1,
			&f.ProductCategoryId2,
			&f.ProductCategoryId3,
			&f.ProductCategoryId4,
			&f.ProductId,
			&f.GrossAmount,
			&f.NetAmount,
			&f.ItemCount,
			&val,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(val, &f.Data)
		if err != nil {
			logger.Pre().Error("单品时段data结构体解析失败：", err)
			return results
		}
		//门店类型
		f.StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(f.StoreType)]["name"]) // 门店经营类型
		//门店
		f.RegionCode = cast.ToString(storeMaps[f.RegionId]["code"])
		f.RegionName = cast.ToString(storeMaps[f.RegionId]["name"])
		f.RegionAddress = cast.ToString(storeMaps[f.RegionId]["region_name"])
		f.RegionAlias = cast.ToString(storeMaps[f.RegionId]["alias"])
		//商品类别
		f.ProductCategoryCode = cast.ToString(categoryMaps[f.ProductCategoryId]["code"])
		f.ProductCategoryName = cast.ToString(categoryMaps[f.ProductCategoryId]["name"])
		f.ProductCategoryCode1 = cast.ToString(categoryMaps[f.ProductCategoryId1]["code"])
		f.ProductCategoryName1 = cast.ToString(categoryMaps[f.ProductCategoryId1]["name"])
		f.ProductCategoryCode2 = cast.ToString(categoryMaps[f.ProductCategoryId2]["code"])
		f.ProductCategoryName2 = cast.ToString(categoryMaps[f.ProductCategoryId2]["name"])
		f.ProductCategoryCode3 = cast.ToString(categoryMaps[f.ProductCategoryId3]["code"])
		f.ProductCategoryName3 = cast.ToString(categoryMaps[f.ProductCategoryId3]["name"])
		f.ProductCategoryCode4 = cast.ToString(categoryMaps[f.ProductCategoryId4]["code"])
		f.ProductCategoryName4 = cast.ToString(categoryMaps[f.ProductCategoryId4]["name"])
		//商品
		productId := f.ProductId
		f.ProductCode = cast.ToString(productMaps[productId]["code"])
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		f.ProductName = pName
		//branch
		branchId := f.BranchId
		f.BranchCode = cast.ToString(branchMaps[branchId]["code"])
		f.BranchName = cast.ToString(branchMaps[branchId]["name"])
		//geo
		geoId := f.GeoId
		f.GeoCode = cast.ToString(geoMaps[geoId]["code"])
		f.GeoName = cast.ToString(geoMaps[geoId]["name"])
		//company
		companyId := f.CompanyId
		f.CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		f.CompanyName = cast.ToString(companyMaps[companyId]["name"])

		results = append(results, f.ToMap())
	}
	return results
}

func getSQLForProductPeriodSalesV2(trackId int64, condition *model.RepoCondition) string {
	var rowSQL string // 行sql

	if condition.TagType == "SUMMARY" {
		rowSQL = `
		--单品时段报表 summary
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						store_id AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date, fact.store_id
					ORDER BY bus_date DESC, fact.store_id
				) T
				GROUP BY region_id
			), base_products AS (
				SELECT
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE(store_id,0) AS region_id,
				    COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id,0) AS product_id,
					COALESCE(product_caches.category,0) AS product_category_id,
					COALESCE(product_caches.categories[1],0) AS product_category_id1,
					COALESCE(product_caches.categories[2],0) AS product_category_id2,
					COALESCE(product_caches.categories[3],0) AS product_category_id3,
					COALESCE(product_caches.categories[4],0) AS product_category_id4,
			
					extract(hour from fact.order_time) AS hours,
					COALESCE(sum(weight), 0) as weight, --商品份量
				    COALESCE(fact.unit, '') as unit,
					COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
					COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
					COALESCE(SUM(qty), 0) AS item_count --商品数量
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					store_caches.company_info, fact.store_id, fact.product_id, product_caches.category,product_category_id1,product_category_id2,product_category_id3,product_category_id4, fact.order_time, fact.unit, fact.has_weight
				ORDER BY
					fact.store_id
			), base_products_for_period AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
					product_id,
					product_category_id,
					product_category_id1,product_category_id2,product_category_id3,product_category_id4,
					unit,
			
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
			
					SUM(CASE WHEN hours = 0 THEN weight ELSE 0 END) AS weight_00,
                    SUM(CASE WHEN hours = 1 THEN weight ELSE 0 END) AS weight_01,
                    SUM(CASE WHEN hours = 2 THEN weight ELSE 0 END) AS weight_02,
                    SUM(CASE WHEN hours = 3 THEN weight ELSE 0 END) AS weight_03,
                    SUM(CASE WHEN hours = 4 THEN weight ELSE 0 END) AS weight_04,
                    SUM(CASE WHEN hours = 5 THEN weight ELSE 0 END) AS weight_05,
                    SUM(CASE WHEN hours = 6 THEN weight ELSE 0 END) AS weight_06,
                    SUM(CASE WHEN hours = 7 THEN weight ELSE 0 END) AS weight_07,
                    SUM(CASE WHEN hours = 8 THEN weight ELSE 0 END) AS weight_08,
                    SUM(CASE WHEN hours = 9 THEN weight ELSE 0 END) AS weight_09,
                    SUM(CASE WHEN hours = 10 THEN weight ELSE 0 END) AS weight_10,
                    SUM(CASE WHEN hours = 11 THEN weight ELSE 0 END) AS weight_11,
                    SUM(CASE WHEN hours = 12 THEN weight ELSE 0 END) AS weight_12,
                    SUM(CASE WHEN hours = 13 THEN weight ELSE 0 END) AS weight_13,
                    SUM(CASE WHEN hours = 14 THEN weight ELSE 0 END) AS weight_14,
                    SUM(CASE WHEN hours = 15 THEN weight ELSE 0 END) AS weight_15,
                    SUM(CASE WHEN hours = 16 THEN weight ELSE 0 END) AS weight_16,
                    SUM(CASE WHEN hours = 17 THEN weight ELSE 0 END) AS weight_17,
                    SUM(CASE WHEN hours = 18 THEN weight ELSE 0 END) AS weight_18,
                    SUM(CASE WHEN hours = 19 THEN weight ELSE 0 END) AS weight_19,
                    SUM(CASE WHEN hours = 20 THEN weight ELSE 0 END) AS weight_20,
                    SUM(CASE WHEN hours = 21 THEN weight ELSE 0 END) AS weight_21,
                    SUM(CASE WHEN hours = 22 THEN weight ELSE 0 END) AS weight_22,
                    SUM(CASE WHEN hours = 23 THEN weight ELSE 0 END) AS weight_23,

					SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
					SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
					SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
					SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
					SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
					SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
					SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
					SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
					SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
					SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
					SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
					SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
					SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
					SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
					SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
					SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
					SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
					SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
					SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
					SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
					SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
					SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
					SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
					SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
					SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
					SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
					SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
					SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
					SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
					SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
					SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
					SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
					SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
					SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
					SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
					SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
					SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
					SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
					SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
					SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
					SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
					SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
					SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
					SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
					SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
					SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
					SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
					SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
					SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
					SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
					SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
					SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
					SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
					SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
					SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
					SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
					SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
					SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
					SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
					SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
					SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
					SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
					SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
					SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
					SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
					SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
					SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
					SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
					SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
					SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23

				FROM base_products
				GROUP BY
					geo_id, branch_id, company_id, region_id, store_type, product_id, product_category_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4,unit
			), base_products_by_date_and_region_for_product AS (
				SELECT
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
				    product_id,
				    product_category_id,
					product_category_id1,product_category_id2,product_category_id3,product_category_id4,
			
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					JSON_BUILD_OBJECT(
                        'h00', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_00), unit),
                                'gross_amount', SUM(gross_amount_00),
                                'net_amount', SUM(net_amount_00),
                                'item_count', SUM(item_count_00)
                        ),
                        'h01', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_01), unit),
                                'gross_amount', SUM(gross_amount_01),
                                'net_amount', SUM(net_amount_01),
                                'item_count', SUM(item_count_01)
                        ),
                        'h02', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_02), unit),
                                'gross_amount', SUM(gross_amount_02),
                                'net_amount', SUM(net_amount_02),
                                'item_count', SUM(item_count_02)
                        ),
                        'h03', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_03), unit),
                                'gross_amount', SUM(gross_amount_03),
                                'net_amount', SUM(net_amount_03),
                                'item_count', SUM(item_count_03)
                        ),
                        'h04', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_04), unit),
                                'gross_amount', SUM(gross_amount_04),
                                'net_amount', SUM(net_amount_04),
                                'item_count', SUM(item_count_04)
                        ),
                        'h05', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_05), unit),
                                'gross_amount', SUM(gross_amount_05),
                                'net_amount', SUM(net_amount_05),
                                'item_count', SUM(item_count_05)
                        ),
                        'h06', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_06), unit),
                                'gross_amount', SUM(gross_amount_06),
                                'net_amount', SUM(net_amount_06),
                                'item_count', SUM(item_count_06)
                        ),
                        'h07', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_07), unit),
                                'gross_amount', SUM(gross_amount_07),
                                'net_amount', SUM(net_amount_07),
                                'item_count', SUM(item_count_07)
                        ),
                        'h08', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_08), unit),
                                'gross_amount', SUM(gross_amount_08),
                                'net_amount', SUM(net_amount_08),
                                'item_count', SUM(item_count_08)
                        ),
                        'h09', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_09), unit),
                                'gross_amount', SUM(gross_amount_09),
                                'net_amount', SUM(net_amount_09),
                                'item_count', SUM(item_count_09)
                        ),
                        'h10', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_10), unit),
                                'gross_amount', SUM(gross_amount_10),
                                'net_amount', SUM(net_amount_10),
                                'item_count', SUM(item_count_10)
                        ),
                        'h11', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_11), unit),
                                'gross_amount', SUM(gross_amount_11),
                                'net_amount', SUM(net_amount_11),
                                'item_count', SUM(item_count_11)
                        ),
                        'h12', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_12), unit),
                                'gross_amount', SUM(gross_amount_12),
                                'net_amount', SUM(net_amount_12),
                                'item_count', SUM(item_count_12)
                        ),
                        'h13', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_13), unit),
                                'gross_amount', SUM(gross_amount_13),
                                'net_amount', SUM(net_amount_13),
                                'item_count', SUM(item_count_13)
                        ),
                        'h14', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_14), unit),
                                'gross_amount', SUM(gross_amount_14),
                                'net_amount', SUM(net_amount_14),
                                'item_count', SUM(item_count_14)
                        ),
                        'h15', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_15), unit),
                                'gross_amount', SUM(gross_amount_15),
                                'net_amount', SUM(net_amount_15),
                                'item_count', SUM(item_count_15)
                        ),
                        'h16', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_16), unit),
                                'gross_amount', SUM(gross_amount_16),
                                'net_amount', SUM(net_amount_16),
                                'item_count', SUM(item_count_16)
                        ),
                        'h17', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_17), unit),
                                'gross_amount', SUM(gross_amount_17),
                                'net_amount', SUM(net_amount_17),
                                'item_count', SUM(item_count_17)
                        ),
                        'h18', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_18), unit),
                                'gross_amount', SUM(gross_amount_18),
                                'net_amount', SUM(net_amount_18),
                                'item_count', SUM(item_count_18)
                        ),
                        'h19', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_19), unit),
                                'gross_amount', SUM(gross_amount_19),
                                'net_amount', SUM(net_amount_19),
                                'item_count', SUM(item_count_19)
                        ),
                        'h20', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_20), unit),
                                'gross_amount', SUM(gross_amount_20),
                                'net_amount', SUM(net_amount_20),
                                'item_count', SUM(item_count_20)
                        ),
                        'h21', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_21), unit),
                                'gross_amount', SUM(gross_amount_21),
                                'net_amount', SUM(net_amount_21),
                                'item_count', SUM(item_count_21)
                        ),
                        'h22', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_22), unit),
                                'gross_amount', SUM(gross_amount_22),
                                'net_amount', SUM(net_amount_22),
                                'item_count', SUM(item_count_22)
                        ),
                        'h23', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_23), unit),
                                'gross_amount', SUM(gross_amount_23),
                                'net_amount', SUM(net_amount_23),
                                'item_count', SUM(item_count_23)
                        )
                	) AS "data"
				FROM base_products_for_period
				GROUP BY
					geo_id, branch_id, company_id, region_id, store_type, product_id, product_category_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4,unit
			)
			SELECT
				'' AS bus_date,
			    for_product.geo_id AS geo_id,
			    for_product.branch_id AS branch_id,
			    for_product.company_id AS company_id,
				for_product.region_id AS region_id,
				for_product.store_type AS store_type,
			    bd.days AS business_days,
			    for_product.product_category_id AS product_category_id,
				product_category_id1,product_category_id2,product_category_id3,product_category_id4,
			    for_product.product_id AS product_id,
			
				for_product.gross_amount AS gross_amount,
				for_product.net_amount AS net_amount,
				for_product.item_count AS item_count,

				COALESCE(for_product.data, '{}'::json) AS data
			FROM
				base_products_by_date_and_region_for_product for_product
					LEFT JOIN business_days bd
						ON for_product.region_id = bd.region_id
		`
	} else {
		rowSQL = `
		--单品时段报表 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						store_id AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY fact.store_id
				) T
				GROUP BY region_id
			), base_products AS (
				SELECT
					fact.bus_date AS bus_date,
                    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
                    COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
                    COALESCE(store_caches.company_info,0) AS company_id,
				    COALESCE(store_id,0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id,0) AS product_id,
					COALESCE(product_caches.category,0) AS product_category_id,
					COALESCE(product_caches.categories[1],0) AS product_category_id1,
					COALESCE(product_caches.categories[2],0) AS product_category_id2,
					COALESCE(product_caches.categories[3],0) AS product_category_id3,
					COALESCE(product_caches.categories[4],0) AS product_category_id4,
			
					extract(hour from fact.order_time) AS hours,
					COALESCE(sum(weight), 0) as weight, --商品份量
				    COALESCE(fact.unit, '') as unit,
					COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
					COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
					COALESCE(SUM(qty), 0) AS item_count --商品数量
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date, store_caches.company_info, fact.store_id, fact.product_id, product_caches.category, product_category_id1,product_category_id2,product_category_id3,product_category_id4, fact.order_time, fact.unit, fact.has_weight
				ORDER BY
					fact.bus_date DESC,
					fact.store_id
			), base_products_for_period AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
					product_id,
					product_category_id,
					product_category_id1,product_category_id2,product_category_id3,product_category_id4,
					unit,

					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					
					SUM(CASE WHEN hours = 0 THEN weight ELSE 0 END) AS weight_00,
                    SUM(CASE WHEN hours = 1 THEN weight ELSE 0 END) AS weight_01,
                    SUM(CASE WHEN hours = 2 THEN weight ELSE 0 END) AS weight_02,
                    SUM(CASE WHEN hours = 3 THEN weight ELSE 0 END) AS weight_03,
                    SUM(CASE WHEN hours = 4 THEN weight ELSE 0 END) AS weight_04,
                    SUM(CASE WHEN hours = 5 THEN weight ELSE 0 END) AS weight_05,
                    SUM(CASE WHEN hours = 6 THEN weight ELSE 0 END) AS weight_06,
                    SUM(CASE WHEN hours = 7 THEN weight ELSE 0 END) AS weight_07,
                    SUM(CASE WHEN hours = 8 THEN weight ELSE 0 END) AS weight_08,
                    SUM(CASE WHEN hours = 9 THEN weight ELSE 0 END) AS weight_09,
                    SUM(CASE WHEN hours = 10 THEN weight ELSE 0 END) AS weight_10,
                    SUM(CASE WHEN hours = 11 THEN weight ELSE 0 END) AS weight_11,
                    SUM(CASE WHEN hours = 12 THEN weight ELSE 0 END) AS weight_12,
                    SUM(CASE WHEN hours = 13 THEN weight ELSE 0 END) AS weight_13,
                    SUM(CASE WHEN hours = 14 THEN weight ELSE 0 END) AS weight_14,
                    SUM(CASE WHEN hours = 15 THEN weight ELSE 0 END) AS weight_15,
                    SUM(CASE WHEN hours = 16 THEN weight ELSE 0 END) AS weight_16,
                    SUM(CASE WHEN hours = 17 THEN weight ELSE 0 END) AS weight_17,
                    SUM(CASE WHEN hours = 18 THEN weight ELSE 0 END) AS weight_18,
                    SUM(CASE WHEN hours = 19 THEN weight ELSE 0 END) AS weight_19,
                    SUM(CASE WHEN hours = 20 THEN weight ELSE 0 END) AS weight_20,
                    SUM(CASE WHEN hours = 21 THEN weight ELSE 0 END) AS weight_21,
                    SUM(CASE WHEN hours = 22 THEN weight ELSE 0 END) AS weight_22,
                    SUM(CASE WHEN hours = 23 THEN weight ELSE 0 END) AS weight_23,
					SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
					SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
					SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
					SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
					SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
					SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
					SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
					SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
					SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
					SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
					SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
					SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
					SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
					SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
					SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
					SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
					SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
					SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
					SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
					SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
					SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
					SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
					SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
					SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
					SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
					SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
					SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
					SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
					SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
					SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
					SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
					SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
					SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
					SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
					SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
					SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
					SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
					SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
					SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
					SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
					SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
					SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
					SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
					SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
					SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
					SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
					SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
					SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
					SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
					SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
					SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
					SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
					SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
					SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
					SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
					SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
					SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
					SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
					SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
					SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
					SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
					SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
					SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
					SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
					SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
					SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
					SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
					SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
					SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
					SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23

				FROM base_products
				GROUP BY
					bus_date, geo_id, branch_id, company_id, region_id, store_type, product_id, product_category_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4,unit
			), base_products_by_date_and_region_for_product AS (
				SELECT
					bus_date,
				    geo_id,
				    branch_id,
				    company_id,
					region_id,
					store_type,
				    product_id,
				    product_category_id,
					product_category_id1,product_category_id2,product_category_id3,product_category_id4,
			
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					JSON_BUILD_OBJECT(
                        'h00', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_00), unit),
                                'gross_amount', SUM(gross_amount_00),
                                'net_amount', SUM(net_amount_00),
                                'item_count', SUM(item_count_00)
                        ),
                        'h01', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_01), unit),
                                'gross_amount', SUM(gross_amount_01),
                                'net_amount', SUM(net_amount_01),
                                'item_count', SUM(item_count_01)
                        ),
                        'h02', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_02), unit),
                                'gross_amount', SUM(gross_amount_02),
                                'net_amount', SUM(net_amount_02),
                                'item_count', SUM(item_count_02)
                        ),
                        'h03', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_03), unit),
                                'gross_amount', SUM(gross_amount_03),
                                'net_amount', SUM(net_amount_03),
                                'item_count', SUM(item_count_03)
                        ),
                        'h04', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_04), unit),
                                'gross_amount', SUM(gross_amount_04),
                                'net_amount', SUM(net_amount_04),
                                'item_count', SUM(item_count_04)
                        ),
                        'h05', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_05), unit),
                                'gross_amount', SUM(gross_amount_05),
                                'net_amount', SUM(net_amount_05),
                                'item_count', SUM(item_count_05)
                        ),
                        'h06', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_06), unit),
                                'gross_amount', SUM(gross_amount_06),
                                'net_amount', SUM(net_amount_06),
                                'item_count', SUM(item_count_06)
                        ),
                        'h07', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_07), unit),
                                'gross_amount', SUM(gross_amount_07),
                                'net_amount', SUM(net_amount_07),
                                'item_count', SUM(item_count_07)
                        ),
                        'h08', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_08), unit),
                                'gross_amount', SUM(gross_amount_08),
                                'net_amount', SUM(net_amount_08),
                                'item_count', SUM(item_count_08)
                        ),
                        'h09', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_09), unit),
                                'gross_amount', SUM(gross_amount_09),
                                'net_amount', SUM(net_amount_09),
                                'item_count', SUM(item_count_09)
                        ),
                        'h10', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_10), unit),
                                'gross_amount', SUM(gross_amount_10),
                                'net_amount', SUM(net_amount_10),
                                'item_count', SUM(item_count_10)
                        ),
                        'h11', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_11), unit),
                                'gross_amount', SUM(gross_amount_11),
                                'net_amount', SUM(net_amount_11),
                                'item_count', SUM(item_count_11)
                        ),
                        'h12', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_12), unit),
                                'gross_amount', SUM(gross_amount_12),
                                'net_amount', SUM(net_amount_12),
                                'item_count', SUM(item_count_12)
                        ),
                        'h13', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_13), unit),
                                'gross_amount', SUM(gross_amount_13),
                                'net_amount', SUM(net_amount_13),
                                'item_count', SUM(item_count_13)
                        ),
                        'h14', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_14), unit),
                                'gross_amount', SUM(gross_amount_14),
                                'net_amount', SUM(net_amount_14),
                                'item_count', SUM(item_count_14)
                        ),
                        'h15', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_15), unit),
                                'gross_amount', SUM(gross_amount_15),
                                'net_amount', SUM(net_amount_15),
                                'item_count', SUM(item_count_15)
                        ),
                        'h16', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_16), unit),
                                'gross_amount', SUM(gross_amount_16),
                                'net_amount', SUM(net_amount_16),
                                'item_count', SUM(item_count_16)
                        ),
                        'h17', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_17), unit),
                                'gross_amount', SUM(gross_amount_17),
                                'net_amount', SUM(net_amount_17),
                                'item_count', SUM(item_count_17)
                        ),
                        'h18', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_18), unit),
                                'gross_amount', SUM(gross_amount_18),
                                'net_amount', SUM(net_amount_18),
                                'item_count', SUM(item_count_18)
                        ),
                        'h19', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_19), unit),
                                'gross_amount', SUM(gross_amount_19),
                                'net_amount', SUM(net_amount_19),
                                'item_count', SUM(item_count_19)
                        ),
                        'h20', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_20), unit),
                                'gross_amount', SUM(gross_amount_20),
                                'net_amount', SUM(net_amount_20),
                                'item_count', SUM(item_count_20)
                        ),
                        'h21', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_21), unit),
                                'gross_amount', SUM(gross_amount_21),
                                'net_amount', SUM(net_amount_21),
                                'item_count', SUM(item_count_21)
                        ),
                        'h22', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_22), unit),
                                'gross_amount', SUM(gross_amount_22),
                                'net_amount', SUM(net_amount_22),
                                'item_count', SUM(item_count_22)
                        ),
                        'h23', JSON_BUILD_OBJECT(
                                'weight_count', concat(sum(weight_23), unit),
                                'gross_amount', SUM(gross_amount_23),
                                'net_amount', SUM(net_amount_23),
                                'item_count', SUM(item_count_23)
                        )
                ) AS "data"
				FROM base_products_for_period
				GROUP BY
					bus_date, geo_id, branch_id, company_id, region_id, store_type, product_id, product_category_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4,unit
			)
			SELECT
				to_char(for_product.bus_date,'YYYY-MM-DD') AS bus_date,
			    for_product.geo_id AS geo_id,
			    for_product.branch_id AS branch_id,
			    for_product.company_id AS company_id,
				for_product.region_id AS region_id,
				for_product.store_type AS store_type,
			    bd.days AS business_days,
			    for_product.product_category_id AS product_category_id,
				product_category_id1,product_category_id2,product_category_id3,product_category_id4,
			    for_product.product_id AS product_id,
			
				for_product.gross_amount AS gross_amount,
				for_product.net_amount AS net_amount,
				for_product.item_count AS item_count,

				COALESCE(for_product.data, '{}'::json) AS data
			FROM
				base_products_by_date_and_region_for_product for_product
					LEFT JOIN business_days bd
						ON for_product.region_id = bd.region_id
		`
	}

	whereSQL := getWhereSQLForProductPeriodSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getWhereSQLForProductPeriodSalesV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		priceScopeSQL         string //  价格区间筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		openStatusSQL         string // 开店类型筛选条件
		storeTypeSQL          string // 门店类型筛选条件
		periodSQL             string // 时段筛选条件
		comboSQL              string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	priceScopeSQL = generatePriceScopeSQLForProductSalesV2(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND product_caches.id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}

	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	if len(condition.Period) != 0 {
		periodSQL = fmt.Sprintf(`
			AND extract(hour from fact.order_time) IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, priceScopeSQL, storeTypeSQL, openStatusSQL, comboSQL, periodSQL, storeSQL)
	return whereSQL
}

func generatePriceScopeSQLForProductSalesV2(condition *model.RepoCondition) string {
	priceScopeSQL := ""
	if len(condition.PriceScope) == 0 {
		return priceScopeSQL
	}
	for i, prices := range condition.PriceScope {
		if len(prices) != 2 {
			return ""
		}
		low, high := prices[0], prices[1]
		if i == 0 {
			priceScopeSQL += fmt.Sprintf(`
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		} else {
			priceScopeSQL += fmt.Sprintf(`
				OR
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		}
	}
	if priceScopeSQL == "" {
		return priceScopeSQL
	} else {
		return fmt.Sprintf(`AND (%s)`, priceScopeSQL)
	}
}
