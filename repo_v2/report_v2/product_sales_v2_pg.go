package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) ProductSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForProductSalesV2(trackId, condition)
	ch1 := make(chan []*report.ProductSalesV2, 1)
	go queryDBWithChanForProductSalesV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.ProductSalesResponseV2{
		Rows: rows,
	}, nil
}

func queryDBWithChanForProductSalesV2(id int64, db *sql.DB, sql string, ch chan []*report.ProductSalesV2) {
	ch <- queryDBForProductSalesV2(id, db, sql)
}

func queryDBForProductSalesV2(trackId int64, db *sql.DB, s string) []*report.ProductSalesV2 {
	results := make([]*report.ProductSalesV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductSalesV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.ProductId,
			&f.ProductCategoryId,
			&f.WeightCount,
			&f.WeightCountReturned,
			&f.GrossAmount,
			&f.GrossAmountReturned,
			&f.NetAmount,
			&f.NetAmountReturned,
			&f.DiscountAmount,
			&f.DiscountAmountReturned,
			&f.ItemCount,
			&f.ItemCountReturned,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForProductSalesV2(trackId int64, condition *model.RepoCondition) string {
	var (
		rowSQL string // 行sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,{REGION_ID}
					ORDER BY bus_date DESC,{REGION_ID}
					)T
				GROUP BY region_id
			) SELECT
					'' AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days,0) AS business_days,
					COALESCE(fact.product_id, 0) AS product_id,--商品id
					COALESCE(product_caches.category, 0) AS product_category_id,--商品类别id
					concat(sum(weight), COALESCE(unit, '')) as weight_count,
					concat(SUM(CASE WHEN refunded THEN weight ELSE 0 END), COALESCE(unit, '')) AS weight_count_returned, --退单商品份量
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					0 AS total
				FROM sales_product_amounts fact
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON {REGION_ID} = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID},store_caches.company_info,bd.days,fact.product_id, product_caches.category,fact.unit,fact.has_weight
				ORDER BY {REGION_ID};
        `
	} else {
		rowSQL = `
			WITH business_days AS(
				SELECT
					region_id, count(1) AS days
				FROM(
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY {REGION_ID}
					)T
				GROUP BY region_id
			) SELECT
					to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID},0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(bd.days,0) AS business_days,
					COALESCE(fact.product_id, 0) AS product_id,--商品id
					COALESCE(product_caches.category, 0) AS product_category_id,--商品类别id
					concat(sum(weight), COALESCE(unit, '')) as weight_count,
					concat(SUM(CASE WHEN refunded THEN weight ELSE 0 END), COALESCE(unit, '')) AS weight_count_returned, --退单商品份量
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					0 AS total
				FROM sales_product_amounts fact
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN business_days bd
						ON {REGION_ID} = bd.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date, {REGION_ID},store_caches.company_info,bd.days,fact.product_id, product_caches.category,fact.unit,fact.has_weight		
				ORDER BY fact.bus_date DESC, {REGION_ID};
        `
	}

	whereSQL := getWhereSQLForProductSalesV2(condition)
	regionSQL := getRegionSQLForProductSalesV2(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getRegionSQLForProductSalesV2(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func getWhereSQLForProductSalesV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		priceScopeSQL         string // 价格区间筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		storeTypeSQL          string // 增加门店类型的过滤
		openStatusSQL         string // 增加开店类型的过滤
		comboSQL              string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	priceScopeSQL = getPriceScopeSQLForProductSalesV2(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, priceScopeSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL)
	return whereSQL
}

func getPriceScopeSQLForProductSalesV2(condition *model.RepoCondition) string {
	priceScopeSQL := ""
	if len(condition.PriceScope) == 0 {
		return priceScopeSQL
	}
	for i, prices := range condition.PriceScope {
		if len(prices) != 2 {
			return ""
		}
		low, high := prices[0], prices[1]
		if i == 0 {
			priceScopeSQL += fmt.Sprintf(`
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		} else {
			priceScopeSQL += fmt.Sprintf(`
				OR
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		}
	}
	if priceScopeSQL == "" {
		return priceScopeSQL
	} else {
		return fmt.Sprintf(`AND (%s)`, priceScopeSQL)
	}
}
