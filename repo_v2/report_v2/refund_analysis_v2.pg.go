package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) RefundAnalysisV2(ctx context.Context, condition *model.RepoCondition) (*report.RefundAnalysisResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForRefundAnalysisV2(trackId, condition)
	ch1 := make(chan []*report.RefundAnalysisV2, 1)
	ch2 := make(chan []*report.RefundAnalysisV2, 1)
	go queryDBWithChanForRefundAnalysisV2(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForRefundAnalysisV2(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.RefundAnalysisV2
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.RefundAnalysisResponseV2{
		Summary: summary,
		Rows:    rows,
	}
	return response, nil
}

func queryDBWithChanForRefundAnalysisV2(id int64, db *sql.DB, sql string, ch chan []*report.RefundAnalysisV2) {
	ch <- queryDBForRefundAnalysisV2(id, db, sql)
}

func queryDBForRefundAnalysisV2(trackId int64, db *sql.DB, s string) []*report.RefundAnalysisV2 {
	results := make([]*report.RefundAnalysisV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.RefundAnalysisV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.RefundSide,
			&f.ChannelId,
			&f.RefundCode,
			&f.GrossAmountReturned,
			&f.OrderCountReturned,
			&f.NetAmountReturned,
			&f.ReceivableReturned,
			&f.PayAmountReturned,
			&f.DiscountAmountReturned,
			&f.MerchantAllowanceReturned,
			&f.CommissionReturned,
			&f.DeliveryFeeReturned,
			&f.PackageFeeReturned,
			&f.TipReturned,
			&f.ServiceFeeReturned,
			&f.TaxFeeReturned,
			&f.RoundingReturned,
			&f.OverflowAmountReturned,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForRefundAnalysisV2(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 行sql
		summarySQL string // 汇总sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
		with business_days as (
			select
				region_id, count(1) as days
			from (
				select
					store_id AS region_id, bus_date
				from sales_ticket_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where
					{WHERE}
				group by store_id,bus_date
				order by bus_date desc, store_id
				) T
				group by region_id
			)
			select
				'' AS bus_date,
				COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
				COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
				COALESCE(store_caches.company_info,0) AS company_id,
				COALESCE(fact.store_id,0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(bd.days,0) AS business_days,
				fact.refund_side AS refund_side, --退单角色
				COALESCE(fact.channel_id, 0) AS channel_id, --退单渠道
				fact.refund_code AS refund_code, --退单原因
				COALESCE(sum(case when refunded then gross_amount else 0 end), 0) as gross_amount_returned, --退单商品流水
				COALESCE(sum(case when refunded then eticket_count else 0 end), 0) as order_count_returned, --退单数
				COALESCE(sum(case when refunded then net_amount else 0 end), 0) as net_amount_returned, --退单商品实收
				COALESCE(sum(case when refunded then amount_4 else 0 end), 0) as receivable_returned, --退单应付金额
				COALESCE(sum(case when refunded then pay_amount else 0 end), 0) as pay_amount_returned, --退单实付
				COALESCE(sum(case when refunded then discount_amount else 0 end), 0) as discount_amount_returned, --退单折扣金额
				COALESCE(sum(case when refunded then merchant_discount_amount+store_discount_amount else 0 end), 0) as merchant_allowance_returned, --退单商家补贴
				COALESCE(sum(case when refunded then commission else 0 end), 0) as commission_returned, --退单佣金
				COALESCE(sum(case when refunded then delivery_fee else 0 end), 0) as delivery_fee_returned, --退单配送费
				COALESCE(sum(case when refunded then package_fee else 0 end), 0) as package_fee_returned, --退单包装费
				COALESCE(sum(case when refunded then tip else 0 end), 0) as tip_returned, --退单小费
				COALESCE(sum(case when refunded then service_fee else 0 end), 0) as service_fee_returned, --退单服务费
				COALESCE(sum(case when refunded then tax_fee else 0 end), 0) as tax_fee_returned, --退单税费
				COALESCE(sum(case when refunded then rounding else 0 end), 0) as rounding_returned, --退单抹零
				COALESCE(sum(case when refunded then overflow_amount else 0 end), 0) as overflow_amount_returned, --退单溢收
				0 as total
			
			from sales_ticket_amounts fact
				left join store_caches on fact.store_id = store_caches.id
				left join business_days bd on fact.store_id = bd.region_id
			where
				{WHERE}
				and refunded
			group by store_caches.company_info, fact.store_id,bd.days,fact.refund_side, fact.channel_id,refund_code
			order by store_id
			{LIMIT};
		`

		summarySQL = `
			with base_refund as (
				select
					COALESCE(sum(case when refunded then gross_amount else 0 end), 0) as gross_amount_returned, --退单商品流水
					COALESCE(sum(case when refunded then eticket_count else 0 end), 0) as order_count_returned, --退单数
					COALESCE(sum(case when refunded then net_amount else 0 end), 0) as net_amount_returned, --退单商品实收
					COALESCE(sum(case when refunded then amount_4 else 0 end), 0) as receivable_returned, --退单应付金额
					COALESCE(sum(case when refunded then pay_amount else 0 end), 0) as pay_amount_returned, --退单实付
					COALESCE(sum(case when refunded then discount_amount else 0 end), 0) as discount_amount_returned, --退单折扣金额
					COALESCE(sum(case when refunded then merchant_discount_amount+store_discount_amount else 0 end), 0) as merchant_allowance_returned, --退单商家补贴
					COALESCE(sum(case when refunded then commission else 0 end), 0) as commission_returned, --退单佣金
					COALESCE(sum(case when refunded then delivery_fee else 0 end), 0) as delivery_fee_returned, --退单配送费
					COALESCE(sum(case when refunded then package_fee else 0 end), 0) as package_fee_returned, --退单包装费
					COALESCE(sum(case when refunded then tip else 0 end), 0) as tip_returned, --退单小费
					COALESCE(sum(case when refunded then service_fee else 0 end), 0) as service_fee_returned, --退单服务费
					COALESCE(sum(case when refunded then tax_fee else 0 end), 0) as tax_fee_returned, --退单税费
					COALESCE(sum(case when refunded then rounding else 0 end), 0) as rounding_returned, --退单抹零
					COALESCE(sum(case when refunded then overflow_amount else 0 end), 0) as overflow_amount_returned --退单溢收
				from sales_ticket_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where 
					{WHERE}
				group by store_id
				order by store_id
			)
			select
						'' AS bus_date,
						0 AS geo_id,
						0 AS branch_id,
						0 AS company_id,
						0 AS region_id,
						'' AS store_type,
						0 AS business_days, --营业天数,
						'' AS refund_side,
						0 as channel_id,
						'' as refund_code,
						sum(gross_amount_returned) as gross_amount_returned, --退单商品流水
						sum(order_count_returned) as order_count_returned, --退单数
						sum(net_amount_returned) as net_amount_returned, --退单商品实收
						sum(receivable_returned) as receivable_returned, --退单应付金额
						sum(pay_amount_returned) as pay_amount_returned, --退单实付
						sum(discount_amount_returned) as discount_amount_returned, --退单折扣金额
						sum(merchant_allowance_returned) as merchant_allowance_returned, --退单商家补贴
						sum(commission_returned) as commission_returned, --退单佣金
						sum(delivery_fee_returned) as delivery_fee_returned, --退单配送费
						sum(package_fee_returned) as package_fee_returned, --退单包装费
						sum(tip_returned) as tip_returned, --退单小费
						sum(service_fee_returned) as service_fee_returned, --退单服务费
						sum(tax_fee_returned) as tax_fee_returned, --退单税费
						sum(rounding_returned) as rounding_returned, --退单抹零
						sum(overflow_amount_returned) as overflow_amount_returned, --退单溢收
						count(1) as total
			from base_refund
 		`
	} else {
		rowSQL = `
		with business_days as (
			select
				region_id, count(1) as days
			from (
				select
					store_id AS region_id
				from sales_ticket_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where
					{WHERE}
				group by store_id
				) T
				group by region_id
			)
			select
				to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date,
				COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
				COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
				COALESCE(store_caches.company_info,0) AS company_id,
				COALESCE(fact.store_id,0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(bd.days,0) AS business_days,
				fact.refund_side AS refund_side, --退单角色
				COALESCE(fact.channel_id, 0) AS channel_id, --退单渠道
				fact.refund_code AS refund_code, --退单原因
				COALESCE(sum(case when refunded then gross_amount else 0 end), 0) as gross_amount_returned, --退单商品流水
				COALESCE(sum(case when refunded then eticket_count else 0 end), 0) as order_count_returned, --退单数
				COALESCE(sum(case when refunded then net_amount else 0 end), 0) as net_amount_returned, --退单商品实收
				COALESCE(sum(case when refunded then amount_4 else 0 end), 0) as receivable_returned, --退单应付金额
				COALESCE(sum(case when refunded then pay_amount else 0 end), 0) as pay_amount_returned, --退单实付
				COALESCE(sum(case when refunded then discount_amount else 0 end), 0) as discount_amount_returned, --退单折扣金额
				COALESCE(sum(case when refunded then merchant_discount_amount+store_discount_amount else 0 end), 0) as merchant_allowance_returned, --退单商家补贴
				COALESCE(sum(case when refunded then commission else 0 end), 0) as commission_returned, --退单佣金
				COALESCE(sum(case when refunded then delivery_fee else 0 end), 0) as delivery_fee_returned, --退单配送费
				COALESCE(sum(case when refunded then package_fee else 0 end), 0) as package_fee_returned, --退单包装费
				COALESCE(sum(case when refunded then tip else 0 end), 0) as tip_returned, --退单小费
				COALESCE(sum(case when refunded then service_fee else 0 end), 0) as service_fee_returned, --退单服务费
				COALESCE(sum(case when refunded then tax_fee else 0 end), 0) as tax_fee_returned, --退单税费
				COALESCE(sum(case when refunded then rounding else 0 end), 0) as rounding_returned, --退单抹零
				COALESCE(sum(case when refunded then overflow_amount else 0 end), 0) as overflow_amount_returned, --退单溢收
				0 as total
			
			from sales_ticket_amounts fact
				left join store_caches on fact.store_id = store_caches.id
				left join business_days bd on fact.store_id = bd.region_id
			where
				{WHERE}
				and refunded
			group by bus_date, store_caches.company_info, fact.store_id,bd.days,fact.refund_side, fact.channel_id,refund_code
			order by bus_date desc,store_id
			{LIMIT};
		`

		summarySQL = `
			with base_refund as (
				select
					COALESCE(sum(case when refunded then gross_amount else 0 end), 0) as gross_amount_returned, --退单商品流水
					COALESCE(sum(case when refunded then eticket_count else 0 end), 0) as order_count_returned, --退单数
					COALESCE(sum(case when refunded then net_amount else 0 end), 0) as net_amount_returned, --退单商品实收
					COALESCE(sum(case when refunded then amount_4 else 0 end), 0) as receivable_returned, --退单应付金额
					COALESCE(sum(case when refunded then pay_amount else 0 end), 0) as pay_amount_returned, --退单实付
					COALESCE(sum(case when refunded then discount_amount else 0 end), 0) as discount_amount_returned, --退单折扣金额
					COALESCE(sum(case when refunded then merchant_discount_amount+store_discount_amount else 0 end), 0) as merchant_allowance_returned, --退单商家补贴
					COALESCE(sum(case when refunded then commission else 0 end), 0) as commission_returned, --退单佣金
					COALESCE(sum(case when refunded then delivery_fee else 0 end), 0) as delivery_fee_returned, --退单配送费
					COALESCE(sum(case when refunded then package_fee else 0 end), 0) as package_fee_returned, --退单包装费
					COALESCE(sum(case when refunded then tip else 0 end), 0) as tip_returned, --退单小费
					COALESCE(sum(case when refunded then service_fee else 0 end), 0) as service_fee_returned, --退单服务费
					COALESCE(sum(case when refunded then tax_fee else 0 end), 0) as tax_fee_returned, --退单税费
					COALESCE(sum(case when refunded then rounding else 0 end), 0) as rounding_returned, --退单抹零
					COALESCE(sum(case when refunded then overflow_amount else 0 end), 0) as overflow_amount_returned --退单溢收
				from sales_ticket_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where 
					{WHERE}
				group by bus_date,store_id
				order by bus_date desc, store_id
			)
			select
						'' AS bus_date,
						0 AS geo_id,
						0 AS branch_id,
						0 AS company_id,
						0 AS region_id,
						'' AS store_type,
						0 AS business_days, --营业天数,
						'' AS refund_side,
						0 as channel_id,
						'' as refund_code,
						sum(gross_amount_returned) as gross_amount_returned, --退单商品流水
						sum(order_count_returned) as order_count_returned, --退单数
						sum(net_amount_returned) as net_amount_returned, --退单商品实收
						sum(receivable_returned) as receivable_returned, --退单应付金额
						sum(pay_amount_returned) as pay_amount_returned, --退单实付
						sum(discount_amount_returned) as discount_amount_returned, --退单折扣金额
						sum(merchant_allowance_returned) as merchant_allowance_returned, --退单商家补贴
						sum(commission_returned) as commission_returned, --退单佣金
						sum(delivery_fee_returned) as delivery_fee_returned, --退单配送费
						sum(package_fee_returned) as package_fee_returned, --退单包装费
						sum(tip_returned) as tip_returned, --退单小费
						sum(service_fee_returned) as service_fee_returned, --退单服务费
						sum(tax_fee_returned) as tax_fee_returned, --退单税费
						sum(rounding_returned) as rounding_returned, --退单抹零
						sum(overflow_amount_returned) as overflow_amount_returned, --退单溢收
						count(1) as total
			from base_refund
 		`
	}

	whereSQL := getWhereSQLForRefundAnalysisV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getWhereSQLForRefundAnalysisV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		storeTypeSQL  string // 门店类型筛选条件
		openStatusSQL string // 开店状态筛选条件
		channelSQL    string // 渠道筛选条件
		refundSideSQL string // 退单方筛选
		refundCodeSQL string // 退单原因筛选
	)
	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	// 增加按渠道筛选
	//if len(condition.ChannelIds) > 0 {
	//	channelSQL = fmt.Sprintf(`AND channel_id IN (%s)`, helpers.JoinInt64(condition.ChannelIds, ","))
	//}
	if condition.RefundSide != "" {
		refundSideSQL = fmt.Sprintf("AND refund_side = '%s'\n", condition.RefundSide)
	}
	if condition.RefundCode != "" {
		refundSideSQL = fmt.Sprintf("AND refund_code = '%s'\n", condition.RefundCode)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeTypeSQL, openStatusSQL, storeSQL, channelSQL, refundSideSQL, refundCodeSQL)
	return whereSQL
}
