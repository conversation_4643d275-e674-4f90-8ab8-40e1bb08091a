package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) PaymentStatisticsV2(ctx context.Context, condition *model.RepoCondition) (*report.PaymentStatisticsResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, _ := getSQLForPaymentStatisticsV2(trackId, condition)
	ch1 := make(chan []*report.PaymentStatisticsV2, 1)
	go queryDBWithChanForPaymentStatisticsV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	response := &report.PaymentStatisticsResponseV2{
		Rows: rows,
	}
	return response, nil
}

func queryDBWithChanForPaymentStatisticsV2(id int64, db *sql.DB, sql string, ch chan []*report.PaymentStatisticsV2) {
	ch <- queryDBForPaymentStatisticsV2(id, db, sql)
}

func queryDBForPaymentStatisticsV2(trackId int64, db *sql.DB, s string) []*report.PaymentStatisticsV2 {
	results := make([]*report.PaymentStatisticsV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PaymentStatisticsV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.ChannelId,
			&f.PaymentId,
			&f.PaymentCode,
			&f.PaymentName,
			&f.Receivable,
			&f.PayAmount,
			&f.TransferRealAmount,
			&f.PaymentTransferAmount,
			&f.Cost,
			&f.TpAllowance,
			&f.Rounding,
			&f.OverflowAmount,
			&f.ChangeAmount,
			&f.ItemCount,
			&f.ItemCountReturned,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForPaymentStatisticsV2(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 行sql
		summarySQL string // 汇总sql
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			with business_days as (
				SELECT
					region_id, COUNT(1) AS days
					FROM (
						SELECT
							store_id AS region_id
						FROM
							sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
						WHERE
							{WHERE}
						GROUP BY bus_date, fact.store_id
						ORDER BY bus_date DESC, fact.store_id
					) T
						GROUP BY region_id
			)
			select
				'' AS bus_date,
				COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
				COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
				COALESCE(store_caches.company_info,0) AS company_id,
				COALESCE(store_id,0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(bd.days,0) AS business_days,
				COALESCE(fact.channel_id, '') AS channel_id, --渠道id
				COALESCE(fact.payment_id,0) AS payment_id, --支付id
				COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
				COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,
				COALESCE(SUM(receivable), 0) AS receivable, --应付金额
				COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
				COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
				COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
				COALESCE(sum(cost), 0) AS cost, -- 用户实际购买金额
				COALESCE(sum(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
				COALESCE(sum(rounding), 0) AS rounding, --抹零
				COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
				COALESCE(sum(change_amount), 0) AS change_amount, --找零
				COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
				COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned --支付退单数
			from sales_payment_amounts fact
				left join store_caches 
					on store_caches.id = fact.store_id
				left join business_days bd
					on fact.store_id = bd.region_id
			where
			   {WHERE}
			group by store_caches.company_info, store_id, bd.days, fact.channel_id, fact.payment_id
			order by store_id
			{LIMIT};
		`
		summarySQL = `
			with base_payments as (
				select
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
					COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
					COALESCE(sum(cost), 0) AS cost, -- 用户实际购买金额
					COALESCE(sum(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
					COALESCE(sum(rounding), 0) AS rounding, --抹零
					COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(sum(change_amount), 0) AS change_amount, --找零
					COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
					COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned --支付退单数
				from sales_payment_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where
					{WHERE}
				group by store_id
				order by store_id
			)
			select
				'' as bus_date,
				0 as geo_id,
				0 as branch_id,
				0 as company_id,
				0 as region_id,
				'' as store_type,
				0 AS business_days, --营业天数,
				'' as channel_id,
				0 as payment_id,
				sum(receivable) as receivable,
				sum(pay_amount) as pay_amount,
				sum(transfer_real_amount) as transfer_real_amount,
				sum(payment_transfer_amount) as payment_transfer_amount,
				sum(cost) as cost,
				sum(tp_allowance) as tp_allowance,
				sum(rounding) as rounding,
				sum(overflow_amount) as overflow_amount,
				sum(change_amount) as change_amount,
				sum(item_count) as item_count,
				sum(item_count_returned) as item_count_returned
			from base_payments
		`
	} else {
		rowSQL = `
			with business_days as (
				SELECT
					region_id, COUNT(1) AS days
					FROM (
						SELECT
							store_id AS region_id
						FROM
							sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
						WHERE
							{WHERE}
						GROUP BY fact.store_id
					) T
						GROUP BY region_id
			)
			select
				to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date,
				COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
				COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
				COALESCE(store_caches.company_info,0) AS company_id,
				COALESCE(store_id,0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(bd.days,0) AS business_days,
				COALESCE(fact.channel_id, '') AS channel_id, --渠道id
				COALESCE(fact.payment_id,0) AS payment_id, --支付id
				COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
				COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,
				COALESCE(SUM(receivable), 0) AS receivable, --应付金额
				COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
				COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
				COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
				COALESCE(sum(cost), 0) AS cost, -- 用户实际购买金额
				COALESCE(sum(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
				COALESCE(sum(rounding), 0) AS rounding, --抹零
				COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
				COALESCE(sum(change_amount), 0) AS change_amount, --找零
				COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
				COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned --支付退单数
			from sales_payment_amounts fact
				left join store_caches
					on store_caches.id = fact.store_id
				left join business_days bd
					on fact.store_id = bd.region_id
			where
			   {WHERE}
			group by fact.bus_date, store_caches.company_info, store_id, bd.days, fact.channel_id, fact.payment_id
			order by fact.bus_date desc, store_id
			{LIMIT};
		`
		summarySQL = `
			with base_payments as (
				select
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance + cost END), 0) AS transfer_real_amount,--财务实收金额(转换后)
					COALESCE(sum(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
					COALESCE(sum(cost), 0) AS cost, -- 用户实际购买金额
					COALESCE(sum(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
					COALESCE(sum(rounding), 0) AS rounding, --抹零
					COALESCE(sum(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(sum(change_amount), 0) AS change_amount, --找零
					COALESCE(sum(case when refunded then 0 else qty end), 0) AS item_count, --支付次数
					COALESCE(sum(case when refunded then qty else 0 end), 0) AS item_count_returned --支付退单数
				from sales_payment_amounts fact
					left join store_caches on fact.store_id = store_caches.id
				where
					{WHERE}
				group by bus_date, store_id
				order by bus_date desc, store_id
			)
			select
				'' as bus_date,
				0 as geo_id,
				0 as branch_id,
				0 as company_id,
				0 as region_id,
				'' as store_type,
				0 AS business_days, --营业天数,
				'' as channel_id,
				0 as payment_id,
				sum(receivable) as receivable,
				sum(pay_amount) as pay_amount,
				sum(transfer_real_amount) as transfer_real_amount,
				sum(payment_transfer_amount) as payment_transfer_amount,
				sum(cost) as cost,
				sum(tp_allowance) as tp_allowance,
				sum(rounding) as rounding,
				sum(overflow_amount) as overflow_amount,
				sum(change_amount) as change_amount,
				sum(item_count) as item_count,
				sum(item_count_returned) as item_count_returned
			from base_payments
		`
	}

	whereSQL := getWhereSQLForPaymentStatisticsV2(condition)
	limitSQL := getLimitOffsetSQLForStoreSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getWhereSQLForPaymentStatisticsV2(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		paymentSQL    string // 支付方式筛选条件
		storeTypeSQL  string // 门店类型筛选条件
		openStatusSQL string // 开店状态筛选条件
		channelSQL    string // 渠道筛选条件
	)
	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	if len(condition.PaymentIds) > 0 {
		paymentSQL = fmt.Sprintf(
			"AND payment_id IN (%s)",
			helpers.JoinInt64(condition.PaymentIds, ","),
		)
	}
	if condition.ChannelId != 0 {
		channelSQL = fmt.Sprintf(
			"AND channel_id = '%d'",
			condition.ChannelId)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentSQL, channelSQL, storeTypeSQL, openStatusSQL, storeSQL)
	return whereSQL
}
