package report_v2

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (rsmm *SalesRepositoryV2PG) DiscountSalesV2(ctx context.Context, condition *model.RepoCondition) (*report.DiscountSalesResponseV2, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForDiscountSalesV2(trackId, condition)
	ch1 := make(chan []*report.DiscountSalesV2, 1)
	go queryDBWithChanForDiscountSalesV2(trackId, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.DiscountSalesResponseV2{
		Rows: rows,
	}, nil
}

func queryDBWithChanForDiscountSalesV2(id int64, db *sql.DB, sql string, ch chan []*report.DiscountSalesV2) {
	ch <- queryDBForDiscountSalesV2(id, db, sql)
}

func queryDBForDiscountSalesV2(trackId int64, db *sql.DB, s string) []*report.DiscountSalesV2 {
	results := make([]*report.DiscountSalesV2, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.DiscountSalesV2)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.DiscountId,
			&f.CategoryId,
			&f.ProductId,
			&f.GrossAmount,
			&f.ProductCount,
			&f.DiscountAmount,
			&f.DiscountContribute,
			&f.TransferRealAmount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func getSQLForDiscountSalesV2(trackId int64, condition *model.RepoCondition) string {
	var rowSQL string // 行sql

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			--交易折扣详细信息导出SQL
				with business_days as (
					select
						region_id, count(1) as days
					from(
						select
							fact.store_id as region_id
						from sales_ticket_amounts fact
							left join store_caches on fact.store_id=store_caches.id
						where
							{DAYSWHERE}
						group by fact.bus_date, fact.store_id
							)T
					group by region_id
				)
				select
					'' AS bus_date, --营业日期
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,--地理区域
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,--管理区域
					COALESCE(store_caches.company_info,0) AS company_id,--公司
					COALESCE(store_id,0) AS region_id,--门店id
					COALESCE(max(store_caches.store_type), '') AS store_type,--门店类型
					COALESCE(max(bd.days),0) AS business_days,--营业天数
					COALESCE(fact.promotion_id,0) AS discount_id,
					coalesce(product_caches.category, 0) as category_id,
					coalesce(fact.product_id, 0) as product_id,
					coalesce(sum(gross_amount), 0) as gross_amount,
					coalesce(sum(product_count), 0) as product_count,
					coalesce(sum(discount_amount), 0) as discount_amount,
					coalesce(sum(merchant_allowance), 0) as discount_contribute, --优惠组成(财务)-折扣
					coalesce(round(sum(cost+tp_allowance)::numeric, 2), 0)::varchar AS transfer_real_amount --实收组成(财务)-折扣
				from sales_discount_amounts fact
					left join store_caches on fact.store_id = store_caches.id
					left join product_caches on fact.product_id = product_caches.id
					left join business_days bd on bd.region_id = fact.store_id
				where
					{WHERE}
				group by company_info, store_id, fact.promotion_id, product_id, product_caches.category
				order by store_id, promotion_id, product_id;
		`
	} else {
		rowSQL = `
			--交易折扣详细信息导出SQL
				with business_days as (
					select
						region_id, count(1) as days
					from(
						select
							fact.store_id as region_id
						from sales_ticket_amounts fact
							left join store_caches on fact.store_id=store_caches.id
						where
							{DAYSWHERE}
						group by fact.store_id
							)T
					group by region_id
				)
				select
					to_char(fact.bus_date, 'YYYY-MM-DD') AS bus_date, --营业日期
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,--地理区域
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,--管理区域
					COALESCE(store_caches.company_info,0) AS company_id,--公司
					COALESCE(store_id,0) AS region_id,--门店id
					COALESCE(max(store_caches.store_type), '') AS store_type,--门店类型
					COALESCE(max(bd.days),0) AS business_days,--营业天数
					COALESCE(fact.promotion_id,0) AS discount_id,
					coalesce(product_caches.category, 0) as category_id,
					coalesce(fact.product_id, 0) as product_id,
					coalesce(sum(gross_amount), 0) as gross_amount,
					coalesce(sum(product_count), 0) as product_count,
					coalesce(sum(discount_amount), 0) as discount_amount,
					coalesce(sum(merchant_allowance), 0) as discount_contribute, --优惠组成(财务)-折扣
					coalesce(round(sum(cost+tp_allowance)::numeric, 2), 0)::varchar AS transfer_real_amount --实收组成(财务)-折扣
				from sales_discount_amounts fact
					left join store_caches on fact.store_id = store_caches.id
					left join product_caches on fact.product_id = product_caches.id
					left join business_days bd on bd.region_id = fact.store_id
				where
					{WHERE}
				group by bus_date, company_info, store_id, fact.promotion_id, product_id,product_caches.category
				order by bus_date desc, store_id, promotion_id, product_id;
		`
	}

	whereSQL, whereDaySQL := getWhereSQLForDiscountSalesV2(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{DAYSWHERE}", whereDaySQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPGV2 row. SQL: `%s`", trackId, rowSQL)

	return rowSQL
}

func getWhereSQLForDiscountSalesV2(condition *model.RepoCondition) (string, string) {
	var (
		regionIdsSQL   string // 区域筛选条件
		discountIdsSQL string // 折扣方式筛选条件
		storeTypeSQL   string // 门店类型筛选条件
		openStatusSQL  string // 开店状态筛选条件
		comboSQL       string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	regionIdsSQL = GenerateRegionWhereSQLV2(condition)
	if len(condition.DiscountIds) > 0 {
		discountIdsSQL = fmt.Sprintf(
			"AND fact.promotion_id IN (%s)",
			helpers.JoinInt64(condition.DiscountIds, ","),
		)
	}
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, discountIdsSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL)
	businessDaysWhereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeTypeSQL, openStatusSQL, storeSQL)
	return whereSQL, businessDaysWhereSQL
}
