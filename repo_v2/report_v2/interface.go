package report_v2

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
)

type SalesRepositoryV2 interface {
	// 单品销售
	ProductSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.ProductSalesResponseV2, error)
	// 单品渠道
	ProductChannelSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.ProductChannelSalesResponseV2, error)
	// 单品时段
	ProductPeriodSalesV2(ctx context.Context, condition *model.RepoCondition, query *model.CommonRequest) []map[string]interface{}
	// 门店销售
	StoreSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.StoreSalesResponseV2, error)
	// 门店渠道
	StoreChannelSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.StoreChannelSalesResponseV2, error)
	// 门店时段
	StorePeriodSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.StorePeriodSalesResponseV2, error)
	// 交易折扣
	DiscountSalesV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.DiscountSalesResponseV2, error)
	// 支付统计
	PaymentStatisticsV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.PaymentStatisticsResponseV2, error)
	// 支付时段
	PaymentPeriodV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.PaymentPeriodResponseV2, error)
	// 退单分析
	RefundAnalysisV2(ctx context.Context, condition *model.RepoCondition) (*report_v2.RefundAnalysisResponseV2, error)
}
