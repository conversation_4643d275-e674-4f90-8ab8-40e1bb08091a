package services

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/test"
	"testing"
	"time"
)

func TestBOHEnabled(t *testing.T) {
	test.InitTest()
	c  := context.WithValue(context.Background(), "Authorization", "Bearer MhdlrgMxlX-NJJzLtswpNIBQqscYG3RRS35EBiVTQOE.OS7o7luDQG3sxAqlB5DomguykGYG99NExRMHDqIVnGo")
	bohEnabled(c)
}

func TestTime(t *testing.T) {
	s, err := time.Parse(time.RFC3339Nano, "2024-04-09T16:00:00.000Z")
	e, err := time.Parse(time.RFC3339Nano, "2024-04-10T15:59:59.999Z")
	if err != nil {
		t.Error(err)
	}
	println(s.In(config.ShanghaiLocation).Format("2006-01-02 15:04:05"))
	println(e.In(config.ShanghaiLocation).Format("2006-01-02 15:04:05"))
}
