package finance

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 外卖渠道
func TakeChannel(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.BusinessStatus] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用物流构成报表（只过滤门店订单类型，不过滤外卖渠道）调用默认财务报表下的物流构成接口
	resp, err := repo.DefaultFinanceRepo.ChannelReport(ctx, condition)
	// 组装查询到的结果数据
	packagingForTakeChannel(ctx, query, resp)
	return resp, err
}

func packagingForTakeChannel(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	paymentChannelMaps := report.GetPaymentChannelMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 汇总商品流水合计和订单合集
	var sumMeiTuanFlow float64
	var sumMeiTuanCount int64
	var sumEleFlow float64
	var sumEleCommission float64
	var sumEleDiscountAmount float64
	var sumEleCount int64
	var sumPosFlow float64
	var sumPosCount int64
	var sumAppFlow float64
	var sumAppCount int64
	// 将一个门店的物流构成映射到一个json中
	var storeMap = make(map[string]map[string]interface{})
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionStatus := regionMaps[regionId]["status"]
		paymentChannelId := cast.ToInt64(r["channel_id"])
		paymentChannelCode := paymentChannelMaps[paymentChannelId]["code"]
		paymentChannelName := paymentChannelMaps[paymentChannelId]["name"]
		var rowData = map[string]interface{}{
			"region_id":     cast.ToString(regionId),
			"region_code":   cast.ToString(regionCode),
			"region_name":   cast.ToString(regionName),
			"region_status": cast.ToString(regionStatus),
			"channel_id":    cast.ToString(r["channel_id"]),
			"channel_code":  cast.ToString(paymentChannelCode),
			"channel_name":  cast.ToString(paymentChannelName),
			"gross_amount":  cast.ToFloat64(r["gross_amount"]),
			//"net_amount":               cast.ToFloat64(r["net_amount"]),
			"discount_amount": cast.ToFloat64(r["discount_amount"]),
			//"tip":                      cast.ToFloat64(r["tip"]),
			"package_fee":  cast.ToFloat64(r["package_fee"]),
			"delivery_fee": cast.ToFloat64(r["delivery_fee"]),
			//"service_fee":              cast.ToFloat64(r["service_fee"]),
			//"tax_fee":                  cast.ToFloat64(r["tax_fee"]),
			//"other_fee":                cast.ToFloat64(r["other_fee"]),
			//"pay_amount":               cast.ToFloat64(r["pay_amount"]),
			//"rounding":                 cast.ToFloat64(r["rounding"]),
			//"overflow_amount":          cast.ToFloat64(r["overflow_amount"]),
			//"change_amount":            cast.ToFloat64(r["change_amount"]),
			//"order_count":              cast.ToInt64(r["order_count"]),
			"gross_amount_returned": cast.ToFloat64(r["gross_amount_returned"]),
			//"net_amount_returned":      cast.ToFloat64(r["net_amount_returned"]),
			"discount_amount_returned": cast.ToFloat64(r["discount_amount_returned"]),
			//"tip_returned":             cast.ToFloat64(r["tip_returned"]),
			//"package_fee_returned":     cast.ToFloat64(r["package_fee_returned"]),
			//"delivery_fee_returned":    cast.ToFloat64(r["delivery_fee_returned"]),
			//"service_fee_returned":     cast.ToFloat64(r["service_fee_returned"]),
			//"tax_fee_returned":         cast.ToFloat64(r["tax_fee_returned"]),
			//"other_fee_returned":       cast.ToFloat64(r["other_fee_returned"]),
			//"pay_amount_returned":      cast.ToFloat64(r["pay_amount_returned"]),
			//"rounding_returned":        cast.ToFloat64(r["rounding_returned"]),
			//"overflow_amount_returned": cast.ToFloat64(r["overflow_amount_returned"]),
			//"change_amount_returned":   cast.ToFloat64(r["change_amount_returned"]),
			//"order_count_returned":     cast.ToInt64(r["order_count_returned"]),
			"commission": cast.ToFloat64(r["commission"]),
			"amount_0":   cast.ToFloat64(r["amount_0"]),
			"amount_1":   cast.ToFloat64(r["amount_1"]),
			"amount_2":   cast.ToFloat64(r["amount_2"]),
			"amount_3":   cast.ToFloat64(r["amount_3"]),
			//"amount_4":                 cast.ToFloat64(r["amount_4"]),
			"commission_returned": cast.ToFloat64(r["commission_returned"]),
			"amount_0_returned":   cast.ToFloat64(r["amount_0_returned"]),
			"amount_1_returned":   cast.ToFloat64(r["amount_1_returned"]),
			"amount_2_returned":   cast.ToFloat64(r["amount_2_returned"]),
			"amount_3_returned":   cast.ToFloat64(r["amount_3_returned"]),
			//"amount_4_returned":        cast.ToFloat64(r["amount_4_returned"]),
		}
		// 计算堂食流水和堂食单数和外卖流水和外卖单数
		// 有效订单商品流水：gross_amount：gross_amount
		// 有效订单数：order_count
		var logisticInfo map[string]interface{}
		// 将一个门店的不同物流信息放到一个map中
		_, ok := storeMap[cast.ToString(r["region_id"])]
		if !ok {
			logisticInfo = map[string]interface{}{
				"region_id":      cast.ToString(regionId),
				"region_code":    cast.ToString(regionCode),
				"region_address": city,
				"region_name":    regionName,
			}
			storeMap[cast.ToString(regionId)] = logisticInfo
		} else {
			logisticInfo = storeMap[cast.ToString(regionId)]
		}
		// 判断外卖类型是哪种
		if cast.ToString(r["channel_name"]) == "eleme" { // 饿了么
			logisticInfo["ele_gross_amount"] = cast.ToFloat64(rowData["amount_0"])
			logisticInfo["ele_discount_amount"] = cast.ToFloat64(rowData["discount_amount"])
			logisticInfo["ele_commission"] = cast.ToFloat64(rowData["commission"])
			logisticInfo["ele_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumEleFlow += cast.ToFloat64(logisticInfo["ele_gross_amount"])
			sumEleCommission += cast.ToFloat64(logisticInfo["ele_commission"])
			sumEleDiscountAmount += cast.ToFloat64(logisticInfo["ele_discount_amount"])
			sumEleCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["channel_name"]) == "meituan" { // 美团外卖
			logisticInfo["meituan_gross_amount"] = cast.ToFloat64(rowData["amount_0"])
			logisticInfo["meituan_discount_amount"] = cast.ToFloat64(rowData["discount_amount"])
			logisticInfo["meituan_commission"] = cast.ToFloat64(rowData["commission"])
			logisticInfo["meituan_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumMeiTuanFlow += cast.ToFloat64(logisticInfo["meituan_gross_amount"])
			sumMeiTuanCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["channel_name"]) == "POS" { // POS
			logisticInfo["pos_gross_amount"] = cast.ToFloat64(rowData["amount_0"])
			logisticInfo["pos_discount_amount"] = cast.ToFloat64(rowData["discount_amount"])
			logisticInfo["pos_commission"] = cast.ToFloat64(rowData["commission"])
			logisticInfo["pos_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumPosFlow += cast.ToFloat64(logisticInfo["pos_gross_amount"])
			sumPosCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["channel_name"]) == "app" || cast.ToString(r["channel_name"]) == "APP" { // 小程序
			logisticInfo["app_gross_amount"] = cast.ToFloat64(rowData["amount_0"])
			logisticInfo["app_discount_amount"] = cast.ToFloat64(rowData["discount_amount"])
			logisticInfo["app_commission"] = cast.ToFloat64(rowData["commission"])
			logisticInfo["app_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumPosFlow += cast.ToFloat64(logisticInfo["app_gross_amount"])
			sumPosCount += cast.ToInt64(rowData["order_count"])
		}
	}
	for _, value := range storeMap {
		var storeInfo = map[string]interface{}{
			"region_id":            cast.ToString(value["region_id"]),
			"region_code":          cast.ToString(value["region_code"]),
			"region_name":          cast.ToString(value["region_name"]),
			"region_city":          cast.ToString(value["region_city"]),
			"ele_gross_amount":     cast.ToString(value["ele_gross_amount"]),
			"ele_order_count":      cast.ToString(value["ele_order_count"]),
			"ele_commission":       cast.ToString(value["ele_commission"]),
			"ele_discount_amount":  cast.ToString(value["ele_discount_amount"]),
			"meituan_gross_amount": cast.ToString(value["meituan_gross_amount"]),
			"meituan_order_count":  cast.ToString(value["meituan_order_count"]),
			"pos_gross_amount":     cast.ToString(value["pos_gross_amount"]),
			"pos_order_count":      cast.ToString(value["pos_order_count"]),
			"app_gross_amount":     cast.ToString(value["app_gross_amount"]),
			"app_order_count":      cast.ToString(value["app_order_count"]),
		}
		nRows = append(nRows, storeInfo)
	}
	resp.Rows = nRows
	resp.Summary["ele_gross_amount"] = sumEleFlow
	resp.Summary["ele_order_count"] = sumEleCount
	resp.Summary["ele_commission"] = sumEleCommission
	resp.Summary["ele_discount_amount"] = sumEleDiscountAmount
	resp.Summary["meituan_gross_amount"] = sumMeiTuanFlow
	resp.Summary["meituan_order_count"] = sumMeiTuanCount
	resp.Summary["pos_gross_amount"] = sumPosFlow
	resp.Summary["pos_order_count"] = sumPosCount
	resp.Summary["app_gross_amount"] = sumAppFlow
	resp.Summary["app_order_count"] = sumAppCount
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
}
