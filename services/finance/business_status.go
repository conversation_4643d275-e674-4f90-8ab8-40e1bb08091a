package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
)

// 财务报表：门店营业状况（按详细信息返回门店销售报表数据）
func BusinessStatus(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.BusinessStatus] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用门店销售报表接口层
	resp, err := repo.DefaultFinanceRepo.BusinessStatus(ctx, condition)
	// 组装查询到的结果数据
	packagingForBusinessStatus(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForBusinessStatus(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	// 当查询门店没有数据时，就增加一个空数据进去
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 统计所有门店总的营业额，支出，有效订单数，客单价等数据汇总
	var sumBusinessAmount = 0.0
	var sumExpendAmount = 0.0
	var sumValidOrderCount = 0
	var sumCustomerPrice = 0.0
	var sumRealAmount = 0.0
	var sumBusinessDays = 0
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]
		// 组装每一条记录，形成每一行记录
		var row = map[string]interface{}{
			"region_id":       cast.ToString(regionId),
			"region_code":     cast.ToString(regionCode),
			"region_name":     cast.ToString(regionName),
			"region_address":  cast.ToString(city), // 门店地址
			"region_status":   cast.ToString(regionStatus),
			"gross_amount":    cast.ToFloat64(r["gross_amount"]),
			"net_amount":      cast.ToFloat64(r["net_amount"]),
			"discount_amount": cast.ToFloat64(r["discount_amount"]),
			//"tip":                               cast.ToFloat64(r["tip"]),
			//"package_fee":                       cast.ToFloat64(r["package_fee"]),
			//"delivery_fee":                      cast.ToFloat64(r["delivery_fee"]),
			//"service_fee":                       cast.ToFloat64(r["service_fee"]),
			//"tax_fee":                           cast.ToFloat64(r["tax_fee"]),
			"other_fee": cast.ToFloat64(r["other_fee"]),
			//"pay_amount":                        cast.ToFloat64(r["pay_amount"]),
			"payment_transfer_amount":      cast.ToFloat64(r["payment_transfer_amount"]),
			"real_amount":                  cast.ToFloat64(r["amount_2"]),
			"discount_merchant_contribute": cast.ToFloat64(r["discount_merchant_contribute"]),
			"merchant_send_fee":            cast.ToFloat64(r["merchant_send_fee"]),
			"pay_merchant_contribute":      cast.ToFloat64(r["pay_merchant_contribute"]),
			"change_amount":                cast.ToFloat64(r["change_amount"]),
			"order_count":                  cast.ToInt64(r["order_count"]),
			"product_count":                cast.ToInt64(r["product_count"]),
			"accessory_count":              cast.ToInt64(r["accessory_count"]),
			"gross_amount_returned":        cast.ToFloat64(r["gross_amount_returned"]),
			"net_amount_returned":          cast.ToFloat64(r["net_amount_returned"]),
			"discount_amount_returned":     cast.ToFloat64(r["discount_amount_returned"]),
			"real_amount_returned":         cast.ToFloat64(r["amount_2_returned"]),
			"business_amount":              cast.ToFloat64(r["amount_0"]),
			"business_amount_returned":     cast.ToFloat64(r["amount_0_returned"]),
			//"package_fee_returned":              cast.ToFloat64(r["package_fee_returned"]),
			//"delivery_fee_returned":             cast.ToFloat64(r["delivery_fee_returned"]),
			//"service_fee_returned":              cast.ToFloat64(r["service_fee_returned"]),
			//"tax_fee_returned":                  cast.ToFloat64(r["tax_fee_returned"]),
			//"other_fee_returned":                cast.ToFloat64(r["other_fee_returned"]),
			//"pay_amount_returned":               cast.ToFloat64(r["pay_amount_returned"]),
			"rounding":          cast.ToFloat64(r["rounding"]),
			"rounding_returned": cast.ToFloat64(r["rounding_returned"]),
			//"overflow_amount_returned":          cast.ToFloat64(r["overflow_amount_returned"]),
			//"change_amount_returned":            cast.ToFloat64(r["change_amount_returned"]),
			"order_count_returned":              cast.ToInt64(r["order_count_returned"]),
			"commission":                        cast.ToFloat64(r["commission"]),
			"commission_returned":               cast.ToFloat64(r["commission_returned"]),
			"merchant_discount_amount":          cast.ToFloat64(r["merchant_discount_amount"]),          // 商家优惠承担
			"merchant_discount_amount_returned": cast.ToFloat64(r["merchant_discount_amount_returned"]), // 退单商家优惠承担
			"platform_discount_amount":          cast.ToFloat64(r["platform_discount_amount"]),          // 平台优惠承担
			"platform_discount_amount_returned": cast.ToFloat64(r["platform_discount_amount_returned"]), // 退单平台优惠承担
			"order_count_sale":                  cast.ToFloat64(r["order_count_sale"]),                  // 销售单数量
			"order_count_partialrefund":         cast.ToFloat64(r["order_count_partialrefund"]),         // 部分退款单数量
			"order_count_refund":                cast.ToFloat64(r["order_count_refund"]),                // 退款单数量
			"business_days":                     cast.ToFloat64(r["business_days"]),                     // 营业天数
		}
		// 营业额：正单营业额-部分退和退单营业额
		var businessAmount = cast.ToFloat64(row["business_amount"])
		// 支出（优惠组成）
		var expendAmount = cast.ToFloat64(r["discount_merchant_contribute"]) + cast.ToFloat64(r["merchant_send_fee"]) + cast.ToFloat64(r["pay_merchant_contribute"]) + cast.ToFloat64(r["other_fee"]) + cast.ToFloat64(r["commission"]) + cast.ToFloat64(r["rounding"])
		row["expend_amount"] = cast.ToFloat64(fmt.Sprintf("%.2f", expendAmount))
		// 实收金额(转换后):统计有效订单，正负担抵消的
		row["real_amount"] = cast.ToFloat64(fmt.Sprintf("%.2f", businessAmount-expendAmount))
		// 有效订单数:正单-全部退单数
		row["valid_order_count"] = cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"])
		orderCount := cast.ToFloat64(row["valid_order_count"])
		var value = 0.0
		if orderCount != 0 {
			value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", businessAmount/orderCount), 64)
		}
		// 客单价
		row["customer_price"] = value
		nRows = append(nRows, row)
		// 汇总营业额，支出等数据
		sumBusinessAmount += cast.ToFloat64(row["business_amount"])
		sumExpendAmount += cast.ToFloat64(row["expend_amount"])
		sumValidOrderCount += cast.ToInt(row["valid_order_count"])
		sumCustomerPrice += cast.ToFloat64(row["customer_price"])
		sumRealAmount += cast.ToFloat64(row["real_amount"])
		sumBusinessDays += cast.ToInt(row["business_days"])
	}
	// 查询的行数据
	resp.Rows = nRows
	// 查询的统计数据汇总
	resp.Summary["business_amount"] = sumBusinessAmount
	resp.Summary["expend_amount"] = sumExpendAmount
	resp.Summary["real_amount"] = sumRealAmount
	resp.Summary["order_count"] = sumValidOrderCount
	resp.Summary["valid_order_count"] = sumValidOrderCount
	resp.Summary["business_days"] = sumBusinessDays
	var customerPrice = 0.0
	if sumValidOrderCount != 0 {
		customerPrice = cast.ToFloat64(cast.ToFloat64(resp.Summary["business_amount"]) / cast.ToFloat64(sumValidOrderCount))
	}
	resp.Summary["customer_price"] = customerPrice
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
}
