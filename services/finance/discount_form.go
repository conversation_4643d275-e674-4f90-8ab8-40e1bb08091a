package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 财务报表：优惠组成
func DiscountFormFinance(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.BusinessStatus] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultFinanceRepo.DiscountReport(ctx, condition)
	// 组装查询到的结果数据
	packagingForDiscountFormFinance(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForDiscountFormFinance(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	discountMaps := report.GetDiscountMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]
		discountId := cast.ToInt64(r["promotion_id"])
		discountCode := discountMaps[discountId]["code"]
		discountName := discountMaps[discountId]["name"]
		// 组装每一条记录，形成每一行记录
		var row = map[string]interface{}{
			"region_id":              cast.ToString(regionId),
			"region_code":            cast.ToString(regionCode),
			"region_name":            cast.ToString(regionName),
			"region_address":         cast.ToString(city), // 门店地址
			"region_status":          cast.ToString(regionStatus),
			"discount_id":            cast.ToString(discountId),
			"discount_code":          cast.ToString(discountCode),
			"discount_name":          cast.ToString(discountName),
			"discount_amount":        cast.ToFloat64(r["amount"]),
			"real_amount":            cast.ToFloat64(r["real_amount"]),
			"discount_amount_refund": cast.ToFloat64(r["amount_returned"]),
			"real_amount_refund":     cast.ToFloat64(r["real_amount_refund"]),
		}
		nRows = append(nRows, row)
	}
	// 将同一个门店的不同折扣类别拆分到child字段里面
	var newRows = make([]interface{}, 0)
	var storeMap = make(map[string]map[string]interface{})
	var promotionSum = make(map[string]interface{})
	var sumRealAmount = 0.0
	var sumDiscountAmount = 0.0
	for index, _ := range nRows {
		storeRow := cast.ToStringMap(nRows[index])
		// sum里面的数据计算
		discountId := cast.ToString(storeRow["discount_id"])
		promoInfo, ok := promotionSum[discountId]
		if !ok {
			promotionSum[discountId] = map[string]float64{
				"real_amount":     cast.ToFloat64(storeRow["real_amount"]),
				"discount_amount": cast.ToFloat64(storeRow["discount_amount"]),
			}
		} else {
			promoMap := promoInfo.(map[string]float64)
			promoMap["real_amount"] += cast.ToFloat64(storeRow["real_amount"])
			promoMap["discount_amount"] += cast.ToFloat64(storeRow["discount_amount"])
		}
		// row里面的数据计算
		regionId := cast.ToString(storeRow["region_id"])
		storeInfo, ok := storeMap[regionId]
		fmt.Println(storeInfo)
		if !ok {
			storeTwo := mapcody(storeRow)
			storeRow["child"] = []map[string]interface{}{storeTwo}
			storeMap[regionId] = storeRow
			continue
		}
		storeInfo["discount_amount"] = cast.ToFloat64(storeInfo["discount_amount"]) + cast.ToFloat64(storeRow["discount_amount"])
		storeInfo["discount_amount_refund"] = cast.ToFloat64(storeInfo["discount_amount_refund"]) + cast.ToFloat64(storeRow["discount_amount_refund"])
		storeChild := cast.ToSlice(storeInfo["child"])
		storeChild = append(storeChild, mapcody(storeRow))
		storeInfo["child"] = storeChild
	}
	for _, value := range storeMap {
		newRows = append(newRows, value)
	}
	for _, value := range promotionSum {
		stringMap := value.(map[string]float64)
		sumRealAmount += stringMap["real_amount"]
		sumDiscountAmount += stringMap["discount_amount"]
	}
	promotionSum["real_amount"] = sumRealAmount
	promotionSum["discount_amount"] = sumDiscountAmount
	// 查询的行数据
	resp.Rows = newRows
	resp.Summary = promotionSum
}

// 对map进行值拷贝
func mapcody(src map[string]interface{}) map[string]interface{} {
	dec := map[string]interface{}{}
	for i, _ := range src {
		dec[i] = src[i]
	}
	return dec
}
