package finance

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 财务报表：支付统计方式
func PaymentServiceFinance(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.BusinessStatus] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultFinanceRepo.NetReport(ctx, condition)
	// 组装查询到的结果数据
	packagingForPaymentServiceFinance(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForPaymentServiceFinance(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	paymentMaps := report.GetPaymentMethodMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]
		paymentId := cast.ToInt64(r["payment_id"])
		paymentCode := paymentMaps[paymentId]["code"]
		paymentName := paymentMaps[paymentId]["name"]
		// 组装每一条记录，形成每一行记录
		var row = map[string]interface{}{
			"region_id":      cast.ToString(regionId),
			"region_code":    cast.ToString(regionCode),
			"region_name":    cast.ToString(regionName),
			"region_address": cast.ToString(city), // 门店地址
			"region_status":  cast.ToString(regionStatus),
			"payment_id":     cast.ToString(paymentId),
			"payment_code":   cast.ToString(paymentCode),
			"payment_name":   cast.ToString(paymentName),
			"real_amount":    cast.ToFloat64(r["real_amount"]),
		}
		nRows = append(nRows, row)
	}
	// 按门店统计将支付方式放到child里面
	var newRows = make([]interface{}, 0)
	var storeMap = make(map[string]map[string]interface{})
	for index, _ := range nRows {
		storeRow := cast.ToStringMap(nRows[index])
		// row里面的数据计算
		regionId := cast.ToString(storeRow["region_id"])
		storeInfo, ok := storeMap[regionId]
		if !ok {
			storeTwo := mapcody(storeRow)
			storeRow["child"] = []map[string]interface{}{storeTwo}
			storeMap[regionId] = storeRow
			continue
		}
		storeInfo["real_amount"] = cast.ToFloat64(storeInfo["real_amount"]) + cast.ToFloat64(storeRow["real_amount"])
		storeChild := cast.ToSlice(storeInfo["child"])
		storeChild = append(storeChild, mapcody(storeRow))
		storeInfo["child"] = storeChild
	}
	for _, value := range storeMap {
		newRows = append(newRows, value)
	}
	// 查询的行数据
	resp.Rows = newRows
}
