package finance

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 财务报表：物流构成（可以借用门店渠道报表）
func QueryLogisticsForm(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.BusinessStatus] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用物流构成报表（只过滤门店订单类型，不过滤外卖渠道）调用默认财务报表下的物流构成接口
	resp, err := repo.DefaultFinanceRepo.LogisticsReport(ctx, condition)
	// 组装查询到的结果数据
	packagingForStoreLogisticForm(ctx, query, resp)
	return resp, err
}

func packagingForStoreLogisticForm(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 汇总的流水和订单数
	var sumDineinFlow float64
	var sumDineinCount int64
	var sumTakeOutFlow float64
	var sumTakeOutCount int64
	var sumSelfHelpFlow float64
	var sumSelfHelpCount int64
	var sumTakeAwayFlow float64
	var sumTakeAwayCount int64
	// 将一个门店的物流构成映射到一个json中
	var storeMap = make(map[string]map[string]interface{})
	for _, row := range resp.Rows {
		// 判断store_id 是否已经在
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionStatus := regionMaps[regionId]["status"]
		var rowData = map[string]interface{}{
			"region_id":     cast.ToString(regionId),
			"region_code":   cast.ToString(regionCode),
			"region_name":   cast.ToString(regionName),
			"region_city":   cast.ToString(city),
			"region_status": cast.ToString(regionStatus),
			"order_type":    cast.ToString(r["order_type"]),
			"gross_amount":  cast.ToFloat64(r["gross_amount"]),
			//"net_amount":               cast.ToFloat64(r["net_amount"]),
			//"discount_amount":          cast.ToFloat64(r["discount_amount"]),
			//"tip":                      cast.ToFloat64(r["tip"]),
			"package_fee":  cast.ToFloat64(r["package_fee"]),
			"delivery_fee": cast.ToFloat64(r["delivery_fee"]),
			//"service_fee":              cast.ToFloat64(r["service_fee"]),
			//"tax_fee":                  cast.ToFloat64(r["tax_fee"]),
			//"other_fee":                cast.ToFloat64(r["other_fee"]),
			//"pay_amount":               cast.ToFloat64(r["pay_amount"]),
			//"rounding":                 cast.ToFloat64(r["rounding"]),
			//"overflow_amount":          cast.ToFloat64(r["overflow_amount"]),
			//"change_amount":            cast.ToFloat64(r["change_amount"]),
			"order_count":           cast.ToInt64(r["order_count"]),
			"gross_amount_returned": cast.ToFloat64(r["gross_amount_returned"]),
			//"net_amount_returned":      cast.ToFloat64(r["net_amount_returned"]),
			//"discount_amount_returned": cast.ToFloat64(r["discount_amount_returned"]),
			//"tip_returned":             cast.ToFloat64(r["tip_returned"]),
			//"package_fee_returned":     cast.ToFloat64(r["package_fee_returned"]),
			//"delivery_fee_returned":    cast.ToFloat64(r["delivery_fee_returned"]),
			//"service_fee_returned":     cast.ToFloat64(r["service_fee_returned"]),
			//"tax_fee_returned":         cast.ToFloat64(r["tax_fee_returned"]),
			//"other_fee_returned":       cast.ToFloat64(r["other_fee_returned"]),
			//"pay_amount_returned":      cast.ToFloat64(r["pay_amount_returned"]),
			"order_count_sale":     cast.ToInt64(r["order_count_sale"]),
			"order_count_returned": cast.ToInt64(r["order_count_returned"]),
		}
		// 计算堂食流水和堂食单数和外卖流水和外卖单数
		//gross_amount：gross_amount
		// 有效订单数:正单-全部退单数
		rowData["valid_order_count"] = cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"])
		var logisticInfo map[string]interface{}
		// 将一个门店的不同物流信息放到一个map中
		_, ok := storeMap[cast.ToString(r["region_id"])]
		if !ok {
			logisticInfo = map[string]interface{}{
				"region_id":   cast.ToString(regionId),
				"region_code": cast.ToString(regionCode),
				"region_city": city,
				"region_name": regionName,
			}
			storeMap[cast.ToString(regionId)] = logisticInfo
		} else {
			logisticInfo = storeMap[cast.ToString(regionId)]
		}
		// 判断物流类型是哪种
		if cast.ToString(r["order_type"]) == "DINEIN" {
			logisticInfo["dinein_gross_amount"] = cast.ToFloat64(r["gross_amount"]) + cast.ToFloat64(r["package_fee"]) + cast.ToFloat64(r["delivery_fee"])
			logisticInfo["dinein_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumDineinFlow += cast.ToFloat64(logisticInfo["dinein_gross_amount"])
			sumDineinCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["order_type"]) == "SELFHELP" {
			logisticInfo["selfhelp_gross_amount"] = cast.ToFloat64(r["gross_amount"]) + cast.ToFloat64(r["package_fee"]) + cast.ToFloat64(r["delivery_fee"])
			logisticInfo["selfhelp_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumSelfHelpFlow += cast.ToFloat64(logisticInfo["selfhelp_gross_amount"])
			sumSelfHelpCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["order_type"]) == "TAKEOUT" {
			logisticInfo["takeout_gross_amount"] = cast.ToFloat64(r["gross_amount"]) + cast.ToFloat64(r["package_fee"]) + cast.ToFloat64(r["delivery_fee"])
			logisticInfo["takeout_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumTakeOutFlow += cast.ToFloat64(logisticInfo["takeout_gross_amount"])
			sumTakeOutCount += cast.ToInt64(rowData["order_count"])
		} else if cast.ToString(r["order_type"]) == "TAKEAWAY" {
			logisticInfo["takeaway_gross_amount"] = cast.ToFloat64(r["gross_amount"]) + cast.ToFloat64(r["package_fee"]) + cast.ToFloat64(r["delivery_fee"])
			logisticInfo["takeaway_order_count"] = cast.ToFloat64(rowData["order_count"])
			sumTakeAwayFlow += cast.ToFloat64(logisticInfo["takeaway_gross_amount"])
			sumTakeAwayCount += cast.ToInt64(rowData["order_count"])
		}
	}
	// 将映射好的数据组装为键值对类型
	for _, value := range storeMap {
		var storeInfo = map[string]interface{}{
			"region_id":             cast.ToString(value["region_id"]),
			"region_code":           cast.ToString(value["region_code"]),
			"region_name":           cast.ToString(value["region_name"]),
			"region_city":           cast.ToString(value["region_city"]),
			"dinein_gross_amount":   cast.ToFloat64(value["dinein_gross_amount"]),
			"dinein_order_count":    cast.ToInt(value["dinein_order_count"]),
			"selfhelp_gross_amount": cast.ToFloat64(value["selfhelp_gross_amount"]),
			"selfhelp_order_count":  cast.ToInt(value["selfhelp_order_count"]),
			"takeout_gross_amount":  cast.ToFloat64(value["takeout_gross_amount"]),
			"takeout_order_count":   cast.ToInt(value["takeout_order_count"]),
			"takeaway_gross_amount": cast.ToFloat64(value["takeaway_gross_amount"]),
			"takeaway_order_count":  cast.ToInt(value["takeaway_order_count"]),
		}
		nRows = append(nRows, storeInfo)
	}
	resp.Rows = nRows
	resp.Summary["dinein_gross_amount"] = sumDineinFlow
	resp.Summary["dinein_order_count"] = sumDineinCount
	resp.Summary["selfhelp_gross_amount"] = sumSelfHelpFlow
	resp.Summary["selfhelp_order_count"] = sumSelfHelpCount
	resp.Summary["takeout_gross_amount"] = sumTakeOutFlow
	resp.Summary["takeout_order_count"] = sumTakeOutCount
	resp.Summary["takeaway_gross_amount"] = sumTakeAwayFlow
	resp.Summary["takeaway_order_count"] = sumTakeAwayCount
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
}
