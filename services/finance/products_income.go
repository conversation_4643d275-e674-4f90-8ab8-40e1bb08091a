package finance

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 财务报表：菜品收入
func ProductIncomeFinance(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.finance.ProductIncome] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	query.TagType = "SUMMARY"
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultFinanceRepo.ProductIncome(ctx, condition)
	// 组装查询到的结果数据
	packagingForProductIncomeFinance(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForProductIncomeFinance(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := report.GetRegionMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]
		// 组装每一条记录，形成每一行记录
		var row = map[string]interface{}{
			"region_id":             cast.ToString(regionId),
			"region_code":           cast.ToString(regionCode),
			"region_name":           cast.ToString(regionName),
			"region_address":        cast.ToString(city), // 门店地址
			"region_status":         cast.ToString(regionStatus),
			"gross_amount":          cast.ToFloat64(r["gross_amount"]),
			"package_fee":           cast.ToFloat64(r["package_fee"]),
			"delivery_fee":          cast.ToFloat64(r["delivery_fee"]),
			"gross_amount_returned": cast.ToFloat64(r["gross_amount_returned"]),
			"package_fee_returned":  cast.ToFloat64(r["package_fee_returned"]),
			"delivery_fee_returned": cast.ToFloat64(r["delivery_fee_returned"]),
			"small_note":            cast.ToFloat64(r["gross_amount"]) + cast.ToFloat64(r["package_fee"]) + cast.ToFloat64(r["delivery_fee"]),
		}
		nRows = append(nRows, row)
	}
	// 统计summary里面的和
	stringMap := cast.ToStringMap(resp.Summary)
	deliveryFee := cast.ToFloat64(stringMap["delivery_fee"])
	packageFee := cast.ToFloat64(stringMap["package_fee"])
	grossAmount := cast.ToFloat64(stringMap["gross_amount"])
	resp.Summary["small_note"] = deliveryFee + packageFee + grossAmount
	// 查询的行数据
	resp.Rows = nRows
}
