package _import

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"strconv"
	"time"
)

func ImportExchangeRates(ctx context.Context, bs []byte) (string, error) {
	if len(bs) == 0 {
		return "", errors.New("文件内容为空")
	}
	rates, err := parseExchangeRates(ctx, bs)
	if err != nil {
		return "", err
	}

	err = repo.DefaultMetadataCache.SaveExchangeRates(ctx, rates)
	if err != nil {
		return "", err
	}

	return "成功导入" + cast.ToString(len(rates)) + "条数据", nil
}

func parseExchangeRates(ctx context.Context, bs []byte) ([]*model.ExchangeRate, error) {
	f, err := excelize.OpenReader(bytes.NewReader(bs))
	if err != nil {
		return nil, err
	}
	// 获取第一个sheet
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	logger.Pre().Infof("exchange_rate_rows: %v \n", rows)
	// 读取表头
	header := rows[0]
	if len(header) < 2 {
		return nil, errors.New("表头不足2列")
	}

	rates := make(ExchangeRateRows, 0)
	identityMap := make(map[ExchangeRateRow]int)
	// 读取每行数据
	for i := 1; i < len(rows); i++ {
		rate, err := parseExchangeRateRow(rows[i])
		if err != nil {
			return nil, errors.New("第" + cast.ToString(i) + "行: " + err.Error())
		}
		if rate != nil {
			identity := rate.toIdentity()
			if v, ok := identityMap[identity]; ok {
				return nil, errors.New(fmt.Sprintf("第%v行: 与第%v行数据重复", i, v))
			} else {
				identityMap[identity] = i
			}
			rates = append(rates, rate)
		} else {
			logger.Pre().Infof("第%d行为空行 \n", i)
		}
	}
	return rates.toGoalDatas(ctx)
}

type ExchangeRateRow struct {
	Date time.Time
	Rate float64
}

func (r *ExchangeRateRow) toIdentity() ExchangeRateRow {
	return ExchangeRateRow{
		Date: r.Date,
		Rate: 0,
	}
}

type ExchangeRateRows []*ExchangeRateRow

func (rows ExchangeRateRows) toGoalDatas(ctx context.Context) ([]*model.ExchangeRate, error) {
	now := time.Now()
	partnerId := cast.ToInt64(ctx.Value("partner_id"))
	ids := uuid.GetIds(len(rows))
	datas := make([]*model.ExchangeRate, len(rows))
	for i, g := range rows {
		datas[i] = &model.ExchangeRate{
			Id:           ids[i],
			PartnerId:    partnerId,
			FromCurrency: "CNY",
			ToCurrency:   "HKD",
			Rate:         g.Rate,
			StartDate:    g.Date,
			EndDate:      g.Date.AddDate(0, 1, -1),
			Deleted:      false,
			CreateAt:     now,
			UpdateAt:     now,
		}
	}
	return datas, nil
}

func parseExchangeRateRow(row []string) (*ExchangeRateRow, error) {
	allEmpty := true
	for _, s := range row {
		if s != "" {
			allEmpty = false
			break
		}
	}
	if allEmpty {
		return nil, nil
	}
	if len(row) < 2 {
		return nil, errors.New("数据不足2列")
	}
	if row[0] == "" {
		return nil, errors.New("日期不能为空")
	}

	date, err := time.Parse("2006/01", row[0])
	if err != nil {
		return nil, errors.New("日期格式错误")
	}

	if row[1] == "" {
		return nil, errors.New("汇率不能为空")
	}

	rate, err := strconv.ParseFloat(row[1], 64)

	if err != nil {
		return nil, errors.New("汇率格式错误")
	}

	return &ExchangeRateRow{
		Date: date.UTC(),
		Rate: rate,
	}, nil
}
