package _import

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"strconv"
	"time"
)

func ImportStoreGoals(ctx context.Context, bs []byte) (string, error) {
	if len(bs) == 0 {
		return "", errors.New("文件内容为空")
	}
	goals, err := parseStoreGoals(ctx, bs)
	if err != nil {
		return "", err
	}

	err = repo.DefaultMetadataCache.SaveStoreGoals(ctx, goals)
	if err != nil {
		return "", err
	}

	return "成功导入" + cast.ToString(len(goals)) + "条数据", nil
}

func parseStoreGoals(ctx context.Context, bs []byte) ([]*model.StoreGoal, error) {
	f, err := excelize.OpenReader(bytes.NewReader(bs))
	if err != nil {
		return nil, err
	}
	// 获取第一个sheet
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	logger.Pre().Infof("store_goal_rows: %v \n", rows)
	// 读取表头
	header := rows[0]
	if len(header) < 3 {
		return nil, errors.New("表头不足3列")
	}

	goals := make(StoreGoalRows, 0)
	identityMap := make(map[StoreGoalRow]int)
	// 读取每行数据
	for i := 1; i < len(rows); i++ {
		goal, err := parseStoreGoalRow(rows[i])
		if err != nil {
			return nil, errors.New("第" + cast.ToString(i) + "行: " + err.Error())
		}
		if goal != nil {
			identity := goal.toIdentity()
			if v, ok := identityMap[identity]; ok {
				return nil, errors.New(fmt.Sprintf("第%v行: 与第%v行数据重复", i, v))
			} else {
				identityMap[identity] = i
			}
			goals = append(goals, goal)
		} else {
			logger.Pre().Infof("第%d行为空行 \b", i)
		}
	}
	return goals.toGoalDatas(ctx)
}

type StoreGoalRow struct {
	StoreCode string
	GoalDate  time.Time
	Goal      float64
}

func (r *StoreGoalRow) toIdentity() StoreGoalRow {
	return StoreGoalRow{
		StoreCode: r.StoreCode,
		GoalDate:  r.GoalDate,
		Goal:      0,
	}
}

type StoreGoalRows []*StoreGoalRow

func (rows StoreGoalRows) toGoalDatas(ctx context.Context) ([]*model.StoreGoal, error) {
	storeCodes := make([]string, len(rows))
	for i, g := range rows {
		storeCodes[i] = g.StoreCode
	}

	idMap, err := entity.GetIdByCodes(ctx, "store", storeCodes, "zh-CN")
	if err != nil {
		return nil, err
	}

	ids := uuid.GetIds(len(rows))
	now := time.Now()
	partnerId := cast.ToInt64(ctx.Value("partner_id"))
	goalDatas := make([]*model.StoreGoal, len(rows))
	for i, g := range rows {
		storeId, ok := idMap[g.StoreCode]
		if !ok {
			return nil, errors.New("第" + cast.ToString(i+1) + "行: " + "门店编码不存在对应门店")
		}
		goalDatas[i] = &model.StoreGoal{
			Id:        ids[i],
			PartnerId: partnerId,
			StoreId:   storeId,
			GoalDate:  g.GoalDate,
			Goal:      g.Goal,
			Deleted:   false,
			CreateAt:  now,
			UpdateAt:  now,
		}
	}
	return goalDatas, nil
}

func parseStoreGoalRow(row []string) (*StoreGoalRow, error) {
	allEmpty := true
	for _, s := range row {
		if s != "" {
			allEmpty = false
			break
		}
	}
	if allEmpty {
		return nil, nil
	}
	if len(row) < 3 {
		return nil, errors.New("数据不足3列")
	}
	if row[0] == "" {
		return nil, errors.New("门店编码不能为空")
	}

	goalDate, err := time.Parse("2006/01/02", row[1])
	if err != nil {
		return nil, errors.New("日期格式错误")
	}

	if row[2] == "" {
		return nil, errors.New("门店目标值不能为空")
	}

	goal, err := strconv.ParseFloat(row[2], 64)

	if err != nil {
		return nil, errors.New("门店目标值格式错误")
	}

	return &StoreGoalRow{
		StoreCode: row[0],
		GoalDate:  goalDate.UTC(),
		Goal:      goal,
	}, nil
}
