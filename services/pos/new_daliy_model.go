package pos

// EmployeeFood 员工餐
type EmployeeFood struct {
	PayAmount float64 `json:"payAmount"`
	Count     int     `json:"count"`
}

// StoreCashManage 门店现金管理
type StoreCashManage struct {
	PettyCash          float64 `json:"pettyCash"`          // 备用金
	BalanceCash        float64 `json:"balanceCash"`        //现金结余
	TurnoverCash       float64 `json:"turnoverCash"`       //现金营业额
	CollectedPettyCash float64 `json:"collectedPettyCash"` // 零用现金领用(领取零用金)
	DifferenceCash     float64 `json:"differenceCash"`     //现金清点差异
}

// BankCash 银行存款
type BankCash struct {
	BalanceCash                  float64 `json:"balanceCash"`                  //现金结余
	CollectPettyCash             float64 `json:"collectPettyCash"`             //领取备用金
	YesterdayPendingEscortAmount float64 `json:"yesterdayPendingEscortAmount"` //昨天累计待交押运金额
	PaidEscortAmount             float64 `json:"paidEscortAmount"`             //已交押运金额
	PendingEscortAmount          float64 `json:"PendingEscortAmount"`          //待交押运金额
}

// SummaryOfPettyCash 零用现金摘要
type SummaryOfPettyCash struct {
	OpeningStoreCash     float64 `json:"openingStoreCash"`     //开店零用现金
	PettyCashBalance     float64 `json:"pettyCashBalance"`     //零用现金开始结余
	CashReceived         float64 `json:"cashReceived"`         //已收取现金
	OtherPettyCashIncome float64 `json:"otherPettyCashIncome"` // 其它零用现金来源
	Expenditure          float64 `json:"expenditure"`          //零用现金支出
	CashBalance          float64 `json:"cashBalance"`          //零用现金结余
}

// OrderCancellation 订单取消
type OrderCancellation struct {
	Code string `json:"code"`
	Name string `json:"name"` //订单取消人
	Statistics
}

// Statistics 统计
type Statistics struct {
	Count          int     `json:"count"`          //单数
	DiscountAmount float64 `json:"discountAmount"` //折扣金额
	Amount         float64 `json:"amount"`         //金额
}

type ReportStatistics struct {
	BusinessAmount float64    `json:"businessAmount"` //营业金额
	RealAmount     float64    `json:"realAmount"`     //实收金额
	DiscountAmount float64    `json:"discountAmount"` //折扣金额
	Count          int        `json:"count"`
	AverageBill    float64    `json:"averageBill"`   //账单平均
	PeopleAverage  Statistics `json:"peopleAverage"` //人数/平均
	AverageDineIn  Statistics `json:"averageDineIn"` //平均/堂食
}

// Audit 核数
type Audit struct {
	OrderCancellations  []OrderCancellation `json:"orderCancellations"`  //订单取消
	OrderCancellation   OrderCancellation   `json:"orderCancellation"`   //订单取消
	Refund              Statistics          `json:"refund"`              //退款
	CombineTableOrder   Statistics          `json:"combineTableOrder"`   //合并账单数目
	AllTicketStatistics Statistics          `json:"allTicketStatistics"` //账单数目
	AverageBill         float64             `json:"averageBill"`         //账单平均
	PeopleAverage       Statistics          `json:"peopleAverage"`       //人数/平均
	AverageDineIn       Statistics          `json:"averageDineIn"`       //平均/堂食
}

type ProductCategory struct {
	CategoryName   string  `json:"categoryName"`   //商品分类
	DiscountAmount float64 `json:"discountAmount"` //折扣金额
	Count          int     `json:"count"`          //单数
	Amount         float64 `json:"amount"`         //营业额
}

// PeriodReport 餐段统计
type PeriodReport struct {
	MealName           string                     `json:"mealName"` //餐段名称
	Count              int                        `json:"count"`
	DineInCount        int                        `json:"dineInCount"`
	Period             []string                   `json:"period"`             //餐段时间
	BowlNum            int32                      `json:"bowlNum"`            // 碗数
	PeopleNumber       int                        `json:"peopleNumber"`       //人数
	DineInNumber       int                        `json:"dineInNumber"`       //堂食人数
	Amount             float64                    `json:"amount"`             //营业净额
	DineInAmount       float64                    `json:"dineInAmount"`       //堂食净额
	DiscountAmount     float64                    `json:"discountAmount"`     //折扣金额
	NonOperatingAmount float64                    `json:"nonOperatingAmount"` //非经营收入
	PackageFee         float64                    `json:"packageFee"`         // 包装费
	DeliveryFee        float64                    `json:"deliveryFee"`        // 累计配送费
	ServiceFee         float64                    `json:"serviceFee"`         // 服务费
	OverflowAmount     float64                    `json:"overflowAmount"`     // 溢收
	Rounding           float64                    `json:"rounding"`           // 抹零
	Tip                float64                    `json:"tip"`                // 小费
	TaxAmount          float64                    `json:"taxAmount"`          // 税额
	SurChargeAmount    float64                    `json:"surChargeAmount"`    //支付手续费
	RealTaxAmount      float64                    `json:"realTaxAmount"`      // 真正的税额
	AverageBill        float64                    `json:"averageBill"`        //账单平均
	PeopleAverage      float64                    `json:"peopleAverage"`      //人数/平均
	AverageDineIn      float64                    `json:"averageDineIn"`      //平均/堂食
	AverageBowlNum     float64                    `json:"averageBowlNum"`     // 碗数/平均
	CategoriesMap      map[string]ProductCategory `json:"categoriesMap"`      //商品类别统计
	Categories         []ProductCategory          `json:"categories"`         //商品类别统计
	Sort               int                        `json:"sort"`
}

type NewPayItem struct {
	PayAmount         float64 `json:"payAmount"`
	Count             int     `json:"count"`
	PaymentName       string  `json:"paymentName"`
	NegativeCount     int     `json:"negativeCount"`
	Sort              int     `json:"sort"`
	TransCode         string  `json:"transCode"`         // 支付方式编码
	MerchantAllowance float64 `json:"merchantAllowance"` //(财务)商家补贴金额
	SurChargeAmount   float64 `json:"surChargeAmount"`   //支付手续费
}

type NewPromotionChannel struct {
	Code             string  `json:"code"`
	Type             string  `json:"type"`
	Name             string  `json:"name"`
	Source           string  `json:"source"`
	IsProduct        bool    `json:"isProduct"`
	PromotionCoupons NewItem `json:"promotionCoupons"`
	PromotionProduct NewItem `json:"promotionProduct"`
}

type NewItem struct {
	Discount   float64 `json:"discount"`   //优惠
	RealAmount float64 `json:"realAmount"` //商家实收
	Sum        float64 `json:"sum"`        //总金额
	Count      int     `json:"count"`
}

type NonSalesCoupon struct {
	Name     string  `json:"name"`
	Count    int     `json:"count"`
	Discount float64 `json:"discount"` // 折扣金额
}

type NonSalesPayment struct {
	Name     string  `json:"name"`
	Count    int     `json:"count"`
	Discount float64 `json:"discount"` // 折扣金额
}

type TotalStatistics struct {
	NonSalesCoupons  []NonSalesCoupon `json:"nonSalesCoupons"`
	NonSalesPayments []NonSalesCoupon `json:"nonSalesPayments"`
	SurChargeAmount  float64          `json:"surChargeAmount"` //支付手续费
	BusinessAmount   float64          `json:"businessAmount"`  //营业金额
	DiscountAmount   float64          `json:"discountAmount"`  //折扣金额
	Amount           float64          `json:"amount"`          //营业净额
}

type ApiCashReportRes struct {
	InitialFloat           int32 `json:"initialFloat"`           // 开机银头
	TodayBalance           int32 `json:"todayBalance"`           // 当天现金结余
	SalesRevenue           int32 `json:"salesRevenue"`           // 当天现金营业额
	CashDifference         int32 `json:"cashDifference"`         // 清点现金差异
	CollectedPettyCash     int32 `json:"collectedPettyCash"`     // 零用现金领用(领取零用金)
	PreviousPendingDeposit int32 `json:"previousPendingDeposit"` // 昨日累计待存款金额
	Deposited              int32 `json:"deposited"`              // 移交押运金额
	FinalPendingDeposit    int32 `json:"finalPendingDeposit"`    // 当天累计待存款金额 (待交押运金额)
	InitialPettyCash       int32 `json:"initialPettyCash"`       // 昨日累计待存款金额 (零用现金初始余额)
	PettyCashSum           int32 `json:"pettyCashSum"`           // 已收取现金 = 领用零用现金+其他零用现金来源 (零用现金入)
	PettyCashExpenses      int32 `json:"pettyCashExpenses"`      // 支出 = 零用现金支出
	TodayPettyCashBalance  int32 `json:"todayPettyCashBalance"`  // 零用现金结余
	OtherPettyCashIncome   int32 `json:"otherPettyCashIncome"`   // 其它零用现金来源
	OtherIncome            int32 `json:"otherIncome"`            // 其他现金收入 (现金入) pay in 总和
	StartupPettyCash       int32 `json:"startupPettyCash"`       // 开店零用现金
}

type ApiCombineTableOrderReportReq struct {
	StoreID int64  `json:"storeID"`
	BusDate string `json:"busDate"`
}
type CashManageResp struct {
	StoreCashManage    StoreCashManage    `json:"storeCashManage"`    //门店现金管理
	BankCash           BankCash           `json:"bankCash"`           //银行存款
	SummaryOfPettyCash SummaryOfPettyCash `json:"summaryOfPettyCash"` //零用现金摘要
}

type DailyInfo struct {
	Store                   storeDailyInfo        `json:"store"`                   // 门店信息
	TotalStatistics         TotalStatistics       `json:"totalStatistics"`         // 总账目统计
	Pays                    []NewPayItem          `json:"pays"`                    //支付统计
	TotalPayItemAmount      float64               `json:"totalPayItemAmount"`      //支付统计小记
	PayStatistics           Statistics            `json:"PayStatistics"`           // 支付笔数 金额统计
	PayItemDifference       float64               `json:"payItemDifference"`       // 支付差异
	StoreCashManage         StoreCashManage       `json:"storeCashManage"`         //门店现金管理
	BankCash                BankCash              `json:"bankCash"`                //银行存款
	SummaryOfPettyCash      SummaryOfPettyCash    `json:"summaryOfPettyCash"`      //零用现金摘要
	PromotionsChannelMap    []NewPromotionChannel `json:"promotionsChannelMap"`    //折扣详细资料
	DiscountDealsStatistics Statistics            `json:"discountDealsStatistics"` //	//折扣统计
	Audit                   Audit                 `json:"audit"`                   //核数
	PeriodReports           []PeriodReport        `json:"periodReports"`           //餐段统计
	AllProduct              PeriodReport          `json:"allProduct"`              //总金额统计
}
