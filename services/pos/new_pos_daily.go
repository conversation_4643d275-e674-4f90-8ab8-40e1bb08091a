package pos

import (
	"context"
	"encoding/json"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"sort"
)

const (
	DINEIN   = "DINEIN"
	TAKEAWAY = "TAKEAWAY"
	TAKEOUT  = "TAKEOUT"
	SELFHELP = "SELFHELP"
	cash     = "Cash"
	nonSALES = "NON_SALES"
	other    = "其他"
)

// CalTicketAmount 根据ticket获取营业额净值
func CalTicketAmount(ticket *model.Ticket) float64 {
	// 营业额统计
	businessAmount := ticket.Amounts.BusinessAmount + ticket.Amounts.SurChargeAmount
	// 折扣金额统计
	discountAmount := ticket.Amounts.DiscountMerchantContribute + ticket.Amounts.Commission + ticket.Amounts.OtherFee + ticket.TakeawayInfo.MerchantSendFee + ticket.Amounts.PayMerchantContribute + ticket.Amounts.Rounding - ticket.Amounts.OverflowAmount

	return utils.FloatFmt(businessAmount - discountAmount)
}

// CalPaymentAndPromotion  计算支付统计 促销统计
func CalPaymentAndPromotion(payAllCount int, cashAmount float64, paymentsMap map[string]NewPayItem, promotionsMap map[string]NewPromotionChannel, ticket *model.Ticket, nonOperatingPayment float64) (map[string]NewPayItem, map[string]NewPromotionChannel, int, float64) {
	var (
		payTmp            float64
		businessAmountTmp float64
	)
	for _, payment := range ticket.Payments {
		var (
			isNonSALES bool
		)
		for _, t := range payment.TransactionTypes {
			if t == nonSALES {
				isNonSALES = true
			}
		}
		if isNonSALES {
			continue
		}
		var payName string
		if len(payment.RefundPathName) != 0 {
			payName = payment.RefundPathName
		} else {
			if len(payment.TransName) != 0 {
				payName = payment.TransName
			} else if len(payment.CardType) != 0 {
				payName = payment.CardType
			} else {
				payName = payment.Name
			}
		}
		payItem, ok := paymentsMap[payName]
		if !ok {
			payItem = NewPayItem{
				PaymentName:       payName,
				PayAmount:         0,
				Count:             0,
				NegativeCount:     0,
				MerchantAllowance: 0,
				TransCode:         payment.TransCode,
			}
			if payment.TransCode == cash {
				payItem.Sort = 1
			}
		}
		isHexWaimai := ticket.TakeawayInfo.Source == "river"
		if len(ticket.Payments) == 1 && isHexWaimai {
			payTmp = CalTicketAmount(ticket)
		} else {
			payTmp = getPaymentRealAmount(payment)
		}
		payItem.PayAmount = utils.Add(2, payItem.PayAmount, payTmp, payment.SurChargeAmount)
		payItem.SurChargeAmount = utils.Add(2, payItem.SurChargeAmount, payment.SurChargeAmount)
		payTmp = utils.Add(2, payTmp, payment.SurChargeAmount)
		if ticket.Status == TicketStatusRefund {
			payItem.Count -= 1
			payAllCount -= 1
		} else if ticket.Status == TicketStatusSale {
			payItem.Count += 1
			payAllCount += 1
		}
		// 统计现金金额
		if payment.RefundPathCode == cash {
			cashAmount = utils.Add(2, cashAmount, payment.RealAmount)
		} else if payment.TransCode == cash {
			cashAmount = utils.Add(2, cashAmount, payment.RealAmount)
		}
		paymentsMap[payName] = payItem
	}
	businessAmountTmp = utils.Add(2, businessAmountTmp, CalTicketAmount(ticket))
	businessAmountTmp = utils.Sub(2, businessAmountTmp, nonOperatingPayment)

	if payTmp != businessAmountTmp {
		logrus.Infof("出现不平 payTmp:%v 净营业额 businessAmountTmp:%v ticketId:%v", payTmp, businessAmountTmp, ticket.TicketId)
	}
	for _, promotion := range ticket.Promotions {
		var isNonPromotion bool
		for _, t := range promotion.PromotionInfo.TransactionTypes {
			if t == nonSALES {
				isNonPromotion = true
			}
		}
		if isNonPromotion {
			continue
		}
		var (
			promoItem NewPromotionChannel
			ok        bool
			item      NewItem
		)
		if len(promotion.PromotionInfo.PromotionCode) == 0 {
			promotion.PromotionInfo.Name = other
		}
		promoItem, ok = promotionsMap[promotion.PromotionInfo.PromotionCode]
		if !ok {
			promoItem = NewPromotionChannel{
				Type:   promotion.PromotionInfo.Type,
				Name:   promotion.PromotionInfo.Name,
				Source: ticket.Channel.Source,
				Code:   promotion.PromotionInfo.PromotionCode,
				//IsProduct: promotion.PromotionInfo.Type == "2", // type 等于2 为商品卷
			}
		}
		if promoItem.IsProduct {
			item = promoItem.PromotionProduct
		} else {
			item = promoItem.PromotionCoupons
		}

		if ticket.Status == TicketStatusRefund {
			item.Count--

		} else if ticket.Status == TicketStatusSale {
			item.Count++
		}
		item.Discount = utils.Add(2, item.Discount, utils.Sub(2, promotion.Source.Discount, promotion.Source.RealAmount))
		item.RealAmount = utils.Add(2, item.RealAmount, cast.ToFloat64(promotion.Source.RealAmount))
		item.Sum = utils.Add(2, item.Sum, cast.ToFloat64(promotion.Source.Discount))
		if promoItem.IsProduct {
			promoItem.PromotionProduct = item
		} else {
			promoItem.PromotionCoupons = item
		}
		promotionsMap[promoItem.Code] = promoItem
	}

	return paymentsMap, promotionsMap, payAllCount, cashAmount
}

// 根据分类id 查询分类名称
func getMenuCategoryName(menus []*Category, categoryId string) string {
	for _, menu := range menus {
		if menu.ID == categoryId {
			return menu.Name
		}
	}
	return ""
}

// CalPeriod 餐段统计
func CalPeriod(ctx context.Context, isNonSale bool, periodReportsMap map[string]*PeriodReport, menus []*Category, productCategoryMap map[int64]int64, ticket *model.Ticket) (map[string]*PeriodReport, error) {
	tmp, ok := periodReportsMap[ticket.MealSegmentName]
	if !ok {
		tmp = &PeriodReport{
			MealName:      ticket.MealSegmentName,
			CategoriesMap: make(map[string]ProductCategory),
		}
	}

	if ticket.Status == TicketStatusRefund {
		if !isNonSale {
			tmp.Count += -1
			tmp.PeopleNumber += ticket.Table.People
			tmp.BowlNum += ticket.BowlNum
			if ticket.Channel.OrderType == DINEIN {
				tmp.DineInCount += -1
				tmp.DineInNumber += ticket.Table.People
				tmp.DineInAmount = utils.Add(2, tmp.DineInAmount, CalTicketAmount(ticket))
			}
		}
	} else if ticket.Status == TicketStatusSale {
		if !isNonSale {
			tmp.Count += 1
			tmp.BowlNum += ticket.BowlNum
			tmp.PeopleNumber += ticket.Table.People
			if ticket.Channel.OrderType == DINEIN {
				tmp.DineInCount += 1
				tmp.DineInNumber += ticket.Table.People
				tmp.DineInAmount = utils.Add(2, tmp.DineInAmount, CalTicketAmount(ticket))
			}
		}
	}
	var (
		nonOperatingAmount float64
	)
	for _, promotion := range ticket.Promotions {
		for _, t := range promotion.PromotionInfo.TransactionTypes {
			if t == nonSALES {
				tmp.NonOperatingAmount = utils.Add(2, tmp.NonOperatingAmount, promotion.Source.Discount)
				nonOperatingAmount = utils.Add(2, nonOperatingAmount, promotion.Source.Discount)
			}
		}
	}
	for _, payment := range ticket.Payments {
		for _, t := range payment.TransactionTypes {
			if t == nonSALES {
				tmp.NonOperatingAmount = utils.Add(2, tmp.NonOperatingAmount, payment.PayAmount)
				nonOperatingAmount = utils.Add(2, nonOperatingAmount, payment.PayAmount)
			}
		}
	}

	tmp.Amount = utils.Add(2, tmp.Amount, CalTicketAmount(ticket))
	tmp.SurChargeAmount = utils.Add(2, tmp.SurChargeAmount, ticket.Amounts.SurChargeAmount)
	tmp.DiscountAmount = utils.Add(2, tmp.DiscountAmount, ticket.Amounts.DiscountAmount)
	tmp.PackageFee = utils.Add(2, tmp.PackageFee, ticket.TakeawayInfo.PackageFee)
	tmp.DeliveryFee = utils.Add(2, tmp.DeliveryFee, ticket.Amounts.DeliveryFee)
	tmp.ServiceFee = utils.Add(2, tmp.ServiceFee, ticket.Amounts.ServiceFee)
	tmp.Rounding = utils.Add(2, tmp.Rounding, ticket.Amounts.Rounding)
	tmp.OverflowAmount = utils.Add(2, tmp.OverflowAmount, ticket.Amounts.OverflowAmount)
	tmp.Tip = utils.Add(2, tmp.Tip, ticket.Amounts.Tip)
	if !ticket.Amounts.TaxIncluded {
		tmp.TaxAmount = utils.Add(2, tmp.TaxAmount, ticket.Amounts.TaxAmount)
	}
	tmp.RealTaxAmount = utils.Add(2, tmp.RealTaxAmount, ticket.Amounts.TaxAmount)

	// 统计餐段商品
	tmp, err := CalPeriodProduct(ctx, menus, productCategoryMap, tmp, ticket.Products)
	if err != nil {
		logrus.Errorf("CalPeriodProduct err:%v", err)
		return nil, err
	}
	periodReportsMap[ticket.MealSegmentName] = tmp
	return periodReportsMap, nil
}

// 统计餐段商品
func CalPeriodProduct(ctx context.Context, menus []*Category, productCategoryMap map[int64]int64, tmp *PeriodReport, products []*model.TicketProduct) (*PeriodReport, error) {
	for _, product := range products {
		// 加料处理
		tmp, err := CalPeriodProduct(ctx, menus, productCategoryMap, tmp, product.Accessories)
		if err != nil {
			logrus.Errorf("CalPeriodProduct Accessories err:%v", err)
			return nil, err
		}
		// 套餐加料处理
		for _, set := range product.ComboItems {
			tmp, err = CalPeriodProduct(ctx, menus, productCategoryMap, tmp, set.Accessories)
			if err != nil {
				logrus.Errorf("CalPeriodProduct Accessories err:%v", err)
				return nil, err
			}
		}
		//查询商品的分类id
		key := product.Id
		var pCate string
		cat, ok := productCategoryMap[cast.ToInt64(key)]
		if ok {
			pCate = cast.ToString(cat)
		}

		//logrus.Infof("productsid:%v pcate:%v", key, pCate)
		//根据分类id查询分类名称
		cateName := getMenuCategoryName(menus, pCate)
		if cateName == "" {
			cateName = unKnownCategory
		}
		categoryMap, ok := tmp.CategoriesMap[cateName]
		if !ok {
			categoryMap = ProductCategory{
				CategoryName: cateName,
			}
		}
		categoryMap.DiscountAmount = utils.Add(2, categoryMap.DiscountAmount, product.DiscountAmount)
		categoryMap.Amount = utils.Add(2, categoryMap.Amount, product.Amount)
		tmp.CategoriesMap[cateName] = categoryMap
	}
	return tmp, nil
}

// CalPeriodReport 将餐段商品合并成一个map 总统计
func CalPeriodReport(periodReportsMap map[string]*PeriodReport) PeriodReport {
	allPeriodReport := PeriodReport{
		CategoriesMap: make(map[string]ProductCategory),
	}
	for _, periodReport := range periodReportsMap {
		allPeriodReport.Count += periodReport.Count
		allPeriodReport.DineInCount += periodReport.DineInCount
		allPeriodReport.BowlNum += periodReport.BowlNum
		allPeriodReport.PeopleNumber += periodReport.PeopleNumber
		allPeriodReport.DineInNumber += periodReport.DineInNumber
		allPeriodReport.Amount = utils.Add(2, allPeriodReport.Amount, periodReport.Amount)
		allPeriodReport.SurChargeAmount = utils.Add(2, allPeriodReport.SurChargeAmount, periodReport.SurChargeAmount)
		allPeriodReport.DineInAmount = utils.Add(2, allPeriodReport.DineInAmount, periodReport.DineInAmount)
		allPeriodReport.DiscountAmount = utils.Add(2, allPeriodReport.DiscountAmount, periodReport.DiscountAmount)
		allPeriodReport.PackageFee = utils.Add(2, allPeriodReport.PackageFee, periodReport.PackageFee)
		allPeriodReport.DeliveryFee = utils.Add(2, allPeriodReport.DeliveryFee, periodReport.DeliveryFee)
		allPeriodReport.ServiceFee = utils.Add(2, allPeriodReport.ServiceFee, periodReport.ServiceFee)
		allPeriodReport.OverflowAmount = utils.Add(2, allPeriodReport.OverflowAmount, periodReport.OverflowAmount)
		allPeriodReport.Rounding = utils.Add(2, allPeriodReport.Rounding, periodReport.Rounding)
		allPeriodReport.Tip = utils.Add(2, allPeriodReport.Tip, periodReport.Tip)
		allPeriodReport.TaxAmount = utils.Add(2, allPeriodReport.TaxAmount, periodReport.TaxAmount)
		allPeriodReport.RealTaxAmount = utils.Add(2, allPeriodReport.RealTaxAmount, periodReport.RealTaxAmount)
		allPeriodReport.NonOperatingAmount = utils.Add(2, allPeriodReport.NonOperatingAmount, periodReport.NonOperatingAmount)
		// 统计餐段商品
		for k, v := range periodReport.CategoriesMap {
			categoryMap, ok := allPeriodReport.CategoriesMap[k]
			if !ok {
				categoryMap = ProductCategory{
					CategoryName: k,
				}
			}
			categoryMap.DiscountAmount = utils.Add(2, categoryMap.DiscountAmount, v.DiscountAmount)
			categoryMap.Amount = utils.Add(2, categoryMap.Amount, v.Amount)
			allPeriodReport.CategoriesMap[k] = categoryMap
		}
	}
	allPeriodReport.Categories = productCategoryToArray(allPeriodReport.CategoriesMap)
	allPeriodReport.CategoriesMap = nil
	return allPeriodReport
}

// 将map[string]PayItem 转为数组
func payItemToArray(paymentsMap map[string]NewPayItem) ([]NewPayItem, float64) {
	var (
		payments           []NewPayItem
		totalPayItemAmount float64
	)
	for _, v := range paymentsMap {
		payments = append(payments, v)
		totalPayItemAmount = utils.Add(2, totalPayItemAmount, v.PayAmount)
	}
	sort.Slice(payments, func(i, j int) bool {
		return payments[i].Sort < payments[j].Sort
	})
	return payments, totalPayItemAmount
}

// 促销map转为数组
func promotionChannelToArray(promotionsMap map[string]NewPromotionChannel) []NewPromotionChannel {
	var promotions []NewPromotionChannel
	for _, v := range promotionsMap {
		if v.PromotionCoupons.Sum == 0 {
			continue
		}
		promotions = append(promotions, v)
	}
	sort.Slice(promotions, func(i, j int) bool {
		return promotions[i].PromotionCoupons.Sum > promotions[j].PromotionCoupons.Sum
	})
	return promotions
}

// 商品分类map转数组
func productCategoryToArray(categoriesMap map[string]ProductCategory) []ProductCategory {
	var categories []ProductCategory
	for _, v := range categoriesMap {
		categories = append(categories, v)
	}
	sort.Slice(categories, func(i, j int) bool {
		return categories[i].Amount > categories[j].Amount
	})
	return categories
}

// 餐段map转数组
func periodReportToArray(ctx context.Context, periodReportsMap map[string]*PeriodReport) ([]PeriodReport, error) {
	mealPeriod, err := getMealPeriod(ctx)
	if err != nil {
		return nil, err
	}
	var periodReports []PeriodReport
	for _, v := range periodReportsMap {
		tmp := *v
		for index, period := range mealPeriod.Data {
			if period.Name == tmp.MealName {
				tmp.Period = period.Period
				tmp.Sort = index
			}
		}
		tmp.AverageBill = utils.Divide(2, tmp.Amount, float64(tmp.Count))
		tmp.PeopleAverage = utils.Divide(2, tmp.Amount, float64(tmp.PeopleNumber))
		tmp.AverageDineIn = utils.Divide(2, tmp.DineInAmount, float64(tmp.DineInNumber))
		tmp.AverageBowlNum = utils.Divide(2, tmp.Amount, float64(tmp.BowlNum))
		tmp.Categories = productCategoryToArray(tmp.CategoriesMap)
		tmp.CategoriesMap = nil
		periodReports = append(periodReports, tmp)
	}

	sort.Slice(periodReports, func(i, j int) bool {
		return periodReports[i].Sort < periodReports[j].Sort
	})
	tmp, _ := json.Marshal(&periodReports)
	logrus.Infof("periodReports:%v", string(tmp))
	return periodReports, nil
}

// 现金管理信息方法
func CalCashManage(ctx context.Context, req *sales_report.DailyRequest) (*CashManageResp, error) {
	url := req.Host + "/api/hex-order/cash-account/report"
	rsp := ApiCashReportRes{}
	storeId := pos.GetStoreId(ctx)
	request := map[string]interface{}{
		"storeID": storeId,
		"busDate": req.BusDate,
	}
	err := OmnichannelNewRequest(ctx, url, "POST", &request, &rsp)
	if err != nil {
		logrus.Errorf("CalCashManage err:%v", err)
		return nil, err
	}
	cashManageResp := CashManageResp{}
	cashManageResp.StoreCashManage = StoreCashManage{
		PettyCash:          utils.Divide(2, cast.ToFloat64(rsp.InitialFloat), 100),
		BalanceCash:        utils.Divide(2, cast.ToFloat64(rsp.TodayBalance), 100),
		TurnoverCash:       utils.Divide(2, cast.ToFloat64(rsp.SalesRevenue), 100),
		CollectedPettyCash: utils.Divide(2, cast.ToFloat64(rsp.CollectedPettyCash), 100),
		DifferenceCash:     utils.Divide(2, cast.ToFloat64(rsp.CashDifference), 100),
	}
	cashManageResp.BankCash = BankCash{
		BalanceCash:                  utils.Divide(2, cast.ToFloat64(rsp.TodayBalance), 100),
		CollectPettyCash:             utils.Divide(2, cast.ToFloat64(rsp.CollectedPettyCash), 100),
		YesterdayPendingEscortAmount: utils.Divide(2, cast.ToFloat64(rsp.PreviousPendingDeposit), 100),
		PaidEscortAmount:             utils.Divide(2, cast.ToFloat64(rsp.Deposited), 100),
		PendingEscortAmount:          utils.Divide(2, cast.ToFloat64(rsp.FinalPendingDeposit), 100),
	}
	cashManageResp.SummaryOfPettyCash = SummaryOfPettyCash{
		OpeningStoreCash:     utils.Divide(2, cast.ToFloat64(rsp.StartupPettyCash), 100),
		PettyCashBalance:     utils.Divide(2, cast.ToFloat64(rsp.InitialPettyCash), 100),
		CashReceived:         utils.Divide(2, cast.ToFloat64(rsp.CollectedPettyCash), 100),
		OtherPettyCashIncome: utils.Divide(2, cast.ToFloat64(rsp.OtherPettyCashIncome), 100),
		Expenditure:          utils.AbsFloat64(utils.Divide(2, cast.ToFloat64(rsp.PettyCashExpenses), 100)),
		CashBalance:          utils.Divide(2, cast.ToFloat64(rsp.TodayPettyCashBalance), 100),
	}

	return &cashManageResp, nil
}

// 获取取消订单统计
func CalCancelOrder(ctx context.Context, req *sales_report.DailyRequest) (*OrderCancellation, error) {
	url := req.Host + "/api/hex-order/pos/canceled-order-report"
	var payload OrderCancellation
	storeId := pos.GetStoreId(ctx)
	request := map[string]interface{}{
		"storeID": storeId,
		"busDate": req.BusDate,
	}
	err := OmnichannelNewRequest(ctx, url, "POST", &request, &payload)
	if err != nil {
		logrus.Errorf("CalCashManage err:%v", err)
		return nil, err
	}
	return &payload, nil
}

// 获取合并桌位
func CalMergeTable(ctx context.Context, req *sales_report.DailyRequest) (*Statistics, error) {
	url := req.Host + "/api/hex-order/pos/combine-table-order-report"
	var payload Statistics
	storeId := pos.GetStoreId(ctx)
	request := map[string]interface{}{
		"storeID": storeId,
		"busDate": req.BusDate,
	}
	err := OmnichannelNewRequest(ctx, url, "POST", &request, &payload)
	if err != nil {
		logrus.Errorf("CalMergeTable err:%v", err)
		return nil, err
	}
	return &payload, nil
}

func isNonSalePayment(ticket *model.Ticket) bool {
	for _, payment := range ticket.Payments {
		for _, t := range payment.TransactionTypes {
			if t == nonSALES {
				return true
			}
		}
	}
	return false
}

func isNonSalePromotion(ticket *model.Ticket) bool {
	for _, promotion := range ticket.Promotions {
		for _, v := range promotion.PromotionInfo.TransactionTypes {
			if v == nonSALES {
				return true
			}
		}
	}
	return false
}

// 判断支付方式是否包含员工福食项
func getEmployeeFood(nonSalesPaymentMap map[string]NonSalesCoupon, ticket *model.Ticket) (map[string]NonSalesCoupon, float64) {
	var nonOperatingPayment float64
	for _, payment := range ticket.Payments {
		for _, t := range payment.TransactionTypes {
			if t == nonSALES {
				var payName string
				if len(payment.RefundPathName) != 0 {
					payName = payment.RefundPathName
				} else {
					if len(payment.Name) == 0 {
						payName = payment.TransName
					} else {
						payName = payment.Name
					}
				}
				tmp, ok := nonSalesPaymentMap[payName]
				if !ok {
					tmp = NonSalesCoupon{
						Name: payName,
					}
				}
				tmp.Discount = utils.Add(2, tmp.Discount, payment.PayAmount)
				nonOperatingPayment = utils.Add(2, nonOperatingPayment, payment.PayAmount)
				if ticket.Status == TicketStatusSale {
					tmp.Count += 1
				} else if ticket.Status == TicketStatusRefund {
					tmp.Count -= 1
				}
				nonSalesPaymentMap[payName] = tmp
			}
		}
	}
	return nonSalesPaymentMap, nonOperatingPayment
}

// 判断卡券和促销 例如同心集
func NonSalePromotion(nonSalesCouponMap map[string]NonSalesCoupon, ticket *model.Ticket) (map[string]NonSalesCoupon, float64) {
	var nonOperatingCoupon float64
	for _, promotion := range ticket.Promotions {
		for _, v := range promotion.PromotionInfo.TransactionTypes {
			if v == nonSALES {
				tmp, ok := nonSalesCouponMap[promotion.PromotionInfo.Name]
				if !ok {
					tmp = NonSalesCoupon{
						Name: promotion.PromotionInfo.Name,
					}
				}
				tmp.Discount = utils.Add(2, tmp.Discount, promotion.Source.Discount)
				nonOperatingCoupon = utils.Add(2, nonOperatingCoupon, promotion.Source.Discount)
				if ticket.Status == TicketStatusSale {
					tmp.Count += 1
				} else if ticket.Status == TicketStatusRefund {
					tmp.Count -= 1
				}
			}
		}
	}
	return nonSalesCouponMap, nonOperatingCoupon
}

// 非销售卡劵map转为数组
func nonSalesCouponToArray(nonSalesCouponMap map[string]NonSalesCoupon) []NonSalesCoupon {
	var nonSalesCoupons []NonSalesCoupon
	for _, v := range nonSalesCouponMap {
		nonSalesCoupons = append(nonSalesCoupons, v)
	}
	sort.Slice(nonSalesCoupons, func(i, j int) bool {
		return nonSalesCoupons[i].Discount > nonSalesCoupons[j].Discount
	})
	return nonSalesCoupons
}

// 获取支付方式的商家实收
func getPaymentRealAmount(payment model.TicketPayment) float64 {
	var amount float64
	// 用户实际购买金额+第三方补贴金额+平台补贴金额
	amount = utils.Add(2, amount, payment.Cost)
	amount = utils.Add(2, amount, payment.TpAllowance)
	amount = utils.Add(2, amount, payment.PlatformAllowance)
	return amount
}

func isNonSale(nonOperatingPayment, nonOperatingCoupon float64) bool {
	if nonOperatingPayment != 0 {
		return true
	}
	if nonOperatingCoupon != 0 {
		return true
	}
	return false
}

func NewCalTicketList(ctx context.Context, req *sales_report.DailyRequest, tickets []*model.Ticket) (*DailyInfo, error) {
	logrus.Infof("参与日结计算的ticket数据数量:%d", len(tickets))
	token := pos.GetToken(ctx)
	logrus.Infof("token:%s", token)
	//md, ok := metadata.FromIncomingContext(ctx)
	//logrus.Infof("md :%v ok:%v", md, ok)
	//支付统计信息，需要之后转换为数组
	paymentsMap := make(map[string]NewPayItem)
	//促销统计信息， 需要之后转换为数组
	promotionsMap := make(map[string]NewPromotionChannel)
	// 餐段统计信息，需要之后转换为数组
	periodReportsMap := make(map[string]*PeriodReport)
	nonSalesCouponMap := make(map[string]NonSalesCoupon)
	nonSalesPaymentMap := make(map[string]NonSalesCoupon)
	allPeriodReport := PeriodReport{}
	categories, err := getProductCategories(ctx)
	if err != nil {
		logrus.Errorf("NewCalTicketList:getMenu err:%v", err)
		return nil, err
	}
	//查询该租户下的商品分类数据
	productCategoryMap, err := queryProductsCategoryMap(ctx)
	if err != nil {
		logrus.Errorf("NewCalTicketList:queryProductsCategoryMap err:%v", err)
		return nil, err
	}
	dailyInfo := DailyInfo{}

	var (
		// 堂食人数统计
		dineInPeople int
		// 堂食金额统计
		dineInAmount float64
		//堂食单数
		dineInCount int
		//营业额
		businessAmount float64
		//支付手续费
		surChargeAmount float64
		//总单数
		allCount int
		//总折扣金额
		discountAmount float64
		//实收金额
		realAmount float64
		// 支付单数
		payAllCount int
		//现金收款
		cashAmount float64
		// 总人数
		allPeople int
		// 退款数量
		refundCount int
		// 退款金额
		refundAmount float64
		// 促销总数量
		promotionCount int
		// 促销总金额
		promotionAmount float64
		// non_sales总额
		nonSalesTotal float64
	)

	for _, ticket := range tickets {
		//非营业收入
		var (
			nonOperatingPayment float64
			nonOperatingCoupon  float64
		)
		//员工福食统计 即非销售支付方式
		nonSalesPaymentMap, nonOperatingPayment = getEmployeeFood(nonSalesPaymentMap, ticket)

		// 非销售卡劵统计 例如同心集系列
		nonSalesCouponMap, nonOperatingCoupon = NonSalePromotion(nonSalesCouponMap, ticket)

		paymentsMap, promotionsMap, payAllCount, cashAmount = CalPaymentAndPromotion(payAllCount, cashAmount, paymentsMap, promotionsMap, ticket, nonOperatingPayment)
		surChargeAmount = utils.Add(2, surChargeAmount, ticket.Amounts.SurChargeAmount)

		periodReportsMap, err = CalPeriod(ctx, isNonSale(nonOperatingPayment, nonOperatingCoupon), periodReportsMap, categories, productCategoryMap, ticket)
		if err != nil {
			logrus.Errorf("NewCalTicketList:CalPeriod err:%v", err)
			return nil, err
		}
		// 统计堂食人数 堂食净额 堂食单数
		if ticket.Channel.OrderType == DINEIN {
			if !isNonSale(nonOperatingPayment, nonOperatingCoupon) {
				if ticket.Status == TicketStatusRefund {
					dineInCount -= 1
					dineInPeople += ticket.Table.People
				} else if ticket.Status == TicketStatusSale {
					dineInCount += 1
					dineInPeople += ticket.Table.People
				}
			}
			dineInAmount = utils.Add(2, dineInAmount, CalTicketAmount(ticket))
			//减去 非销售收入
			dineInAmount = utils.Sub(2, dineInAmount, nonOperatingPayment)
		}

		// 统计退款
		if ticket.Status != TicketStatusSale {
			if !isNonSale(nonOperatingPayment, nonOperatingCoupon) {
				if ticket.Status == TicketStatusRefund {
					refundCount++
				}
				refundAmount = utils.Add(2, refundAmount, CalTicketAmount(ticket))
				refundAmount = utils.Add(2, refundAmount, ticket.Amounts.SurChargeAmount)
			}

		}
		// 总人数统计
		if !isNonSale(nonOperatingPayment, nonOperatingCoupon) {
			if ticket.Status == TicketStatusSale {
				allPeople += ticket.Table.People
				allCount += 1
			} else if ticket.Status == TicketStatusRefund {
				allCount -= 1
				allPeople += ticket.Table.People
			}
		}

		// 营业额统计
		businessAmount += ticket.Amounts.BusinessAmount + ticket.Amounts.SurChargeAmount
		// 折扣金额统计
		discountAmount += ticket.Amounts.DiscountMerchantContribute + ticket.Amounts.Commission + ticket.Amounts.OtherFee + ticket.TakeawayInfo.MerchantSendFee + ticket.Amounts.PayMerchantContribute + ticket.Amounts.Rounding - ticket.Amounts.OverflowAmount
		nonSalesTotal += nonOperatingPayment
	}

	businessAmount = utils.FloatFmt(businessAmount)
	realAmount = utils.FloatFmt(businessAmount - discountAmount)
	discountAmount = utils.FloatFmt(discountAmount - nonSalesTotal)

	for _, promotion := range promotionsMap {
		promotionCount += promotion.PromotionCoupons.Count
		promotionAmount = utils.Add(2, promotionAmount, promotion.PromotionCoupons.Sum)
	}
	allPeriodReport = CalPeriodReport(periodReportsMap)
	allPeriodReport.AverageBill = utils.Divide(2, allPeriodReport.Amount, float64(allPeriodReport.Count))
	allPeriodReport.PeopleAverage = utils.Divide(2, allPeriodReport.Amount, float64(allPeriodReport.PeopleNumber))
	allPeriodReport.AverageDineIn = utils.Divide(2, allPeriodReport.DineInAmount, float64(allPeriodReport.DineInNumber))
	allPeriodReport.AverageBowlNum = utils.Divide(2, allPeriodReport.Amount, float64(allPeriodReport.BowlNum))

	nonSalesCoupons := nonSalesCouponToArray(nonSalesCouponMap)
	nonSalesPayments := nonSalesCouponToArray(nonSalesPaymentMap)
	nonSalesCoupons = append(nonSalesCoupons, nonSalesPayments...)
	//报表数据填充
	dailyInfo.TotalStatistics = TotalStatistics{
		NonSalesCoupons:  nonSalesCoupons,
		NonSalesPayments: nonSalesPayments,
		BusinessAmount:   businessAmount,
		DiscountAmount:   discountAmount,
		Amount:           realAmount,
		SurChargeAmount:  surChargeAmount,
	}
	dailyInfo.Pays, dailyInfo.TotalPayItemAmount = payItemToArray(paymentsMap)

	dailyInfo.PayStatistics = Statistics{
		Amount:         realAmount,
		Count:          payAllCount,
		DiscountAmount: discountAmount,
	}
	dailyInfo.PayItemDifference = utils.Sub(2, realAmount, dailyInfo.TotalPayItemAmount)
	rsp, err := CalCashManage(ctx, req)
	if err != nil {
		logrus.Errorf("CalCashManage err:%v", err)
		return nil, err
	}
	dailyInfo.StoreCashManage = rsp.StoreCashManage
	//dailyInfo.StoreCashManage.TurnoverCash = cashAmount
	dailyInfo.BankCash = rsp.BankCash
	dailyInfo.SummaryOfPettyCash = rsp.SummaryOfPettyCash

	dailyInfo.PromotionsChannelMap = promotionChannelToArray(promotionsMap)
	dailyInfo.DiscountDealsStatistics = Statistics{
		Count:  promotionCount,
		Amount: promotionAmount,
	}
	combineTableOrder, err := CalMergeTable(ctx, req)
	if err != nil {
		logrus.Errorf("CalMergeTable err:%v", err)
		return nil, err
	}
	// 核数
	dailyInfo.Audit = Audit{
		Refund: Statistics{
			Count:  refundCount,
			Amount: refundAmount,
		},
		CombineTableOrder: *combineTableOrder,
		AllTicketStatistics: Statistics{
			Count:  allCount,
			Amount: realAmount,
		},
		AverageBill: utils.Divide(2, realAmount, float64(allCount)),
		PeopleAverage: Statistics{
			Count:  allPeople,
			Amount: utils.Divide(2, realAmount, float64(allPeople)),
		},
		AverageDineIn: Statistics{
			Count:  dineInPeople,
			Amount: utils.Divide(2, dineInAmount, float64(dineInPeople)),
		},
	}
	cancelOrder, err := CalCancelOrder(ctx, req)
	if err != nil {
		logrus.Errorf("CalCancelOrder err:%v", err)
		return nil, err
	}
	dailyInfo.Audit.OrderCancellation = *cancelOrder

	periodReports, err := periodReportToArray(ctx, periodReportsMap)
	if err != nil {
		return nil, err
	}
	dailyInfo.PeriodReports = periodReports
	dailyInfo.AllProduct = allPeriodReport
	return &dailyInfo, nil
}
