package pos

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

type SalesProduct struct {
	Id    string       `json:"id"`
	Name  string       `json:"name"`
	Sum   OrderTypeSum `json:"sum"`
	Combo bool         `json:"combo"`
	Count int          `json:"count"`
}

type OrderTypeSum struct {
	DineInCount   int `json:"dineInCount"`   // 堂食
	TakeawayCount int `json:"takeawayCount"` // 外带
	TakeoutCount  int `json:"takeoutCount"`  // 外卖
	SelfHelpCount int `json:"selfHelpCount"` // 自提
}

func (o OrderTypeSum) Add(sum OrderTypeSum) OrderTypeSum {
	o.DineInCount += sum.DineInCount
	o.TakeawayCount += sum.TakeawayCount
	o.TakeoutCount += sum.TakeoutCount
	o.SelfHelpCount += sum.SelfHelpCount
	return o
}

type ProductRsp struct {
	Name  string         `json:"name"`
	List  []SalesProduct `json:"list"`
	Sum   OrderTypeSum   `json:"sum"`
	Count int            `json:"count"`
}

type PosCategoryReportRsp struct {
	Products      []ProductRsp `json:"products"`
	Sum           OrderTypeSum `json:"sum"`
	SumCount      int          `json:"sumCount"`
	ComboProducts []ProductRsp `json:"comboProducts"`
	ComboSum      OrderTypeSum `json:"comboSum"`
	ComBoSumCount int          `json:"comBoSumCount"`
}

// 获取销售类型的商品 排除非销售促销
func getSalesProduct(ticket *model.Ticket) []*model.TicketProduct {
	products := make([]*model.TicketProduct, 0)
	var promotionProductIds []string
	for _, promotion := range ticket.Promotions {
		for _, v := range promotion.PromotionInfo.TransactionTypes {
			if v == nonSALES {
				for _, product := range promotion.Products {
					promotionProductIds = append(promotionProductIds, product.KeyId)
				}
			}
		}
	}
	for index, product := range ticket.Products {
		if !utils.IN(product.Id, promotionProductIds) {
			products = append(products, ticket.Products[index])
		}
	}

	return products
}

func setOrderTypeSum(orderTypeSum OrderTypeSum, qty int, orderType string) OrderTypeSum {
	switch orderType {
	case DINEIN:
		orderTypeSum.DineInCount += qty
	case TAKEAWAY:
		orderTypeSum.TakeawayCount += qty
	case TAKEOUT:
		orderTypeSum.TakeoutCount += qty
	case SELFHELP:
		orderTypeSum.SelfHelpCount += qty
	}
	return orderTypeSum
}

func setCategoryProductMap(cateName, orderType string, categoryProductMap map[string]map[string]SalesProduct, product *model.TicketProduct, qty int) map[string]map[string]SalesProduct {
	tmp, ok := categoryProductMap[cateName]
	if !ok {
		tmp = make(map[string]SalesProduct)
		salesProduct := SalesProduct{
			Id:    product.Id,
			Name:  product.Name,
			Combo: len(product.ComboItems) > 0,
			Count: qty,
		}
		salesProduct.Sum = setOrderTypeSum(salesProduct.Sum, qty, orderType)
		tmp[product.Id] = salesProduct
		categoryProductMap[cateName] = tmp
	} else {
		salesProduct, ok := tmp[product.Id]
		if !ok {
			salesProduct := SalesProduct{
				Id:    product.Id,
				Name:  product.Name,
				Combo: len(product.ComboItems) > 0,
				Count: qty,
			}
			salesProduct.Sum = setOrderTypeSum(salesProduct.Sum, qty, orderType)
			tmp[product.Id] = salesProduct
		} else {
			salesProduct.Sum = setOrderTypeSum(salesProduct.Sum, qty, orderType)
			salesProduct.Count += qty
			tmp[product.Id] = salesProduct
		}
		categoryProductMap[cateName] = tmp
	}
	return categoryProductMap
}

func getCategoryProductMap(ctx context.Context, categoryProductMap map[string]map[string]SalesProduct, orderType string, status model.TicketStatus, menus []*Category, productCategoryMap map[int64]int64, product *model.TicketProduct) (map[string]map[string]SalesProduct, error) {
	//查询商品的分类id
	key := product.Id
	var (
		pCate string
		err   error
	)

	cat, ok := productCategoryMap[cast.ToInt64(key)]
	if ok {
		pCate = cast.ToString(cat)
	}
	//根据分类id查询分类名称
	cateName := getMenuCategoryName(menus, pCate)
	if cateName == "" {
		cateName = unKnownCategory
	}
	var qty int
	qty = product.Qty
	logrus.Infof("cateName：%v productName：%v qty：%v productId:%v pcate:%v", cateName, product.Name, qty, key, pCate)
	//if len(product.ComboItems) > 0 {
	//	categoryProductMap = setCategoryProductMap(cateName, orderType, categoryProductMap, product, qty)
	//}
	for _, item := range product.ComboItems {
		categoryProductMap, err = getCategoryProductMap(ctx, categoryProductMap, orderType, status, menus, productCategoryMap, item)
		if err != nil {
			return nil, err
		}
	}
	for _, item := range product.Accessories {
		categoryProductMap, err = getCategoryProductMap(ctx, categoryProductMap, orderType, status, menus, productCategoryMap, item)
		if err != nil {
			return nil, err
		}
	}
	categoryProductMap = setCategoryProductMap(cateName, orderType, categoryProductMap, product, qty)

	return categoryProductMap, nil
}

func categoryReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.CategoryReportResponse, error) {
	categories, err := getProductCategories(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "categoryReport:loadProductCategories")
	}

	//查询该租户下的商品分类数据
	productCategoryMap, err := queryProductsCategoryMap(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "categoryReport:queryProductsCategoryMap")
	}

	tickets, err := queryTickets(ctx, req)
	if err != nil {
		return nil, err
	}
	logrus.Infoln("参与计算的订单数量:", len(tickets))

	categoryProductMap := make(map[string]map[string]SalesProduct)
	for _, ticket := range tickets {
		// 非交易类型支付方式过滤
		var payNonSales bool
		for _, payment := range ticket.Payments {
			for _, t := range payment.TransactionTypes {
				if t == nonSALES {
					payNonSales = true
				}
			}
		}
		if payNonSales {
			continue
		}

		// 获取销售类型的商品
		products := getSalesProduct(ticket)
		// 套餐处理
		logrus.Println("ticketId:", ticket.TicketId)
		for _, product := range products {
			categoryProductMap, err = getCategoryProductMap(ctx, categoryProductMap, ticket.Channel.OrderType, ticket.Status, categories, productCategoryMap, product)
			if err != nil {
				return nil, err
			}
		}
	}
	tmp, _ := json.Marshal(&categoryProductMap)
	logrus.Infof("categoryReport categoryProductMap:%v", string(tmp))
	rsp := PosCategoryReportRsp{}
	// 将categoryProductMap 转成数据格式 用于前端显示
	for k, categoryProduct := range categoryProductMap {
		products := ProductRsp{
			Name: k,
		}
		comboProducts := ProductRsp{
			Name: k,
		}
		var hasCombo bool
		for _, product := range categoryProduct {
			if product.Combo {
				comboProducts.List = append(comboProducts.List, product)
				comboProducts.Sum = comboProducts.Sum.Add(product.Sum)
				comboProducts.Count += product.Count
				rsp.ComBoSumCount += product.Count
				hasCombo = true
				rsp.ComboSum = rsp.ComboSum.Add(product.Sum)
			} else {
				products.List = append(products.List, product)
				products.Sum = products.Sum.Add(product.Sum)
				products.Count += product.Count
				rsp.Sum = rsp.Sum.Add(product.Sum)
				rsp.SumCount += product.Count
			}
		}
		rsp.Products = append(rsp.Products, products)
		if hasCombo {
			rsp.ComboProducts = append(rsp.ComboProducts, comboProducts)
		}
	}

	result := sales_report.CategoryReportResponse{}
	data, err := json.Marshal(&rsp)
	if err != nil {
		logrus.Errorf("categoryReport rsp Marshal err:%v", err)
		return nil, err
	}
	logrus.Infoln("分类报表数据:", string(data))
	result.Extend = data
	return &result, nil

}
