package pos

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"sort"
	"strings"
)

func getMealPeriod(ctx context.Context) (*MealPeriod, error) {
	storeId := pos.GetStoreId(ctx)
	posInfo, err := entity.GetIdsFieldMap(ctx, "store", []int64{storeId}, "")
	if err != nil {
		logrus.Errorf("getMealPeriod err:%v", err)
		return nil, err
	}
	var mealPeriod MealPeriod
	meal_period, ok := posInfo[storeId]["meal_period"]
	if ok {
		tmp, err := json.Marshal(&meal_period)
		if err != nil {
			logrus.Errorf("getMealPeriod meal_period Marshal:%v", err)
			return nil, err
		}
		err = json.Unmarshal(tmp, &mealPeriod)
		if err != nil {
			logrus.Infof("getMealPeriod meal_period %s: %v", tmp, mealPeriod)
			return nil, err
		}
		return &mealPeriod, nil
	}
	return nil, errors.New("meal period not fount")
}

// CalTicketList isDaily 是否是日结计算，否则就当做交班计算
func CalTicketList(ctx context.Context, tickets []*model.Ticket, isDaily bool) (DailyKnotInfo, ticketStatistic) {
	logrus.Infof("参与日结计算的ticket数据数量:%d", len(tickets))
	//抹零的总和
	var removeZeroSum float64
	//抹零的订单条目
	var removeZeroCount int
	//是否有订单没有上传
	var isUnUpload int
	//所有的订单的金额
	var allMoney float64
	//人数统计
	var people int
	//溢收统计
	var overflowSum float64
	//溢收条目统计
	var overflowCount int

	//促销统计信息， 需要之后转换为数组
	promotionsMap := make(map[string]PromotionItem)
	addPromotion := func(t string, w, s int) {
		promotionsMap[t] = PromotionItem{Type: t, Sort: w*len(ChannelMap) + s}
	}
	addPromotion(ChannelMap[WeChatAPPTp].PromotionsTrans.DiscountMerchantContribute, 0, 0)
	for _, v := range ChannelMap {
		if v.IsPublicChannel {
			addPromotion(v.PromotionsTrans.DiscountMerchantContribute, SortWeightDiscount, v.Sort)
			addPromotion(v.PromotionsTrans.Commission, SortWeightCommission, v.Sort)
			addPromotion(v.PromotionsTrans.MerchantSendFee, SortWeightSendFee, v.Sort)
			addPromotion(v.PromotionsTrans.OtherFee, SortWeightOtherFee, v.Sort)
		}
	}

	//支付统计信息，需要之后转换为数组
	paymentsMap := make(map[string]PayItem)

	userItemsMap := make(map[string]UserSaleItem)

	//总金额统计
	var allTicketStatistics = commonStatistical{}
	//折扣交易统计
	var discountDealsStatistics = commonStatistical{}
	//抹零统计
	var removeZeroStatistics = commonStatistical{}
	//取消交易统计
	var cancelTicketStatistics = commonStatistical{}
	//甩尾统计
	var roundingStatistics = commonStatistical{}
	//溢收统计
	var overflowStatistics = commonStatistical{}
	//交易净额
	//var netTrade float64 = 0 //all money
	//税统计信息
	taxMap := make(map[string]TaxItem)
	// 根据渠道的统计信息
	var channelMap = make(map[string]channels)
	//统计商品销售数据
	var saleProduct = make(map[string]ProductInfo)
	// 统计促销卡券详情
	var promotionsChannelMap = make(map[string]PromotionChannel)
	var mealSegmentNameChannel = make(map[string]MealSegmentNameChannel)
	//var promotionsChannel []PromotionChannel //返回使用 处理后的数据
	var amount float64
	var mealSegmentNameList []MealSegmentNameChannel

	////非原单退款
	//var cashRefunds []*CashRefund
	//var cashRefundAmount float64
	////门店报损
	//var frmLossList []*FrmLoss
	//var frmLossAmount float64
	//var frmLossCount float64

	//change := make(map[string]PayItem)
	//payAmountMap := make(map[string]PayItem)
	for i, _ := range tickets {
		ticket := formatTicket(tickets[i])
		people += ticket.People
		allMoney = utils.Add(2, allMoney, cast.ToFloat64(ticket.NetAmt))
		amount = utils.Add(2, amount, ticket.DiscountAmount)
		sumRouding(ticket, &roundingStatistics)
		userItemsMap, _ = sumUserItem(&ticket, userItemsMap, false)
		promotionsMap, _ = sumPromotions(ctx, &ticket, promotionsMap, false) //折扣额计算不包含取消交易的订单，包括原单和负单
		promotionsChannelMap = sumPromotionsChannel(ctx, &ticket, promotionsChannelMap)
		taxMap, _, _ = sumTax(&ticket, taxMap, false)
		sumCancel(ctx, ticket, &allTicketStatistics) //统计取消交易信息
		paymentsMap, overflowSum, overflowCount = sumPayment(ctx, ticket, paymentsMap, overflowSum, overflowCount)
		chName := ticket.Channel
		if ticket.TpName == AliPay {
			chName = strings.ToLower(fmt.Sprintf("%s_%s", "TAKEOUT", WeChatAPPTp))
		}
		channelMap, saleProduct = extendReport(ctx, chName, ticket, saleProduct, channelMap)
		channelMap, _ = extendReport(ctx, ticket.TpName, ticket, nil, channelMap)
		mealSegmentNameChannel = sumMealSegmentNameChannel(ctx, ticket, mealSegmentNameChannel)
	}

	mealPeriod, err := getMealPeriod(ctx)
	if err != nil {
		logrus.Errorf("CalTicketList getMealPeriod err:%v", err)
	} else {
		for _, v := range mealPeriod.Data {
			tmp, ok := mealSegmentNameChannel[v.Name]
			if ok {
				mealSegmentNameList = append(mealSegmentNameList, tmp)
				delete(mealSegmentNameChannel, v.Name)
			} else {
				mealSegmentNameList = append(mealSegmentNameList, MealSegmentNameChannel{
					MealSegmentName: v.Name,
					Amount:          0,
					Count:           0,
				})
			}
		}

		for _, v := range mealSegmentNameChannel {
			mealSegmentNameList = append(mealSegmentNameList, MealSegmentNameChannel{
				MealSegmentName: v.MealSegmentName,
				Amount:          v.Amount,
				Count:           v.Count,
			})
		}
	}
	// 云端配置的卡劵渠道
	//couponCooperations, err := getCouponCooperation(d)
	//if err == nil {
	//	for _, v := range couponCooperations {
	//		to, ok := promotionsChannelMap[v.PromotionCode]
	//		if ok {
	//			promotionsChannel = append(promotionsChannel, to)
	//			delete(promotionsChannelMap, v.PromotionCode)
	//		}
	//	}
	//}
	// hub 门店促销处理
	//var items []PromotionItem
	//for i := range promotionsChannelMap {
	//	if promotionsChannelMap[i].Source == TicketSourcePos && !promotionsChannelMap[i].IsProduct {
	//		items = append(items, PromotionItem{
	//			Type:     promotionsChannelMap[i].Name,
	//			Discount: promotionsChannelMap[i].PromotionCoupons.Discount,
	//			Count:    promotionsChannelMap[i].PromotionCoupons.Count,
	//		})
	//	}
	//}
	var ticketCounts = allTicketStatistics.Count - cancelTicketStatistics.Count //订单交易数=总订单数-取消交易数
	overflowStatistics.Amount = -overflowSum
	overflowStatistics.Count = overflowCount
	if removeZeroCount > 0 {
		removeZeroStatistics.Count = removeZeroCount
		removeZeroStatistics.Amount = removeZeroSum
	}
	taxMap, tax, taxAmount := sumTax(nil, taxMap, true)
	//payInfoNew := dailyPayInfo(d)
	var (
		pays        PayItems
		payOverflow PayItems
	)
	var (
		paysAmount                float64
		paycount                  int
		payMerchantAllowance      float64
		overflowAmount            float64
		overflow_Count            int
		overflowMerchantAllowance float64
	)

	for k, v := range paymentsMap {
		if strings.Contains(k, "溢收") {
			payOverflow = append(payOverflow, v)
			overflowAmount = utils.Add(2, overflowAmount, v.PayAmount)
			overflowMerchantAllowance = utils.Add(2, overflowMerchantAllowance, v.MerchantAllowance)
			overflow_Count += v.Count
		} else {
			pays = append(pays, v)
			paysAmount = utils.Add(2, paysAmount, v.PayAmount)
			payMerchantAllowance = utils.Add(2, payMerchantAllowance, v.MerchantAllowance)
			paycount += v.Count
		}

	}

	sort.Sort(pays)
	sort.Sort(payOverflow)
	for _, k := range promotionsMap {
		if k.Count == 0 && strings.Contains(k.Type, TicketSourcePos) {
			delete(promotionsMap, k.Type)
		}
		discountDealsStatistics.Count += k.Count
		discountDealsStatistics.Amount += k.Discount
	}
	_, promotions := sumPromotions(ctx, nil, promotionsMap, true)
	sort.Sort(promotions)
	var changeList []PayItem
	var changeAmount float64
	_, userInfoItems := sumUserItem(nil, userItemsMap, true)
	var shiftResult ticketStatistic
	channel_map, channel_new := calChannel(allTicketStatistics.GrossAmount, channelMap)

	allTicketStatistics.PriceTrade = utils.Divide(2, allTicketStatistics.BusinessAmount, float64(allTicketStatistics.Count))
	allTicketStatistics.PriceQuantity = channel_map["price_quantity"].(float64)

	//非原单退款计算
	//err = d.getDataDB().db.Find("BusDate", d.getSaleTimeStr(), &cashRefunds)
	//if err != nil {
	//	logrus.Errorf("日结结算非原单退款 err:%v", err)
	//}
	//for _, v := range cashRefunds {
	//	cashRefund := v
	//	cashRefundAmount = util.Add(2, cashRefundAmount, cashRefund.Amount)
	//}

	//门店报损
	//err = d.getDataDB().db.Find("BusDate", d.getSaleTimeStr(), &frmLossList)
	//if err != nil {
	//	logrus.Errorf("日结计算门店报损 err:%v", err)
	//}
	//for index := range frmLossList {
	//	frmLoss := frmLossList[index]
	//	for _, v := range frmLoss.FrmLossProduct {
	//		product := v
	//		frmLossAmount = util.Add(2, frmLossAmount, util.DecimalTwo(product.Price*product.Quantity))
	//		frmLossCount = util.Add(2, frmLossCount, product.Quantity)
	//		for _, addition := range product.Accesssories {
	//			frmLossAmount = util.Add(2, frmLossAmount, util.DecimalTwo(addition.Quantity*addition.Price))
	//			frmLossCount = util.Add(2, frmLossCount, addition.Quantity)
	//		}
	//	}
	//}
	if isDaily {
		ticketStatistics := appendDaily(ticketCounts, people, allTicketStatistics)
		dailyKnotInfo := DailyKnotInfo{
			//Payinfo:                   payInfoNew,
			//Store:                     storeInfo,
			Pays:                      pays,
			PayOverflow:               payOverflow,
			OverflowAmount:            overflowAmount,
			OverflowCount:             overflow_Count,
			OverflowMerchantAllowance: overflowMerchantAllowance,
			//OfflinePays:             offline,
			//OnlinePays:              online,
			Change: changeList,
			//OfflinePaysAmount:       offlineAmount,
			//OnlinePaysAmount:        onlineAmount,
			PayCount:             paycount,
			PayMerchantAllowance: payMerchantAllowance,
			ChangeAmount:         changeAmount,
			Tickets:              ticketStatistics,
			Count:                int64(ticketCounts),
			SumOverflow:          overflowSum,
			UnUpload:             isUnUpload,
			Promotion:            promotions,
			//PosPromotion:            items,
			//PromotionChannel:        promotionsChannel,
			PromotionsChannelMap:    promotionsChannelMap,
			DiscountDealsStatistics: discountDealsStatistics,
			AllTicketStatistics:     allTicketStatistics,
			RemoveZeroStatistics:    removeZeroStatistics,
			CancelTicketStatistics:  cancelTicketStatistics,
			RoundingStatistics:      roundingStatistics,
			OverflowStatistics:      overflowStatistics,
			Tax:                     tax,
			TaxTotalAmount:          taxAmount,
			NetTrade:                allMoney,
			PaysAmount:              paysAmount,
			UserItems:               userInfoItems,
			Channels:                channel_map,
			ChannelsNew:             channel_new,
			//CashRefundAmount:        cashRefundAmount,
			//CashRefundCount:         len(cashRefunds),
			//FrmLossAmount:           frmLossAmount,
			//FrmLossCount:            frmLossCount,
			MealSegmentNameList: mealSegmentNameList,
		}
		return dailyKnotInfo, shiftResult
	}

	// 交班计算
	shiftResult = ticketStatistic{
		Pays:                      pays,
		PayCount:                  paycount,
		PayOverflow:               payOverflow,
		OverflowAmount:            overflowAmount,
		OverflowCount:             overflow_Count,
		OverflowMerchantAllowance: overflowMerchantAllowance,
		//OfflinePays:             offline,
		//OnlinePays:              online,
		Change: changeList,
		//OfflinePaysAmount:       offlineAmount,
		//OnlinePaysAmount:        onlineAmount,
		ChangeAmount:            changeAmount,
		Count:                   int64(ticketCounts),
		SumOverflow:             overflowSum,
		Promotion:               promotions,
		DiscountDealsStatistics: discountDealsStatistics,
		AllTicketStatistics:     allTicketStatistics,
		RemoveZeroStatistics:    removeZeroStatistics,
		CancelTicketStatistics:  cancelTicketStatistics,
		OverflowStatistics:      overflowStatistics,
		RoundingStatistics:      roundingStatistics,
		TaxTotalAmount:          taxAmount,
		Tax:                     tax,
		NetTrade:                allMoney,
		PaysAmount:              paysAmount,
		PayMerchantAllowance:    payMerchantAllowance,
		PromotionsAmount:        sumPromotions1(promotionsMap),
	}
	return DailyKnotInfo{}, shiftResult
}

// pos日结报表
func PosDailyReport(ctx context.Context, req *sales_report.DailyRequest) (*sales_report.DailyResponse, error) {
	pos.SetLang(ctx)
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	if len(req.StartTime) == 0 && len(req.EndTime) == 0 {
		req.StartTime = req.BusDate
		req.EndTime = req.BusDate
	}
	tickets, err := repo.DefaultPosRepo.QueryStoreTickets(ctx, partnerID, storeId, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	// todo  Store  云端配置的卡劵渠道 hub 门店促销处理 payInfoNew 非原单退款计算 门店报损 需要hub在处理
	var info []byte
	if len(req.Host) == 0 {
		dailyKnotInfo, _ := CalTicketList(ctx, tickets, true)
		info, err = json.Marshal(&dailyKnotInfo)
		if err != nil {
			logrus.Errorf("json.Marshal err:%v", err)
			return nil, err
		}
	} else {
		dailyKnotInfo, _ := NewCalTicketList(ctx, req, tickets)
		info, err = json.Marshal(&dailyKnotInfo)
		if err != nil {
			logrus.Errorf("json.Marshal err:%v", err)
			return nil, err
		}
	}
	resp := sales_report.DailyResponse{}
	resp.Extend = string(info)
	return &resp, nil
}
