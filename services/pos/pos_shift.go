package pos

import (
	"context"
	"encoding/json"
	"github.com/sirupsen/logrus"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

// pos交班报表
func PosShiftReport(ctx context.Context, req *sales_report.ShiftReportRequest) (*sales_report.ShiftInfoResponse, error) {
	pos.SetLang(ctx)
	storeId := pos.GetStoreId(ctx)
	tickets, err := repo.DefaultPosRepo.QueryStoreTicketsByShiftNumber(ctx, storeId, req.ShiftNumber)
	if err != nil {
		logrus.Errorf("PosShiftReport QueryStoreTicketsByShiftNumber err:%v", err)
		return nil, err
	}
	_, shiftInfo := CalTicketList(ctx, tickets, false)
	info, err := json.Marshal(&shiftInfo)
	if err != nil {
		logrus.Errorf("PosShiftReport json.Marshal err:%v", err)
		return nil, err
	}
	resp := sales_report.ShiftInfoResponse{}
	resp.Extend = string(info)
	return &resp, nil
}
