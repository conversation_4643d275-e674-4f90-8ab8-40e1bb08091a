package pos

import (
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

const (
	DeviceTypePC         = "PC"
	DeviceTypeKIOSK      = "KIOSK"
	DeviceTypePADAndroid = "PAD_ANDROID"
	DeviceTypePAD        = "PAD"
	TicketSourcePos      = "POS"
	DineInOrder          = "DINEIN"   // 堂食
	TakeWayOrder         = "TAKEAWAY" // 外带
	TakeOutOrder         = "TAKEOUT"  // 外卖
	SelfHelpOrder        = "SELFHELP" // 自提
	INVALIDOrder         = "INVALID"  // INVALID
	WeChatAPPTp          = "wechat"
	MtTp                 = "meituan"
	KeeTa                = "keeta_isv"
	ElemeTp              = "eleme"
	ElemeIsvTp           = "ele_isv"
	KouBeiTp             = "koubei"
	AliPayApp            = "alipayapp" // 支付宝
	AliPay               = "alipay"
	MtIsvTp              = "mt_isv"
)

type channelKey struct {
	NotifyTakeMeal  bool            `json:"notify_take_meal"`
	IsAppChannel    bool            `json:"is_app_channel"`
	Name            string          `json:"name,omitempty"`
	Sort            int             `json:"sort"`
	PromotionsTrans PromotionsTrans `json:"-"` // 日结促销用于显示的中文描述
	IsPublicChannel bool
}

// PromotionsTrans 添加中文说明
type PromotionsTrans struct {
	MerchantSendFee            string //配送费
	Commission                 string //佣金
	OtherFee                   string // 其他
	DiscountMerchantContribute string // 折扣
}

type codeKey struct {
	TpName     string
	DeviceType string
	OrderType  string
}

var (
	CodeMap    map[codeKey]string
	ChannelMap map[string]channelKey
)

const (
	SortWeightDiscount = iota
	SortWeightCommission
	SortWeightSendFee
	SortWeightOtherFee
)

func init() {
	InitConfigMap()
}

func InitConfigMap() {
	ChannelMap = map[string]channelKey{
		WeChatAPPTp: {
			IsAppChannel: true,
			Name:         utils.GetI18nLabel("WechatMiniProgram"),
			Sort:         2,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("WechatMiniProgramDiscount"),
				Commission:                 utils.GetI18nLabel("WechatMiniProgramCommissions"),
				MerchantSendFee:            utils.GetI18nLabel("WechatMiniProgramDeliveryFee"),
				OtherFee:                   utils.GetI18nLabel("WechatMiniProgramOther"),
			},
		},
		AliPay: { // 日结计算时：支付宝小程序和微信小程序算在一起，统一处理
			IsAppChannel: true,
			Name:         utils.GetI18nLabel("AlipayMiniProgram"),
			Sort:         2,
		},
		MtTp: {
			NotifyTakeMeal:  true,
			Name:            utils.GetI18nLabel("Meituan"),
			Sort:            3,
			IsPublicChannel: true,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("MeituanDiscount"),
				Commission:                 utils.GetI18nLabel("MeituanCommissions"),
				MerchantSendFee:            utils.GetI18nLabel("MeituanDeliveryFee"),
				OtherFee:                   utils.GetI18nLabel("MeituanOther"),
			},
		},
		KeeTa: {
			NotifyTakeMeal:  true,
			Name:            utils.GetI18nLabel("KettaISV"),
			Sort:            3,
			IsPublicChannel: true,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("KettaISVDiscount"),
				Commission:                 utils.GetI18nLabel("KettaISVCommissions"),
				MerchantSendFee:            utils.GetI18nLabel("KettaISVDeliveryFee"),
				OtherFee:                   utils.GetI18nLabel("KettaISVOther"),
			},
		},
		ElemeTp: {
			NotifyTakeMeal:  true,
			Name:            utils.GetI18nLabel("Eleme"),
			Sort:            4,
			IsPublicChannel: true,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("ElemeDiscount"),
				Commission:                 utils.GetI18nLabel("ElemeCommissions"),
				MerchantSendFee:            utils.GetI18nLabel("ElemeDeliveryFee"),
				OtherFee:                   utils.GetI18nLabel("ElemeOther"),
			},
		},
		ElemeIsvTp: {
			NotifyTakeMeal:  true,
			Name:            utils.GetI18nLabel("ElemeISV"),
			Sort:            4,
			IsPublicChannel: true,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("ElemeDiscountISV"),
				Commission:                 utils.GetI18nLabel("ElemeCommissionsISV"),
				MerchantSendFee:            utils.GetI18nLabel("ElemeDeliveryFeeISV"),
				OtherFee:                   utils.GetI18nLabel("ElemeOtherISV"),
			},
		},
		MtIsvTp: {
			NotifyTakeMeal:  true,
			Name:            utils.GetI18nLabel("MeituanISV"),
			Sort:            3,
			IsPublicChannel: true,
			PromotionsTrans: PromotionsTrans{
				DiscountMerchantContribute: utils.GetI18nLabel("MeituanDiscountISV"),
				Commission:                 utils.GetI18nLabel("MeituanCommissionsISV"),
				MerchantSendFee:            utils.GetI18nLabel("MeituanDeliveryFeeISV"),
				OtherFee:                   utils.GetI18nLabel("MeituanOtherISV"),
			},
		},
		TicketSourcePos: {
			NotifyTakeMeal: true,
			Name:           "POS",
			Sort:           1,
		},
	}

	CodeMap = map[codeKey]string{
		{
			DeviceType: DeviceTypePC,
		}: "703",
		{
			DeviceType: DeviceTypeKIOSK,
		}: "666",
		{
			DeviceType: DeviceTypePAD,
		}: "999",
		{
			DeviceType: DeviceTypePADAndroid,
		}: "999",
		{
			TpName: WeChatAPPTp,
		}: "801",
		{
			TpName: AliPay,
		}: "801",
		{
			TpName:    AliPay,
			OrderType: DineInOrder,
		}: "703",
		{
			TpName:    WeChatAPPTp,
			OrderType: DineInOrder,
		}: "703",
		{
			TpName: MtTp,
		}: "133",
		{
			TpName: ElemeTp,
		}: "135",
		{
			TpName: MtIsvTp,
		}: "MT_ISV",
		{
			TpName: ElemeIsvTp,
		}: "ELE_ISV",
	}
}

func IsAppChannel(tpName string) bool {
	return ChannelMap[tpName].IsAppChannel
}

func GetChannelName(tpName string) string {
	if channel, ok := ChannelMap[tpName]; ok {
		return channel.Name
	} else {
		return tpName
	}
}

func GetChannelSort(tpName string) int {
	if channel, ok := ChannelMap[tpName]; ok {
		return channel.Sort
	} else {
		return 1
	}
}
