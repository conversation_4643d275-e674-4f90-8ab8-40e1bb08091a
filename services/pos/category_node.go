package pos

import (
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"sort"
)

type Node struct {
	TicketId        string   `json:"ticket_id" storm:"id"`
	RefTicketId     string   `json:"ref_ticket_id"`
	ProductName     string   `json:"product_name"`   // 商品名称 / 类目名称
	ProductCode     string   `json:"product_code"`   // 商品code / 类目 编码
	Quantity        int      `json:"quantity"`       // 商品数量
	SumAmount       float64  `json:"sum_net_amount"` //商品实收
	ProductCategory string   `json:"-"`              // 商品编码
	CategoryID      string   `json:"category_id"`    // 商品所属类目
	ParentID        string   `json:"parent_id"`
	Children        NodeList `json:"children"`  //[]*Node
	UnitName        string   `json:"unit_name"` // 商品单位
	HasWeight       bool     `json:"has_weight"`
	Weight          float64  `json:"weight"` // 称重商品的重量
}

type NodeList []*Node

func (n NodeList) Len() int {
	return len(n)
}

func (n NodeList) Less(i, j int) bool {
	if n[i].Quantity == n[j].Quantity {
		return n[i].SumAmount > n[j].SumAmount
	}
	return n[i].Quantity > n[j].Quantity
}

func (n NodeList) Swap(i, j int) {
	n[i], n[j] = n[j], n[i]
}

func addChildren(t *Node, children []*Node) {
	//t.Children=children
	t.Children = append(t.Children, children...)
	sort.Sort(t.Children)
}

// BuildNode  将list 转换成一个具有层级关系的树， 类似多级菜单,
// 全部的数量， 全部的钱
func BuildNode(nodes []*Node) ([]*Node, int, float64) {
	var (
		rootMenu  NodeList
		sumNumber int
		sumAmount float64
	)
	// 找到所有的一级目录
	for i := 0; i < len(nodes); i++ {
		if nodes[i].ParentID == "0" || nodes[i].ParentID == "" {
			rootMenu = append(rootMenu, nodes[i])
			sumAmount = utils.Add(2, sumAmount, nodes[i].SumAmount)
			sumNumber = sumNumber + nodes[i].Quantity
		}
	}
	/* 根据NodesPtr排序 */
	sort.Sort(rootMenu)
	//为根菜单设置子节点，getClild是递归调用的
	for index := range rootMenu {
		p := rootMenu[index]
		childList := getChild(p.CategoryID, nodes)
		addChildren(p, childList)
	}
	return rootMenu, sumNumber, sumAmount
}

// getChild 根据id 查找该节点的所有子节点
func getChild(id string, allNodes []*Node) []*Node {
	var childList NodeList
	for index := range allNodes {
		p := allNodes[index]
		// 遍历所有节点，将所有菜单的父id与传过来的根节点的id比较 相等说明：为该根节点的子节点。
		if p.ParentID == id {
			childList = append(childList, p)
		}
	}
	//递归
	for cidx := range childList {
		nav := childList[cidx]
		addChildren(nav, getChild(nav.CategoryID, allNodes))
	}
	/* 根据NodesPtr排序 */

	sort.Sort(childList)
	//如果节点下没有子节点，返回一个空List（递归退出）
	if childList == nil || len(childList) == 0 {
		return childList
	}
	return childList
}

// buildCategoryLevel 构建商品类目等级 [level3, level2, level1], 目前只管理3层
func buildCategoryLevel(son *Category, allCategory []*Category, result []*Category) []*Category {
	if son.ParentID == "" || son.ParentID == "0" {
		return result
	}
	for index := range allCategory {
		if allCategory[index].ID == son.ParentID {
			result = append(result, allCategory[index])
			son = allCategory[index]
			return buildCategoryLevel(son, allCategory, result)
		}
	}
	return result
}
