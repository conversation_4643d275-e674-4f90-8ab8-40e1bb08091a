package pos

import (
	"context"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

const (
	TicketStatusRefund         = "REFUND"
	TicketStatusPartialRefund  = "PARTIALREFUND"
	TicketStatusRefundComplete = "REFUNDCOMPLETED"
	StatusRefunding            = "REFUNDING"
	StatusRefundComplete       = "REFUNDCOMPLETED"
	StatusRefund               = "REFUND"
	StatusSuccessFul           = "SUCCESSFUL"
	StatusPARTIALSUCCESSFUL    = "PARTIALSUCCESSFUL"
	TicketStatusSale           = "SALE"
)

func queryTickets(ctx context.Context, req *sales_report.HubReportQuery) ([]*model.Ticket, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	tickets, err := repo.DefaultPosRepo.QueryStoreTickets(ctx, partnerID, storeId, req.StartTime, req.EndTime)
	if err != nil {
		return nil, err
	}
	return tickets, nil
}

func queryProductsCategory(ctx context.Context, skus []int64) (map[int64]int64, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	return repo.DefaultPosRepo.QueryProductsCategory(ctx, partnerID, skus)
}

func queryProductsCategoryMap(ctx context.Context) (map[int64]int64, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	return repo.DefaultPosRepo.QueryProductsCategoryMap(ctx, partnerID)
}

func queryGetStoreCache(ctx context.Context) (string, error) {
	storeId := pos.GetStoreId(ctx)
	return repo.DefaultMetadataCache.GetStoreCacheById(ctx, storeId)
}
