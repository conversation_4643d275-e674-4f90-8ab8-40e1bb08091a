package pos

import (
	"context"
	"errors"
	"github.com/sirupsen/logrus"
	"github.com/zeromicro/go-zero/core/mr"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"sort"
)

type ProductChannel struct {
	Name         string  `json:"name"`           //产品名称
	SumAmount    float64 `json:"sum_amount"`     //商品原价
	SumNetAmount float64 `json:"sum_net_amount"` //商品实收
	TaxAmount    float64 `json:"taxAmount"`      // 税额
	Quantity     int     `json:"quantity"`       // 商品数量
	HasWeight    bool    `json:"has_weight"`     // 是否称重
	Weight       float64 `json:"weight"`         // 称重商品的重量
	TpName       string  `json:"tp_name"`
}

type SumProduct struct {
	SumAmount    float64 `json:"sum_amount"`     //商品原价
	SumNetAmount float64 `json:"sum_net_amount"` //商品实收
	TaxAmount    float64 `json:"taxAmount"`      // 税额
	Quantity     int     `json:"quantity"`       // 商品数量
	Weight       float64 `json:"weight"`         // 称重商品的重量
}

type ProductReport struct {
	Id         string      `json:"id" storm:"id"`
	PosCode    string      `json:"pos_code" storm:"index"`
	StartTime  string      `json:"start_time"`
	EndTime    string      `json:"end_time"`
	Operator   string      `json:"operator"`                  // 操作人
	ParentTime string      `json:"parent_time" storm:"index"` // start_time+end_time  为了加速查找
	Products   ProductList `json:"channel"`
	Sum        SumProduct  `json:"sum"`
}

type ProductList []ProductChannel

func (p ProductList) Len() int {
	return len(p)
}

func (p ProductList) Less(i, j int) bool {
	if p[i].SumAmount == p[j].SumAmount {
		return p[i].SumNetAmount > p[j].SumNetAmount
	}
	return p[i].SumAmount > p[j].SumAmount
}

func (p ProductList) Swap(i, j int) {
	p[i], p[j] = p[j], p[i]
}

// pos商品报表
func PosProductReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.ProductReportResponse, error) {
	pos.SetLang(ctx)
	productReport := ProductReport{}
	channel := make(map[string]ProductChannel)
	to, err := queryTickets(ctx, req)
	if err != nil {
		logrus.Errorf("PosProductReport queryTicketserr:%v", err)
		return nil, err
	}
	_, err = mr.MapReduce(func(source chan<- interface{}) {
		for i, _ := range to {
			tpName := to[i].Channel.TpName
			if len(tpName) == 0 || tpName == "pos" {
				tpName = "POS"
			}
			var isChannel bool
			for _, v := range req.Channel {
				if v == tpName {
					isChannel = true
				}
			}
			if len(req.Channel) == 0 {
				isChannel = true
			}
			if !isChannel {
				continue
			}
			source <- to[i]
		}
	}, func(item interface{}, writer mr.Writer, cancel func(error)) {
		//数据处理
		t := item.(*model.Ticket)
		businessChannel := ProductChannel{}
		businessChannel.Name = t.Channel.TpName
		tpName := t.Channel.TpName
		if len(tpName) == 0 {
			tpName = "POS"
		}
		businessChannel.TpName = tpName

		writeChannel := func(product *model.TicketProduct) {
			businessChannel.Name = product.Name
			businessChannel.SumAmount = product.Amount
			businessChannel.SumNetAmount = product.NetAmount
			businessChannel.Quantity = product.Qty
			businessChannel.HasWeight = product.HasWeight
			businessChannel.Weight = product.Weight
			businessChannel.TaxAmount = product.TaxAmount
			writer.Write(businessChannel)
		}
		for i := range t.Products {
			writeChannel(t.Products[i])
			for j := range t.Products[i].Accessories {
				writeChannel(t.Products[i].Accessories[j])
			}
		}

	},
		func(pipe <-chan interface{}, writer mr.Writer, cancel func(error)) {
			//数据聚合
			for p := range pipe {
				businessChannel, ok := p.(ProductChannel)
				if ok {
					channelName := GetChannelName(businessChannel.TpName)
					var cname string
					if len(req.Channel) == 0 {
						cname = businessChannel.Name
						channelName = utils.GetI18nLabel("Summarychannels")
					} else {
						cname = channelName + businessChannel.Name
					}
					chInfo, ok := channel[cname]
					if !ok {
						chInfo = ProductChannel{}
					}
					chInfo.TpName = channelName
					chInfo.Name = businessChannel.Name
					chInfo.Quantity += businessChannel.Quantity
					chInfo.SumAmount = utils.Add(2, chInfo.SumAmount, businessChannel.SumAmount)
					chInfo.SumNetAmount = utils.Add(2, chInfo.SumNetAmount, businessChannel.SumNetAmount)
					chInfo.HasWeight = businessChannel.HasWeight
					chInfo.TaxAmount = utils.Add(2, chInfo.TaxAmount, businessChannel.TaxAmount)
					if businessChannel.HasWeight {
						chInfo.Weight = utils.Add(2, chInfo.Weight, businessChannel.Weight)
					}

					channel[cname] = chInfo
				}
			}
			writer.Write(channel)

		})
	if err != nil && !errors.Is(err, mr.ErrReduceNoOutput) {
		logrus.Errorf("PosProductReport MapReduce err:%v", err)
		return nil, err
	}
	sum := SumProduct{}
	for k := range channel {
		p := channel[k]
		productReport.Products = append(productReport.Products, p)
		sum.Quantity += p.Quantity
		sum.SumAmount = utils.Add(2, sum.SumAmount, p.SumAmount)
		sum.SumNetAmount = utils.Add(2, sum.SumNetAmount, p.SumNetAmount)
		sum.Weight = utils.Add(2, sum.Weight, p.Weight)
		sum.TaxAmount = utils.Add(2, sum.TaxAmount, p.TaxAmount)
	}
	productReport.Sum = sum
	sort.Sort(productReport.Products)
	rsp := sales_report.ProductReportResponse{}
	var channelProducts []*sales_report.ProductReportResponse_Product
	for _, k := range productReport.Products {
		channelProducts = append(channelProducts, &sales_report.ProductReportResponse_Product{
			Name:         k.Name,
			SumAmount:    k.SumAmount,
			SumNetAmount: k.SumNetAmount,
			Quantity:     int32(k.Quantity),
			HasWeight:    k.HasWeight,
			Weight:       k.Weight,
			TpName:       k.TpName,
			TaxAmount:    k.TaxAmount,
		})
	}
	sumProducts := sales_report.ProductReportResponse_SumProduct{
		SumAmount:    sum.SumAmount,
		SumNetAmount: sum.SumNetAmount,
		Quantity:     int32(sum.Quantity),
		Weight:       sum.Weight,
		TaxAmount:    sum.TaxAmount,
	}
	rsp.Channel = channelProducts
	rsp.Sum = &sumProducts

	return &rsp, nil
}
