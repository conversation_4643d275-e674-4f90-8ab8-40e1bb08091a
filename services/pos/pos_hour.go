package pos

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"math"
	"strings"
	"time"
)

const TimeFormatSecond = "2006-01-02 15:04:05"

// HourReport storm 建立不同bucket
type HourReport struct {
	ID         string                 `json:"id" storm:"id"`             // primary key with auto increment
	ParentTime string                 `json:"parent_time" storm:"index"` // start_time+end_time  为了加速查找
	BeginTime  string                 `json:"start_time"`
	EndTime    string                 `json:"end_time"`
	Operator   string                 `json:"operator"` // 操作人
	PosCode    string                 `json:"pos_code"` // pos编码
	Data       map[string]interface{} `json:"data"`
	BusDate    string                 `json:"busDate" storm:"index"`
}

// Period 时段报表
type Period struct {
	MealName              string   `json:"meal_name"`
	Date                  string   `json:"date"`
	Quantity              int      `json:"quantity"` // 该时间段的商品数量
	Amount                float64  `json:"amount"`   // 该时间段的商品金额
	BusinessAmount        float64  `json:"business_amount"`
	Trade                 int      `json:"trade"` // 订单数量， 交易量
	BussinessDayStartTime string   `json:"bussinessDayStartTime"`
	Period                []string `json:"period"`
	HasTrade              bool     `json:"-"`
}

// periodKeys 生成时段数组
// isHour 是否以 1个小时为间隔时间
func periodKeys(mealPeriod *MealPeriod, isHour, isMeal bool) []Period {
	//餐段统计
	var keys = make([]Period, 0, 48)
	if isMeal {
		for _, v := range mealPeriod.Data {
			duration := fmt.Sprintf("%s (%s-%s)", v.Name, v.Period[0], v.Period[1])
			tmp := Period{
				MealName: v.Name,
				Date:     duration,
				Period:   v.Period,
			}
			keys = append(keys, tmp)
		}
		return keys
	}

	choices := []string{"00", "30"}
	if isHour {
		for i := 0; i < 24; i++ {
			if isHour {
				duration := fmt.Sprintf("%d:%s-%d:%s", i, choices[0], i+1, choices[0])
				tmp := Period{
					Date: duration,
				}
				keys = append(keys, tmp)
			}
		}
		return keys
	}

	for i := 0; i < 24; i++ {

		duration := fmt.Sprintf("%d:%s-%d:%s", i, choices[0], i, choices[1])
		tmp := Period{
			Date: duration,
		}
		keys = append(keys, tmp)
		duration = fmt.Sprintf("%d:%s-%d:%s", i, choices[1], i+1, choices[0])
		tmp = Period{
			Date: duration,
		}
		keys = append(keys, tmp)
	}
	return keys
}

// getPeriodIndex 获取输入t 在时间段中的位置
func getPeriodIndex(t string, isHour bool) (int, error) {
	now, err := time.Parse(TimeFormatSecond, t)
	if err != nil {
		return 0, errors.New("getPeriodIndex:time.Parse")
	}
	totalMinutes := now.Hour()*60 + now.Minute()
	if isHour {
		return totalMinutes / 60, nil
	}
	return totalMinutes / 30, nil
}

// 根据餐段名称 获取位置
func getMealPeriodIndex(t *model.Ticket, periods []Period) (int, error) {
	endTime := strings.Split(t.EndTime, " ")[0]
	var duration string
	for i, v := range periods {
		if v.MealName == t.MealSegmentName {
			if t.BusDate == endTime {
				return i, nil
			} else {
				if v.BussinessDayStartTime >= strings.Split(t.EndTime, " ")[1] {
					duration = fmt.Sprintf("%s (%s-%s)", v.MealName, v.Period[0], v.BussinessDayStartTime)
				} else {
					duration = fmt.Sprintf("%s (%s-%s)", v.MealName, v.BussinessDayStartTime, v.Period[1])
				}
				if v.Date == duration {
					return i, nil
				}
			}
		}
	}
	return 0, errors.New("getMealPeriodIndex:time.Parse")
}

// findInterval 在对应的时间段添加该商品的信息
func findInterval(ticket *model.Ticket, result []Period, quantity int, amount float64, business float64, isHour, isMeal bool) ([]Period,
	int, float64, float64, error) {
	var (
		idx int
		err error
	)
	// 决定这个ticket 是那个区间的
	if !isMeal {
		idx, err = getPeriodIndex(ticket.EndTime, isHour)
		if err != nil {
			return nil, 0, 0, 0, err
		}
	} else {
		idx, err = getMealPeriodIndex(ticket, result)
		if err != nil {
			logrus.Errorf("getMealPeriodIndex: %v", err)
			return result, 0, 0, 0, nil
		}
	}

	v := result[idx]
	for pix := range ticket.Products {
		p := ticket.Products[pix]
		if p.Qty < 0 {
			p.Qty = -p.Qty
		}
		v.Quantity = v.Quantity + p.Qty
		quantity += p.Qty
	}
	if ticket.Status == TicketStatusRefundComplete {
		ticket.Amounts.RealAmount = math.Abs(ticket.Amounts.RealAmount) // 退款单是负数
		ticket.Amounts.Rounding = math.Abs(ticket.Amounts.Rounding)
		ticket.Amounts.OverflowAmount = math.Abs(ticket.Amounts.OverflowAmount)
	}
	if ticket.Status != TicketStatusPartialRefund {
		v.Trade = v.Trade + 1
	}
	v.Amount = utils.Add(2, v.Amount, RealAmount(ticket))
	v.BusinessAmount = utils.Add(2, v.BusinessAmount, ticket.Amounts.BusinessAmount)
	v.HasTrade = true
	result[idx] = v
	return result, quantity, utils.Add(2, amount, RealAmount(ticket)), utils.Add(2, business, ticket.Amounts.BusinessAmount), nil
}

// RealAmount 实收金额
func RealAmount(t *model.Ticket) float64 {
	return utils.Sub(2, t.Amounts.BusinessAmount, GetExpendAmount(t))
}

func GetExpendAmount(t *model.Ticket) float64 {
	sum1 := utils.Add(2, t.Amounts.DiscountMerchantContribute, t.Amounts.Commission)
	sum2 := utils.Add(2, sum1, t.Amounts.OtherFee)
	sum3 := utils.Add(2, sum2, t.TakeawayInfo.MerchantSendFee)
	sum4 := utils.Add(2, sum3, t.Amounts.PayMerchantContribute)
	sum5 := utils.Add(2, sum4, t.Amounts.Rounding)
	return utils.Sub(2, sum5, t.Amounts.OverflowAmount)
}

// filterPeriods 过滤没有订单信息的时间段
func filterPeriods(periods []Period) []Period {
	var result []Period
	for _, v := range periods {
		if v.HasTrade {
			result = append(result, v)
		}
	}
	return result
}

// GetMealPeriod 根据 endTime 2024-01-23 19:27:08 在 period中 找到对应的餐段
func GetMealPeriod(mealPeriod *MealPeriod, endTime string) (string, []string) {
	//将2024-01-23 19:27:08 转换成 19:27:08
	if len(strings.Split(endTime, " ")) == 2 {
		endTime = strings.Split(endTime, " ")[1]
		for _, v := range mealPeriod.Data {
			// 判断endTime是否在period中 endTime大于等于per iod[0] 小于period[1]
			if endTime >= v.Period[0]+":00" && endTime < v.Period[1]+":00" {
				return v.Name, v.Period
			}
		}
	}
	return "", nil
}

// pos时段报表
func PosHourReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.HourReportResponse, error) {
	tickets, err := queryTickets(ctx, req)
	if err != nil {
		return nil, err
	}

	timeZone, err := queryGetStoreCache(ctx)
	if err != nil {
		logrus.Errorf("queryGetStoreCache err:%v", err)
	}
	logrus.Infoln("时段报表storeCache 时区:", timeZone)
	mealPeriod, err := getMealPeriod(ctx)
	if err != nil {
		logrus.Errorf("PosHourReport getMealPeriod err:%v", err)
		return nil, err
	}
	for index := range tickets {
		t := tickets[index]
		if len(timeZone) != 0 {
			timeZone, err := time.LoadLocation(timeZone)
			if err == nil {
				endTime := time.Unix(t.OrderTime/1000, 0).In(timeZone)
				//logrus.Infoln("时段报表时区转换前:", t.EndTime)
				tickets[index].EndTime = endTime.Format(TimeFormatSecond)
				//logrus.Infoln("时段报表时区转换后:", tickets[index].EndTime)
			} else {
				logrus.Errorf("时段报表LoadLocation err：%v", err)
			}
		}
	}
	resultSale := periodKeys(mealPeriod, req.IsHour, req.IsMeal) // 销售单
	// todo 餐段报表需要检查tickets中是否有 跨天的餐段
	if req.IsMeal {
		var (
			keys       = make([]Period, 0, 2)
			periodName string
		)
		for index := range tickets {
			t := tickets[index]
			endTime := strings.Split(t.EndTime, " ")[0]
			if endTime != t.BusDate {
				name, period := GetMealPeriod(mealPeriod, t.EndTime)
				if len(period) != 0 {
					duration := fmt.Sprintf("%s (%s-%s)", name, period[0], req.BussinessDayStartTime)
					tmp := Period{
						MealName:              name,
						Date:                  duration,
						BussinessDayStartTime: req.BussinessDayStartTime,
						Period:                period,
					}
					periodName = name
					keys = append(keys, tmp)
					duration = fmt.Sprintf("%s (%s-%s)", name, req.BussinessDayStartTime, period[1])
					tmp = Period{
						MealName:              name,
						Date:                  duration,
						BussinessDayStartTime: req.BussinessDayStartTime,
						Period:                period,
					}
					keys = append(keys, tmp)
				}
				break
			}
		}

		if len(keys) != 0 {
			var newKeys = make([]Period, 0, 6)
			for i, v := range resultSale {
				if v.MealName != periodName {
					newKeys = append(newKeys, resultSale[i])
				} else {
					newKeys = append(newKeys, keys...)
				}
			}
			resultSale = newKeys
		}
	}

	resultRefund := append([]Period(nil), resultSale...) // deep copy slice， 退货单
	var (
		sumQuantitySale int
		sumAmountSale   float64
		sumBusinessSale float64
		sumTradeSale    int
		sumTradeRefund  int

		sumQuantityRefund int
		sumAmountRefund   float64
		sumBusinessRefund float64
		errSale           error
		errRefund         error
	)

	for index := range tickets {
		t := tickets[index]
		if len(t.MealSegmentName) == 0 {
			t.MealSegmentName, _ = GetMealPeriod(mealPeriod, t.EndTime)
		}
		// 决定这个ticket 是那个区间的
		if t.Status == TicketStatusSale || t.Status == TicketStatusRefundComplete || t.Status == StatusSuccessFul {
			if req.IsMeal && len(t.MealSegmentName) != 0 {
				sumTradeSale += 1
			} else {
				sumTradeSale += 1
			}

			resultSale, sumQuantitySale, sumAmountSale, sumBusinessSale, errSale = findInterval(t, resultSale, sumQuantitySale,
				sumAmountSale, sumBusinessSale, req.IsHour, req.IsMeal)
			if errSale != nil {
				return nil, errors.Wrap(errSale, "GetHourReport:get findInterval")
			}
		}

		if t.Status == TicketStatusRefund || t.Status == TicketStatusPartialRefund { // 退单
			if t.Status == TicketStatusRefund {
				if req.IsMeal && len(t.MealSegmentName) != 0 {
					sumTradeRefund += 1
				} else {
					sumTradeRefund += 1
				}
			}
			resultRefund, sumQuantityRefund, sumAmountRefund, sumBusinessRefund, errRefund = findInterval(t, resultRefund, sumQuantityRefund,
				sumAmountRefund, sumBusinessRefund, req.IsHour, req.IsMeal)
			if errRefund != nil {
				return nil, errors.Wrap(errRefund, "GetHourReport:get findInterval")
			}
		}
	}
	// filter , 删除 不包含订单的时间段
	// 汇总数据
	var all []Period
	for i := 0; i < len(resultSale); i++ {
		psale := resultSale[i]
		prefund := resultRefund[i]
		tmp := Period{
			Date:           psale.Date,
			Quantity:       psale.Quantity - prefund.Quantity,
			Amount:         utils.Add(2, psale.Amount, prefund.Amount),
			BusinessAmount: utils.Add(2, psale.BusinessAmount, prefund.BusinessAmount),
			Trade:          psale.Trade - prefund.Trade,
			HasTrade:       psale.HasTrade || prefund.HasTrade,
		}
		all = append(all, tmp)
	}
	all = filterPeriods(all)
	resultSale = filterPeriods(resultSale)
	resultRefund = filterPeriods(resultRefund)
	allTickets := map[string]interface{}{
		"data":            all,
		"quantity":        sumQuantitySale - sumQuantityRefund,
		"amount":          utils.Add(2, sumAmountSale, sumAmountRefund),
		"business_amount": utils.Add(2, sumBusinessSale, sumBusinessRefund),
		"trade":           sumTradeSale - sumTradeRefund}

	result := sales_report.HourReportResponse{}
	data, err := json.Marshal(&allTickets)
	if err != nil {
		logrus.Errorf("PosHourReport allTickets Marshal err:%v", err)
		return nil, err
	}
	result.Extend = data
	return &result, nil
}
