package pos

import (
	"context"
	"encoding/json"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"sort"
)

// PosBusinessReport pos营业报表
func PosBusinessReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.BusinessReportResponse, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	report, err := repo.DefaultNewPosRepo.PosBusinessReport(ctx, req.StartTime, req.EndTime, storeId, partnerID)
	if err != nil {
		return nil, err
	}
	sort.Sort(report.Channel)

	data, err := json.Marshal(report)
	if err != nil {
		logrus.Errorf("PosBusinessReport rsp Marshal err:%v", err)
		return nil, err
	}

	businessReport := sales_report.BusinessReportResponse{}
	businessReport.Extend = string(data)

	return &businessReport, nil
}
