package pos

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"sort"
)

// PosProductSalesReport pos商品报表
func PosProductSalesReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.PosProductSalesReportResponse, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	rows, err := repo.DefaultNewPosRepo.PosProductSalesReport(ctx, req.StartTime, req.EndTime, storeId, partnerID)
	if err != nil {
		return nil, err
	}

	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)

	for _, row := range rows {
		categoryIds = append(categoryIds, row.Summary.Id)
		for _, p := range row.ProductSalesList {
			productIds = append(productIds, p.Id)
		}
	}

	lan := pos.GetLang(ctx)
	query := &model.CommonRequest{Lan: lan}
	categoryMaps := report.GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := report.GetProductMapsByIds(ctx, query, productIds)

	for _, row := range rows {
		row.Summary.Name = cast.ToString(categoryMaps[row.Summary.Id]["name"])
		for _, p := range row.ProductSalesList {
			p.Name = cast.ToString(productMaps[p.Id]["name"])
		}
		// 商品明细排序
		sort.Slice(row.ProductSalesList, func(i, j int) bool {
			return row.ProductSalesList[i].Name < row.ProductSalesList[j].Name
		})
	}

	// 对商品类目排序
	sort.Slice(rows, func(i, j int) bool {
		return rows[i].Summary.Name < rows[j].Summary.Name
	})

	rsp := sales_report.PosProductSalesReportResponse{}
	marshal, err := json.Marshal(rows)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &rsp.Rows)
	if err != nil {
		return nil, err
	}

	return &rsp, nil
}
