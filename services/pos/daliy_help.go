package pos

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func formatTicket(t *model.Ticket) TicketInfo {
	ticketInfo := TicketInfo{
		ID:                    t.TicketId,
		RefTicketId:           t.RefundInfo.RefTicketId,
		Amount:                t.Amounts,
		TakeawayInfo:          t.TakeawayInfo,
		BusinessAmount:        t.Amounts.BusinessAmount, //营业额
		RealAmount:            t.Amounts.RealAmount,     //实收金额
		ExpendAmount:          t.Amounts.ExpendAmount,   //支出金额
		Receivable:            t.Amounts.Receivable,     //应付金额
		BusDate:               t.BusDate,
		NetAmt:                t.Amounts.Receivable,
		People:                t.People,
		Tax:                   t.Amounts.TaxAmount,
		Status:                t.Status.String(),
		RefundId:              "",
		Rouding:               t.Amounts.Rounding,
		OverflowAmount:        t.Amounts.OverflowAmount,
		DiscountAmount:        t.Amounts.DiscountAmount,
		Source:                t.Channel.Source,
		TpName:                t.Channel.TpName,
		GrossAmount:           t.Amounts.GrossAmount,
		PayMerchantContribute: t.Amounts.PayMerchantContribute,
		MealSegmentName:       t.MealSegmentName,
	}
	if len(ticketInfo.TpName) == 0 {
		ticketInfo.TpName = TicketSourcePos
	}
	if ticketInfo.TpName == "pos" {
		ticketInfo.TpName = TicketSourcePos
	}
	if t.Status == "REFUND" {
		ticketInfo.RefundId = t.RefundInfo.RefTicketId
	} else if t.Status == "REFUNDCOMPLETED" {
		ticketInfo.RefundId = t.RefundInfo.RefundId
	}
	if t.Channel.Source == "PUSH" {
		ticketInfo.OperatorCode = "UNKNOWN"
		ticketInfo.OperatorName = "外卖"
	} else {
		ticketInfo.OperatorCode = t.Operator.Code
		ticketInfo.OperatorName = t.Operator.Name
	}

	for _, p := range t.Products {
		ticketInfo.Products = append(ticketInfo.Products, ProductInfo{
			ID:       p.Id,
			Name:     p.Name,
			Price:    p.Price,
			Quantity: p.Qty,
		})
	}

	for _, p := range t.Payments {
		ticketInfo.Payments = append(ticketInfo.Payments, p)
	}

	ticketInfo.Promotions = formatPromotions(t, ticketInfo)
	ticketInfo.TicketPromotions = t.Promotions

	for _, p := range t.TaxList {
		ticketInfo.Taxs = append(ticketInfo.Taxs, TaxInfo{
			Code:   p.Code,
			Total:  p.SubTotal,
			Name:   p.Name,
			Amount: p.Amount,
		})
	}
	ticketInfo.Channel = strings.ToLower(formatChannel(t.Channel))
	return ticketInfo
}

// formatPromotions 组装每个渠道的促销信息
func formatPromotions(t *model.Ticket, interTicket TicketInfo) []PromotionInfo {
	result := interTicket.Promotions
	tpName := interTicket.TpName
	source := interTicket.Source
	if isAppChannelAndPos(tpName) { // 微信小程序，支付宝小程序, 线下POS
		for i := range t.Promotions {
			result = append(result, PromotionInfo{
				Name:        t.Promotions[i].PromotionInfo.Name,
				Type:        source,
				Discount:    t.Promotions[i].Source.MerchantAllowance,
				Cost:        t.Promotions[i].Source.Cost,
				TpAllowance: t.Promotions[i].Source.TpAllowance,
			})
		}
	} else { // mt, mt_isv, ele
		order := GetChannelSort(tpName)
		result = append(result, PromotionInfo{
			Type:     ChannelMap[tpName].PromotionsTrans.MerchantSendFee,
			Discount: t.TakeawayInfo.MerchantSendFee,
			Order:    order,
		})
		result = append(result, PromotionInfo{
			Type:     ChannelMap[tpName].PromotionsTrans.Commission,
			Discount: t.Amounts.Commission,
			Order:    order,
		})
		result = append(result, PromotionInfo{
			Type:     ChannelMap[tpName].PromotionsTrans.OtherFee,
			Discount: t.Amounts.OtherFee,
			Order:    order,
		})

		result = append(result, PromotionInfo{
			Type:     ChannelMap[tpName].PromotionsTrans.DiscountMerchantContribute,
			Discount: t.Amounts.DiscountMerchantContribute,
			Order:    order,
		})
	}
	return result
}

func isAppChannelAndPos(tpName string) bool {
	return IsAppChannel(tpName) || tpName == TicketSourcePos || tpName == "pos" // 微信小程序，支付宝小程序, 线下POS
}

func formatChannel(channel model.TicketChannel) string {
	if channel.Source == "POS" {
		if channel.OrderType == DineInOrder { // 堂食
			// POS_DINEIN
			return fmt.Sprintf("%s_%s", channel.Source, DineInOrder)
		}

		if channel.OrderType == TakeWayOrder { // 外带
			// POS_TAKEAWAY
			return fmt.Sprintf("%s_%s", channel.Source, TakeWayOrder)
		}

		if channel.OrderType == SelfHelpOrder { // 自取
			// POS_SELFHELP
			return fmt.Sprintf("%s_%s", channel.Source, SelfHelpOrder)
		}

		if channel.OrderType == TakeOutOrder { // 外卖
			// POS_TAKEOUT
			return fmt.Sprintf("%s_%s", channel.Source, TakeOutOrder)
		}
	}
	//小程序自取单子
	//if channel.TpName == common.WeChatAPPTp {
	//	// SELFHELP_WECHATAPP
	//	return fmt.Sprintf("%s_%s", "TAKEOUT", common.WeChatAPPTp)
	//}

	// 小程序/ 美团 :外卖单子
	return fmt.Sprintf("%s_%s", "TAKEOUT", channel.TpName)
}

// 甩尾
func sumRouding(ticket TicketInfo, roundingStatistics *commonStatistical) {
	if ticket.Rouding == 0 {
		return
	}
	roundingStatistics.Amount = utils.Add(2, roundingStatistics.Amount, ticket.Rouding)
	roundingStatistics.Count++
}

func sumUserItem(ticket *TicketInfo, userItemsMap map[string]UserSaleItem, shoulSum bool) (map[string]UserSaleItem, []UserSaleItem) {
	if shoulSum {
		var userInfoItems []UserSaleItem

		for _, i := range userItemsMap {
			userInfoItems = append(userInfoItems, i)
		}
		return nil, userInfoItems
	}
	userItem, ok := userItemsMap[ticket.OperatorCode]
	if ok {
		userItem.TotalAmount = utils.Add(2, userItem.TotalAmount, ticket.NetAmt)
	} else {
		userItem.Code = ticket.OperatorCode
		userItem.Name = ticket.OperatorName
		userItem.TotalAmount = utils.Add(2, userItem.TotalAmount, ticket.NetAmt)
	}
	userItemsMap[ticket.OperatorCode] = userItem
	return userItemsMap, nil
}

func sumPromotions(ctx context.Context, ticket *TicketInfo, promotionsMap map[string]PromotionItem, shouldSum bool) (
	map[string]PromotionItem, PromotionItems) {
	//统计促销信息
	//折扣额计算不包含取消交易的订单，包括原单和负单
	if shouldSum {
		var promotions []PromotionItem
		for k, v := range promotionsMap {
			if k != "服务费" {
				promotions = append(promotions, PromotionItem{
					Type:     v.Type,
					Discount: v.Discount,
					Count:    v.Count,
					Sort:     v.Sort,
				})
			} else {
				promotions = append(promotions, PromotionItem{
					Type:     v.Type,
					Discount: v.Discount,
					Count:    v.Count,
					Sort:     v.Sort,
				})
			}
		}
		return nil, promotions
	}

	for _, promotion := range ticket.Promotions {
		var (
			promoItem PromotionItem
			ok        bool
		)
		if isAppChannelAndPos(ticket.TpName) {
			var promoName string
			if IsAppChannel(ticket.TpName) {
				promoName = GetChannelName(ticket.TpName) + "-" + promotion.Name
			} else {
				promoName = ticket.Source + "-" + promotion.Name
			}

			promoItem, ok = promotionsMap[promoName]
			if !ok {
				promoItem = PromotionItem{
					Type:     promoName,
					Discount: 0,
					Count:    0,
					Sort:     GetChannelSort(ticket.TpName),
				}
			}

		} else {
			promoItem, ok = promotionsMap[promotion.Type]
			if !ok {
				promoItem = PromotionItem{
					Type:     GetChannelName(ticket.TpName),
					Discount: 0,
					Count:    0,
					Sort:     GetChannelSort(ticket.TpName),
				}
			}
		}

		if ticket.Status == TicketStatusRefund {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				if promotion.Discount != 0 {
					promoItem.Count--
				}
			}
		} else if ticket.Status != TicketStatusPartialRefund {
			if promotion.Discount != 0 {
				promoItem.Count++
			}
		}
		promoItem.Discount = utils.Add(2, promoItem.Discount, cast.ToFloat64(promotion.Discount))
		promotionsMap[promoItem.Type] = promoItem
	}
	return promotionsMap, nil
}

func sumPromotionsChannel(ctx context.Context, ticket *TicketInfo, promotionsChannelMap map[string]PromotionChannel) map[string]PromotionChannel {
	//统计促销信息
	for _, promotion := range ticket.TicketPromotions {
		var (
			promoItem PromotionChannel
			ok        bool
			item      Item
		)
		if len(promotion.PromotionInfo.PromotionCode) == 0 {
			continue
		}
		promoItem, ok = promotionsChannelMap[promotion.PromotionInfo.PromotionCode]
		if !ok {
			promoItem = PromotionChannel{
				Type:      promotion.PromotionInfo.Type,
				Name:      promotion.PromotionInfo.Name,
				Source:    ticket.Source,
				Code:      promotion.PromotionInfo.PromotionCode,
				IsProduct: promotion.PromotionInfo.Type == "2", // type 等于2 为商品卷
			}
		}
		if promoItem.IsProduct {
			item = promoItem.PromotionProduct
		} else {
			item = promoItem.PromotionCoupons
		}

		if ticket.Status == TicketStatusRefund {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				if promotion.Source.MerchantAllowance != 0 {
					item.Count--
				}
			}
		} else if ticket.Status != TicketStatusPartialRefund {
			if promotion.Source.MerchantAllowance != 0 {
				item.Count++
			}
		}
		item.Discount = utils.Add(2, item.Discount, utils.Sub(2, promotion.Source.Discount, promotion.Source.RealAmount))
		item.RealAmount = utils.Add(2, item.RealAmount, cast.ToFloat64(promotion.Source.RealAmount))
		item.Sum = utils.Add(2, item.Sum, cast.ToFloat64(promotion.Source.Discount))
		if promoItem.IsProduct {
			promoItem.PromotionProduct = item
		} else {
			promoItem.PromotionCoupons = item
		}
		promotionsChannelMap[promoItem.Code] = promoItem
	}
	return promotionsChannelMap
}

func GetPayItemCount(ctx context.Context, refTicketId, busDate string) bool {
	ticket, err := repo.DefaultPosRepo.QueryTicketById(ctx, refTicketId)
	if err != nil {
		logrus.Infof("GetCount 查询正单%s失败", refTicketId)
		return false
	} else {
		if ticket.BusDate == busDate {
			return true
		} else {
			return false
		}
	}
}

func sumTax(ticket *TicketInfo, taxMap map[string]TaxItem, shouldSum bool) (map[string]TaxItem, []TaxItem, float64) {
	if shouldSum {
		var taxsAmount float64
		var taxs []TaxItem
		for key, t := range taxMap {
			taxsAmount = utils.Add(2, taxsAmount, t.Amount)
			t.Count = t.Count - t.NegativeCount
			taxMap[key] = t
			taxs = append(taxs, t)
		}
		return taxMap, taxs, taxsAmount
	}
	for _, tax := range ticket.Taxs {
		taxItem, ok := taxMap[tax.Code]
		if !ok {
			taxItem = TaxItem{
				Code:          tax.Code,
				Name:          tax.Name,
				TotalAmount:   0,
				Amount:        0,
				NegativeCount: 0,
				Count:         0,
			}
		}

		//退单
		if ticket.Status == "REFUND" {
			taxItem.NegativeCount++
			taxItem.Amount = utils.Add(2, taxItem.Amount, -tax.Amount)
			taxItem.TotalAmount = utils.Add(2, taxItem.TotalAmount, -tax.Total)
		} else {
			taxItem.Count++
			taxItem.Amount = utils.Add(2, taxItem.Amount, tax.Amount)
			taxItem.TotalAmount = utils.Add(2, taxItem.TotalAmount, tax.Total)
		}
		taxMap[tax.Code] = taxItem
	}
	return taxMap, nil, 0
}

// 统计取消交易信息
func sumCancel(ctx context.Context, ticket TicketInfo, allTicketStatistics *commonStatistical) {
	if ticket.Status == TicketStatusRefund {
		allTicketStatistics.ChargebackCount++
		if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
			allTicketStatistics.Count--
		}
	} else if ticket.Status != TicketStatusPartialRefund {
		allTicketStatistics.Count++
	}

	if ticket.Status == TicketStatusPartialRefund {
		allTicketStatistics.ChargebackCount++
	}

	//营业额
	allTicketStatistics.BusinessAmount = utils.Add(2, allTicketStatistics.BusinessAmount, ticket.BusinessAmount)
	//实收金额
	allTicketStatistics.RealAmount = utils.Add(2, allTicketStatistics.RealAmount, ticket.GetRealAmount())

	//支出
	allTicketStatistics.ExpendAmount = utils.Add(2, allTicketStatistics.ExpendAmount, ticket.GetExpendAmount())
	//应付
	allTicketStatistics.Receivable = utils.Add(2, allTicketStatistics.Receivable, ticket.Receivable)

	//商品原价合计
	allTicketStatistics.GrossAmount = utils.Add(2, allTicketStatistics.GrossAmount, ticket.GrossAmount)

	//订单数
	allTicketStatistics.Trade++
	//allTicketStatistics.ID = append(allTicketStatistics.ID, ticket.ID)

}

func sumPayment(ctx context.Context, ticket TicketInfo, paymentsMap map[string]PayItem, overflowSum float64, overflowCount int) (map[string]PayItem, float64, int) {
	var chanName string
	if ticket.Source == TicketSourcePos {
		chanName = TicketSourcePos
	} else {
		chanName = GetChannelName(ticket.TpName)
	}

	// mt, ele,ele_isv, mt_isv 渠道
	if ChannelMap[ticket.TpName].IsPublicChannel {
		payItem, ok := paymentsMap[chanName]
		if !ok {
			payItem = PayItem{
				PaymentName:       chanName,
				PayAmount:         0,
				Count:             0,
				NegativeCount:     0,
				MerchantAllowance: 0,
				Sort:              GetChannelSort(ticket.TpName),
			}
		}
		payItem.PayAmount = utils.Add(2, payItem.PayAmount, ticket.GetRealAmount())
		if ticket.Status == TicketStatusRefund {
		} else if ticket.Status != TicketStatusPartialRefund {
			payItem.Count++
		}
		paymentsMap[chanName] = payItem
		return paymentsMap, overflowSum, overflowCount
	}

	if len(ticket.Payments) == 0 {
		payItem, ok := paymentsMap[chanName]
		if !ok {
			payItem = PayItem{
				PaymentName:       chanName,
				PayAmount:         0,
				Count:             0,
				NegativeCount:     0,
				MerchantAllowance: 0,
				Sort:              GetChannelSort(ticket.TpName),
			}
		}
		if ticket.Status == TicketStatusRefund {
			//if GetPayItemCount(d, ticket) {
			//	payItem.Count--
			//}
		} else if ticket.Status != TicketStatusPartialRefund {
			payItem.Count++
		}
		paymentsMap[chanName] = payItem

	} else {
		for _, payment := range ticket.Payments {
			var payName string
			if len(payment.Name) == 0 {
				payName = payment.TransName
			} else {
				payName = payment.Name
			}
			paymentName := fmt.Sprintf("%s-%s", chanName, payName)
			payItem, ok := paymentsMap[paymentName]
			if !ok {
				payItem = PayItem{
					PaymentName:       paymentName,
					PayAmount:         0,
					Count:             0,
					NegativeCount:     0,
					MerchantAllowance: 0,
					Sort:              GetChannelSort(ticket.TpName),
				}
			}
			payItem.PayAmount = utils.Add(2, payItem.PayAmount, utils.Add(2, payment.Cost, payment.TpAllowance))
			merchant_allowance := utils.Sub(2, payment.MerchantAllowance, payment.Overflow)
			payItem.MerchantAllowance = utils.Add(2, payItem.MerchantAllowance, utils.Add(2, merchant_allowance, payment.Rounding))

			if ticket.Status == TicketStatusRefund {
				//if GetPayItemCount(d, ticket) {
				//	payItem.Count--
				//}
			} else if ticket.Status != TicketStatusPartialRefund {
				payItem.Count++
			}
			paymentsMap[paymentName] = payItem
		}
		for _, promotion := range ticket.Promotions {
			var paymentName string
			if len(promotion.Name) == 0 {
				paymentName = chanName
			} else {
				paymentName = fmt.Sprintf("%s-%s", chanName, promotion.Name)
			}
			payItem, ok := paymentsMap[paymentName]
			if !ok {
				payItem = PayItem{
					PaymentName:       paymentName,
					PayAmount:         0,
					Count:             0,
					NegativeCount:     0,
					MerchantAllowance: 0,
					Sort:              GetChannelSort(ticket.TpName),
				}
			}
			payItem.PayAmount = utils.Add(2, payItem.PayAmount, utils.Add(2, promotion.Cost, promotion.TpAllowance))
			//payItem.MerchantAllowance = util.Add(2, payItem.MerchantAllowance, util.Sub(2, payment.MerchantAllowance, payment.Overflow))

			if ticket.Status == TicketStatusRefund {
				//if GetPayItemCount(d, ticket) {
				//	payItem.Count--
				//}
			} else if ticket.Status != TicketStatusPartialRefund {
				payItem.Count++
			}
			paymentsMap[paymentName] = payItem
		}
	}

	if ticket.Source == TicketSourcePos {
		payItem1, ok := paymentsMap["POS-溢收"]
		if !ok {
			payItem1 = PayItem{
				PaymentName:       "POS-溢收",
				PayAmount:         0,
				Count:             0,
				NegativeCount:     0,
				MerchantAllowance: 0,
			}
		}
		payItem1.MerchantAllowance = utils.Add(2, payItem1.MerchantAllowance, ticket.OverflowAmount)
		if ticket.Status == "REFUND" {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				if ticket.OverflowAmount != 0 {
					payItem1.Count--
				}
			}
		} else {
			if ticket.OverflowAmount != 0 {
				payItem1.Count++
			}
		}
		paymentsMap["POS-溢收"] = payItem1

	} else {
		name := GetChannelName(ticket.TpName)
		payMentName := name + "-溢收"
		payItem1, ok := paymentsMap[payMentName]
		if !ok {
			payItem1 = PayItem{
				PaymentName:       payMentName,
				PayAmount:         0,
				Count:             0,
				NegativeCount:     0,
				MerchantAllowance: 0,
			}
		}
		payItem1.MerchantAllowance = utils.Add(2, payItem1.MerchantAllowance, ticket.OverflowAmount)
		if ticket.Status == "REFUND" {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				if ticket.OverflowAmount != 0 {
					payItem1.Count--
				}
			}
		} else {
			if ticket.OverflowAmount != 0 {
				payItem1.Count++
			}
		}
		paymentsMap[payMentName] = payItem1
	}
	return paymentsMap, overflowSum, overflowCount
}

// 日结报表需要加上不同渠道数据统计
func extendReport(ctx context.Context, chName string, ticket TicketInfo, saleProduct map[string]ProductInfo, channelMap map[string]channels) (map[string]channels, map[string]ProductInfo) {
	chInfo, ok := channelMap[chName]
	if !ok {
		chInfo = channels{}
	}
	var channelName string
	if chName == ticket.TpName {
		channelName = GetChannelName(ticket.TpName)
	}
	sale := chInfo.Sale
	if ticket.Status == TicketStatusRefund {
		if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
			sale.Trade = sale.Trade - 1
		}
	} else if ticket.Status != TicketStatusPartialRefund {
		sale.Trade = sale.Trade + 1
	}

	// 交易量
	if saleProduct != nil {
		for _, t := range ticket.Products {
			sale.Quantity += t.Quantity
			product, ok := saleProduct[t.ID]
			if ok {
				product.Quantity += t.Quantity
				saleProduct[t.ID] = product
			} else {
				product := ProductInfo{
					ID:       t.ID,
					Name:     t.Name,
					Quantity: t.Quantity,
				}
				saleProduct[t.ID] = product
			}
		}
	} else {
		for _, t := range ticket.Products {
			sale.Quantity += t.Quantity
		}
	}
	sale.Amount = utils.Add(2, sale.Amount, ticket.NetAmt)
	sale.BusinessAmount = utils.Add(2, sale.BusinessAmount, ticket.BusinessAmount) // 交易额
	sale.RealAmount = utils.Add(2, sale.RealAmount, ticket.GetRealAmount())        // 交易额
	chInfo.Sale = sale
	chInfo.Name = channelName

	//if ticket.Status == common.TicketStatusSale || ticket.Status == common.TicketStatusRefundComplete { // 包括退单，销售单, 统计今天发生的所有交易
	//	sale := chInfo.Sale
	//	sale.Trade = sale.Trade + 1                                     // 交易量
	//	sale.Quantity = len(ticket.Products) + sale.Quantity            // 商品数量
	//	sale.Amount = util.Add(2, sale.Amount, math.Abs(ticket.NetAmt)) // 交易净额
	//	chInfo.Sale = sale
	//}
	if ticket.Status == TicketStatusRefund || ticket.Status == TicketStatusPartialRefund { // 退单
		refund := chInfo.Refund
		refund.Trade = refund.Trade + 1
		for _, t := range ticket.Products {
			refund.Quantity += t.Quantity
		}
		refund.Amount = utils.Add(2, refund.Amount, ticket.NetAmt)
		refund.ID = append(refund.ID, ticket.ID)
		chInfo.Refund = refund
	} else {
		sale.ID = append(sale.ID, ticket.ID)
	}
	//update map
	channelMap[chName] = chInfo
	return channelMap, saleProduct
}

func calChannel(grossAmount float64, channelMap map[string]channels) (map[string]interface{}, []interface{}) {
	// 渠道统计
	// 外卖单子 合并统计
	var takeOut channel
	var selfHelp channel
	var pos channel
	var sum channel
	delete(channelMap, strings.ToLower(INVALIDOrder))
	for k, v := range channelMap {
		all := channel{
			Quantity:       v.Sale.Quantity,
			Trade:          v.Sale.Trade,
			Amount:         v.Sale.Amount,
			BusinessAmount: v.Sale.BusinessAmount,
			RealAmount:     v.Sale.RealAmount,
		}
		v.Total = all // 最后的纯收入
		channelMap[k] = v
	}
	data := make(map[string]interface{})
	for k, v := range channelMap {
		if strings.HasPrefix(k, strings.ToLower(TakeOutOrder)) {
			takeOut = channel{
				Quantity:       v.Total.Quantity + takeOut.Quantity,
				Amount:         utils.Add(2, v.Total.Amount, takeOut.Amount),
				Trade:          v.Total.Trade + takeOut.Trade,
				BusinessAmount: utils.Add(2, v.Total.BusinessAmount, takeOut.BusinessAmount),
				RealAmount:     utils.Add(2, v.Total.RealAmount, takeOut.RealAmount),
			}

		} else if strings.HasPrefix(k, strings.ToLower(SelfHelpOrder)) {
			// 自取
			selfHelp = channel{
				Quantity:       v.Total.Quantity + selfHelp.Quantity,
				Amount:         utils.Add(2, v.Total.Amount, selfHelp.Amount),
				Trade:          v.Total.Trade + selfHelp.Trade,
				BusinessAmount: utils.Add(2, v.Total.BusinessAmount, selfHelp.BusinessAmount),
				RealAmount:     utils.Add(2, v.Total.RealAmount, selfHelp.RealAmount),
			}
		} else if strings.HasPrefix(k, "pos") {
			// pos
			pos = channel{
				Quantity:       v.Total.Quantity + pos.Quantity,
				Amount:         utils.Add(2, v.Total.Amount, pos.Amount),
				Trade:          v.Total.Trade + pos.Trade,
				BusinessAmount: utils.Add(2, v.Total.BusinessAmount, pos.BusinessAmount),
				RealAmount:     utils.Add(2, v.Total.RealAmount, pos.RealAmount),
			}
		} else {
			data[k] = v
		}
		if len(v.Name) == 0 {
			sum = channel{
				Quantity:       v.Total.Quantity + sum.Quantity,
				Amount:         utils.Add(2, v.Total.Amount, sum.Amount),
				Trade:          v.Total.Trade + sum.Trade,
				BusinessAmount: utils.Add(2, v.Total.BusinessAmount, sum.BusinessAmount),
				RealAmount:     utils.Add(2, v.Total.RealAmount, sum.RealAmount)}
		}
	}

	// 每笔均价（每一笔交易的均价）：总销售净额/总销售净单
	//priceTrade := util.Divide(2, sum.Amount, float64(sum.Trade))
	// 每项均价（每项商品的均价）：总销售净额/总销售净项
	priceQuantity := utils.Divide(2, grossAmount, float64(sum.Quantity))
	result := make(map[string]interface{})
	result["data"] = data
	result["pos"] = pos
	result[strings.ToLower(TakeOutOrder)] = takeOut
	result[strings.ToLower(SelfHelpOrder)] = selfHelp
	result["total"] = sum
	//result["price_trade"] = priceTrade
	result["price_quantity"] = priceQuantity

	// 合并到同一个dict 中
	for k, v := range channelMap {
		result[k] = v
	}
	var channelNew []interface{}
	for _, v := range data {
		channelNew = append(channelNew, v)
	}
	return result, channelNew
}

func appendDaily(ticketCounts, people int, allTicketStatistics commonStatistical) []TicketStatistic {
	//获取所有的支付信息
	var ticketStatistics []TicketStatistic
	amountStatistic := TicketStatistic{
		Name:  utils.GetI18nLabel("CountTicket"),
		Value: float64(ticketCounts),
	}
	peopleStatistic := TicketStatistic{
		Name:  utils.GetI18nLabel("TotalNumberOfPeopleDining"),
		Value: float64(people),
	}
	allMoneyStatistic := TicketStatistic{
		Name:  utils.GetI18nLabel("TotalCateringRevenue"),
		Value: allTicketStatistics.Amount,
	}
	ticketStatistics = append(ticketStatistics, amountStatistic, peopleStatistic, allMoneyStatistic)
	return ticketStatistics
}

func sumPromotions1(promotionsMap map[string]PromotionItem) float64 {
	var promotionsAmount float64
	for k, v := range promotionsMap {
		if k != "服务费" {
			promotionsAmount = utils.Add(2, promotionsAmount, 0-v.Discount)
		}
	}
	return promotionsAmount
}

func sumMealSegmentNameChannel(ctx context.Context, ticket TicketInfo, mealSegmentNameChannel map[string]MealSegmentNameChannel) map[string]MealSegmentNameChannel {
	tmp, ok := mealSegmentNameChannel[ticket.MealSegmentName]
	if !ok {
		count := 0
		if ticket.Status == TicketStatusRefund {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				count = -1
			}
		} else if ticket.Status != TicketStatusPartialRefund {
			count = 1
		}
		tmp := MealSegmentNameChannel{
			MealSegmentName: ticket.MealSegmentName,
			Amount:          ticket.Amount.BusinessAmount,
			Count:           count,
		}
		mealSegmentNameChannel[ticket.MealSegmentName] = tmp
	} else {
		if ticket.Status == TicketStatusRefund {
			if GetPayItemCount(ctx, ticket.RefTicketId, ticket.BusDate) {
				tmp.Count += -1
			}
		} else if ticket.Status != TicketStatusPartialRefund {
			tmp.Count += 1
		}
		tmp.Amount = utils.Add(2, tmp.Amount, ticket.Amount.BusinessAmount)
		mealSegmentNameChannel[ticket.MealSegmentName] = tmp
	}
	return mealSegmentNameChannel
}
