package pos

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"sort"
)

func OrderTypeReport(ctx context.Context, req *sales_report.OrderTypeRequest) (*sales_report.OrderTypeResponse, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	rsp, err := repo.DefaultNewPosRepo.PosOrderTypeReport(ctx, req.StartTime, req.EndTime, storeId, partnerID)
	if err != nil {
		return nil, err
	}
	mealPeriod, err := getMealPeriod(ctx)
	if err != nil {
		return nil, err
	}
	// 定义餐段排序用的顺序映射
	mealPeriodMap := make(map[string]struct {
		Sort   int
		Period []string
	})
	for index, v := range mealPeriod.Data {
		mealPeriodMap[v.Name] = struct {
			Sort   int
			Period []string
		}{Sort: index, Period: v.Period}
	}

	orderTypeMapSort := map[string]int{
		DINEIN:   1, //堂食
		TAKEAWAY: 2, //外带
		TAKEOUT:  3, //外卖
		SELFHELP: 4, //自提
	}

	// 对total的用餐方式排序
	sort.Slice(rsp.Total.TypePeriodList, func(i, j int) bool {
		return orderTypeMapSort[rsp.Total.TypePeriodList[i].OrderType] < orderTypeMapSort[rsp.Total.TypePeriodList[j].OrderType]
	})

	// 对period的餐段信息赋值，并对餐段和用餐方式排序
	sort.Slice(rsp.Period, func(i, j int) bool {
		return mealPeriodMap[rsp.Period[i].MealName].Sort < mealPeriodMap[rsp.Period[j].MealName].Sort
	})

	for _, period := range rsp.Period {
		period.Period = mealPeriodMap[period.MealName].Period
		sort.Slice(period.TypePeriodList, func(i, j int) bool {
			return orderTypeMapSort[period.TypePeriodList[i].OrderType] < orderTypeMapSort[period.TypePeriodList[j].OrderType]
		})
	}

	bufData, err := json.Marshal(&rsp)
	if err != nil {
		return nil, err
	}
	return &sales_report.OrderTypeResponse{
		Extend: string(bufData),
	}, nil
}
