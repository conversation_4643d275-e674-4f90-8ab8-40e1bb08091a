package pos

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"sort"
)

func PosDepartmentBusinessReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.PosDepartmentBusinessResponse, error) {
	partnerID := cast.ToInt64(ctx.Value(oauth.PartnerID))
	storeId := pos.GetStoreId(ctx)
	row, err := repo.DefaultNewPosRepo.PosDepartmentBusinessReport(ctx, req.StartTime, req.EndTime, storeId, partnerID)
	if err != nil {
		return nil, err
	}

	categoryIds := make([]int64, 0)

	for _, p := range row.DepartmentList {
		categoryIds = append(categoryIds, p.Id)
	}

	lan := pos.GetLang(ctx)
	query := &model.CommonRequest{Lan: lan}
	categoryMaps := report.GetCategoryMapsByIds(ctx, query, categoryIds)

	for _, p := range row.DepartmentList {
		p.Name = cast.ToString(categoryMaps[p.Id]["name"])
	}
	// 商品明细排序
	sort.Slice(row.DepartmentList, func(i, j int) bool {
		return row.DepartmentList[i].Name < row.DepartmentList[j].Name
	})

	rsp := sales_report.PosDepartmentBusinessResponse{}
	marshal, err := json.Marshal(row)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(marshal, &rsp)
	if err != nil {
		return nil, err
	}

	return &rsp, nil
}
