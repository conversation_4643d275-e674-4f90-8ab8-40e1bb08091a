package pos

import (
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

/***
1.需要统计3个不同的渠道：
外卖（小程序和美团，订单和退单都需要显示-后面用正负单简称）

自取（指小程序点单自取，需要显示正负单）

pos点单（分为堂食和外带，需要显示正负单）
2.每个渠道下面都会有一个小计：
渠道销售净单（渠道总单数-渠道退单数）

渠道销售净项（渠道销售的总商品项数-渠道退单的商品项数）

渠道销售净额（渠道总交易金额额-渠道退单金额）


字段说明：
# POS
pos_dinein: 堂食
pos_takeway: 外带

# 自取
selfhelp_wechat: 小程序自取


#外卖：
takeout_wechat :小程序
takeout_meituan：美团
takeout_eleme :饿了吗
takeout_koubei :口碑
***/

type channel struct {
	Quantity       int      `json:"quantity"`        // 该时间段的商品数量
	BusinessAmount float64  `json:"business_amount"` //营业额
	RealAmount     float64  `json:"real_amount"`     //实收金额
	Amount         float64  `json:"amount"`
	Trade          int      `json:"trade"` // 订单数量， 交易量
	ID             []string `json:"id"`
}

type channels struct {
	Total  channel `json:"total"`  // 销售单
	Sale   channel `json:"sale"`   // 销售单
	Refund channel `json:"refund"` // 退货单
	Name   string  `json:"name"`
}

// 转换为日结信息中的ProductItem
type ProductInfo struct {
	ID    string  `json:"id"`
	Name  string  `json:"name"`
	Price float64 `json:"price"`
	//数量
	Quantity int `json:"qty"`
}

type PromotionInfo struct {
	Name        string  `json:"name"`
	Type        string  `json:"type"`
	Discount    float64 `json:"discount"`
	Code        string  `json:"code"`
	Order       int     `json:"order"`
	Cost        float64 `json:"cost"`         //(财务)用户实际购买金额
	TpAllowance float64 `json:"tp_allowance"` //(财务)第三方补贴金额

}

type TaxInfo struct {
	Code   string  `json:"code"`
	Total  float64 `json:"subTotal"`
	Name   string  `json:"name"`
	Amount float64 `json:"taxAmount"`
}

// amt: abbr for amount
type TicketInfo struct {
	ID                    string                  `json:"id"`
	RefTicketId           string                  `json:"ref_ticket_id"`
	OperatorCode          string                  `json:"operatorCode"`
	OperatorName          string                  `json:"operatorName"`
	RemovezeroAmt         float64                 `json:"removezero_amount"`
	IsUpload              bool                    `json:"isUpload"`
	NetAmt                float64                 `json:"net_amount"`
	People                int                     `json:"people"`
	Tax                   float64                 `json:"tax"`
	Amount                model.TicketAmounts     `json:"amounts"`
	TakeawayInfo          model.TicketTakeaway    `json:"takeaway_info"`
	Products              []ProductInfo           `json:"products"`
	Payments              []model.TicketPayment   `json:"payments"`
	Promotions            []PromotionInfo         `json:"promotions"`
	TicketPromotions      []model.TicketPromotion `json:"ticket_promotions"`
	Status                string                  `json:"status"`
	BusinessAmount        float64                 `json:"business_amount"` //营业额
	RealAmount            float64                 `json:"real_amount"`     //实收金额
	ExpendAmount          float64                 `json:"expend_amount"`   //支出
	Receivable            float64                 `json:"receivable"`      //应付金额/
	BusDate               string                  `json:"bus_date" storm:"index"`
	Taxs                  []TaxInfo               `json:"taxList"`
	RefundId              string                  `json:"refund_id"`
	Rouding               float64                 `json:"rouding"`
	OverflowAmount        float64                 `json:"overflow"`
	Channel               string                  `json:"channel"`
	DiscountAmount        float64                 `json:"discount_amount"`
	Source                string
	TpName                string  `json:"tpName"`
	MerchantSendFee       float64 `json:"merchant_send_fee"`       //商家承担配送费
	GrossAmount           float64 `json:"gross_amount"`            //商品原价合计
	PayMerchantContribute float64 `json:"pay_merchant_modelibute"` //财务)实付商家出资（补贴）
	MealSegmentName       string  `json:"mealSegmentName"`         //餐段名称
}

func (t TicketInfo) GetRealAmount() float64 {
	return utils.Sub(2, t.BusinessAmount, t.GetExpendAmount())
}

func (t TicketInfo) GetExpendAmount() float64 {
	sum1 := utils.Add(2, t.Amount.DiscountMerchantContribute, t.Amount.Commission)
	sum2 := utils.Add(2, sum1, t.Amount.OtherFee)
	sum3 := utils.Add(2, sum2, t.TakeawayInfo.MerchantSendFee)
	sum4 := utils.Add(2, sum3, t.Amount.PayMerchantContribute)
	sum5 := utils.Add(2, sum4, t.Amount.Rounding)
	return utils.Sub(2, sum5, t.Amount.OverflowAmount)
}

type PayItems []PayItem

func (p PayItems) Len() int {
	return len(p)
}

func (p PayItems) Less(i, j int) bool {
	if p[j].Sort == p[i].Sort {
		return p[j].PaymentName > p[i].PaymentName
	}
	return p[j].Sort > p[i].Sort
}

func (p PayItems) Swap(i, j int) {
	p[j], p[i] = p[i], p[j]
}

type storeDailyInfo struct {
	Id       string `json:"id"`
	Code     string `json:"code"`
	Name     string `json:"name"`
	Address  string `json:"address"`
	SaleTime string `json:"saleTime"`
	PosId    string `json:"posId"`
	PosCode  string `json:"posCode"`
	BusDate  string `json:"busDate"`
}

type userInfo struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

// 日结中的支付统计
type PayItem struct {
	PayAmount         float64 `json:"pay_amount"`
	Count             int     `json:"count"`
	PaymentName       string  `json:"payment_name"`
	NegativeCount     int     `json:"negativeCount"`
	Sort              int     `json:"sort"`
	TransCode         string  `json:"trans_code"`         // 支付方式编码
	MerchantAllowance float64 `json:"merchant_allowance"` //(财务)商家补贴金额
}

// 日结小票的统计信息
type TicketStatistic struct {
	Name  string  `json:"name"`
	Value float64 `json:"value"`
}

type PayioInfo struct {
	PayIn  float64 `json:"payin"`
	PayOut float64 `json:"payout"`
	PayioInfoNew
}

type PayioInfoNew struct {
	PayInNew  []map[string]interface{} `json:"payinNew"`
	PayOutNew []map[string]interface{} `json:"payoutNew"`
	Sum       float64                  `json:"sum"`
}

// 日结中的促销统计
type PromotionItem struct {
	Type     string  `json:"type"`
	Discount float64 `json:"discount"`
	Count    int     `json:"count"`
	Sort     int     `json:"sort"`
}

type Item struct {
	Discount   float64 `json:"discount"`    //优惠
	RealAmount float64 `json:"real_amount"` //商家实收
	Sum        float64 `json:"sum"`         //总金额
	Count      int     `json:"count"`
}

// PromotionChannel  渠道统计
type PromotionChannel struct {
	Code             string `json:"code"`
	Type             string `json:"type"`
	Name             string `json:"name"`
	Source           string `json:"source"`
	IsProduct        bool   `json:"is_product"`
	PromotionCoupons Item   `json:"promotion_coupons"`
	PromotionProduct Item   `json:"promotion_product"`
}

type TaxItem struct {
	Code          string  `json:"code"`
	Name          string  `json:"name"`
	TotalAmount   float64 `json:"totalAmount"`
	Amount        float64 `json:"taxAmount"`
	Count         int     `json:"count"`
	NegativeCount int     `json:"negativeCount"`
}

// 收银员销售汇总
type UserSaleItem struct {
	Code        string  `json:"code"`
	TotalAmount float64 `json:"totalAmount"`
	Name        string  `json:"name"`
}

// 通用统计信息
type commonStatistical struct {
	BusinessAmount  float64  `json:"business_amount"`  //营业额
	RealAmount      float64  `json:"real_amount"`      //实收金额
	ExpendAmount    float64  `json:"expend_amount"`    //支出金额
	Receivable      float64  `json:"receivable"`       //应付金额/
	GrossAmount     float64  `json:"gross_amount"`     //商品原价合计
	Count           int      `json:"count"`            //有效单数
	Trade           int      `json:"trade"`            // 订单数量， 交易量
	ChargebackCount int      `json:"chargeback_count"` //退单数
	PriceTrade      float64  `json:"price_trade"`      //单均价
	PriceQuantity   float64  `json:"price_quantity"`   //每项均价
	Amount          float64  `json:"amount"`           //甩尾金额
	ID              []string `json:"id"`
}

// extend字段中的具体结构
type DailyKnotInfo struct {
	Store    storeDailyInfo `json:"store"`
	UserInfo userInfo       `json:"userInfo"`
	//Items    []ProductItem  `json:"items"`
	Pays        []PayItem `json:"pays"`
	PayOverflow []PayItem `json:"pay_overflow"`
	//线上实收汇总
	OnlinePays []PayItem `json:"onlinePays"`
	//线下实收汇总
	OfflinePays []PayItem `json:"offlinePays"`
	//线下找零汇总
	Change []PayItem `json:"change"`
	//小票单数
	Count int64 `json:"count"`
	//小票统计信息
	Tickets []TicketStatistic `json:"tickets"`
	//小票
	SumOverflow float64   `json:"sumOverflow"`
	UnUpload    int       `json:"unupload"`
	Payinfo     PayioInfo `json:"payInfo"`
	//PayinfoNew  PayioInfoNew    `json:"payInfoNew"`
	Promotion            []PromotionItem             `json:"promotions"`
	PosPromotion         []PromotionItem             `json:"pos_promotion"` // 门店促销统计
	PromotionChannel     []PromotionChannel          `json:"promotion_channel"`
	PromotionsChannelMap map[string]PromotionChannel `json:"promotions_channel_map"`
	Tax                  []TaxItem                   `json:"taxs"`
	UserItems            []UserSaleItem              `json:"userItems"`
	//折扣统计
	DiscountDealsStatistics commonStatistical `json:"discountDealsStatistics"`
	//销售统计
	AllTicketStatistics commonStatistical `json:"allTicketStatistics"`
	//抹零统计
	RemoveZeroStatistics commonStatistical `json:"removeZeroStatistics"`
	//甩尾统计
	RoundingStatistics commonStatistical `json:"roundingStatistics"`
	OverflowStatistics commonStatistical `json:"overflowStatistics"`
	//取消订单统计
	CancelTicketStatistics commonStatistical `json:"cancelTicketStatistics"`
	//营收差异
	//Diff float64 `json:"diff"`
	//销售净额
	NetTrade float64 `json:"netTrade"`
	//税总计
	TaxTotalAmount float64 `json:"taxTotalAmount"`
	//支付小记
	PaysAmount                float64 `json:"paysAmount"`
	PayCount                  int     `json:"pay_count"`
	PayMerchantAllowance      float64 `json:"pay_merchant_allowance"`
	OverflowAmount            float64 `json:"overflow_amount"`
	OverflowCount             int     `json:"overflow_count"`
	OverflowMerchantAllowance float64 `json:"overflow_merchant_allowance"`

	OfflinePaysAmount   float64                  `json:"offlinePaysAmount"`
	OnlinePaysAmount    float64                  `json:"onlinePaysAmount"`
	ChangeAmount        float64                  `json:"changeAmount"`
	Channels            map[string]interface{}   `json:"channels"`
	ChannelsNew         []interface{}            `json:"channels_new"`
	IsToday             bool                     `json:"isToday,omitempty"`
	IsSummary           bool                     `json:"is_summary"`
	CashRefundAmount    float64                  `json:"cash_refund_amount"` //非原单退款
	CashRefundCount     int                      `json:"cash_refund_count"`  //非原单退款数量
	FrmLossAmount       float64                  `json:"frm_loss_amount"`    //门店报损金额
	FrmLossCount        float64                  `json:"frm_loss_count"`     //门店报损数量
	MealSegmentNameList []MealSegmentNameChannel `json:"meal_segment_name_list"`
}

type ticketStatistic struct {
	StartTime          string    `json:"startTime"`
	EndTime            string    `json:"endTime"`
	DeviceIdentityCode string    `json:"deviceIdentityCode"`
	Pays               []PayItem `json:"pays"`
	PayCount           int       `json:"pay_count"`
	PayOverflow        []PayItem `json:"pay_overflow"`
	//线上实收汇总
	OnlinePays []PayItem `json:"onlinePays"`
	//线下实收汇总
	OfflinePays []PayItem `json:"offlinePays"`
	//线下找零汇总
	Change []PayItem `json:"change"`
	//小票单数
	Count int64 `json:"count"`
	//小票
	SumOverflow float64         `json:"sumOverflow"`
	UnUpload    bool            `json:"unupload"`
	Payinfo     PayioInfo       `json:"payInfo"`
	Promotion   []PromotionItem `json:"promotions"`
	Tax         []TaxItem       `json:"taxs"`
	//折扣统计
	DiscountDealsStatistics commonStatistical `json:"discountDealsStatistics"`
	//销售统计
	AllTicketStatistics commonStatistical `json:"allTicketStatistics"`
	//抹零统计
	RemoveZeroStatistics commonStatistical `json:"removeZeroStatistics"`
	//取消订单统计
	CancelTicketStatistics commonStatistical `json:"cancelTicketStatistics"`
	//甩尾统计
	RoundingStatistics commonStatistical `json:"roundingStatistics"`
	OverflowStatistics commonStatistical `json:"overflowStatistics"`
	//营收差异
	Diff float64 `json:"diff"`
	//销售净额
	NetTrade float64 `json:"netTrade"`
	//税总计
	TaxTotalAmount float64 `json:"taxTotalAmount"`
	//支付小记
	PaysAmount                float64 `json:"paysAmount"`
	OfflinePaysAmount         float64 `json:"offlinePaysAmount"`
	OnlinePaysAmount          float64 `json:"onlinePaysAmount"`
	ChangeAmount              float64 `json:"changeAmount"`
	PromotionsAmount          float64 `json:"promotionsAmount"`
	PayMerchantAllowance      float64 `json:"pay_merchant_allowance"`
	OverflowAmount            float64 `json:"overflow_amount"`
	OverflowCount             int     `json:"overflow_count"`
	OverflowMerchantAllowance float64 `json:"overflow_merchant_allowance"`
	CashRefundAmount          float64 `json:"cash_refund_amount"` //非原单退款
	CashRefundCount           int     `json:"cash_refund_count"`  //非原单退款数量
	FrmLossAmount             float64 `json:"frm_loss_amount"`    //门店报损金额
	FrmLossCount              float64 `json:"frm_loss_count"`     //门店报损数量
}

type MealSegmentNameChannel struct {
	MealSegmentName string  `json:"mealSegmentName"`
	Amount          float64 `json:"amount"`
	Count           int     `json:"count"`
}

type MealPeriod struct {
	Data []struct {
		Id     int      `json:"id"`
		Name   string   `json:"name"`
		Period []string `json:"period"`
	} `json:"data"`
}

type PromotionItems []PromotionItem

func (p PromotionItems) Len() int {
	return len(p)
}

func (p PromotionItems) Less(i, j int) bool {
	if p[j].Sort == p[i].Sort {
		return p[j].Type > p[i].Type
	}
	return p[j].Sort > p[i].Sort
}

func (p PromotionItems) Swap(i, j int) {
	p[j], p[i] = p[i], p[j]
}
