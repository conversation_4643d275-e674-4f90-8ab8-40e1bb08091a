package pos

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/omnichannel"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// Category 商品分类
type Category struct {
	Code          string          `json:"code"` // 商品类目code
	Created       string          `json:"created"`
	CreatedBy     string          `json:"created_by"`
	ID            string          `json:"id"` // 商品id
	Name          string          `json:"name"`
	Relation      json.RawMessage `json:"relation"`
	RequestID     interface{}     `json:"request_id"`
	Status        string          `json:"status"`
	Updated       string          `json:"updated"`
	UpdatedBy     string          `json:"updated_by"`
	ParentCode    string          `json:"parent_code"`
	ParentID      string          `json:"parent_id"`
	Parent        *Category       `json:"parent"`
	Localizations json.RawMessage `json:"localizations"`
	State         string          `json:"state"`
}

// 主档获取商品分类
func getProductCategories(ctx context.Context) ([]*Category, error) {
	var productC []*Category
	productCategory, err := entity.GetProductCategory(ctx)
	if err != nil {
		logrus.Errorf("getProductCategories err:%v", err)
		return nil, err
	}
	logrus.Infof("getProductCategories data1:%v", productCategory)
	data, err := json.Marshal(&productCategory)
	if err != nil {
		logrus.Errorf("getProductCategories Marshal err:%v", err)
		return nil, err
	}
	err = json.Unmarshal(data, &productC)
	if err != nil {
		return nil, errors.Wrap(err, "getProductCategories")
	}
	logrus.Infof("getProductCategories data:%v", string(data))
	return productC, nil
}

const productPath = "/api/omnichannel/channel/product/list"

type ProductListRequest struct {
	StoreId string `json:"storeId"`
	Channel string `json:"channel"`
}

type OmnichannelHostReponse struct {
	StatusCode int             `json:"status_code"`
	Code       string          `json:"code"`
	Message    string          `json:"message"`
	Payload    json.RawMessage `json:"payload"`
}

func OmnichannelNewRequest(ctx context.Context, url string, method string, body interface{}, payload interface{}) error {
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return err
	}
	logrus.Infof("Url:%s request:%s", url, bodyBytes)
	req, err := http.NewRequest(method, url, bytes.NewReader(bodyBytes))
	if err != nil {
		logrus.Errorf("url:%v err:%v", url, err)
		return err
	}
	storeId := pos.GetStoreId(ctx)
	traceId := utils.GetUUId()
	// 去除uuid的-
	traceId = strings.Replace(traceId, "-", "", -1)
	partnerID := cast.ToString(ctx.Value(oauth.PartnerID))
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Partner_Id", partnerID)
	req.Header.Add("User_Id", "1")
	req.Header.Add("Store-Id", cast.ToString(storeId))
	req.Header.Add("App-Name", "salesReport")
	req.Header.Add("Trace-Id", traceId)
	req.Header.Add("Authorization", pos.GetToken(ctx))
	begin := time.Now()
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	var resp *http.Response
	err = tracing.ModuleTracing(ctx, "http-call:"+url, func(ctx2 context.Context) error {
		resp, err = client.Do(req)
		return err
	})

	if err != nil {
		logrus.Errorf("hostClient do url:%v err:%v", url, err)
		return err
	}
	logrus.Infof("请求耗时: %s %v, trace-id:%s", url, time.Since(begin), traceId)
	defer resp.Body.Close()
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("can't read response,url:%s,trace-id:%s err:%v", url, traceId, err)
		return err
	}
	//url 包含channel/product/list 就不打印
	logrus.Infof("Url:%s, code:%d, response:%s", url, resp.StatusCode, respBody)

	var res OmnichannelHostReponse
	if err = json.Unmarshal(respBody, &res); err != nil {
		logrus.Errorf("json.Unmarshal %s to HostResponse failed:%s,trace-id:%s err:%v", respBody, url, traceId, err)
		return err
	}

	if res.StatusCode != 0 {
		logrus.Errorf("response status_code:%d,非0,desc:%s,payload:%s, url:%s，rawMessage:%s,trace-id%s", res.StatusCode,
			res.Message, res.Payload, url, respBody, traceId)
		return errors.New(res.Message)

	}
	if payload == nil {
		return nil
	}
	// 解析返回值
	if err := json.Unmarshal(res.Payload, payload); err != nil {
		logrus.Errorf("json.Unmarshal 解析payload 失败 %s,url:%s,rawMessage:%s,trace-id:%s err:%v",
			res.Payload, url, respBody, traceId, err)
		return err
	}
	return nil
}

func getMenu(ctx context.Context, host string) ([]*omnichannel.Category, error) {
	url := host + productPath
	storeId := pos.GetStoreId(ctx)
	body := ProductListRequest{
		StoreId: cast.ToString(storeId),
		Channel: "POS",
	}
	var rsp interface{}
	err := OmnichannelNewRequest(ctx, url, http.MethodPost, body, &rsp)
	if err != nil {
		logrus.Errorf("getMenu OmnichannelNewRequest err:%v", err)
		return nil, err
	}
	var payloads []omnichannel.Payload
	data, err := json.Marshal(&rsp)
	if err != nil {
		logrus.Errorf("DownloadProductsFromOmnichannel Marshal err %v", err)
		return nil, err
	}
	err = json.Unmarshal(data, &payloads)
	if err != nil {
		logrus.Errorf("DownloadProductsFromOmnichannel Unmarshal err %v", err)
		return nil, err
	}
	payload := payloads[0]
	category := payload.Category
	logrus.Infof("获取到菜单:%v", len(category))
	return category, nil
}
