package pos

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	sales_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"gitlab.hexcloud.cn/histore/sales-report/model/omnichannel"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

const allTickets = "all"
const menuTickets = "menu"
const productTickets = "product"

func PosCategoryReport(ctx context.Context, req *sales_report.HubReportQuery) (*sales_report.CategoryReportResponse, error) {
	logrus.Infof("PosCategoryReport get req：%v", req)
	if len(req.Host) == 0 {
		return categoryReport(ctx, req)
	}
	if len(req.Query) == 0 {
		return nil, errors.New("PosCategoryReport:queryTickets:query is empty")
	}
	tickets, err := queryTickets(ctx, req)
	if err != nil {
		return nil, err
	}
	categories, err := getProductCategories(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "PosCategoryReport:loadProductCategories")
	}
	var (
		menus []*omnichannel.Category
	)
	if req.Query[0] != allTickets {
		menus, err = getMenu(ctx, req.Host)
		if err != nil {
			logrus.Errorf("PosCategoryReport:getMenu err:%v", err)
			if len(req.Channel) != 0 {
				return nil, errors.Wrap(err, "PosCategoryReport:getMenu")
			}
		}
	}
	dataMap := make(map[string]map[string]*Node) //	// 归类统计商品
	for _, t := range tickets {
		if t.Status == TicketStatusRefundComplete {
			t.Status = TicketStatusSale
		}

		if t.Status == TicketStatusPartialRefund {
			t.Status = TicketStatusRefund
		}

		if t.Status == StatusSuccessFul {
			t.Status = TicketStatusSale
		}

		// 这个做个处理把加料和商品都统一放到products 中
		temp, ok4 := dataMap[t.Status.String()] // 销售订单的商品统计
		if !ok4 {
			temp = make(map[string]*Node)
		}
		temp, err = loadProducts(ctx, t, t.Products, temp)
		if err != nil {
			return nil, errors.Wrap(err, utils.GetI18nLabel("CategoryReport"))
		}

		dataMap[t.Status.String()] = temp // 填充原来的值
	}
	// 构建数据： 变成树结构
	productSaleMap := dataMap[TicketStatusSale]     // 1. 销售单
	productRefundMap := dataMap[TicketStatusRefund] // 退货单
	// 汇总数据
	all := make(map[string]*Node)
	for k, v := range productSaleMap {
		all[k] = copyNode(v) // pointer 类型，必须要拷贝出来
	}
	for k, v := range productRefundMap {
		exist, ok := all[k]
		if ok {
			exist.SumAmount = utils.Add(2, exist.SumAmount, v.SumAmount)
			exist.Quantity = exist.Quantity - v.Quantity
		} else {
			all[k] = copyNode(v)
		}
	}

	var (
		allTree     []*Node
		allQuantity int
		allAmount   float64
	)
	if req.Query[0] == allTickets {
		allTree, allQuantity, allAmount = getTreeList(all, categories)
	} else if req.Query[0] == menuTickets {
		all, chennelMenu := channelMenu2Category(all, menus, req.Channel)
		allTree, allQuantity, allAmount = getTreeList(all, chennelMenu)
		allTree = MenuAllTree(allTree)
	} else {
		allTree, allQuantity, allAmount = getProductTreeList(all, menus, req.Channel)
		allTree = MenuAllTree(allTree)
	}
	allTickets := map[string]interface{}{
		"data":           allTree,
		"quantity":       allQuantity,
		"sum_net_amount": allAmount,
	}

	result := sales_report.CategoryReportResponse{}
	data, err := json.Marshal(&allTickets)
	if err != nil {
		logrus.Errorf("PosCategoryReport allTickets Marshal err:%v", err)
		return nil, err
	}
	result.Extend = data
	return &result, nil
}
