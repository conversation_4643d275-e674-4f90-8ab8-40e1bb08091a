package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	oauth_client "gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"strconv"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/mq_task"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"hexcloud.cn/histore/hexerror"
)

func FindSalesTicket(ctx context.Context, ticketId string, partnerId int64) (*model.SalesTicketContent, error) {
	salesTicket := &model.SalesTicketContent{TicketId: ticketId, PartnerId: partnerId}
	ticketContent, err := repo.DefaultSalesDB.FindSales(ctx, salesTicket)
	if err != nil {
		return nil, err
	}
	return ticketContent, nil
}

func ReInitTicket(ctx context.Context, eticketIds []int64) (int64, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Infof("[%d] [services.ReInitTicket] reinit", trackId)
	return repo.DefaultSalesDB.ReinitSales(ctx, eticketIds)
}

func SaveSalesInfo(ctx context.Context, body string) (id string, err error) {
	trackId := ctx.Value(config.TrackId)
	partnerId := ctx.Value("partner_id")
	logger.Pre().Debugf("[%d] [services.SaveSalesInfo] partner_id: `%s` receive body.\nData:`%s`", trackId, partnerId, body)
	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.SaveSalesInfo] get user info failed.\nErr: `%+v`", trackId, err)
	//	return "", hexerror.PermissionDenied("No user info.")
	//}
	var ticket model.Ticket
	if err = json.Unmarshal([]byte(body), &ticket); err != nil {
		logger.Pre().Errorf("[%d] [services.SaveSalesInfo] unmarshal ticket failed.\nData: `%s`\nErr: `%+v`", trackId, body, err)
		return
	}
	// 如果堂食渠道id为空，就复制为source的值
	if ticket.Channel.TpName == "" && ticket.Channel.Source == "POS" {
		ticket.Channel.TpName = "POS"
	}
	var (
		storeId int64
		busDate time.Time
		utcNow  = time.Now().UTC()
	)
	if storeId, err = strconv.ParseInt(ticket.Store.Id, 10, 64); err != nil {
		// logger.Pre().Errorf("[%d] [services.SaveSalesInfo] parse store id failed.\nData: `%s`\nErr: `%+v`", trackId, ticket.Store.Id, err)
		storeId = 0
	}
	// 如果从token中获取不到partnerId，就获取电子小票里面的
	if partnerId == "" {
		partnerId = ticket.Store.PartnerId
		logger.Pre().Warnf("没有从token中获取到partnerID，使用小票中的partnerId：%s", partnerId)
	}
	if busDate, err = time.ParseInLocation(config.DateFormat, ticket.BusDate, time.UTC); err != nil {
		busDate = config.MaxYearDate
	}
	jsonBody := json.RawMessage(body)
	si := &model.SalesTicketContent{
		Id:        utils.GetHexUUid(ctx),
		EticketId: ticket.Id,       // 电子小票的id
		TicketId:  ticket.TicketId, // 真正的电子小票id
		PartnerId: cast.ToInt64(partnerId),
		//ScopeId:       int64(info.ScopeId),
		StoreId:   storeId,
		BusDate:   busDate.UTC(),
		OrderTime: ticket.GetOrderTime(),
		Created:   utcNow, // 记录时间
		Updated:   utcNow, // 更新时间
		//UpdatedBy:     int64(info.UserId),
		Status:        model.Init,         // 新增，异常，完成
		ProcessStatus: model.Process_Init, // 新增，验证成功，折扣分摊，完成
		Content:       &jsonBody,
		ChannelId:     cast.ToInt64(ticket.Channel.Id),
		ChannelName:   ticket.Channel.TpName,
		OrderType:     ticket.Channel.OrderType,
		OrderTypeName: ticket.Channel.OrderType,
		ShiftNumber:   ticket.ShiftNumber,
	}
	is, err := repo.DefaultSalesDB.InsertOrUpdateSale(ctx, si) //插入到数据库中
	if err != nil {
		return "", err
	}
	if is.Id == si.Id {
		mq_task.PublishMessage(ctx, config.WaitCheckTopic, []byte(strconv.Itoa(int(si.Id))))
	}

	return strconv.FormatInt(is.Id, 10), nil
}

func SaveAbnormalSalesInfo(ctx context.Context, body string) (id string, err error) {
	trackId := ctx.Value(config.TrackId)
	partnerId := ctx.Value("partner_id")
	logger.Pre().Debugf("[%d] [services.SaveAbnormalSalesInfo] partner_id: `%s` receive body.\nData:`%s`", trackId, partnerId, body)
	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.SaveAbnormalSalesInfo] get user info failed.\nErr: `%+v`", trackId, err)
	//	return "", hexerror.PermissionDenied("No user info.")
	//}
	var ticket model.Ticket
	if err = json.Unmarshal([]byte(body), &ticket); err != nil {
		logger.Pre().Errorf("[%d] [services.SaveAbnormalSalesInfo] unmarshal ticket failed.\nData: `%s`\nErr: `%+v`", trackId, body, err)
		return
	}
	// 如果堂食渠道id为空，就复制为source的值
	if ticket.Channel.TpName == "" && ticket.Channel.Source == "POS" {
		ticket.Channel.TpName = "POS"
	}
	var (
		storeId            int64
		busDate, orderDate time.Time
		utcNow             = time.Now().UTC()
	)
	zone := ticket.GetTimeZone()
	if storeId, err = strconv.ParseInt(ticket.Store.Id, 10, 64); err != nil {
		// logger.Pre().Errorf("[%d] [services.SaveAbnormalSalesInfo] parse store id failed.\nData: `%s`\nErr: `%+v`", trackId, ticket.Store.Id, err)
		storeId = 0
	}
	// 如果从token中获取不到partnerId，就获取电子小票里面的
	if partnerId == "" {
		partnerId = ticket.Store.PartnerId
		logger.Pre().Warnf("没有从token中获取到partnerID，使用小票中的partnerId：%s", partnerId)
	}
	if busDate, err = time.ParseInLocation(config.DateFormat, ticket.BusDate, time.UTC); err != nil {
		busDate = config.MaxYearDate
	}
	if orderDate, err = time.ParseInLocation(config.DateTimeFormat, ticket.EndTime, zone); err != nil {
		orderDate = utcNow
	}
	jsonBody := json.RawMessage(body)
	si := &model.SalesTicketContentAbnormal{
		Id:        uuid.GetId(),
		EticketId: ticket.Id,       // 电子小票的id
		TicketId:  ticket.TicketId, // 真正的电子小票id
		PartnerId: cast.ToInt64(partnerId),
		//ScopeId:       int64(info.ScopeId),
		StoreId:   storeId,
		BusDate:   busDate.UTC(),
		OrderTime: orderDate.UTC(),
		Created:   utcNow, // 记录时间
		Updated:   utcNow, // 更新时间
		//UpdatedBy:     int64(info.UserId),
		Status:        model.Init,         // 新增，异常，完成
		ProcessStatus: model.Process_Init, // 新增，验证成功，折扣分摊，完成
		Content:       &jsonBody,
		ChannelId:     cast.ToInt64(ticket.Channel.Id),
		ChannelName:   ticket.Channel.TpName,
		OrderType:     ticket.Channel.OrderType,
		OrderTypeName: ticket.Channel.OrderType,
		ShiftNumber:   ticket.ShiftNumber,
	}
	is, err := repo.DefaultAbnormalSalesDB.InsertOrUpdateAbnormalSale(ctx, si) //插入到数据库中
	if err != nil {
		return "", err
	}
	if is.Id == si.Id {
		mq_task.PublishMessage(ctx, config.ResolveAbnormalTicketTopic, []byte(strconv.Itoa(int(si.Id))))
	}

	return strconv.FormatInt(is.Id, 10), nil
}

func QuerySalesInfo(ctx context.Context, errReq *model.ErrorTicketRequest) (result []*model.SalesTicketContent, total int64, err error) {
	const (
		timeLimit = 31 * 24 * time.Hour
	)
	var (
		timeKeyArray  = []model.SalesTimeKey{model.BusDate, model.OrderDate, model.CreateDate, model.UpdateDate}
		timeStrArray  = []string{errReq.BusDate, errReq.OrderDate, errReq.CreateDate, errReq.UpdateDate}
		timeCondition = make(map[model.SalesTimeKey][]*time.Time)
	)
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.QuerySalesInfo] receive body.\nbusDate: `%s`\norderDate: `%s`\ncreateDate: `%s`\nupdateDate: `%s`\nstoreId: `%d`\nstatus: `%s`\nlimit: `%d`\noffset: `%d`", trackId, errReq.BusDate, errReq.OrderDate, errReq.CreateDate, errReq.UpdateDate, errReq.StoreId, errReq.Status, errReq.Limit, errReq.Offset)
	// 校验门店 id 是否在权限内
	//if storeId != 0 {
	//	if _, ids, err := entity.GetRegionAndStoreIdsByType(ctx, _type); err != nil {
	//		return nil, 0, err
	//	} else if !slice.Uint64InSlice(ids, uint64(storeId)) {
	//		return []*model.SalesTicketContent{}, 0, nil
	//	}
	//}

	for i, item := range timeKeyArray {
		var f, t *time.Time
		if f, t, err = utils.ParseTimeWithLimit(timeStrArray[i], timeLimit); err != nil {
			logger.Pre().Errorf("[%d] [services.QuerySalesInfo] parse %s time failed.\nData: `%s`\nErr: `%+v`", trackId, item.String(), timeStrArray[i], err)
			return nil, 0, err
		}
		// 要不两边都存在，要不都不存在，所以只判断一边
		if f != nil {
			timeCondition[item] = []*time.Time{f, t}
			// 如果是交易日期/订货日期，时间范围的右值需要加一天，例如：
			// 传入 2020-01-01;2020-01-01
			// 最后的结果需要是：>= 2020-01-01 AND < 2020-01-02
			if timeStrArray[i] == errReq.BusDate || timeStrArray[i] == errReq.CreateDate {
				addDay := timeCondition[item][1].Add(24 * time.Hour)
				timeCondition[item][1] = &addDay
			}
		}
	}
	cond := &model.SalesTicketContent{StoreId: errReq.StoreId}
	// 如果查询状态不为空，就加上状态过滤
	if errReq.Status != model.NilSalesInfoStatus {
		cond.Status = errReq.Status
	}
	// 查询并返回异常结果result
	if result, total, err = repo.DefaultSalesDB.SearchSales(ctx, cond, timeCondition, errReq, false); err != nil {
		return
	}
	var storeIds, oauthIds []int64
	// 遍历查询到的每一个电子小票数据
	for _, item := range result {
		logger.Pre().Debugf("[%d] [services.QuerySalesInfo] receive database sales info.\nData: `%+v`", trackId, item)
		// 如果ContentModified是空，就将Content内容赋值给ContentModified
		if item.ContentModified == nil || len([]byte(*item.ContentModified)) == 0 {
			item.ContentModified = item.Content
		}
		var ticket model.Ticket
		if err := json.Unmarshal([]byte(*item.ContentModified), &ticket); err != nil {
			logger.Pre().Warningf("[%d] [services.QuerySalesInfo] unmarshal database ticket failed.\nData: `%s`\nErr: `%+v`", trackId, string([]byte(*item.ContentModified)), err)
			continue
		}

		item.ChannelName = ticket.Channel.TpName
		storeIds = append(storeIds, item.StoreId)
		oauthIds = append(oauthIds, item.UpdatedBy)
	}

	// 填充元数据和认证：门店名称等信息
	metadataResp, err := entity.GetIdsFieldMap(ctx, "store", storeIds, ctx.Value("lan").(string))
	oauthResp, _ := oauth_client.GetIdsFieldMap(ctx, oauthIds)
	for _, item := range result {
		// 映射门店名称
		if v, in := metadataResp[item.StoreId]; in && v["name"] != nil {
			item.StoreName, _ = v["name"].(string)
		}
		// 映射操作人员名字
		if v, in := oauthResp[item.UpdatedBy]; in {
			item.UpdatedName = v["name"]
		}
	}
	return
}

func UpdateSalesInfo(ctx context.Context, id int64, initStatus bool, modified string) error {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.UpdateSalesInfo] receive body.\nId: `%d`\nInit status: `%t`\nContent modified: `%s`", trackId, id, initStatus, modified)

	if id <= 0 || strings.TrimSpace(modified) == "" {
		return hexerror.InvalidData(fmt.Sprintf("Id: `%d`, Content modified: `%s`", id, modified))
	}

	// 通过id查找到原电子小票
	sales := &model.SalesTicketContent{Id: id}
	if s, err := repo.DefaultSalesDB.FindSales(ctx, sales); err != nil || s == nil {
		logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] Find sales info by id failed.\nId: `%d`\nResult:`%+v`\nErr:`%+v`", trackId, id, s, err)
		return hexerror.NotFound(fmt.Sprintf("Id:`%d`, Err: %+v", id, err))
	} else if s.Status != model.DataError {
		logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] Status is %s != %s.\nId: `%d`", trackId, s.Status, model.DataError, id)
		return hexerror.InvalidAction(fmt.Sprintf("Status is %s != %s", s.Status, model.DataError))
	}

	modifiedBs := json.RawMessage([]byte(modified))
	sales.ContentModified = &modifiedBs
	// 更新之后，将状态改为init
	if initStatus {
		sales.Status = model.Init
	}
	sales.Updated = time.Now().UTC()
	//sales.UpdatedBy = int64(info.UserId)
	var err error
	if err := repo.DefaultSalesDB.UpdateSales(ctx, sales); err == nil && initStatus {
		mq_task.PublishMessage(ctx, config.WaitCheckTopic, []byte(strconv.Itoa(int(sales.Id))))
	}
	return err
}

func RollbackSalesModified(ctx context.Context, id int64) error {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.RollbackSalesModified] receive body.\nId: `%d`", trackId, id)

	if id <= 0 {
		return hexerror.InvalidData(fmt.Sprintf("Id: `%d`", id))
	}

	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.RollbackSalesModified] get user info failed.\nErr: `%+v`", trackId, err)
	//	return hexerror.PermissionDenied("No user info.")
	//}

	sales := &model.SalesTicketContent{Id: id}
	if s, err := repo.DefaultSalesDB.FindSales(ctx, sales); err != nil || s == nil {
		logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] Find sales info by id failed.\nId: `%d`\nResult:`%+v`\nErr:`%+v`", trackId, id, s, err)
		return hexerror.NotFound(fmt.Sprintf("Id:`%d`, Err: %+v", id, err))
	} else if s.Status != model.DataError && s.Status != model.Init {
		logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] Status is %s != %s.\nId: `%d`", trackId, s.Status, model.DataError, id)
		return hexerror.InvalidAction(fmt.Sprintf("Status is %s != %s", s.Status, model.DataError))
	}
	return repo.DefaultSalesDB.RollbackSalesModified(ctx)
}
