package services

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"time"
)

func SaveInboundInfo(ctx context.Context, body string) (id string, err error) {
	content, info, err := inboundBodyToContent(ctx, body)
	if err != nil {
		return "0", err
	}

	products, err := toInboundProducts(content, info)
	if err != nil {
		return "0", err
	}

	ids := uuid.GetIds(2 + len(products))
	content.Id = ids[0]
	content.InboundId = ids[1]
	for i, p := range products {
		p.Id = ids[i+2]
		p.InboundId = content.InboundId
	}

	userId := cast.ToInt64(ctx.Value("user_id"))

	for i := 0; i < 3; i++ {
		err = repo.DefaultInboundDB.SaveContent(ctx, userId, content)
		if err == nil {
			break
		}
	}
	if err != nil {
		return "0", err
	}

	for i := 0; i < 3; i++ {
		err = repo.DefaultInboundDB.SaveProducts(ctx, userId, products)
		if err == nil {
			break
		}
	}
	return cast.ToString(content.Id), err
}

func inboundBodyToContent(ctx context.Context, body string) (*model.SalesInboundContent, *model.InboundInfo, error) {
	var info model.InboundInfo
	err := json.Unmarshal([]byte(body), &info)
	if err != nil {
		return nil, nil, err
	}

	if info.Products == nil || len(info.Products) == 0 {
		return nil, nil, errors.New("商品不能为空")
	}

	partnerId := ctx.Value("partner_id")
	userId := cast.ToInt64(ctx.Value("user_id"))
	content := json.RawMessage(body)
	inboundTime, err := parseInboundOperateTime(info.InboundTime)
	if err != nil {
		return nil, nil, err
	}
	var productCount int32
	var productNames string
	mp := make(map[string]struct{})
	for _, p := range info.Products {
		productCount += int32(p.Qty)
		if _, ok := mp[p.Name]; !ok {
			if productNames != "" {
				productNames += "、"
			}
			productNames += p.Name
			mp[p.Name] = struct{}{}
		}
	}
	now := time.Now()
	contentModel := &model.SalesInboundContent{
		Id:           0,
		InboundId:    0,
		PartnerId:    cast.ToInt64(partnerId),
		StoreId:      cast.ToInt64(info.StoreID),
		InboundTime:  inboundTime,
		ProductNames: productNames,
		ProductQty:   productCount,
		OperatorName: info.OperatorName,
		Content:      &content,
		Created:      now,
		Updated:      now,
		UpdatedBy:    userId,
		Deleted:      0,
	}
	return contentModel, &info, err
}

func toInboundProducts(content *model.SalesInboundContent, info *model.InboundInfo) ([]*model.SalesInboundProduct, error) {
	products := make([]*model.SalesInboundProduct, 0)
	for _, p := range info.Products {
		if len(p.ComboItems) > 0 {
			for _, c := range p.ComboItems {
				product := toInboundProduct(content, c)
				products = append(products, product)
			}
		} else {
			product := toInboundProduct(content, p)
			products = append(products, product)
		}
	}
	return products, nil
}

func toInboundProduct(content *model.SalesInboundContent, p *model.InboundProduct) *model.SalesInboundProduct {
	accPrice := 0.0
	var accQty int32
	for _, a := range p.Accessories {
		accPrice += a.Price
		accQty += a.Qty
	}
	accPrice, _ = decimal.NewFromFloat(accPrice * (float64(accQty) / float64(p.Qty))).Round(2).Float64()
	product := &model.SalesInboundProduct{
		Id:           0,
		InboundId:    content.Id,
		PartnerId:    content.PartnerId,
		StoreId:      content.StoreId,
		SkuId:        cast.ToInt64(p.Id),
		SpuId:        cast.ToInt64(p.SpuId),
		SkuCode:      p.Code,
		SkuName:      p.Name,
		Price:        p.Price,
		AccPrice:     accPrice,
		Qty:          float64(p.Qty),
		InboundTime:  content.InboundTime,
		OperatorName: content.OperatorName,
		Created:      content.Created,
		Updated:      content.Updated,
		UpdatedBy:    content.UpdatedBy,
		Deleted:      0,
	}
	return product
}

func parseInboundOperateTime(t string) (operateTime time.Time, err error) {
	return time.ParseInLocation(config.DateTimeFormat, t, config.ShanghaiLocation)
}
