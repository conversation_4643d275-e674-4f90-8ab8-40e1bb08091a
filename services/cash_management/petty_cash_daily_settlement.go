package cash_management

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/cash_management"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryPettyCashDailySettlementReport(ctx context.Context, query *model.CommonRequest) (*cash_management.PettyCashDailySettlementReportResp, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.cash_management.QueryPettyCashDailySettlementReport] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	//var storeIDs = make([]int64, 0)
	//if condition.RegionSearchType == "STORE" {
	//	storeIDs = condition.RegionSearchIds
	//}
	////增加数据校验
	//domain := "hipos.report"
	//schema := "STORE"
	//userId := cast.ToInt64(ctx.Value("user_id"))
	//storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs)
	//if err != nil {
	//	return nil, err
	//}
	//if condition.RegionSearchType == "STORE" {
	//	condition.RegionSearchIds = storeIds
	//} else {
	//	condition.StoreIDs = storeIds
	//}
	resp, err := repo.DefaultCashManagementDB.PettyCashDailySettlementReport(ctx, condition)
	return resp, err
}
