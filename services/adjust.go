package services

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/grpc/adjust"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"io/ioutil"
	"net/http"
	"time"
)

func SaveAdjustInfo(ctx context.Context, body string) (id string, err error) {
	var adj model.FrmLoss
	err = json.Unmarshal([]byte(body), &adj)
	if err != nil {
		return "0", err
	}

	err = uploadAdjustToBoh(ctx, &adj)
	if err != nil {
		return "0", err
	}

	partnerId := ctx.Value("partner_id")
	userId := cast.ToInt64(ctx.Value("user_id"))
	content := json.RawMessage(body)
	bd, err := time.Parse("2006-01-02", adj.BusDate)
	if err != nil {
		return "0", err
	}
	operateTime, err := parseOperateTime(adj.CreatedTime)
	if err != nil {
		return "0", err
	}
	now := time.Now()
	adjContent := &model.SalesAdjustContent{
		Id:          uuid.GetId(),
		AdjustId:    adj.Id,
		BusDate:     bd,
		PartnerId:   cast.ToInt64(partnerId),
		StoreId:     cast.ToInt64(adj.StoreID),
		OperateTime: operateTime,
		Content:     &content,
		Created:     now,
		Updated:     now,
		UpdatedBy:   userId,
		Deleted:     0,
	}
	products := make([]*model.SalesAdjustProduct, 0)
	for _, p := range adj.FrmLossProduct {
		products = append(products, frmLossProductToModel(&adj, adjContent, &p, operateTime, userId)...)
	}
	for i := 0; i < 3; i++ {
		err = repo.DefaultAdjustDB.SaveContent(ctx, userId, adjContent)
		if err == nil {
			break
		}
	}
	if err != nil {
		return "0", err
	}
	for i := 0; i < 3; i++ {
		err = repo.DefaultAdjustDB.SaveProducts(ctx, userId, products)
		if err == nil {
			break
		}
	}
	return cast.ToString(adjContent.Id), err
}

func frmLossProductToModel(adj *model.FrmLoss, adjContent *model.SalesAdjustContent, p *model.FrmLossProduct, operateTime time.Time, userId int64) []*model.SalesAdjustProduct {
	result := make([]*model.SalesAdjustProduct, 0)
	if len(p.ComboItems) > 0 {
		for _, item := range p.ComboItems {
			result = append(result, frmLossProductToModel(adj, adjContent, item, operateTime, userId)...)
		}
	} else {
		skuRemarkBody, _ := json.Marshal(p.SkuRemark)
		skuRemark := json.RawMessage(skuRemarkBody)
		accBody, _ := json.Marshal(p.Accesssories)
		acc := json.RawMessage(accBody)
		mp := &model.SalesAdjustProduct{
			Id:           uuid.GetId(),
			AdjustId:     adjContent.AdjustId,
			BusDate:      adjContent.BusDate,
			PartnerId:    adjContent.PartnerId,
			StoreId:      adjContent.StoreId,
			SkuId:        cast.ToInt64(p.ProductId),
			SpuId:        cast.ToInt64(p.LinkSpuId),
			SkuCode:      p.ProductCode,
			SkuName:      p.ProductName,
			Price:        p.Price,
			AccPrice:     0,
			Qty:          p.Quantity,
			Unit:         p.UnitName,
			ReasonCode:   adj.FrmCode,
			ReasonName:   adj.ReasonName,
			OperateTime:  operateTime,
			OperatorName: adj.OperatorName,
			SkuRemark:    &skuRemark,
			Accessories:  &acc,
			Remark:       adj.Remark,
			Created:      adjContent.Created,
			Updated:      adjContent.Created,
			UpdatedBy:    userId,
			Deleted:      0,
		}
		// 计算单个商品中的加料总价格
		accPrice := 0.0
		for _, a := range p.Accesssories {
			// 单个商品中此加料的价格
			accPrice += a.Price * (a.Quantity / p.Quantity)
		}
		for _, r := range p.SkuRemark {
			// 单个商品中此属性的价格
			accPrice += r.Values.Price
		}
		mp.AccPrice = accPrice
		result = append(result, mp)
	}
	return result
}

func RollbackAdjust(ctx context.Context, id string) error {
	userId := cast.ToInt64(ctx.Value("user_id"))
	content, err := repo.DefaultAdjustDB.FindContent(ctx, id)
	if err != nil {
		return err
	}
	bs, err := content.Content.MarshalJSON()
	if err != nil {
		return err
	}
	var adj model.FrmLoss
	err = json.Unmarshal(bs, &adj)
	if err != nil {
		return err
	}

	newAdj := adj.Flip()
	err = uploadAdjustToBoh(ctx, &newAdj)
	if err != nil {
		return err
	}

	for i := 0; i < 3; i++ {
		err = repo.DefaultAdjustDB.Rollback(ctx, userId, id)
		if err == nil {
			break
		}
	}

	return err
}

func uploadAdjustToBoh(ctx context.Context, adj *model.FrmLoss) (err error) {
	enabled, err := bohEnabled(ctx)
	if err != nil {
		return err
	}
	if !enabled {
		return nil
	}
	sid := cast.ToInt64(adj.StoreID)
	storeMaps, _ := entity.GetIdsFieldMap(ctx, "store", []int64{sid}, "")
	operateTime, err := parseOperateTime(adj.CreatedTime)
	if err != nil {
		return err
	}

	bohAdj := adjust.CreatedAdjustByCodeRequest{
		AdjustStore: cast.ToString(storeMaps[sid]["code"]),
		ReasonType:  adj.FrmCode,
		Remark:      adj.Remark,
		RequestId:   adj.RequestId,
		AdjustDate:  operateTime.Format(config.DateFormat),
	}
	bohAdj.Products = make([]*adjust.CreatedAdjustProductByCode, 0)
	for _, p := range adj.FrmLossProduct {
		bohAdj.Products = append(bohAdj.Products, frmLossProductToCreatedAdjustProductByCode(&p, adj.FrmCode)...)
	}

	partnerId := ctx.Value("partner_id")
	md := metadata.New(map[string]string{
		"partner_id":    cast.ToString(partnerId),
		"user_id":       "1",
		"scope_id":      "0",
		"store_id":      adj.StoreID,
		"authorization": cast.ToString(ctx.Value("Authorization")),
	})
	ctx = metadata.NewOutgoingContext(ctx, md)
	conn, err := grpc.Dial(config.DefaultConfig.ConnStr.Supply, grpc.WithInsecure())
	if err != nil {
		logger.Pre().Errorf("grpc.Dial err: %v", err)
		return err
	}
	defer conn.Close()
	client := adjust.NewAdjustClient(conn)
	logger.Pre().Infof("supply.adjust.req: %+v\n metadata: %+v\n", &bohAdj, md)
	resp, err := client.CreatedAdjustByCode(ctx, &bohAdj)
	if err != nil {
		logger.Pre().Errorf("supply.adjust.err: %v", err)
		return err
	}
	logger.Pre().Infof("supply.adjust.resp: %+v\n", resp)
	return nil
}

func frmLossProductToCreatedAdjustProductByCode(p *model.FrmLossProduct, reasonType string) []*adjust.CreatedAdjustProductByCode {
	result := make([]*adjust.CreatedAdjustProductByCode, 0)
	for _, a := range p.Accesssories {
		result = append(result, frmLossProductToCreatedAdjustProductByCode(a, reasonType)...)
	}
	if len(p.ComboItems) > 0 {
		for _, a := range p.ComboItems {
			result = append(result, frmLossProductToCreatedAdjustProductByCode(a, reasonType)...)
		}
	} else {
		skuRemarks := make([]*adjust.SkuRemark, 0)
		for _, r := range p.SkuRemark {
			skuRemarks = append(skuRemarks, &adjust.SkuRemark{
				Name: &adjust.SkuRemark_Tag{
					Id:   cast.ToUint64(r.Name.ID),
					Code: r.Name.Code,
					Name: r.Name.Name,
				},
				Values: &adjust.SkuRemark_Tag{
					Id:   cast.ToUint64(r.Values.ID),
					Code: r.Values.Code,
					Name: r.Values.Name,
				},
			})
		}
		result = append(result, &adjust.CreatedAdjustProductByCode{
			Id:          cast.ToUint64(p.ProductId),
			ProductCode: p.ProductCode,
			Quantity:    p.Quantity,
			ReasonType:  reasonType,
			SkuRemark:   skuRemarks,
		})
	}
	return result
}

type AppsIntegrationResponse struct {
	Status_code int             `json:"status_code"`
	Description string          `json:"description"`
	Payload     AppsIntegration `json:"payload"`
}

type AppsIntegration struct {
	Apps []App `json:"apps"`
}

type App struct {
	Name  string `json:"name"`
	Title string `json:"title"`
}

func bohEnabled(ctx context.Context) (bool, error) {
	// 优先判断apollo配置
	partnerId := cast.ToInt64(ctx.Value("partner_id"))
	if config.BohEnabled[partnerId] {
		return true, nil
	}
	if config.BohDisabled[partnerId] {
		return false, nil
	}
	// 调用基础服务接口，判断是否启用boh
	client := http.Client{
		Timeout: 3 * time.Second,
	}
	req, _ := http.NewRequest("GET", config.DefaultConfig.ConnStr.Basics+"/api/v2/apps-integration/apps", nil)
	req.Header.Set("Authorization", cast.ToString(ctx.Value("Authorization")))
	logger.Pre().Infof("http.Client.Get: %+v\n", req.Header)
	resp, err := client.Do(req)
	if err != nil {
		logger.Pre().Errorf("http.Client.Get err: %v", err)
		return false, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		logger.Pre().Errorf("http.Client.Get resp.StatusCode: %v", resp.StatusCode)
		return false, errors.New(fmt.Sprintf("apps-integration status: %v", resp.StatusCode))
	}
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.Pre().Errorf("ioutil.ReadAll err: %v", err)
		return false, err
	}
	logger.Pre().Infof("appsintegration.apps.%v: %s \n", partnerId, string(body))
	var result AppsIntegrationResponse
	err = json.Unmarshal(body, &result)
	if err != nil {
		logger.Pre().Errorf("json.Unmarshal err: %v", err)
		return false, err
	}
	if result.Status_code != 0 {
		logger.Pre().Errorf("appsintegration.apps.%v: %v", partnerId, result.Description)
		return false, errors.New(result.Description)
	}
	for _, app := range result.Payload.Apps {
		if app.Name == "BOH" {
			return true, nil
		}
	}

	return false, nil
}

func parseOperateTime(t string) (operateTime time.Time, err error) {
	return time.ParseInLocation(config.DateTimeFormat, t, config.ShanghaiLocation)
}
