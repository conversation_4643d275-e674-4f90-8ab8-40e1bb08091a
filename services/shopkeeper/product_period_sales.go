package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductPeriodSales(ctx context.Context, query *model.CommonRequest) (*report.ShopkeeperProductPeriodResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductPeriodSales] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	resp, err := repo.DefaultShopKeeper.ProductPeriodSales(ctx, condition)
	packagingForProductPeriodSales(ctx, query, resp)
	return resp, err

}

func packagingForProductPeriodSales(ctx context.Context, query *model.CommonRequest, resp *report.ShopkeeperProductPeriodResponse) {
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		productIds = append(productIds, row.ProductId)
	}
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	// 组装code和name
	for i := range resp.Rows {
		productId := resp.Rows[i].ProductId
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[i].ProductCode = cast.ToString(productMaps[productId]["code"])
		resp.Rows[i].ProductName = pName
	}
}
