package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
)

func PlatformDiscount(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.PlatformDiscount] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	resp, err := repo.DefaultShopKeeper.PlatformDiscount(ctx, condition)
	// 组装查询到的数据
	packagingForPlatformDiscount(ctx, query, resp)
	return resp, err
}

func packagingForPlatformDiscount(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		platformDiscountAmountTotal float64 // 平台补贴合计
		platformTotal               float64 // 正补贴合计，用于计算占比
	)
	nRows := make([]interface{}, 0, len(resp.Rows))
	channelMaps := report.GetPaymentChannelMaps(ctx, query, resp)
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		channelId := cast.ToInt64(r["channel_id"])
		channelCode := channelMaps[channelId]["code"]
		channelName := channelMaps[channelId]["name"]
		platformDiscountAmount, _ := strconv.ParseFloat(fmt.Sprintf("%2f", cast.ToFloat64(r["platform_discount_amount"])), 64)
		m := map[string]interface{}{
			"channel_code":             cast.ToString(channelCode), // 渠道名称，下钻也使用
			"platform_discount_amount": platformDiscountAmount,     // 补贴金额，渠道也使用
		}
		if platformDiscountAmount < 0 {
			m["proportion"] = "负数不参数占比计算"
		} else {
			platformTotal += platformDiscountAmount
		}

		nRows = append(nRows, map[string]interface{}{
			cast.ToString(channelName): m,
		})
	}
	if platformTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			for _, v := range r {
				m := v.(map[string]interface{})
				platformDiscountAmount := cast.ToFloat64(m["platform_discount_amount"])
				if platformDiscountAmount >= 0 {
					proportion := platformDiscountAmount / platformTotal
					m["proportion"] = proportion
				}
			}
		}
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	platformDiscountAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["platform_discount_amount"])), 64)
	resp.Summary = map[string]interface{}{
		"platform_discount_amount": platformDiscountAmountTotal,
	}
}
