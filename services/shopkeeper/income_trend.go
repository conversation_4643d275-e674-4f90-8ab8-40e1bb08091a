package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
)

// IncomeTrend 收入趋势
func IncomeTrend(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.IncomeTrend] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultShopKeeper.IncomeTrend(ctx, condition)
	// 组装查询到的结果数据
	packagingForIncomeTrend(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForIncomeTrend(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		businessAmountTotal  float64 // 营业额合计
		validOrderCountTotal int64   // 销量合计
		businessTotal        float64 // 正营业额合计，用于计算占比
		orderTotal           int64   // 正销量合计，用于计算占比
	)
	sum := resp.Summary
	validOrderCountTotal = cast.ToInt64(sum["order_count"])
	nRows := make([]interface{}, 0)
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		// 营业额
		businessAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["business_amount"])), 64)
		var month string
		switch cast.ToInt64(r["months"]) {
		case 1:
			month = "Jan"
		case 2:
			month = "Feb"
		case 3:
			month = "Mar"
		case 4:
			month = "Apr"
		case 5:
			month = "May"
		case 6:
			month = "Jun"
		case 7:
			month = "Jul"
		case 8:
			month = "Aug"
		case 9:
			month = "Sep"
		case 10:
			month = "Oct"
		case 11:
			month = "Nov"
		case 12:
			month = "Dec"
		}
		validOrderCount := cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"])
		m := map[string]interface{}{
			"business_amount":   businessAmount,  // 营业额
			"valid_order_count": validOrderCount, // 销量
		}
		if businessAmount <= 0 {
			m["proportion_business_amount"] = "负数不参与占比计算"
		} else {
			businessTotal += businessAmount
		}
		if validOrderCount <= 0 {
			m["proportion_order_count"] = "负数不参与占比计算"
		} else {
			orderTotal += validOrderCount
		}
		nRows = append(nRows, map[string]interface{}{
			month: m,
		})
	}
	if orderTotal > 0 || businessTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			for _, v := range r {
				m := v.(map[string]interface{})
				orderCount := cast.ToInt64(m["valid_order_count"])
				businessAmount := cast.ToFloat64(m["business_amount"])
				if orderTotal > 0 && orderCount > 0 { // 销量占比
					proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(orderCount)/float64(orderTotal)), 64)
					m["proportion_order_count"] = proportion
				}
				if businessTotal > 0 && businessAmount > 0 { // 营业额占比
					proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", businessAmount/businessTotal), 64)
					m["proportion_business_amount"] = proportion
				}
			}
		}
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	businessAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(sum["business_amount"])), 64)
	resp.Summary = map[string]interface{}{
		"business_amount":   businessAmountTotal,
		"valid_order_count": validOrderCountTotal,
	}
}
