package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
)

// DiscountCat 折扣.折扣分类
func DiscountCat(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.DiscountCat] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.DiscountCat(ctx, condition)
	// 组装查询到的结果数据
	packagingForDiscountCat(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForDiscountCat(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	discountMaps := report.GetDiscountMaps(ctx, query, resp) // 折扣map，包含折扣信息
	nRows := make([]interface{}, 0, len(resp.Rows))
	discountAmountTotal := 0.0
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		discountId := cast.ToInt64(r["promotion_id"])
		discountCode := discountMaps[discountId]["code"]
		discountName := discountMaps[discountId]["name"]
		discountAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["amount"])), 64)
		var record = map[string]interface{}{
			"promotion_id":    discountId,
			"promotion_code":  cast.ToString(discountCode),
			"promotion_name":  cast.ToString(discountName),
			"discount_amount": discountAmount,
		}
		amount := cast.ToFloat64(r["amount"])
		if amount <= 0.0 {
			record["proportion"] = "负数不参与占比统计"
		} else {
			discountAmountTotal += amount
		}
		nRows = append(nRows, record)
	}
	if discountAmountTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			discountAmount := cast.ToFloat64(r["discount_amount"])
			if discountAmount > 0 {
				proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["discount_amount"])/discountAmountTotal), 64)
				r["proportion"] = proportion
			}
		}
	}
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	resp.Rows = nRows
	total, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["amount"])), 64)
	resp.Summary = map[string]interface{}{
		"discount_amount": total,
	}
}
