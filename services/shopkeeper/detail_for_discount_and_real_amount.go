package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 小掌柜下钻.财务实收和优惠组成详情
func Detail(ctx context.Context, query *model.CommonRequest) (*report.DetailForDiscountAndRealAmountResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.detail] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	// 查询小掌柜商品分布
	resp, err := repo.DefaultShopKeeper.Detail(ctx, condition)
	packagingForDetail(ctx, query, resp)
	return resp, err
}

func packagingForDetail(ctx context.Context, query *model.CommonRequest, resp *report.DetailForDiscountAndRealAmountResponse) {
	storeIds := make([]int64, 0, len(resp.Rows))
	channelIds := make([]int64, 0)
	discountIds := make([]int64, 0)
	//paymentIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeIds = append(storeIds, row.StoreId)
		for _, pay := range row.CompositionOfPay {
			channelIds = append(channelIds, pay.ChannelId)
			//for _, p := range pay.Payments {
			//	paymentIds = append(paymentIds, p.PaymentId)
			//}
			for _, d := range pay.Discounts {
				discountIds = append(discountIds, d.DiscountId)
			}
		}
		for _, d := range row.CompositionOfDiscount {
			channelIds = append(channelIds, d.ChannelId)
			//for _, p := range d.Payments {
			//	paymentIds = append(paymentIds, p.PaymentId)
			//}
			for _, discount := range d.Discounts {
				discountIds = append(discountIds, discount.DiscountId)
			}
		}
	}
	storeMaps := report2.GetStoreMapsByIds(ctx, query, storeIds)
	channelMaps := report2.GetChannelMapsByIds(ctx, query, channelIds)
	discountMaps := report2.GetDiscountMapsByIds(ctx, query, discountIds)
	//paymentMaps := report2.GetPaymentMapsByIds(ctx, query, paymentIds)

	for i := range resp.Rows {
		storeId := resp.Rows[i].StoreId
		resp.Rows[i].StoreCode = cast.ToString(storeMaps[storeId]["code"])
		resp.Rows[i].StoreName = cast.ToString(storeMaps[storeId]["name"])
		for j := range resp.Rows[i].CompositionOfPay {
			channelId := resp.Rows[i].CompositionOfPay[j].ChannelId
			resp.Rows[i].CompositionOfPay[j].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
			resp.Rows[i].CompositionOfPay[j].ChannelName = cast.ToString(channelMaps[channelId]["name"])
			//for p := range resp.Rows[i].CompositionOfPay[j].Payments {
			//	paymentId := resp.Rows[i].CompositionOfPay[j].Payments[p].PaymentId
			//	//resp.Rows[i].CompositionOfPay[j].Payments[p].PaymentCode = cast.ToString(paymentMaps[paymentId]["code"])
			//	//resp.Rows[i].CompositionOfPay[j].Payments[p].PaymentName = cast.ToString(paymentMaps[paymentId]["name"])
			//}
			for d := range resp.Rows[i].CompositionOfPay[j].Discounts {
				discountId := resp.Rows[i].CompositionOfPay[j].Discounts[d].DiscountId
				resp.Rows[i].CompositionOfPay[j].Discounts[d].DiscountCode = cast.ToString(discountMaps[discountId]["code"])
				resp.Rows[i].CompositionOfPay[j].Discounts[d].DiscountName = cast.ToString(discountMaps[discountId]["name"])
			}
		}
		for jj := range resp.Rows[i].CompositionOfDiscount {
			channelId := resp.Rows[i].CompositionOfDiscount[jj].ChannelId
			resp.Rows[i].CompositionOfDiscount[jj].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
			resp.Rows[i].CompositionOfDiscount[jj].ChannelName = cast.ToString(channelMaps[channelId]["name"])
			//for k := range resp.Rows[i].CompositionOfDiscount[jj].Payments {
			//	paymentId := resp.Rows[i].CompositionOfDiscount[jj].Payments[k].PaymentId
			//	resp.Rows[i].CompositionOfDiscount[jj].Payments[k].PaymentCode = cast.ToString(paymentMaps[paymentId]["code"])
			//	resp.Rows[i].CompositionOfDiscount[jj].Payments[k].PaymentName = cast.ToString(paymentMaps[paymentId]["name"])
			//}
			for d := range resp.Rows[i].CompositionOfDiscount[jj].Discounts {
				discountId := resp.Rows[i].CompositionOfDiscount[jj].Discounts[d].DiscountId
				resp.Rows[i].CompositionOfDiscount[jj].Discounts[d].DiscountCode = cast.ToString(discountMaps[discountId]["code"])
				resp.Rows[i].CompositionOfDiscount[jj].Discounts[d].DiscountName = cast.ToString(discountMaps[discountId]["name"])
			}
		}
	}
}

func mergeToSlice(nums []int64, num int64) []int64 {
	m := make(map[int64]struct{})
	for _, v := range nums {
		m[v] = struct{}{}
	}
	if _, ok := m[num]; !ok {
		nums = append(nums, num)
	}
	return nums
}
