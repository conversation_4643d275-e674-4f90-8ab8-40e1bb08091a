package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
)

// ProductCategory 实收金额.商品类别
func ProductCategory(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductCategory] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultShopKeeper.BusCategory(ctx, condition)
	// 组装查询到的结果数据
	packagingForBusCategory(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForBusCategory(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		categoryId      int64                            // 类别id
		categoryName    string                           // 类别名称
		netAmount       float64                          // 商品实收
		validOrderCount int64                            // 订单量
		categoryMaps    map[int64]map[string]interface{} // 商品类别信息
		nRows           []interface{}                    // 查询记录
		orderCountTotal int64                            // 订单量合计
		netAmountTotal  float64                          // 商品实收合计
		orderTotal      int64                            // 正单合计，用于计算占比
		netTotal        float64                          // 正商品实收合计，用于计算占比
	)
	categoryMaps = report.GetProductCategoryMaps(ctx, query, resp)
	nRows = make([]interface{}, 0, len(resp.Rows))
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		categoryId = cast.ToInt64(r["product_category_id"])
		categoryName = cast.ToString(categoryMaps[categoryId]["name"])
		netAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["net_amount"])), 64)
		validOrderCount = cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"])
		// 组装每一条记录，形成每一行记录
		var record = map[string]interface{}{
			"category_id":       categoryId,
			"category_name":     categoryName,
			"net_amount":        netAmount,
			"valid_order_count": validOrderCount,
			"net_average":       nil,
			"order_average":     nil,
		}
		if netAmount <= 0 {
			record["proportion_net_amount"] = "负数不参与占比计算"
		} else {
			netTotal += netAmount
		}
		if validOrderCount <= 0 {
			record["proportion_order_count"] = "负数不参与占比计算"
		} else {
			orderTotal += validOrderCount
		}
		nRows = append(nRows, record)
		orderCountTotal += validOrderCount
		netAmountTotal += netAmount
	}
	if orderTotal > 0 || netTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			orderCount := cast.ToInt64(r["valid_order_count"])
			netAmount := cast.ToFloat64(r["net_amount"])
			if orderCount > 0 && orderTotal > 0 { // 订单量占比
				proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", float64(orderCount)/float64(orderTotal)), 64)
				r["proportion_order_count"] = proportion
			}
			if netAmount > 0 && netTotal > 0 { // 商品实收占比
				proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", netAmount/netTotal), 64)
				r["proportion_net_amount"] = proportion
			}
		}
	}
	// 查询的行数据
	resp.Rows = nRows
	// 查询的统计数据汇总
	netAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", netAmountTotal), 64)
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	resp.Summary = map[string]interface{}{
		"net_amount":        netAmountTotal,
		"valid_order_count": orderCountTotal,
	}
}
