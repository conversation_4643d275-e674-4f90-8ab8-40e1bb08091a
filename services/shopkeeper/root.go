package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
)

const TRANSFER_LEN int = 1000

// 获取门店信息
func GetStoreMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	storeIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		storeIds = append(storeIds, cast.ToInt64(r["store_id"]))
	}
	regionMaps, _ := entity.GetIdsFieldMap(ctx, "store", storeIds, query.Lan)
	return regionMaps
}

// 获取类别信息map
func GetCategoryMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productCategoryMaps := make(map[int64]map[string]interface{})
	for i := 0; i < len(ids); i += TRANSFER_LEN {
		end := i + TRANSFER_LEN
		if end > len(ids) {
			end = len(ids)
		}
		productCategoryMapTemp, _ := entity.GetIdsFieldMap(ctx, "product_category", ids, query.Lan)
		productCategoryMaps = helpers.MergeMap(productCategoryMaps, productCategoryMapTemp)
	}
	//productCategoryMaps, err := entity.GetIdsFieldMap(ctx, "product_category", ids, query.Lan)
	//if err != nil {
	//	logger.Pre().Errorf("调用主档商品分类服务失败 error: %v", err)
	//}
	return productCategoryMaps
}

// 获取区域信息map
func GetRegionMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	schema := "store"
	switch query.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "geo_region"
		}
	case "BRANCH_REGION": // 管理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "branch_region"
		}
	case "COMPANY":
		schema = "COMPANY_INFO"
	}
	regionMaps, err := entity.GetIdsFieldMap(ctx, schema, ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _ , _ , _ , _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

// 获取商品信息map
func GetProductMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productMaps := make(map[int64]map[string]interface{})
	for i := 0; i < len(ids); i += TRANSFER_LEN {
		end := i + TRANSFER_LEN
		if end > len(ids) {
			end = len(ids)
		}
		productMapTemp, _ := entity.GetIdsFieldMap(ctx, "sku", ids[i:end], query.Lan)
		productMaps = helpers.MergeMap(productMaps, productMapTemp)
	}
	//productMaps, err := entity.GetIdsFieldMap(ctx, "sku", ids, query.Lan)
	//if err != nil {
	//	logger.Pre().Errorf("调用主档商品服务失败 error: %v", err)
	//}
	return productMaps
}

// 获取门店类型信息map
func GetStoreTypeMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	storeTypeMaps, _ := entity.GetIdsFieldMap(ctx, "store_type", ids, query.Lan)
	return storeTypeMaps
}
