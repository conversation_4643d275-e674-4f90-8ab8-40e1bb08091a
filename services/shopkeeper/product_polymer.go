package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func ProductPolymer(ctx context.Context, query *model.CommonRequest) (*report.ProductPolymerResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductPolymer] receive body. Content: `%+v`\n", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.ProductPolymer(ctx, condition)
	// 组装查询结果
	packagingForProductPolymer(ctx, query, resp)
	return resp, err
}

func packagingForProductPolymer(ctx context.Context, query *model.CommonRequest, resp *report.ProductPolymerResponse) {

	channelIds := make([]int64, 0)
	for _, row := range resp.Rows {
		channelIds = append(channelIds, row.ChannelId)
	}
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	for i := range resp.Rows {
		channelId := resp.Rows[i].ChannelId
		channelName := channelMaps[channelId]["name"]
		resp.Rows[i].ChannelName = cast.ToString(channelName)
	}
}
