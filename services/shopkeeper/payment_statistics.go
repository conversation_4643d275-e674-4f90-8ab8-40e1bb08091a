package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryPaymentStatistics(ctx context.Context, query *model.CommonRequest) (*report.PaymentStatisticsResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.QueryPaymentStatistics] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.PaymentStatistics(ctx, condition)
	packagingForPaymentStatistics(ctx, query, resp)
	return resp, err
}

func packagingForPaymentStatistics(ctx context.Context, query *model.CommonRequest, resp *report.PaymentStatisticsResponse) {
	//channelIds := make([]int64, 0)
	//for _, row := range resp.Rows {
	//	//for _, r := range row.Child {
	//	//	channelIds = append(channelIds, r.ChannelId)
	//	//}
	//}
	//channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	// 组装code和name
	//for r := range resp.Rows {
	//	for i := range resp.Rows[r].Child {
	//		channelId := resp.Rows[r].Child[i].ChannelId
	//		resp.Rows[r].Child[i].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
	//		resp.Rows[r].Child[i].ChannelName = cast.ToString(channelMaps[channelId]["name"])
	//	}
	//}
}

// 获取渠道信息map
func GetChannelMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	channelMaps, err := entity.GetIdsFieldMap(ctx, "channel_company", ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	return channelMaps
}
