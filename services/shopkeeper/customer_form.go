package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func CustomerForm(ctx context.Context, query *model.CommonRequest) (*report.CustomerFormResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.CustomerForm] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	resp, err := repo.DefaultShopKeeper.CustomerForm(ctx, condition)
	// 组装查询到的结果数据
	packagingForCustomerForm(ctx, query, resp)
	return resp, err
}

func packagingForCustomerForm(ctx context.Context, query *model.CommonRequest, resp *report.CustomerFormResponse) {
	type CompareAndSame struct {
		CompareRow     *report.CustomerForm
		CompareSameRow *report.CustomerForm
	}
	rowsMap := make(map[bool]*CompareAndSame) // 相同member的环比和同比信息放在一个结构体里
	for i := range resp.Rows {
		for i := range resp.Rows {
			if resp.Rows[i].Member {
				resp.Rows[i].MemberName = "会员"
			} else {
				resp.Rows[i].MemberName = "非会员"
			}
		}
		// 初始化
		newStruct := &CompareAndSame{
			CompareRow:     new(report.CustomerForm),
			CompareSameRow: new(report.CustomerForm),
		}
		rowsMap[resp.Rows[i].Member] = newStruct
	}
	// 环比数据放到map里
	for _, row := range resp.CompareRows {
		if rowsMap[row.Member] == nil {
			continue
		}
		rowsMap[row.Member].CompareRow = row
	}
	// 同比数据放到map里
	for _, row := range resp.CompareSameRows {
		if rowsMap[row.Member] == nil {
			continue
		}
		rowsMap[row.Member].CompareSameRow = row
	}
	// 计算
	for i := range resp.Rows {
		member := resp.Rows[i].Member
		resp.Rows[i].CompareBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[member].CompareRow.BusinessAmount
		if rowsMap[member].CompareRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareBusinessAmount = resp.Rows[i].CompareBusinessAmount / rowsMap[member].CompareRow.BusinessAmount
		}
		resp.Rows[i].CompareSameBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[member].CompareSameRow.BusinessAmount
		if rowsMap[member].CompareSameRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareSameBusinessAmount = resp.Rows[i].CompareSameBusinessAmount / rowsMap[member].CompareSameRow.BusinessAmount
		}
	}
}
