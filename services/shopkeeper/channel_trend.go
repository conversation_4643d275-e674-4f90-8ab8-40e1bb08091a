package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
)

// ChannelTrend 小掌柜-渠道收入趋势
func ChannelTrend(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.queryChannelTrend] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.ChannelTrend(ctx, condition)
	// 组装查询到的结果数据
	packagingForChannelTrend(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForChannelTrend(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		//businessAmount  float64       // 营业额 = 商品流水(gross_amount) + 包装费(package_fee) + 配送费(delivery_fee) + 税额(tax_fee)
		//expendAmount    float64       // 支出 = 商家促销活动补贴(merchant_discount_amount) + 佣金(commission) + 其他(other_fee)
		projectedIncome float64       // 实收金额
		channelName     string        // 渠道名称
		busDate         string        // 日期
		nRows           []interface{} // 返回结果
	)
	nRows = make([]interface{}, 0, len(resp.Rows))
	var tmpMap = make(map[string]map[string]float64)
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		channelId := cast.ToInt64(r["channel_id"])
		switch channelId {
		case 4483229963985649664: // 饿了吗
			channelName = "ele_info"
		case 4483230370032025600: // 美团
			channelName = "meituan_info"
		case 4483230552136122368: // pos
			channelName = "pos_info"
		case 4483914227022594048: // 小程序
			channelName = "app_info"
		}
		// 营业额
		//businessAmount,_ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["business_amount"])), 64)
		//expendAmount,_ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["expend_amount"])), 64)
		projectedIncome, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64("projected_income")), 64) // 实收金额
		busDate = cast.ToString(r["bus_date"])
		if tmpMap[channelName] == nil {
			tmpMap[channelName] = make(map[string]float64)
		}
		tmpMap[channelName][busDate] = projectedIncome
	}
	for k, v := range tmpMap {
		nRows = append(nRows, map[string]interface{}{
			k: v,
		})
	}
	// 查询的行数据
	resp.Rows = nRows
	// 查询的统计数据汇总
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	resp.Summary = nil
}
