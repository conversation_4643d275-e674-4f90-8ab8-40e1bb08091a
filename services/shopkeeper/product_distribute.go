package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report2 "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// 小掌柜-商品分布
func ProductDistribute(ctx context.Context, query *model.CommonRequest) (*report2.ProductDistributeResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.channel] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	// 查询小掌柜商品分布
	resp, err := repo.DefaultShopKeeper.ProductDistribute(ctx, condition)
	packagingForProductDistribute(ctx, query, resp)
	return resp, err
}

func packagingForProductDistribute(ctx context.Context, query *model.CommonRequest, resp *report2.ProductDistributeResponse) {
	categoryIds := make([]int64, 0)
	for _, row := range resp.Rows {
		categoryIds = append(categoryIds, row.Category0)
		for _, r := range row.Child {
			categoryIds = append(categoryIds, r.Category1)
			for _, rr := range r.Child {
				categoryIds = append(categoryIds, rr.Category2)
			}
		}
	}
	categoryMaps := report.GetCategoryMapsByIds(ctx, query, categoryIds)
	for i := range resp.Rows {
		category0 := resp.Rows[i].Category0
		category0Code := categoryMaps[category0]["code"]
		category0Name := categoryMaps[category0]["name"]
		resp.Rows[i].Category0Code = cast.ToString(category0Code)
		resp.Rows[i].Category0Name = cast.ToString(category0Name)

		for j := range resp.Rows[i].Child {
			category1 := resp.Rows[i].Child[j].Category1
			category1Code := categoryMaps[category1]["code"]
			category1Name := categoryMaps[category1]["name"]
			resp.Rows[i].Child[j].Category1Code = cast.ToString(category1Code)
			resp.Rows[i].Child[j].Category1Name = cast.ToString(category1Name)
			// 代表有子类目的商品
			if len(resp.Rows[i].Child) > 1 {
				if category1 == 0 {
					resp.Rows[i].Child[j].Category1 = 1
					resp.Rows[i].Child[j].Category1Code = "1"
					resp.Rows[i].Child[j].Category1Name = resp.Rows[i].Category0Name + "-商品合计"
				}
			}
			for k := range resp.Rows[i].Child[j].Child {
				category2 := resp.Rows[i].Child[j].Child[k].Category2
				category2Code := categoryMaps[category2]["code"]
				category2Name := categoryMaps[category2]["name"]
				resp.Rows[i].Child[j].Child[k].Category2Code = cast.ToString(category2Code)
				resp.Rows[i].Child[j].Child[k].Category2Name = cast.ToString(category2Name)
				// 代表有子类目的商品
				if len(resp.Rows[i].Child[j].Child) > 1 {
					if category2 == 0 {
						resp.Rows[i].Child[j].Child[k].Category2 = 1
						resp.Rows[i].Child[j].Child[k].Category2Code = "1"
						resp.Rows[i].Child[j].Child[k].Category2Name = resp.Rows[i].Child[j].Category1Name + "-商品合计"
					}
				}
			}
		}
	}
}
