package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func ProductStoreRank(ctx context.Context, query *model.CommonRequest) (*report.ProductStoreRankResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductStoreRank] receive body. Content: `%+v`\n", trackId, query)
	// 组装查询条件
	condition := query.ToRepoCondition([]string{"gross_amount", "net_amount", "item_count", "weight_count"})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验Store
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.ProductStoreRank(ctx, condition)
	// 组装查询结果
	packagingForProductStoreRank(ctx, query, resp)
	return resp, err
}

func packagingForProductStoreRank(ctx context.Context, query *model.CommonRequest, resp *report.ProductStoreRankResponse) {
	storeIds := make([]int64, 0, len(resp.Rows))
	for _, r := range resp.Rows {
		storeIds = append(storeIds, r.StoreId)
	}
	storeMaps := report2.GetStoreMapsByIds(ctx, query, storeIds)
	for i := range resp.Rows {
		storeId := resp.Rows[i].StoreId
		resp.Rows[i].StoreCode = cast.ToString(storeMaps[storeId]["code"])
		resp.Rows[i].StoreName = cast.ToString(storeMaps[storeId]["name"])
	}
}
