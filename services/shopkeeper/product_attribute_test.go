package shopkeeper

import (
	"fmt"
	"testing"
)

func Test_getGraphs(t *testing.T) {
	sku1 := map[string]string{
		"温度": "常温",
		"甜度": "少糖",
	}
	sku2 := map[string]string{
		"温度": "常温",
		"甜度": "不加糖",
	}
	sku3 := map[string]string{
		"温度": "加冰",
		"甜度": "少糖",
	}
	sku := [...]map[string]string{sku3, sku2, sku1}
	attrMap := make(map[string]map[string]int64)
	for _, row := range sku {
		for k, v := range row {
			if attr, ok := attrMap[k]; !ok { // 如果还没有该属性记录，增加一项
				m := map[string]int64{ // 先增加一项属性值
					v: 1,
				}
				attrMap[k] = m
			} else {
				if value, ok := attr[v]; !ok {
					// 如果有该属性，但没有该属性值，增加一个属性值
					attr[v] = 1
				} else {
					value += 1
					attr[v] = value
				}

			}
		}
	}

	m1 := map[string]int64{
		"珍珠": 2,
		"椰果": 2,
	}
	m2 := map[string]int64{
		"红豆": 1,
		"珍珠": 2,
		"椰果": 1,
	}
	m3 := map[string]int64{
		"椰果":  1,
		"红豆":  3,
		"脆脆波": 2,
	}
	var m4 map[string]int64
	accessMap := make(map[string]int64)
	arr := [...]map[string]int64{m4, m1, m2, m3}
	for _, row := range arr {
		if row == nil { // 如果没有加料，就增加无加料记录
			if name, ok := accessMap[NOFEED]; !ok {
				accessMap[NOFEED] = 1
			} else {
				name += 1
			}
		} else {
			// 遍历加料map
			for k, v := range row {
				if name, ok := accessMap[k]; !ok {
					accessMap[k] = v
				} else {
					name += v
					accessMap[k] = name
				}
			}
		}
	}
	fmt.Println(accessMap)
}
