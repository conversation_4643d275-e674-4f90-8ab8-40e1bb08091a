package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
	"strings"
)

func ProductRank(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductRank] receive body. Content: `%+v`\n", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.ProductRank(ctx, condition)
	// 组装查询结果
	packagingForProductRank(ctx, query, resp)
	return resp, err
}

func packagingForProductRank(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		productMaps         map[int64]map[string]interface{} // 菜品信息
		productCategoryMaps map[int64]map[string]interface{} // 菜品类别信息
		nRows               []interface{}                    // 记录行
		proportion          float64                          // 占比
		netAmountTotal      float64                          // 商品实收总计
		total               float64                          // 正商品实收合计，用于计算占比
	)
	productMaps = report.GetProductMaps(ctx, query, resp)
	productCategoryMaps = report.GetProductCategoryMaps(ctx, query, resp)
	nRows = make([]interface{}, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		productID := cast.ToInt64(r["product_id"])            // 商品id
		name := cast.ToString(productMaps[productID]["name"]) // 商品名称
		saleName := cast.ToString(productMaps[productID]["sale_name"])
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}

		productCode := productMaps[productID]["code"]                         // 商品编码
		productCategoryId := cast.ToInt64(r["product_category_id"])           // 商品类别id
		productCategoryName := productCategoryMaps[productCategoryId]["name"] // 商品类别名称
		netAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["net_amount"])), 64)
		m := map[string]interface{}{
			"product_code":          cast.ToString(productCode),
			"product_category_name": cast.ToString(productCategoryName),
			"net_amount":            netAmount,
		}
		if netAmount <= 0 {
			m["proportion"] = "负数不参与占比计算"
		} else {
			total += netAmount
		}
		netAmountTotal += netAmount
		nRows = append(nRows, map[string]interface{}{
			cast.ToString(pName): m,
		})
	}
	if total > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			for _, v := range r {
				m := v.(map[string]interface{})
				netAmount := cast.ToFloat64(m["net_amount"])
				if netAmount > 0 {
					proportion, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", netAmount/total), 64)
					m["proportion"] = proportion
				}
			}
		}
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	netAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", netAmountTotal), 64)
	resp.Summary = map[string]interface{}{
		"net_amount": netAmountTotal,
	}
}
