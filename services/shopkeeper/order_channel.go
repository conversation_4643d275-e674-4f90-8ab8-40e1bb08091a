package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
)

// OrderAndRefundChannel 下钻.订单渠道和退单渠道通用一个接口
func OrderAndRefundChannel(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.OrderAndRefundChannel] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 查询有效订单量和退单量
	resp, err := repo.DefaultShopKeeper.OrderAndRefundChannel(ctx, condition)
	// 组装查询到的结果数据
	packagingForOrderChannel(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForOrderChannel(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	//channelMaps := report.GetOrderAndRefundChannelMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	refundCountTotal := cast.ToInt64(resp.Summary["order_count_refund"])                                                 // 退单量合计
	grossAmountRefundTotal := cast.ToFloat64(resp.Summary["gross_amount_returned"])                                      // 退单商品流水合计
	validOrderCountTotal := cast.ToInt64(resp.Summary["order_count"]) + cast.ToInt64(resp.Summary["order_count_refund"]) // 有效订单合计
	var orderTotal int64
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		channelId := cast.ToInt64(r["channel_id"])
		//channelCode := channelMaps[channelId]["code"]
		//channelName := channelMaps[channelId]["name"]
		channelName := cast.ToString(r["channel_name"])
		validOrderCount := cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"]) // 有效订单量
		refundCount := cast.ToInt64(r["order_count_refund"])                                           // 退单量
		var proportionOrder, proportionGross float64
		if refundCountTotal != 0 {
			proportionOrder, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(refundCount)/cast.ToFloat64(refundCountTotal)), 64)
		}
		grossAmountReturned, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["gross_amount_returned"])), 64)
		if grossAmountRefundTotal != 0 {
			proportionGross, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", grossAmountReturned/grossAmountRefundTotal), 64)
		}
		// 组装每一条记录，形成每一行记录
		var record = map[string]interface{}{
			"channel_id": channelId, // 渠道id
			//"channel_code":              cast.ToString(channelCode), // 渠道code
			"channel_name":              cast.ToString(channelName), // 渠道名称
			"refund_order_count":        refundCount,                // 退单量
			"proportion_refund_count":   proportionOrder,            // 退单占比
			"gross_amount_returned":     grossAmountReturned,        // 退单商品流水
			"proportion_gross_returned": proportionGross,            // 退单商品流水占比
			"valid_order_count":         validOrderCount,            // 有效订单量
		}
		if validOrderCount <= 0 {
			record["proportion_order_count"] = "负数不参与占比计算"
		} else {
			orderTotal += validOrderCount
		}
		nRows = append(nRows, record)
	}
	if orderTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			orderCount := cast.ToInt64(r["valid_order_count"])
			if orderCount > 0 { // 有效订单占比
				proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(orderCount)/cast.ToFloat64(orderTotal)), 64)
				r["proportion_order_count"] = proportion
			}
		}
	}
	// 查询的行数据
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	grossAmountRefundTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", grossAmountRefundTotal), 64)
	resp.Summary = map[string]interface{}{
		"valid_order_count":     validOrderCountTotal,   // 订单量合计
		"refund_order_count":    refundCountTotal,       // 退单量合计
		"gross_amount_returned": grossAmountRefundTotal, //退单商品流水合计
	}
}
