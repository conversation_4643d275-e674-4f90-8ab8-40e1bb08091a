package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
)

// Period 小掌柜-营业时段报表
func Period(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.Period] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.Period(ctx, condition)
	// 组装查询到的结果数据
	packagingForPeriod(ctx, query, resp)
	return resp, err
}

func packagingForPeriod(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		businessAmount          float64       // 营业额 = 商品流水(gross_amount) + 包装费(package_fee) + 配送费(delivery_fee) + 税额(tax_fee)
		expendAmount            float64       // 支出 = 商家促销活动补贴(merchant_discount_amount) + 佣金(commission) + 其他(other_fee)
		realAmount              float64       // 实收金额 = 营业额 - 支出
		validOrderCountTotal    int64         // 销量合计
		realAmountTotal         float64       // 实收金额合计
		grossAmountTotal        float64       // 交易毛额合计
		transferRealAmountTotal float64       // 正实收金额转换后合计，用于计算占比
		expendTotal             float64       // 正支出合计，用于计算占比
		nRows                   []interface{} // 记录行数
	)
	nRows = make([]interface{}, 0, len(resp.Rows))
	realTotal := 0.0
	grossTotal := 0.0
	var orderCountTotal int64 = 0
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		// 营业额
		businessAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["business_amount"])), 64)
		expendAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["expend_amount"])), 64)
		realAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["real_amount"])), 64)
		businessAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", businessAmount), 64)
		grossAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["gross_amount"])), 64)
		transferRealAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["transfer_real_amount"])), 64)
		discountContribute, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["discount_amount1"])), 64)
		validOrderCount := cast.ToInt64(r["order_count_sale"]) + cast.ToInt64(r["order_count_refund"])
		productCount := cast.ToInt64(r["product_count"])
		var m = map[string]interface{}{
			"real_amount":          realAmount,         // 实收金额，下钻也使用
			"transfer_real_amount": transferRealAmount, // 实收金额（财务）
			"discount_contribute":  discountContribute, // 优惠组成
			"expend_amount":        expendAmount,       // 支出（交易）
			"business_amount":      businessAmount,     // 营业额
			"valid_order_count":    validOrderCount,    // 有效订单量，或销量，下钻使用销量
			"gross_amount":         grossAmount,        // 交易毛额，仅下钻使用
			"product_count":        productCount,       // 商品数量
		}
		if expendAmount < 0 {
			m["proportion_expend_amount"] = "负数不参与占比计算"
		} else {
			expendTotal += expendAmount
		}
		if transferRealAmount < 0 {
			m["proportion_transfer_real_amount"] = "负数不参与占比计算"
		} else {
			transferRealAmountTotal += transferRealAmount
		}
		if realAmount < 0.0 {
			m["proportion_real_amount"] = "负数不参与占比计算"
		} else {
			realTotal += realAmount
		}
		if validOrderCount < 0 {
			m["proportion_order_count"] = "负数不参与占比计算"
		} else {
			orderCountTotal += validOrderCount
		}
		if grossAmount < 0.0 {
			m["proportion_gross_amount"] = "负数不参与占比计算"
		} else {
			grossTotal += grossAmount
		}
		nRows = append(nRows, map[string]interface{}{
			cast.ToString(r["hours"]): m,
		})
		validOrderCountTotal += validOrderCount
		grossAmountTotal += cast.ToFloat64(r["gross_amount"])
	}
	if realTotal > 0 || orderCountTotal > 0 || grossTotal > 0 || transferRealAmountTotal > 0 || expendTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			for _, v := range r {
				m := v.(map[string]interface{})
				realAmount := cast.ToFloat64(m["real_amount"])
				orderCount := cast.ToInt64(m["valid_order_count"])
				grossAmount := cast.ToFloat64(m["gross_amount"])
				transfer := cast.ToFloat64(m["transfer_real_amount"])
				expend := cast.ToFloat64(m["expend_amount"])
				if expend > 0 && expendTotal > 0 {
					prop := expend / expendTotal
					m["proportion_expend_amount"] = prop
				}
				if transfer >= 0 && transferRealAmountTotal > 0 {
					prop := transfer / transferRealAmountTotal
					m["proportion_transfer_real_amount"] = prop
				}
				if realTotal > 0 && realAmount >= 0 { // 计算实收金额占比
					proportion := realAmount / realTotal
					m["proportion_real_amount"] = proportion
				}
				if orderCount >= 0 && orderCountTotal > 0 { // 计算销量占比
					proportion := float64(orderCount) / float64(orderCountTotal)
					m["proportion_order_count"] = proportion
				}
				if grossTotal > 0 && grossAmount >= 0 { // 计算交易毛额占比
					proportion := grossAmount / grossTotal
					m["proportion_gross_amount"] = proportion
				}
			}
		}
	}
	realAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["real_amount"])), 64)
	grossAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["gross_amount"])), 64)
	transferAmountTotal, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["transfer_real_amount"])), 64)
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	resp.Summary = map[string]interface{}{
		"real_amount":          realAmountTotal,
		"valid_order_count":    validOrderCountTotal,
		"gross_amount":         grossAmountTotal,
		"transfer_real_amount": transferAmountTotal,
	}
}
