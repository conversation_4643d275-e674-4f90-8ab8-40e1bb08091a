package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

// BusinessAmount 营业额
func BusinessAmount(ctx context.Context, query *model.CommonRequest) (*report.BusinessSituationResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.BusinessAmount] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.BusinessAmount(ctx, condition)
	// 组装查询到的结果数据
	packagingForBusinessAmount(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForBusinessAmount(ctx context.Context, query *model.CommonRequest, resp *report.BusinessSituationResponse) {
	packagingForOneReport(resp.Rows)
	packagingForOneReport(resp.CompareRows)
	packagingForOneReport(resp.CompareSameRows)
	for i := range resp.Rows {
		// 计算环比
		resp.Rows[i].CompareBusinessAmount = resp.Rows[i].BusinessAmount - resp.CompareRows[i].BusinessAmount
		resp.Rows[i].CompareRealAmount = resp.Rows[i].RealAmount - resp.CompareRows[i].RealAmount
		resp.Rows[i].CompareExpendAmount = resp.Rows[i].ExpendAmount - resp.CompareRows[i].ExpendAmount
		resp.Rows[i].CompareMerchantAllowance = resp.Rows[i].MerchantAllowance - resp.CompareRows[i].MerchantAllowance
		resp.Rows[i].CompareValidOrderCount = resp.Rows[i].ValidOrderCount - resp.CompareRows[i].ValidOrderCount
		resp.Rows[i].CompareRefundOrderCount = resp.Rows[i].RefundOrderCount - resp.CompareRows[i].RefundOrderCount
		resp.Rows[i].CompareCustomerPrice = resp.Rows[i].CustomerPrice - resp.CompareRows[i].CustomerPrice
		resp.Rows[i].CompareDiscountContribute = resp.Rows[i].DiscountContribute - resp.CompareRows[i].DiscountContribute
		resp.Rows[i].CompareTransferRealAmount = resp.Rows[i].TransferRealAmount - resp.CompareRows[i].TransferRealAmount
		resp.Rows[i].CompareAverageBusinessAmount = resp.Rows[i].AverageBusinessAmount - resp.CompareRows[i].AverageBusinessAmount
		resp.Rows[i].CompareAverageValidOrderCount = resp.Rows[i].AverageValidOrderCount - resp.CompareRows[i].AverageValidOrderCount
		resp.Rows[i].CompareProductCount = resp.Rows[i].ProductCount - resp.CompareRows[i].ProductCount
		resp.Rows[i].CompareCupCount = resp.Rows[i].CupCount - resp.CompareRows[i].CupCount
		resp.Rows[i].CompareStoreBusinessAmount = resp.Rows[i].StoreBusinessAmount - resp.CompareRows[i].StoreBusinessAmount
		resp.Rows[i].CompareTakeoutBusinessAmount = resp.Rows[i].TakeoutBusinessAmount - resp.CompareRows[i].TakeoutBusinessAmount
		resp.Rows[i].CompareStoreTransferRealAmount = resp.Rows[i].StoreTransferRealAmount - resp.CompareRows[i].StoreTransferRealAmount
		resp.Rows[i].CompareTakeoutTransferRealAmount = resp.Rows[i].TakeoutTransferRealAmount - resp.CompareRows[i].TakeoutTransferRealAmount
		// 计算同比
		resp.Rows[i].CompareSameBusinessAmount = resp.Rows[i].BusinessAmount - resp.CompareSameRows[i].BusinessAmount
		resp.Rows[i].CompareSameRealAmount = resp.Rows[i].RealAmount - resp.CompareSameRows[i].RealAmount
		resp.Rows[i].CompareSameExpendAmount = resp.Rows[i].ExpendAmount - resp.CompareSameRows[i].ExpendAmount
		resp.Rows[i].CompareSameMerchantAllowance = resp.Rows[i].MerchantAllowance - resp.CompareSameRows[i].MerchantAllowance
		resp.Rows[i].CompareSameValidOrderCount = resp.Rows[i].ValidOrderCount - resp.CompareSameRows[i].ValidOrderCount
		resp.Rows[i].CompareSameRefundOrderCount = resp.Rows[i].RefundOrderCount - resp.CompareSameRows[i].RefundOrderCount
		resp.Rows[i].CompareSameCustomerPrice = resp.Rows[i].CustomerPrice - resp.CompareSameRows[i].CustomerPrice
		resp.Rows[i].CompareSameDiscountContribute = resp.Rows[i].DiscountContribute - resp.CompareSameRows[i].DiscountContribute
		resp.Rows[i].CompareSameTransferRealAmount = resp.Rows[i].TransferRealAmount - resp.CompareSameRows[i].TransferRealAmount
		resp.Rows[i].CompareSameAverageBusinessAmount = resp.Rows[i].AverageBusinessAmount - resp.CompareSameRows[i].AverageBusinessAmount
		resp.Rows[i].CompareSameAverageValidOrderCount = resp.Rows[i].AverageValidOrderCount - resp.CompareSameRows[i].AverageValidOrderCount
		resp.Rows[i].CompareSameProductCount = resp.Rows[i].ProductCount - resp.CompareSameRows[i].ProductCount
		resp.Rows[i].CompareSameCupCount = resp.Rows[i].CupCount - resp.CompareSameRows[i].CupCount
		resp.Rows[i].CompareSameStoreBusinessAmount = resp.Rows[i].StoreBusinessAmount - resp.CompareSameRows[i].StoreBusinessAmount
		resp.Rows[i].CompareSameTakeoutBusinessAmount = resp.Rows[i].TakeoutBusinessAmount - resp.CompareSameRows[i].TakeoutBusinessAmount
		resp.Rows[i].CompareSameStoreTransferRealAmount = resp.Rows[i].StoreTransferRealAmount - resp.CompareSameRows[i].StoreTransferRealAmount
		resp.Rows[i].CompareSameTakeoutTransferRealAmount = resp.Rows[i].TakeoutTransferRealAmount - resp.CompareSameRows[i].TakeoutTransferRealAmount
	}
}

func packagingForOneReport(rows []*report.BusinessSituation) {
	for _, r := range rows {
		for i := range r.Child {
			switch r.Child[i].OrderType {
			case "TAKEOUT":
				r.TakeoutBusinessAmount += r.Child[i].BusinessAmount
				r.TakeoutTransferRealAmount += r.Child[i].TransferRealAmount
			default:
				r.StoreBusinessAmount += r.Child[i].BusinessAmount
				r.StoreTransferRealAmount += r.Child[i].TransferRealAmount
			}
		}
	}
}
