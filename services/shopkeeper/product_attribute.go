package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
	"sort"
	"strconv"
	"strings"
)

func ProductAttribute(ctx context.Context, query *model.CommonRequest) (*report.ProductAttributeResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductTop20Rank] receive body. Content: `%+v`\n", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.ProductAttribute(ctx, condition)
	// 组装查询结果
	packagingForProductAttribute(ctx, query, resp)
	return resp, err
}

func packagingForProductAttribute(ctx context.Context, query *model.CommonRequest, resp *report.ProductAttributeResponse) {
	// 获取商品编号和名称
	productIds := make([]int64, 0, len(resp.Rows))
	for _, r := range resp.Rows {
		productIds = append(productIds, r.ProductId)
	}
	productMaps := report2.GetProductMapsByIds(ctx, query, productIds)
	// 口味分布
	percentTotal := 0.0
	for i, _ := range resp.Rows {
		// 组装商品code和name
		productId := resp.Rows[i].ProductId
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"])
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[i].ProductCode = cast.ToString(productMaps[productId]["code"])
		resp.Rows[i].ProductName = pName
		// 口味分布
		if resp.Rows[i].Flavor == "" {
			resp.Rows[i].Flavor = "-"
		}
		// 占比保留4位小数
		resp.Rows[i].PercentProductCount = saveFourEffectiveDigits(resp.Rows[i].PercentProductCount)
		// 计算占比合计
		percentTotal += resp.Rows[i].PercentProductCount
	}
	// 计算误差
	diff := 1 - percentTotal
	if len(resp.Rows) > 0 && diff != 0 {
		// 误差给第一个不为零的
		for i, _ := range resp.Rows {
			if resp.Rows[i].ProductCount != 0 {
				resp.Rows[i].PercentProductCount = saveFourEffectiveDigits(resp.Rows[i].PercentProductCount + diff)
				break
			}
		}
	}
	// 图表
	getGraphs(resp)
}

func saveFourEffectiveDigits(f float64) (float float64) {
	float, _ = strconv.ParseFloat(fmt.Sprintf("%.4f", f), 64)
	return
}

const NOFEED string = "无加料"

func getGraphs(resp *report.ProductAttributeResponse) {
	/**
	1、初始化两个map
		accessMap: 存储加料信息，key是加料名称，value是加料数量
		attrMap: 保存属性，key是属性名，value是属性值map，属性值map的key是属性值，value是数量
	*/
	accessMap, attrMap := make(map[string]int64), make(map[string]map[string]int64)
	for _, row := range resp.Graphs {
		// 1.1 更新加料map
		// 遍历加料map
		for k, v := range row.Accessories {
			if name, ok := accessMap[k]; !ok {
				accessMap[k] = v
			} else {
				name += v
				accessMap[k] = name
			}
		}
		// 1.2 更新属性map
		for k, v := range row.SkuRemark {
			if attr, ok := attrMap[k]; !ok { // 如果还没有该属性记录，增加一项
				m := map[string]int64{ // 先增加一项属性值
					v: row.ProductCount,
				}
				attrMap[k] = m
			} else {
				if value, ok := attr[v]; !ok {
					// 如果有该属性，但没有该属性值，增加一个属性值
					attr[v] = row.ProductCount
				} else {
					value += row.ProductCount
					attr[v] = value
				}
			}
		}
	}
	// 2、将上面的map中的内容转成数组
	// 2.1 加料map转成加料数组
	feeds := make(report.Feeds, 0)
	for k, v := range accessMap {
		feeds = append(feeds, &report.Feed{
			FeedName:  k,
			FeedCount: v,
		})
	}
	sort.Sort(feeds)
	var sumFeedCount float64
	for _, row := range feeds {
		sumFeedCount += float64(row.FeedCount)
	}
	if sumFeedCount > 0 {
		var sumFeedCountPercent float64
		for _, row := range feeds {
			row.FeedCountPercent = saveFourEffectiveDigits(float64(row.FeedCount) / sumFeedCount)
			sumFeedCountPercent += row.FeedCountPercent
		}
		diff := 1 - sumFeedCountPercent
		if diff != 0 {
			for _, row := range feeds {
				if row.FeedCount != 0 {
					row.FeedCountPercent += diff
					break
				}
			}
		}
	}

	// 2.2、属性map转成属性数组
	attrNames := make([]*report.Attribute, 0)
	for attrName, attrValueMap := range attrMap { // key是属性名，value是属性值map
		attrValues := make(report.AttributeNameValues, 0)
		for attrValue, count := range attrValueMap {
			attrValues = append(attrValues, &report.AttributeNameValue{
				AttributeValueName: attrValue,
				ProductCount:       count,
			})
		}
		sort.Sort(attrValues)
		var sumAttrCount float64
		for _, row := range attrValues {
			sumAttrCount += float64(row.ProductCount)
		}
		if sumAttrCount > 0 {
			var sumAttrCountPercent float64
			for _, row := range attrValues {
				row.ProductCountPercent = saveFourEffectiveDigits(float64(row.ProductCount) / sumAttrCount)
				sumAttrCountPercent += row.ProductCountPercent
			}
			diff := 1 - sumAttrCountPercent
			if diff != 0 {
				for _, row := range attrValues {
					if row.ProductCount != 0 {
						row.ProductCountPercent += diff
						break
					}
				}
			}
		}
		attrNames = append(attrNames, &report.Attribute{
			AttributeName:       attrName,
			AttributeNameValues: attrValues,
		})
	}

	if len(resp.Rows) > 0 && len(resp.Graphs) > 0 {
		resp.Graphs[0].ProductCode = resp.Rows[0].ProductCode
		resp.Graphs[0].ProductName = resp.Rows[0].ProductName
		resp.Graphs[0].Feeds = feeds
		resp.Graphs[0].AttributeNames = attrNames
	}
}
