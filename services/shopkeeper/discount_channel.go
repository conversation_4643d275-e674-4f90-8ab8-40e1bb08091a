package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strconv"
)

// DiscountChannel 折扣渠道
func DiscountChannel(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.DiscountChannel] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.DiscountChannel(ctx, condition)
	// 组装查询到的结果数据
	packagingForDiscountChannel(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForDiscountChannel(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		discountAmountTotal float64 // 折扣金额合计
		discountTotal       float64 // 正折扣金额合计，用于计算占比
	)
	channelMaps := report.GetDiscountChannelMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		channelId := cast.ToInt64(r["channel_id"])
		channelCode := channelMaps[channelId]["code"]
		channelName := channelMaps[channelId]["name"]
		discountAmount, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["discount_amount"])), 64)
		// 组装每一条记录，形成每一行记录
		var record = map[string]interface{}{
			"channel_id":      channelId,                  // 折扣渠道id
			"channel_code":    cast.ToString(channelCode), // 折扣渠道code
			"channel_name":    cast.ToString(channelName), // 折扣渠道名称
			"discount_amount": discountAmount,             // 折扣金额
		}
		if discountAmount <= 0 {
			record["proportion"] = "负数不参与占比计算"
		} else {
			discountTotal += discountAmount
		}
		discountAmountTotal += discountAmount
		nRows = append(nRows, record)
	}
	if discountTotal > 0 {
		for _, row := range nRows {
			r := row.(map[string]interface{})
			discountAmount := cast.ToFloat64(r["discount_amount"])
			if discountAmount > 0 {
				proportion, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", discountAmount/discountTotal), 64)
				r["proportion"] = proportion
			}
		}
	}
	// 查询的行数据
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	discountAmountTotal, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", discountAmountTotal), 64)
	resp.Summary = map[string]interface{}{
		"discount_amount": discountAmountTotal,
	}
}
