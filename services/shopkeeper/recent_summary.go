package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
	"time"
)

func QueryRecentSummary(ctx context.Context, query *model.CommonRequest) (*report.RecentSummaryResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.QueryRecentSummary] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	ch := make(chan *report.RecentSummaryResponse, 1)
	chCompare := make(chan *report.RecentSummaryResponse, 1)
	go doQuery(ctx, condition, ch)

	compareCondition := *condition
	compareStart := strings.Split(compareCondition.CompareStart, "T")[0]
	compareEnd := strings.Split(compareCondition.CompareEnd, "T")[0]
	compareCondition.Start, err = time.Parse(config.DateFormat, compareStart)
	compareCondition.End, err = time.Parse(config.DateFormat, compareEnd)
	go doQuery(ctx, &compareCondition, chCompare)

	result := <-ch
	resultCompare := <-chCompare
	doCompare(result, resultCompare)
	return result, err
}

func doCompare(result *report.RecentSummaryResponse, compare *report.RecentSummaryResponse) {
	s := result.Summary
	c := compare.Summary
	s.CompareBusinessAmount = s.BusinessAmount - c.BusinessAmount
	s.CompareFinanceExpendAmount = s.FinanceExpendAmount - c.FinanceExpendAmount
	s.CompareFinanceRealAmount = s.FinanceRealAmount - c.FinanceRealAmount
	s.ComparePayAmount = s.PayAmount - c.PayAmount
	s.CompareTransferRealAmount = s.TransferRealAmount - c.TransferRealAmount
	s.CompareValidOrderCount = s.ValidOrderCount - c.ValidOrderCount
}

func doQuery(ctx context.Context, condition *model.RepoCondition, ch chan *report.RecentSummaryResponse) {
	resp, _ := repo.DefaultAllTicketSalesRepository.RecentSummary(ctx, condition)
	ch <- resp
}
