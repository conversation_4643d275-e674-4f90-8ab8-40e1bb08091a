package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

// 小掌柜-门店营业额Top10排行
func StoreBusinessTop10Rank(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.storeBusinessTop10Rank] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.BusinessRank(ctx, condition)
	// 组装查询到的结果数据
	packagingForBusinessRank(ctx, query, resp)
	return resp, err
}

func packagingForBusinessRank(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	storeMaps := GetStoreMaps(ctx, query, resp)
	for i := range resp.Rows {
		r := cast.ToStringMap(resp.Rows[i])
		storeId := cast.ToInt64(r["store_id"])
		storeCode := storeMaps[storeId]["code"]
		storeName := storeMaps[storeId]["name"]
		alias := storeMaps[storeId]["alias"]
		r["code"] = storeCode
		r["name"] = storeName
		r["alias"] = alias
		resp.Rows[i] = r
	}
}
