package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
	"strings"
)

func LogisticsTrend(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.LogisticsRank] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultShopKeeper.LogisticsTrend(ctx, condition)
	// 组装查询到的结果数据
	packagingForLogisticsTrend(ctx, query, resp)
	return resp, err
}

func packagingForLogisticsTrend(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	var (
		projectedIncome float64       // 实收金额
		logisticsName   string        // 物流名称
		busDate         string        // 营业时间段
		nRows           []interface{} // 记录行数
	)
	nRows = make([]interface{}, 0, len(resp.Rows))
	var tmpMap = make(map[string]map[string]float64)
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		logisticsName = strings.ToLower(cast.ToString(r["order_type"]))
		//businessAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["business_amount"])), 64)
		//expendAmount, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["expend_amount"])), 64)
		// 实收金额
		projectedIncome, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(r["projected_income"])), 64) // 实收金额
		if tmpMap[logisticsName] == nil {
			tmpMap[logisticsName] = make(map[string]float64)
		}
		busDate = cast.ToString(r["bus_date"])
		tmpMap[logisticsName][busDate] = projectedIncome
	}
	for k, v := range tmpMap {
		nRows = append(nRows, map[string]interface{}{
			k: v,
		})
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
	resp.Summary = nil
}
