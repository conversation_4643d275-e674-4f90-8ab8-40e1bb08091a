package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func PeriodNew(ctx context.Context, query *model.CommonRequest) (*report.PeriodNewResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.PeriodNew] receive body. Content: `%+v`\n", trackId, query)
	// 组装查询条件
	condition := query.ToRepoCondition([]string{})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验TopN
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.PeriodNew(ctx, condition)
	// 组装查询结果
	packagingForPeriodNew(ctx, query, resp)
	return resp, err
}

type PeriodNewPeriod struct {
	PeriodName string
	PeriodTime string
}

// 早市6:00-12:00；午市12:00-14:00；下午14:00-18:00；晚市18:00-次日6:00
var periodNewPeriods = []PeriodNewPeriod{
	{
		PeriodName: "早市",
		PeriodTime: "06:00-12:00",
	},
	{
		PeriodName: "午市",
		PeriodTime: "12:00-14:00",
	},
	{
		PeriodName: "下午",
		PeriodTime: "14:00-18:00",
	},
	{
		PeriodName: "晚市",
		PeriodTime: "18:00-次日6:00",
	},
}

func packagingForPeriodNew(ctx context.Context, query *model.CommonRequest, resp *report.PeriodNewResponse) {
	rows := make([]*report.PeriodNewRow, len(periodNewPeriods))
	for i, period := range periodNewPeriods {
		rows[i] = &report.PeriodNewRow{
			PeriodName: period.PeriodName,
			PeriodTime: period.PeriodTime,
		}
		for _, row := range resp.Rows {
			if row.PeriodName == period.PeriodName {
				rows[i].RealAmount = row.RealAmount
				rows[i].FinanceRealAmount = row.FinanceRealAmount
				rows[i].BusinessAmount = row.BusinessAmount
				rows[i].ValidTicketCount = row.ValidTicketCount
				break
			}
		}
	}
	resp.Rows = rows
}
