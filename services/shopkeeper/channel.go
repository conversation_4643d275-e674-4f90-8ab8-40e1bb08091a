package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report2 "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

// Channel 小掌柜-渠道收入
func Channel(ctx context.Context, query *model.CommonRequest) (*report2.ChannelDistributeResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.channel] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	// 调用小掌柜营业额等数据
	resp, err := repo.DefaultShopKeeper.Channel(ctx, condition)
	// 组装查询到的结果数据
	packagingForChannel(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForChannel(ctx context.Context, query *model.CommonRequest, resp *report2.ChannelDistributeResponse) {
	channelIds := make([]int64, 0)
	for _, r := range resp.Rows {
		channelIds = append(channelIds, r.ChannelId)
	}
	channelMaps := report.GetChannelMapsByIds(ctx, query, channelIds)
	type CompareAndSame struct {
		CompareRow     *report2.ChannelDistribute
		CompareSameRow *report2.ChannelDistribute
	}
	rowsMap := make(map[int64]*CompareAndSame) // 相同channel_id的环比和同比信息放在一个结构体里
	for i := range resp.Rows {
		channelId := resp.Rows[i].ChannelId
		resp.Rows[i].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.Rows[i].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		// 初始化
		newStruct := &CompareAndSame{
			CompareRow:     new(report2.ChannelDistribute),
			CompareSameRow: new(report2.ChannelDistribute),
		}
		rowsMap[channelId] = newStruct
	}
	for _, row := range resp.CompareRows {
		if rowsMap[row.ChannelId] == nil {
			continue
		}
		rowsMap[row.ChannelId].CompareRow = row
	}
	for _, row := range resp.CompareSameRows {
		if rowsMap[row.ChannelId] == nil {
			continue
		}
		rowsMap[row.ChannelId].CompareSameRow = row
	}

	for i := range resp.Rows {
		channelId := resp.Rows[i].ChannelId
		resp.Rows[i].CompareBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[channelId].CompareRow.BusinessAmount
		if rowsMap[channelId].CompareRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareBusinessAmount = resp.Rows[i].CompareBusinessAmount / rowsMap[channelId].CompareRow.BusinessAmount
		}
		resp.Rows[i].CompareSameBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[channelId].CompareSameRow.BusinessAmount
		if rowsMap[channelId].CompareSameRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareSameBusinessAmount = resp.Rows[i].CompareSameBusinessAmount / rowsMap[channelId].CompareSameRow.BusinessAmount
		}
	}
}
