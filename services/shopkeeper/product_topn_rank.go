package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strings"
)

func ProductTopNRank(ctx context.Context, query *model.CommonRequest) (*report.ProductTopNRankResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.ProductTopNRank] receive body. Content: `%+v`\n", trackId, query)
	// 组装查询条件
	condition := query.ToRepoCondition([]string{"gross_amount", "net_amount", "item_count", "weight_count"})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验TopN
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.ProductTopNRank(ctx, condition)
	// 组装查询结果
	packagingForProductTopNRank(ctx, query, resp)
	return resp, err
}

func packagingForProductTopNRank(ctx context.Context, query *model.CommonRequest, resp *report.ProductTopNRankResponse) {
	productIds := make([]int64, 0, len(resp.Rows))
	for _, r := range resp.Rows {
		productIds = append(productIds, r.ProductId)
	}
	productMaps := report2.GetProductMapsByIds(ctx, query, productIds)
	for i := range resp.Rows {
		productId := resp.Rows[i].ProductId
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"])
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[i].ProductCode = cast.ToString(productMaps[productId]["code"])
		resp.Rows[i].ProductName = pName
	}
}
