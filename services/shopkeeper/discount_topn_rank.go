package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func DiscountTopNRank(ctx context.Context, query *model.CommonRequest) (*report.DiscountTopNRankResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.DiscountTopNRank] receive body. Content: `%+v`\n", trackId, query)
	// 组装查询条件
	condition := query.ToRepoCondition([]string{"discount_name", "discount_amount", "discount_count"})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验TopN
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds
	resp, err := repo.DefaultShopKeeper.DiscountTopNRank(ctx, condition)
	// 组装查询结果
	packagingForDiscountTopNRank(ctx, query, resp)
	return resp, err
}

func packagingForDiscountTopNRank(ctx context.Context, query *model.CommonRequest, resp *report.DiscountTopNRankResponse) {
	for i, row := range resp.Rows {
		// 计算占比
		if row.DiscountAmount > 0 && resp.Summary.DiscountAmount > 0 {
			resp.Rows[i].DiscountAmountPercent = row.DiscountAmount / resp.Summary.DiscountAmount
		}
		if row.DiscountCount > 0 && resp.Summary.DiscountCount > 0 {
			resp.Rows[i].DiscountCountPercent = row.DiscountCount / resp.Summary.DiscountCount
		}
	}
}
