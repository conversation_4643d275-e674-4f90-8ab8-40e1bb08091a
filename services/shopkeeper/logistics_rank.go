package shopkeeper

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report2 "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func LogisticsRank(ctx context.Context, query *model.CommonRequest) (*report2.LogisticsRankResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.shopkeeper.LogisticsRank] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	domain := "hipos.mobile"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, condition.RegionSearchIds, query.Lan)
	if err != nil {
		return nil, err
	}
	condition.RegionSearchIds = storeIds

	resp, err := repo.DefaultShopKeeper.LogisticsRank(ctx, condition)
	// 组装查询到的结果数据
	packagingForLogisticsRank(ctx, query, resp)
	return resp, err
}

func packagingForLogisticsRank(ctx context.Context, query *model.CommonRequest, resp *report2.LogisticsRankResponse) {
	type CompareAndSame struct {
		CompareRow     *report2.LogisticsRank
		CompareSameRow *report2.LogisticsRank
	}
	rowsMap := make(map[string]*CompareAndSame)
	for i := range resp.Rows {
		orderType := resp.Rows[i].OrderType
		switch orderType {
		// todo...
		case "":

		}
		// 初始化
		newStruct := &CompareAndSame{
			CompareRow:     new(report2.LogisticsRank),
			CompareSameRow: new(report2.LogisticsRank),
		}
		rowsMap[orderType] = newStruct
	}
	for _, row := range resp.CompareRows {
		if rowsMap[row.OrderType] == nil {
			continue
		}
		rowsMap[row.OrderType].CompareRow = row
	}
	for _, row := range resp.CompareSameRows {
		if rowsMap[row.OrderType] == nil {
			continue
		}
		rowsMap[row.OrderType].CompareSameRow = row
	}

	for i := range resp.Rows {
		orderType := resp.Rows[i].OrderType
		resp.Rows[i].CompareBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[orderType].CompareRow.BusinessAmount
		if rowsMap[orderType].CompareRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareBusinessAmount = resp.Rows[i].CompareBusinessAmount / rowsMap[orderType].CompareRow.BusinessAmount
		}
		resp.Rows[i].CompareSameBusinessAmount = resp.Rows[i].BusinessAmount - rowsMap[orderType].CompareSameRow.BusinessAmount
		if rowsMap[orderType].CompareSameRow.BusinessAmount != 0 {
			resp.Rows[i].IncreaseCompareSameBusinessAmount = resp.Rows[i].CompareSameBusinessAmount / rowsMap[orderType].CompareSameRow.BusinessAmount
		}
	}
}
