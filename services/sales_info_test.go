package services

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/test"
	"testing"
)

const (
	ticket = "{\"ticket_id\":\"75432408-84c9-11ed-a585-01004449cf12\",\"ticket_no\":\"00002\",\"ticketUno\":\"1008001822122600002\",\"start_time\":\"2022-12-26 11:00:30\",\"end_time\":\"2022-12-26 11:00:30\",\"bus_date\":\"2022-12-26\",\"pos\":{\"id\":\"4698468256435261441\",\"code\":\"10080018\",\"device_id\":\"NDA6NjI6MzE6MTY6MDA6M2I=\",\"device_code\":\"HiPos_03\"},\"operator\":{\"id\":\"4660075737099829248\",\"loginId\":\"56dc9078-84c9-11ed-a584-01004449cf12\",\"name\":\"test\",\"code\":\"001\",\"login_time\":\"2022-12-26 10:59:39\"},\"amounts\":{\"taxAmount\":0,\"gross_amount\":-12,\"pay_amount\":-12,\"net_amount\":-12,\"discount_amount\":0,\"removezero_amount\":0,\"rounding\":0,\"overflow_amount\":0,\"changeAmount\":0,\"serviceFee\":0,\"tip\":0,\"projected_income\":-12,\"merchant_discount_amount\":0,\"platform_discount_amount\":0,\"total_amount\":0,\"taxIncluded\":false,\"otherFee\":0,\"receivable\":-12,\"real_amount\":-12,\"business_amount\":-12,\"expend_amount\":0,\"payment_transfer_amount\":0,\"discount_transfer_amount\":0,\"store_discount_amount\":0,\"discount_merchant_contribute\":0,\"discount_platform_contribute\":0,\"discount_buyer_contribute\":0,\"discount_other_contribute\":0,\"pay_merchant_contribute\":0,\"pay_platform_contribute\":0,\"pay_buyer_contribute\":-12,\"pay_other_contribute\":0,\"delivery_fee\":0,\"delivery_fee_for_platform\":0,\"delivery_fee_for_merchant\":0,\"package_fee\":0},\"takemealNumber\":\"50001\",\"qty\":-1,\"status\":\"REFUND\",\"refundInfo\":{\"refund_id\":\"\",\"ref_ticket_id\":\"58473e59-84c9-11ed-a584-01004449cf12\",\"ref_ticket_no\":\"00001\",\"refund_reason\":\"不要了哦\",\"refund_code\":\"002\",\"refund_side\":\"MERCHANT\",\"refund_no\":\"\"},\"channel\":{\"source\":\"POS\",\"deviceType\":\"PC\",\"orderType\":\"DINEIN\",\"deliveryType\":\"REALTIME\",\"tpName\":\"\",\"code\":\"POS\",\"id\":\"4660081984649854976\",\"mapping_code\":\"pos\"},\"products\":[{\"id\":\"4704923405298761728\",\"name\":\"测试物料\",\"code\":\"7106\",\"seq_id\":1,\"price\":-12,\"pos_price\":0,\"amount\":-12,\"qty\":-1,\"weight\":-1,\"has_weight\":false,\"discount_amount\":0,\"type\":\"NORMAL\",\"accessories\":null,\"combo_items\":null,\"remark\":\"\",\"operation_records\":\"\",\"skuRemark\":null,\"spellUsers\":null,\"taxAmount\":0,\"net_amount\":-12,\"image\":\"\",\"sum_amount\":-12,\"sum_discount_amount\":0,\"sum_net_amount\":-12,\"has_make_span\":false,\"avg_make_span\":0,\"unit\":{\"code\":\"12\",\"unit_id\":\"4488983570144067584\",\"name\":\"件\"},\"serving_size\":1,\"invoice_amount\":-12,\"rate\":0,\"tax_code\":\"\",\"finance_real_amount\":-12,\"transfer_amount\":0,\"jtp_items\":[{\"pid\":\"4704923405298761728\",\"bom_id\":\"\",\"code\":\"7106\",\"name\":\"测试物料\",\"rate\":0,\"qty\":1,\"total\":-1,\"pos_price\":0,\"jt_price\":0,\"unit\":\"件\"}],\"discount_transfer_amount\":0,\"delivery_weight\":0,\"plus_price\":0}],\"payments\":[{\"id\":\"4660084535914954752\",\"seq_id\":\"6c326b99-84c9-11ed-a584-01004449cf12\",\"name\":\"现金\",\"pay_amount\":-12,\"receivable\":-12,\"change\":0,\"overflow\":0,\"rounding\":0,\"pay_time\":\"2022-12-26 11:00:30\",\"trans_code\":\"89\",\"tpTransactionNo\":\"\",\"tp_allowance\":0,\"merchant_allowance\":0,\"trans_name\":\"\",\"price\":0,\"cost\":-12,\"real_amount\":-12,\"has_invoiced\":false,\"platform_allowance\":0,\"payerNo\":\"\"}],\"table\":{\"id\":\"\",\"zone_id\":\"\",\"tableNo\":\"\",\"no\":\"\",\"people\":1,\"temporary\":false},\"people\":-1,\"room_no\":\"-1\",\"order_time_type\":\"BFAST\",\"shiftNumber\":\"100800182022122600003\",\"taxList\":[{\"amount\":0,\"subTotal\":0,\"code\":\"\",\"name\":\"\",\"rate\":0.06}],\"takeaway_info\":{\"order_method\":\"\",\"tp_order_id\":\"1008001822122600001\",\"order_time\":\"\",\"description\":\"\",\"consignee\":\"\",\"phone_list\":null,\"tp\":\"\",\"source\":\"\",\"source_order_id\":\"\",\"day_seq\":\"\",\"delivery_type\":0,\"delivery_name\":\"\",\"delivery_poi_address\":\"\",\"invoice_title\":\"\",\"waiting_time\":\"\",\"tableware_num\":0,\"send_fee\":0,\"package_fee\":0,\"delivery_time\":\"\",\"deliver_time\":\"\",\"take_meal_sn\":\"\",\"partnerPlatformId\":0,\"partnerPlatformName\":\"\",\"wxName\":\"\",\"isHighPriority\":false,\"takeoutType\":\"\",\"originalOrderNo\":\"\",\"selfDelivery\":false,\"delivery_phone\":\"\",\"platform_send_fee\":0,\"merchant_send_fee\":0,\"send_fee_for_platform\":0,\"send_fee_for_merchant\":0,\"invoice_provider\":\"hangxing\",\"invoice_amount\":\"12\",\"invoice_url\":\"\",\"demotion_level\":0,\"delivery_weight\":0},\"store\":{\"id\":\"4616235875167207424\",\"code\":\"166\",\"secondCode\":\"166\",\"companyId\":\"\",\"partnerId\":\"1092\",\"scopeId\":\"\",\"branchId\":\"\"},\"fees\":null,\"fees_no_account\":null,\"timeZone\":\"+08:00,28800\",\"efficiency\":{\"confirmed_time\":\"\",\"made_time\":\"\",\"assigned_time\":\"\",\"arrived_time\":\"\",\"fetched_time\":\"\",\"delivered_time\":\"\",\"make_span\":0,\"avg_make_span\":0,\"arrive_span\":0,\"deliver_span\":0},\"pending_sync_member\":true,\"id\":4704927094969270272}"
)

func Test_SaveAbnormalSalesInfo(t *testing.T) {
	test.InitTest()
	ctx := context.WithValue(context.Background(), "partner_id", 1092)
	ctx = context.WithValue(ctx, config.TrackId, 1)
	SaveAbnormalSalesInfo(ctx, ticket)
}
