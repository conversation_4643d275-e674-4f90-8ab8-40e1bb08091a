package ticket

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"math"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

type Product struct {
	ProductId         string
	ProductCode       string
	GrossAmount       float64
	NetAmount         float64
	DiscountAmount    float64
	Cost              float64
	TpAllowance       float64
	MerchantAllowance float64
	PlatformAllowance float64
	TransferAmount    float64
	ProductCount      int
	IsAccessory       bool
	ComboType         int
	Weight            float64 // 重量/份量
	HasWeight         bool    // 是否称重商品
	Unit              string  // 单位
}

// 拆解折扣统计表
func resolveDiscountAmount(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	bd, _ := time.Parse(config.DateFormat, ticket.BusDate)
	et := ticket.GetOrderTime()

	storeTagCodes, storeTagNames := storeTagCodesAndNames(ticket)

	type Promotion struct {
		Id                     string
		Code                   string
		Name                   string
		Amount                 float64
		Qty                    int
		ProductId              int64
		ProductCode            string
		NetAmount              float64 // 商品实收
		RealAmount             float64
		GrossAmount            float64 // 商品流水
		DiscountAmount         float64 // 商品分摊到的折扣金额
		MerchantDiscount       float64
		DiscountTransferAmount float64 // 商品分摊到的折扣转支付
		Cost                   float64 // 商品分摊到的用户实际购买金额
		TpAllowance            float64 // 商品分摊到的第三方补贴金额
		MerchantAllowance      float64 // 商品分摊到的商品补贴金额
		PlatformAllowance      float64 // 商品分摊到的平台补贴金额
		StoreDiscountAmount    float64
		ProductCount           int // 商品数量

		SumDiscountAmount    float64
		SumCost              float64
		SumTpAllowance       float64
		SumMerchantAllowance float64
		SumPlatformAllowance float64
		SumTransferAmount    float64

		ComboType         int
		IsAccessory       bool
		Weight            float64 // 重量/份量
		HasWeight         bool    // 是否称重商品
		Unit              string  // 单位
		PromotionCateId   string
		PromotionCateName string
		CouponChannelCode string
		CouponChannelName string

		products []*Product
	}
	tmp := make(map[string]*Promotion)
	for _, p := range ticket.Promotions {
		var productInfos []*Product // 该折扣方式涉及到的商品结果集
		if len(p.Products) > 0 {
			// 指定折扣
			productInfos = getAssignPromotionProductInfo(p, ticket.Products)
		} else {
			// 整单折扣
			productInfos = getAllPromotionProductInfo(p, ticket.Products)
		}
		products := resolve(productInfos) // 相同商品合并
		// 获取卡券渠道
		couponChannelCode, couponChannelName := "", ""
		for _, coupon := range ticket.Coupons {
			if coupon.Id == p.PromotionInfo.CouponId {
				couponChannelCode, couponChannelName = coupon.ChannelCode, coupon.ChannelName
			}
		}
		var v *Promotion
		var in bool
		if v, in = tmp[p.PromotionInfo.Name]; !in {
			v = &Promotion{Id: p.PromotionInfo.PromotionId, Code: p.PromotionInfo.PromotionCode, Name: p.PromotionInfo.Name,
				PromotionCateId: p.PromotionInfo.PromotionCateId, PromotionCateName: p.PromotionInfo.PromotionCateName,
				CouponChannelCode: couponChannelCode, CouponChannelName: couponChannelName,
			}
		}
		v.Amount += p.Source.Discount
		v.Qty++
		v.RealAmount += p.Source.RealAmount                  // 商家实收
		v.MerchantDiscount += p.Source.MerchantDiscount      // 商家优惠承担
		v.SumDiscountAmount += p.Source.Discount             // 折扣金额
		v.SumCost += p.Source.Cost                           // 用户实际购买金额
		v.SumTpAllowance += p.Source.TpAllowance             // 第三方优惠承担（财务）
		v.SumMerchantAllowance += p.Source.MerchantAllowance // 商家补贴金额
		v.SumPlatformAllowance += p.Source.PlatformAllowance // 平台补贴金额（财务）
		v.StoreDiscountAmount += p.Source.StoreDiscount      // 门店折扣
		v.SumTransferAmount += p.Source.TransferAmount       // 折扣转支付

		v.products = updateProducts(products, v.products) // 该折扣方式涉及到的商品列表
		tmp[p.PromotionInfo.Name] = v
	}

	type PPAC struct {
		PromotionName string
		ProductId     string
		IsAccessory   bool
		ComboType     int
	}
	promotionMap := make(map[PPAC]*Promotion)
	for name, v := range tmp {
		for _, product := range v.products {
			// 折扣id、商品id、是否加料、商品套餐类型组成key
			key := PPAC{
				PromotionName: name,
				ProductId:     product.ProductId,
				IsAccessory:   product.IsAccessory,
				ComboType:     product.ComboType,
			}
			if _, ok := promotionMap[key]; !ok {
				promotionMap[key] = &Promotion{
					Id:                     v.Id,
					Code:                   v.Code,
					Name:                   v.Name,
					Amount:                 v.Amount,
					Qty:                    v.Qty,
					ProductId:              cast.ToInt64(product.ProductId),
					ProductCode:            product.ProductCode,
					ProductCount:           product.ProductCount,
					NetAmount:              product.NetAmount,
					RealAmount:             v.RealAmount,
					GrossAmount:            product.GrossAmount,
					DiscountAmount:         product.DiscountAmount,
					MerchantDiscount:       v.MerchantDiscount,
					DiscountTransferAmount: product.TransferAmount,
					Cost:                   product.Cost,
					TpAllowance:            product.TpAllowance,
					MerchantAllowance:      product.MerchantAllowance,
					PlatformAllowance:      product.PlatformAllowance,
					StoreDiscountAmount:    v.StoreDiscountAmount,
					SumDiscountAmount:      v.SumDiscountAmount,
					SumCost:                v.SumCost,
					SumTpAllowance:         v.SumTpAllowance,
					SumMerchantAllowance:   v.SumMerchantAllowance,
					SumPlatformAllowance:   v.SumPlatformAllowance,
					SumTransferAmount:      v.SumTransferAmount,
					ComboType:              product.ComboType,
					IsAccessory:            product.IsAccessory,
					// 增加重量/份量、是否称重商品、单位
					Weight:            product.Weight,
					HasWeight:         product.HasWeight,
					Unit:              product.Unit,
					PromotionCateId:   v.PromotionCateId,
					PromotionCateName: v.PromotionCateName,
					CouponChannelCode: v.CouponChannelCode,
					CouponChannelName: v.CouponChannelName,
				}
			} else {
				p := promotionMap[key]
				//p.ProductCount += product.ProductCount
				//p.NetAmount += product.NetAmount
				//p.GrossAmount += product.GrossAmount
				p.DiscountAmount += product.DiscountAmount
				p.DiscountTransferAmount += product.TransferAmount
				p.Cost += product.Cost
				p.TpAllowance += product.TpAllowance
				p.MerchantAllowance += product.MerchantAllowance
				p.PlatformAllowance += product.PlatformAllowance
				p.Weight += product.Weight
			}
		}
	}
	for _, item := range promotionMap {
		rsm := &model.SalesDiscountAmount{
			Id:                        utils.GetHexUUid(ctx),
			PartnerId:                 cast.ToInt64(ticket.Store.PartnerId),
			ScopeId:                   cast.ToInt64(ticket.Store.ScopeId),
			BusDate:                   time.Date(bd.Year(), bd.Month(), bd.Day(), 0, 0, 0, 0, time.UTC),
			StoreId:                   utils.StringMust2Int(ticket.Store.Id),
			EticketId:                 ticket.Id,
			OrderTime:                 et,
			Refunded:                  ticket.Status == model.REFUND || ticket.Status == model.PARTIALREFUND,
			PromotionId:               utils.StringMust2Int(item.Id),
			PromotionCode:             item.Code,
			PromotionName:             item.Name,
			ChannelId:                 cast.ToInt64(ticket.Channel.Id),
			ChannelName:               ticket.Channel.TpName,
			OrderType:                 ticket.Channel.OrderType,
			Created:                   time.Now().UTC(),
			Amount:                    item.Amount,
			Qty:                       item.Qty,
			EticketCount:              1,
			OrderStatus:               ticket.Status,
			RealAmount:                item.RealAmount,
			MerchantDiscountAmount:    item.MerchantDiscount,
			StoreDiscountAmount:       item.StoreDiscountAmount,
			SumDiscountAmount:         item.SumDiscountAmount,
			SumCost:                   item.SumCost,
			SumMerchantAllowance:      item.SumMerchantAllowance,
			SumTpAllowance:            item.SumTpAllowance,
			SumPlatformAllowance:      item.SumPlatformAllowance,
			SumDiscountTransferAmount: item.SumTransferAmount,

			// 商品信息
			ProductId:              item.ProductId,
			ProductCode:            item.ProductCode,
			IsAccessory:            item.IsAccessory,
			ComboType:              item.ComboType,
			GrossAmount:            item.GrossAmount,
			NetAmount:              item.NetAmount,
			DiscountAmount:         item.DiscountAmount, // 折扣金额
			Cost:                   item.Cost,
			TpAllowance:            item.TpAllowance,
			MerchantAllowance:      item.MerchantAllowance,
			PlatformAllowance:      item.PlatformAllowance,
			DiscountTransferAmount: item.DiscountTransferAmount,
			ProductCount:           item.ProductCount,
			Weight:                 item.Weight,
			HasWeight:              item.HasWeight,
			Unit:                   item.Unit,
			PromotionCateId:        item.PromotionCateId,
			PromotionCateName:      item.PromotionCateName,
			StoreTags:              storeTagCodes,
			StoreTagNames:          storeTagNames,
			CouponChannelCode:      item.CouponChannelCode,
			CouponChannelName:      item.CouponChannelName,
		}
		rsm.BusDateYear = time.Date(bd.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		rsm.BusDateMonth = time.Date(bd.Year(), bd.Month(), 1, 0, 0, 0, 0, time.UTC)
		var sub int
		if sub = int(rsm.BusDate.Weekday()) - 1; sub < 0 {
			sub = 6
		}
		if rsm.Refunded {
			rsm.EticketCount = -rsm.EticketCount
			rsm.Qty = -rsm.Qty
		}

		if rsm.BusDateWeek = rsm.BusDate.Add(-time.Duration(sub) * 24 * time.Hour); rsm.BusDateWeek.Year() != rsm.BusDate.Year() {
			rsm.BusDateWeek = time.Date(rsm.BusDate.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		}
		resolveResult = append(resolveResult, rsm)
	}
	fmt.Println(len(resolveResult))
	return
}

type PAC struct {
	ProductId   string
	IsAccessory bool
	ComboType   int
}

func resolve(infos []*Product) []*Product {
	products := make([]*Product, 0)
	m := make(map[PAC]*Product)
	for _, product := range infos {
		pac := PAC{
			ProductId:   product.ProductId,
			IsAccessory: product.IsAccessory,
			ComboType:   product.ComboType,
		}
		if p, ok := m[pac]; !ok {
			m[pac] = product
		} else {
			p.GrossAmount += product.GrossAmount
			p.NetAmount += product.NetAmount
			p.DiscountAmount += product.DiscountAmount
			p.Cost += product.Cost
			p.TpAllowance += product.TpAllowance
			p.MerchantAllowance += product.MerchantAllowance
			p.PlatformAllowance += product.PlatformAllowance
			p.TransferAmount += product.TransferAmount
			p.ProductCount += product.ProductCount
		}
	}
	for _, v := range m {
		products = append(products, v)
	}
	return products
}

type Amounts struct {
	Discount          float64
	MerchantAllowance float64
	Cost              float64
	TpAllowance       float64
	PlatformAllowance float64
	TransferAmount    float64
}

// 获取指定折扣方式涉及到的商品，由于指定折扣不针对加料，所以只需要统计该折扣生效的商品，如果该商品是套餐，还需要继续统计套餐子项
func getAssignPromotionProductInfo(promotion model.TicketPromotion, ticketProducts []*model.TicketProduct) []*Product {
	products := make([]*Product, 0) // 结果集
	// 第一步、折扣金额等分摊到指定商品上
	// 1.1、计算参与促销的商品总金额。需要将参与该促销的所以商品amt和加料amt相加
	var sumAmount, RATE float64
	// 计算财务折扣与交易折扣的占比
	if promotion.Source.Discount != 0 {
		RATE = promotion.Source.MerchantAllowance / promotion.Source.Discount
	}
	for _, promotionProduct := range promotion.Products {
		if promotionProduct.FreeAmt != 0 { // freeAmt不为零，说明其参与了该促销，故加上该商品的amt。
			sumAmount += promotionProduct.Amt
		}
		for _, access := range promotionProduct.Accessories {
			sumAmount += access.Amt
		}
	}
	// 折扣金额分摊到每个商品上
	var sumCost, sumTp float64
	for i, promotionProduct := range promotion.Products {
		// 1.2、先从该商品加料计算分摊
		for j, access := range promotionProduct.Accessories {
			var rate float64 // 占比
			if sumAmount != 0 {
				rate = access.Amt / sumAmount
			}
			accessCost, accessTp := 0.0, 0.0
			if i == len(promotion.Products)-1 && j == len(promotionProduct.Accessories)-1 && promotionProduct.FreeAmt == 0 { // 最后一个补误差
				accessCost = promotion.Source.Cost - sumCost
				accessTp = promotion.Source.TpAllowance - sumTp
			} else {
				// 截取小数点前两位,后面直接丢弃
				r := math.Floor(rate*100) / 100
				accessCost = promotion.Source.Cost * r
				sumCost += accessCost
				accessTp = promotion.Source.TpAllowance * r
				sumTp += accessTp
			}
			// 加料商品金额: 计算该商品分摊到的金额:discount,cost,merchant_allowance,tp_allowance,platform_allowance,transfer_amount等
			accessAmounts := &Amounts{
				Discount:          access.FreeAmt,
				MerchantAllowance: access.FreeAmt * RATE,
				Cost:              promotion.Source.Cost * rate,
				TpAllowance:       promotion.Source.TpAllowance * rate,
				PlatformAllowance: promotion.Source.PlatformAllowance * rate,
				TransferAmount:    promotion.Source.TransferAmount * rate,
			}
			accessory := &Product{
				ProductId:         access.KeyId,                        // 加料id
				GrossAmount:       access.Amt,                          // 加料商品流水
				NetAmount:         access.Amt - accessAmounts.Discount, // 加料实收
				DiscountAmount:    accessAmounts.Discount,              // 加料商品交易折扣
				Cost:              accessAmounts.Cost,                  // (财务)加料用户实际购买金额
				TpAllowance:       accessAmounts.TpAllowance,           // (财务)加料第三方补贴金额
				MerchantAllowance: accessAmounts.MerchantAllowance,     // (财务)加料商品补贴
				PlatformAllowance: accessAmounts.PlatformAllowance,     // (财务)加料平台补贴
				TransferAmount:    accessAmounts.TransferAmount,        // 折扣转支付
				ProductCount:      access.Qty,                          // 加料数量
				IsAccessory:       true,                                // 加料标识
				ComboType:         0,                                   // 套餐标识。0表示不是套餐子项也不是套餐头
				Weight:            access.Weight,                       // 增加重量/份量
				HasWeight:         access.HasWeight,                    // 是否称重商品
				Unit:              access.Unit.Name,                    // 单位
			}
			products = append(products, accessory)
		}

		// freeAmt为零，说明其没有参与该促销
		if promotionProduct.FreeAmt == 0 {
			continue
		}
		// 1.3、计算占比和金额
		var rate float64 // 占比
		if sumAmount != 0 {
			rate = promotionProduct.Amt / sumAmount
		}
		// 商品金额: 计算该商品分摊到的金额:discount,cost,merchant_allowance,tp_allowance,platform_allowance,transfer_amount等
		cost, tp := 0.0, 0.0
		if i == len(promotion.Products)-1 && len(promotionProduct.Accessories) == 0 { // 最后一个补误差
			cost = promotion.Source.Cost - sumCost
			tp = promotion.Source.TpAllowance - sumTp
		} else {
			// 截取小数点前两位,后面直接丢弃
			r := math.Floor(rate*100) / 100
			cost = promotion.Source.Cost * r
			sumCost += cost
			tp = promotion.Source.TpAllowance * r
			sumTp += tp
		}
		// 加料商品金额: 计算该商品分摊到的金额:discount,cost,merchant_allowance,tp_allowance,platform_allowance,transfer_amount等
		amounts := &Amounts{
			Discount:          promotionProduct.FreeAmt,
			MerchantAllowance: promotionProduct.FreeAmt * RATE,
			Cost:              promotion.Source.Cost * rate,
			TpAllowance:       promotion.Source.TpAllowance * rate,
			PlatformAllowance: promotion.Source.PlatformAllowance * rate,
			TransferAmount:    promotion.Source.TransferAmount * rate,
		}
		product := &Product{
			ProductId:         promotionProduct.KeyId,                  // 商品id
			GrossAmount:       promotionProduct.Amt,                    // 商品流水
			NetAmount:         promotionProduct.Amt - amounts.Discount, // 商品实收
			DiscountAmount:    amounts.Discount,                        // 商品交易折扣
			Cost:              amounts.Cost,                            // (财务)用户实际购买金额
			TpAllowance:       amounts.TpAllowance,                     // (财务)第三方补贴金额
			MerchantAllowance: amounts.MerchantAllowance,               // (财务)商品补贴
			PlatformAllowance: amounts.PlatformAllowance,               // (财务)第三方补贴
			TransferAmount:    amounts.TransferAmount,                  // 折扣转支付
			ProductCount:      promotionProduct.Qty,                    // 商品数量
			IsAccessory:       false,                                   // 不是加料标识
			Weight:            promotionProduct.Weight,                 // 增加重量/份量
			HasWeight:         promotionProduct.HasWeight,              // 是否称重商品
			Unit:              promotionProduct.Unit.Name,              // 单位
		}
		// 第二步、折扣金额等继续分摊到套餐子项
		// 去ticket.products匹配该商品，来区分该商品是否是套餐商品
		for _, p := range ticketProducts {
			if p.Id != promotionProduct.KeyId {
				continue
			}
			product.ProductCode = p.Code                       // 商品code
			if p.ComboItems == nil || len(p.ComboItems) == 0 { // 不是套餐商品，跳过
				continue
			}
			// 如果是套餐商品，需要继续分摊到套餐子项
			var ppRate float64 // 参与折扣商品占比 promotionProductRate缩写
			if p.Qty != 0 {
				// 该商品参与该折扣方式的数量/该商品总数量 = 该商品参与折扣占比。例如2份商品只有1份参与了该折扣，占比就是1/2
				ppRate = cast.ToFloat64(promotionProduct.Qty) / cast.ToFloat64(p.Qty)
			}
			product.ComboType = 1 // 套餐头
			// 统计套餐子项商品
			var sumAmt float64 // 所有套餐子项总金额
			for _, comboItem := range p.ComboItems {
				sumAmt += comboItem.Amount
			}
			var sumD, sumM float64
			for k, comboItem := range p.ComboItems {
				var rate float64 // 该套餐子项占比
				if sumAmt != 0 {
					rate = comboItem.Amount / sumAmt
				}
				var comboDiscount float64
				var comboMerchant float64
				if k == len(p.ComboItems)-1 { // 最后一个补误差
					comboDiscount = amounts.Discount - sumD
					comboMerchant = amounts.MerchantAllowance - sumM
				} else {
					//rr := math.Floor(rate*100) / 100
					comboDiscount = amounts.Discount * rate
					comboMerchant = amounts.MerchantAllowance * rate
					sumD += comboDiscount
					sumM += comboMerchant
				}
				comboAmounts := &Amounts{
					Discount:          comboDiscount,
					MerchantAllowance: comboMerchant,
					Cost:              amounts.Cost * rate,
					TpAllowance:       amounts.TpAllowance * rate,
					PlatformAllowance: amounts.PlatformAllowance * rate,
					TransferAmount:    amounts.TransferAmount * rate,
				}
				comboItemCount := cast.ToFloat64(comboItem.Qty) * ppRate // 套餐子项的数量
				// 套餐子项
				comboProduct := &Product{
					ProductId:         comboItem.Id,
					ProductCode:       comboItem.Code,
					GrossAmount:       comboItem.Amount * ppRate,
					NetAmount:         comboItem.NetAmount * ppRate,
					DiscountAmount:    comboAmounts.Discount,
					Cost:              comboAmounts.Cost,
					TpAllowance:       comboAmounts.TpAllowance,
					MerchantAllowance: comboAmounts.MerchantAllowance,
					PlatformAllowance: comboAmounts.PlatformAllowance,
					TransferAmount:    comboAmounts.TransferAmount,
					ProductCount:      cast.ToInt(comboItemCount),
					IsAccessory:       false,
					ComboType:         2,                         // 套餐子项
					Weight:            comboItem.Weight * ppRate, // 增加重量/份量
					HasWeight:         comboItem.HasWeight,       // 是否称重商品
					Unit:              comboItem.Unit.Name,       // 单位
				}
				products = append(products, comboProduct)
			}
			break
		}
		products = append(products, product) // 该商品所有字段已计算完毕，加入结果集
	}
	return products
}

// 如果是整单折扣，需要统计商品，及其加料商品，如果该商品是套餐，不需要统计加料，但要继续分摊到套餐子项
func getAllPromotionProductInfo(p model.TicketPromotion, ticketProducts []*model.TicketProduct) []*Product {
	products := make([]*Product, 0)
	// 第一步、折扣金额等分摊到所有商品上
	// 1.1、先计算总金额，将商品及其加料金额相加
	var sumAmt float64
	for _, v := range ticketProducts { // 商品及其加料金额合计
		sumAmt += v.Amount
		for _, access := range v.Accessories {
			sumAmt += access.Amount
		}
		for _, cItem := range v.ComboItems {
			for _, access := range cItem.Accessories {
				sumAmt += access.Amount
			}
		}
	}
	// 折扣金额分摊到每个商品上
	var sumMerchant, sumDiscount float64
	for i, v := range ticketProducts {
		// 1.2、遍历每一个商品，先计算占比
		var rate float64
		if sumAmt != 0 {
			rate = v.Amount / sumAmt
		}
		// 商品金额: 计算该商品分摊到的金额:discount,cost,merchant_allowance,tp_allowance,platform_allowance,transfer_amount等
		merchant := 0.0
		dis := 0.0
		// 拿到所有小料, 包括商品小料和子商品中的小料
		productAccs := v.Accessories
		for _, ci := range v.ComboItems {
			productAccs = append(productAccs, ci.Accessories...)
		}
		if i == len(ticketProducts)-1 && len(productAccs) == 0 { // 最后一个补误差
			merchant = p.Source.MerchantAllowance - sumMerchant
			dis = p.Source.Discount - sumDiscount
		} else {
			//截取小数点前两位,后面直接丢弃
			//r := math.Floor(rate*100) / 100
			merchant = p.Source.MerchantAllowance * rate
			sumMerchant += merchant
			dis = p.Source.Discount * rate
			sumDiscount += dis
		}
		// 1.3、计算该商品分摊到的金额
		amounts := &Amounts{
			Discount:          dis,
			MerchantAllowance: merchant,
			Cost:              p.Source.Cost * rate,
			TpAllowance:       p.Source.TpAllowance * rate,
			PlatformAllowance: p.Source.PlatformAllowance * rate,
			TransferAmount:    p.Source.TransferAmount * rate,
		}
		product := &Product{
			ProductId:         v.Id,
			ProductCode:       v.Code,
			GrossAmount:       v.Amount,
			NetAmount:         v.NetAmount,
			DiscountAmount:    amounts.Discount,
			Cost:              amounts.Cost,
			TpAllowance:       amounts.TpAllowance,
			MerchantAllowance: amounts.MerchantAllowance,
			PlatformAllowance: amounts.PlatformAllowance,
			TransferAmount:    amounts.TransferAmount,
			ProductCount:      v.Qty,
			IsAccessory:       false,       // 不是加料商品
			Weight:            v.Weight,    // 增加重量/份量
			HasWeight:         v.HasWeight, // 是否称重商品
			Unit:              v.Unit.Name, // 单位
		}
		// 第二步、折扣金额等分摊到加料上
		for j, access := range productAccs {
			// 2.1、计算加料占比
			var aRate float64
			if sumAmt != 0 {
				aRate = access.Amount / sumAmt
			}
			// 2.2、计算该商品分摊到的金额
			accessD, accessM := 0.0, 0.0
			if i == len(ticketProducts)-1 && j == len(productAccs)-1 {
				accessD = p.Source.Discount - sumDiscount
				accessM = p.Source.MerchantAllowance - sumMerchant
			} else {
				// 截取小数点前两位,后面直接丢弃
				//r := math.Floor(aRate*100) / 100
				accessM = p.Source.MerchantAllowance * aRate
				sumMerchant += accessM
				accessD = p.Source.Discount * aRate
				sumDiscount += accessD
			}
			accessAmounts := &Amounts{
				Discount:          accessD,
				MerchantAllowance: accessM,
				Cost:              p.Source.Cost * aRate,
				TpAllowance:       p.Source.TpAllowance * aRate,
				PlatformAllowance: p.Source.PlatformAllowance * aRate,
				TransferAmount:    p.Source.TransferAmount * aRate,
			}
			accessory := &Product{
				ProductId:         access.Id,
				ProductCode:       access.Code,
				GrossAmount:       access.Amount,
				NetAmount:         access.NetAmount,
				DiscountAmount:    accessAmounts.Discount,
				Cost:              accessAmounts.Cost,
				TpAllowance:       accessAmounts.TpAllowance,
				MerchantAllowance: accessAmounts.MerchantAllowance,
				PlatformAllowance: accessAmounts.PlatformAllowance,
				TransferAmount:    accessAmounts.TransferAmount,
				ProductCount:      access.Qty,
				IsAccessory:       true,             // 是加料
				ComboType:         0,                // 不是套餐头，也不是套餐子项
				Weight:            access.Weight,    // 增加重量/份量
				HasWeight:         access.HasWeight, // 是否称重商品
				Unit:              access.Unit.Name, // 单位
			}
			products = append(products, accessory)
		}
		// 第三步、如果是套餐商品，继续分摊到套餐子项
		if len(v.ComboItems) > 0 {
			product.ComboType = 1 // 该商品是套餐头
			// 3.1 计算所以套餐子项金额合计
			var sumComboAmt float64
			for _, item := range v.ComboItems {
				sumComboAmt += item.Amount
			}
			// 3.2 遍历每一个套餐子项，先计算占比，再计算分摊到的折扣金额等
			var sumDiscount, sumMerchant float64
			for k, comboItem := range v.ComboItems {
				var rate float64
				if sumComboAmt != 0 {
					rate = comboItem.Amount / sumComboAmt
				}
				comboD, comboM := 0.0, 0.0
				if k == len(v.ComboItems)-1 { // 最后一个补误差
					comboD = amounts.Discount - sumDiscount
					comboM = amounts.MerchantAllowance - sumMerchant
				} else {
					//rr := math.Floor(rate*100) / 100
					comboD = amounts.Discount * rate
					comboM = amounts.MerchantAllowance * rate
					sumDiscount += comboD
					sumMerchant += comboM
				}
				comboAmounts := &Amounts{
					Discount:          comboD,
					MerchantAllowance: comboM,
					Cost:              amounts.Cost * rate,
					TpAllowance:       amounts.TpAllowance * rate,
					PlatformAllowance: amounts.PlatformAllowance * rate,
					TransferAmount:    amounts.TransferAmount * rate,
				}

				comboProduct := &Product{
					ProductId:         comboItem.Id,
					ProductCode:       comboItem.Code,
					GrossAmount:       comboItem.Amount,
					NetAmount:         comboItem.NetAmount,
					DiscountAmount:    comboAmounts.Discount,
					Cost:              comboAmounts.Cost,
					TpAllowance:       comboAmounts.TpAllowance,
					MerchantAllowance: comboAmounts.MerchantAllowance,
					PlatformAllowance: comboAmounts.PlatformAllowance,
					TransferAmount:    comboAmounts.TransferAmount,
					ProductCount:      comboItem.Qty,
					IsAccessory:       false,               // 不是加料
					ComboType:         2,                   // 是套餐子项
					Weight:            comboItem.Weight,    // 增加重量/份量
					HasWeight:         comboItem.HasWeight, // 是否称重商品
					Unit:              comboItem.Unit.Name, // 单位
				}
				products = append(products, comboProduct)
			}
		}
		products = append(products, product)
	}
	return products
}

func updateProducts(dest []*Product, src []*Product) []*Product {
	if len(dest) == 0 {
		return src
	}
	if len(src) == 0 {
		return dest
	}
	msrc := make(map[PAC]*Product)
	for _, product := range src {
		msrc[PAC{
			ProductId:   product.ProductId,
			IsAccessory: product.IsAccessory,
			ComboType:   product.ComboType,
		}] = product
	}
	for _, product := range dest {
		if p, ok := msrc[PAC{
			ProductId:   product.ProductId,
			IsAccessory: product.IsAccessory,
			ComboType:   product.ComboType,
		}]; !ok { // 不存在直接存
			src = append(src, product)
		} else { // 存在要更新
			//p.GrossAmount += product.GrossAmount
			//p.NetAmount += product.NetAmount
			p.DiscountAmount += product.DiscountAmount
			p.Cost += product.Cost
			p.MerchantAllowance += product.MerchantAllowance
			p.TpAllowance += product.TpAllowance
			p.PlatformAllowance += product.PlatformAllowance
			p.TransferAmount += product.TransferAmount
			//p.ProductCount += product.ProductCount
		}
	}
	return src
}
