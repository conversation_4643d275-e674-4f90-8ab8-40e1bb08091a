package ticket

import (
	"context"
	"encoding/json"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

// 校验电子小票是否合法
func ValidateTicket(ctx context.Context, salesId int64) (succ bool, err error) {
	trackId := ctx.Value(config.TrackId)
	var (
		si      *model.SalesTicketContent // 电子小票在数据库中的模型
		ticket  *model.Ticket             // 电子小票模型
		errcode string
		bs      []byte
	)
	// 查询SalesTicketContent数据库
	if si, err = repo.DefaultSalesDB.FindSales(ctx, &model.SalesTicketContent{Id: salesId}); err != nil || si == nil {
		logger.Pre().Errorf("[%d] [services.ticket.ValidateTicket] Couldn't find by Id `%d`.\nData: `%+v`\nErr: `%+v`", trackId, salesId, si, err)
		return
	}
	// 判断查到的电子小票状态
	if si.Status != model.Init {
		logger.Pre().Warnf("[%d] [services.ticket.ValidateTicket] Id `%d` status is `%s` != `%s`", trackId, salesId, si.Status, model.Init)
		return
	}
	// 将查到的ContentModified转成ticket
	if ticket, err = si.ToTicket(); err != nil {
		logger.Pre().Errorf("[%d] [services.ticket.ValidateTicket] Unmashal ticket failed. Content: `%s`\nModifContent: `%s`\nErr: `%+v`.", trackId, string(*si.Content), string(*si.ContentModified), err)
		errcode = "content:TYPEERROR:" + err.Error()
	} else {
		// 电子小票已校验，报表就不再校验
		//// 电子小票数据检验规则:会收集错误信息切片，然后转成字符串返回
		//if errcode, err = validate.Validate(ctx, ticket); err != nil {
		//	errcode = "content:TYPEERROR:" + err.Error()
		//}
	}
	//先清除err-msg
	err = repo.DefaultSalesErrDB.DeleteSalesErr(ctx, si.EticketId)
	if err != nil {
		return
	}

	// 如果有校验异常信息，就将异常信息存储到电子小票的模型里面,然后更新这个记录
	update := &model.SalesTicketContent{Id: si.Id}
	if errcode != "" {
		// 将错误分开成一个列表
		errCodes := strings.Split(errcode, ";")
		for _, errMsg := range errCodes {
			logger.Pre().Error("异常电子小票信息：", si.EticketId, errMsg)
			errType := strings.Split(errMsg, ":")[0]
			// 每一个异常和小票关联存储到SalesTicketErrmsg中
			var ticketErrorMsg = &model.SalesTicketErrmsg{
				EticketId:   si.EticketId,
				ErrorMsg:    errType,
				ChannelId:   cast.ToString(si.ChannelId),
				ChannelName: si.ChannelName,
				StoreId:     si.StoreId,
				ErrorInfo:   errMsg,
				TicketId:    si.TicketId,
			}
			success, err := repo.DefaultSalesErrDB.InsertSaleErr(ctx, ticketErrorMsg)
			if success == nil {
				return true, err
			}
			logger.Pre().Info("services.err_ticket.insert success")
		}
		ticket.ErrorCodes = errcode
		if bs, err = json.Marshal(ticket); err != nil {
			logger.Pre().Errorf("[%d] [services.ticket.ValidateTicket] Marshal ticket failed .\nData: `%+v`\nErr: `%+v`", trackId, ticket, err)
			return
		}
		jbs := json.RawMessage(bs)
		update.Status = model.DataError
		update.ContentModified = &jbs
		update.ChannelId = utils.StringMust2Int(ticket.Channel.Id)
		update.ChannelName = ticket.Channel.TpName
		update.OrderType = ticket.Channel.OrderType
		update.OrderTypeName = ticket.Channel.OrderType
	} else {
		// 没有校验异常，就将状态设置为验证成功
		update.ProcessStatus = model.Process_Valid
		succ = true
	}
	return succ, repo.DefaultSalesDB.UpdateSales(ctx, update)
}
