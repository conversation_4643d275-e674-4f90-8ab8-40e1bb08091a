package ticket

import (
	"context"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"sort"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

type Remark []string

func (a Remark) Len() int           { return len(a) }
func (a Remark) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a Remark) Less(i, j int) bool { return a[i] < a[j] }

// 拆解商品统计表
func resolveProductAmount(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	now := time.Now()
	bd, _ := time.Parse(config.DateFormat, ticket.BusDate)
	s := strings.Split(ticket.EndTime, " ")[0]
	nd, _ := time.Parse(config.DateFormat, s)
	et := ticket.GetOrderTime()
	// 加一个start_time下单时间
	st, _ := time.Parse(config.DateTimeFormat, ticket.StartTime)

	storeTagCodes, storeTagNames := storeTagCodesAndNames(ticket)

	openTableAt, _ := time.ParseInLocation(config.DateTimeFormat, ticket.Table.OpenTableAt, config.ShanghaiLocation)
	var couponChannelCode, couponChannelName string
	for _, coupon := range ticket.Coupons {
		couponChannelCode = coupon.ChannelCode
		couponChannelName = coupon.ChannelName
		if couponChannelCode != "" && couponChannelName != "" {
			break
		}
	}

	productMap := make(map[string]*products)
	for _, item := range ticket.Products { // 第一类：普通商品要统计sku属性和加料
		skuMap := make(map[string]string)  // 保存sku属性名和属性值
		var skuRemarks, accessories Remark // 分别保存sku属性name和加料name
		// 将商品所有的sku属性name取出，并按字典序排序
		for _, remark := range item.SkuRemark {
			skuRemarks = append(skuRemarks, remark.Values.Name) // 取出sku属性name
			skuMap[remark.Name.Name] = remark.Values.Name       // 将所有的sku属性映射成键值对，key是属性名，value是属性值
		}
		skuRemarkStr := SkuRemarksToString(skuMap) // 将sku属性键值对拼接成string
		//fmt.Println("skuRemark to str:", skuRemarkStr)
		var accCount = 0
		for _, access := range item.Accessories { // 取出所有加料的name
			accessories = append(accessories, access.Name)
			accCount += access.Qty
		}
		// 商品口味：将商品所有的sku属性code和所有的加料code拼接成一个string。flavor形如：aa;bb;cc&00;11
		flavor := SkuRemarkAndAccessoryToFlavor(skuRemarks, accessories)
		//fmt.Println("flavor:", flavor)

		var prod *products
		// 单价(Price)不一样不能合并
		// 单价一样，但是优惠(DiscountAmount)不同也不能合并
		// 单价与优惠一样，但是顾客要求可能不同，因此(Remark)不一样也不能合并
		id := flavor + ":" + item.Id + ":" + item.CreateTime + ":" + cast.ToString(accCount) + cast.ToString(item.Price) + cast.ToString(item.DiscountAmount) + item.Remark // 用商品口味(由商品sku属性和加料组成)和商品id拼接成唯一标识
		if prod = productMap[id]; prod == nil {
			prod = &products{}
		}
		prod.ProductCode = item.Code
		prod.ProductName = item.Name
		prod.SkuRemark = item.SkuRemark
		prod.Flavor = flavor                // 1、商品的口味
		prod.SkuRemarkMapStr = skuRemarkStr // 2、保存商品所有的sku属性的键值对

		prod.Price = item.Price
		prod.Rate = item.Rate // 税率
		prod.Qty += item.Qty
		prod.Remark = item.Remark
		var bowlNum int32 = 0
		if len(item.ComboItems) == 0 {
			bowlNum = item.BowlNum
		} else {
			for _, comboItem := range item.ComboItems {
				bowlNum += comboItem.BowlNum
			}
		}
		prod.BowlNum += bowlNum
		prod.ProductsRealAmount += item.ProductsRealAmount
		prod.GrossAmount += item.Amount
		prod.DiscountAmount += item.DiscountAmount
		prod.NetAmount += item.Amount - item.DiscountAmount
		prod.TaxFee += item.TaxAmount

		// 增加重量/份量、是否称重商品、单位
		prod.Weight += item.Weight
		prod.HasWeight = item.HasWeight
		prod.Unit = item.Unit.Name

		prod.TransferAmount += item.TransferAmount                 // 支付转折扣
		prod.DiscountTransferAmount += item.DiscountTransferAmount // 折扣转支付
		prod.InvoiceAmount += item.InvoiceAmount                   // 开票金额
		prod.FinanceRealAmount += item.FinanceRealAmount           // 财务实收金额
		prod.Type = item.Type
		if item.Qty != 0 {
			prod.AccCount = accCount / item.Qty
		}

		prod.GrossAmount2 += item.Amount
		prod.calcProductsAmount(item)
		if len(item.ComboItems) > 0 { // 如果有套餐子项，说明该商品是套餐头，需要将套餐类型置为1
			prod.ComboType = 1
		}
		accessoryMap := make(map[string]int)  // 统计商品加料及其加料数量的map
		for _, it := range item.Accessories { // 第二类：加料商品没有sku属性和加料了
			accessoryMap[it.Name] = it.Qty
			if it.CreateTime == "" {
				it.CreateTime = item.CreateTime
			}
			id := item.Id + ":" + it.Id + ":" + it.CreateTime + ":" + cast.ToString(prod.AccCount) // 用商品id和加料id拼接成唯一标志
			var accessory *products
			if accessory = productMap[id]; accessory == nil {
				accessory = &products{}
			}
			accessory.ProductCode = it.Code
			accessory.ProductName = it.Name
			accessory.Price = it.Price // 价格
			accessory.Qty += it.Qty
			accessory.BowlNum += it.BowlNum
			accessory.Remark = it.Remark
			accessory.ProductsRealAmount += it.ProductsRealAmount
			accessory.GrossAmount += it.Amount
			accessory.DiscountAmount += it.DiscountAmount
			accessory.NetAmount += it.Amount - it.DiscountAmount
			accessory.TaxFee += it.TaxAmount
			accessory.TransferAmount += it.TransferAmount
			accessory.DiscountTransferAmount += it.DiscountTransferAmount
			accessory.InvoiceAmount += it.InvoiceAmount
			accessory.FinanceRealAmount += it.FinanceRealAmount
			accessory.Rate = it.Rate
			accessory.IsAccessory = true

			// 增加重量/份量、是否称重商品、单位
			accessory.Weight += it.Weight
			accessory.HasWeight = it.HasWeight
			accessory.Unit = it.Unit.Name
			accessory.Type = it.Type
			accessory.ProductAccCount = prod.AccCount

			accessory.GrossAmount2 += it.Amount
			accessory.calcProductsAmount(it)
			productMap[id] = accessory

		}
		prod.Accessories = accessoryMapToStr(accessoryMap) // 3、统计商品的加料
		productMap[id] = prod
		// 统计套餐子项
		for _, comboItem := range item.ComboItems { // 第三类：套餐子项有sku属性，但没有加料
			// 套餐子项也有sku属性，但没有加料，故只需要统计所有sku属性，并排序，再拼接成string
			var comboSkuRemarks, comboAccessories Remark
			comboSkuMap := make(map[string]string) // 保存sku属性名和属性值
			for _, skuRemark := range comboItem.SkuRemark {
				comboSkuRemarks = append(comboSkuRemarks, skuRemark.Values.Name)
				comboSkuMap[skuRemark.Name.Name] = skuRemark.Values.Name
			}
			if comboItem.CreateTime == "" {
				comboItem.CreateTime = item.CreateTime
			}
			var comboAccCount = 0
			for _, access := range comboItem.Accessories { // 取出所有加料的name
				comboAccessories = append(comboAccessories, access.Name)
				comboAccCount += access.Qty
			}
			// 将套餐子项的属性code拼接成一个string
			flavorCombo := SkuRemarkAndAccessoryToFlavor(comboSkuRemarks, comboAccessories)
			// 将sku属性键值对拼接成string
			comboSkuRemarkStr := SkuRemarksToString(comboSkuMap)
			var comboProduct *products
			comboId := item.Id + "&" + flavorCombo + ":" + comboItem.Id + ":" + comboItem.CreateTime + ":" + cast.ToString(comboAccCount) + cast.ToString(comboItem.Price) + cast.ToString(comboItem.DiscountAmount) + comboItem.Remark // 将套餐头id、sku属性信息和子项id拼接成key
			if comboProduct = productMap[comboId]; comboProduct == nil {
				comboProduct = &products{}
			}
			comboProduct.ProductCode = comboItem.Code
			comboProduct.ProductName = comboItem.Name
			comboProduct.SkuRemark = comboItem.SkuRemark
			comboProduct.SkuRemarkMapStr = comboSkuRemarkStr // 套餐子项的sku属性键值对
			comboProduct.Flavor = flavorCombo                // 套餐子项的口味

			comboProduct.Price = comboItem.Price // 价格
			comboProduct.Qty += comboItem.Qty
			comboProduct.Remark = comboItem.Remark
			comboProduct.BowlNum += comboItem.BowlNum
			comboProduct.ProductsRealAmount += comboItem.ProductsRealAmount
			comboProduct.GrossAmount += comboItem.Amount
			comboProduct.DiscountAmount += comboItem.DiscountAmount
			comboProduct.NetAmount += comboItem.Amount - comboItem.DiscountAmount
			comboProduct.TaxFee += comboItem.TaxAmount
			comboProduct.TransferAmount += comboItem.TransferAmount
			comboProduct.DiscountTransferAmount += comboItem.DiscountTransferAmount
			comboProduct.InvoiceAmount += comboItem.InvoiceAmount
			comboProduct.FinanceRealAmount += comboItem.FinanceRealAmount
			comboProduct.Rate = comboItem.Rate
			comboProduct.IsCombo = true
			comboProduct.ComboType = 2 // 套餐子项商品的套餐类型是2
			// 增加重量/份量、是否称重商品、单位
			comboProduct.Weight += comboItem.Weight
			comboProduct.HasWeight = comboItem.HasWeight
			comboProduct.Unit = comboItem.Unit.Name
			comboProduct.Type = comboItem.Type
			comboProduct.GrossAmount2 += comboItem.SubAmount
			comboProduct.calcProductsAmount(comboItem)
			comboAccessoryMap := make(map[string]int)  // 统计商品加料及其加料数量的map
			for _, it := range comboItem.Accessories { // 第二类：加料商品没有sku属性和加料了
				comboAccessoryMap[it.Name] = it.Qty
				if it.CreateTime == "" {
					it.CreateTime = comboItem.CreateTime
				}
				id := item.Id + "&" + comboItem.Id + ":" + it.Id + ":" + it.CreateTime + ":" + cast.ToString(comboProduct.AccCount) // 用商品id和加料id拼接成唯一标志
				var accessory *products
				if accessory = productMap[id]; accessory == nil {
					accessory = &products{}
				}
				accessory.ProductCode = it.Code
				accessory.ProductName = it.Name
				accessory.ComboType = 3
				accessory.Price = it.Price // 价格
				accessory.Qty += it.Qty
				accessory.Remark = it.Remark
				accessory.BowlNum += it.BowlNum
				accessory.ProductsRealAmount += it.ProductsRealAmount
				accessory.GrossAmount += it.Amount
				accessory.DiscountAmount += it.DiscountAmount
				accessory.NetAmount += it.Amount - it.DiscountAmount
				accessory.TaxFee += it.TaxAmount
				accessory.TransferAmount += it.TransferAmount
				accessory.DiscountTransferAmount += it.DiscountTransferAmount
				accessory.InvoiceAmount += it.InvoiceAmount
				accessory.FinanceRealAmount += it.FinanceRealAmount
				accessory.Rate = it.Rate
				accessory.IsAccessory = true

				// 增加重量/份量、是否称重商品、单位
				accessory.Weight += it.Weight
				accessory.HasWeight = it.HasWeight
				accessory.Unit = it.Unit.Name
				accessory.Type = it.Type
				accessory.ProductAccCount = comboProduct.AccCount

				accessory.GrossAmount2 += it.Amount
				accessory.calcProductsAmount(it)
				productMap[id] = accessory
			}

			comboProduct.Accessories = accessoryMapToStr(comboAccessoryMap)
			productMap[comboId] = comboProduct
		}
	}

	for id, item := range productMap { // 普通商品，加料商品和套餐子商品
		//  如果是套餐子项，id是由套餐头id、sku属性信息和子项id拼接的，需要取出子项的id；
		//	如果是加料商品，id是由主商品和加料商品id拼接的，也要取出加料的id
		//	如果是普通商品或套餐头，id是有sku属性和加料以及商品id拼接的，还是要取出商品id
		var createTime time.Time
		var comboProductId int64
		if strings.Contains(id, ":") {
			arr := strings.Split(id, ":")
			if len(arr) >= 3 {
				id = arr[1]
				if len(arr) >= 5 {
					ct, e := time.ParseInLocation(config.DateTimeFormat, arr[2]+":"+arr[3]+":"+arr[4], config.ShanghaiLocation)
					if e != nil {
						logger.Pre().Error("resolveProductAmount time parse error:", e)
					}
					createTime = ct
				}
				if item.ComboType == 2 || item.ComboType == 3 {
					s := strings.Split(arr[0], "&")
					comboProductId = utils.StringMust2Int(s[0])
				}
			}
		}
		rsm := &model.SalesProductAmount{
			Id:            utils.GetHexUUid(ctx),
			PartnerId:     cast.ToInt64(ticket.Store.PartnerId),
			ScopeId:       cast.ToInt64(ticket.Store.ScopeId),
			BusDate:       time.Date(bd.Year(), bd.Month(), bd.Day(), 0, 0, 0, 0, time.UTC),
			NaturalDate:   time.Date(nd.Year(), nd.Month(), nd.Day(), 0, 0, 0, 0, time.UTC),
			StoreId:       utils.StringMust2Int(ticket.Store.Id),
			EticketId:     ticket.Id,
			ChannelId:     utils.StringMust2Int(ticket.Channel.Id),
			ChannelName:   ticket.Channel.TpName,
			OrderType:     ticket.Channel.OrderType,
			OrderTypeName: ticket.Channel.OrderType,
			OrderTime:     et,
			Refunded:      ticket.Status == model.REFUND || ticket.Status == model.PARTIALREFUND,
			ProductId:     utils.StringMust2Int(id),
			ProductCode:   item.ProductCode,
			ProductName:   item.ProductName,
			// TODO
			// product_code: item.,
			// SaleType: string,
			// IsAccessory: bool,
			// CategoryId: int64,

			Qty:                    item.Qty,
			GrossAmount:            item.GrossAmount,
			NetAmount:              item.NetAmount,
			DiscountAmount:         item.DiscountAmount,
			TaxFee:                 item.TaxFee,
			Created:                time.Now().UTC(),
			EticketCount:           1,
			OrderStatus:            ticket.Status,
			Price:                  item.Price,
			StartTime:              st, // 下单时间
			IsCombo:                item.IsCombo,
			ComboType:              item.ComboType,              // 商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品
			Accessories:            item.Accessories,            // 加料信息
			Flavor:                 item.Flavor,                 // 商品口味：sku属性和加料
			SkuRemark:              item.SkuRemarkMapStr,        // sku属性组成的key,value对
			IsAccessory:            item.IsAccessory,            // 是否是加料商品。true：是加料，false：不是加料
			Weight:                 item.Weight,                 // 重量/份量
			HasWeight:              item.HasWeight,              // 是否是称重商品
			Unit:                   item.Unit,                   // 单位
			TransferAmount:         item.TransferAmount,         // 支付转折扣
			DiscountTransferAmount: item.DiscountTransferAmount, // 折扣转支付
			InvoiceAmount:          item.InvoiceAmount,          // 开票金额
			ProductTax:             item.Rate,                   // 商品税率
			FinanceRealAmount:      item.FinanceRealAmount,
			CreateTime:             createTime,
			Remark:                 item.Remark,
			BowlNum:                item.BowlNum,
			StoreTags:              storeTagCodes,
			StoreTagNames:          storeTagNames,
			MealSegmentName:        ticket.MealSegmentName,
			CouponChannelCode:      couponChannelCode,
			CouponChannelName:      couponChannelName,
			ZoneName:               ticket.Table.ZoneName,
			TableNo:                ticket.Table.No,
			OpenTableBy:            ticket.Table.OpenTableBy,
			OperatorName:           ticket.Operator.Name,
			OpenTableAt:            openTableAt,
			EndTime:                et,
			TicketNo:               ticket.TakeawayInfo.TpOrderId,
			ComboProductId:         comboProductId,
			ProductsRealAmount:     item.ProductsRealAmount,
			AccCount:               float64(item.AccCount),
			ProductAccCount:        float64(item.ProductAccCount),
			SaleType:               item.Type,

			GrossAmount2:               item.GrossAmount2,
			MerchantDiscountAmount:     item.MerchantDiscountAmount,
			PackageAmount:              item.PackageAmount,
			ServiceFee:                 item.ServiceFee,
			Tip:                        item.Tip,
			OtherFee:                   item.OtherFee,
			Rounding:                   item.Rounding,
			OverflowAmount:             item.OverflowAmount,
			Commission:                 item.Commission,
			MerchantSendFee:            item.MerchantSendFee,
			SendFeeForMerchant:         item.SendFeeForMerchant,
			SendFeeForPlatform:         item.SendFeeForPlatform,
			PayCost:                    item.PayCost,
			PayTpAllowance:             item.PayTpAllowance,
			PayMerchantAllowance:       item.PayMerchantAllowance,
			PayPlatformAllowance:       item.PayPlatformAllowance,
			PromotionCost:              item.PromotionCost,
			PromotionTpAllowance:       item.PromotionTpAllowance,
			PromotionMerchantAllowance: item.PromotionMerchantAllowance,
			PromotionPlatformAllowance: item.PromotionPlatformAllowance,
		}

		// 通过遍历优惠项里面的折扣优惠商品数组，找到这个商品用的优惠项名称
		// 找到了一个商品使用了两种优惠，怎么记？
		rsm.BusDateYear = time.Date(bd.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		rsm.BusDateMonth = time.Date(bd.Year(), bd.Month(), 1, 0, 0, 0, 0, time.UTC)
		var sub int
		if sub = int(rsm.BusDate.Weekday()) - 1; sub < 0 {
			sub = 6
		}
		if rsm.Refunded {
			rsm.EticketCount = -rsm.EticketCount
		}
		if rsm.BusDateWeek = rsm.BusDate.Add(-time.Duration(sub) * 24 * time.Hour); rsm.BusDateWeek.Year() != rsm.BusDate.Year() {
			rsm.BusDateWeek = time.Date(rsm.BusDate.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		}
		resolveResult = append(resolveResult, rsm)

		// 添加属性销售数据
		for _, skuRemark := range item.SkuRemark {
			resolveResult = append(resolveResult, &model.SalesProductSkuRemark{
				Id:         utils.GetHexUUid(ctx),
				EticketId:  rsm.EticketId,
				BusDate:    rsm.BusDate,
				PartnerId:  rsm.PartnerId,
				StoreId:    rsm.StoreId,
				Pid:        rsm.Id,
				ProductId:  rsm.ProductId,
				Code:       skuRemark.Name.Code,
				Name:       skuRemark.Name.Name,
				ValueCode:  skuRemark.Values.Code,
				ValueName:  skuRemark.Values.Name,
				ValuePrice: skuRemark.Values.Price,
				Qty:        rsm.Qty,
				ComboType:  rsm.ComboType,
				Created:    now,
				Updated:    now,
				UpdatedBy:  0,
				Deleted:    0,
			})
		}
	}
	//fmt.Println("resolve result:", resolveResult)
	return
}

func SkuRemarkAndAccessoryToFlavor(skus, accessories Remark) string {
	// sku属性code和加料code先按字典序排序
	sort.Sort(skus)
	sort.Sort(accessories)
	skus = append(skus, accessories...)
	var flavor string
	for _, remark := range skus {
		flavor = flavor + "、" + remark
	}
	flavor = strings.Trim(flavor, "、")
	return flavor
}

func SkuRemarksToString(m map[string]string) string {
	var body string // 将sku属性键值对拼接成string
	var remarks []string
	for key, value := range m {
		mark := fmt.Sprintf(`"%s":"%s"`, key, value)
		remarks = append(remarks, mark)
	}
	sort.Sort(sort.StringSlice(remarks))
	for _, r := range remarks {
		body = body + "," + r
	}

	if body != "" {
		body = strings.Trim(body, ",")
	}
	if body != "" {
		body = fmt.Sprintf(`{%s}`, body)
	}
	return body
}

func accessoryMapToStr(m map[string]int) string {
	var body string // 将加料id及其数量键值对拼接成string
	var tmp []string
	for key, value := range m {
		mark := fmt.Sprintf(`"%s":%d`, key, value)
		tmp = append(tmp, mark)
	}
	sort.Sort(sort.StringSlice(tmp))
	for _, s := range tmp {
		body = body + "," + s
	}
	if body != "" {
		body = strings.Trim(body, ",")
	}
	if body != "" {
		body = fmt.Sprintf(`{%s}`, body)
	}
	return body
}

type products struct {
	ProductCode            string
	ProductName            string
	Qty                    int
	GrossAmount            float64
	NetAmount              float64
	DiscountAmount         float64
	TaxFee                 float64
	Price                  float64
	IsCombo                bool
	ComboType              int    // 商品套餐类型：0:非套餐商品；1:套餐头商品；2:套餐子项商品; 3:套餐子项商品加料
	Accessories            string // 统计商品的加料
	Flavor                 string // 口味：所有属性和所有加料
	SkuRemarkMapStr        string // 所有sku属性组成的键值对
	SkuRemark              []model.TicketSkuRemark
	IsAccessory            bool    // 是否是加料商品
	Weight                 float64 // 重量/份量
	HasWeight              bool    // 是否称重商品
	Unit                   string  // 单位
	TransferAmount         float64 // 支付转折扣
	Rate                   float64 // 税率
	InvoiceAmount          float64 // 开票金额
	FinanceRealAmount      float64 // 财务实收金额
	DiscountTransferAmount float64 // 折扣转支付
	BowlNum                int32   // 碗数
	ProductsRealAmount     float64 // 商品实收金额
	AccCount               int     // 加料数量
	ProductAccCount        int     // 加料数量
	Remark                 string
	Type                   string

	MerchantDiscountAmount float64 `json:"merchant_discount_amount,omitempty"` // 商家折扣承担 - 分摊

	GrossAmount2 float64 // 折扣三方承担 - 分摊

	PackageAmount              float64 `json:"package_amount,omitempty"`               // 分摊
	ServiceFee                 float64 `json:"service_fee,omitempty"`                  // 分摊
	Tip                        float64 `json:"tip,omitempty"`                          // 分摊
	OtherFee                   float64 `json:"other_fee,omitempty"`                    // 分摊
	Rounding                   float64 `json:"rounding,omitempty"`                     // 分摊
	OverflowAmount             float64 `json:"overflow_amount,omitempty"`              // 分摊
	Commission                 float64 `json:"commission,omitempty"`                   // 分摊
	MerchantSendFee            float64 `json:"merchant_send_fee,omitempty"`            // 分摊
	SendFeeForMerchant         float64 `json:"send_fee_for_merchant,omitempty"`        // 分摊
	SendFeeForPlatform         float64 `json:"send_fee_for_platform,omitempty"`        // 分摊
	PayCost                    float64 `json:"pay_cost,omitempty"`                     // 分摊
	PayTpAllowance             float64 `json:"pay_tp_allowance,omitempty"`             // 分摊
	PayMerchantAllowance       float64 `json:"pay_merchant_allowance,omitempty"`       // 分摊
	PayPlatformAllowance       float64 `json:"pay_platform_allowance,omitempty"`       // 分摊
	PromotionCost              float64 `json:"promotion_cost,omitempty"`               // 分摊
	PromotionTpAllowance       float64 `json:"promotion_tp_allowance,omitempty"`       // 分摊
	PromotionMerchantAllowance float64 `json:"promotion_merchant_allowance,omitempty"` // 分摊
	PromotionPlatformAllowance float64 `json:"promotion_platform_allowance,omitempty"` // 分摊
}

// sum(coalesce(fact.gross_amount2, 0) + coalesce(fact.package_amount, 0) + coalesce(fact.send_fee_for_merchant, 0) + coalesce(fact.service_fee, 0) + coalesce(fact.tip, 0)) as gross_amount,
// sum(coalesce(fact.pay_merchant_allowance, 0) + coalesce(fact.promotion_merchant_allowance, 0) + coalesce(fact.other_fee, 0) + coalesce(fact.merchant_send_fee, 0) + coalesce(fact.commission, 0) + coalesce(fact.rounding, 0) - coalesce(fact.overflow_amount, 0)) as discount_amount,
func (p *products) calcProductsAmount(item *model.TicketProduct) {
	p.MerchantDiscountAmount += item.MerchantDiscountAmount
	p.PackageAmount += item.PackageAmount
	p.ServiceFee += item.ServiceFee
	p.Tip += item.Tip
	p.OtherFee += item.OtherFee
	p.Rounding += item.Rounding
	p.OverflowAmount += item.OverflowAmount
	p.Commission += item.Commission
	p.MerchantSendFee += item.MerchantSendFee
	p.SendFeeForMerchant += item.SendFeeForMerchant
	p.SendFeeForPlatform += item.SendFeeForPlatform
	p.PayCost += item.PayCost
	p.PayTpAllowance += item.PayTpAllowance
	p.PayMerchantAllowance += item.PayMerchantAllowance
	p.PayPlatformAllowance += item.PayPlatformAllowance
	p.PromotionCost += item.PromotionCost
	p.PromotionTpAllowance += item.PromotionTpAllowance
	p.PromotionMerchantAllowance += item.PromotionMerchantAllowance
	p.PromotionPlatformAllowance += item.PromotionPlatformAllowance
	business := p.GrossAmount2 + p.PackageAmount + p.SendFeeForMerchant + p.ServiceFee + p.Tip
	discount := p.PayMerchantAllowance + p.PromotionMerchantAllowance + p.OtherFee + p.MerchantSendFee + p.Commission + p.Rounding - p.OverflowAmount
	p.ProductsRealAmount, _ = decimal.NewFromFloat(business - discount).Round(2).Float64()
}
