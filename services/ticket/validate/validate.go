package validate

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

const (
	// TODO use MaxDelayDays set in metadata
	MaxDelayDays = 1800 // 延长时间
)

// doc: http://docs.hexcloud.org/hipos-api/#/api%E6%96%87%E6%A1%A3/%E9%94%80%E5%94%AE%E6%8A%A5%E8%A1%A8/%E7%94%B5%E5%AD%90%E5%B0%8F%E7%A5%A8%E6%A0%A1%E9%AA%8C?id=%e9%aa%8c%e8%af%81%e8%a7%84%e5%88%99
func Validate(ctx context.Context, ticket *model.Ticket) (errCode string, err error) {
	var errcodes, tmp []string
	// 验证营业日期和订单时间
	if tmp, err = valid_date(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证门店id和商品id和促销id
	if tmp, err = valid_meteadata(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证正负单的金额等信息
	if tmp, err = valid_positive_negative(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证打包费
	//if tmp, err = valid_money_fee(ctx, ticket); err != nil {
	//	return
	//}
	//errcodes = append(errcodes, tmp...)
	// 验证费率
	if tmp, err = valid_money_tax(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证折扣数据
	if tmp, err = valid_money_discount(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证promotions子项
	if tmp, err = valid_promotions(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证payments子项
	if tmp, err = valid_payments(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证delivery_fee
	if tmp, err = valid_delivery_fee(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	//// 验证营业额
	//if tmp, err = valid_business_amount(ctx, ticket); err != nil {
	//	return
	//}
	//errcodes = append(errcodes, tmp...)
	// 验证支出
	if tmp, err = valid_expend_amount(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证real_amount
	if tmp, err = valid_real_amount(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证毛额信息
	if tmp, err = valid_money_gross(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证杂项数据，实付金额异常
	if tmp, err = valid_money_sundry(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	//验证商家补贴金额
	//if tmp, err = valid_merchant_allowance(ctx, ticket); err != nil {
	//	return
	//}
	//errcodes = append(errcodes, tmp...)
	// 验证第三方补贴金额
	if tmp, err = valid_tp_allowance(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证支付转折扣
	if tmp, err = valid_payment_transfer(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证折扣转支付
	if tmp, err = valid_discount_transfer(ctx, ticket); err != nil {
		return
	}
	errcodes = append(errcodes, tmp...)
	// 验证促销项下面的商品关联列表是否为空
	//if tmp, err = valid_business_amount(ctx, ticket); err != nil {
	//	return
	//}
	//errcodes = append(errcodes, tmp...)
	//if tmp, err = valid_expend_amount(ctx, ticket); err != nil {
	//	return
	//}
	//errcodes = append(errcodes, tmp...)
	return strings.Join(errcodes, ";"), nil
}

func valid_real_amount(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	if compareMoney([]float64{ticket.Amounts.BusinessAmount}, []float64{ticket.Amounts.ExpendAmount, ticket.Amounts.RealAmount}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("real_amount:ERROR:%f", ticket.Amounts.RealAmount))
	}
	return
}

func valid_delivery_fee(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	if compareMoney([]float64{ticket.Amounts.DeliveryFee}, []float64{ticket.TakeawayInfo.SendFee, ticket.TakeawayInfo.MerchantSendFee, ticket.TakeawayInfo.PlatformSendFee}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("delivery_fee:ERROR:%f", ticket.Amounts.DeliveryFee))
	}
	// 新增一条delivery_fee校验规则 2021-07-29
	if compareMoney([]float64{ticket.Amounts.DeliveryFee}, []float64{ticket.Amounts.DeliveryFeeForPlatform, ticket.Amounts.DeliveryFeeForMerchant}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("delivery_fee:ERROR:%f", ticket.Amounts.DeliveryFee))
	}
	if compareMoney([]float64{ticket.TakeawayInfo.SendFee}, []float64{ticket.TakeawayInfo.SendFeeForPlatform, ticket.TakeawayInfo.SendFeeForMerchant}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("send_fee:ERROR:%f", ticket.TakeawayInfo.SendFee))
	}
	return
}

func valid_payments(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	receivables := make([]float64, 0, len(ticket.Payments))
	payAmounts := make([]float64, 0, len(ticket.Payments))
	changeAmounts := make([]float64, 0, len(ticket.Payments))
	payMerchantContributes := make([]float64, 0, len(ticket.Payments))
	payPlatformContributes := make([]float64, 0, len(ticket.Payments))
	payBuyerContributes := make([]float64, 0, len(ticket.Payments))
	payOtherContributes := make([]float64, 0, len(ticket.Payments))
	overflows := make([]float64, 0, len(ticket.Payments))
	roundings := make([]float64, 0, len(ticket.Payments))
	for _, payment := range ticket.Payments {
		receivables = append(receivables, payment.Receivable)
		payAmounts = append(payAmounts, payment.PayAmount)
		changeAmounts = append(changeAmounts, payment.Change)
		payMerchantContributes = append(payMerchantContributes, payment.MerchantAllowance)
		payPlatformContributes = append(payPlatformContributes, payment.PlatformAllowance)
		payBuyerContributes = append(payBuyerContributes, payment.Cost)
		payOtherContributes = append(payOtherContributes, payment.TpAllowance)
		overflows = append(overflows, payment.Overflow)
		roundings = append(roundings, payment.Rounding)

		if compareMoney([]float64{payment.Receivable, payment.Change, payment.Overflow},
			[]float64{payment.PayAmount, payment.Rounding}, 0.01) != 0 {
			errcodes = append(errcodes, fmt.Sprintf("payments:analyse-α:ERROR:%f", payment.PayAmount))
		}
		if compareMoney([]float64{payment.PayAmount},
			[]float64{payment.Cost, payment.PlatformAllowance, payment.MerchantAllowance,
				payment.TpAllowance, payment.Change}, 0.01) != 0 {
			errcodes = append(errcodes, fmt.Sprintf("payments:analyse-β:ERROR:%f", payment.PayAmount))
		}
	}

	if compareMoney([]float64{ticket.Amounts.PayMerchantContribute}, payMerchantContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_merchant_contribute:ERROR:%f", ticket.Amounts.PayMerchantContribute))
	}
	if compareMoney([]float64{ticket.Amounts.PayPlatformContribute}, payPlatformContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_platform_contribute:ERROR:%f", ticket.Amounts.PayPlatformContribute))
	}
	if compareMoney([]float64{ticket.Amounts.PayBuyerContribute}, payBuyerContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_buyer_contribute:ERROR:%f", ticket.Amounts.PayBuyerContribute))
	}
	if compareMoney([]float64{ticket.Amounts.PayOtherContribute}, payOtherContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_other_contribute:ERROR:%f", ticket.Amounts.PayOtherContribute))
	}
	if compareMoney([]float64{ticket.Amounts.Receivable}, receivables, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("receivable:ERROR:%f", ticket.Amounts.Receivable))
	}
	if compareMoney([]float64{ticket.Amounts.PayAmount}, payAmounts, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_amount:ERROR:%f", ticket.Amounts.PayAmount))
	}
	if compareMoney([]float64{ticket.Amounts.ChangeAmount}, changeAmounts, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("change_amount:ERROR:%f", ticket.Amounts.ChangeAmount))
	}
	if compareMoney([]float64{ticket.Amounts.OverflowAmount}, overflows, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("overflow_amount:ERROR:%f", ticket.Amounts.OverflowAmount))
	}
	if compareMoney([]float64{ticket.Amounts.Rounding}, roundings, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("rounding:ERROR:%f", ticket.Amounts.Rounding))
	}
	return
}

func valid_promotions(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	merchantDiscountAmounts := make([]float64, 0, len(ticket.Promotions))
	platformDiscountAmounts := make([]float64, 0, len(ticket.Promotions))
	storeDiscountAmounts := make([]float64, 0, len(ticket.Promotions))
	discountMerchantContributes := make([]float64, 0, len(ticket.Promotions))
	discountPlatformContributes := make([]float64, 0, len(ticket.Promotions))
	discountBuyerContributes := make([]float64, 0, len(ticket.Promotions))
	discountOtherContributes := make([]float64, 0, len(ticket.Promotions))
	for _, promotion := range ticket.Promotions {
		merchantDiscountAmounts = append(merchantDiscountAmounts, promotion.Source.MerchantDiscount)
		platformDiscountAmounts = append(platformDiscountAmounts, promotion.Source.PlatformDiscount)
		storeDiscountAmounts = append(storeDiscountAmounts, promotion.Source.StoreDiscount)
		discountMerchantContributes = append(discountMerchantContributes, promotion.Source.MerchantAllowance)
		discountPlatformContributes = append(discountPlatformContributes, promotion.Source.PlatformAllowance)
		discountBuyerContributes = append(discountBuyerContributes, promotion.Source.Cost)
		discountOtherContributes = append(discountOtherContributes, promotion.Source.TpAllowance)

		if compareMoney([]float64{promotion.Source.Discount},
			[]float64{promotion.Source.StoreDiscount, promotion.Source.MerchantDiscount,
				promotion.Source.PlatformDiscount}, 0.01) != 0 {
			errcodes = append(errcodes, fmt.Sprintf("promotions:analyse-α:ERROR:%f", promotion.Source.Discount))
		}
		if compareMoney([]float64{promotion.Source.Discount},
			[]float64{promotion.Source.Cost, promotion.Source.PlatformAllowance,
				promotion.Source.MerchantAllowance, promotion.Source.TpAllowance}, 0.01) != 0 {
			errcodes = append(errcodes, fmt.Sprintf("promotions:analyse-β:ERROR:%f", promotion.Source.Discount))
		}
	}
	// 如果促销是空
	if len(ticket.Promotions) == 0 {
		merchantDiscountAmounts = []float64{0}
		platformDiscountAmounts = []float64{0}
		storeDiscountAmounts = []float64{0}
		discountMerchantContributes = []float64{0}
		discountPlatformContributes = []float64{0}
		discountBuyerContributes = []float64{0}
		discountOtherContributes = []float64{0}
	}
	if compareMoney([]float64{ticket.Amounts.MerchantDiscountAmount}, merchantDiscountAmounts, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("merchant_discount_amount:ERROR:%f", ticket.Amounts.MerchantDiscountAmount))
	}
	if compareMoney([]float64{ticket.Amounts.PlatformDiscountAmount}, platformDiscountAmounts, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("platform_discount_amount:ERROR:%f", ticket.Amounts.PlatformDiscountAmount))
	}
	if compareMoney([]float64{ticket.Amounts.StoreDiscountAmount}, storeDiscountAmounts, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("store_discount_amount:ERROR:%f", ticket.Amounts.StoreDiscountAmount))
	}
	if compareMoney([]float64{ticket.Amounts.DiscountMerchantContribute}, discountMerchantContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("discount_merchant_contribute:ERROR:%f", ticket.Amounts.DiscountMerchantContribute))
	}
	if compareMoney([]float64{ticket.Amounts.DiscountPlatformContribute}, discountPlatformContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("discount_platform_contribute:ERROR:%f", ticket.Amounts.DiscountPlatformContribute))
	}
	if compareMoney([]float64{ticket.Amounts.DiscountBuyerContribute}, discountBuyerContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("dicount_buyer_contribute:ERROR:%f", ticket.Amounts.DiscountBuyerContribute))
	}
	if compareMoney([]float64{ticket.Amounts.DiscountOtherContribute}, discountOtherContributes, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("dicount_other_contribute:ERROR:%f", ticket.Amounts.DiscountOtherContribute))
	}
	return
}

// 验证营业额:
func valid_business_amount(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	if compareMoney([]float64{ticket.Amounts.BusinessAmount}, []float64{ticket.Amounts.GrossAmount, ticket.TakeawayInfo.PackageFee, ticket.Amounts.DeliveryFeeForMerchant, ticket.Amounts.ServiceFee, ticket.Amounts.Tip}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("business_amount:ERROR:%f", ticket.Amounts.BusinessAmount))
	}
	return
}

// 验证支出:
func valid_expend_amount(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	if compareMoney([]float64{ticket.Amounts.ExpendAmount}, []float64{ticket.Amounts.MerchantDiscountAmount, ticket.Amounts.Commission, ticket.Amounts.OtherFee, ticket.Amounts.StoreDiscountAmount, ticket.TakeawayInfo.MerchantSendFee}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("expend_amount:ERROR:%f", ticket.Amounts.ExpendAmount))
	}
	return
}

// 验证实收金额(转换后):
//func valid_projected_income(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
//
//}

// 实收金额
//func valid_real_amount(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
//
//}

func valid_discount_transfer(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	// discountTransfer := make([]float64, 0, len(ticket.Coupons))
	// for _, coupon := range ticket.Coupons {
	// 	discountTransfer = append(discountTransfer, coupon.TransferAmount)
	// }
	discountTransfer := make([]float64, 0, len(ticket.Promotions))
	for _, promotion := range ticket.Promotions {
		discountTransfer = append(discountTransfer, promotion.Source.TransferAmount)
	}
	if compareMoney([]float64{ticket.Amounts.DiscountTransferAmount}, discountTransfer, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("discount_transfer_amount:ERROR:%f", ticket.Amounts.DiscountTransferAmount))
	}
	return
}

func valid_payment_transfer(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	paymentTransfer := make([]float64, 0, len(ticket.Payments))
	for _, payment := range ticket.Payments {
		paymentTransfer = append(paymentTransfer, payment.TransferAmount)
	}
	if compareMoney([]float64{ticket.Amounts.PaymentTransferAmount}, paymentTransfer, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("payment_transfer_amount:ERROR:%f", ticket.Amounts.PaymentTransferAmount))
	}
	return
}

func valid_date(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	mdd := MaxDelayDays
	var today = time.Now().UTC()
	// zone := ticket.GetTimeZone()

	// 营业日期
	if strings.TrimSpace(ticket.BusDate) == "" {
		errcodes = append(errcodes, "bus_date:EMPTY")
	} else {
		if busDate, e := time.ParseInLocation(config.DateFormat, ticket.BusDate, time.UTC); e != nil {
			errcodes = append(errcodes, "bus_date:NOT_TIME:"+ticket.BusDate)
		} else if busDate.
			Add(24 * time.Hour).Add(time.Duration(mdd) * 24 * time.Hour).
			Before(time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, time.UTC)) {
			errcodes = append(errcodes, "bus_date:MAXDELAY")
		}
	}

	// TODO 订单时间
	// if strings.TrimSpace(ticket.EndTime) == "" {
	// 	errcodes = append(errcodes, "order_time:EMPTY")
	// }
	// if endTime, e := time.ParseInLocation(config.DateTimeFormat, ticket.EndTime, zone); e != nil {
	// 	errcodes = append(errcodes, "order_time:NOT_TIME:"+ticket.EndTime)
	// } else if endTime.Add(time.Duration(mdd) * 24 * time.Hour).Before(today) {
	// 	errcodes = append(errcodes, "order_time:MAXDELAY")
	// }
	return
}

func valid_meteadata(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	// 主档 TODO 未去主档查询
	// 新增channel_id为空校验           wangheng 2021-09-03 -------
	if ticket.Channel.Id == "0" || strings.TrimSpace(ticket.Channel.Id) == "" {
		errcodes = append(errcodes, "channel_id:EMPTY")
	}
	// 新增pos_id为空校验
	if ticket.Pos.Id == "0" || strings.TrimSpace(ticket.Pos.Id) == "" {
		errcodes = append(errcodes, "pos_id:EMPTY")
	}
	if ticket.Store.Id == "0" || strings.TrimSpace(ticket.Store.Id) == "" { // 门店id为空/0
		errcodes = append(errcodes, "store_id:EMPTY")
	}
	for _, item := range ticket.Products { //商品id为空/0
		if item.Id == "0" || strings.TrimSpace(item.Id) == "" {
			errcodes = append(errcodes, "product_id:EMPTY")
			break
		}
	}
	for _, item := range ticket.Payments { //支付id为空/0
		if item.Id == "0" || strings.TrimSpace(item.Id) == "" {
			errcodes = append(errcodes, "payment_id:EMPTY")
			break
		}
	}
	for _, item := range ticket.Promotions { //促销id为空/0
		if item.PromotionInfo.PromotionId == "0" || strings.TrimSpace(item.PromotionInfo.PromotionId) == "" {
			errcodes = append(errcodes, "promotion_id:EMPTY")
			break
		}
	}
	// for _, item := range ticket.Fees {
	// 	if item.Id == "0" || strings.TrimSpace(item.Id) == "" {
	// 		errcodes = append(errcodes, "fee_id:EMPTY")
	// 		break
	// 	}
	// }
	// for _, item := range ticket.TaxList {
	// 	if item.Id == "0" || strings.TrimSpace(item.Id) == "" {
	// 		errcodes = append(errcodes, "tax_id:EMPTY")
	// 		break
	// 	}
	// }
	return
}

// 校验正负单的各类金额
func valid_positive_negative(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	isSales := ticket.Status == model.SALE || ticket.Status == model.REFUNDCOMPLETED // 销售单和退单原单金额都是正的
	errMsg := "NEGATIVE"
	// 退单的金额和商品数量都要是负数才可以通过校验
	if !isSales {
		errMsg = "POSITIVE"
	}
	for _, item := range ticket.Products {
		if item.Amount != 0 && isSales != (item.Amount > 0) {
			errcodes = append(errcodes, fmt.Sprintf("product_amount:%s_ERROR", errMsg))
		}
		if item.Qty != 0 && isSales != (item.Qty > 0) {
			errcodes = append(errcodes, fmt.Sprintf("product_qty:%s_ERROR", errMsg))
		}
		for _, it := range item.Accessories {
			if it.Amount != 0 && isSales != (it.Amount > 0) {
				errcodes = append(errcodes, fmt.Sprintf("product_accessories_amount:%s_ERROR", errMsg))
			}
			if it.Qty != 0 && isSales != (it.Qty > 0) {
				errcodes = append(errcodes, fmt.Sprintf("product_accessories_qty:%s_ERROR", errMsg))
			}
		}
	}
	for _, item := range ticket.Payments {
		//if item.Receivable != 0 && isSales != (item.Receivable > 0) {
		//	errcodes = append(errcodes, fmt.Sprintf("payments_receivable:%s_ERROR", errMsg))
		//}
		//if item.PayAmount != 0 && isSales != (item.PayAmount > 0) {
		//	errcodes = append(errcodes, fmt.Sprintf("payments_pay_amount:%s_ERROR", errMsg))
		//}
		if item.Change != 0 && isSales != (item.Change > 0) {
			errcodes = append(errcodes, fmt.Sprintf("payments_change:%s_ERROR", errMsg))
		}
		if item.Overflow != 0 && isSales != (item.Overflow > 0) {
			errcodes = append(errcodes, fmt.Sprintf("payments_overflow:%s_ERROR", errMsg))
		}
		if item.Rounding != 0 && isSales != (item.Rounding > 0) {
			//errcodes = append(errcodes, fmt.Sprintf("payments_rounding:%s_ERROR", errMsg))
		}
	}

	for _, item := range ticket.TaxList {
		if item.Amount != 0 && isSales != (item.Amount > 0) {
			errcodes = append(errcodes, fmt.Sprintf("tax_list_amount:%s_ERROR", errMsg))
		}
	}

	for _, promotion := range ticket.Promotions {
		//if promotion.Source.Discount != 0 && isSales != (promotion.Source.Discount > 0) {
		//	errcodes = append(errcodes, fmt.Sprintf("discount:%s_ERROR", errMsg))
		//}
		//if promotion.Source.MerchantDiscount != 0 && isSales != (promotion.Source.MerchantDiscount > 0) {
		//	errcodes = append(errcodes, fmt.Sprintf("merchant_discount:%s_ERROR", errMsg))
		//}
		if promotion.Source.PlatformDiscount != 0 && isSales != (promotion.Source.PlatformDiscount > 0) {
			errcodes = append(errcodes, fmt.Sprintf("platform_discount:%s_ERROR", errMsg))
		}
		//if promotion.Source.StoreDiscount != 0 && isSales != (promotion.Source.StoreDiscount > 0) {
		//	errcodes = append(errcodes, fmt.Sprintf("store_discount:%s_ERROR", errMsg))
		//}
		if promotion.Source.Cost != 0 && isSales != (promotion.Source.Cost > 0) {
			errcodes = append(errcodes, fmt.Sprintf("cost:%s_ERROR", errMsg))
		}
		if promotion.Source.TpAllowance != 0 && isSales != (promotion.Source.TpAllowance > 0) {
			errcodes = append(errcodes, fmt.Sprintf("tp_allowance:%s_ERROR", errMsg))
		}
		// if promotion.Source.MerchantAllowance != 0 && isSales != (promotion.Source.MerchantAllowance > 0) {
		// 	errcodes = append(errcodes, fmt.Sprintf("merchant_allowance:%s_ERROR", errMsg))
		// }
		if promotion.Source.PlatformAllowance != 0 && isSales != (promotion.Source.PlatformAllowance > 0) {
			errcodes = append(errcodes, fmt.Sprintf("platform_allowance:%s_ERROR", errMsg))
		}
		if promotion.Source.RealAmount != 0 && isSales != (promotion.Source.RealAmount > 0) {
			errcodes = append(errcodes, fmt.Sprintf("real_amount:%s_ERROR", errMsg))
		}
		if promotion.Source.TransferAmount != 0 && isSales != (promotion.Source.TransferAmount > 0) {
			errcodes = append(errcodes, fmt.Sprintf("discount_transfer_amount:%s_ERROR", errMsg))
		}
	}

	//for _, item := range ticket.Fees {
	//	if item.Amount != 0 && isSales != (item.Amount > 0) {
	//		errcodes = append(errcodes, fmt.Sprintf("fees_amount:%s_ERROR", errMsg))
	//	}
	//	if item.Qty != 0 && isSales != (item.Qty > 0) {
	//		errcodes = append(errcodes, fmt.Sprintf("fees_qty:%s_ERROR", errMsg))
	//	}
	//}

	if ticket.Amounts.TaxAmount != 0 && isSales != (ticket.Amounts.TaxAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("tax_amount:%s_ERROR", errMsg))
	}
	if ticket.Amounts.GrossAmount != 0 && isSales != (ticket.Amounts.GrossAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("gross_amount:%s_ERROR", errMsg))
	}
	//if ticket.Amounts.RealAmount != 0 && isSales != (ticket.Amounts.RealAmount > 0) {
	//	errcodes = append(errcodes, fmt.Sprintf("real_amount:%s_ERROR", errMsg))
	//}
	if ticket.Amounts.BusinessAmount != 0 && isSales != (ticket.Amounts.BusinessAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("business_amount:%s_ERROR", errMsg))
	}
	// if ticket.Amounts.ProjectedIncome != 0 && isSales != (ticket.Amounts.ProjectedIncome > 0) {
	// 	errcodes = append(errcodes, fmt.Sprintf("projected_amount:%s_ERROR", errMsg))
	// }
	//if ticket.Amounts.NetAmount != 0 && isSales != (ticket.Amounts.NetAmount > 0) {
	//	errcodes = append(errcodes, fmt.Sprintf("net_amount:%s_ERROR", errMsg))
	//}
	//if ticket.Amounts.PayAmount != 0 && isSales != (ticket.Amounts.PayAmount > 0) {
	//	errcodes = append(errcodes, fmt.Sprintf("pay_amount:%s_ERROR", errMsg))
	//}
	if ticket.Amounts.RemovezeroAmount != 0 && isSales != (ticket.Amounts.RemovezeroAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("removezero_amount:%s_ERROR", errMsg))
	}
	if ticket.Amounts.Rounding != 0 && isSales != (ticket.Amounts.Rounding > 0) {
		//errcodes = append(errcodes, fmt.Sprintf("rounding:%s_ERROR", errMsg))
	}
	if ticket.Amounts.OverflowAmount != 0 && isSales != (ticket.Amounts.OverflowAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("overflow_amount:%s_ERROR", errMsg))
	}
	if ticket.Amounts.ChangeAmount != 0 && isSales != (ticket.Amounts.ChangeAmount > 0) {
		errcodes = append(errcodes, fmt.Sprintf("change_amount:%s_ERROR", errMsg))
	}
	if ticket.Amounts.ServiceFee != 0 && isSales != (ticket.Amounts.ServiceFee > 0) {
		errcodes = append(errcodes, fmt.Sprintf("service_fee:%s_ERROR", errMsg))
	}
	if ticket.Amounts.Tip != 0 && isSales != (ticket.Amounts.Tip > 0) {
		errcodes = append(errcodes, fmt.Sprintf("tip:%s_ERROR", errMsg))
	}
	if ticket.Amounts.PackageFee != 0 && isSales != (ticket.Amounts.PackageFee > 0) {
		errcodes = append(errcodes, fmt.Sprintf("package_fee:%s_ERROR", errMsg))
	}
	if ticket.Amounts.DeliveryFee != 0 && isSales != (ticket.Amounts.DeliveryFee > 0) {
		errcodes = append(errcodes, fmt.Sprintf("delivery_fee:%s_ERROR", errMsg))
	}
	if ticket.Amounts.DiscountTransferAmount != 0 && isSales != (ticket.Amounts.DiscountTransferAmount > 0) { //折扣转支付校验
		errcodes = append(errcodes, fmt.Sprintf("discount_transfer_amount:%s_ERROR", errMsg))
	}
	// if ticket.Amounts.PaymentTransferAmount != 0 && isSales != (ticket.Amounts.PaymentTransferAmount > 0) { //支付转折扣校验
	// 	errcodes = append(errcodes, fmt.Sprintf("payment_transfer_amount:%s_ERROR", errMsg))
	// }
	//if ticket.Amounts.Receivable != 0 && isSales != (ticket.Amounts.Receivable > 0) { //支付转折扣校验
	//	errcodes = append(errcodes, fmt.Sprintf("receivable:%s_ERROR", errMsg))
	//}
	//if ticket.Amounts.MerchantDiscountAmount != 0 && isSales != (ticket.Amounts.MerchantDiscountAmount > 0) { //支付转折扣校验
	//	errcodes = append(errcodes, fmt.Sprintf("merchant_discount_amount:%s_ERROR", errMsg))
	//}
	if ticket.Amounts.PlatformDiscountAmount != 0 && isSales != (ticket.Amounts.PlatformDiscountAmount > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("platform_discount_amount:%s_ERROR", errMsg))
	}
	//if ticket.Amounts.StoreDiscountAmount != 0 && isSales != (ticket.Amounts.StoreDiscountAmount > 0) { //支付转折扣校验
	//	errcodes = append(errcodes, fmt.Sprintf("store_discount_amount:%s_ERROR", errMsg))
	//}
	// if ticket.Amounts.DiscountMerchantContribute != 0 && isSales != (ticket.Amounts.DiscountMerchantContribute > 0) { //支付转折扣校验
	// 	errcodes = append(errcodes, fmt.Sprintf("discount_merchant_contribute:%s_ERROR", errMsg))
	// }
	if ticket.Amounts.DiscountPlatformContribute != 0 && isSales != (ticket.Amounts.DiscountPlatformContribute > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("discount_platform_contribute:%s_ERROR", errMsg))
	}
	if ticket.Amounts.DiscountBuyerContribute != 0 && isSales != (ticket.Amounts.DiscountBuyerContribute > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("discount_buyer_contribute:%s_ERROR", errMsg))
	}
	if ticket.Amounts.DiscountOtherContribute != 0 && isSales != (ticket.Amounts.DiscountOtherContribute > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("discount_other_contribute:%s_ERROR", errMsg))
	}
	//if ticket.Amounts.PayMerchantContribute != 0 && isSales != (ticket.Amounts.PayMerchantContribute > 0) { //支付转折扣校验
	//	errcodes = append(errcodes, fmt.Sprintf("pay_merchant_contribute:%s_ERROR", errMsg))
	//}
	if ticket.Amounts.PayPlatformContribute != 0 && isSales != (ticket.Amounts.PayPlatformContribute > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("pay_platform_contribute:%s_ERROR", errMsg))
	}
	//if ticket.Amounts.PayBuyerContribute != 0 && isSales != (ticket.Amounts.PayBuyerContribute > 0) { //支付转折扣校验
	//	errcodes = append(errcodes, fmt.Sprintf("pay_buyer_contribute:%s_ERROR", errMsg))
	//}
	if ticket.Amounts.PayOtherContribute != 0 && isSales != (ticket.Amounts.PayOtherContribute > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("pay_other_contribute:%s_ERROR", errMsg))
	}
	if ticket.TakeawayInfo.SendFee != 0 && isSales != (ticket.TakeawayInfo.SendFee > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("send_fee:%s_ERROR", errMsg))
	}
	if ticket.TakeawayInfo.SendFeeForPlatform != 0 && isSales != (ticket.TakeawayInfo.SendFeeForPlatform > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("send_fee_for_platform:%s_ERROR", errMsg))
	}
	if ticket.TakeawayInfo.SendFeeForMerchant != 0 && isSales != (ticket.TakeawayInfo.SendFeeForMerchant > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("send_fee_for_merchant:%s_ERROR", errMsg))
	}
	if ticket.TakeawayInfo.MerchantSendFee != 0 && isSales != (ticket.TakeawayInfo.MerchantSendFee > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("merchant_send_fee:%s_ERROR", errMsg))
	}
	if ticket.TakeawayInfo.PlatformSendFee != 0 && isSales != (ticket.TakeawayInfo.PlatformSendFee > 0) { //支付转折扣校验
		errcodes = append(errcodes, fmt.Sprintf("platform_send_fee:%s_ERROR", errMsg))
	}
	return
}

func valid_money_fee(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	var fees []float64
	var fee float64
	for _, item := range ticket.Fees {
		fees = append(fees, item.Amount)
		fee += item.Amount
	}
	if compareMoney([]float64{ticket.Amounts.Tip, ticket.Amounts.PackageFee, ticket.Amounts.DeliveryFee, ticket.Amounts.OtherFee},
		fees, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("fee_amount:ERROR:%f", fee))
	}
	return
}

func valid_money_tax(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	var taxs []float64
	for _, item := range ticket.TaxList {
		taxs = append(taxs, item.Amount)
	}
	if compareMoney([]float64{ticket.Amounts.TaxAmount}, taxs, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("tax_amount:ERROR:%f", ticket.Amounts.TaxAmount))
	}
	return
}

func valid_money_discount(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	var disscounts []float64
	var promotions []float64
	for _, item := range ticket.Products {
		disscounts = append(disscounts, item.DiscountAmount)
		for _, it := range item.Accessories {
			disscounts = append(disscounts, it.DiscountAmount)
		}
	}
	for _, item := range ticket.Promotions {
		promotions = append(promotions, item.Source.Discount)
	}
	//// 折扣金额异常
	//if ticket.Amounts.DiscountAmount != 0 &&
	//	compareMoney([]float64{0}, disscounts, 0.01) != 0 &&
	//	compareMoney([]float64{ticket.Amounts.DiscountAmount}, disscounts, 0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("discount_amount:ERROR:%f", ticket.Amounts.DiscountAmount))
	//}
	//// 折扣金额异常
	//if compareMoney([]float64{ticket.Amounts.DiscountAmount}, []float64{ticket.Amounts.MerchantDiscountAmount, ticket.Amounts.PlatformDiscountAmount, ticket.Amounts.StoreDiscountAmount}, 0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("discount_amount:ERROR:%f", ticket.Amounts.DiscountAmount))
	//}
	//// 折扣金额异常
	//if compareMoney([]float64{ticket.Amounts.DiscountAmount}, []float64{ticket.Amounts.DiscountMerchantContribute, ticket.Amounts.DiscountPlatformContribute, ticket.Amounts.DiscountBuyerContribute, ticket.Amounts.DiscountOtherContribute}, 0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("discount_amount:ERROR:%f", ticket.Amounts.DiscountAmount))
	//}
	// 促销金额异常
	if compareMoney([]float64{ticket.Amounts.DiscountAmount}, promotions, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("promotion_amount:ERROR:%f", sumMoney(promotions)))
	}
	return
}

func valid_money_gross(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	var gross []float64
	for _, item := range ticket.Products {
		gross = append(gross, item.Amount)
		for _, it := range item.Accessories {
			gross = append(gross, it.Amount)
		}
	}
	// 交易额异常
	//if compareMoney([]float64{ticket.Amounts.GrossAmount}, gross, 0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("gross_amount:ERROR:%f", ticket.Amounts.GrossAmount))
	//}
	// 交易净额异常
	//if compareMoney([]float64{ticket.Amounts.GrossAmount}, []float64{ticket.Amounts.NetAmount, ticket.Amounts.DiscountAmount}, 0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("net_amount:ERROR:%f", ticket.Amounts.NetAmount))
	//}
	return
}

func valid_money_sundry(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	//if compareMoney([]float64{ticket.Amounts.PayAmount, ticket.Amounts.Rounding},
	//	[]float64{ticket.Amounts.NetAmount, ticket.TakeawayInfo.PackageFee, ticket.TakeawayInfo.SendFee, ticket.Amounts.TaxAmount, ticket.Amounts.OverflowAmount, ticket.Amounts.ChangeAmount, ticket.Amounts.OtherFee},
	//	0.01) != 0 {
	//	errcodes = append(errcodes, fmt.Sprintf("pay_amount:ERROR:%f", ticket.Amounts.PayAmount))
	//}

	if compareMoney([]float64{ticket.Amounts.PayAmount}, []float64{ticket.Amounts.ChangeAmount, ticket.Amounts.PayMerchantContribute, ticket.Amounts.PayPlatformContribute, ticket.Amounts.PayBuyerContribute, ticket.Amounts.PayOtherContribute}, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("pay_amount:ERROR:%f", ticket.Amounts.PayAmount))
	}
	return
}

func valid_merchant_allowance(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	merchantAllowances := make([]float64, 0, len(ticket.Payments))
	for _, payment := range ticket.Payments {
		merchantAllowances = append(merchantAllowances, payment.MerchantAllowance)
	}
	if compareMoney([]float64{ticket.Amounts.MerchantDiscountAmount}, merchantAllowances, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("merchant_allowance:ERROR:%f", ticket.Amounts.MerchantDiscountAmount))
	}
	return
}

func valid_tp_allowance(ctx context.Context, ticket *model.Ticket) (errcodes []string, err error) {
	tpAllowances := make([]float64, 0, len(ticket.Payments))
	//for _, payment := range ticket.Payments {
	//	tpAllowances = append(tpAllowances, payment.TpAllowance)
	//}
	for _, promotion := range ticket.Promotions {
		tpAllowances = append(tpAllowances, promotion.Source.PlatformDiscount)
	}
	//
	if len(ticket.Promotions) == 0 {
		tpAllowances = []float64{0}
	}
	if compareMoney([]float64{ticket.Amounts.PlatformDiscountAmount}, tpAllowances, 0.01) != 0 {
		errcodes = append(errcodes, fmt.Sprintf("platform_discount_amount:ERROR:%f", ticket.Amounts.PlatformDiscountAmount))
	}
	return
}

func compareMoney(one, two []float64, scope float64) (diff float64) {
	var oneR, twoR float64
	for _, item := range one {
		oneR += float64(item)
	}
	for _, item := range two {
		twoR += float64(item)
	}
	//v := oneR - twoR
	//if v == 0 {
	//
	//}
	if v := oneR - twoR; math.Abs(v) >= math.Abs(scope) {
		return float64(v)
	}
	return 0
}

func sumMoney(moneys []float64) (sum float64) {
	for _, item := range moneys {
		sum += item
	}
	return
}
