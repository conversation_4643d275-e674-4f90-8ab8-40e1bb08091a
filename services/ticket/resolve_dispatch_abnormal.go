package ticket

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"time"
)

var (
	handles_abnormal []resolveTicket
)

// 拆解2张基础表的函数
func init() {
	handles_abnormal = append(handles_abnormal, resolveSalesMoneyAbnormal)
	handles_abnormal = append(handles_abnormal, resolvePaymentAmountAbnormal)
}

// 拆分成2张基础表
func ResolveTicketAbnormal(ctx context.Context, salesId int64) (err error) {
	trackId := ctx.Value(config.TrackId)
	var (
		si     *model.SalesTicketContentAbnormal
		ticket *model.Ticket
	)
	if salesId == 0 {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicketAbnormal] Couldn't find by Id `%d`.\nData: `%+v`\nErr: `%+v`", trackId, salesId, si, err)
		return
	}
	if si, err = repo.DefaultAbnormalSalesDB.FindAbnormalSales(ctx, &model.SalesTicketContentAbnormal{Id: salesId}); err != nil || si == nil {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicketAbnormal] Couldn't find by Id `%d`.\nData: `%+v`\nErr: `%+v`", trackId, salesId, si, err)
		return
	}
	if si.ProcessStatus != model.Process_Init {
		logger.Pre().Warnf("[%d] [services.ticket.ResolveTicketAbnormal] Illegal process status. Except: `%s`, NOT `%s`.", trackId, model.Process_Discountsh, si.ProcessStatus)
		return nil
	}

	if ticket, err = si.ToTicket(); err != nil {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicketAbnormal] Unmashal ticket failed. Content: `%s`\nModifContent: `%s`\nErr: `%+v`.", trackId, string(*si.Content), string(*si.ContentModified), err)
		return
	}
	ticket.Store.PartnerId = cast.ToString(si.PartnerId)
	ticket.Store.ScopeId = cast.ToString(si.ScopeId)

	var result [][]model.Resolve

	// 执行拆解2个表的处理流程
	for _, item := range handles_abnormal {
		var v []model.Resolve
		if v, err = item(ctx, ticket); err != nil {
			return err
		}
		// 映射后的2张基础表对象模型
		result = append(result, v)
	}
	update := &model.SalesTicketContentAbnormal{
		Id:        si.Id,
		EticketId: si.EticketId,
		Status:    model.Finish,
	}
	if update.ContentModified = si.ContentModified; update.ContentModified == nil {
		update.ContentModified = si.Content
	}
	if si.EticketId == 0 {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicketAbnormal] Invalid eticketId. %v", trackId, si.Id)
		return
	}
	// 已存在正常小票，不进行异常小票拆表
	if record, _ := repo.DefaultSalesDB.FindSales(ctx, &model.SalesTicketContent{EticketId: si.EticketId}); record != nil && record.Id != 0 {
		// 置为完成
		repo.DefaultAbnormalSalesDB.UpdateAbnormalSales(ctx, &model.SalesTicketContentAbnormal{Id: si.Id, Status: model.Finish, ProcessStatus: model.Process_Finish, Updated: time.Now().UTC()})
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicketAbnormal] found normal ticket, ID: %v", trackId, record.Id)
		return
	}

	// 使用事务将报表数据添加到四个数据库中
	for i := 0; i < 3; i++ { // 重复3次，如果还报错，说明真的有问题
		err = repo.DefaultResolveTicketAll.InsertResolveTicketAll(ctx, update, false, result...)
		if err == nil {
			break
		}
		logger.Pre().Warningf("insert resolve abnormal ticket err:%v", err)
	}
	if err != nil {
		return err
	}
	// 拆解完成，把异常电子小票记录清理掉

	return nil
}
