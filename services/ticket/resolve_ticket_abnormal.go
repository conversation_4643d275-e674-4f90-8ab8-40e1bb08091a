package ticket

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

// 拆解销售金额统计表
func resolveSalesMoneyAbnormal(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	resolves, err := resolveSalesMoney(ctx, ticket)
	if err != nil {
		return nil, err
	}
	rsm := resolves[0]
	result := rsm.(*model.SalesTicketAmount).ToAll(ticket, 1)
	return []model.Resolve{result}, nil
}

// 拆解支付统计表
func resolvePaymentAmountAbnormal(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	resolves, err := resolvePaymentAmount(ctx, ticket)
	if err != nil {
		return nil, err
	}
	var results []model.Resolve
	for _, rsm := range resolves {
		result := rsm.(*model.SalesPaymentAmount).ToAll(ticket, 1)
		results = append(results, result)
	}
	return results, nil
}
