package ticket

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/test"
	"testing"
)

const ticketPay = `
{"ticket_id":"2023120810320113723257280007","ticket_no":"7","ticketUno":"7","start_time":"2024-05-29 12:30:35","end_time":"2023-12-08 10:30:30","bus_date":"2023-12-08","pos":{"id":"AndroidDCB_a9b6821ba178","code":"00","device_id":"","device_code":""},"operator":{"id":"","loginId":"","name":"999|信息部","code":"999|信息部","login_time":""},"amounts":{"taxAmount":0,"gross_amount":-62,"pay_amount":0,"net_amount":0,"discount_amount":-62,"removezero_amount":0,"rounding":0,"overflow_amount":0,"changeAmount":0,"serviceFee":0,"tip":0,"projected_income":0,"merchant_discount_amount":0,"platform_discount_amount":0,"total_amount":0,"taxIncluded":false,"otherFee":0,"real_amount":0,"business_amount":-62,"expend_amount":-62,"payment_transfer_amount":0,"discount_transfer_amount":0,"store_discount_amount":-62,"discount_merchant_contribute":-62,"discount_platform_contribute":0,"discount_buyer_contribute":0,"discount_other_contribute":0,"pay_merchant_contribute":0,"pay_platform_contribute":0,"pay_buyer_contribute":0,"pay_other_contribute":0,"delivery_fee":0,"delivery_fee_for_platform":0,"delivery_fee_for_merchant":0,"package_fee":0,"finance_expend_amount":-62,"invoice_amount":124,"products_real_amount":0},"status":"REFUND","refundInfo":{"refund_id":"","ref_ticket_id":"2023120810293013723257280002","ref_ticket_no":"","refund_reason":"【退单作废】顾客取消，原POS账单号:2023120810293013723257280002","refund_code":"","refund_side":"","refund_no":""},"channel":{"source":"PUSH","deviceType":"","orderType":"DINEIN","deliveryType":"REALTIME","tpName":"pos","code":"POS","id":"4869877013990637568","mapping_code":""},"products":[{"id":"4887962317163102208","name":"招牌麻辣汤","code":"SFA1011002","seq_id":0,"price":-22,"pos_price":0,"amount":-22,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-22,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-44,"image":"","sum_amount":-22,"sum_discount_amount":-22,"sum_net_amount":-44,"has_make_span":false,"avg_make_span":0,"unit":{"code":"63870425","unit_id":"4511148072973369344","name":"碗"},"serving_size":1,"invoice_amount":-44,"rate":0,"tax_code":"","finance_real_amount":-44,"transfer_amount":0,"jtp_items":[{"pid":"4887962317163102208","bom_id":"","code":"SFA1011002","name":"招牌麻辣汤","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"碗"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":-1,"metadata_categorys":[{"cate_id":"4869863961916375040"}]},{"id":"4869881222349750272","name":"豆卜","code":"SFA1020011","seq_id":0,"price":-4,"pos_price":0,"amount":-4,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-4,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-8,"image":"","sum_amount":-4,"sum_discount_amount":-4,"sum_net_amount":-8,"has_make_span":false,"avg_make_span":0,"unit":{"code":"DW026","unit_id":"4303986578688049153","name":"份"},"serving_size":1,"invoice_amount":-8,"rate":0,"tax_code":"","finance_real_amount":-8,"transfer_amount":0,"jtp_items":[{"pid":"4869881222349750272","bom_id":"","code":"SFA1020011","name":"豆卜","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"份"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":0,"metadata_categorys":[{"cate_id":"4869865744478797824"}]},{"id":"4869883417321308160","name":"鸡肉","code":"SFA1020002","seq_id":0,"price":-5,"pos_price":0,"amount":-5,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-5,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-10,"image":"","sum_amount":-5,"sum_discount_amount":-5,"sum_net_amount":-10,"has_make_span":false,"avg_make_span":0,"unit":{"code":"DW026","unit_id":"4303986578688049153","name":"份"},"serving_size":1,"invoice_amount":-10,"rate":0,"tax_code":"","finance_real_amount":-10,"transfer_amount":0,"jtp_items":[{"pid":"4869883417321308160","bom_id":"","code":"SFA1020002","name":"鸡肉","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"份"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":0,"metadata_categorys":[{"cate_id":"4869865744478797824"}]},{"id":"4887961958210371584","name":"秘制清汤","code":"SFA1011001","seq_id":0,"price":-22,"pos_price":0,"amount":-22,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-22,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-44,"image":"","sum_amount":-22,"sum_discount_amount":-22,"sum_net_amount":-44,"has_make_span":false,"avg_make_span":0,"unit":{"code":"63870425","unit_id":"4511148072973369344","name":"碗"},"serving_size":1,"invoice_amount":-44,"rate":0,"tax_code":"","finance_real_amount":-44,"transfer_amount":0,"jtp_items":[{"pid":"4887961958210371584","bom_id":"","code":"SFA1011001","name":"秘制清汤","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"碗"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":-1,"metadata_categorys":[{"cate_id":"4869863961916375040"}]},{"id":"4869881222349750272","name":"豆卜","code":"SFA1020011","seq_id":0,"price":-4,"pos_price":0,"amount":-4,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-4,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-8,"image":"","sum_amount":-4,"sum_discount_amount":-4,"sum_net_amount":-8,"has_make_span":false,"avg_make_span":0,"unit":{"code":"DW026","unit_id":"4303986578688049153","name":"份"},"serving_size":1,"invoice_amount":-8,"rate":0,"tax_code":"","finance_real_amount":-8,"transfer_amount":0,"jtp_items":[{"pid":"4869881222349750272","bom_id":"","code":"SFA1020011","name":"豆卜","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"份"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":0,"metadata_categorys":[{"cate_id":"4869865744478797824"}]},{"id":"4869883417321308160","name":"鸡肉","code":"SFA1020002","seq_id":0,"price":-5,"pos_price":0,"amount":-5,"qty":-1,"weight":-1,"has_weight":false,"discount_amount":-5,"type":"","accessories":null,"combo_items":null,"remark":"","operation_records":"","skuRemark":null,"spellUsers":null,"taxAmount":0,"net_amount":-10,"image":"","sum_amount":-5,"sum_discount_amount":-5,"sum_net_amount":-10,"has_make_span":false,"avg_make_span":0,"unit":{"code":"DW026","unit_id":"4303986578688049153","name":"份"},"serving_size":1,"invoice_amount":-10,"rate":0,"tax_code":"","finance_real_amount":-10,"transfer_amount":0,"jtp_items":[{"pid":"4869883417321308160","bom_id":"","code":"SFA1020002","name":"鸡肉","rate":0,"qty":0,"total":0,"pos_price":0,"jt_price":0,"unit":"份"}],"discount_transfer_amount":0,"delivery_weight":0,"plus_price":0,"products_real_amount":0,"bowl_num":0,"metadata_categorys":[{"cate_id":"4869865744478797824"}]}],"payments":[{"id":"4892759956161331200","seq_id":"","name":"","pay_amount":0,"receivable":0,"change":0,"overflow":0,"rounding":0,"pay_time":"2023-12-08 10:32:01","trans_code":"*********","tpTransactionNo":"","tp_allowance":0,"merchant_allowance":0,"trans_name":"员工福利","price":0,"cost":0,"real_amount":0,"has_invoiced":false,"platform_allowance":0,"payerNo":""},{"id":"4892759956161331200","seq_id":"","name":"","pay_amount":0,"receivable":0,"change":0,"overflow":0,"rounding":0,"pay_time":"2023-12-08 10:32:01","trans_code":"*********","tpTransactionNo":"","tp_allowance":0,"merchant_allowance":0,"trans_name":"员工福利","price":0,"cost":0,"real_amount":0,"has_invoiced":false,"platform_allowance":0,"payerNo":""}],"promotions":[{"promotionInfo":{"type":"","discount_type":"","name":"哗啦啦通用促销","promotion_id":"4893040766676434944","promotion_code":"9999","promotion_type":"","allow_overlap":false,"trigger_times_custom":false,"ticket_display":"","max_discount":0,"coupon_id":""},"source":{"trigger":0,"discount":-31,"fired":null,"store_discount":-31,"cost":0,"tp_allowance":0,"merchant_allowance":-31,"platform_allowance":0,"real_amount":0,"transfer_amount":0},"products":null},{"promotionInfo":{"type":"","discount_type":"","name":"哗啦啦通用促销","promotion_id":"4893040766676434944","promotion_code":"9999","promotion_type":"","allow_overlap":false,"trigger_times_custom":false,"ticket_display":"","max_discount":0,"coupon_id":""},"source":{"trigger":0,"discount":-31,"fired":null,"store_discount":-31,"cost":0,"tp_allowance":0,"merchant_allowance":-31,"platform_allowance":0,"real_amount":0,"transfer_amount":0},"products":null}],"table":{"id":"","zone_id":"","zoneNo":"","tableNo":"","no":"","people":0,"temporary":false},"takeaway_info":{"order_method":"","tp_order_id":"","order_time":"2024-05-29 12:30:35","description":"","consignee":"","phone_list":null,"tp":"","source":"","source_order_id":"","day_seq":"","delivery_type":0,"delivery_name":"","delivery_poi_address":"","invoice_title":"","waiting_time":"","tableware_num":0,"send_fee":0,"package_fee":0,"delivery_time":"","deliver_time":"","take_meal_sn":"","partnerPlatformId":0,"partnerPlatformName":"","wxName":"","isHighPriority":false,"takeoutType":"","originalOrderNo":"","selfDelivery":false,"delivery_phone":"","platform_send_fee":0,"merchant_send_fee":0,"send_fee_for_platform":0,"send_fee_for_merchant":0,"invoice_provider":"","invoice_amount":"","invoice_url":"","demotion_level":0,"delivery_weight":0},"store":{"id":"4881550515827146752","code":"FSJ001","name":"谭仔米线（佛山南海万科店）","secondCode":"","companyId":"","partnerId":"1372","scopeId":"","branchId":"","tags":[{"code":"001","name":"广州财务主体"}]},"fees":null,"fees_no_account":null,"efficiency":{"confirmed_time":"","made_time":"","assigned_time":"","arrived_time":"","fetched_time":"","delivered_time":"","make_span":0,"avg_make_span":0,"arrive_span":0,"deliver_span":0},"pending_sync_member":false,"consumption_time":0,"bowl_num":-2,"id":4893391444972208128}
`

func TestResolvePayment(t *testing.T) {
	test.InitTest()
	ticket := &model.Ticket{}
	json.Unmarshal([]byte(ticketPay), ticket)
	a ,_  := resolvePaymentAmount(context.Background(), ticket)
	for _, m := range a {
		x, _ := m.(*model.SalesPaymentAmount)
		t.Log(x)
	}
}
