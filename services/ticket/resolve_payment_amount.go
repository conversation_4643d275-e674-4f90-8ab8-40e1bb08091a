package ticket

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

// 拆解支付统计表
func resolvePaymentAmount(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	bd, _ := time.Parse(config.DateFormat, ticket.BusDate)
	et := ticket.GetOrderTime()
	type Pay struct {
		PaymentCode          string
		PaymentName          string
		Receivable           float64
		PayAmount            float64
		Rounding             float64
		OverflowAmount       float64
		ChangeAmount         float64
		Qty                  int64
		TpAllowance          float64
		Cost                 float64
		RealAmount           float64
		TransferAmount       float64
		MerchantAllowance    float64
		FinancePayAmount     float64
		FinancePayAmountUsed bool
		//卡号
		CardNo string
		// todo 服务费
		SurChargeAmount float64
		// 支付时间
		PayTime    time.Time
		IsNonSales bool
	}

	storeTagCodes, storeTagNames := storeTagCodesAndNames(ticket)

	var deliveryFeeForPlatform []float64
	if len(ticket.Payments) > 0 {
		payAmounts := make([]float64, len(ticket.Payments))
		for i, v := range ticket.Payments {
			payAmounts[i] = v.PayAmount
		}
		deliveryFeeForPlatform = AmountContributes(ticket.Amounts.DeliveryFeeForPlatform, payAmounts)
	}
	// 建立一个临时存储支付项统计的数据
	tmp := make(map[string]*Pay)
	// 遍历payments中的每一个支付项，包装成一个Pay结构体
	for i, pay := range ticket.Payments {
		var v *Pay
		var in bool
		// 判断这个支付id是否已经存在tem中
		if v, in = tmp[pay.Id]; !in {
			// 不在里面，就赋值交易id为PaymentCode
			name := pay.TransName
			if name == "" {
				name = pay.Name
			}
			v = &Pay{PaymentCode: pay.TransCode, PaymentName: name}
		}
		v.Receivable += pay.Receivable
		v.PayAmount += pay.PayAmount
		v.Rounding += pay.Rounding
		v.OverflowAmount += pay.Overflow
		v.ChangeAmount += pay.Change
		v.Qty++
		v.RealAmount += pay.RealAmount
		v.TransferAmount += pay.TransferAmount
		v.TpAllowance += pay.TpAllowance
		v.Cost += pay.Cost
		v.MerchantAllowance += pay.MerchantAllowance
		v.CardNo = pay.CardNo
		v.SurChargeAmount = pay.SurChargeAmount
		payTime, err := time.Parse(config.DateTimeFormat, pay.PayTime)
		if err != nil {
			payTime = et
		}
		v.PayTime = payTime
		tmp[pay.Id] = v
		// 美团或者饿了么渠道如果只有一个支付项，计算一下财务实付金额
		// 修改该公式，最后减去rounding(抹零)加上overflow(溢收)  2021-11-24 wangheng---
		if len(ticket.Payments) == 1 && (ticket.Channel.TpName == "meituan" || ticket.Channel.TpName == "eleme" || ticket.Channel.TpName == "mt_isv" || ticket.Channel.TpName == "ele_isv" || ticket.Channel.TpName == "douyin_isv" || ticket.Channel.TpName == "keeta_isv") {
			v.FinancePayAmount = ticket.Amounts.BusinessAmount - ticket.Amounts.DiscountMerchantContribute - ticket.TakeawayInfo.MerchantSendFee -
				ticket.Amounts.OtherFee - ticket.Amounts.Commission - ticket.Amounts.PayMerchantContribute - ticket.Amounts.Rounding + ticket.Amounts.OverflowAmount
			v.FinancePayAmountUsed = true
		} else if len(deliveryFeeForPlatform) > 0 {
			v.FinancePayAmount = v.Cost + v.TpAllowance - deliveryFeeForPlatform[i]
			v.FinancePayAmountUsed = true
		}
		if helpers.Contains(pay.TransactionTypes, "NON_SALES") {
			v.IsNonSales = true
		}
	}
	// 遍历Payment中每一项，形成一条SalesPaymentAmount记录
	for id, item := range tmp {
		rsm := &model.SalesPaymentAmount{
			Id:                   utils.GetHexUUid(ctx),
			PartnerId:            cast.ToInt64(ticket.Store.PartnerId),
			ScopeId:              cast.ToInt64(ticket.Store.ScopeId),
			BusDate:              time.Date(bd.Year(), bd.Month(), bd.Day(), 0, 0, 0, 0, time.UTC),
			StoreId:              utils.StringMust2Int(ticket.Store.Id),
			EticketId:            ticket.Id,
			OrderTime:            et,
			Refunded:             ticket.Status == model.REFUND || ticket.Status == model.PARTIALREFUND,
			PaymentId:            utils.StringMust2Int(id),
			PaymentCode:          item.PaymentCode,
			PaymentName:          item.PaymentName,
			Receivable:           item.Receivable,
			PayAmount:            item.PayAmount,
			Rounding:             item.Rounding,
			OverflowAmount:       item.OverflowAmount,
			ChangeAmount:         item.ChangeAmount,
			Created:              time.Now().UTC(),
			Qty:                  item.Qty,
			EticketCount:         1,
			OrderStatus:          ticket.Status,
			ChannelId:            ticket.Channel.Id,
			TpAllowance:          item.TpAllowance,
			RealAmount:           item.RealAmount,
			TransferAmount:       item.TransferAmount,
			Cost:                 item.Cost,
			MerchantAllowance:    item.MerchantAllowance,
			FinancePayAmount:     item.FinancePayAmount,     // 财务实收
			FinancePayAmountUsed: item.FinancePayAmountUsed, // 财务实收标识
			StoreTags:            storeTagCodes,
			StoreTagNames:        storeTagNames,
			CardNo:               item.CardNo,
			PayTime:              item.PayTime,
			SurChargeAmount:      item.SurChargeAmount,
			IsNonSales:           item.IsNonSales,
		}
		rsm.BusDateYear = time.Date(bd.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		rsm.BusDateMonth = time.Date(bd.Year(), bd.Month(), 1, 0, 0, 0, 0, time.UTC)
		var sub int
		if sub = int(rsm.BusDate.Weekday()) - 1; sub < 0 {
			sub = 6
		}
		if rsm.Refunded {
			rsm.EticketCount = -rsm.EticketCount
			rsm.Qty = -rsm.Qty
		}

		if rsm.BusDateWeek = rsm.BusDate.Add(-time.Duration(sub) * 24 * time.Hour); rsm.BusDateWeek.Year() != rsm.BusDate.Year() {
			rsm.BusDateWeek = time.Date(rsm.BusDate.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
		}
		resolveResult = append(resolveResult, rsm)
	}
	return
}
