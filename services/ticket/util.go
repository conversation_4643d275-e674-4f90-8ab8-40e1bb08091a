package ticket

import (
	"math"
)

func AmountContributes(amt float64, amounts []float64) []float64 {
	if len(amounts) == 0 {
		return make([]float64, 0)
	}

	var sum float64
	for _, amount := range amounts {
		sum = sum + amount
	}

	if sum == 0 {
		return make([]float64, len(amounts))
	}

	contributesExcludeLast := make([]float64, 0)
	for i, a := range amounts {
		if i != len(amounts)-1 {
			contributesExcludeLast = append(contributesExcludeLast, math.Round(amt*(a/sum)*100)/100)
		}
	}

	contributes := make([]float64, 0)
	rest := amt
	for _, a := range contributesExcludeLast {
		if math.Abs(rest) < math.Abs(a) {
			contributes = append(contributes, rest)
			rest = 0
		} else {
			contributes = append(contributes, a)
			rest = rest - a
		}
	}
	contributes = append(contributes, rest)

	return contributes
}
