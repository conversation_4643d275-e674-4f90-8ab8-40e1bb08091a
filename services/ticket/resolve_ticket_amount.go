package ticket

import (
	"context"
	"github.com/spf13/cast"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
)

// 拆解销售金额统计表
func resolveSalesMoney(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error) {
	bd, _ := time.Parse(config.DateFormat, ticket.BusDate)
	s := strings.Split(ticket.EndTime, " ")[0]
	nd, _ := time.Parse(config.DateFormat, s)
	et := ticket.GetOrderTime()
	var pc, pac int
	for _, item := range ticket.Products {
		pc += item.Qty
		for _, it := range item.Accessories {
			pac += it.Qty
		}
	}
	people := ticket.Table.People
	//if people == 0 && ticket.Status == model.SALE {
	//	people = 1
	//}
	//if people == 0 && ticket.Status == model.REFUND {
	//	people = -1
	//}

	storeTagCodes, storeTagNames := storeTagCodesAndNames(ticket)

	openTableAt, _ := time.ParseInLocation(config.DateTimeFormat, ticket.Table.OpenTableAt, config.ShanghaiLocation)
	var couponChannelCode, couponChannelName string
	for _, coupon := range ticket.Coupons {
		couponChannelCode = coupon.ChannelCode
		couponChannelName = coupon.ChannelName
		if couponChannelCode != "" && couponChannelName != "" {
			break
		}
	}

	// 组装门店报表模型数据
	rsm := &model.SalesTicketAmount{
		Id:                         utils.GetHexUUid(ctx),
		PartnerId:                  cast.ToInt64(ticket.Store.PartnerId),
		ScopeId:                    cast.ToInt64(ticket.Store.ScopeId),
		BusDate:                    time.Date(bd.Year(), bd.Month(), bd.Day(), 0, 0, 0, 0, time.UTC),
		NaturalDate:                time.Date(nd.Year(), nd.Month(), nd.Day(), 0, 0, 0, 0, time.UTC),
		StoreId:                    utils.StringMust2Int(ticket.Store.Id),
		EticketId:                  ticket.Id,
		ChannelId:                  utils.StringMust2Int(ticket.Channel.Id),
		ChannelName:                ticket.Channel.TpName,
		OrderType:                  ticket.Channel.OrderType,
		OrderTypeName:              ticket.Channel.OrderType,
		OrderTime:                  et,
		Refunded:                   ticket.Status == model.REFUND || ticket.Status == model.PARTIALREFUND,
		GrossAmount:                ticket.Amounts.GrossAmount,
		NetAmount:                  ticket.Amounts.NetAmount,
		DiscountAmount:             ticket.Amounts.DiscountAmount,
		Tip:                        ticket.Amounts.Tip,
		PackageFee:                 ticket.TakeawayInfo.PackageFee,        // 打包费统一使用外卖信息下面的
		DeliveryFee:                ticket.Amounts.DeliveryFee,            // 配送费使用外卖下面的配送费，之前的方式取消
		DeliveryFeeForMerchant:     ticket.Amounts.DeliveryFeeForMerchant, // 累计付给商家配送费（自配送场景） (对应收银汇总表的配送费商家流水)
		ServiceFee:                 ticket.Amounts.ServiceFee,
		TaxFee:                     ticket.Amounts.TaxAmount,
		PayAmount:                  ticket.Amounts.PayAmount,
		Rounding:                   ticket.Amounts.Rounding,
		OverflowAmount:             ticket.Amounts.OverflowAmount,
		ChangeAmount:               ticket.Amounts.ChangeAmount,
		Commission:                 ticket.Amounts.Commission,
		MerchantDiscountAmount:     ticket.Amounts.MerchantDiscountAmount, // 商家补贴金额
		PlatformDiscountAmount:     ticket.Amounts.PlatformDiscountAmount, // 第三方补贴金额
		PaymentTransferAmount:      ticket.Amounts.PaymentTransferAmount,  //支付转折扣
		DiscountTransferAmount:     ticket.Amounts.DiscountTransferAmount, //折扣转支付
		RefundCode:                 ticket.RefundInfo.RefundCode,          //退单原因code
		RefundSide:                 ticket.RefundInfo.RefundSide,          //退单方
		RefundReason:               ticket.RefundInfo.RefundReason,        //退单原因
		Amount0:                    ticket.Amounts.BusinessAmount,         // 营业额
		Amount1:                    ticket.Amounts.ExpendAmount,           // 支出
		Amount2:                    ticket.Amounts.RealAmount,             // 实收金额转换后
		Amount3:                    ticket.Amounts.ProjectedIncome,        // 实收金额
		Amount4:                    ticket.Amounts.Receivable,             // 应付金额
		MerchantSendFee:            ticket.TakeawayInfo.MerchantSendFee,   // 商家承担配送费
		StoreDiscountAmount:        ticket.Amounts.StoreDiscountAmount,    // 门店折扣金额
		ProductCount:               pc,
		AccessoryCount:             pac,
		EticketCount:               1,
		Created:                    time.Now().UTC(),
		OrderStatus:                ticket.Status,
		Member:                     len(ticket.Members) > 0, // 是否会员
		DiscountMerchantContribute: ticket.Amounts.DiscountMerchantContribute,
		DiscountPlatformContribute: ticket.Amounts.DiscountPlatformContribute,
		DiscountBuyerContribute:    ticket.Amounts.DiscountBuyerContribute,
		DiscountOtherContribute:    ticket.Amounts.DiscountOtherContribute,
		PayMerchantContribute:      ticket.Amounts.PayMerchantContribute,
		PayPlatformContribute:      ticket.Amounts.PayPlatformContribute,
		PayBuyerContribute:         ticket.Amounts.PayBuyerContribute,
		PayOtherContribute:         ticket.Amounts.PayOtherContribute,
		SendFeeForMerchant:         ticket.TakeawayInfo.SendFeeForMerchant,
		SendFee:                    ticket.TakeawayInfo.SendFee,
		SendFeeForPlatform:         ticket.TakeawayInfo.SendFeeForPlatform,
		PlatformSendFee:            ticket.TakeawayInfo.PlatformSendFee,
		OtherFee:                   ticket.Amounts.OtherFee, // 其他
		SurchargeAmount:            ticket.Amounts.SurChargeAmount,
		SendFeeRate:                ticket.TakeawayInfo.SendFeeRate,
		PackageFeeRate:             ticket.TakeawayInfo.PackageFeeRate,
		MealSegmentName:            ticket.MealSegmentName,
		People:                     float64(people),
		StoreTags:                  storeTagCodes,
		StoreTagNames:              storeTagNames,
		CouponChannelCode:          couponChannelCode,
		CouponChannelName:          couponChannelName,
		ZoneName:                   ticket.Table.ZoneName,
		TableNo:                    ticket.Table.No,
		OpenTableBy:                ticket.Table.OpenTableBy,
		OperatorName:               ticket.Operator.Name,
		OperatorCode:               ticket.Operator.Code,
		OpenTableAt:                openTableAt,
		EndTime:                    et,
		TicketNo:                   ticket.TakeawayInfo.TpOrderId,
		BowlNum:                    float64(ticket.BowlNum),
		ConsumptionTime:            ticket.ConsumptionTime,
		RealTicketNo:               ticket.TicketNo,
		PosDeviceCode:              ticket.Pos.Code,
		RefTicketNo:                ticket.RefundInfo.RefTicketNo, //原单交易号
		PlateNo:                    ticket.PlateNo,
	}
	if len(ticket.TakeawayInfo.PhoneList) != 0 {
		rsm.PhoneNo = ticket.TakeawayInfo.PhoneList[0]
	}
	rsm.BusDateYear = time.Date(bd.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
	rsm.BusDateMonth = time.Date(bd.Year(), bd.Month(), 1, 0, 0, 0, 0, time.UTC)
	var sub int
	if sub = int(rsm.BusDate.Weekday()) - 1; sub < 0 {
		sub = 6
	}
	if rsm.BusDateWeek = rsm.BusDate.Add(-time.Duration(sub) * 24 * time.Hour); rsm.BusDateWeek.Year() != rsm.BusDate.Year() {
		rsm.BusDateWeek = time.Date(rsm.BusDate.Year(), time.January, 1, 0, 0, 0, 0, time.UTC)
	}
	if rsm.Refunded {
		rsm.EticketCount = -rsm.EticketCount
		if rsm.People > 0 {
			rsm.People = -rsm.People
		}
		if rsm.BowlNum > 0 {
			rsm.BowlNum = -rsm.BowlNum
		}
		if rsm.ConsumptionTime > 0 {
			rsm.ConsumptionTime = -rsm.ConsumptionTime
		}
	}
	if rsm.OrderStatus == model.PARTIALREFUND {
		rsm.EticketCount = 0
	}

	return []model.Resolve{rsm}, nil
}
