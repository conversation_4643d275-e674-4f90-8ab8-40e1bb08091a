package ticket

import (
	"context"
	"errors"
	"github.com/spf13/cast"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

var (
	IllegalProcessStatus error = errors.New("illegal_process_status")
	handels              []resolveTicket
)

type resolveTicket func(ctx context.Context, ticket *model.Ticket) (resolveResult []model.Resolve, err error)

// 拆解5张基础表的函数
func init() {
	handels = append(handels, resolveSalesMoney)
	handels = append(handels, resolveProductAmount)
	handels = append(handels, resolvePaymentAmount)
	handels = append(handels, resolveDiscountAmount)
	handels = append(handels, resolveItemPaymentAmount)
}

// 拆分成5张基础表
func ResolveTicket(ctx context.Context, salesId int64) (err error) {
	trackId := ctx.Value(config.TrackId)
	start := time.Now()
	logger.Pre().Infof("[%d] ResolveTicket Start, id: %v\n", trackId, salesId)
	var (
		si     *model.SalesTicketContent
		ticket *model.Ticket
	)
	if salesId == 0 {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicket] Couldn't find by Id `%d`.\nData: `%+v`\nErr: `%+v`", trackId, salesId, si, err)
		return
	}
	if si, err = repo.DefaultSalesDB.FindSales(ctx, &model.SalesTicketContent{Id: salesId}); err != nil || si == nil {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicket] Couldn't find by Id `%d`.\nData: `%+v`\nErr: `%+v`", trackId, salesId, si, err)
		return
	}
	if si.ProcessStatus != model.Process_Discountsh {
		logger.Pre().Warnf("[%d] [services.ticket.ResolveTicket] Illegal process status. Except: `%s`, NOT `%s`.", trackId, model.Process_Discountsh, si.ProcessStatus)
		return nil
	}

	if ticket, err = si.ToTicket(); err != nil {
		logger.Pre().Errorf("[%d] [services.ticket.ResolveTicket] Unmashal ticket failed. Content: `%s`\nModifContent: `%s`\nErr: `%+v`.", trackId, string(*si.Content), string(*si.ContentModified), err)
		return
	}
	ticket.Store.PartnerId = cast.ToString(si.PartnerId)
	ticket.Store.ScopeId = cast.ToString(si.ScopeId)

	var result [][]model.Resolve

	// 执行拆解5个表的处理流程
	for _, item := range handels {
		var v []model.Resolve
		if v, err = item(ctx, ticket); err != nil {
			return err
		}
		// 映射后的基础表对象模型
		result = append(result, v)
	}
	update := &model.SalesTicketContent{
		Id:        si.Id,
		EticketId: si.EticketId,
		Status:    model.Finish,
	}
	if update.ContentModified = si.ContentModified; update.ContentModified == nil {
		update.ContentModified = si.Content
	}

	resolveAll := toNormalResolveAll(ticket, &result)

	// 使用事务将报表数据添加到5个数据库中
	for i := 0; i < 3; i++ { // 重复3次，如果还报错，说明真的有问题
		err = repo.DefaultResolveTicket.InsertResolveTicket(ctx, update, result...)
		err_all := repo.DefaultResolveTicketAll.InsertResolveTicketAll(ctx, &model.SalesTicketContentAbnormal{
			EticketId: update.EticketId,
		}, true, *resolveAll...)
		if err_all != nil {
			logger.Pre().Errorf("[%d] [services.ticket.ResolveTicket] InsertResolveTicketAll failed. Content: `%s`\nModifContent: `%s`\nErr: `%+v`", trackId, string(*si.Content), string(*si.ContentModified), err)
		}
		if err == nil {
			break
		}
		logger.Pre().Warningf("insert resolve ticket err:%v", err)
	}
	if err != nil {
		return err
	}
	elapsed := time.Since(start)
	logger.Pre().Infof("[%d] ResolveTicket End, id:%v, elapsed: %v\n", trackId, salesId, elapsed)
	return nil
}

func toNormalResolveAll(ticket *model.Ticket, result *[][]model.Resolve) *[][]model.Resolve {
	var resolveAll [][]model.Resolve
	for _, rs := range *result {
		var resolveAllInner []model.Resolve
		for _, r := range rs {
			switch r.(type) {
			case *model.SalesTicketAmount:
				resolveAllInner = append(resolveAllInner, r.(*model.SalesTicketAmount).ToAll(ticket, 0))
			case *model.SalesPaymentAmount:
				resolveAllInner = append(resolveAllInner, r.(*model.SalesPaymentAmount).ToAll(ticket, 0))
			default:
			}
		}
		if len(resolveAllInner) > 0 {
			resolveAll = append(resolveAll, resolveAllInner)
		}
	}
	return &resolveAll
}

func storeTagCodesAndNames(ticket *model.Ticket) ([]string, []string) {
	storeTagCodes := make([]string, 0)
	storeTagNames := make([]string, 0)
	for _, tag := range ticket.Store.Tags {
		storeTagCodes = append(storeTagCodes, tag.Code)
		storeTagNames = append(storeTagNames, tag.Name)
	}
	return storeTagCodes, storeTagNames
}
