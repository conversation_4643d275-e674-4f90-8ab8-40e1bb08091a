package financial

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/translation"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/pos"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"math"
	"sort"
)

func QueryFinancialReport(ctx context.Context, query *model.CommonRequest) (*model.FinancialResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryStoreSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesTicketAmount{}
	// 组装Sql查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}

	// 调用门店销售报表接口层
	resp, err := repo.DefaultFinancialRepository.FinancialReport(ctx, condition)
	packagingForFinancialReport(ctx, query, resp)
	return resp, err
}

func packagingForFinancialReport(ctx context.Context, query *model.CommonRequest, resp *model.FinancialResponse) {
	regionIds := make([]int64, len(resp.Rows))
	branchRegionIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	paymentIds := make([]int64, 0)
	discountIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(resp.Rows))
	incomeSubjectDetailIds := make([]int64, 0)
	for _, r := range resp.Rows {
		regionIds = append(regionIds, r.RegionId)
		branchRegionIds = append(branchRegionIds, r.BranchRegionId)
	}
	if resp.Summary == nil {
		return
	}
	for _, r := range resp.Summary.CompositionOfDiscount {
		channelIds = append(channelIds, r.ChannelId)
		for _, r2 := range r.Payments {
			paymentIds = append(paymentIds, r2.PaymentId)
		}
		for _, d := range r.Discounts {
			discountIds = append(discountIds, d.DiscountId)
		}
	}
	for _, r := range resp.Summary.CompositionOfPaidIn {
		channelIds = append(channelIds, r.ChannelId)
		for _, d := range r.Discounts {
			discountIds = append(discountIds, d.DiscountId)
		}
		for _, p := range r.Payments {
			paymentIds = append(paymentIds, p.PaymentId)
		}
	}
	for _, r := range resp.Summary.CompositionOfTakeAway {
		channelIds = append(channelIds, r.ChannelId)
	}
	regionMaps := report.GetRegionMapsByIds(ctx, query, regionIds)
	branchRegionMaps := report.GetRegionMapsByIdsSchema(ctx, "branch_region", query.Lan, branchRegionIds)
	channelMaps := GetChannelMethodMaps(ctx, channelIds, query.Lan)
	paymentMaps := GetPaymentMethodMaps(ctx, paymentIds, query.Lan)
	discountMaps := GetDiscountMethodMaps(ctx, discountIds, query.Lan)
	storeTypeMaps := report.GetStoreTypeMapsByIds(ctx, query, storeTypes)
	incomeSubjectSort := GetIncomeSubjectSort(ctx, incomeSubjectDetailIds, query.Lan)
	discountIdMap := buildDiscountIdMapForFinancialReport(resp)
	for _, r := range resp.Rows {
		packagingForFinancialReportOne(ctx, discountIdMap, regionMaps, branchRegionMaps, storeTypeMaps, channelMaps, paymentMaps, discountMaps, incomeSubjectSort, r, query)
	}
	packagingForFinancialReportOne(ctx, discountIdMap, regionMaps, branchRegionMaps, storeTypeMaps, channelMaps, paymentMaps, discountMaps, incomeSubjectSort, resp.Summary, query)
}

// 由于取的是max 所以rows和summary名称一样的id可能不一样，并且id一样的名称可能不一样，所以需要做一下处理
// 让名称相同的id一样
func buildDiscountIdMapForFinancialReport(resp *model.FinancialResponse) map[string]int64 {
	discountIdMap := make(map[string]int64)
	// 不能从0开始，data-export 有特殊判断
	var count int64 = 10010000
	discountNames := make([]string, 0)
	discountNameMap := make(map[string]struct{})
	for _, r := range resp.Summary.CompositionOfDiscount {
		for _, d := range r.Discounts {
			discountName := fmt.Sprintf("%d%s", d.DiscountId, d.DiscountName)
			if _, ok := discountNameMap[discountName]; !ok {
				discountNames = append(discountNames, discountName)
				discountNameMap[discountName] = struct{}{}
			}
		}
	}
	sort.Sort(sort.StringSlice(discountNames))
	for _, name := range discountNames {
		if _, ok := discountIdMap[name]; !ok {
			discountIdMap[name] = count
			count += 1
		}
	}
	return discountIdMap
}

func packagingForFinancialReportOne(ctx context.Context, discountIdMap map[string]int64, storeMaps, branchRegionMaps, storeTypeMaps, channelMaps, paymentMaps, discountMaps map[int64]map[string]interface{}, incomeSubjectSort map[int64]int, row *model.Financial, query *model.CommonRequest) {

	row.RegionName = cast.ToString(storeMaps[row.RegionId]["name"])
	row.RegionCode = cast.ToString(storeMaps[row.RegionId]["code"])
	row.RegionAddress = cast.ToString(storeMaps[row.RegionId]["region_name"])
	row.RegionAddress1 = cast.ToString(storeMaps[row.RegionId]["region_name1"])
	row.RegionAddress2 = cast.ToString(storeMaps[row.RegionId]["region_name2"])
	row.RegionAddress3 = cast.ToString(storeMaps[row.RegionId]["region_name3"])
	row.RegionAlias = cast.ToString(storeMaps[row.RegionId]["alias"])
	row.BranchRegion = cast.ToString(branchRegionMaps[row.BranchRegionId]["name"]) // 管理区域
	// 四舍五入保留两位小数
	row.RealAmountPerCapita = math.Round(row.RealAmountPerCapita*100) / 100
	row.CustomerCountPerDay = math.Round(row.CustomerCountPerDay*100) / 100
	row.RealAmountPerDay = math.Round(row.RealAmountPerDay*100) / 100
	row.RealAmountPerBowl = math.Round(row.RealAmountPerBowl*100) / 100

	for i := range row.CompositionOfBillTypes {
		row.CompositionOfBillTypes[i].OrderName = translation.OrderType[query.Lan][row.CompositionOfBillTypes[i].OrderType]
	}
	for i := range row.CompositionOfDiscount {
		row.CompositionOfDiscount[i].ChannelName = cast.ToString(
			channelMaps[row.CompositionOfDiscount[i].ChannelId]["name"])
		if row.CompositionOfDiscount[i].ChannelName == "HiPOS" {
			row.CompositionOfDiscount[i].ChannelName = ""
		}
		tmp := make([]struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		}, 0)
		for _, v := range row.CompositionOfDiscount[i].Payments {
			// 优惠组成如果支付优惠为零，删除该支付方式
			if v.DiscountContribute != 0 {
				//TODO jiqin 处理如果支付名称为空 就调用数据 兼容处理
				if v.PaymentName == "" {
					paymentId := v.PaymentId
					v.PaymentName = cast.ToString(
						paymentMaps[paymentId]["name"])
				}
				tmp = append(tmp, v)
			}
		}
		sort.Slice(tmp, func(i, j int) bool {
			return tmp[i].PaymentName < tmp[j].PaymentName
		})
		row.CompositionOfDiscount[i].Payments = tmp

		tmpp := make([]struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		}, 0)
		for _, v := range row.CompositionOfDiscount[i].Discounts {
			// 优惠组成如果折扣优惠为零，删除该折扣方式
			if v.DiscountContribute != 0 {
				//TODO jiqin 处理如果支付名称为空 就调用数据 兼容处理
				channelName := discountMaps[v.DiscountId]["name"]
				if v.DiscountName == "" {
					v.DiscountName = cast.ToString(cast.ToString(channelName))
				}
				v.DiscountId = discountIdMap[fmt.Sprintf("%d%s", v.DiscountId, v.DiscountName)]
				if channelName != nil {
					v.DiscountName = fmt.Sprintf("%s%s", channelName, v.DiscountName)
				}
				tmpp = append(tmpp, v)
			}
		}
		sort.Slice(tmpp, func(i, j int) bool {
			return tmpp[i].DiscountName < tmpp[j].DiscountName
		})
		row.CompositionOfDiscount[i].Discounts = tmpp
	}
	for i := range row.CompositionOfPaidIn {
		row.CompositionOfPaidIn[i].ChannelName = cast.ToString(
			channelMaps[row.CompositionOfPaidIn[i].ChannelId]["name"])
		if row.CompositionOfPaidIn[i].ChannelName == "HiPOS" {
			row.CompositionOfPaidIn[i].ChannelName = ""
		}
		tmp := make([]struct {
			DiscountId         int64   `json:"discount_id"`
			DiscountName       string  `json:"discount_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		}, 0)
		for _, v := range row.CompositionOfPaidIn[i].Discounts {
			// 实收组成如果折扣实收为零，删除该折扣方式
			if v.TransferRealAmount != 0 {
				//TODO jiqin 处理如果支付名称为空 就调用数据 兼容处理
				channelName := discountMaps[v.DiscountId]["name"]
				if v.DiscountName == "" {
					v.DiscountName = cast.ToString(channelName)
				}
				v.DiscountId = discountIdMap[fmt.Sprintf("%d%s", v.DiscountId, v.DiscountName)]
				if channelName != nil {
					v.DiscountName = fmt.Sprintf("%s%s", channelName, v.DiscountName)
				}
				tmp = append(tmp, v)
			}

		}
		sort.Slice(tmp, func(i, j int) bool {
			return tmp[i].DiscountName < tmp[j].DiscountName
		})
		row.CompositionOfPaidIn[i].Discounts = tmp

		tmpp := make([]struct {
			PaymentId          int64   `json:"payment_id"`
			PaymentName        string  `json:"payment_name"`
			RealAmount         float64 `json:"real_amount"`
			DiscountContribute float64 `json:"discount_contribute"`
			TransferRealAmount float64 `json:"transfer_real_amount"`
		}, 0)
		for _, v := range row.CompositionOfPaidIn[i].Payments {
			// 实收组成如果支付实收为零，删除该支付方式
			if v.TransferRealAmount != 0 {
				//TODO jiqin 处理如果支付名称为空 就调用数据 兼容处理
				if v.PaymentName == "" {
					paymentId := v.PaymentId
					v.PaymentName = cast.ToString(
						paymentMaps[paymentId]["name"])
				}
				tmpp = append(tmpp, v)
				//getCouponType := cast.ToString(paymentMaps[paymentId]["getCouponType"])
				//if getCouponType != "free" { // 如果是无偿，删除该记录
				//	tmpp = append(tmpp, v)
				//}
			}
		}
		sort.Slice(tmpp, func(i, j int) bool {
			return tmpp[i].PaymentName < tmpp[j].PaymentName
		})
		row.CompositionOfPaidIn[i].Payments = tmpp
	}
	for i := range row.CompositionOfTakeAway {
		row.CompositionOfTakeAway[i].ChannelName = cast.ToString(
			channelMaps[row.CompositionOfTakeAway[i].ChannelId]["name"])
	}

	// 汇总行需要进行排序
	if row.RegionId == 0 {
		// 用餐方式排序
		sort.Slice(row.CompositionOfBillTypes, func(i, j int) bool {
			return model.OrderMap[row.CompositionOfBillTypes[i].OrderType] < model.OrderMap[row.CompositionOfBillTypes[j].OrderType]
		})
		// 餐段排序
		mealPeriod := &pos.MealPeriod{}
		for _, value := range storeMaps {
			if mealPeriodData, ok := value["meal_period"]; ok {
				tmp, err := json.Marshal(&mealPeriodData)
				if err != nil {
					logrus.Errorf("getMealPeriod meal_period Marshal:%v", err)
					continue
				}
				err = json.Unmarshal(tmp, mealPeriod)
				if err != nil {
					logrus.Infof("getMealPeriod meal_period %s: %v", tmp, mealPeriod)
					continue
				}
				break
			}
		}
		if mealPeriod == nil {
			return
		}
		// 定义餐段式排序用的顺序映射
		mealPeriodMap := make(map[string]int)
		for index, v := range mealPeriod.Data {
			mealPeriodMap[v.Name] = index
		}
		sort.Slice(row.MealSegments, func(i, j int) bool {
			return mealPeriodMap[row.MealSegments[i].MealSegmentName] < mealPeriodMap[row.MealSegments[j].MealSegmentName]
		})
		// 渠道排序
		sort.Slice(row.CompositionOfTakeAway, func(i, j int) bool {
			return row.CompositionOfTakeAway[i].ChannelName < row.CompositionOfTakeAway[j].ChannelName
		})
		// 实收排序
		sort.Slice(row.CompositionOfPaidIn, func(i, j int) bool {
			return row.CompositionOfPaidIn[i].ChannelName < row.CompositionOfPaidIn[j].ChannelName
		})
		// 优惠排序
		sort.Slice(row.CompositionOfDiscount, func(i, j int) bool {
			return row.CompositionOfDiscount[i].ChannelName < row.CompositionOfDiscount[j].ChannelName
		})
	}

}
