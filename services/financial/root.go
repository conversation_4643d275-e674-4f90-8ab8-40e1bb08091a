package financial

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
)

func GetStoreMaps(ctx context.Context, ids []int64, lan string) map[int64]map[string]interface{} {
	storeMaps, _ := entity.GetIdsFieldMap(ctx, "store", ids, lan)
	return storeMaps
}

// 获取门店信息
func GetRegionMaps(ctx context.Context, ids []int64, lan string) map[int64]map[string]interface{} {
	regionMaps, _ := entity.GetIdsFieldMap(ctx, "store", ids, lan)
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _, _, _, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

// 获取支付方式map
func GetPaymentMethodMaps(ctx context.Context, ids []int64, lan string) map[int64]map[string]interface{} {
	paymentMethodMaps, _ := entity.GetIdsFieldMap(ctx, "PAYMENT_CHANNEL", ids, lan)
	return paymentMethodMaps
}

func GetChannelMethodMaps(ctx context.Context, ids []int64, lan string) map[int64]map[string]interface{} {
	paymentMethodMaps, _ := entity.GetIdsFieldMap(ctx, "CHANNEL_COMPANY", ids, lan)
	return paymentMethodMaps
}

func GetDiscountMethodMaps(ctx context.Context, codes []int64, lan string) map[int64]map[string]interface{} {
	paymentMethodMaps, _ := entity.GetCodesFieldMap(ctx, "promotion_rule", codes, lan)
	return paymentMethodMaps
}

// GetIncomeSubjectSort 获取收入科目排序
func GetIncomeSubjectSort(ctx context.Context, ids []int64, lan string) map[int64]int {
	sort := make(map[int64]int)
	if len(ids) == 0 {
		return sort
	}
	maps, _ := entity.GetIdsFieldMap(ctx, "INCOME_SUBJECT_DETAIL", ids, lan)
	for id, v := range maps {
		sort[id] = cast.ToInt(v["sort"])
	}
	return sort
}
