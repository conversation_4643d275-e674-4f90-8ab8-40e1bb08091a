package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/grpc/oms"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"math"
	"time"
)

const TRANSFER_LEN int = 1000

func GetStoreMapsForDashboard(ctx context.Context, query *model.DashboardRequest, resp *model.DashboardResponse,
) map[int64]map[string]interface{} {
	storeIds := make([]int64, 0, len(resp.StoreRank))
	for _, row := range resp.StoreRank {
		r := row.(map[string]interface{})
		storeIds = append(storeIds, cast.ToInt64(r["store_id"]))
	}
	storeMaps, _ := entity.GetIdsFieldMap(ctx, "store", storeIds, query.Lan)
	return storeMaps
}

func GetChannelMapsForDashboard(ctx context.Context, query *model.DashboardRequest, resp *model.DashboardResponse,
) map[int64]map[string]interface{} {
	channelIds := make([]int64, 0, len(resp.ChannelRank))
	for _, row := range resp.ChannelRank {
		r := row.(map[string]interface{})
		channelIds = append(channelIds, cast.ToInt64(r["channel_id"]))
	}
	channelMaps, _ := entity.GetIdsFieldMap(ctx, "payment_channel", channelIds, query.Lan)
	return channelMaps
}

func GetProductMapsForDashboard(ctx context.Context, query *model.DashboardRequest, resp *model.DashboardResponse,
) map[int64]map[string]interface{} {
	productIds := make([]int64, 0, len(resp.ProductRank))
	for _, row := range resp.ProductRank {
		r := row.(map[string]interface{})
		productIds = append(productIds, cast.ToInt64(r["product_id"]))
	}
	productMaps, _ := entity.GetIdsFieldMap(ctx, "product", productIds, query.Lan)
	return productMaps
}

// 获取门店信息
func GetRegionMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	regionIds := make([]int64, 0, len(resp.Rows))
	if len(resp.Rows) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionIds = append(regionIds, cast.ToInt64(r["region_id"]))
	}
	schema := "store"
	switch query.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "geo_region"
		}
	case "BRANCH_REGION": // 管理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "branch_region"
		}
	case "FRANCHISEE_REGION": // 加盟商管理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "franchisee_region"
		}
	case "COMPANY":
		schema = "COMPANY_INFO"
	}
	regionMaps, err := entity.GetIdsFieldMap(ctx, schema, regionIds, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _, _, _, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

func GetProductCategoryMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	productCategoryIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		pro_cat_id0 := cast.ToInt64(r["product_category_id"])
		pro_cat_id1 := cast.ToInt64(r["product_category_id1"])
		pro_cat_id2 := cast.ToInt64(r["product_category_id2"])
		if pro_cat_id0 != 0 {
			productCategoryIds = append(productCategoryIds, pro_cat_id0)
		}
		if pro_cat_id1 != 0 {
			productCategoryIds = append(productCategoryIds, pro_cat_id1)
		}
		if pro_cat_id2 != 0 {
			productCategoryIds = append(productCategoryIds, pro_cat_id2)
		}
	}
	productCategoryMaps, _ := entity.GetIdsFieldMap(ctx, "product_category", productCategoryIds, query.Lan)
	return productCategoryMaps
}

func GetDiscountMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	discontIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		discontIds = append(discontIds, cast.ToInt64(r["promotion_id"]))
	}
	discontMaps, _ := entity.GetIdsFieldMap(ctx, "promotion_rule", discontIds, query.Lan)
	return discontMaps
}

// 获取支付方式map
func GetPaymentMethodMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	paymentMethodIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		paymentMethodIds = append(paymentMethodIds, cast.ToInt64(r["payment_id"]))
	}
	paymentMethodMaps, _ := entity.GetIdsFieldMap(ctx, "payment_channel", paymentMethodIds, query.Lan)
	return paymentMethodMaps
}

// 获取支付渠道map
func GetPaymentChannelMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	paymentChannelIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		paymentChannelIds = append(paymentChannelIds, cast.ToInt64(r["channel_id"]))
	}
	paymentChannelMaps, _ := entity.GetIdsFieldMap(ctx, "channel_company", paymentChannelIds, query.Lan)
	return paymentChannelMaps
}

// 获取折扣渠道map
func GetDiscountChannelMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	discountChannelIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		discountChannelIds = append(discountChannelIds, cast.ToInt64(r["channel_id"]))
	}
	discountChannelMaps, _ := entity.GetIdsFieldMap(ctx, "discount_channel", discountChannelIds, query.Lan)
	return discountChannelMaps
}

// 转成时段报表map
func ToPeriodMaps(hours, data []interface{}) map[string]interface{} {
	periodMaps := make(map[string]interface{})
	if len(hours) != len(data) || len(hours) < 1 {
		return periodMaps
	}
	var key string
	for i, hour := range hours {
		if value, ok := hour.(float64); ok {
			key = fmt.Sprintf("%02d", cast.ToInt8(value))
		} else {
			key = cast.ToString(hour)
		}
		periodMaps[key] = cast.ToFloat64(periodMaps[key]) + cast.ToFloat64(data[i])
	}
	return periodMaps
}

// 转成营业额的map
func ToBusinessMaps(hours, business []interface{}) map[string]interface{} {
	var periodMaps = make(map[string]interface{})
	if len(hours) != len(business) {
		return periodMaps
	}
	for i := range hours {
		key := fmt.Sprintf("%02d", cast.ToInt8(hours[i]))
		periodMaps[key] = cast.ToFloat64(business[i])
	}
	return periodMaps
}

// 转成实收的map
func ToExpendAmount(hours, expend []interface{}) map[string]interface{} {
	var periodMaps = make(map[string]interface{})
	if len(hours) != len(expend) {
		return periodMaps
	}
	for i := range hours {
		key := fmt.Sprintf("%02d", cast.ToInt8(hours[i]))
		periodMaps[key] = cast.ToFloat64(expend[i])
	}
	return periodMaps
}

// 转成支出的map
func ToRealAmount(hours []interface{}, bussiness, expend map[string]interface{}) map[string]interface{} {
	var periodMaps = make(map[string]interface{})
	if len(hours) != len(bussiness) || len(hours) != len(expend) {
		return periodMaps
	}
	for i := range hours {
		key := fmt.Sprintf("%02d", cast.ToInt8(hours[i]))
		periodMaps[key] = cast.ToFloat64(bussiness[key]) - cast.ToFloat64(expend[key])
	}
	return periodMaps
}

// 为预计算转成时段报表map
func ToPeriodMapsForPre(fields string, data map[string]interface{}) map[string]interface{} {
	return map[string]interface{}{
		"00": cast.ToFloat64(data[fmt.Sprintf("%s_00", fields)]),
		"01": cast.ToFloat64(data[fmt.Sprintf("%s_01", fields)]),
		"02": cast.ToFloat64(data[fmt.Sprintf("%s_02", fields)]),
		"03": cast.ToFloat64(data[fmt.Sprintf("%s_03", fields)]),
		"04": cast.ToFloat64(data[fmt.Sprintf("%s_04", fields)]),
		"05": cast.ToFloat64(data[fmt.Sprintf("%s_05", fields)]),
		"06": cast.ToFloat64(data[fmt.Sprintf("%s_06", fields)]),
		"07": cast.ToFloat64(data[fmt.Sprintf("%s_07", fields)]),
		"08": cast.ToFloat64(data[fmt.Sprintf("%s_08", fields)]),
		"09": cast.ToFloat64(data[fmt.Sprintf("%s_09", fields)]),
		"10": cast.ToFloat64(data[fmt.Sprintf("%s_10", fields)]),
		"11": cast.ToFloat64(data[fmt.Sprintf("%s_11", fields)]),
		"12": cast.ToFloat64(data[fmt.Sprintf("%s_12", fields)]),
		"13": cast.ToFloat64(data[fmt.Sprintf("%s_13", fields)]),
		"14": cast.ToFloat64(data[fmt.Sprintf("%s_14", fields)]),
		"15": cast.ToFloat64(data[fmt.Sprintf("%s_15", fields)]),
		"16": cast.ToFloat64(data[fmt.Sprintf("%s_16", fields)]),
		"17": cast.ToFloat64(data[fmt.Sprintf("%s_17", fields)]),
		"18": cast.ToFloat64(data[fmt.Sprintf("%s_18", fields)]),
		"19": cast.ToFloat64(data[fmt.Sprintf("%s_19", fields)]),
		"20": cast.ToFloat64(data[fmt.Sprintf("%s_20", fields)]),
		"21": cast.ToFloat64(data[fmt.Sprintf("%s_21", fields)]),
		"22": cast.ToFloat64(data[fmt.Sprintf("%s_22", fields)]),
		"23": cast.ToFloat64(data[fmt.Sprintf("%s_23", fields)]),
	}
}

func GetGeoMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	regionMaps, err := entity.GetIdsFieldMap(ctx, "geo_region", ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _, _, _, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

func GetCompanyMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	companyMaps, err := entity.GetIdsFieldMap(ctx, "COMPANY_INFO", ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	return companyMaps
}

func GetBranchMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	regionMaps, err := entity.GetIdsFieldMap(ctx, "branch_region", ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _, _, _, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

// 获取区域信息map
func GetRegionMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	schema := "store"
	switch query.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "geo_region"
		}
	case "BRANCH_REGION": // 管理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "branch_region"
		}
	case "FRANCHISEE_REGION": // 管理区域
		switch query.RegionGroupLevel {
		case 0, 1, 2:
			schema = "franchisee_region"
		}
	case "COMPANY":
		schema = "COMPANY_INFO"
	}
	return GetRegionMapsByIdsSchema(ctx, schema, query.Lan, ids)
}

// 获取区域信息map
func GetRegionMapsByIdsSchema(ctx context.Context, schema string, lan string, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	regionMaps, err := entity.GetIdsFieldMap(ctx, schema, ids, lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, r1, r2, r3, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
			storeInfo["region_name1"] = r1
			storeInfo["region_name2"] = r2
			storeInfo["region_name3"] = r3
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}

func GetProductMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	productIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		productIds = append(productIds, cast.ToInt64(r["product_id"]))
	}
	productMaps, _ := entity.GetIdsFieldMap(ctx, "sku", productIds, query.Lan)
	return productMaps
}

// 获取商品信息map
func GetProductMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productMaps := make(map[int64]map[string]interface{})
	for i := 0; i < len(ids); i += TRANSFER_LEN {
		end := i + TRANSFER_LEN
		if end > len(ids) {
			end = len(ids)
		}
		productMapTemp, _ := entity.GetIdsFieldMap(ctx, "sku", ids[i:end], query.Lan)
		productMaps = helpers.MergeMap(productMaps, productMapTemp)
	}
	//productMaps, err := entity.GetIdsFieldMap(ctx, "sku", ids, query.Lan)
	//if err != nil {
	//	logger.Pre().Errorf("调用主档商品服务失败 error: %v", err)
	//}
	return productMaps
}

// 获取商品信息map
func GetSchemaProductMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productMaps := make(map[int64]map[string]interface{})
	for i := 0; i < len(ids); i += TRANSFER_LEN {
		end := i + TRANSFER_LEN
		if end > len(ids) {
			end = len(ids)
		}
		productMapTemp, _ := entity.GetIdsFieldMap(ctx, "product", ids[i:end], query.Lan)
		productMaps = helpers.MergeMap(productMaps, productMapTemp)
	}
	//productMaps, err := entity.GetIdsFieldMap(ctx, "sku", ids, query.Lan)
	//if err != nil {
	//	logger.Pre().Errorf("调用主档商品服务失败 error: %v", err)
	//}
	return productMaps
}

// 获取折扣信息
func GetDiscountMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	discountMaps, _ := entity.GetIdsFieldMap(ctx, "promotion_rule", ids, query.Lan)
	return discountMaps
}

// 获取类别信息map
func GetCategoryMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productCategoryMaps := make(map[int64]map[string]interface{})
	for i := 0; i < len(ids); i += TRANSFER_LEN {
		end := i + TRANSFER_LEN
		if end > len(ids) {
			end = len(ids)
		}
		productCategoryMapTemp, _ := entity.GetIdsFieldMap(ctx, "product_category", ids, query.Lan)
		productCategoryMaps = helpers.MergeMap(productCategoryMaps, productCategoryMapTemp)
	}
	//productCategoryMaps, err := entity.GetIdsFieldMap(ctx, "product_category", ids, query.Lan)
	//if err != nil {
	//	logger.Pre().Errorf("调用主档商品分类服务失败 error: %v", err)
	//}
	return productCategoryMaps
}

// 获取门店信息map
func GetStoreMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	storeMaps, _ := entity.GetIdsFieldMap(ctx, "store", ids, query.Lan)
	return storeMaps
}

// 获取渠道信息map
func GetChannelMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	if len(ids) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	channelMaps, err := entity.GetIdsFieldMap(ctx, "channel_company", ids, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	return channelMaps
}

// 获取支付信息map
func GetPaymentMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	productCategoryMaps, _ := entity.GetIdsFieldMap(ctx, "payment_channel", ids, query.Lan)
	return productCategoryMaps
}

// 获取门店类型信息map
func GetStoreTypeMapsByIds(ctx context.Context, query *model.CommonRequest, ids []int64) map[int64]map[string]interface{} {
	storeTypeMaps, _ := entity.GetIdsFieldMap(ctx, "store_type", ids, query.Lan)
	return storeTypeMaps
}

func GetRefundReasonByCodes(ctx context.Context) (map[string]string, error) {
	partnerId := ctx.Value("partner_id")
	userId := ctx.Value("user_id")
	scopeId := ctx.Value("scope_id")
	md := metadata.New(map[string]string{
		"partner_id": cast.ToString(partnerId),
		"user_id":    cast.ToString(userId),
		"scope_id":   cast.ToString(scopeId),
	})
	ctx = metadata.NewOutgoingContext(ctx, md)
	conn, err := grpc.Dial(config.DefaultConfig.ConnStr.Oms, grpc.WithInsecure())
	if err != nil {
		logger.Pre().Errorf("grpc.Dial err: %v", err)
		return nil, err
	}
	defer conn.Close()
	client := oms.NewOmsApiServiceClient(conn)
	request := &oms.CancelChannelRequ{Channel: "all"}
	resp, err := client.RefundOrderReasonsAPI(ctx, request)
	if err != nil {
		logger.Pre().Errorf("client.Search err: %v", err)
		return nil, err
	}
	results := make(map[string]string)
	for _, v := range resp.Reasons {
		for key, value := range v.Rows {
			results[key] = value
		}
	}
	return results, nil
}

// 获取门店类型信息
func GetStoreTypeMaps(ctx context.Context, query *model.CommonRequest, resp *model.Response) map[int64]map[string]interface{} {
	storeTypes := make([]int64, 0, len(resp.Rows))
	if len(resp.Rows) <= 0 {
		return make(map[int64]map[string]interface{})
	}
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		storeTypes = append(storeTypes, cast.ToInt64(r["store_type"]))
	}
	storeTypeMaps, err := entity.GetIdsFieldMap(ctx, "store_type", storeTypes, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}

	return storeTypeMaps
}

func DataCheck(ctx context.Context, condition *model.RepoCondition) error {
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, condition.Lan)
	if err != nil {
		return err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	return nil
}

func GetOrderTypeName(orderType string) string {
	switch orderType {
	case "DINEIN":
		return "堂食"
	case "TAKEAWAY":
		return "外带"
	case "TAKEOUT":
		return "外卖"
	case "SELFHELP":
		return "自提"
	default:
		return "堂食"
	}
}

func GetOrderStatusName(orderStatus string) string {
	switch orderStatus {
	case "SALE":
		return "销售单"
	case "REFUND":
		return "退单"
	case "REFUNDCOMPLETED":
		return "退单原单"
	case "RESET":
		return "重结单"
	case "PARTIALREFUND":
		return "部分退款单"
	default:
		return ""
	}
}

var OrderStatus = map[string]map[string]string{
	"zh-CN": {
		"SALE":          "销售单",
		"PARTIALREFUND": "部分退款单",
		"REFUND":        "退单",
	},
	"en-US": {
		"SALE":          "Sales",
		"PARTIALREFUND": "Partial Refund",
		"REFUND":        "Refund",
	},
}

func RoundFloat64(value float64) float64 {
	return math.Round(value*100) / 100
}

func RoundFloat64_4(value float64) float64 {
	return math.Round(value*10000) / 10000
}

func GetStoreBusinessHours(storeInfo map[string]interface{}) ([]int64, error) {
	return entity.GetBusinessHour(storeInfo, time.Now().Format("2006-01-02"))
}
