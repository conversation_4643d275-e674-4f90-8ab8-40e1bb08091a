package report

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
)

// 口味分布
type Taste struct {
}

func (t *Taste) Transform(ctx context.Context, condition *model.RepoCondition, resp *report.ProductFlavorSalesResponse) {
	for r := range resp.Rows {
		if resp.Rows[r].Flavor == "" {
			resp.Rows[r].FlavorDistribute = "-"
		} else {
			resp.Rows[r].FlavorDistribute = resp.Rows[r].Flavor
		}
	}
}
