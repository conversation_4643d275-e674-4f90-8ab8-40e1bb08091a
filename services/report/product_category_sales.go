package report

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryCategorySales(ctx context.Context, query *model.CommonRequest) (*report.CategorySalesResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductCategorySales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}

	resp, err := repo.DefaultSalesRepository.ProductCategorySales(ctx, condition)
	PackagingForCategorySales(ctx, query, resp)
	return resp, err
}

func PackagingForCategorySales(ctx context.Context, query *model.CommonRequest, resp *report.CategorySalesResponse) {
	categoryIds := make([]int64, 0)
	for _, row := range resp.Rows {
		categoryIds = append(categoryIds, row.CategoryId)
	}
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)

	for i := range resp.Rows {
		PackagingForCategorySalesRow(resp.Rows[i], categoryMaps)
	}
	PackagingForCategorySalesRow(resp.Summary, categoryMaps)
}

func PackagingForCategorySalesRow(row *report.CategorySales, productMaps map[int64]map[string]interface{}) {
	row.CategoryName = cast.ToString(productMaps[row.CategoryId]["name"])
	row.ProductCount = RoundFloat64(row.ProductCount)
	row.NetAmount = RoundFloat64(row.NetAmount)
}
