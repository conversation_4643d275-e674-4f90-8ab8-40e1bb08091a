package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo_v2"
)

// 查询门店时段报表
func QueryStorePeriodSales(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryStorePeriodSales] receive body. Content: `%+v`", trackId, query)

	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] get user info failed.\nErr: `%+v`", trackId, err)
	//	return nil, hexerror.PermissionDenied("No user info.")
	//}
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}

	//if condition.IsPre {
	//	resp, err := repo.DefaultSalesRepository.StorePeriodSalesForPre(ctx, condition)
	//	packagingForStorePeriodSalesForPre(ctx, query, resp)
	//	return resp, err
	//}
	modelResp, err := repo.DefaultSalesRepositoryV2.StorePeriodSalesV2(ctx, condition)
	resp := packagingForStorePeriodSales(ctx, query, modelResp)
	return resp, err
}

func packagingForStorePeriodSales(ctx context.Context, query *model.CommonRequest, modelResp *report_v2.StorePeriodSalesResponseV2) *model.Response {
	regionIds := make([]int64, 0, len(modelResp.Rows))
	geoIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(modelResp.Rows))
	for _, row := range modelResp.Rows {
		regionIds = append(regionIds, row.RegionId)
		geoIds = append(geoIds, row.GeoId)
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	geoMaps := GetGeoMapsByIds(ctx, query, geoIds)
	branchMaps := GetBranchMapsByIds(ctx, query, branchIds)
	companyMaps := GetCompanyMapsByIds(ctx, query, companyIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	rows := make([]interface{}, 0, len(modelResp.Rows))
	// 组装code和name
	for r := range modelResp.Rows {
		regionId := modelResp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		modelResp.Rows[r].RegionCode = cast.ToString(code)
		modelResp.Rows[r].RegionName = cast.ToString(name)
		modelResp.Rows[r].RegionAddress = cast.ToString(city)
		modelResp.Rows[r].RegionAlias = cast.ToString(alias)
		geoId := modelResp.Rows[r].GeoId
		modelResp.Rows[r].GeoCode = cast.ToString(geoMaps[geoId]["code"])
		modelResp.Rows[r].GeoName = cast.ToString(geoMaps[geoId]["name"])
		branchId := modelResp.Rows[r].BranchId
		modelResp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		modelResp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])
		companyId := modelResp.Rows[r].CompanyId
		modelResp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		modelResp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		modelResp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(modelResp.Rows[r].StoreType)]["name"]) // 门店经营类型
		// 将查询结果转成map
		// 每小时数据列转行
		hours, err := GetStoreBusinessHours(regionMaps[regionId])
		if err != nil {
			logger.Pre().Errorf("get business hour failed: %v", err)
		}
		m := modelResp.Rows[r].HourDataColumn2Row(hours)
		modelResp.Rows[r].Data = nil
		modelResp.Rows[r].DetailData = m
		rows = append(rows, modelResp.Rows[r])
	}
	res := &model.Response{
		Rows:  rows,
		Total: modelResp.Total,
	}
	return res
}

func packagingForStorePeriodSalesForPre(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	helpers.DddEmptySales(query, resp)
	regionMaps := GetRegionMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]

		nRows = append(nRows, map[string]interface{}{
			"region_id":          cast.ToString(regionId),
			"region_code":        cast.ToString(regionCode),
			"region_name":        cast.ToString(regionName),
			"region_status":      cast.ToString(regionStatus),
			"net_amount_period":  ToPeriodMapsForPre("net_amount", r),
			"order_count_period": ToPeriodMapsForPre("order_count", r),
		})
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(resp.Summary["total"])
	resp.Summary = map[string]interface{}{
		"net_amount_period":  ToPeriodMapsForPre("net_amount", resp.Summary),
		"order_count_period": ToPeriodMapsForPre("order_count", resp.Summary),
	}
}

func calcTotal(data interface{}) []interface{} {
	values := cast.ToSlice(data)
	var sum float64
	for _, v := range values {
		if num, ok := v.(float64); ok {
			sum += num
		}
	}

	values = append(values, sum)
	return values
}
