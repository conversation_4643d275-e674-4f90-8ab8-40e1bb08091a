package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func StorePerformanceAnalysis(ctx context.Context, query *model.CommonRequest) (*report.StorePerformanceAnalysisResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryStorePerformanceAnalysis] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.StorePerformanceAnalysis(ctx, condition)
	packagingForStorePerformanceAnalysis(ctx, query, resp)
	return resp, err
}

func packagingForStorePerformanceAnalysis(ctx context.Context, query *model.CommonRequest, resp *report.StorePerformanceAnalysisResponse) {
	partnerID := cast.ToInt64(ctx.Value("partner_id"))

	regionIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.StoreId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	for i := range resp.Rows {
		regionId := resp.Rows[i].StoreId
		regionName := regionMaps[regionId]["name"]
		resp.Rows[i].StoreName = cast.ToString(regionName)

		resp.Rows[i].BusinessAmount = RoundFloat64(resp.Rows[i].BusinessAmount)
		resp.Rows[i].BowlCount = RoundFloat64(resp.Rows[i].BowlCount)
		resp.Rows[i].SeatTurnoverRate = RoundFloat64(resp.Rows[i].SeatTurnoverRate)
		resp.Rows[i].AverageDailySales = RoundFloat64(resp.Rows[i].AverageDailySales)
		resp.Rows[i].AverageSalesPerOrder = RoundFloat64(resp.Rows[i].AverageSalesPerOrder)
		resp.Rows[i].AverageBowlSpending = RoundFloat64(resp.Rows[i].AverageBowlSpending)
		resp.Rows[i].WeekdayAverageDailySales = RoundFloat64(resp.Rows[i].WeekdayAverageDailySales)
		resp.Rows[i].WeekendAverageDailySales = RoundFloat64(resp.Rows[i].WeekendAverageDailySales)

		for _, turnover := range resp.Rows[i].WeekdayChannelTurnovers {
			turnover.OrderTypeName = GetOrderTypeName(turnover.OrderType)
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}

		resp.Rows[i].WeekdayChannelTurnovers = fillEmptyOrderTypesStorePerformance(config.AllOrderTypeNames, resp.Rows[i].WeekdayChannelTurnovers)

		for _, turnover := range resp.Rows[i].WeekendChannelTurnovers {
			turnover.OrderTypeName = GetOrderTypeName(turnover.OrderType)
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}
		resp.Rows[i].WeekendChannelTurnovers = fillEmptyOrderTypesStorePerformance(config.AllOrderTypeNames, resp.Rows[i].WeekendChannelTurnovers)

		for _, turnover := range resp.Rows[i].WeekdayOrderTypeMealSegmentTurnovers {
			turnover.OrderTypeName = GetOrderTypeName(turnover.OrderType)
			turnover.Name = turnover.OrderTypeName + turnover.MealSegmentName
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}
		p, ok := config.PartnerConfs[partnerID]
		if ok {
			resp.Rows[i].WeekdayOrderTypeMealSegmentTurnovers = fillEmptyOrderTypesMealSegmentsStorePerformance(config.AllOrderTypeNames, p.MealSegments, resp.Rows[i].WeekdayOrderTypeMealSegmentTurnovers)
		}

		for _, turnover := range resp.Rows[i].WeekendOrderTypeMealSegmentTurnovers {
			turnover.OrderTypeName = GetOrderTypeName(turnover.OrderType)
			turnover.Name = turnover.OrderTypeName + turnover.MealSegmentName
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}

		if ok {
			resp.Rows[i].WeekendOrderTypeMealSegmentTurnovers = fillEmptyOrderTypesMealSegmentsStorePerformance(config.AllOrderTypeNames, p.MealSegments, resp.Rows[i].WeekendOrderTypeMealSegmentTurnovers)
		}

		for _, turnover := range resp.Rows[i].WeekdayMealSegmentTurnovers {
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}

		if ok {
			resp.Rows[i].WeekdayMealSegmentTurnovers = fillEmptyMealSegmentsStorePerformance(p.MealSegments, resp.Rows[i].WeekdayMealSegmentTurnovers)
		}

		for _, turnover := range resp.Rows[i].WeekendMealSegmentTurnovers {
			turnover.BowlTurnover = RoundFloat64(turnover.BowlTurnover)
		}

		if ok {
			resp.Rows[i].WeekendMealSegmentTurnovers = fillEmptyMealSegmentsStorePerformance(p.MealSegments, resp.Rows[i].WeekendMealSegmentTurnovers)
		}

	}
	summary := report.StorePerformanceAnalysis{
		WeekdayMealSegmentTurnovers:          make([]*report.StorePerformanceAnalysisMealSegment, 0),
		WeekendMealSegmentTurnovers:          make([]*report.StorePerformanceAnalysisMealSegment, 0),
		WeekdayChannelTurnovers:              make([]*report.StorePerformanceAnalysisOrderType, 0),
		WeekendChannelTurnovers:              make([]*report.StorePerformanceAnalysisOrderType, 0),
		WeekdayOrderTypeMealSegmentTurnovers: make([]*report.StorePerformanceAnalysisOrderTypeMealSegment, 0),
		WeekendOrderTypeMealSegmentTurnovers: make([]*report.StorePerformanceAnalysisOrderTypeMealSegment, 0),
	}
	p, ok := config.PartnerConfs[partnerID]
	if ok {
		summary.WeekdayMealSegmentTurnovers = fillEmptyMealSegmentsStorePerformance(p.MealSegments, summary.WeekdayMealSegmentTurnovers)
		summary.WeekendMealSegmentTurnovers = fillEmptyMealSegmentsStorePerformance(p.MealSegments, summary.WeekendMealSegmentTurnovers)
		summary.WeekdayOrderTypeMealSegmentTurnovers = fillEmptyOrderTypesMealSegmentsStorePerformance(config.AllOrderTypeNames, p.MealSegments, summary.WeekdayOrderTypeMealSegmentTurnovers)
		summary.WeekendOrderTypeMealSegmentTurnovers = fillEmptyOrderTypesMealSegmentsStorePerformance(config.AllOrderTypeNames, p.MealSegments, summary.WeekendOrderTypeMealSegmentTurnovers)
	}
	summary.WeekdayChannelTurnovers = fillEmptyOrderTypesStorePerformance(config.AllOrderTypeNames, summary.WeekdayChannelTurnovers)
	summary.WeekendChannelTurnovers = fillEmptyOrderTypesStorePerformance(config.AllOrderTypeNames, summary.WeekendChannelTurnovers)

	resp.Summary = &summary
}

func fillEmptyOrderTypesStorePerformance(orderTypes []string, ms []*report.StorePerformanceAnalysisOrderType) []*report.StorePerformanceAnalysisOrderType {
	res := make([]*report.StorePerformanceAnalysisOrderType, 0)
	orderTypeMap := make(map[string]*report.StorePerformanceAnalysisOrderType)
	for _, v := range ms {
		orderTypeMap[v.OrderTypeName] = v
	}
	for _, v := range orderTypes {
		if _, ok := orderTypeMap[v]; !ok {
			res = append(res, &report.StorePerformanceAnalysisOrderType{
				OrderType:     config.OrderTypeMap[v],
				OrderTypeName: v,
			})
		} else {
			res = append(res, orderTypeMap[v])
		}
	}
	return res
}

func fillEmptyMealSegmentsStorePerformance(mealSegments []string, ms []*report.StorePerformanceAnalysisMealSegment) []*report.StorePerformanceAnalysisMealSegment {
	res := make([]*report.StorePerformanceAnalysisMealSegment, 0)
	mealSegmentMap := make(map[string]*report.StorePerformanceAnalysisMealSegment)
	for _, v := range ms {
		mealSegmentMap[v.MealSegmentName] = v
	}
	for _, v := range mealSegments {
		if _, ok := mealSegmentMap[v]; !ok {
			res = append(res, &report.StorePerformanceAnalysisMealSegment{
				MealSegmentName: v,
			})
		} else {
			res = append(res, mealSegmentMap[v])
		}
	}
	return res
}

func fillEmptyOrderTypesMealSegmentsStorePerformance(orderTypes, mealSegments []string, ms []*report.StorePerformanceAnalysisOrderTypeMealSegment) []*report.StorePerformanceAnalysisOrderTypeMealSegment {
	res := make([]*report.StorePerformanceAnalysisOrderTypeMealSegment, 0)
	orderTypeMealSegmentMap := make(map[string]*report.StorePerformanceAnalysisOrderTypeMealSegment)
	for _, v := range ms {
		orderTypeMealSegmentMap[v.Name] = v
	}
	for _, orderType := range orderTypes {
		for _, mealSegment := range mealSegments {
			name := orderType + mealSegment
			if _, ok := orderTypeMealSegmentMap[name]; !ok {
				res = append(res, &report.StorePerformanceAnalysisOrderTypeMealSegment{
					OrderType:       config.OrderTypeMap[orderType],
					OrderTypeName:   orderType,
					MealSegmentName: mealSegment,
					Name:            name,
				})
			} else {
				res = append(res, orderTypeMealSegmentMap[name])
			}
		}
	}
	return res
}
