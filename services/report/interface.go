package report

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
)

// 策略模式
type FlavorSales interface {
	Transform(ctx context.Context, condition *model.RepoCondition, resp *report.ProductFlavorSalesResponse)
}

var flavorSales FlavorSales

type Transfer struct {
	ctx       context.Context
	tagTypes  string
	resp      *report.ProductFlavorSalesResponse
	query     *model.CommonRequest
	condition *model.RepoCondition
}

const (
	TASTE     string = "TASTE"
	FEED      string = "FEED"
	ATTRIBUTE string = "ATTRIBUTE"
	GRAPH     string = "GRAPH"
)

func (t *Transfer) SetTransfer() {
	switch t.tagTypes {
	case TASTE:
		flavorSales = new(Taste)
	case FEED:
		flavorSales = &Feed{query: t.query}
	case ATTRIBUTE:
		flavorSales = new(Attribute)
	case GRAPH:
		flavorSales = new(Graph)
	}
}

func (t *Transfer) Response() {
	flavorSales.Transform(t.ctx, t.condition, t.resp)
}
