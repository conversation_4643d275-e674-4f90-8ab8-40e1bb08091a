package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryProductSalesSummary(ctx context.Context, query *model.CommonRequest) (*report.ProductSalesSummaryResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesSummary] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.ProductSalesSummary(ctx, condition)
	PackagingForProductSalesSummary(ctx, query, resp)
	return resp, err
}

func PackagingForProductSalesSummary(ctx context.Context, query *model.CommonRequest, resp *report.ProductSalesSummaryResponse) {
	productIds := make([]int64, 0)
	categoryIds := make([]int64, 0)
	for _, row := range resp.Rows {
		productIds = append(productIds, row.ProductId)
		categoryIds = append(categoryIds, row.CategoryId)
	}
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)

	for i := range resp.Rows {
		PackagingForProductSalesSummaryRow(resp.Rows[i], productMaps, categoryMaps)
	}
	PackagingForProductSalesSummaryRow(resp.Summary, productMaps, categoryMaps)
}

func PackagingForProductSalesSummaryRow(row *report.ProductSalesSummary, productMaps map[int64]map[string]interface{}, categoryMaps map[int64]map[string]interface{}) {
	row.ProductCode = cast.ToString(productMaps[row.ProductId]["code"])
	row.ProductName = cast.ToString(productMaps[row.ProductId]["name"])
	row.CategoryCode = cast.ToString(categoryMaps[row.CategoryId]["code"])
	row.CategoryName = cast.ToString(categoryMaps[row.CategoryId]["name"])
	row.ProductCount = RoundFloat64(row.ProductCount)
	row.NetAmount = RoundFloat64(row.NetAmount)
}
