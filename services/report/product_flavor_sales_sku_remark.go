package report

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"sort"
)

// sku属性
type Attribute struct {
}

// 准备工作：1、属性值结构体AttributeValue；2、属性名结构体AttributeName；3、属性值数组实现sort方法； 4、Attributes结构体包括数据库查询结果row和属性信息数组
type AttributeValue struct { // 属性值
	AttributeValueCode  string
	AttributeValueName  string
	ProductCount        int64
	PercentProductCount float64
}

type AttributeName struct { // 属性名
	AttributeNameCode   string
	AttributeNameName   string
	ProductCount        int64
	PercentProductCount float64
	AttributeNameValues AttributeValues // 该属性名下的属性值
}

type AttributeValues []*AttributeValue

func (a AttributeValues) Len() int      { return len(a) }
func (a AttributeValues) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a AttributeValues) Less(i, j int) bool {
	return a[i].PercentProductCount > a[j].PercentProductCount
}

type Attributes struct {
	ProductFlavor *report.ProductFlavorSales
	AttributeInfo []*AttributeName
}

func (a *Attribute) Transform(ctx context.Context, condition *model.RepoCondition, resp *report.ProductFlavorSalesResponse) {
	attrMap := getAttrMap(resp)
	Rows := make([]*report.ProductFlavorSales, 0)
	for _, row := range resp.Rows {
		key := row.BusDate + cast.ToString(row.RegionId) + cast.ToString(row.ProductId)
		tmpFlavors := make([]report.ProductFlavorSales, 0) // 这里不能传指针
		if value, ok := attrMap[key]; !ok {
			continue
		} else {
			if len(value.AttributeInfo) == 0 {
				Rows = append(Rows, row)
			} else {
				for _, attrName := range value.AttributeInfo {
					flavor := value.ProductFlavor
					//flavor.AttributeNameCode = attrName.AttributeNameCode
					flavor.AttributeNameName = attrName.AttributeNameName
					for _, attrValue := range attrName.AttributeNameValues {
						//flavor.AttributeValueCode = attrValue.AttributeValueCode
						flavor.AttributeValueName = attrValue.AttributeValueName
						flavor.ProductCount = attrValue.ProductCount
						flavor.PercentProductCount = attrValue.PercentProductCount
						tmpFlavors = append(tmpFlavors, *flavor)
					}
				}
				for i := range tmpFlavors { // 加到结果集中
					Rows = append(Rows, &tmpFlavors[i])
				}
				delete(attrMap, key)
			}
		}
	}
	resp.Total = cast.ToInt64(len(Rows))
	if condition.Limit == -1 {
		resp.Rows = Rows
	} else {
		if cast.ToInt64(condition.Offset) >= cast.ToInt64(len(Rows)) {
			resp.Rows = Rows
		} else if cast.ToInt64(condition.Offset)+condition.Limit > cast.ToInt64(len(Rows)) {
			resp.Rows = Rows[condition.Offset:]
		} else {
			resp.Rows = Rows[condition.Offset : cast.ToInt64(condition.Offset)+condition.Limit]
		}
		fmt.Println(len(resp.Rows))
	}
}

func getAttrMap(resp *report.ProductFlavorSalesResponse) map[string]*Attributes {
	var attrMap = make(map[string]*Attributes)
	for _, row := range resp.Rows {
		skuRemarkMap, _ := getSkuRemarkMap(row.SkuRemark)
		attributes := make([]*AttributeName, 0)
		if skuRemarkMap == nil {
			attributes = append(attributes, &AttributeName{
				ProductCount:        row.ProductCount,
				AttributeNameValues: make([]*AttributeValue, 0),
			})
		}
		for key, value := range skuRemarkMap {
			if key == "" || value == "" {
				continue
			}
			attrValues := make(AttributeValues, 0)
			attrValues = append(attrValues, &AttributeValue{
				AttributeValueName: value,
				ProductCount:       row.ProductCount,
			})
			attributes = append(attributes, &AttributeName{
				AttributeNameName:   key,
				ProductCount:        row.ProductCount,
				AttributeNameValues: attrValues,
			})
		}
		// 以 营业日期+门店id+商品id 为key
		key := row.BusDate + cast.ToString(row.RegionId) + cast.ToString(row.ProductId)
		if _, ok := attrMap[key]; !ok { // 还没有记录，直接存新的记录
			attrMap[key] = &Attributes{
				ProductFlavor: row,
				AttributeInfo: attributes,
			}
		} else { // 已有记录，需要更新该记录
			newAttributes := updateAttributes(attributes, attrMap[key].AttributeInfo)
			attrMap[key] = &Attributes{
				ProductFlavor: row,
				AttributeInfo: newAttributes,
			}
		}
	}

	// 计算占比
	for _, value := range attrMap {
		// 遍历属性名
		for _, attrName := range value.AttributeInfo {
			//attrName.AttributeNameName = attrNameMap[attrName.AttributeNameCode] // 属性名的名称
			var total int64
			// 遍历该属性名下的属性值，并计算销量合计
			for _, attrValue := range attrName.AttributeNameValues {
				//attrValue.AttributeValueName = attrValueMap[attrName.AttributeNameCode][attrValue.AttributeValueCode] // 属性值的名称
				if attrValue.ProductCount >= 0 {
					total += attrValue.ProductCount
				} else {
					attrValue.PercentProductCount = 0
				}
			}
			for i := range attrName.AttributeNameValues {
				if total != 0 { // 计算占比
					attrName.AttributeNameValues[i].PercentProductCount = cast.ToFloat64(attrName.AttributeNameValues[i].ProductCount) / cast.ToFloat64(total)
				}
			}
			sort.Sort(attrName.AttributeNameValues)
		}
	}
	return attrMap
}

func updateAttributes(dest []*AttributeName, src []*AttributeName) []*AttributeName {
	// 1、特判
	if src == nil || len(src) == 0 {
		return dest
	}
	if dest == nil || len(dest) == 0 {
		return src
	}
	// 2、准备工作：用两个map分别存目标和源数组属性信息，key是属性名name，value是属性信息结构体
	mdest, msrc := make(map[string]*AttributeName), make(map[string]*AttributeName)
	for _, attribute := range dest {
		mdest[attribute.AttributeNameName] = attribute
	}
	for _, attribute := range src { // 该map最终会存放最新的属性信息
		msrc[attribute.AttributeNameName] = attribute
	}
	// 3、获取目标数组和源数组的属性names
	destNames, srcNames := getAttributeCodes(dest, src)
	// 4、获取两者的交集和新数组
	unite, diff := getAttributeSameAndDiff(destNames, srcNames)
	// 5、对于新数组，直接添加到源数组中
	for _, name := range diff {
		src = append(src, mdest[name])
	}
	// 6、对于交集，和旧数据相加，再加到源数组中
	for _, name := range unite {
		oldAttribute := msrc[name]                            // 旧属性数组
		newAttribute := mdest[name]                           // 新属性数组
		attributeValueMap := make(map[string]*AttributeValue) // 属性值map
		for _, value := range oldAttribute.AttributeNameValues {
			attributeValueMap[value.AttributeValueName] = value
		}
		for _, value := range newAttribute.AttributeNameValues {
			if attributeValue, ok := attributeValueMap[value.AttributeValueName]; !ok { // 如果还没有该属性值，直接存进去
				attributeValueMap[value.AttributeValueName] = value
			} else { // 否则，和旧数据相加
				attributeValue.ProductCount += value.ProductCount
				//attributeValueMap[value.AttributeValueCode] = attributeValue
			}
		}
		// 此时属性值map里的数据是最新的
		attributeValuesNew := make(AttributeValues, 0)
		for _, value := range attributeValueMap {
			attributeValuesNew = append(attributeValuesNew, value)
		}
		oldAttribute.AttributeNameValues = attributeValuesNew // 更新该属性的属性值数组
	}
	return src
}

func getAttributeSameAndDiff(dest []string, src []string) (unite []string, diff []string) {
	unite, diff = make([]string, 0), make([]string, 0)
	if dest == nil || len(dest) == 0 {
		return
	}
	if src == nil || len(src) == 0 {
		diff = dest
		return
	}
	mdest, mall := make(map[string]byte), make(map[string]byte)
	// 源数组建立map
	for _, old := range src {
		mall[old] = 1
	}
	// 目标数组，存不进去，说明有相同元素，就存进交集
	for _, v := range dest {
		mdest[v] = 1 // 目标数组存进map
		l := len(mall)
		mall[v] = 1
		if l != len(mall) { // 长度变化，可以存
			l = len(mall)
		} else { // 长度不变，说明是相同元素，存交集
			unite = append(unite, v)
		}
	}
	// 遍历交集
	for _, v := range unite { // 在目标数组map中删除交集，剩下的就是
		delete(mdest, v)
	}
	for k := range mdest {
		diff = append(diff, k)
	}
	return
}

func getAttributeCodes(dest []*AttributeName, src []*AttributeName) ([]string, []string) {
	one, two := make([]string, 0, len(dest)), make([]string, 0, len(src))
	for _, v := range dest {
		one = append(one, v.AttributeNameName)
	}
	for _, v := range src {
		two = append(two, v.AttributeNameName)
	}
	return one, two
}

func getSkuRemarkMap(skuRemark string) (map[string]string, error) {
	if skuRemark == "" {
		return nil, nil
	}
	res := make(map[string]string)
	err := json.Unmarshal([]byte(skuRemark), &res)
	if err != nil {
		logger.Pre().Errorf("sku_remark解析成map出错：%v", err)
		return nil, err
	}
	return res, nil
}
