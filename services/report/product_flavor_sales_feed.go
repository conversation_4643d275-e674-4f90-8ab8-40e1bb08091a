package report

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"sort"
)

// 加料
type Feed struct {
	query *model.CommonRequest
}

// 准备工作：1、加料信息结构体Accessory；2、实现sort方法；3、Feeds结构体包括数据库查询结果row和加料信息数组
type Accessory struct { // 加料信息
	AccessoryId         int64
	AccessoryCode       string
	AccessoryName       string
	ProductCount        int64
	PercentProductCount float64
}

type Accessories []*Accessory

func (a Accessories) Len() int           { return len(a) }
func (a Accessories) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a Accessories) Less(i, j int) bool { return a[i].PercentProductCount > a[j].PercentProductCount }

type Feeds struct {
	ProductFlavor   *report.ProductFlavorSales
	AccessoriesInfo Accessories
}

func (f *Feed) Transform(ctx context.Context, condition *model.RepoCondition, resp *report.ProductFlavorSalesResponse) {
	accessoryMap := getFeedMap(resp)
	Rows := make([]*report.ProductFlavorSales, 0) // 结果集
	for _, row := range resp.Rows {
		key := row.BusDate + cast.ToString(row.RegionId) + cast.ToString(row.ProductId)
		tmpFlavors := make([]report.ProductFlavorSales, 0) // 这里不能传指针
		if feed, ok := accessoryMap[key]; !ok {
			continue
		} else {
			if len(feed.AccessoriesInfo) == 0 {
				Rows = append(Rows, row)
			} else {
				for _, accessory := range feed.AccessoriesInfo {
					flavor := feed.ProductFlavor
					flavor.AccessoryName = accessory.AccessoryName
					flavor.ProductCount = accessory.ProductCount
					flavor.PercentProductCount = accessory.PercentProductCount
					tmpFlavors = append(tmpFlavors, *flavor)
				}
				for i := range tmpFlavors { // 加到结果集中
					Rows = append(Rows, &tmpFlavors[i])
				}
				delete(accessoryMap, key)
			}
		}
	}
	resp.Total = cast.ToInt64(len(Rows))
	if condition.Limit == -1 {
		resp.Rows = Rows
	} else {
		if cast.ToInt64(condition.Offset) >= cast.ToInt64(len(Rows)) {
			resp.Rows = Rows
		} else if cast.ToInt64(condition.Offset)+condition.Limit > cast.ToInt64(len(Rows)) {
			resp.Rows = Rows[condition.Offset:]
		} else {
			resp.Rows = Rows[condition.Offset : cast.ToInt64(condition.Offset)+condition.Limit]
		}
		fmt.Println(len(resp.Rows))
	}
}

func getFeedMap(resp *report.ProductFlavorSalesResponse) map[string]*Feeds {
	var accessoryMap = make(map[string]*Feeds)
	for _, row := range resp.Rows {
		accessories := make([]*Accessory, 0)                                      // 加料数组
		accessoryNames, feedMap := separateAccessoryNamesFromStr(row.Accessories) // 加料names，有序数组
		if len(accessoryNames) == 0 || feedMap == nil {
			accessories = append(accessories, &Accessory{
				AccessoryId:         0,
				AccessoryCode:       "",
				AccessoryName:       "无加料",
				ProductCount:        row.ProductCount,
				PercentProductCount: 0,
			})
		}
		// 遍历加料name数组
		for _, name := range accessoryNames {
			accessories = append(accessories, &Accessory{
				AccessoryName: name,
				ProductCount:  cast.ToInt64(feedMap[name]),
			})
		}
		// 以 营业日期+门店id+商品id 为key
		key := row.BusDate + cast.ToString(row.RegionId) + cast.ToString(row.ProductId)
		if _, ok := accessoryMap[key]; !ok { // 还没有记录，直接存新的记录
			accessoryMap[key] = &Feeds{
				ProductFlavor:   row,
				AccessoriesInfo: accessories,
			}
		} else { // 已有记录，需要更新该记录
			newAccessories := updateAccessories(accessories, accessoryMap[key].AccessoriesInfo) // 更新加料数组
			accessoryMap[key] = &Feeds{
				ProductFlavor:   row,
				AccessoriesInfo: newAccessories,
			}
		}
	}

	// 计算占比
	for _, value := range accessoryMap {
		var total int64 // 加料合计
		for _, accessory := range value.AccessoriesInfo {
			if accessory.ProductCount >= 0 {
				total += accessory.ProductCount
			} else {
				accessory.PercentProductCount = 0
			}
		}
		for i := range value.AccessoriesInfo {
			if total != 0 {
				value.AccessoriesInfo[i].PercentProductCount = cast.ToFloat64(value.AccessoriesInfo[i].ProductCount) / cast.ToFloat64(total)
			}
		}
		sort.Sort(value.AccessoriesInfo) // 按占比从大到小排序
	}
	return accessoryMap
}

func updateIds(src []int64, dest []int64) []int64 {
	if len(src) == 0 {
		return dest
	}
	if len(dest) == 0 {
		return src
	}
	msrc := make(map[int64]byte)
	for _, id := range src {
		msrc[id] = 1
	}
	for _, id := range dest {
		if _, ok := msrc[id]; !ok {
			msrc[id] = 1
			src = append(src, id)
		}
	}
	return src
}

// 更新加料数组：1、新数组加到旧数组中；2、交集部分要和旧数据相加
func updateAccessories(dest []*Accessory, src []*Accessory) []*Accessory {
	// 1、特判
	if dest == nil || len(dest) == 0 {
		return src
	}
	if src == nil || len(src) == 0 {
		return dest
	}
	// 2、准备工作：用两个map分别存目标和源数组加料信息，key是加料name，value是加料信息结构体
	mdest, msrc := make(map[string]*Accessory), make(map[string]*Accessory)
	for _, accessory := range dest {
		mdest[accessory.AccessoryName] = accessory
	}
	for _, accessory := range src {
		msrc[accessory.AccessoryName] = accessory
	}
	// 3、获取目标数组和源数组的加料ids
	destNames, srcNames := getAccessoryNames(dest, src)
	// 4、获取两者的交集和新数组
	unite, diff := getFeedSameAndDiff(destNames, srcNames)
	// 5、对于新数组，直接添加到源数组中
	for _, name := range diff {
		src = append(src, mdest[name])
	}
	// 6、对于交集，和旧数据相加，再加到源数组中
	for _, name := range unite {
		oldAccessory := msrc[name]  // 旧加料数据
		newAccessory := mdest[name] // 新加料数据
		oldAccessory.ProductCount += newAccessory.ProductCount
		for i := range src {
			if src[i].AccessoryName == name {
				src[i] = oldAccessory // 更新源数组
				break
			}
		}
	}
	return src
}

func getAccessoryNames(dest []*Accessory, src []*Accessory) ([]string, []string) { // 取出目标数组的加料name，和源数组的加料name
	one, two := make([]string, 0, len(dest)), make([]string, 0, len(src))
	for _, v := range dest {
		one = append(one, v.AccessoryName)
	}
	for _, v := range src {
		two = append(two, v.AccessoryName)
	}
	return one, two
}

type AccessoryNames []string

func (a AccessoryNames) Len() int           { return len(a) }
func (a AccessoryNames) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a AccessoryNames) Less(i, j int) bool { return a[i] < a[j] }

func getAccessoryMap(accessory string) (map[string]int, error) {
	if accessory == "" {
		return nil, errors.New("empty string")
	}
	res := make(map[string]int)
	if err := json.Unmarshal([]byte(accessory), &res); err != nil {
		logger.Pre().Errorf("accessories解析成map出错：%v", err)
		return nil, err
	}
	return res, nil
}

func separateAccessoryNamesFromStr(access string) ([]string, map[string]int) { // 返回一个有序数组
	var accessories AccessoryNames
	if access == "" {
		return accessories, nil
	}
	if accessoryMap, err := getAccessoryMap(access); err != nil || accessoryMap == nil {
		return accessories, nil
	} else {
		for k, _ := range accessoryMap {
			accessories = append(accessories, k)
		}
		sort.Sort(accessories) // 有序数组
		return accessories, accessoryMap
	}
}

// 分别取出两个数组交集、目标数组不在源数组的部分
func getFeedSameAndDiff(dest []string, src []string) (unite []string, diff []string) {
	unite, diff = make([]string, 0), make([]string, 0)
	if dest == nil || len(dest) == 0 {
		return
	}
	if src == nil || len(src) == 0 {
		diff = dest
		return
	}
	mdest, mall := make(map[string]byte), make(map[string]byte)
	// 源数组建立map
	for _, old := range src {
		mall[old] = 1
	}
	// 目标数组，存不进去，说明有相同元素，就存进交集
	for _, v := range dest {
		mdest[v] = 1 // 目标数组存进map
		l := len(mall)
		mall[v] = 1
		if l != len(mall) { // 长度变化，可以存
			l = len(mall)
		} else { // 长度不变，说明是相同元素，存交集
			unite = append(unite, v)
		}
	}
	// 遍历交集
	for _, v := range unite { // 在目标数组map中删除交集，剩下的就是
		delete(mdest, v)
	}
	for k := range mdest {
		diff = append(diff, k)
	}
	return
}
