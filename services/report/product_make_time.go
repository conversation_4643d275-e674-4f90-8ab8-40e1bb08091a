package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductMakeTime(ctx context.Context, query *model.CommonRequest) (*report.ProductMakeTimeResponse, error) {
	condition := query.ToRepoCondition([]string{})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultMakeTimeQueryRepository.ProductMakeTime(ctx, condition)
	productMakeTimeProcessResponse(ctx, query, resp)
	return resp, err
}

func productMakeTimeProcessResponse(ctx context.Context, query *model.CommonRequest, resp *report.ProductMakeTimeResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, row.StoreType)
		regionIds = append(regionIds, row.RegionId)
		categoryIds = append(categoryIds, row.ProductCategoryId)
		productIds = append(productIds, row.ProductId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)

	// 组装code和name
	for i := range resp.Rows {
		resp.Rows[i].TimePeriod = fmt.Sprintf("%v:00 - %v:00", resp.Rows[i].MakeHour, resp.Rows[i].MakeHour+1)

		resp.Rows[i].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[i].StoreType)]["name"]) // 门店经营类型
		regionId := resp.Rows[i].RegionId
		resp.Rows[i].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[i].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[i].RegionAddress = cast.ToString(regionMaps[regionId]["region_name"])
		resp.Rows[i].RegionAlias = cast.ToString(regionMaps[regionId]["alias"])

		cid := resp.Rows[i].ProductCategoryId
		resp.Rows[i].ProductCategoryName = cast.ToString(categoryMaps[cid]["name"])
		resp.Rows[i].ProductCategoryCode = cast.ToString(categoryMaps[cid]["code"])

		productId := resp.Rows[i].ProductId
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[i].ProductCode = cast.ToString(productMaps[productId]["code"])
		resp.Rows[i].ProductName = pName
	}

}
