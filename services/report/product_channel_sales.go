package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductChannelSales(ctx context.Context, query *model.CommonRequest) (*report.ProductChannelResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductChannelSales] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	if err := DataCheck(ctx, condition); err != nil {
		logger.Pre().Errorf("单品渠道报表数据检验失败: %v", err)
		return nil, err
	}
	resp, err := repo.DefaultSalesRepository.ProductChannelSales(ctx, condition)
	packagingForProductChannelSales(ctx, query, resp)
	return resp, err
}

func packagingForProductChannelSales(ctx context.Context, query *model.CommonRequest, resp *report.ProductChannelResponse) {
	//helpers.DddEmptySales(query, resp)
	if resp.Summary.ItemCount != 0 {
		resp.Summary.ProductAveragePrice = resp.Summary.GrossAmount / cast.ToFloat64(resp.Summary.ItemCount)
	}
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		regionIds = append(regionIds, row.RegionId)
		for _, r := range row.Child {
			categoryIds = append(categoryIds, r.ProductCategoryId)
			productIds = append(productIds, r.ProductId)
			for _, rr := range r.Child {
				channelIds = append(channelIds, rr.ChannelId)
			}
		}
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	// 组装code和name
	for i := range resp.Rows {
		resp.Rows[i].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[i].StoreType)]["name"]) // 门店经营类型
		regionId := resp.Rows[i].RegionId
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionAddress := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		resp.Rows[i].RegionCode = cast.ToString(regionCode)
		resp.Rows[i].RegionName = cast.ToString(regionName)
		resp.Rows[i].RegionAddress = cast.ToString(regionAddress)
		resp.Rows[i].RegionAlias = cast.ToString(alias)
		if resp.Rows[i].ItemCount != 0 {
			resp.Rows[i].ProductAveragePrice = resp.Rows[i].GrossAmount / cast.ToFloat64(resp.Rows[i].ItemCount)
		}
		for j := range resp.Rows[i].Child {
			productId := resp.Rows[i].Child[j].ProductId
			productCode := productMaps[productId]["code"]
			categoryId := resp.Rows[i].Child[j].ProductCategoryId
			categoryCode := categoryMaps[categoryId]["code"]
			categoryName := categoryMaps[categoryId]["name"]
			resp.Rows[i].Child[j].ProductCode = cast.ToString(productCode)
			name := cast.ToString(productMaps[productId]["name"])
			saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
			pName := ""
			if name == saleName {
				pName = saleName
			} else {
				if strings.Contains(saleName, name) {
					pName = saleName
				} else {
					pName = name + saleName
				}
			}
			resp.Rows[i].Child[j].ProductName = pName
			resp.Rows[i].Child[j].ProductCategoryCode = cast.ToString(categoryCode)
			resp.Rows[i].Child[j].ProductCategoryName = cast.ToString(categoryName)
			if resp.Rows[i].Child[j].ItemCount != 0 {
				resp.Rows[i].Child[j].ProductAveragePrice = resp.Rows[i].Child[j].GrossAmount / cast.ToFloat64(resp.Rows[i].Child[j].ItemCount)
			}
			for k := range resp.Rows[i].Child[j].Child {
				channelId := resp.Rows[i].Child[j].Child[k].ChannelId
				channelCode := channelMaps[channelId]["code"]
				channelName := channelMaps[channelId]["name"]
				resp.Rows[i].Child[j].Child[k].ChannelCode = cast.ToString(channelCode)
				resp.Rows[i].Child[j].Child[k].ChannelName = cast.ToString(channelName)
				if resp.Rows[i].Child[j].Child[k].ItemCount != 0 {
					resp.Rows[i].Child[j].Child[k].ProductAveragePrice = resp.Rows[i].Child[j].Child[k].GrossAmount / cast.ToFloat64(resp.Rows[i].Child[j].Child[k].ItemCount)
				}
				for m := range resp.Rows[i].Child[j].Child[k].Child {
					if resp.Rows[i].Child[j].Child[k].Child[m].ItemCount != 0 {
						resp.Rows[i].Child[j].Child[k].Child[m].ProductAveragePrice = resp.Rows[i].Child[j].Child[k].Child[m].GrossAmount / cast.ToFloat64(resp.Rows[i].Child[j].Child[k].Child[m].ItemCount)
					}
				}
			}
		}
	}
}
