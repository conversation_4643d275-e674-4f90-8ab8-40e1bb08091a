package report

import (
	"fmt"
	"strings"
	"testing"
)

func TestPackagingForProductFlavorSales(t *testing.T) {
	skus := make([]string, 0)
	skus = []string{"002", "009"}

	accessoes := make([]string, 0)
	//accessoes = []string{"90120", "90123"}

	var flavor, access string
	for _, remark := range skus {
		flavor = flavor + ";" + remark
	}
	fmt.Println("flavor:", flavor)
	flavor = strings.Trim(flavor, ";")
	fmt.Println("flavor:", flavor)

	for _, a := range accessoes {
		access = access + ";" + a
	}
	fmt.Println("access:", access)
	access = strings.Trim(access, ";")
	fmt.Println("access:", access)

	flavor = flavor + "&" + access
	fmt.Println("flavor:", flavor)
	if flavor != "" {
		flavor = strings.Trim(flavor, ";")
		//flavor = strings.Trim(flavor, "&")
	}
	fmt.Println("flavor:", flavor)

	//flavor = "203904;椰果;温&819829;9983894"

	var remarks, accessories string
	arr := strings.Split(flavor, "&")
	fmt.Println("arr:", arr)
	remarks = arr[0]
	if len(arr) == 2 {
		accessories = arr[1]
	}
	fmt.Printf("sku: %s, accessory: %s\n", remarks, accessories)
	remarkCodes, accessCodes := make([]string, 0), make([]string, 0)
	for _, code := range strings.Split(remarks, ";") {
		if code != "" {
			remarkCodes = append(remarkCodes, code)
		}
	}
	for _, code := range strings.Split(accessories, ";") {
		if code != "" {
			accessCodes = append(accessCodes, code)
		}
	}

	fmt.Printf("skuRemark_code: %v, accessories_code: %v\n", remarkCodes, accessCodes)
	fmt.Println("len of sku:", len(remarkCodes))
	fmt.Println("len of access:", len(accessCodes))
}
