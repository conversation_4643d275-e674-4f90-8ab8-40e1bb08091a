package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductTaxSales(ctx context.Context, query *model.CommonRequest) (*report.ProductTaxSalesResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	// 增加数据校验
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.ProductTaxSales(ctx, condition)
	PackagingForProductTaxSales(ctx, query, resp)
	return resp, err
}

func PackagingForProductTaxSales(ctx context.Context, query *model.CommonRequest, resp *report.ProductTaxSalesResponse) {
	storeIds := make([]int64, 0, len(resp.Rows))
	productIds := make([]int64, 0)
	categoryIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		storeIds = append(storeIds, row.StoreId)
		productIds = append(productIds, row.ProductId)
		companyIds = append(companyIds, row.CompanyId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		categoryIds = append(categoryIds, row.CategoryId)
		channelIds = append(channelIds, row.ChannelId)
	}
	for _, row := range resp.PackageFees {
		storeIds = append(storeIds, row.StoreId)
		companyIds = append(companyIds, row.CompanyId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		channelIds = append(channelIds, row.ChannelId)
	}

	for _, row := range resp.SendFees {
		storeIds = append(storeIds, row.StoreId)
		companyIds = append(companyIds, row.CompanyId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		channelIds = append(channelIds, row.ChannelId)
	}
	storeMaps := GetRegionMapsByIds(ctx, query, storeIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	companyMaps := GetCompanyMapsByIds(ctx, query, companyIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	// 组装code和name
	for r := range resp.Rows {
		storeId := resp.Rows[r].StoreId
		resp.Rows[r].StoreCode = cast.ToString(storeMaps[storeId]["code"])
		resp.Rows[r].StoreName = cast.ToString(storeMaps[storeId]["name"])
		resp.Rows[r].StoreAddress = cast.ToString(storeMaps[storeId]["region_name"])
		resp.Rows[r].StoreAlias = cast.ToString(storeMaps[storeId]["alias"])
		productId := resp.Rows[r].ProductId
		resp.Rows[r].ProductCode = cast.ToString(productMaps[productId]["code"])
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[r].ProductName = pName
		category := resp.Rows[r].CategoryId
		resp.Rows[r].CategoryCode = cast.ToString(categoryMaps[category]["code"])
		resp.Rows[r].CategoryName = cast.ToString(categoryMaps[category]["name"])
		companyId := resp.Rows[r].CompanyId
		resp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		channelId := resp.Rows[r].ChannelId
		resp.Rows[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.Rows[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])
	}
	// 组装code和name
	tmp := make([]*report.ProductTaxSales, 0, len(resp.SendFees))
	for r := range resp.SendFees {
		if resp.SendFees[r].InvoiceAmount == 0 {
			continue
		}
		storeId := resp.SendFees[r].StoreId
		resp.SendFees[r].StoreCode = cast.ToString(storeMaps[storeId]["code"])
		resp.SendFees[r].StoreName = cast.ToString(storeMaps[storeId]["name"])
		resp.SendFees[r].StoreAddress = cast.ToString(storeMaps[storeId]["region_name"])
		resp.SendFees[r].StoreAlias = cast.ToString(storeMaps[storeId]["alias"])

		companyId := resp.SendFees[r].CompanyId
		resp.SendFees[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.SendFees[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		channelId := resp.SendFees[r].ChannelId
		resp.SendFees[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.SendFees[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		resp.SendFees[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.SendFees[r].StoreType)]["name"])
		tmp = append(tmp, resp.SendFees[r])
	}
	resp.SendFees = tmp
	tmpp := make([]*report.ProductTaxSales, 0, len(resp.SendFees))
	// 组装code和name
	for r := range resp.PackageFees {
		if resp.PackageFees[r].InvoiceAmount == 0 {
			continue
		}
		storeId := resp.PackageFees[r].StoreId
		resp.PackageFees[r].StoreCode = cast.ToString(storeMaps[storeId]["code"])
		resp.PackageFees[r].StoreName = cast.ToString(storeMaps[storeId]["name"])
		resp.PackageFees[r].StoreAddress = cast.ToString(storeMaps[storeId]["region_name"])
		resp.PackageFees[r].StoreAlias = cast.ToString(storeMaps[storeId]["alias"])

		companyId := resp.PackageFees[r].CompanyId
		resp.PackageFees[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.PackageFees[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		channelId := resp.PackageFees[r].ChannelId
		resp.PackageFees[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.PackageFees[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		resp.PackageFees[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.PackageFees[r].StoreType)]["name"])
		tmpp = append(tmpp, resp.PackageFees[r])
	}
	resp.PackageFees = tmpp
}
