package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"

	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func StoreBusiness(ctx context.Context, req *report.StoreBusinessRequest) (*report.StoreBusinessResponse, error) {
	resp, err := repo.DefaultSalesRepository.StoreBusiness(ctx, req)
	ids := make([]int64, 0)
	for _, row := range resp.Rows {
		ids = append(ids, row.StoreId)
	}
	ctx = context.WithValue(ctx, oauth.PartnerID, req.PartnerId)
	ctx = context.WithValue(ctx, oauth.UserID, uint64(104))
	ctx = context.WithValue(ctx, oauth.UserInfo, &oauth.UserAccount{
		ID: uint64(104),
		PartnerID: req.PartnerId,
	})
	entityMap, err := entity.GetIdsFieldMap(ctx, "STORE", ids, "zh-CN")
	if err != nil {
		return nil, err
	}
	for _, row := range resp.Rows {
		row.StoreName = cast.ToString(entityMap[row.StoreId]["name"])
		row.StoreCode = cast.ToString(entityMap[row.StoreId]["code"])
	}
	return resp, err
}
