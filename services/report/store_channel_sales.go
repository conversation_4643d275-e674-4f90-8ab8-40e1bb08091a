package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryStoreChannelSales(ctx context.Context, query *model.CommonRequest) (*report.StoreChannelSalesResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryStoreChannelSales] receive body. Content: `%+v`", trackId, query)

	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] get user info failed.\nErr: `%+v`", trackId, err)
	//	return nil, hexerror.PermissionDenied("No user info.")
	//}

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	// 增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.StoreChannelSales(ctx, condition)
	packagingForStoreChannelSales(ctx, query, resp)
	return resp, err
}

func packagingForStoreChannelSales(ctx context.Context, query *model.CommonRequest, resp *report.StoreChannelSalesResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	channelIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		regionIds = append(regionIds, row.RegionId)
		for _, r := range row.Child {
			channelIds = append(channelIds, r.ChannelId)
		}
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	for r := range resp.Rows {
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		resp.Rows[r].RegionCode = cast.ToString(code)    // 门店code
		resp.Rows[r].RegionName = cast.ToString(name)    // 门店名称
		resp.Rows[r].RegionAddress = cast.ToString(city) // 门店所在城市
		resp.Rows[r].RegionAlias = cast.ToString(alias)  // 门店别名
		for i := range resp.Rows[r].Child {
			channelId := resp.Rows[r].Child[i].ChannelId
			resp.Rows[r].Child[i].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
			resp.Rows[r].Child[i].ChannelName = cast.ToString(channelMaps[channelId]["name"])
			for j := range resp.Rows[r].Child[i].Child {
				switch resp.Rows[r].Child[i].Child[j].OrderType {
				case "DINEIN":
					resp.Rows[r].Child[i].Child[j].OrderTypeName = "堂食"
				case "TAKEAWAY":
					resp.Rows[r].Child[i].Child[j].OrderTypeName = "外带"
				case "TAKEOUT":
					resp.Rows[r].Child[i].Child[j].OrderTypeName = "外卖"
				case "SELFHELP":
					resp.Rows[r].Child[i].Child[j].OrderTypeName = "自提"
				default:
					resp.Rows[r].Child[i].Child[j].OrderTypeName = "无需此信息"
				}
			}
		}
	}
}
