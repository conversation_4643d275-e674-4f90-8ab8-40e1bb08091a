package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryPaymentPeriodSales(ctx context.Context, query *model.CommonRequest) (*report.PaymentPeriodResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryPaymentPeriodSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	if err := DataCheck(ctx, condition); err != nil {
		return nil, err
	}
	resp, err := repo.DefaultSalesRepository.PaymentPeriodSales(ctx, condition)
	packagingForPaymentPeriodSales(ctx, query, resp)
	return resp, err
}

func packagingForPaymentPeriodSales(ctx context.Context, query *model.CommonRequest, resp *report.PaymentPeriodResponse) {
	//helpers.DddEmptySales(query, resp)
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	paymentIds := make([]int64, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	paymentMaps := GetPaymentMapsByIds(ctx, query, paymentIds)
	// 组装code和name
	for r := range resp.Rows {
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])
		regionId := resp.Rows[r].RegionId
		resp.Rows[r].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[r].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[r].RegionAddress = cast.ToString(regionMaps[regionId]["region_name"])
		resp.Rows[r].RegionAlias = cast.ToString(regionMaps[regionId]["alias"])
		for i := range resp.Rows[r].Child {
			//TODO 兼容处理
			if resp.Rows[r].Child[i].PaymentName == ""{
				paymentId := resp.Rows[r].Child[i].PaymentId
				resp.Rows[r].Child[i].PaymentCode = cast.ToString(paymentMaps[paymentId]["code"])
				resp.Rows[r].Child[i].PaymentName = cast.ToString(paymentMaps[paymentId]["name"])
				resp.Rows[r].Child[i].PaymentType = cast.ToString(paymentMaps[paymentId]["getCouponType"])
			}
		}
	}
}

func packagingForPaymentPeriodSalesForPre(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	regionMaps, paymentMaps := GetRegionMaps(ctx, query, resp), GetPaymentMethodMaps(ctx, query, resp)
	nRows := make([]interface{}, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]

		paymentId := cast.ToInt64(r["payment_id"])
		paymentCode := paymentMaps[paymentId]["code"]
		paymentName := paymentMaps[paymentId]["name"]

		nRows = append(nRows, map[string]interface{}{
			"region_id":          cast.ToString(regionId),
			"region_code":        cast.ToString(regionCode),
			"region_name":        cast.ToString(regionName),
			"region_status":      cast.ToString(regionStatus),
			"payment_id":         cast.ToString(paymentId),
			"payment_code":       cast.ToString(paymentCode),
			"payment_name":       cast.ToString(paymentName),
			"net_amount_period":  ToPeriodMapsForPre("net_amount", r),
			"order_count_period": ToPeriodMapsForPre("order_count", r),
		})
	}
	resp.Rows = nRows
	resp.Total = cast.ToInt64(resp.Summary["total"])
	resp.Summary = map[string]interface{}{
		"net_amount_period":  ToPeriodMapsForPre("net_amount", resp.Summary),
		"order_count_period": ToPeriodMapsForPre("order_count", resp.Summary),
	}
}
