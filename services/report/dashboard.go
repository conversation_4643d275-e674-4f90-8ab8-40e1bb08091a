package report

import (
	"context"

	"github.com/spf13/cast"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryDashboard(ctx context.Context, query *model.DashboardRequest) (*model.DashboardResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryDashboard] receive body. Content: `%+v`", trackId, query)

	//info, err := oauth.GetUserInfoFromCtx(ctx)
	//if err != nil {
	//	logger.Pre().Errorf("[%d] [services.UpdateSalesInfo] get user info failed.\nErr: `%+v`", trackId, err)
	//	return nil, hexerror.PermissionDenied("No user info.")
	//}
	if len(query.Panel) == 0 {
		query.Panel = []string{"summary", "line_chart", "store_rank", "channel_rank", "product_rank"}
	}
	condition := query.ToRepoCondition()
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	resp, err := repo.DefaultSalesRepository.Dashboard(ctx, condition)
	packagingForDashboard(ctx, query, resp)
	return resp, err
}

func packagingForDashboard(ctx context.Context, query *model.DashboardRequest, resp *model.DashboardResponse) {
	if helpers.Contains(query.Panel, "line_chart") {
		r := cast.ToStringMap(resp.LineChart["line_chart"])
		resp.LineChart = map[string]interface{}{
			"net_amount_period":  ToPeriodMapsForPre("net_amount", r),
			"order_count_period": ToPeriodMapsForPre("order_count", r),
		}
	}
	if helpers.Contains(query.Panel, "store_rank") {
		storeMaps := GetStoreMapsForDashboard(ctx, query, resp)
		nRows := make([]interface{}, 0, len(resp.StoreRank))
		for i, row := range resp.StoreRank {
			r := row.(map[string]interface{})
			storeId := cast.ToInt64(r["store_id"])
			storeName := storeMaps[storeId]["name"]
			alias := storeMaps[storeId]["alias"]
			nRows = append(nRows, map[string]interface{}{
				"number":      i,
				"store_id":    cast.ToString(storeId),
				"store_name":  cast.ToString(storeName),
				"store_alias": cast.ToString(alias),
				"net_amount":  cast.ToFloat64(r["net_amount"]),
				"order_count": cast.ToFloat64(r["order_count"]),
			})
		}
		resp.StoreRank = nRows
	}
	if helpers.Contains(query.Panel, "channel_rank") {
		channelMaps := GetChannelMapsForDashboard(ctx, query, resp)
		nRows := make([]interface{}, 0, len(resp.ChannelRank))
		for i, row := range resp.ChannelRank {
			r := row.(map[string]interface{})
			channelId := cast.ToInt64(r["channel_id"])
			channelName := channelMaps[channelId]["name"]
			nRows = append(nRows, map[string]interface{}{
				"number":       i,
				"channel_id":   cast.ToString(channelId),
				"channel_name": cast.ToString(channelName),
				"net_amount":   cast.ToFloat64(r["net_amount"]),
				"order_count":  cast.ToFloat64(r["order_count"]),
			})
		}
		resp.ChannelRank = nRows
	}
	if helpers.Contains(query.Panel, "product_rank") {
		productMaps := GetProductMapsForDashboard(ctx, query, resp)
		nRows := make([]interface{}, 0, len(resp.ProductRank))
		for i, row := range resp.ProductRank {
			r := row.(map[string]interface{})
			productId := cast.ToInt64(r["product_id"])
			productName := productMaps[productId]["name"]
			nRows = append(nRows, map[string]interface{}{
				"number":       i,
				"product_id":   cast.ToString(productId),
				"product_name": cast.ToString(productName),
				"net_amount":   cast.ToFloat64(r["net_amount"]),
				"order_count":  cast.ToFloat64(r["order_count"]),
			})
		}
		resp.ProductRank = nRows
	}
}
