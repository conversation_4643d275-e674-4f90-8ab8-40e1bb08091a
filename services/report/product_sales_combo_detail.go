package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductSalesComboDetail(ctx context.Context, query *model.CommonRequest) (*report.ProductSalesComboDetailResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesComboDetail] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	// 为了区分是套餐销售明细
	condition.IsCombo = true
	resp, err := repo.DefaultSalesRepository.ProductSalesComboDetail(ctx, condition)
	PackagingForProductSalesComboDetail(ctx, query, resp)
	return resp, err
}

func PackagingForProductSalesComboDetail(ctx context.Context, query *model.CommonRequest, resp *report.ProductSalesComboDetailResponse) {
	regionIds := make([]int64, 0)
	branchRegionIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	storeTypes := make([]int64, 0, 0)
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		branchRegionIds = append(branchRegionIds, row.BranchRegionId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		categoryIds = append(categoryIds, row.CategoryId)
		productIds = append(productIds, row.ProductId)

		if row.ComboProductId != 0 {
			productIds = append(productIds, row.ComboProductId)
		}
		//if row.ChannelId != 0 {
		//	channelIds = append(channelIds, row.ChannelId)
		//}
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	branchRegionMaps := GetRegionMapsByIdsSchema(ctx, "branch_region", query.Lan, branchRegionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	fmt.Println("channelMaps", channelMaps)
	for i := range resp.Rows {
		PackagingForProductSalesComboDetailRow(ctx, query, resp.Rows[i], regionMaps, branchRegionMaps, categoryMaps, productMaps, channelMaps, storeTypeMaps)
		resp.Rows[i].RowIdx = int32(query.Offset + i + 1)
	}

}

func PackagingForProductSalesComboDetailRow(ctx context.Context, query *model.CommonRequest, row *report.ProductSalesComboDetail,
	regionMaps map[int64]map[string]interface{}, branchRegionMaps map[int64]map[string]interface{}, categoryMaps map[int64]map[string]interface{},
	productMaps map[int64]map[string]interface{}, channelMaps map[int64]map[string]interface{}, storeTypeMaps map[int64]map[string]interface{},
) {
	if row.AddTime == "0001-01-01 00:00:00" {
		row.AddTime = "-"
	}
	row.Qty = RoundFloat64(row.Qty)
	row.Price = RoundFloat64(row.Price)
	row.GrossAmount = RoundFloat64(row.GrossAmount)
	row.DiscountAmount = RoundFloat64(row.DiscountAmount)
	row.NetAmount = RoundFloat64(row.NetAmount)

	row.BranchRegion = cast.ToString(branchRegionMaps[row.BranchRegionId]["name"])
	row.RegionAddress = cast.ToString(regionMaps[row.RegionId]["region_name"])
	row.RegionAddress1 = cast.ToString(regionMaps[row.RegionId]["region_name1"])
	row.RegionAddress2 = cast.ToString(regionMaps[row.RegionId]["region_name2"])
	row.RegionAddress3 = cast.ToString(regionMaps[row.RegionId]["region_name3"])
	row.StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(row.StoreType)]["name"])
	row.RegionCode = cast.ToString(regionMaps[row.RegionId]["code"])
	row.RegionName = cast.ToString(regionMaps[row.RegionId]["name"])
	row.CategoryName = cast.ToString(categoryMaps[row.CategoryId]["name"])
	row.ProductCode = cast.ToString(productMaps[row.ProductId]["code"])
	row.ProductName = cast.ToString(productMaps[row.ProductId]["name"])

	row.SkuRemark = cast.ToString(productMaps[row.ProductId]["sale_name"])
	if row.SkuRemark == row.ProductName {
		row.SkuRemark = "-"
	}
	servingSize := cast.ToString(productMaps[row.ProductId]["serving_size"])
	if servingSize == "" {
		row.Unit = row.Unit
	} else if row.Unit != "" {
		row.Unit = fmt.Sprintf("%s %s", servingSize, row.Unit)
	} else {
		row.Unit = "-"
	}
	row.ComboProductName = cast.ToString(productMaps[row.ComboProductId]["name"])
	if row.ProductId == 0 {
		row.ProductCode = cast.ToString(productMaps[row.ComboProductId]["code"])
		row.ProductName = row.ComboProductName
	}
	row.OrderTypeName = GetOrderTypeName(row.OrderType)
	row.GroupPurchaseChannel = strings.Replace(row.GroupPurchaseChannel, "券ISV", "", 1)

}
