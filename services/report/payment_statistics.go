package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryPaymentStatistics(ctx context.Context, query *model.CommonRequest) (*report.PaymentStatisticsResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.financial.QueryPaymentStatistics] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	if err := DataCheck(ctx, condition); err != nil {
		return nil, err
	}
	resp, err := repo.DefaultSalesRepository.PaymentStatistics(ctx, condition)
	packagingForPaymentStatistics(ctx, query, resp)
	return resp, err
}

func packagingForPaymentStatistics(ctx context.Context, query *model.CommonRequest, resp *report.PaymentStatisticsResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	branchIds := make([]int64, 0, len(resp.Rows))
	channelIds := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		branchIds = append(branchIds, row.BranchId)
		channelIds = append(channelIds, row.ChannelId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	branchMap := GetBranchMapsByIds(ctx, query, branchIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	//paymentMaps := GetPaymentMapsByIds(ctx, query, paymentIds)
	// 组装code和name
	for r := range resp.Rows {
		regionId := resp.Rows[r].RegionId
		resp.Rows[r].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[r].RegionName = cast.ToString(regionMaps[regionId]["name"])
		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMap[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMap[branchId]["name"])
		channelId := resp.Rows[r].ChannelId
		resp.Rows[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.Rows[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
	}
}
