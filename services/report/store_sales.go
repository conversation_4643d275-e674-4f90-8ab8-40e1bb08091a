package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"strconv"
)

// 门店销售报表，http和grpc用的都是这个接口
func QueryStoreSales(ctx context.Context, query *model.CommonRequest) (*model.Response, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryStoreSales] receive body. Content: `%+v`", trackId, query)
	table := model.SalesTicketAmount{}
	// 组装Sql查询条件
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	// 调用门店销售报表接口层
	resp, err := repo.DefaultSalesRepository.StoreSales(ctx, condition)
	// 组装查询到的结果数据
	packagingForStoreSales(ctx, query, resp)
	return resp, err
}

// 将响应结果封装返回
func packagingForStoreSales(ctx context.Context, query *model.CommonRequest, resp *model.Response) {
	// 查询异常电子小票单数的和
	ctx = context.WithValue(ctx, "lan", query.Lan)
	var sumErrorCount int64
	var busDate = fmt.Sprintf("%s;%s", query.Start.Format("2006-01-02T15:04:05Z"), query.End.Format("2006-01-02T15:04:05Z"))
	if len(query.RegionSearchIds) > 0 {
		for _, storeId := range query.RegionSearchIds {
			total, _ := QueryErrorTicket(ctx, storeId, busDate, query)
			sumErrorCount += total
		}
	} else {
		total, _ := QueryErrorTicket(ctx, "0", busDate, query)
		sumErrorCount += total
	}
	//helpers.DddEmptySales(query, resp)
	regionMaps := GetRegionMaps(ctx, query, resp)
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		m := cast.ToStringMap(row)
		storeTypes = append(storeTypes, cast.ToInt64(m["store_type"]))
	}
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	nRows := make([]interface{}, 0, len(resp.Rows))
	var (
		sumBusinessDays int64
	)
	// 遍历查询到的每一行数据，将数据映射为响应体
	for _, row := range resp.Rows {
		r := row.(map[string]interface{})
		regionId := cast.ToInt64(r["region_id"])
		city := regionMaps[regionId]["region_name"] //门店所在城市
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionStatus := regionMaps[regionId]["status"]
		alias := regionMaps[regionId]["alias"]
		storeTypeName := storeTypeMaps[cast.ToInt64(r["store_type"])]["name"]
		// 组装每一条记录，形成每一行记录
		var row = map[string]interface{}{
			"period_group":                cast.ToString(r["period_group"]), // 营业日期
			"region_address":              cast.ToString(city),              // 所在城市
			"region_id":                   cast.ToString(regionId),
			"region_code":                 cast.ToString(regionCode), // 门店编号
			"region_name":                 cast.ToString(regionName), // 门店名称
			"region_status":               cast.ToString(regionStatus),
			"region_alias":                cast.ToString(alias),
			"store_type":                  cast.ToInt64(r["store_type"]),
			"store_type_name":             cast.ToString(storeTypeName),
			"gross_amount":                cast.ToFloat64(r["gross_amount"]),
			"net_amount":                  cast.ToFloat64(r["net_amount"]),
			"discount_amount":             cast.ToFloat64(r["discount_amount"]),
			"tip":                         cast.ToFloat64(r["tip"]),
			"package_fee":                 cast.ToFloat64(r["package_fee"]),
			"delivery_fee":                cast.ToFloat64(r["delivery_fee"]),
			"service_fee":                 cast.ToFloat64(r["service_fee"]),
			"tax_fee":                     cast.ToFloat64(r["tax_fee"]),
			"other_fee":                   cast.ToFloat64(r["other_fee"]),
			"pay_amount":                  cast.ToFloat64(r["pay_amount"]),
			"rounding":                    cast.ToFloat64(r["rounding"]),
			"overflow_amount":             cast.ToFloat64(r["overflow_amount"]),
			"change_amount":               cast.ToFloat64(r["change_amount"]),
			"valid_order_count":           cast.ToInt64(r["order_count"]),
			"product_count":               cast.ToInt64(r["product_count"]),
			"accessory_count":             cast.ToInt64(r["accessory_count"]),
			"gross_amount_returned":       cast.ToFloat64(r["gross_amount_returned"]), // 退单商品流水
			"net_amount_returned":         cast.ToFloat64(r["net_amount_returned"]),
			"discount_amount_returned":    cast.ToFloat64(r["discount_amount_returned"]),
			"tip_returned":                cast.ToFloat64(r["tip_returned"]),
			"package_fee_returned":        cast.ToFloat64(r["package_fee_returned"]),
			"delivery_fee_returned":       cast.ToFloat64(r["delivery_fee_returned"]),
			"service_fee_returned":        cast.ToFloat64(r["service_fee_returned"]),
			"tax_fee_returned":            cast.ToFloat64(r["tax_fee_returned"]),
			"other_fee_returned":          cast.ToFloat64(r["other_fee_returned"]),
			"pay_amount_returned":         cast.ToFloat64(r["pay_amount_returned"]),
			"rounding_returned":           cast.ToFloat64(r["rounding_returned"]),
			"overflow_amount_returned":    cast.ToFloat64(r["overflow_amount_returned"]),
			"change_amount_returned":      cast.ToFloat64(r["change_amount_returned"]),
			"order_count_returned":        cast.ToInt64(r["order_count_returned"]),
			"commission":                  cast.ToFloat64(r["commission"]),
			"payment_transfer_amount":     cast.ToFloat64(r["payment_transfer_amount"]),  //支付转折扣
			"discount_transfer_amount":    cast.ToFloat64(r["discount_transfer_amount"]), //折扣转支付
			"commission_returned":         cast.ToFloat64(r["commission_returned"]),
			"business_amount":             cast.ToFloat64(r["amount_0"]),                                                                                // 营业额
			"expend_amount":               cast.ToFloat64(r["amount_1"]) + cast.ToFloat64(r["rounding"]),                                                // 支出
			"transfer_real_amount":        cast.ToFloat64(r["transfer_real_amount"]),                                                                    // 实收金额转换后（财务）
			"real_amount":                 cast.ToFloat64(r["amount_2"]) - cast.ToFloat64(r["rounding"]),                                                // 实收金额
			"receivable":                  cast.ToFloat64(r["receivable"]),                                                                              // 应收金额
			"receivable_returned":         cast.ToFloat64(r["receivable_returned"]),                                                                     // 退单应收金额
			"merchant_send_fee":           cast.ToFloat64(r["merchant_send_fee"]),                                                                       // 商家运费承担
			"merchant_send_fee_returned":  cast.ToFloat64(r["merchant_send_fee_returned"]),                                                              // 退单商家运费承担
			"business_amount_returned":    cast.ToFloat64(r["business_amount_returned"]),                                                                // 退单营业额
			"merchant_allowance":          cast.ToFloat64(r["merchant_discount_amount"]) + cast.ToFloat64(r["store_discount_amount"]),                   // 商家活动补贴
			"merchant_allowance_returned": cast.ToFloat64(r["merchant_discount_amount_returned"]) + cast.ToFloat64(r["store_discount_amount_returned"]), // 退单商家活动补贴
			"platform_allowance":          cast.ToFloat64(r["platform_discount_amount"]),                                                                // 平台优惠承担
			"platform_allowance_returned": cast.ToFloat64(r["platform_discount_amount_returned"]),                                                       // 退单平台优惠承担
			"order_count":                 cast.ToInt(r["order_count_sale"]),                                                                            // 销售单数量
			"order_count_partialrefund":   cast.ToInt(r["order_count_partialrefund"]),                                                                   // 部分退款单数量
			"order_count_refund":          cast.ToInt64(r["order_count_refund"]),                                                                        // 退款单数量
			"business_days":               cast.ToFloat64(r["business_days"]),                                                                           // 营业天数
			//"transfer_real_amount": cast.ToFloat64(r["transfer_real_amount"]), // 实收金额转换后（财务）
			"discount_contribute":          cast.ToFloat64(r["discount_amount1"]),                                                        // 优惠组成（财务）
			"send_fee":                     cast.ToFloat64(r["send_fee"]),                                                                // 配送费用户实际支付
			"platform_send_fee":            cast.ToFloat64(r["platform_send_fee"]),                                                       // 配送费平台承担
			"discount_merchant_contribute": cast.ToFloat64(r["discount_merchant_contribute"]),                                            // 优惠组成-折扣（财务）
			"pay_merchant_contribute":      cast.ToFloat64(r["pay_merchant_contribute"]),                                                 // 优惠组成-支付
			"real_amount_discount":         cast.ToFloat64(r["real_amount_discount"]),                                                    // 实收组成-折扣（财务）
			"real_amount_payment":          cast.ToFloat64(r["real_amount_payment"]),                                                     // 实收组成-支付（财务）
			"real_amount_without_gst":      cast.ToFloat64(r["amount_2"]) - cast.ToFloat64(r["rounding"]) - cast.ToFloat64(r["tax_fee"]), // 实收不包含税额
			"business_amount_without_gst":  cast.ToFloat64(r["amount_0"]) - cast.ToFloat64(r["tax_fee"]),                                 // 营业额不包含税额
		}
		// 营业额：正单营业额 sql算出来的时候已经将退单的减去了
		var businessAmount = cast.ToFloat64(row["business_amount"])
		// 有效订单数:正单-全部退单数
		orderCount := cast.ToFloat64(row["valid_order_count"])
		var value = 0.0
		if orderCount != 0 {
			value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", businessAmount/orderCount), 64)
		}
		row["customer_price"] = value // 客单价
		// 其他费用
		nRows = append(nRows, row)
		// 汇总营业额，支出等数据
		sumBusinessDays += cast.ToInt64(row["business_days"])
	}
	// 查询的行数据
	resp.Rows = nRows
	// 查询的统计数据汇总
	if resp.Summary != nil {
		resp.Summary["business_amount"] = resp.Summary["amount_0"]                                                          // 营业额
		resp.Summary["business_amount_returned"] = resp.Summary["amount_0_returned"]                                        // 退单营业额
		resp.Summary["expend_amount"] = cast.ToFloat64(resp.Summary["amount_1"]) + cast.ToFloat64(resp.Summary["rounding"]) // 支出
		resp.Summary["real_amount"] = cast.ToFloat64(resp.Summary["amount_2"]) - cast.ToFloat64(resp.Summary["rounding"])   // 实收
		//resp.Summary["transform_real_amount"] = resp.Summary["amount_2"]
		resp.Summary["valid_order_count"] = resp.Summary["order_count"]                                                                                       // 有效订单数
		resp.Summary["merchant_allowance"] = cast.ToFloat64(resp.Summary["merchant_discount_amount"]) + cast.ToFloat64(resp.Summary["store_discount_amount"]) // 商家活动补贴
		resp.Summary["order_count_refund"] = resp.Summary["order_count_refund"]                                                                               // 退单数
		//resp.Summary["tp_allowance"] = resp.Summary["platform_discount_amount"]                                                                               // 平台活动补贴
		resp.Summary["err_ticket_count"] = sumErrorCount
		resp.Summary["business_days"] = cast.ToInt64(resp.Summary["business_days"])
		var customerPrice = 0.0
		if cast.ToFloat64(resp.Summary["valid_order_count"]) != 0 {
			customerPrice, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", cast.ToFloat64(resp.Summary["business_amount"])/cast.ToFloat64(resp.Summary["valid_order_count"])), 64)
		}
		resp.Summary["customer_price"] = customerPrice
		resp.Summary["real_amount_without_gst"] = cast.ToFloat64(resp.Summary["amount_2"]) - cast.ToFloat64(resp.Summary["rounding"]) - cast.ToFloat64(resp.Summary["tax_fee"])
		resp.Summary["business_amount_without_gst"] = cast.ToFloat64(resp.Summary["amount_0"]) - cast.ToFloat64(resp.Summary["tax_fee"])
	}
	resp.Total = cast.ToInt64(cast.ToStringMap(resp.Summary)["total"])
}

func QueryErrorTicket(ctx context.Context, storeId, busDate string, query *model.CommonRequest) (int64, error) {
	// 组装查询条件
	var errTicketReq = &model.ErrorTicketRequest{
		StoreId:      cast.ToInt64(storeId),
		BusDate:      busDate,
		Status:       model.NewSalesInfoStatus("DATAERROR"),
		Limit:        query.Limit,
		Offset:       query.Offset,
		IncludeTotal: cast.ToString(query.IncludeTotal),
		Lan:          query.Lan,
	}
	_, total, err := services.QuerySalesInfo(ctx, errTicketReq)
	return total, err
}
