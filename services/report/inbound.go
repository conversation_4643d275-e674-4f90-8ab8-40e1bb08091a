package report

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	//"log/slog"
	"math"
	"strings"
)

func QueryInbound(ctx context.Context, query *model.CommonRequest) (*report.InboundResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryInbound] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultInboundQueryDB.Inbound(ctx, condition)
	packagingForInbound(ctx, query, resp)
	return resp, err
}

func packagingForInbound(ctx context.Context, query *model.CommonRequest, resp *report.InboundResponse) {
	// 四舍五入保留2位小数
	for _, row := range resp.Rows {
		row.Price = math.Round(row.Price*100) / 100
		row.Qty = math.Round(row.Qty*100) / 100
		row.Amount = math.Round(row.Amount*100) / 100
	}
	productIds := make([]int64, 0)
	for _, r := range resp.Rows {
		productIds = append(productIds, r.ProductId)
	}
	productMaps := GetSchemaProductMapsByIds(ctx, query, productIds)
	for _, r := range resp.Rows {
		r.ProductCode = cast.ToString(productMaps[r.ProductId]["code"])
		r.ProductName = cast.ToString(productMaps[r.ProductId]["sale_name"])
		r.SkuRemarkName = cast.ToString(productMaps[r.ProductId]["sale_name"])

		// 将 Content 解析为 map[string]interface{}
		var contentMap map[string]interface{}
		if err := json.Unmarshal(r.Content, &contentMap); err != nil {
			//slog.Error("Unmarshal productMaps err:", err)
			return
		}
		names := ""

		if value, found := contentMap["products"]; found {
			if vList, ok := value.([]interface{}); ok {
				for _, p := range vList {
					product := p.(map[string]interface{})
					if cast.ToInt64(product["id"]) == r.ProductId {
						for _, s := range product["skuRemark"].([]interface{}) {
							skuRemark := s.(map[string]interface{})
							tempSkuName := skuRemark["name"].(map[string]interface{})
							nameToString := cast.ToString(tempSkuName["name"])
							names += nameToString + ","
						}
					}
				}
			}
		}
		// 使用 strings.TrimSuffix 去掉最后一个逗号
		names = strings.TrimSuffix(names, ",")
		r.SkuRemarkName = names
		//if r.ProductName == r.SkuRemarkName {
		//	// 单规格
		//	r.SkuRemarkName = ""
		//}
	}
}
