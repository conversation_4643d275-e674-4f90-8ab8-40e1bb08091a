package report

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
)

type Graph struct {
}

func (g *Graph) Transform(ctx context.Context, condition *model.RepoCondition, resp *report.ProductFlavorSalesResponse) {
	accessoryMap := getFeedMap(resp)
	feedGraphs := make([]*report.ProductFlavorSales, 0)
	for _, v := range accessoryMap {
		flavor := v.ProductFlavor
		flavor.Feeds = make([]*report.Accessory, 0)
		for _, feed := range v.AccessoriesInfo {
			if feed.AccessoryName == "" {
				continue
			}
			flavor.Feeds = append(flavor.Feeds, &report.Accessory{
				AccessoryName:       feed.AccessoryName,
				ProductCount:        feed.ProductCount,
				PercentProductCount: feed.PercentProductCount,
			})
		}
		feedGraphs = append(feedGraphs, flavor)
	}
	resp.Graph = feedGraphs

	attrMap := getAttrMap(resp)
	attrGraphs := make([]*report.ProductFlavorSales, 0)
	for _, v := range attrMap {
		flavor := v.ProductFlavor
		flavor.AttributeNames = make([]*report.AttributeName, 0)
		for _, attrName := range v.AttributeInfo {
			if attrName.AttributeNameName == "" {
				continue
			}
			attrValues := make([]*report.AttributeValue, 0)
			for _, attrValue := range attrName.AttributeNameValues {
				if attrValue.AttributeValueName == "" {
					continue
				}
				attrValues = append(attrValues, &report.AttributeValue{
					AttributeValueCode:  attrValue.AttributeValueCode,
					AttributeValueName:  attrValue.AttributeValueName,
					ProductCount:        attrValue.ProductCount,
					PercentProductCount: attrValue.PercentProductCount,
				})
			}
			flavor.AttributeNames = append(flavor.AttributeNames, &report.AttributeName{
				AttributeNameCode:   attrName.AttributeNameCode,
				AttributeNameName:   attrName.AttributeNameName,
				ProductCount:        attrName.ProductCount,
				PercentProductCount: attrName.PercentProductCount,
				AttributeNameValues: attrValues,
			})
		}
		attrGraphs = append(attrGraphs, flavor)
	}
	resp.Graph = attrGraphs
}
