package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryStoreMakeTime(ctx context.Context, query *model.CommonRequest) (*report.StoreMakeTimeResponse, error) {
	condition := query.ToRepoCondition([]string{})
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultMakeTimeQueryRepository.StoreMakeTime(ctx, condition)
	storeMakeTimeProcessResponse(ctx, query, resp)
	return resp, err
}

func storeMakeTimeProcessResponse(ctx context.Context, query *model.CommonRequest, resp *report.StoreMakeTimeResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, row.StoreType)
		regionIds = append(regionIds, row.RegionId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)

	// 组装code和name
	for i := range resp.Rows {
		resp.Rows[i].TimePeriod = fmt.Sprintf("%v:00 - %v:00", resp.Rows[i].MakeHour, resp.Rows[i].MakeHour+1)

		resp.Rows[i].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[i].StoreType)]["name"]) // 门店经营类型
		regionId := resp.Rows[i].RegionId
		resp.Rows[i].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[i].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[i].RegionAddress = cast.ToString(regionMaps[regionId]["region_name"])
		resp.Rows[i].RegionAlias = cast.ToString(regionMaps[regionId]["alias"])
	}

}
