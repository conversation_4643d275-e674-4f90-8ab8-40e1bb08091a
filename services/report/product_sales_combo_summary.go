package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductSalesComboSummary(ctx context.Context, query *model.CommonRequest) (*report.ProductSalesComboSummaryResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesComboSummary] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	// 为了区分是套餐销售汇总，所以设置为true
	condition.IsCombo = true
	resp, err := repo.DefaultSalesRepository.ProductSalesComboSummary(ctx, condition)
	PackagingForProductSalesComboSummary(ctx, query, resp)
	return resp, err
}

func PackagingForProductSalesComboSummary(ctx context.Context, query *model.CommonRequest, resp *report.ProductSalesComboSummaryResponse) {
	regionIds := make([]int64, 0)
	branchRegionIds := make([]int64, 0)
	storeTypes := make([]int64, 0, 0)
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		branchRegionIds = append(branchRegionIds, row.BranchRegionId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		categoryIds = append(categoryIds, row.CategoryId)
		productIds = append(productIds, row.ProductId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	branchRegionMaps := GetRegionMapsByIdsSchema(ctx, "branch_region", query.Lan, branchRegionIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	for i := range resp.Rows {
		PackagingForProductSalesComboSummaryRow(ctx, query, resp.Rows[i], regionMaps, branchRegionMaps, categoryMaps, productMaps, storeTypeMaps)
		resp.Rows[i].RowIdx = int32(query.Offset + i + 1)
	}

}

func PackagingForProductSalesComboSummaryRow(ctx context.Context, query *model.CommonRequest, row *report.ProductSalesComboSummary,
	regionMaps map[int64]map[string]interface{}, branchRegionMaps map[int64]map[string]interface{},
	categoryMaps map[int64]map[string]interface{}, productMaps map[int64]map[string]interface{}, storeTypeMaps map[int64]map[string]interface{},
) {
	row.Qty = RoundFloat64(row.Qty)
	row.Price = RoundFloat64(row.Price)
	row.GrossAmount = RoundFloat64(row.GrossAmount)
	row.DiscountAmount = RoundFloat64(row.DiscountAmount)
	row.NetAmount = RoundFloat64(row.NetAmount)
	row.BranchRegion = cast.ToString(branchRegionMaps[row.BranchRegionId]["name"])
	row.RegionName = cast.ToString(regionMaps[row.RegionId]["name"])
	row.RegionCode = cast.ToString(regionMaps[row.RegionId]["code"])
	row.RegionAddress = cast.ToString(regionMaps[row.RegionId]["region_name"])
	row.RegionAddress1 = cast.ToString(regionMaps[row.RegionId]["region_name1"])
	row.RegionAddress2 = cast.ToString(regionMaps[row.RegionId]["region_name2"])
	row.RegionAddress3 = cast.ToString(regionMaps[row.RegionId]["region_name3"])
	row.StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(row.StoreType)]["name"])
	row.CategoryName = cast.ToString(categoryMaps[row.CategoryId]["name"])
	row.ProductCode = cast.ToString(productMaps[row.ProductId]["code"])
	row.ProductName = cast.ToString(productMaps[row.ProductId]["name"])
	row.GroupPurchaseChannel = strings.Replace(row.GroupPurchaseChannel, "券ISV", "", 1)
}
