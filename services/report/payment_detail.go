package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryPaymentDetail(ctx context.Context, query *model.CommonRequest) (*report.PaymentDetailResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.financial.QueryPaymentStatistics] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 数据校验
	//if err := DataCheck(ctx, condition); err != nil {
	//	return nil, err
	//}
	resp, err := repo.DefaultSalesRepository.PaymentDetail(ctx, condition)
	return resp, err
}
