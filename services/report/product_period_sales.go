package report

import (
	"context"
	"errors"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductPeriodSales(ctx context.Context, query *model.CommonRequest) (*report.ProductPeriodResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductPeriodSales] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	if len(condition.PriceScope) == 0 && query.PriceScope != "" {
		return nil, errors.New("invalid price scope")
	}
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.ProductPeriodSales(ctx, condition)
	packagingForProductPeriodSales(ctx, query, resp)
	return resp, err
}

func packagingForProductPeriodSales(ctx context.Context, query *model.CommonRequest, resp *report.ProductPeriodResponse) {
	//helpers.DddEmptySales(query, resp)
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		regionIds = append(regionIds, row.RegionId)
		for _, r := range row.Child {
			categoryIds = append(categoryIds, r.ProductCategoryId)
			productIds = append(productIds, r.ProductId)
		}
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	// 组装code和name
	for i := range resp.Rows {
		resp.Rows[i].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[i].StoreType)]["name"]) // 门店经营类型
		regionId := resp.Rows[i].RegionId
		resp.Rows[i].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[i].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[i].RegionAddress = cast.ToString(regionMaps[regionId]["region_name"])
		resp.Rows[i].RegionAlias = cast.ToString(regionMaps[regionId]["alias"])
		for j := range resp.Rows[i].Child {
			productId := resp.Rows[i].Child[j].ProductId
			name := cast.ToString(productMaps[productId]["name"])
			saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
			pName := ""
			if name == saleName {
				pName = saleName
			} else {
				if strings.Contains(saleName, name) {
					pName = saleName
				} else {
					pName = name + saleName
				}
			}
			resp.Rows[i].Child[j].ProductCode = cast.ToString(productMaps[productId]["code"])
			resp.Rows[i].Child[j].ProductName = pName // 修改商品名称获取方式
			categoryId := resp.Rows[i].Child[j].ProductCategoryId
			resp.Rows[i].Child[j].ProductCategoryCode = cast.ToString(categoryMaps[categoryId]["code"])
			resp.Rows[i].Child[j].ProductCategoryName = cast.ToString(categoryMaps[categoryId]["name"])
		}
	}
}
