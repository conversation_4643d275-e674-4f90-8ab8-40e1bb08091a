package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"math"
)

func QueryAdjust(ctx context.Context, query *model.CommonRequest) (*report.AdjustResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryAdjust] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultAdjustQueryDB.Adjust(ctx, condition)
	packagingForAdjust(ctx, query, resp)
	return resp, err
}

func packagingForAdjust(ctx context.Context, query *model.CommonRequest, resp *report.AdjustResponse) {
	// 四舍五入保留2位小数
	for _, row := range resp.Rows {
		row.Price = math.Round(row.Price*100) / 100
		row.Qty = math.Round(row.Qty*100) / 100
		row.Amount = math.Round(row.Amount*100) / 100
	}
	productIds := make([]int64, 0)
	for _, r := range resp.Rows {
		productIds = append(productIds, r.SkuId)
	}
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	for _, r := range resp.Rows {
		r.ProductCode = cast.ToString(productMaps[r.SkuId]["code"])
		r.ProductName = cast.ToString(productMaps[r.SkuId]["name"])
		r.SkuRemarkName = cast.ToString(productMaps[r.SkuId]["sale_name"])
		if r.ProductName == r.SkuRemarkName {
			// 单规格
			r.SkuRemarkName = ""
		}
	}
}
