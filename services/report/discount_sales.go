package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func QueryDiscountSales(ctx context.Context, query *model.CommonRequest) (*report.DiscountSalesResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryDiscountSales] receive body. Content: `%+v`", trackId, query)
	table := model.SalesDiscountAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.DiscountSales(ctx, condition)
	packagingForDiscountSales(ctx, query, resp)
	return resp, err
}

func packagingForDiscountSales(ctx context.Context, query *model.CommonRequest, resp *report.DiscountSalesResponse) {
	//helpers.DddEmptySales(query, resp)
	regionIds := make([]int64, 0, len(resp.Rows))

	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	discountIds := make([]int64, 0)
	for _, row := range resp.Rows {
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		regionIds = append(regionIds, row.RegionId)
		discountIds = append(discountIds, row.DiscountId)
	}
	branchMaps := GetBranchMapsByIds(ctx, query, branchIds)
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	discountMaps := GetDiscountMapsByIds(ctx, query, discountIds)
	// 组装code和name
	for i := range resp.Rows {
		branchId := resp.Rows[i].BranchId
		resp.Rows[i].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[i].BranchName = cast.ToString(branchMaps[branchId]["name"])

		regionId := resp.Rows[i].RegionId
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		regionAddress := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		resp.Rows[i].RegionCode = cast.ToString(regionCode)       // 门店code
		resp.Rows[i].RegionName = cast.ToString(regionName)       // 门店名称
		resp.Rows[i].RegionAddress = cast.ToString(regionAddress) // 所在城市
		resp.Rows[i].RegionAlias = cast.ToString(alias)           // 门店别名
		//TODO 兼容处理
		if resp.Rows[i].DiscountName == "" {
			discountId := resp.Rows[i].DiscountId
			resp.Rows[i].DiscountCode = cast.ToString(discountMaps[discountId]["code"]) // 折扣code
			resp.Rows[i].DiscountName = cast.ToString(discountMaps[discountId]["name"]) // 折扣名称
			resp.Rows[i].DiscountType = cast.ToString(discountMaps[discountId]["getCouponType"])
		}
	}
}
