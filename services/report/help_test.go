package report

import (
	"fmt"
	"testing"
)

func TestSameAndDiff(t *testing.T) {
	unite, diff := make([]int64, 0), make([]int64, 0)
	src := []int64{1}
	dest := []int64{0, 2, 4, 6, 7}
	if dest == nil || len(dest) == 0 {
		return
	}
	if src == nil || len(src) == 0 {
		diff = dest
		return
	}
	mdest, mall := make(map[int64]byte), make(map[int64]byte)
	// 源数组建立map
	for _, old := range src {
		mall[old] = 1
	}
	// 目标数组，存不进去，说明有相同元素，就存进交集
	for _, v := range dest {
		mdest[v] = 1 // 目标数组存进map
		l := len(mall)
		mall[v] = 1
		if l != len(mall) { // 长度变化，可以存
			l = len(mall)
		} else { // 长度不变，说明是相同元素，存交集
			unite = append(unite, v)
		}
	}
	// 遍历交集
	for _, v := range unite { // 在目标数组map中删除交集，剩下的就是
		delete(mdest, v)
	}
	for k := range mdest {
		diff = append(diff, k)
	}
	fmt.Println(unite)
	fmt.Println(diff)
	return
}
