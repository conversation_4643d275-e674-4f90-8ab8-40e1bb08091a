package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/translation"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductSalesDetail(ctx context.Context, query *model.CommonRequest) (*report.ProductSalesDetailResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesDetail] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.ProductSalesDetail(ctx, condition)
	PackagingForProductSalesDetail(ctx, query, resp)
	return resp, err
}

func PackagingForProductSalesDetail(ctx context.Context, query *model.CommonRequest, resp *report.ProductSalesDetailResponse) {
	regionIds := make([]int64, 0)
	branchRegionIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	storeTypes := make([]int64, 0, 0)
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	spuIds := make([]int64, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		branchRegionIds = append(branchRegionIds, row.BranchRegionId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		categoryIds = append(categoryIds, row.CategoryId)
		productIds = append(productIds, row.ProductId)
		channelIds = append(channelIds, row.ChannelId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	branchRegionMaps := GetRegionMapsByIdsSchema(ctx, "branch_region", query.Lan, branchRegionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)

	for _, m := range productMaps {
		if id, ok := m["product_id"]; ok {
			spuIds = append(spuIds, cast.ToInt64(id))
		}
	}
	spuIdMaps := GetSchemaProductMapsByIds(ctx, query, spuIds)
	for i := range resp.Rows {
		PackagingForProductSalesDetailRow(ctx, query, resp.Rows[i], regionMaps, branchRegionMaps, categoryMaps, productMaps, channelMaps, storeTypeMaps, spuIdMaps)
		resp.Rows[i].RowIdx = int32(query.Offset + i + 1)
	}

}

func PackagingForProductSalesDetailRow(ctx context.Context, query *model.CommonRequest, row *report.ProductSalesDetail,
	regionMaps map[int64]map[string]interface{}, branchRegionMaps map[int64]map[string]interface{}, categoryMaps map[int64]map[string]interface{},
	productMaps map[int64]map[string]interface{}, channelMaps map[int64]map[string]interface{}, storeTypeMaps map[int64]map[string]interface{}, spuIdMaps map[int64]map[string]interface{},
) {
	row.ProductWeight = RoundFloat64(row.ProductWeight)
	row.Price = RoundFloat64(row.Price)
	row.ProductCount = RoundFloat64(row.ProductCount)
	row.BowlCount = RoundFloat64(row.BowlCount)
	row.GrossAmount = RoundFloat64(row.GrossAmount)
	row.DiscountAmount = RoundFloat64(row.DiscountAmount)
	row.NetAmount = RoundFloat64(row.NetAmount)
	row.ProductWeightReturned = RoundFloat64(row.ProductWeightReturned)

	row.StoreName = cast.ToString(regionMaps[row.RegionId]["name"])
	row.StoreCode = cast.ToString(regionMaps[row.RegionId]["code"])
	row.RegionAddress = cast.ToString(regionMaps[row.RegionId]["region_name"])
	row.RegionAddress1 = cast.ToString(regionMaps[row.RegionId]["region_name1"])
	row.RegionAddress2 = cast.ToString(regionMaps[row.RegionId]["region_name2"])
	row.RegionAddress3 = cast.ToString(regionMaps[row.RegionId]["region_name3"])
	row.BranchRegion = cast.ToString(branchRegionMaps[row.BranchRegionId]["name"])
	row.StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(row.StoreType)]["name"])
	row.CategoryCode = cast.ToString(categoryMaps[row.CategoryId]["code"])
	row.CategoryName = cast.ToString(categoryMaps[row.CategoryId]["name"])
	row.ProductCode = cast.ToString(productMaps[row.ProductId]["code"])
	row.ProductName = cast.ToString(productMaps[row.ProductId]["name"])
	row.SkuRemark = cast.ToString(productMaps[row.ProductId]["sale_name"])
	if row.SkuRemark == row.ProductName {
		row.SkuRemark = "-"
	}
	//    //商品名称辅助列
	if spuId, ok := productMaps[row.ProductId]["product_id"]; ok {
		if v, ok := spuIdMaps[cast.ToInt64(spuId)]["product_secondary_column"]; ok {
			row.ProductSecondaryColumn = cast.ToString(v)
		}
	}
	servingSize := cast.ToString(productMaps[row.ProductId]["serving_size"])
	if servingSize == "" {
		row.UnitInfo = row.Unit
	} else if row.Unit != "" {
		row.UnitInfo = fmt.Sprintf("%s %s", servingSize, row.Unit)
	} else {
		row.UnitInfo = "-"
	}
	row.Weight = fmt.Sprintf("%.2f %s", row.ProductWeight, row.Unit)
	row.WeightCountReturned = fmt.Sprintf("%.2f %s", row.ProductWeightReturned, row.Unit)
	row.ChannelName = cast.ToString(channelMaps[row.ChannelId]["name"])
	row.DiningMethod = translation.OrderType[query.Lan][row.DiningMethodCode]
	//row.OrderTypeName = GetOrderStatusName(row.OrderType)
	row.OrderTypeName = OrderStatus[query.Lan][row.OrderType]
	if row.EndTime == "0001-01-01 00:00:00" {
		row.EndTime = "-"
	} else {
		row.EndTime = utils.ConvertTimeToTimeZone(row.EndTime, query.Timezone)
	}
	if row.OpenTime == "0001-01-01 00:00:00" {
		row.OpenTime = "-"
	} else {
		row.OpenTime = utils.ConvertTimeToTimeZone(row.OpenTime, query.Timezone)
	}
	if row.OrderTime == "0001-01-01 00:00:00" {
		row.OrderTime = "-"
	} else {
		row.OrderTime = utils.ConvertTimeToTimeZone(row.OrderTime, query.Timezone)
	}
	row.Spicy = config.SpicyMap[row.SpicyCode]
	var discountRate float64
	if row.GrossAmount != 0 {
		discountRate = row.DiscountAmount / row.GrossAmount * 100
	}
	row.DiscountRate = fmt.Sprintf("%.1f%%", discountRate)
	row.GroupPurchaseChannel = strings.Replace(row.GroupPurchaseChannel, "券ISV", "", 1)
}
