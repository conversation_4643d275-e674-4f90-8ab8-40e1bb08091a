package report

import (
	"context"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func RefundAnalyse(ctx context.Context, query *model.CommonRequest) (*report.RefundAnalysisResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}

	// 退单原因分析接口
	resp, err := repo.DefaultSalesRepository.RefundAnalyse(ctx, condition)
	packagingForRefundAnalyse(ctx, query, resp)
	return resp, err
}

func packagingForRefundAnalyse(ctx context.Context, query *model.CommonRequest, resp *report.RefundAnalysisResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	channelIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	for _, r := range resp.Rows {
		regionIds = append(regionIds, r.RegionId)
		channelIds = append(channelIds, r.ChannelId)
		branchIds = append(branchIds, r.BranchId)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	channelMaps := GetChannelMapsByIds(ctx, query, channelIds)
	branchMao := GetBranchMapsByIds(ctx, query, branchIds)
	for r := range resp.Rows {
		// 门店的信息
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		resp.Rows[r].RegionCode = cast.ToString(code)
		resp.Rows[r].RegionName = cast.ToString(name)
		resp.Rows[r].ChannelCode = cast.ToString(channelMaps[resp.Rows[r].ChannelId]["code"])
		resp.Rows[r].ChannelName = cast.ToString(channelMaps[resp.Rows[r].ChannelId]["name"])
		resp.Rows[r].BranchCode = cast.ToString(branchMao[resp.Rows[r].BranchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMao[resp.Rows[r].BranchId]["name"])
		resp.Rows[r].PayAmountReturned = decimal.NewFromFloat(resp.Rows[r].PayAmountReturned).Round(2).InexactFloat64()
	}
}
