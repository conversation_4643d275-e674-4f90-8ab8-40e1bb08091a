package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func PosPromotion(ctx context.Context, query *model.CommonRequest) (*report.PosPromotionResponse, error) {
	query.RegionSearchType = "STORE"
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.PosPromotion] receive body. Content: `%+v`", trackId, query)
	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	// 数据校验
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 增加数据校验
	storeId := cast.ToInt64(ctx.Value(oauth.StoreID))
	condition.RegionSearchType = "STORE"
	condition.RegionSearchIds = []int64{storeId}
	condition.StoreIDs = condition.RegionSearchIds
	resp, err := repo.DefaultSalesRepository.PosPromotion(ctx, condition)
	return resp, err
}
