package report

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
	"time"
)

func QueryProductFlavorSales(ctx context.Context, query *model.CommonRequest) (*report.ProductFlavorSalesResponse, error) {
	start := time.Now()
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}

	resp, err := repo.DefaultSalesRepository.ProductFlavorSales(ctx, condition)
	PackagingForProductFlavorSales(ctx, query, resp)
	flavor := &Transfer{
		ctx:       ctx,
		tagTypes:  condition.TagTypes,
		resp:      resp,
		query:     query,
		condition: condition,
	}
	flavor.SetTransfer()
	flavor.Response()

	fmt.Printf("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	return resp, err
}

func PackagingForProductFlavorSales(ctx context.Context, query *model.CommonRequest, resp *report.ProductFlavorSalesResponse) {
	regionIds := make([]int64, 0, len(resp.Rows))
	productIds := make([]int64, 0)
	geoIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		productIds = append(productIds, row.ProductId)
		geoIds = append(geoIds, row.GeoId)
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))

		categoryIds = append(categoryIds, row.ProductCategoryId1)
		categoryIds = append(categoryIds, row.ProductCategoryId2)
		categoryIds = append(categoryIds, row.ProductCategoryId3)
		categoryIds = append(categoryIds, row.ProductCategoryId4)
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	geoMaps := GetGeoMapsByIds(ctx, query, geoIds)
	branchMaps := GetBranchMapsByIds(ctx, query, branchIds)
	companyMaps := GetCompanyMapsByIds(ctx, query, companyIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	categoryMaps := GetCategoryMapsByIds(ctx, query, helpers.RemoveDuplicateElement(categoryIds))
	// 组装code和name
	for r := range resp.Rows {
		regionId := resp.Rows[r].RegionId
		resp.Rows[r].RegionCode = cast.ToString(regionMaps[regionId]["code"])
		resp.Rows[r].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[r].RegionAddress = cast.ToString(regionMaps[regionId]["region_name"])
		resp.Rows[r].RegionAlias = cast.ToString(regionMaps[regionId]["alias"])
		productId := resp.Rows[r].ProductId
		resp.Rows[r].ProductCode = cast.ToString(productMaps[productId]["code"])
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[r].ProductName = pName
		geoId := resp.Rows[r].GeoId
		resp.Rows[r].GeoCode = cast.ToString(geoMaps[geoId]["code"])
		resp.Rows[r].GeoName = cast.ToString(geoMaps[geoId]["name"])
		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])
		companyId := resp.Rows[r].CompanyId
		resp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])

		categoryId1 := resp.Rows[r].ProductCategoryId1
		resp.Rows[r].ProductCategoryCode1 = cast.ToString(categoryMaps[categoryId1]["code"])
		resp.Rows[r].ProductCategoryName1 = cast.ToString(categoryMaps[categoryId1]["name"])

		categoryId2 := resp.Rows[r].ProductCategoryId2
		resp.Rows[r].ProductCategoryCode2 = cast.ToString(categoryMaps[categoryId2]["code"])
		resp.Rows[r].ProductCategoryName2 = cast.ToString(categoryMaps[categoryId2]["name"])

		categoryId3 := resp.Rows[r].ProductCategoryId3
		resp.Rows[r].ProductCategoryCode3 = cast.ToString(categoryMaps[categoryId3]["code"])
		resp.Rows[r].ProductCategoryName3 = cast.ToString(categoryMaps[categoryId3]["name"])

		categoryId4 := resp.Rows[r].ProductCategoryId4
		resp.Rows[r].ProductCategoryCode4 = cast.ToString(categoryMaps[categoryId4]["code"])
		resp.Rows[r].ProductCategoryName4 = cast.ToString(categoryMaps[categoryId4]["name"])

	}
}
