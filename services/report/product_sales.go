package report

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strings"
)

func QueryProductSales(ctx context.Context, query *model.CommonRequest) (*report.ProductSalesResponse, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSales] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepository.ProductSales(ctx, condition)
	PackagingForProductSales(ctx, query, resp)
	return resp, err
}

func PackagingForProductSales(ctx context.Context, query *model.CommonRequest, resp *report.ProductSalesResponse) {
	//helpers.DddEmptySales(query, resp)
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		regionIds = append(regionIds, row.RegionId)
		for _, r := range row.Data {
			categoryIds = append(categoryIds, r.Category0)
			for _, rr := range r.Data {
				categoryIds = append(categoryIds, rr.Category1)
				for _, rrr := range rr.Data {
					categoryIds = append(categoryIds, rrr.Category2)
					for _, i := range rrr.Data {
						productIds = append(productIds, i.ProductId)
					}
				}
			}
		}
	}
	regionMaps := GetRegionMapsByIds(ctx, query, regionIds)
	categoryMaps := GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := GetProductMapsByIds(ctx, query, productIds)
	storeTypeMaps := GetStoreTypeMapsByIds(ctx, query, storeTypes)
	// 组装code和name
	for r := range resp.Rows {
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		resp.Rows[r].RegionCode = cast.ToString(code)
		resp.Rows[r].RegionName = cast.ToString(name)
		resp.Rows[r].RegionAddress = cast.ToString(city)
		resp.Rows[r].RegionAlias = cast.ToString(alias)
		for i := range resp.Rows[r].Data {
			cat0 := resp.Rows[r].Data[i].Category0
			cat0Code := categoryMaps[cat0]["code"]
			cat0Name := categoryMaps[cat0]["name"]
			resp.Rows[r].Data[i].Category0Code = cast.ToString(cat0Code)
			resp.Rows[r].Data[i].Category0Name = cast.ToString(cat0Name)
			for j := range resp.Rows[r].Data[i].Data {
				cat1 := resp.Rows[r].Data[i].Data[j].Category1
				cat1Code := categoryMaps[cat1]["code"]
				cat1Name := categoryMaps[cat1]["name"]
				resp.Rows[r].Data[i].Data[j].Category1Code = cast.ToString(cat1Code)
				resp.Rows[r].Data[i].Data[j].Category1Name = cast.ToString(cat1Name)
				for k := range resp.Rows[r].Data[i].Data[j].Data {
					cat2 := resp.Rows[r].Data[i].Data[j].Data[k].Category2
					cat2Code := categoryMaps[cat2]["code"]
					cat2Name := categoryMaps[cat2]["name"]
					resp.Rows[r].Data[i].Data[j].Data[k].Category2Code = cast.ToString(cat2Code)
					resp.Rows[r].Data[i].Data[j].Data[k].Category2Name = cast.ToString(cat2Name)
					for m := range resp.Rows[r].Data[i].Data[j].Data[k].Data {
						pId := resp.Rows[r].Data[i].Data[j].Data[k].Data[m].ProductId
						pCode := productMaps[pId]["code"]
						name := cast.ToString(productMaps[pId]["name"])
						productName := cast.ToString(productMaps[pId]["product_name"])
						saleName := cast.ToString(productMaps[pId]["sale_name"])
						saleType := cast.ToString(productMaps[pId]["sale_type"])

						finalName, finalSaleName := createProductNames(name, productName, saleName, saleType)

						resp.Rows[r].Data[i].Data[j].Data[k].Data[m].ProductCode = cast.ToString(pCode)
						resp.Rows[r].Data[i].Data[j].Data[k].Data[m].ProductName = finalName
						resp.Rows[r].Data[i].Data[j].Data[k].Data[m].ProductSaleName = finalSaleName
					}
				}
			}
		}
	}
}

// name是spu的"商品名称"，boh那边如果修改商品名称会同步修改这个字段
// productName是spu的"渠道销售名称"+规格名称（单规格没有规格名称部分），旧数据这个字段有可能为空
// saleName: 单规格、套餐头、加料商品的saleName与productName一致，多规格商品是规格名称
func createProductNames(name, productName, saleName string, saleType string) (finalName string, finalSaleName string) {
	// 小料/套餐头
	if saleType == "ADDITION" || saleType == "SET" {
		if productName != "" {
			return productName, "-"
		}
		if saleName != "" {
			return saleName, "-"
		}
		return name, "-"
	}
	// 套餐子商品/单规格/多规格
	if productName != "" {
		rest := strings.TrimSuffix(productName, saleName)
		// 单规格
		if rest == "" {
			return saleName, "-"
		}
		// 多规格
		return rest, saleName
	} else {
		rest := strings.TrimSuffix(name, saleName)
		// 单规格
		if rest == "" {
			return saleName, "-"
		}
		// 多规格
		return rest, saleName
	}
}
