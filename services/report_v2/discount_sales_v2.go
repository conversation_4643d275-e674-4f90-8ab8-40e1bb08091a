package report_v2

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gitlab.hexcloud.cn/histore/sales-report/repo_v2"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strings"
)

func QueryDiscountSalesV2(ctx context.Context, query *model.CommonRequest) ([]map[string]interface{}, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesV2] receive body. Content: `%+v`", trackId, query)

	table := model.SalesDiscountAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID

	if err := report2.DataCheck(ctx, condition); err != nil {
		//logger.Pre().Errorf("data check failed for exporting discount_sales_report : %v", err)
		return nil, errors.New(fmt.Sprintf("data check failed: %v", err))
	}
	var storeIDs = make([]int64, 0)
	if condition.RegionSearchType == "STORE" {
		storeIDs = condition.RegionSearchIds
	}
	//增加数据校验
	domain := "hipos.report"
	schema := "STORE"
	userId := cast.ToInt64(ctx.Value("user_id"))
	storeIds, err := utils.DataScopeCheck(ctx, domain, partnerID, userId, schema, storeIDs, query.Lan)
	if err != nil {
		return nil, err
	}
	if condition.RegionSearchType == "STORE" {
		condition.RegionSearchIds = storeIds
	} else {
		condition.StoreIDs = storeIds
	}
	resp, err := repo.DefaultSalesRepositoryV2.DiscountSalesV2(ctx, condition)
	res := PackagingForDiscountSalesV2(ctx, query, resp)
	return res, err
}

func PackagingForDiscountSalesV2(ctx context.Context, query *model.CommonRequest, resp *report.DiscountSalesResponseV2) []map[string]interface{} {
	regionIds := make([]int64, 0, len(resp.Rows))
	geoIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	discountIds := make([]int64, 0)
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		geoIds = append(geoIds, row.GeoId)
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		discountIds = append(discountIds, row.DiscountId)
		categoryIds = append(categoryIds, row.CategoryId)
		productIds = append(productIds, row.ProductId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
	}
	regionMaps := report2.GetRegionMapsByIds(ctx, query, regionIds)
	geoMaps := report2.GetGeoMapsByIds(ctx, query, geoIds)
	branchMaps := report2.GetBranchMapsByIds(ctx, query, branchIds)
	companyMaps := report2.GetCompanyMapsByIds(ctx, query, companyIds)
	discountMaps := report2.GetDiscountMapsByIds(ctx, query, discountIds)
	categoryMaps := report2.GetCategoryMapsByIds(ctx, query, categoryIds)
	productMaps := report2.GetProductMapsByIds(ctx, query, productIds)
	storeTypeMaps := report2.GetStoreTypeMapsByIds(ctx, query, storeTypes)
	res := make([]map[string]interface{}, 0, len(resp.Rows))
	// 组装code和name
	for r := range resp.Rows {
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		status := regionMaps[regionId]["region_status"]
		resp.Rows[r].RegionCode = cast.ToString(code)
		resp.Rows[r].RegionName = cast.ToString(regionMaps[regionId]["name"])
		resp.Rows[r].RegionAddress = cast.ToString(city)
		resp.Rows[r].RegionAlias = cast.ToString(alias)
		resp.Rows[r].RegionStatus = cast.ToString(status)
		geoId := resp.Rows[r].GeoId
		resp.Rows[r].GeoCode = cast.ToString(geoMaps[geoId]["code"])
		resp.Rows[r].GeoName = cast.ToString(geoMaps[geoId]["name"])
		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])
		companyId := resp.Rows[r].CompanyId
		resp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		discountId := resp.Rows[r].DiscountId
		resp.Rows[r].DiscountCode = cast.ToString(discountMaps[discountId]["code"])
		resp.Rows[r].DiscountName = cast.ToString(discountMaps[discountId]["name"])
		getCouponType := cast.ToString(discountMaps[discountId]["getCouponType"])
		if getCouponType == "free" {
			resp.Rows[r].DiscountType = "无偿"
		} else if getCouponType == "paid" {
			resp.Rows[r].DiscountType = "有偿"
		}
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])

		categoryId := resp.Rows[r].CategoryId
		resp.Rows[r].CategoryCode = cast.ToString(categoryMaps[categoryId]["code"])
		resp.Rows[r].CategoryName = cast.ToString(categoryMaps[categoryId]["name"])
		pId := resp.Rows[r].ProductId
		resp.Rows[r].ProductCode = cast.ToString(productMaps[pId]["code"])
		name := cast.ToString(productMaps[pId]["name"])
		saleName := cast.ToString(productMaps[pId]["sale_name"]) // // sale_name是商品销售名称
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[r].ProductName = pName
		// 将查询结果转成map
		res = append(res, resp.Rows[r].ToMap())
	}
	return res
}
