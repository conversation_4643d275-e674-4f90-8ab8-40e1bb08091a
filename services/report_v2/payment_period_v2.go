package report_v2

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo_v2"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func QueryPaymentPeriodV2(ctx context.Context, query *model.CommonRequest) ([]map[string]interface{}, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesV2] receive body. Content: `%+v`", trackId, query)
	table := model.SalesPaymentAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	// 数据校验
	if err := report2.DataCheck(ctx, condition); err != nil {
		return nil, errors.New(fmt.Sprintf("data check failed: %v", err))
	}
	resp, err := repo.DefaultSalesRepositoryV2.PaymentPeriodV2(ctx, condition)
	res := PackagingForPaymentPeriodV2(ctx, query, resp)
	return res, err
}

func PackagingForPaymentPeriodV2(ctx context.Context, query *model.CommonRequest, resp *report.PaymentPeriodResponseV2) []map[string]interface{} {
	regionIds := make([]int64, 0, len(resp.Rows))
	geoIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	paymentIds := make([]int64, 0)
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		geoIds = append(geoIds, row.GeoId)
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		paymentIds = append(paymentIds, row.PaymentId)
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
	}
	regionMaps := report2.GetRegionMapsByIds(ctx, query, regionIds)
	geoMaps := report2.GetGeoMapsByIds(ctx, query, geoIds)
	branchMaps := report2.GetBranchMapsByIds(ctx, query, branchIds)
	companyMaps := report2.GetCompanyMapsByIds(ctx, query, companyIds)
	paymentMaps := report2.GetPaymentMapsByIds(ctx, query, paymentIds)
	storeTypeMaps := report2.GetStoreTypeMapsByIds(ctx, query, storeTypes)

	res := make([]map[string]interface{}, 0, len(resp.Rows))
	// 组装code和name
	for r := range resp.Rows {
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		status := regionMaps[regionId]["region_status"]
		resp.Rows[r].RegionCode = cast.ToString(code)
		resp.Rows[r].RegionName = cast.ToString(name)
		resp.Rows[r].RegionAddress = cast.ToString(city)
		resp.Rows[r].RegionAlias = cast.ToString(alias)
		resp.Rows[r].RegionStatus = cast.ToString(status)
		geoId := resp.Rows[r].GeoId
		resp.Rows[r].GeoCode = cast.ToString(geoMaps[geoId]["code"])
		resp.Rows[r].GeoName = cast.ToString(geoMaps[geoId]["name"])
		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])
		companyId := resp.Rows[r].CompanyId
		resp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"])
		//TODO 兼容处理
		if resp.Rows[r].PaymentName == "" {
			paymentId := resp.Rows[r].PaymentId
			resp.Rows[r].PaymentCode = cast.ToString(paymentMaps[paymentId]["code"])
			resp.Rows[r].PaymentName = cast.ToString(paymentMaps[paymentId]["name"])
			getCouponType := cast.ToString(paymentMaps[paymentId]["getCouponType"])
			if getCouponType == "free" {
				resp.Rows[r].PaymentType = "无偿"
			} else if getCouponType == "paid" {
				resp.Rows[r].PaymentType = "有偿"
			}
		}
		res = append(res, resp.Rows[r].ToMap())
	}
	return res
}
