package report_v2

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo_v2"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
	"strings"
)

func QueryProductChannelSalesV2(ctx context.Context, query *model.CommonRequest) ([]map[string]interface{}, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductChannelSalesV2] receive body. Content: `%+v`", trackId, query)

	table := model.SalesProductAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	if len(condition.PriceScope) == 0 && query.PriceScope != "" {
		return nil, errors.New("invalid price scope")
	}
	//增加数据校验
	if err := report2.DataCheck(ctx, condition); err != nil {
		return nil, errors.New(fmt.Sprintf("data check failed: %v", err))
	}
	resp, err := repo.DefaultSalesRepositoryV2.ProductChannelSalesV2(ctx, condition)
	res := PackagingForProductChannelSalesV2(ctx, query, resp)
	return res, err
}

func PackagingForProductChannelSalesV2(ctx context.Context, query *model.CommonRequest, resp *report.ProductChannelSalesResponseV2) []map[string]interface{} {
	regionIds := make([]int64, 0, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	categoryIds := make([]int64, 0)
	productIds := make([]int64, 0)
	geoIds := make([]int64, 0)
	branchIds := make([]int64, 0)
	companyIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	for _, row := range resp.Rows {
		storeTypes = append(storeTypes, cast.ToInt64(row.StoreType))
		regionIds = append(regionIds, row.RegionId)
		categoryIds = append(categoryIds, row.ProductCategoryId)
		categoryIds = append(categoryIds, row.ProductCategoryId1)
		categoryIds = append(categoryIds, row.ProductCategoryId2)
		categoryIds = append(categoryIds, row.ProductCategoryId3)
		categoryIds = append(categoryIds, row.ProductCategoryId4)
		productIds = append(productIds, row.ProductId)
		geoIds = append(geoIds, row.GeoId)
		branchIds = append(branchIds, row.BranchId)
		companyIds = append(companyIds, row.CompanyId)
		channelIds = append(channelIds, row.ChannelId)
	}
	regionMaps := report2.GetRegionMapsByIds(ctx, query, helpers.RemoveDuplicateElement(regionIds))
	categoryMaps := report2.GetCategoryMapsByIds(ctx, query, helpers.RemoveDuplicateElement(categoryIds))
	productMaps := report2.GetProductMapsByIds(ctx, query, helpers.RemoveDuplicateElement(productIds))
	geoMaps := report2.GetGeoMapsByIds(ctx, query, helpers.RemoveDuplicateElement(geoIds))
	branchMaps := report2.GetBranchMapsByIds(ctx, query, helpers.RemoveDuplicateElement(branchIds))
	companyMaps := report2.GetCompanyMapsByIds(ctx, query, helpers.RemoveDuplicateElement(companyIds))
	channelMaps := report2.GetChannelMapsByIds(ctx, query, helpers.RemoveDuplicateElement(channelIds))
	storeTypeMaps := report2.GetStoreTypeMapsByIds(ctx, query, helpers.RemoveDuplicateElement(storeTypes))
	res := make([]map[string]interface{}, 0, len(resp.Rows))
	// 组装code和name
	for r := range resp.Rows {
		resp.Rows[r].StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(resp.Rows[r].StoreType)]["name"]) // 门店经营类型
		regionId := resp.Rows[r].RegionId
		regionCode := regionMaps[regionId]["code"]
		regionName := regionMaps[regionId]["name"]
		city := regionMaps[regionId]["region_name"]
		alias := regionMaps[regionId]["alias"]
		resp.Rows[r].RegionCode = cast.ToString(regionCode)
		resp.Rows[r].RegionName = cast.ToString(regionName)
		resp.Rows[r].RegionAddress = cast.ToString(city)
		resp.Rows[r].RegionAlias = cast.ToString(alias)
		productId := resp.Rows[r].ProductId
		resp.Rows[r].ProductCode = cast.ToString(productMaps[productId]["code"])
		name := cast.ToString(productMaps[productId]["name"])
		saleName := cast.ToString(productMaps[productId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		resp.Rows[r].ProductName = pName

		categoryId := resp.Rows[r].ProductCategoryId
		resp.Rows[r].ProductCategoryCode = cast.ToString(categoryMaps[categoryId]["code"])
		resp.Rows[r].ProductCategoryName = cast.ToString(categoryMaps[categoryId]["name"])

		categoryId1 := resp.Rows[r].ProductCategoryId1
		resp.Rows[r].ProductCategoryCode1 = cast.ToString(categoryMaps[categoryId1]["code"])
		resp.Rows[r].ProductCategoryName1 = cast.ToString(categoryMaps[categoryId1]["name"])

		categoryId2 := resp.Rows[r].ProductCategoryId2
		resp.Rows[r].ProductCategoryCode2 = cast.ToString(categoryMaps[categoryId2]["code"])
		resp.Rows[r].ProductCategoryName2 = cast.ToString(categoryMaps[categoryId2]["name"])

		categoryId3 := resp.Rows[r].ProductCategoryId3
		resp.Rows[r].ProductCategoryCode3 = cast.ToString(categoryMaps[categoryId3]["code"])
		resp.Rows[r].ProductCategoryName3 = cast.ToString(categoryMaps[categoryId3]["name"])

		categoryId4 := resp.Rows[r].ProductCategoryId4
		resp.Rows[r].ProductCategoryCode4 = cast.ToString(categoryMaps[categoryId4]["code"])
		resp.Rows[r].ProductCategoryName4 = cast.ToString(categoryMaps[categoryId4]["name"])

		geoId := resp.Rows[r].GeoId
		resp.Rows[r].GeoCode = cast.ToString(geoMaps[geoId]["code"])
		resp.Rows[r].GeoName = cast.ToString(geoMaps[geoId]["name"])
		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])
		companyId := resp.Rows[r].CompanyId
		resp.Rows[r].CompanyCode = cast.ToString(companyMaps[companyId]["code"])
		resp.Rows[r].CompanyName = cast.ToString(companyMaps[companyId]["name"])
		channelId := resp.Rows[r].ChannelId
		resp.Rows[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.Rows[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		switch resp.Rows[r].OrderType {
		case "DINEIN":
			resp.Rows[r].OrderType = "堂食"
		case "TAKEAWAY":
			resp.Rows[r].OrderType = "外带"
		case "TAKEOUT":
			resp.Rows[r].OrderType = "外卖"
		case "SELFHELP":
			resp.Rows[r].OrderType = "自提"
		default:
			resp.Rows[r].OrderType = "无需此信息"
		}
		// 将查询结果转成map
		res = append(res, resp.Rows[r].ToMap())
	}
	return res
}
