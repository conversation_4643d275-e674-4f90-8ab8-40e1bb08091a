package report_v2

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	report "gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	report2 "gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func QueryRefundAnalysisV2(ctx context.Context, query *model.CommonRequest) ([]map[string]interface{}, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Debugf("[%d] [services.report.QueryProductSalesV2] receive body. Content: `%+v`", trackId, query)

	table := model.SalesTicketAmount{}
	condition := query.ToRepoCondition(table.SortFields())
	partnerID := cast.ToInt64(ctx.Value("partner_id"))
	condition.PartnerId = partnerID
	//增加数据校验
	if err := report2.DataCheck(ctx, condition); err != nil {
		return nil, errors.New(fmt.Sprintf("data check failed: %v", err))
	}
	resp, err := repo.DefaultSalesRepository.RefundAnalyse(ctx, condition)
	res := PackagingForRefundAnalysisV2(ctx, query, resp)
	return res, err
}

func PackagingForRefundAnalysisV2(ctx context.Context, query *model.CommonRequest, resp *report.RefundAnalysisResponse) []map[string]interface{} {
	regionIds := make([]int64, 0, len(resp.Rows))
	branchIds := make([]int64, 0)
	channelIds := make([]int64, 0)
	refundCodes := make([]string, 0)
	for _, row := range resp.Rows {
		regionIds = append(regionIds, row.RegionId)
		branchIds = append(branchIds, row.BranchId)
		channelIds = append(channelIds, row.ChannelId)
		refundCodes = append(refundCodes, row.RefundCode)
	}
	//code2Reason, _ := report2.GetRefundReasonByCodes(ctx) // 退单原因和code对应表
	regionMaps := report2.GetRegionMapsByIds(ctx, query, regionIds)
	branchMaps := report2.GetBranchMapsByIds(ctx, query, branchIds)
	channelMaps := report2.GetChannelMapsByIds(ctx, query, channelIds)
	res := make([]map[string]interface{}, 0, len(resp.Rows))
	// 组装code和name
	for r := range resp.Rows {
		regionId := resp.Rows[r].RegionId
		code := regionMaps[regionId]["code"]
		name := regionMaps[regionId]["name"]
		resp.Rows[r].RegionCode = cast.ToString(code)
		resp.Rows[r].RegionName = cast.ToString(name)

		branchId := resp.Rows[r].BranchId
		resp.Rows[r].BranchCode = cast.ToString(branchMaps[branchId]["code"])
		resp.Rows[r].BranchName = cast.ToString(branchMaps[branchId]["name"])

		channelId := resp.Rows[r].ChannelId
		resp.Rows[r].ChannelCode = cast.ToString(channelMaps[channelId]["code"])
		resp.Rows[r].ChannelName = cast.ToString(channelMaps[channelId]["name"])
		//resp.Rows[r].RefundReason = code2Reason[resp.Rows[r].RefundCode]
		resp.Rows[r].PayAmountReturned = decimal.NewFromFloat(resp.Rows[r].PayAmountReturned).Round(2).InexactFloat64()
		bt, err := json.Marshal(resp.Rows[r])
		if err != nil {
			logger.Pre().WithField("func", "PackagingForRefundAnalysisV2").Errorf("json marshal error: %v", err)
			return nil
		}
		m := make(map[string]interface{})
		if err := json.Unmarshal(bt, &m); err != nil {
			logger.Pre().WithField("func", "PackagingForRefundAnalysisV2").Errorf("json unmarshal error: %v", err)
			return nil
		}
		// 将查询结果转成map
		res = append(res, m)
	}
	return res
}
