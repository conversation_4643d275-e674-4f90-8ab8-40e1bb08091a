package open

import (
	"context"
	"errors"
	"strings"
)

func Sync(ctx context.Context, start, end string, storeId int64) error {
	errs := make([]string, 0)
	err := SyncStoreReport(ctx, start, end, storeId)
	if err != nil {
		errs = append(errs, err.<PERSON>rror())
	}
	err = SyncProductReport(ctx, start, end, storeId)
	if err != nil {
		errs = append(errs, err.<PERSON>rror())
	}
	if len(errs) > 0 {
		return errors.New(strings.Join(errs, ";\n"))
	}
	return nil
}
