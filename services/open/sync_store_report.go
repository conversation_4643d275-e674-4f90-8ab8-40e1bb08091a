package open

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/db"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"gorm.io/gorm"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"math"
	"strings"
	"time"
)

func SyncStoreReport(ctx context.Context, start string, end string, storeId int64) error {
	req := &open.SyncStoreReportReq{
		PartnerId: cast.ToInt64(ctx.Value("partner_id")),
		StartDate: start,
		EndDate:   end,
		StoreId:   storeId,
	}
	resp, err := repo.DefaultOpenRepository.SyncStoreReport(ctx, req)
	if err != nil {
		return err
	}

	if len(resp.Rows) == 0 {
		logger.Pre().Warningf("SyncStoreReport_rows_empty req: %+v \n", req)
		return nil
	}

	rows, err := transformSyncStoreDataRows(ctx, resp.Rows)
	if err != nil {
		return err
	}

	var (
		dbObj *gorm.DB
		sqlDb *sql.DB
	)
	for i := 0; i < 3; i++ {
		dbObj, sqlDb, err = db.CreateMysqlDb(&config.DefaultConfig.DB.Linlee)
		if err == nil {
			break
		}
		logger.Pre().Errorf("SyncStoreReportError CreateMysqlDb req: %+v error: %v \n", req, err)
	}
	if err != nil {
		return err
	}
	defer sqlDb.Close()
	return dbObj.Transaction(func(tx *gorm.DB) error {
		// delete
		sql := fmt.Sprintf("delete from store_channel_report where bus_date >= '%s' and bus_date <= '%s'",
			start, end)
		if storeId != 0 {
			sql += fmt.Sprintf(" and store_id = %d", storeId)
		}
		if err := tx.Debug().Exec(sql).Error; err != nil {
			return err
		}
		sql = buildSyncStoreInsertSql(rows)
		return tx.Exec(sql).Error
	})
}

func transformSyncStoreDataRows(ctx context.Context, rows []*open.SyncStoreReportRow) (dataRows []*open.SyncStoreReportData, err error) {
	channelIds := make([]int64, 0)
	storeIds := make([]int64, 0)
	for _, item := range rows {
		channelIds = append(channelIds, item.ChannelId)
		storeIds = append(storeIds, item.StoreId)
	}
	query := &model.CommonRequest{}
	regionMaps := report.GetRegionMapsByIds(ctx, query, storeIds)
	channelMaps := report.GetChannelMapsByIds(ctx, query, channelIds)
	ids := uuid.GetIds(len(rows))
	now := time.Now()
	for i, item := range rows {
		r := &open.SyncStoreReportData{
			Id:               ids[i],
			BusDate:          item.BusDate,
			StoreId:          item.StoreId,
			StoreCode:        cast.ToString(regionMaps[item.StoreId]["code"]),
			StoreName:        cast.ToString(regionMaps[item.StoreId]["name"]),
			ChannelId:        item.ChannelId,
			OrderChannel:     cast.ToString(channelMaps[item.ChannelId]["name"]),
			BusinessAmount:   math.Round(item.BusinessAmount*100) / 100,
			DiscountAmount:   math.Round(item.DiscountAmount*100) / 100,
			ValidTicketCount: item.ValidTicketCount,
			Currency:         item.Currency,
			CreatedAt:        now,
			UpdatedAt:        now,
		}
		r.RealAmount = math.Round((r.BusinessAmount-r.DiscountAmount)*100) / 100
		dataRows = append(dataRows, r)
	}
	return
}

const syncStoreInsertSqlTpl = `
insert into store_channel_report 
(id,bus_date,store_id,store_code,store_name,channel_id,order_channel,business_amount,real_amount,discount_amount,valid_ticket_count,currency,created_at,updated_at)
values %s
`

func buildSyncStoreInsertSql(rows []*open.SyncStoreReportData) string {
	var arr = make([]string, 0)
	for _, item := range rows {
		arr = append(arr, fmt.Sprintf("(%d,'%s',%d,'%s','%s',%d,'%s',%f,%f,%f,%d,'%s','%s','%s')",
			item.Id, item.BusDate, item.StoreId,
			strings.ReplaceAll(item.StoreCode, "'", "''"),
			strings.ReplaceAll(item.StoreName, "'", "''"),
			item.ChannelId,
			strings.ReplaceAll(item.OrderChannel, "'", "''"),
			item.BusinessAmount, item.RealAmount, item.DiscountAmount, item.ValidTicketCount,
			strings.ReplaceAll(item.Currency, "'", "''"),
			item.CreatedAt.In(config.ShanghaiLocation).Format(config.DateTimeFormat),
			item.UpdatedAt.In(config.ShanghaiLocation).Format(config.DateTimeFormat)))
	}
	return fmt.Sprintf(syncStoreInsertSqlTpl, strings.Join(arr, ","))
}
