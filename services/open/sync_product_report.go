package open

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/db"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"gorm.io/gorm"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"math"
	"strings"
	"time"
)

func SyncProductReport(ctx context.Context, start string, end string, storeId int64) error {
	req := &open.SyncProductReportReq{
		PartnerId: cast.ToInt64(ctx.Value("partner_id")),
		StartDate: start,
		EndDate:   end,
		StoreId:   storeId,
	}
	resp, err := repo.DefaultOpenRepository.SyncProductReport(ctx, req)
	if err != nil {
		return err
	}

	if len(resp.Rows) == 0 {
		logger.Pre().Warningf("SyncProductReport_rows_empty req: %+v \n", req)
		return nil
	}

	rows, err := transformSyncProductDataRows(ctx, resp.Rows)
	if err != nil {
		return err
	}

	var (
		dbObj *gorm.DB
		sqlDb *sql.DB
	)
	for i := 0; i < 3; i++ {
		dbObj, sqlDb, err = db.CreateMysqlDb(&config.DefaultConfig.DB.Linlee)
		if err == nil {
			break
		}
		logger.Pre().Errorf("SyncProductReportError CreateMysqlDb req: %+v error: %v \n", req, err)
	}
	if err != nil {
		return err
	}
	defer sqlDb.Close()
	return dbObj.Transaction(func(tx *gorm.DB) error {
		// delete
		sql := fmt.Sprintf("delete from store_product_report where bus_date >= '%s' and bus_date <= '%s'",
			start, end)
		if storeId != 0 {
			sql += fmt.Sprintf(" and store_id = %d", storeId)
		}
		if err := tx.Debug().Exec(sql).Error; err != nil {
			return err
		}
		sql = buildSyncProductInsertSql(rows)
		return tx.Exec(sql).Error
	})
}

func transformSyncProductDataRows(ctx context.Context, rows []*open.SyncProductReportRow) (dataRows []*open.SyncProductReportData, err error) {
	channelIds := make([]int64, 0)
	storeIds := make([]int64, 0)
	productId := make([]int64, 0)
	for _, item := range rows {
		channelIds = append(channelIds, item.ChannelId)
		storeIds = append(storeIds, item.StoreId)
		productId = append(productId, item.ProductId)
	}
	query := &model.CommonRequest{}
	regionMaps := report.GetRegionMapsByIds(ctx, query, storeIds)
	channelMaps := report.GetChannelMapsByIds(ctx, query, channelIds)
	productMaps := report.GetProductMapsByIds(ctx, query, productId)
	ids := uuid.GetIds(len(rows))
	now := time.Now()
	for i, item := range rows {
		r := &open.SyncProductReportData{
			Id:           ids[i],
			BusDate:      item.BusDate,
			StoreId:      item.StoreId,
			StoreCode:    cast.ToString(regionMaps[item.StoreId]["code"]),
			StoreName:    cast.ToString(regionMaps[item.StoreId]["name"]),
			ChannelId:    item.ChannelId,
			OrderChannel: cast.ToString(channelMaps[item.ChannelId]["name"]),
			ProductId:    item.ProductId,
			ProductName:  "",
			GrossAmount:  math.Round(item.GrossAmount*100) / 100,
			RealAmount:   math.Round(item.RealAmount*100) / 100,
			Qty:          math.Round(item.Qty*100) / 100,
			Currency:     item.Currency,
			CreatedAt:    now,
			UpdatedAt:    now,
		}
		// 商品名字，与单品渠道表逻辑保持一致
		name := cast.ToString(productMaps[item.ProductId]["name"])
		saleName := cast.ToString(productMaps[item.ProductId]["sale_name"]) // sale_name是商品销售name
		pName := ""
		if name == saleName {
			pName = saleName
		} else {
			if strings.Contains(saleName, name) {
				pName = saleName
			} else {
				pName = name + saleName
			}
		}
		r.ProductName = pName
		dataRows = append(dataRows, r)
	}
	return
}

const syncProductInsertSqlTpl = `
insert into store_product_report 
(id,bus_date,store_id,store_code,store_name,channel_id,order_channel,product_id,product_name,gross_amount,real_amount,qty,currency,created_at,updated_at)
values %s
`

func buildSyncProductInsertSql(rows []*open.SyncProductReportData) string {
	var arr = make([]string, 0)
	for _, item := range rows {
		arr = append(arr, fmt.Sprintf("(%d,'%s',%d,'%s','%s',%d,'%s',%d,'%s',%f,%f,%f,'%s','%s','%s')",
			item.Id, item.BusDate, item.StoreId,
			strings.ReplaceAll(item.StoreCode, "'", "''"),
			strings.ReplaceAll(item.StoreName, "'", "''"),
			item.ChannelId,
			strings.ReplaceAll(item.OrderChannel, "'", "''"),
			item.ProductId,
			strings.ReplaceAll(item.ProductName, "'", "''"),
			item.GrossAmount, item.RealAmount, item.Qty,
			strings.ReplaceAll(item.Currency, "'", "''"),
			item.CreatedAt.In(config.ShanghaiLocation).Format(config.DateTimeFormat),
			item.UpdatedAt.In(config.ShanghaiLocation).Format(config.DateTimeFormat)))
	}
	return fmt.Sprintf(syncProductInsertSqlTpl, strings.Join(arr, ","))
}
