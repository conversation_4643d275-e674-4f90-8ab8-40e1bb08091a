package open

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func QueryOpenProductReport(ctx context.Context, query *open.ProductReportReq) (*open.ProductReportResp, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Infof("[%d] [services.report.QueryOpenProductReport] receive body. Content: `%+v`", trackId, query)
	resp, err := repo.DefaultOpenRepository.OpenProductReport(ctx, query)
	packOpenProductReportResp(ctx, resp)
	return resp, err
}

func packOpenProductReportResp(ctx context.Context, resp *open.ProductReportResp) {
	regionIds := make([]int64, len(resp.Rows))
	productIds := make([]int64, len(resp.Rows))
	channelIds := make([]int64, len(resp.Rows))
	for _, r := range resp.Rows {
		regionIds = append(regionIds, r.StoreId)
		productIds = append(productIds, r.ProductId)
		channelIds = append(channelIds, r.ChannelId)
	}
	query := &model.CommonRequest{}
	regionMaps := report.GetRegionMapsByIds(ctx, query, regionIds)
	productMaps := report.GetProductMapsByIds(ctx, query, productIds)
	channelMaps := report.GetChannelMapsByIds(ctx, query, channelIds)
	for _, row := range resp.Rows {
		row.StoreName = cast.ToString(regionMaps[row.StoreId]["name"])
		row.StoreCode = cast.ToString(regionMaps[row.StoreId]["code"])
		row.ProductName = cast.ToString(productMaps[row.ProductId]["name"])
		row.ProductCode = cast.ToString(productMaps[row.ProductId]["code"])
		row.ChannelCode = cast.ToString(channelMaps[row.ChannelId]["code"])
		row.ChannelName = cast.ToString(channelMaps[row.ChannelId]["name"])
	}
}
