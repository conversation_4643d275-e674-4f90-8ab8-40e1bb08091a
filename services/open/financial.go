package open

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

func QueryOpenFinancialReport(ctx context.Context, query *open.FinancialReportReq) (*open.FinancialReportResp, error) {
	trackId := ctx.Value(config.TrackId)
	logger.Pre().Infof("[%d] [services.report.QueryOpenFinancialReport] receive body. Content: `%+v`", trackId, query)
	resp, err := repo.DefaultOpenRepository.OpenFinancialReport(ctx, query)
	packOpenFinancialReportResp(ctx, resp)
	return resp, err
}

func packOpenFinancialReportResp(ctx context.Context, resp *open.FinancialReportResp) {
	regionIds := make([]int64, len(resp.Rows))
	storeTypes := make([]int64, 0, len(resp.Rows))
	for _, r := range resp.Rows {
		regionIds = append(regionIds, r.StoreId)
		storeTypes = append(storeTypes, cast.ToInt64(r.StoreType))
	}
	query := &model.CommonRequest{}
	regionMaps := report.GetRegionMapsByIds(ctx, query, regionIds)
	storeTypeMaps := report.GetStoreTypeMapsByIds(ctx, query, storeTypes)
	for _, row := range resp.Rows {
		row.StoreName = cast.ToString(regionMaps[row.StoreId]["name"])
		row.StoreCode = cast.ToString(regionMaps[row.StoreId]["code"])
		row.StoreCity = cast.ToString(regionMaps[row.StoreId]["region_name"])
		row.StoreTypeName = cast.ToString(storeTypeMaps[cast.ToInt64(row.StoreType)]["name"])
	}
}

