package new_pos_report

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
)

// PosDepartmentBusinessReport pos部门营业报表
func (pr *NewPosRepoPG) PosDepartmentBusinessReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.DepartmentBusinessReport, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := generateQuerySQLPosDepartmentBusiness(trackId, start, end, storeId, partnerID)

	row := queryDBForPosDepartmentBusiness(trackId, pr.DB, rowSQL)
	if len(row) > 0 {
		return row[0], nil
	}

	return nil, nil
}

// 查询数据库，并将结果映射为map对象
func queryDBForPosDepartmentBusiness(trackId int64, db *sql.DB, sqlStr string) []*pos.DepartmentBusinessReport {
	results := make([]*pos.DepartmentBusinessReport, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(pos.DepartmentBusinessReport)
		var productList json.RawMessage
		if err := r.Scan(
			&f.Summary.Qty,
			&f.Summary.Amount,
			&productList,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(productList, &f.DepartmentList)
		if err != nil {
			logger.Pre().Error("pos销售报表解析失败")
			return results
		}
		results = append(results, f)
	}

	return results
}

func generateQuerySQLPosDepartmentBusiness(trackId int64, start string, end string, storeId int64, partnerID int64) string {
	var rowSQL string
	rowSQL = `
	-- pos部门营业报表
	WITH base_category AS (
						  SELECT pc.category                         AS category_id,
								 SUM(fact.qty)                       AS qty,
								 SUM((COALESCE(gross_amount2, 0) +
									  COALESCE(package_amount, 0) +
									  COALESCE(send_fee_for_merchant, 0) +
									  COALESCE(service_fee, 0) +
									  COALESCE(tip, 0)) -
									 (COALESCE(pay_merchant_allowance, 0) +
									  COALESCE(promotion_merchant_allowance, 0) +
									  COALESCE(other_fee, 0) +
									  COALESCE(merchant_send_fee, 0) +
									  COALESCE(commission, 0) +
									  COALESCE(rounding, 0) -
									  COALESCE(overflow_amount, 0))) AS amount
						  FROM sales_product_amounts fact
								   LEFT JOIN product_caches pc ON fact.product_id = pc.id
						  WHERE {WHERE}
							AND fact.combo_type IN (0, 1)
						  GROUP BY pc.category
						  ),
		 category_stats AS (
						  SELECT qty,
								 amount,
								 JSON_BUILD_OBJECT(
										 'id', category_id,
										 'qty', COALESCE(qty, 0),
										 'amount', COALESCE(amount, 0),
										 'percentage', COALESCE(amount / NULLIF(SUM(amount) OVER (), 0), 0)
								 ) AS category_business
						  FROM base_category
						  )
	SELECT COALESCE(SUM(qty), 0)                             AS qty,
		   COALESCE(SUM(amount), 0)                          AS amount,
		   COALESCE(JSON_AGG(category_business), '[]'::JSON) AS category_business_list
	FROM category_stats;
`

	whereSQL := generateWhereSQLForPosDepartmentBusinessReport(start, end, storeId, partnerID)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func generateWhereSQLForPosDepartmentBusinessReport(start string, end string, storeId int64, partnerID int64) string {
	storeIdsSQL := fmt.Sprintf(`
				AND fact.store_id = %d
			`, storeId)

	dateSQL := "" // 日期
	if start == end {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = DATE '%s'
		`, start)
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= DATE '%s'
			AND fact.bus_date <= DATE '%s'
		`, start, end)
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		%s
		%s
	`, partnerID, storeIdsSQL, dateSQL)
	return whereSQL
}
