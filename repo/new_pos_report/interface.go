package new_pos_report

import (
	"context"

	"gitlab.hexcloud.cn/histore/sales-report/model/pos"
)

type NewPosRepo interface {

	// PosBusinessReport pos营业报表
	PosBusinessReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.BusinessReport, error)

	PosOrderTypeReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.OrderTypeResponse, error)

	PosStorePeriodReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.StorePeriodReportResponse, error)

	PosDepartmentBusinessReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.DepartmentBusinessReport, error)

	PosProductSalesReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) ([]*pos.ProductSalesReport, error)
}
