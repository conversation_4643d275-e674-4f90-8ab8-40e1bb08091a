package new_pos_report

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
)

// PosOrderTypeReport pos就餐方式报表
func (pr *NewPosRepoPG) PosOrderTypeReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.OrderTypeResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLPosOrderTypeReport(trackId, start, end, storeId, partnerID)
	ch1 := make(chan []*pos.OrderTypePeriodReport, 1)
	ch2 := make(chan []*pos.OrderTypePeriodReport, 1)
	go queryDBWithChanForPosOrderTypeReport(trackId, pr.DB, rowSQL, ch1)
	go queryDBWithChanForPosOrderTypeReport(trackId, pr.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *pos.OrderTypePeriodReport
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &pos.OrderTypeResponse{
		Total:  summary,
		Period: rows,
	}
	return response, nil
}

func queryDBWithChanForPosOrderTypeReport(trackId int64, db *sql.DB, sqlStr string, ch chan []*pos.OrderTypePeriodReport) {
	ch <- queryDBForPosOrderTypeReport(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForPosOrderTypeReport(trackId int64, db *sql.DB, sqlStr string) []*pos.OrderTypePeriodReport {
	results := make([]*pos.OrderTypePeriodReport, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(pos.OrderTypePeriodReport)
		var typePeriodList json.RawMessage
		if err := r.Scan(
			&f.MealName,
			&f.BowlNum,
			&f.AverageBowlNum,
			&f.Amount,
			&typePeriodList,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(typePeriodList, &f.TypePeriodList)
		if err != nil {
			logger.Pre().Error("pos用餐方式报表解析失败")
			return results
		}
		results = append(results, f)
	}

	return results
}

func generateQuerySQLPosOrderTypeReport(trackId int64, start string, end string, storeId int64, partnerID int64) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	rowSQL = `
	--用餐方式报表 rows
	WITH base_tickets AS (
						 SELECT fact.meal_segment_name,
								fact.order_type,
								SUM(fact.amount_0 + fact.surcharge_amount
									- (fact.discount_merchant_contribute
										   + fact.merchant_send_fee
										   + fact.other_fee
										   + fact.pay_merchant_contribute
										   - fact.overflow_amount
										+ fact.rounding)) AS real_amount,
								SUM(fact.bowl_num)        AS bowl_num
						 FROM sales_ticket_amounts fact
						 WHERE {WHERE}
						 GROUP BY fact.meal_segment_name, fact.order_type
						 ),
		 order_type_stats AS (
						 SELECT meal_segment_name,
								SUM(bowl_num)    AS bowl_num,
								SUM(real_amount) AS real_amount,
								JSON_BUILD_OBJECT(
										'value', COALESCE(order_type, ''),
										'bowlNum', COALESCE(SUM(bowl_num), 0),
										'amount', COALESCE(SUM(real_amount), 0),
										'averageBowlNum', COALESCE(SUM(real_amount) / NULLIF(SUM(bowl_num), 0), 0),
										'revenue_share', COALESCE(SUM(real_amount) / NULLIF(
												SUM(SUM(real_amount)) OVER (PARTITION BY meal_segment_name ), 0), 0)
								)                AS type_period
						 FROM base_tickets
						 GROUP BY meal_segment_name, order_type
						 )
	SELECT COALESCE(meal_segment_name, '')                          AS meal_segment_name,
		   COALESCE(SUM(bowl_num), 0)                               AS bowl_num,
		   COALESCE(SUM(real_amount) / NULLIF(SUM(bowl_num), 0), 0) AS real_amount_per_bowl,
		   COALESCE(SUM(real_amount), 0)                            AS real_amount,
		   COALESCE(JSON_AGG(type_period), '[]'::JSON)              AS type_period_list
	FROM order_type_stats
	GROUP BY meal_segment_name;
`
	summarySQL = `
		--用餐方式报表 total
		WITH base_tickets AS (
							 SELECT fact.order_type,
									SUM(fact.amount_0 + fact.surcharge_amount
										- (fact.discount_merchant_contribute
											   + fact.merchant_send_fee
											   + fact.other_fee
											   + fact.pay_merchant_contribute
											   - fact.overflow_amount
											+ fact.rounding)) AS real_amount,
									SUM(fact.bowl_num)        AS bowl_num
							 FROM sales_ticket_amounts fact
							 WHERE {WHERE}
							 GROUP BY fact.order_type
							 ),
			 order_type_stats AS (
							 SELECT bowl_num,
									real_amount,
									JSON_BUILD_OBJECT(
											'value', COALESCE(order_type, ''),
											'bowlNum', COALESCE(bowl_num, 0),
											'amount', COALESCE(real_amount, 0),
											'averageBowlNum', COALESCE(real_amount / NULLIF(bowl_num, 0), 0),
											'revenue_share', COALESCE(real_amount / NULLIF(SUM(real_amount) OVER (), 0), 0)
									) AS type_period
							 FROM base_tickets
							 )
		SELECT ''                                                       AS meal_segment_name,
			   COALESCE(SUM(bowl_num), 0)                               AS bowl_num,
			   COALESCE(SUM(real_amount) / NULLIF(SUM(bowl_num), 0), 0) AS real_amount_per_bowl,
			   COALESCE(SUM(real_amount), 0)                            AS real_amount,
			   COALESCE(JSON_AGG(type_period), '[]'::JSON)              AS type_period_list
		FROM order_type_stats;
`

	whereSQL := generateWhereSQLForPosOrderTypeReport(start, end, storeId, partnerID)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForPosOrderTypeReport(start string, end string, storeId int64, partnerID int64) string {
	storeIdsSQL := fmt.Sprintf(`
				AND fact.store_id = %d
			`, storeId)

	dateSQL := "" // 日期
	if start == end {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = DATE '%s'
		`, start)
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= DATE '%s'
			AND fact.bus_date <= DATE '%s'
		`, start, end)
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		%s
		%s
	`, partnerID, storeIdsSQL, dateSQL)
	return whereSQL
}
