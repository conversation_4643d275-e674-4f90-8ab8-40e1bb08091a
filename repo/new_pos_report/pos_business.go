package new_pos_report

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
)

// PosBusinessReport pos营业报表
func (pr *NewPosRepoPG) PosBusinessReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) (*pos.BusinessReport, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForPosBusinessReport(trackId, start, end, storeId, partnerID)
	ch1 := make(chan []*pos.BusinessChannel, 1)
	ch2 := make(chan []*pos.BusinessChannel, 1)
	go queryDBWithChanForPosBusinessReport(trackId, pr.DB, rowSQL, ch1)
	go queryDBWithChanForPosBusinessReport(trackId, pr.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *pos.BusinessChannel
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &pos.BusinessReport{
		Sum:     summary,
		Channel: rows,
	}
	return response, nil
}

func queryDBWithChanForPosBusinessReport(trackId int64, db *sql.DB, sqlStr string, ch chan []*pos.BusinessChannel) {
	ch <- queryDBForPosBusinessReport(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForPosBusinessReport(trackId int64, db *sql.DB, sqlStr string) []*pos.BusinessChannel {
	results := make([]*pos.BusinessChannel, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(pos.BusinessChannel)
		if err := r.Scan(
			&f.TpName,
			&f.BusinessAmount,
			&f.ExpendAmount,
			&f.RealAmount,
			&f.Count,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForPosBusinessReport(trackId int64, start string, end string, storeId int64, partnerID int64) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	rowSQL = `
		--pos营业报表 ROWS
		WITH base_tickets AS (SELECT fact.channel_id,
                             fact.channel_name,                                             --渠道
                             SUM(fact.amount_0 + fact.surcharge_amount) AS business_amount, --流水金额
                             SUM(fact.eticket_count)                    AS order_count,     --账单数
                             SUM(fact.discount_merchant_contribute + fact.merchant_send_fee + fact.other_fee +
                                 fact.pay_merchant_contribute -
                                 fact.overflow_amount)                  AS discount_amount, --优惠金额
                             SUM(fact.amount_0 + fact.surcharge_amount -
                                 (fact.discount_merchant_contribute + fact.merchant_send_fee + fact.other_fee +
                                  fact.pay_merchant_contribute + fact.rounding -
                                  fact.overflow_amount))                AS real_amount      --实收金额
                      FROM sales_ticket_amounts fact
                               LEFT JOIN store_caches store_caches
                                         ON fact.store_id = store_caches.id
                      WHERE {WHERE}
                        AND fact.is_non_sales = FALSE
                      GROUP BY fact.channel_id, fact.channel_name),
     base_payments_non_sales AS (SELECT CAST(fact.channel_id AS bigint), --渠道
                                        SUM(fact.merchant_allowance - fact.overflow_amount) AS discount_contribute
                                 FROM sales_payment_amounts fact
                                          LEFT JOIN store_caches store_caches
                                                    ON fact.store_id = store_caches.id
                                 WHERE {WHERE}
                                   AND is_non_sales = TRUE
                                 GROUP BY fact.channel_id)
	SELECT bt.channel_name                                                           AS channel_name,
		   COALESCE(bt.business_amount - COALESCE(bpns.discount_contribute, 0), 0) AS business_amount,  --流水金额
		   COALESCE(bt.discount_amount - COALESCE(bpns.discount_contribute, 0), 0) AS discount_amount,  --优惠金额
		   COALESCE(bt.real_amount, 0)                                             AS real_amount,--实收金额
		   COALESCE(bt.order_count, 0)                                             AS valid_order_count --账单数
	FROM base_tickets bt
			 LEFT JOIN base_payments_non_sales bpns ON bt.channel_id = bpns.channel_id;
`
	summarySQL = `
		--pos营业报表 Summary
		WITH base_tickets AS (SELECT SUM(fact.amount_0 + fact.surcharge_amount) AS business_amount, --流水金额
									 SUM(fact.eticket_count)                    AS order_count,     --账单数
									 SUM(fact.discount_merchant_contribute + fact.merchant_send_fee + fact.other_fee +
										 fact.pay_merchant_contribute -
										 fact.overflow_amount)                  AS discount_amount, --优惠金额
									 SUM(fact.amount_0 + fact.surcharge_amount -
										 (fact.discount_merchant_contribute + fact.merchant_send_fee + fact.other_fee +
										  fact.pay_merchant_contribute + fact.rounding -
										  fact.overflow_amount))                AS real_amount      --实收金额
							  FROM sales_ticket_amounts fact
									   LEFT JOIN store_caches store_caches
												 ON fact.store_id = store_caches.id
							  WHERE {WHERE}
								AND fact.is_non_sales = FALSE),
			 base_payments_non_sales AS (SELECT SUM(fact.merchant_allowance - fact.overflow_amount) AS discount_contribute
										 FROM sales_payment_amounts fact
												  LEFT JOIN store_caches store_caches
															ON fact.store_id = store_caches.id
										 WHERE {WHERE}
										   AND is_non_sales = TRUE)
		SELECT ''                                                          				AS channel_name,
               COALESCE(bt.business_amount - COALESCE(bpns.discount_contribute, 0), 0) AS business_amount,  --流水金额
			   COALESCE(bt.discount_amount - COALESCE(bpns.discount_contribute, 0), 0) AS discount_amount,  --优惠金额
			   COALESCE(bt.real_amount, 0)                                             AS real_amount,--实收金额
			   COALESCE(bt.order_count, 0)                                             AS valid_order_count --账单数
		FROM base_tickets bt,
			 base_payments_non_sales bpns;
`

	whereSQL := generateWhereSQLForPosBusinessReport(start, end, storeId, partnerID)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForPosBusinessReport(start string, end string, storeId int64, partnerID int64) string {
	storeIdsSQL := fmt.Sprintf(`
				AND fact.store_id = %d
			`, storeId)

	dateSQL := "" // 日期
	if start == end {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = DATE '%s'
		`, start)
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= DATE '%s'
			AND fact.bus_date <= DATE '%s'
		`, start, end)
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		%s
		%s
	`, partnerID, storeIdsSQL, dateSQL)
	return whereSQL
}
