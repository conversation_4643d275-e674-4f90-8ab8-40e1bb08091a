package new_pos_report

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/pos"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
)

// PosProductSalesReport pos商品销售报表
func (pr *NewPosRepoPG) PosProductSalesReport(ctx context.Context, start string, end string, storeId int64, partnerID int64) ([]*pos.ProductSalesReport, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := generateQuerySQLPosProductReport(trackId, start, end, storeId, partnerID)
	return queryDBForPosProductReport(trackId, pr.DB, rowSQL), nil
}

// 查询数据库，并将结果映射为map对象
func queryDBForPosProductReport(trackId int64, db *sql.DB, sqlStr string) []*pos.ProductSalesReport {
	results := make([]*pos.ProductSalesReport, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(pos.ProductSalesReport)
		var productList json.RawMessage
		if err := r.Scan(
			&f.Summary.Id,
			&f.Summary.Qty,
			&f.Summary.Amount,
			&f.Summary.Percentage,
			&productList,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(productList, &f.ProductSalesList)
		if err != nil {
			logger.Pre().Error("pos销售报表解析失败")
			return results
		}
		results = append(results, f)
	}

	return results
}

func generateQuerySQLPosProductReport(trackId int64, start string, end string, storeId int64, partnerID int64) string {
	var rowSQL string
	rowSQL = `
-- pos销售报表
WITH
    -- 金额计算表达式封装
    amount_calculator AS (
                         SELECT id,
                                -- 单条记录金额计算逻辑（复用模块）
                                (COALESCE(gross_amount2, 0) +
                                 COALESCE(package_amount, 0) +
                                 COALESCE(send_fee_for_merchant, 0) +
                                 COALESCE(service_fee, 0) +
                                 COALESCE(tip, 0)) -
                                (COALESCE(pay_merchant_allowance, 0) +
                                 COALESCE(promotion_merchant_allowance, 0) +
                                 COALESCE(other_fee, 0) +
                                 COALESCE(merchant_send_fee, 0) +
                                 COALESCE(commission, 0) +
                                 COALESCE(rounding, 0) -
                                 COALESCE(overflow_amount, 0)) AS calc_amount
                         FROM sales_product_amounts
                         ),
    -- 预计算全局总金额（不受后续筛选影响）
    global_total AS (
                         SELECT SUM(ac.calc_amount) AS total
                         FROM sales_product_amounts fact
                                  JOIN amount_calculator ac ON fact.id = ac.id
                         WHERE {WHERE}
                           AND fact.combo_type in (0, 1)
                         ),
    product_base AS (
                         SELECT pc.category AS category_id,
                                fact.product_id,
                                fact.qty,
                                ac.calc_amount
                         FROM sales_product_amounts fact
                                  JOIN amount_calculator ac ON fact.id = ac.id
                                  LEFT JOIN product_caches pc ON fact.product_id = pc.id
                         WHERE {WHERE}
                           AND fact.combo_type in (0, 1)

--  todo                        and pc.category = 1
                         ),
    product_stats AS (
                         SELECT category_id,
                                SUM(qty)                                                AS qty,
                                SUM(calc_amount)                                        AS amount,
                                MAX(global_total.total)                                 AS global_total,
                                -- 计算分类总金额用于商品占比
                                SUM(SUM(calc_amount)) OVER (PARTITION BY category_id )  AS category_total,
                                JSON_BUILD_OBJECT(
                                        'id', product_id,
                                        'qty', SUM(qty),
                                        'amount', SUM(calc_amount),
                                        'percentage', SUM(calc_amount) /
                                                      SUM(NULLIF(SUM(calc_amount), 0))
                                                      OVER (PARTITION BY category_id )) AS product_detail
                         FROM product_base
                                  CROSS JOIN global_total
                         GROUP BY category_id, product_id
                         )
SELECT ps.category_id,
       SUM(ps.qty)                                    AS qty,
       SUM(ps.amount)                                 AS amount,
       SUM(ps.amount)::numeric / MAX(ps.global_total) AS percentage,
       JSON_AGG(ps.product_detail)                    AS product_details
FROM product_stats ps
GROUP BY ps.category_id;
`

	whereSQL := generateWhereSQLForPosProductReport(start, end, storeId, partnerID)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func generateWhereSQLForPosProductReport(start string, end string, storeId int64, partnerID int64) string {
	storeIdsSQL := fmt.Sprintf(`
				AND fact.store_id = %d
			`, storeId)

	dateSQL := "" // 日期
	if start == end {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = DATE '%s'
		`, start)
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= DATE '%s'
			AND fact.bus_date <= DATE '%s'
		`, start, end)
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		%s
		%s
	`, partnerID, storeIdsSQL, dateSQL)
	return whereSQL
}
