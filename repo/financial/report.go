package financial

import (
	"context"
	"fmt"
	repo "gitlab.hexcloud.cn/histore/sales-report/repo/report"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (f *FinancialRepositoryPG) FinancialReport(ctx context.Context, condition *model.RepoCondition) (*model.FinancialResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForFinancialReport(trackId, condition)
	ch1 := make(chan []*model.Financial, 1)
	ch2 := make(chan []*model.Financial, 1)
	go QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go QueryDBWithChan(trackId, f.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *model.Financial
	if len(summarys) > 0 {
		summary = summarys[0]
		if condition.TagType == "SUMMARY" {
			summary.Tag = "收银汇总表汇总信息"
		} else {
			summary.Tag = "收银汇总表详细信息"
		}
	}
	return &model.FinancialResponse{Rows: rows, Summary: summary}, nil
}

func generateQuerySQLForFinancialReport(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	if condition.TagType == "SUMMARY" {
		rowSQL = `
		--收银汇总表汇总信息 ROWS
			WITH base_tickets AS (
			SELECT
				{REGION_ID} AS region_id,
				fact.channel_id, --渠道
				fact.order_type, --账单类型
				coalesce(fact.meal_segment_name, '') as meal_segment_name, --餐段
			    COALESCE(max(branchs[array_length(branchs, 1)]), 0) AS branch_region_id,
				SUM(fact.amount_0+fact.surcharge_amount) AS business_amount, --流水金额（营业流水）/ 商品销售收入流水
				SUM(fact.gross_amount) AS gross_amount,
				SUM(fact.eticket_count) AS order_count, --账单数
				SUM(fact.discount_merchant_contribute) AS discount_amount2, --优惠金额（优惠组成）[子项]
				SUM(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount) AS discount_amount1, --优惠金额（优惠组成）[前面的]
				SUM(fact.amount_0+fact.surcharge_amount-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount+fact.rounding)) AS real_amount, --实收金额（实收组成）
				SUM(fact.commission) AS commission, --佣金
				SUM(fact.merchant_send_fee) AS send_fee_for_merchant, --配送费商家承担
				SUM(fact.other_fee) AS other_fee, --其他费
				SUM(fact.people) as people, --客流
				sum(fact.bowl_num) as bowl_num, --碗数
				sum(fact.tip) as tip, --小费
				sum(fact.tax_fee) as tax_fee,  -- 税费
				sum(fact.surcharge_amount) as surcharge_amount  -- 附加费

			FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE} {WHERE_TICKET}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY {REGION_ID}, fact.channel_id, fact.order_type, fact.meal_segment_name, fact.coupon_channel_name
		),
		base_payments AS (
			SELECT
				{REGION_ID} AS region_id,
				cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
				fact.payment_id, --支付方式
				string_agg(distinct payment_name, ',') as payment_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute, --支付优惠组成金额
				SUM(CASE WHEN fact.finance_pay_amount_used THEN fact.finance_pay_amount ELSE fact.tp_allowance + fact.cost END) AS transfer_real_amount
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY {REGION_ID}, fact.channel_id, fact.payment_id
		),
		base_payments_non_sales AS (
					SELECT
						{REGION_ID} AS region_id,
						SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						AND {REGION_ID} > 0
						AND is_non_sales = TRUE
					GROUP BY {REGION_ID}
				),
	     base_discounts AS (
			SELECT
				{REGION_ID} AS region_id,
				fact.channel_id AS channel_id, --渠道
				max(fact.promotion_id) AS discount_id, --折扣方式
				fact.promotion_name as discount_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance) AS discount_contribute, --折扣优惠组成金额
				SUM(case when fact.channel_name = 'POS' or fact.channel_name = 'pos' or fact.channel_name = '' then fact.tp_allowance + fact.cost + fact.platform_allowance else fact.tp_allowance + fact.cost end) AS transfer_real_amount --折扣实收组成金额
			FROM
				sales_discount_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND (fact.combo_type = 0 OR fact.combo_type = 1)
				AND {REGION_ID} > 0
			GROUP BY {REGION_ID}, fact.channel_id, fact.promotion_id, fact.promotion_name
		),
		base_payments_by_store_and_channel AS (
			SELECT
				region_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'payment_id', payment_id, --支付方式ID
						'payment_name', payment_name, --支付方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --支付优惠组成金额
					    'transfer_real_amount', transfer_real_amount --支付实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_payments, --支付优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_payments --支付实收组成金额汇总
			FROM base_payments
			GROUP BY region_id, channel_id
		),
	    base_discounts_by_store_and_channel AS (
			SELECT
				region_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'discount_id', discount_id, --折扣方式ID
						'discount_name', discount_name, --折扣方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --折扣优惠组成金额
					    'transfer_real_amount', transfer_real_amount --折扣实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_discounts, --折扣优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_discounts --折扣实收组成金额汇总
			FROM base_discounts
			GROUP BY region_id, channel_id
		),
		base_tickets_by_store AS (
			SELECT
				COUNT(1) AS total, --总条数
				region_id,
				max(branch_region_id) as branch_region_id,
				SUM(business_amount) AS business_amount, --折扣
				SUM(discount_amount1) AS discount_amount1, --流水金额（营业流水）
				SUM(discount_amount2) AS discount_amount2, --流水金额（营业流水）
				SUM(gross_amount) AS gross_amount, --流水金额（营业流水）
				SUM(real_amount) AS real_amount,--实收金额（实收组成）
				SUM(order_count) AS order_count, --账单数
				sum(people) as people, --客流
				sum(bowl_num) as bowl_num, --碗数
				sum(tip) as tip, --小费
				SUM(tax_fee) as tax_fee  -- 税费
			FROM
				base_tickets
			GROUP BY region_id
            {LIMIT}
		),
		base_tickets_by_store_and_channel AS (
			SELECT
				region_id,
				channel_id,
				SUM(discount_amount2) AS discount_amount, --折扣
				SUM(commission) AS commission, --佣金
				SUM(send_fee_for_merchant) AS send_fee_for_merchant, --配送费商家承担
				SUM(other_fee) AS other_fee, --其他费
				SUM(surcharge_amount)      AS surcharge_amount       --附加费
			FROM
				base_tickets
			GROUP BY region_id, channel_id
		),
		business_days AS (
			SELECT
				region_id, COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY fact.bus_date, {REGION_ID}
			) T
			GROUP BY region_id
		),
		composition_of_bill_types AS ( --账单类型组成
			SELECT
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'order_type', order_type, --账单类型
						'order_name', '', --账单类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
					region_id,
					order_type, --账单类型
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
					SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY region_id, order_type
			) T
			GROUP BY region_id
		),
		composition_of_take_away AS ( --外卖渠道组成
			SELECT
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
					region_id,
					channel_id,                       --渠道
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
					sum(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY region_id, channel_id
			) T
			GROUP BY region_id
		),
		composition_of_discount AS ( --优惠组成
			SELECT
				region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'send_fee_for_merchant', send_fee_for_merchant, --配送费商家承担
						'other_fee', other_fee, --其他费
						'payments', payments, --支付
					    'discounts', discounts, --折扣
						'discount_contribute_total_payments', discount_contribute_total_payments, --支付汇总
					    'discount_contribute_total_discounts', discount_contribute_total_discounts --折扣汇总
					)
				) AS "data",
				SUM(discount_contribute_total_payments + send_fee_for_merchant + other_fee + discount_contribute_total_discounts) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.region_id,
					bt.channel_id,
					bt.send_fee_for_merchant AS send_fee_for_merchant, --配送费商家承担
					bt.other_fee AS other_fee, --其他费
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.discount_contribute_total_payments, 0) AS discount_contribute_total_payments,
				    COALESCE(bd.discount_contribute_total_discounts, 0) AS discount_contribute_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.region_id = bp.region_id AND bt.channel_id = bp.channel_id
						LEFT JOIN base_discounts_by_store_and_channel bd
			                ON bt.region_id = bd.region_id AND bt.channel_id=bd.channel_id
			) T
			GROUP BY region_id
		),
	     composition_of_pay AS ( --实收组成
			SELECT
				region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'discounts', discounts, --折扣
						'payments', payments, --支付
						'transfer_real_amount_total_payments', transfer_real_amount_total_payments, --支付汇总
					    'transfer_real_amount_total_discounts',transfer_real_amount_total_discounts,--折扣汇总
						'commission', commission, --佣金
						'surcharge_amount', surcharge_amount --附加费
					)
				) AS "data",
				SUM(transfer_real_amount_total_payments+commission+transfer_real_amount_total_discounts+surcharge_amount) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.region_id,
					bt.channel_id,
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.transfer_real_amount_total_payments, 0) AS transfer_real_amount_total_payments, --设默认值
				    COALESCE(bd.transfer_real_amount_total_discounts, 0) AS transfer_real_amount_total_discounts, --设默认值
					bt.commission                                        AS commission,                           --佣金
				    bt.surcharge_amount                                  AS surcharge_amount                      --附加费
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.region_id = bp.region_id AND bt.channel_id = bp.channel_id
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    ON bt.region_id = bd.region_id AND bt.channel_id = bd.channel_id
			) T
			GROUP BY region_id
		),
		composition_of_meal_segment as (
			SELECT
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'meal_segment_name', meal_segment_name,
						'gross_amount', gross_amount,
						'real_amount', real_amount,
						'valid_order_count', valid_order_count
					)
				) AS "data"
			FROM (
				SELECT
					region_id,
					meal_segment_name, --餐段
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
					SUM(order_count) AS valid_order_count --单数
				FROM base_tickets
				where meal_segment_name is not null and meal_segment_name != ''
				GROUP BY region_id, meal_segment_name
			) T
			GROUP BY region_id
		)
		SELECT
			'' AS bus_date,
			COALESCE(bt.region_id, 0) AS region_id, --店铺ID
			COALESCE(bt.branch_region_id, 0) AS branch_region_id,
			COALESCE(bt.business_amount, 0) AS business_amount, --流水金额（营业流水）
			COALESCE(bt.discount_amount1 - bpns.discount_contribute, 0) AS discount_amount,               --优惠金额（优惠组成）
			COALESCE(bt.real_amount, 0) AS real_amount,--实收金额（实收组成）
			COALESCE(bt.real_amount, 0) / 1.1 AS real_amount_without_gst,--实收不含税
			COALESCE(bt.order_count, 0) AS valid_order_count, --账单数
			coalesce(bt.people, 0) as people, -- 客流
			coalesce(bt.tip, 0) as tip, -- 小费
			coalesce(bt.tax_fee, 0) as tax_fee, -- 税费
			coalesce(bt.bowl_num, 0) as bowl_num, -- 碗数
			COALESCE((CASE WHEN bt.people = 0 THEN 0 ELSE bt.real_amount / bt.people END), 0) AS real_amount_per_capita, --人均消费
			COALESCE((CASE WHEN bt.order_count = 0 THEN 0 ELSE bt.real_amount / bt.order_count END), 0) AS customer_price, --单均消费
			-- 日均客流 客流/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.people / bd.days END), 0) AS customer_count_per_day,
			-- 日均实收: 实收金额/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.real_amount / bd.days END), 0) AS real_amount_per_day,
			COALESCE((CASE WHEN bt.bowl_num = 0 THEN 0 ELSE bt.real_amount / bt.bowl_num END), 0) AS real_amount_per_bowl,

			COALESCE(coby.data, '[]'::json) AS composition_of_bill_types, --账单类型组成
			COALESCE(cota.data, '[]'::json) AS composition_of_take_away, --外卖渠道组成

			COALESCE(cop.data, '[]'::json) AS composition_of_paid_in, --实收组成
			COALESCE(cop.real_amount_total, 0) AS composition_of_paid_in_total, --实收组成小计

			COALESCE(cod.data, '[]'::json) AS composition_of_discount, --优惠组成
			COALESCE(cod.real_amount_total, 0) AS composition_of_discount_total, --优惠组成小计
			COALESCE(coms.data, '[]'::json) AS composition_of_meal_segment, --餐段组成
			0 AS total --summary时的汇总条数
		FROM
			base_tickets_by_store bt
				LEFT JOIN business_days bd ON bt.region_id = bd.region_id
				LEFT JOIN composition_of_bill_types coby ON bt.region_id = coby.region_id
				LEFT JOIN composition_of_take_away cota ON bt.region_id = cota.region_id
			    LEFT JOIN composition_of_pay cop ON bt.region_id = cop.region_id
				LEFT JOIN composition_of_discount cod ON bt.region_id = cod.region_id
				LEFT JOIN composition_of_meal_segment coms ON bt.region_id = coms.region_id
				LEFT JOIN base_payments_non_sales bpns ON bt.region_id = bpns.region_id
		ORDER BY bt.region_id
 		`
		summarySQL = `
		--收银汇总表汇总信息 SUMMARY
		WITH base_tickets AS (
			SELECT
				{REGION_ID} AS region_id,                                                                                                                                                                                        --门店
				fact.channel_id,                                                                                                                                                                                      --渠道
				fact.order_type,                                                                                                                                                                                      --账单类型
				coalesce(fact.meal_segment_name, '') as meal_segment_name, --餐段
				SUM(fact.amount_0+fact.surcharge_amount) AS business_amount,                                                                                                                                                                --流水金额（营业流水）/ 商品销售收入流水
				SUM(fact.gross_amount) AS gross_amount,
				SUM(fact.eticket_count) AS order_count,                                                                                                                                                               --账单数
				SUM(fact.discount_merchant_contribute) AS discount_amount2,                                                                                                                                           --优惠金额（优惠组成）[单项]
				SUM(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount) AS discount_contribute,                  --优惠金额（优惠组成）[前面的]
				SUM(fact.amount_0+fact.surcharge_amount-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount+fact.rounding)) AS transfer_real_amount, --实收金额（实收组成）
				SUM(fact.commission) AS commission,                                                                                                                                                                   --佣金
				SUM(fact.merchant_send_fee) AS send_fee_for_merchant,                                                                                                                                                 --配送费商家承担
				SUM(fact.other_fee) AS other_fee,                                                                                                                                                       --其他费
				SUM(fact.people) as people, --客流
				sum(fact.bowl_num) as bowl_num, --碗数
				sum(fact.tip) as tip, --小费
				sum(fact.tax_fee) as tax_fee,  -- 税费
				sum(fact.surcharge_amount) as surcharge_amount  -- 附加费
			
			FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE} {WHERE_TICKET}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY {REGION_ID}, fact.channel_id, fact.order_type, fact.meal_segment_name, fact.coupon_channel_name
		),
		base_payments AS (
			SELECT
				cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
				fact.payment_id, --支付方式
				string_agg(distinct payment_name, ',') as payment_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute, --支付优惠组成金额
				SUM(CASE WHEN fact.finance_pay_amount_used THEN fact.finance_pay_amount ELSE fact.tp_allowance + fact.cost END) AS transfer_real_amount
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY fact.channel_id, fact.payment_id
		),
		base_payments_non_sales AS (
					SELECT
						SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						AND {REGION_ID} > 0
						AND is_non_sales = TRUE
				),
		base_discounts AS (
			SELECT
				fact.channel_id AS channel_id, --渠道
				max(fact.promotion_id) AS discount_id, --折扣方式
				fact.promotion_name as discount_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance) AS discount_contribute, --折扣优惠组成金额
				SUM(case when fact.channel_name = 'POS' or fact.channel_name = 'pos' or fact.channel_name = '' then fact.tp_allowance + fact.cost + fact.platform_allowance else fact.tp_allowance + fact.cost end) AS transfer_real_amount --折扣实收组成金额
			FROM
				sales_discount_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND (fact.combo_type = 0 OR fact.combo_type = 1)
				AND {REGION_ID} > 0
			GROUP BY fact.channel_id, fact.promotion_id, fact.promotion_name
		),
		base_discounts_by_store_and_channel AS (
			SELECT
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'discount_id', discount_id, --折扣方式ID
						'discount_name', discount_name, --折扣方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --折扣优惠组成金额
					    'transfer_real_amount', transfer_real_amount --折扣实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_discounts, --折扣优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_discounts --折扣实收组成金额汇总
			FROM base_discounts
			GROUP BY channel_id
		),
		base_payments_by_store_and_channel AS (
			SELECT
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'payment_id', payment_id, --支付方式ID
						'payment_name', payment_name, --支付方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --支付优惠组成金额
					    'transfer_real_amount', transfer_real_amount --支付实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_payments, --支付优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_payments --支付实收组成金额汇总
			FROM base_payments
			GROUP BY channel_id
		),
		base_tickets_by_store AS (
			SELECT
				COUNT(1) AS total, --总条数
				SUM(discount_amount1) AS discount_amount1, --折扣
				SUM(discount_amount2) AS discount_amount2, --折扣
				SUM(business_amount) AS business_amount, --流水金额（营业流水）
				SUM(gross_amount) AS gross_amount, --流水金额（营业流水）
				SUM(real_amount) AS real_amount,--实收金额（实收组成）
				SUM(order_count) AS order_count, --账单数
				sum(people) as people, --客流
				sum(bowl_num) as bowl_num, --碗数
				sum(tip) as tip, --小费
				sum(tax_fee) as tax_fee  -- 税费

			FROM (
				SELECT
					COUNT(1) AS total, --总条数
					SUM(discount_contribute)  AS discount_amount1, --折扣
					SUM(discount_amount2)     AS discount_amount2, --折扣
					SUM(business_amount)      AS business_amount,  --流水金额（营业流水）
					SUM(gross_amount)         AS gross_amount,     --流水金额（营业流水）
					SUM(transfer_real_amount) AS real_amount,--实收金额（实收组成）
					SUM(order_count)          AS order_count,      --账单数
					sum(people) as people, --客流
					sum(bowl_num) as bowl_num, --碗数
					sum(tip) as tip, --小费
					sum(tax_fee) as tax_fee  -- 税费
				FROM
					base_tickets
				GROUP BY region_id
			) T
		),
		base_tickets_by_store_and_channel AS (
			SELECT
				channel_id,
				SUM(discount_amount2) AS discount_amount, --折扣
				SUM(commission) AS commission, --佣金
				SUM(send_fee_for_merchant) AS send_fee_for_merchant, --配送费商家承担
				SUM(other_fee) AS other_fee, --其他费
				SUM(surcharge_amount)      AS surcharge_amount       --附加费
			FROM
				base_tickets
			GROUP BY channel_id
		),
		business_days AS (
			SELECT
				COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY fact.bus_date, {REGION_ID}
			) T
		),
		composition_of_bill_types AS ( --账单类型组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'order_type', order_type, --账单类型
						'order_name', '', --账单类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
					order_type, --账单类型
					SUM(business_amount) AS gross_amount, --流水
					SUM(transfer_real_amount) AS real_amount, --实收
					SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY order_type
			) T
		),
		composition_of_take_away AS ( --外卖渠道组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
 						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
						channel_id,                       --渠道
						SUM(business_amount) AS gross_amount, --流水
						SUM(transfer_real_amount) AS real_amount, --实收
						SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY channel_id
			) T
		),
		composition_of_pay AS ( --实收组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'discounts', discounts, --折扣
						'payments', payments, --支付
						'transfer_real_amount_total_payments', transfer_real_amount_total_payments, --支付汇总
					    'transfer_real_amount_total_discounts',transfer_real_amount_total_discounts, --折扣汇总
						'commission', commission, --佣金
						'surcharge_amount', surcharge_amount --附加费
					)
				) AS "data",
				SUM(transfer_real_amount_total_payments+commission+transfer_real_amount_total_discounts+surcharge_amount) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.channel_id,
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.transfer_real_amount_total_payments, 0) AS transfer_real_amount_total_payments, --设默认值
				    COALESCE(bd.transfer_real_amount_total_discounts, 0) AS transfer_real_amount_total_discounts, --设默认值
					bt.commission                                        AS commission,                           --佣金
				    bt.surcharge_amount                                  AS surcharge_amount                      --附加费
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON  bt.channel_id = bp.channel_id
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    ON bt.channel_id = bd.channel_id
			) T
		),
		composition_of_discount AS ( --优惠组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'discount_amount', discount_amount, --折扣
						'send_fee_for_merchant', send_fee_for_merchant, --配送费商家承担
						'other_fee', other_fee, --其他费
						'payments', payments, --支付
					    'discounts', discounts, --折扣
						'discount_contribute_total_payments', discount_contribute_total_payments, --支付汇总
					    'discount_contribute_total_discounts', discount_contribute_total_discounts --折扣汇总
					)
				) AS "data",
				SUM(discount_contribute_total_payments + send_fee_for_merchant + other_fee + discount_contribute_total_discounts) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.channel_id,
					bt.discount_amount AS discount_amount, --折扣
					bt.send_fee_for_merchant AS send_fee_for_merchant, --配送费商家承担
					bt.other_fee AS other_fee, --其他费
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
					COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.discount_contribute_total_payments, 0) AS discount_contribute_total_payments,
					COALESCE(bd.discount_contribute_total_discounts, 0) AS discount_contribute_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.channel_id = bp.channel_id
						LEFT JOIN base_discounts_by_store_and_channel bd
			                ON bt.channel_id=bd.channel_id
			) T
		),
		composition_of_meal_segment as (
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'meal_segment_name', meal_segment_name,
						'gross_amount', gross_amount,
						'real_amount', real_amount,
						'valid_order_count', valid_order_count
					)
				) AS "data"
			FROM (
				SELECT
					meal_segment_name, --餐段
					SUM(business_amount) AS gross_amount, --流水
					SUM(transfer_real_amount) AS real_amount, --实收
					SUM(order_count) AS valid_order_count --单数
				FROM base_tickets
				where meal_segment_name is not null and meal_segment_name != ''
				GROUP BY meal_segment_name
			) T
		)
		SELECT
			'' AS bus_date,
			0 AS region_id, --店铺ID
			0 AS branch_region_id,
			COALESCE(bt.business_amount, 0) AS business_amount, --流水金额（营业流水）
			COALESCE(bt.discount_amount1 - bpns.discount_contribute, 0) AS discount_amount,               --优惠金额（优惠组成）
			COALESCE(bt.real_amount, 0) AS real_amount,--实收金额（实收组成）
			COALESCE(bt.real_amount, 0) / 1.1 AS real_amount_without_gst,--实收不含税
			COALESCE(bt.order_count, 0) AS valid_order_count, --账单数
			coalesce(bt.people, 0) as people, -- 客流
			coalesce(bt.tip, 0) as tip, -- 小费
			coalesce(bt.tax_fee, 0) as tax_fee, -- 	税费
			coalesce(bt.bowl_num, 0) as bowl_num, -- 碗数
			COALESCE((CASE WHEN bt.people = 0 THEN 0 ELSE bt.real_amount / bt.people END), 0) AS real_amount_per_capita, --人均消费
			COALESCE((CASE WHEN bt.order_count = 0 THEN 0 ELSE bt.real_amount / bt.order_count END), 0) AS customer_price, --单均消费
			-- 日均客流 客流/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.people / bd.days END), 0) AS customer_count_per_day,
			-- 日均实收: 实收金额/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.real_amount / bd.days END), 0) AS real_amount_per_day,
			COALESCE((CASE WHEN bt.bowl_num = 0 THEN 0 ELSE bt.real_amount / bt.bowl_num END), 0) AS real_amount_per_bowl,

			COALESCE(coby.data, '[]'::json) AS composition_of_bill_types, --账单类型组成
			COALESCE(cota.data, '[]'::json) AS composition_of_take_away, --外卖渠道组成

			COALESCE(copi.data, '[]'::json) AS composition_of_paid_in, --实收组成
			COALESCE(copi.real_amount_total, 0) AS composition_of_paid_in_total, --实收组成小计

			COALESCE(cod.data, '[]'::json) AS composition_of_discount, --优惠组成
			COALESCE(cod.real_amount_total, 0) AS composition_of_discount_total, --优惠组成小计

			COALESCE(coms.data, '[]'::json) AS composition_of_meal_segment, --餐段组成
			bt.total AS total --summary时的汇总条数
		FROM
			base_tickets_by_store bt,
			business_days bd,
			composition_of_bill_types coby,
			composition_of_take_away cota,
			composition_of_pay copi,
			composition_of_discount cod,
			composition_of_meal_segment coms,
			base_payments_non_sales bpns
 		`
	} else {
		rowSQL = `
		--收银汇总表详细信息 ROWS
		WITH base_tickets AS (
				SELECT
			    to_char(fact.bus_date,'YYYY-MM-DD') AS bus_date,
				{REGION_ID} AS region_id,
				COALESCE(max(branchs[array_length(branchs, 1)]), 0) AS branch_region_id,
				fact.channel_id, --渠道
				fact.order_type, --账单类型
				coalesce(fact.meal_segment_name, '') as meal_segment_name, --餐段
				SUM(fact.amount_0+fact.surcharge_amount) AS business_amount, --流水金额（营业流水）/ 商品销售收入流水
				SUM(fact.gross_amount) AS gross_amount,
				SUM(fact.eticket_count) AS order_count, --账单数
				SUM(fact.discount_merchant_contribute) AS discount_amount2, --优惠金额（优惠组成）[子项]
				SUM(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount) AS discount_amount1, --优惠金额（优惠组成）[前面的]
				SUM(fact.amount_0+fact.surcharge_amount-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount)) AS real_amount, --实收金额（实收组成）
				SUM(fact.commission) AS commission, --佣金
				SUM(fact.merchant_send_fee) AS send_fee_for_merchant, --配送费商家承担
				SUM(fact.other_fee) AS other_fee, --其他费
				SUM(fact.people) as people, --客流
				sum(fact.bowl_num) as bowl_num, --碗数
				sum(fact.tip) as tip, --小费
				sum(fact.tax_fee) as tax_fee,  -- 税费
				sum(fact.surcharge_amount) as surcharge_amount  -- 附加费
			FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE} {WHERE_TICKET}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY fact.bus_date, {REGION_ID},fact.channel_id, fact.order_type, fact.meal_segment_name
	       ORDER BY fact.bus_date DESC,{REGION_ID}
		),
		base_payments AS (
			SELECT
			    to_char(fact.bus_date,'YYYY-MM-DD') AS bus_date,
				{REGION_ID} AS region_id,
				cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
				fact.payment_id, --支付方式
				max(fact.payment_name) as payment_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute, --支付优惠组成金额
				SUM(CASE WHEN fact.finance_pay_amount_used THEN fact.finance_pay_amount ELSE fact.tp_allowance + fact.cost END) AS transfer_real_amount
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND {REGION_ID} > 0
				AND is_non_sales = FALSE
			GROUP BY fact.bus_date, {REGION_ID}, fact.channel_id, fact.payment_id
		    ORDER BY fact.bus_date DESC, {REGION_ID}
		),
		base_payments_non_sales AS (
					SELECT
						to_char(fact.bus_date,'YYYY-MM-DD') AS bus_date,
						{REGION_ID} AS region_id,
						SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						AND {REGION_ID} > 0
						AND is_non_sales = TRUE
					GROUP BY fact.bus_date, {REGION_ID}
					ORDER BY fact.bus_date DESC, {REGION_ID}
				),
	    base_discounts AS (
			SELECT
			    to_char(fact.bus_date,'YYYY-MM-DD') AS bus_date,
				{REGION_ID} AS region_id,
				fact.channel_id AS channel_id, --渠道
				max(fact.promotion_id) AS discount_id, --折扣方式
				fact.promotion_name as discount_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance) AS discount_contribute, --折扣优惠组成金额
				SUM(case when fact.channel_name = 'POS' or fact.channel_name = 'pos' or fact.channel_name = '' then fact.tp_allowance + fact.cost + fact.platform_allowance else fact.tp_allowance + fact.cost end) AS transfer_real_amount --折扣实收组成金额
			FROM
				sales_discount_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND (fact.combo_type = 0 OR fact.combo_type = 1)
				AND {REGION_ID} > 0
			GROUP BY fact.bus_date, {REGION_ID}, fact.channel_id, fact.promotion_id, fact.promotion_name
	       ORDER BY fact.bus_date DESC, {REGION_ID}
		),
		base_payments_by_store_and_channel AS (
			SELECT
			    bus_date,
				region_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'payment_id', payment_id, --支付方式ID
						'payment_name', payment_name, --支付方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --支付优惠组成金额
					    'transfer_real_amount', transfer_real_amount --支付实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_payments, --支付优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_payments --支付实收组成金额汇总
			FROM base_payments
			GROUP BY bus_date, region_id, channel_id
		),
	   base_discounts_by_store_and_channel AS (
			SELECT
			    bus_date,
				region_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'discount_id', discount_id, --折扣方式ID
						'discount_name', discount_name, --折扣方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --折扣优惠组成金额
					    'transfer_real_amount', transfer_real_amount --折扣实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_discounts, --折扣优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_discounts --折扣实收组成金额汇总
			FROM base_discounts
			GROUP BY bus_date, region_id, channel_id
		),
		base_tickets_by_store AS (
			SELECT
				COUNT(1) AS total, --总条数
			    bus_date,
				region_id,
				max(branch_region_id) as branch_region_id,
				SUM(business_amount) AS business_amount, --折扣
				SUM(discount_amount1) AS discount_amount1, --流水金额（营业流水）
				SUM(discount_amount2) AS discount_amount2, --流水金额（营业流水）
				SUM(gross_amount) AS gross_amount, --流水金额（营业流水）
				SUM(real_amount) AS real_amount,--实收金额（实收组成）
				SUM(order_count) AS order_count, --账单数
				sum(people) as people, --客流
				sum(bowl_num) as bowl_num, --碗数
				sum(tip) as tip, --小费
				sum(tax_fee) as tax_fee --税费
			FROM
				base_tickets
			GROUP BY bus_date,region_id
			ORDER BY bus_date DESC ,region_id
	       {LIMIT}
		),
		base_tickets_by_store_and_channel AS (
			SELECT
			    bus_date,
				region_id,
				channel_id,
				SUM(discount_amount2) AS discount_amount, --折扣
				SUM(commission) AS commission, --佣金
				SUM(send_fee_for_merchant) AS send_fee_for_merchant, --配送费商家承担
				SUM(other_fee) AS other_fee, --其他费
				SUM(surcharge_amount)      AS surcharge_amount       --附加费
			FROM
				base_tickets
			GROUP BY bus_date,region_id, channel_id
		),
		business_days AS (
			SELECT
				region_id, COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY {REGION_ID}
			) T
			GROUP BY region_id
		),
		composition_of_bill_types AS ( --账单类型组成
			SELECT
			bus_date,
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'order_type', order_type, --账单类型
						'order_name', '', --账单类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
				    bus_date,
					region_id,
					order_type, --账单类型
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
					SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY bus_date,region_id, order_type
			) T
			GROUP BY bus_date,region_id
		),
		composition_of_take_away AS ( --外卖渠道组成
			SELECT
			bus_date,
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
					    'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总

			FROM (
				SELECT
				    bus_date,
					region_id,
					channel_id,                       --渠道
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
				    sum(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY bus_date,region_id, channel_id
			) T
			GROUP BY bus_date,region_id
		),
		composition_of_discount AS ( --优惠组成
			SELECT
			    bus_date,
				region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'send_fee_for_merchant', send_fee_for_merchant, --配送费商家承担
						'other_fee', other_fee, --其他费
						'payments', payments, --支付
						'discounts', discounts, --折扣
						'discount_contribute_total_payments', discount_contribute_total_payments, --支付汇总
						'discount_contribute_total_discounts', discount_contribute_total_discounts --折扣汇总
					)
				) AS "data",
				SUM(discount_contribute_total_discounts + send_fee_for_merchant + other_fee + discount_contribute_total_payments) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
				    bt.bus_date,
					bt.region_id,
					bt.channel_id,
					bt.discount_amount AS discount_amount, --折扣
					bt.send_fee_for_merchant AS send_fee_for_merchant, --配送费商家承担
					bt.other_fee AS other_fee, --其他费
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.discount_contribute_total_payments, 0) AS discount_contribute_total_payments,
				    COALESCE(bd.discount_contribute_total_discounts, 0) AS discount_contribute_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.region_id = bp.region_id AND bt.channel_id = bp.channel_id
							AND bt.bus_date = bp.bus_date
 					LEFT JOIN base_discounts_by_store_and_channel bd
			                ON bt.region_id = bd.region_id AND bt.channel_id=bd.channel_id
			                AND bt.bus_date = bd.bus_date
			) T
			GROUP BY bus_date,region_id
		),
	    composition_of_pay AS ( --实收组成
			SELECT
			    bus_date,
				region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'discounts', discounts, --折扣
						'payments', payments, --支付
						'transfer_real_amount_total_payments', transfer_real_amount_total_payments, --支付汇总
					    'transfer_real_amount_total_discounts',transfer_real_amount_total_discounts, --折扣汇总
						'commission', commission, --佣金
						'surcharge_amount', surcharge_amount --附加费
					)
				) AS "data",
				SUM(transfer_real_amount_total_payments+commission+transfer_real_amount_total_discounts+surcharge_amount) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
				    bt.bus_date,
					bt.region_id,
					bt.channel_id,
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.transfer_real_amount_total_payments, 0) AS transfer_real_amount_total_payments, --设默认值
				    COALESCE(bd.transfer_real_amount_total_discounts, 0) AS transfer_real_amount_total_discounts, --设默认值
					bt.commission                                        AS commission,                           --佣金
				    bt.surcharge_amount                                  AS surcharge_amount                      --附加费
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.region_id = bp.region_id AND bt.channel_id = bp.channel_id AND bt.bus_date=bp.bus_date
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    ON bt.region_id = bd.region_id AND bt.channel_id = bd.channel_id AND bt.bus_date = bd.bus_date
			) T
			GROUP BY bus_date,region_id
		),
		composition_of_meal_segment as (
			SELECT
			bus_date,
			region_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'meal_segment_name', meal_segment_name,
						'gross_amount', gross_amount,
						'real_amount', real_amount,
						'valid_order_count', valid_order_count
					)
				) AS "data"
			FROM (
				SELECT
				    bus_date,
					region_id,
					meal_segment_name, --餐段
					SUM(business_amount) AS gross_amount, --流水
					SUM(real_amount) AS real_amount, --实收
					SUM(order_count) AS valid_order_count --单数
				FROM base_tickets
				where meal_segment_name is not null and meal_segment_name != ''
				GROUP BY bus_date, region_id, meal_segment_name
			) T
			GROUP BY bus_date, region_id
		)
		SELECT
		    bt.bus_date AS bus_date,
			COALESCE(bt.region_id, 0) AS region_id, --店铺ID
		    COALESCE(bt.branch_region_id, 0) AS branch_region_id,
			COALESCE(bt.business_amount, 0) AS business_amount, --流水金额（营业流水）
			COALESCE(bt.discount_amount1 - bpns.discount_contribute, 0) AS discount_amount,               --优惠金额（优惠组成）
			COALESCE(bt.real_amount, 0) AS real_amount,--实收金额（实收组成）
			COALESCE(bt.real_amount, 0) / 1.1               AS real_amount_without_gst,--实收不含税
			COALESCE(bt.order_count, 0) AS valid_order_count, --账单数
			coalesce(bt.people, 0) as people, -- 客流
			coalesce(bt.tip, 0) as tip, -- 小费
			coalesce(bt.tax_fee, 0) as tax_fee, --税费
			coalesce(bt.bowl_num, 0) as bowl_num, -- 碗数
			COALESCE((CASE WHEN bt.people = 0 THEN 0 ELSE bt.real_amount / bt.people END), 0) AS real_amount_per_capita, --人均消费
			COALESCE((CASE WHEN bt.order_count = 0 THEN 0 ELSE bt.real_amount / bt.order_count END), 0) AS customer_price, --单均消费
				-- 日均客流 客流/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.people / bd.days END), 0) AS customer_count_per_day,
			-- 日均实收: 实收金额/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.real_amount / bd.days END), 0) AS real_amount_per_day,
			COALESCE((CASE WHEN bt.bowl_num = 0 THEN 0 ELSE bt.real_amount / bt.bowl_num END), 0) AS real_amount_per_bowl,

			COALESCE(coby.data, '[]'::json) AS composition_of_bill_types, --账单类型组成
			COALESCE(cota.data, '[]'::json) AS composition_of_take_away, --外卖渠道组成

			COALESCE(cop.data, '[]'::json) AS composition_of_paid_in, --实收组成
			COALESCE(cop.real_amount_total, 0) AS composition_of_paid_in_total, --实收组成小计

			COALESCE(cod.data, '[]'::json) AS composition_of_discount, --优惠组成
			COALESCE(cod.real_amount_total, 0) AS composition_of_discount_total, --优惠组成小计
			COALESCE(coms.data, '[]'::json) AS composition_of_meal_segment, --餐段组成
			0 AS total --summary时的汇总条数
		FROM
			base_tickets_by_store bt
				LEFT JOIN business_days bd ON bt.region_id = bd.region_id
				LEFT JOIN composition_of_bill_types coby ON bt.region_id = coby.region_id AND bt.bus_date = coby.bus_date
				LEFT JOIN composition_of_take_away cota ON bt.region_id = cota.region_id AND bt.bus_date = cota.bus_date
			    LEFT JOIN composition_of_pay cop ON bt.region_id = cop.region_id AND bt.bus_date = cop.bus_date
				LEFT JOIN composition_of_discount cod ON bt.region_id = cod.region_id AND bt.bus_date = cod.bus_date
				LEFT JOIN composition_of_meal_segment coms ON bt.region_id = coms.region_id AND bt.bus_date = coms.bus_date
				LEFT JOIN base_payments_non_sales bpns ON bt.region_id = bpns.region_id AND bt.bus_date = bpns.bus_date
		ORDER BY bt.bus_date DESC, bt.region_id;
		`

		summarySQL = `
		--收银汇总表详细信息 SUMMARY
		WITH base_tickets AS (
			SELECT
				fact.bus_date,                                               																																		  --日期
				{REGION_ID} AS region_id,                                                                                                                                                                                        --门店
				fact.channel_id,                                                                                                                                                                                      --渠道
				fact.order_type,                                                                                                                                                                                      --账单类型
				coalesce(fact.meal_segment_name, '') as meal_segment_name, --餐段
				SUM(fact.amount_0+fact.surcharge_amount) AS business_amount,                                                                                                                                                                --流水金额（营业流水）/ 商品销售收入流水
				SUM(fact.gross_amount) AS gross_amount,
				SUM(fact.eticket_count) AS order_count,                                                                                                                                                               --账单数
				SUM(fact.discount_merchant_contribute) AS discount_amount2,                                                                                                                                           --优惠金额（优惠组成）[单项]
				SUM(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount) AS discount_contribute,                  --优惠金额（优惠组成）[前面的]
				SUM(fact.amount_0+fact.surcharge_amount-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.pay_merchant_contribute-fact.overflow_amount+fact.rounding)) AS transfer_real_amount, --实收金额（实收组成）
				SUM(fact.commission) AS commission,                                                                                                                                                                   --佣金
				SUM(fact.merchant_send_fee) AS send_fee_for_merchant,                                                                                                                                                 --配送费商家承担
				SUM(fact.other_fee) AS other_fee,                                                                                                                                                       --其他费
				SUM(fact.people) as people, --客流
				sum(fact.bowl_num) as bowl_num, --碗数
				sum(fact.tip) as tip, --小费
				sum(fact.tax_fee) as tax_fee,  -- 税费
				sum(fact.surcharge_amount) as surcharge_amount  -- 附加费
			FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE} {WHERE_TICKET}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY fact.bus_date, {REGION_ID}, fact.channel_id, fact.order_type, fact.meal_segment_name
		),
		base_payments AS (
			SELECT
				cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
				fact.payment_id, --支付方式
				max(fact.payment_name) as payment_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute, --支付优惠组成金额
				SUM(CASE WHEN fact.finance_pay_amount_used THEN fact.finance_pay_amount ELSE fact.tp_allowance + fact.cost END) AS transfer_real_amount
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND {REGION_ID} > 0
				AND fact.is_non_sales = FALSE
			GROUP BY fact.channel_id, fact.payment_id
		),
		base_payments_non_sales AS (
			SELECT
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute --支付优惠组成金额
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND {REGION_ID} > 0
				AND is_non_sales = TRUE
		),
		base_discounts AS (
			SELECT
				{REGION_ID} AS region_id,
				fact.channel_id AS channel_id, --渠道
				max(fact.promotion_id) AS discount_id, --折扣方式
				fact.promotion_name as discount_name,
				SUM(fact.real_amount) AS real_amount, --账单类型组成金额
				SUM(fact.merchant_allowance) AS discount_contribute, --折扣优惠组成金额
				SUM(case when fact.channel_name = 'POS' or fact.channel_name = 'pos' or fact.channel_name = '' then fact.tp_allowance + fact.cost + fact.platform_allowance else fact.tp_allowance + fact.cost end) AS transfer_real_amount --折扣实收组成金额
			FROM
				sales_discount_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
			WHERE
				{WHERE}
				AND (fact.combo_type = 0 OR fact.combo_type = 1)
				AND {REGION_ID} > 0
			GROUP BY {REGION_ID}, fact.channel_id, fact.promotion_id, fact.promotion_name
		),
		base_discounts_by_store_and_channel AS (
			SELECT
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'discount_id', discount_id, --折扣方式ID
						'discount_name', discount_name, --折扣方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --折扣优惠组成金额
					    'transfer_real_amount', transfer_real_amount --折扣实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_discounts, --折扣优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_discounts --折扣实收组成金额汇总
			FROM base_discounts
			GROUP BY channel_id
		),
		base_payments_by_store_and_channel AS (
			SELECT
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'payment_id', payment_id, --支付方式ID
						'payment_name', payment_name, --支付方式名称
						'real_amount', real_amount, --账单类型组成金额
						'discount_contribute', discount_contribute, --支付优惠组成金额
					    'transfer_real_amount', transfer_real_amount --支付实收组成金额
					)
				) AS "data",
				SUM(discount_contribute) AS discount_contribute_total_payments, --支付优惠组成金额汇总
			    SUM(transfer_real_amount) AS transfer_real_amount_total_payments --支付实收组成金额汇总
			FROM base_payments
			GROUP BY channel_id
		),

		base_tickets_by_store AS (
			SELECT
				COUNT(1) AS total, --总条数
				SUM(discount_amount1) AS discount_amount1, --折扣
				SUM(discount_amount2) AS discount_amount2, --折扣
				SUM(business_amount) AS business_amount, --流水金额（营业流水）
				SUM(gross_amount) AS gross_amount, --流水金额（营业流水）
				SUM(real_amount) AS real_amount,--实收金额（实收组成）
				SUM(order_count) AS order_count, --账单数
				sum(people) as people, --客流
				sum(bowl_num) as bowl_num, --碗数
				sum(tip) as tip, --小费
				sum(tax_fee) as tax_fee --税费

			FROM (
				SELECT
					COUNT(1) AS total, --总条数
					SUM(discount_contribute)  AS discount_amount1, --折扣
					SUM(discount_amount2)     AS discount_amount2, --折扣
					SUM(business_amount)      AS business_amount,  --流水金额（营业流水）
					SUM(gross_amount)         AS gross_amount,     --流水金额（营业流水）
					SUM(transfer_real_amount) AS real_amount,--实收金额（实收组成）
					SUM(order_count)          AS order_count,      --账单数
					sum(people) as people, --客流
					sum(bowl_num) as bowl_num, --碗数
					sum(tip) as tip, --小费
					sum(tax_fee) as tax_fee --税费
				FROM
					base_tickets
				GROUP BY bus_date,region_id
			) T
		),
		base_tickets_by_store_and_channel AS (
			SELECT
				channel_id,
				SUM(discount_amount2) AS discount_amount, --折扣
				SUM(commission) AS commission, --佣金
				SUM(send_fee_for_merchant) AS send_fee_for_merchant, --配送费商家承担
				SUM(other_fee) AS other_fee, --其他费
				SUM(surcharge_amount)      AS surcharge_amount       --附加费
			FROM
				base_tickets
			GROUP BY channel_id
		),
		business_days AS (
			SELECT
				COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_ticket_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY fact.bus_date, {REGION_ID}
			) T
		),
		composition_of_bill_types AS ( --账单类型组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'order_type', order_type, --账单类型
						'order_name', '', --账单类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
					order_type, --账单类型
					SUM(business_amount) AS gross_amount, --流水
					SUM(transfer_real_amount) AS real_amount, --实收
					SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY order_type
			) T
		),
		composition_of_take_away AS ( --外卖渠道组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'gross_amount', gross_amount, --流水
						'real_amount', real_amount,
						'order_count', order_count --单数
					)
				) AS "data",
				SUM(gross_amount) AS gross_amount_total, --流水汇总
				SUM(order_count) AS order_count_total --单数汇总
			FROM (
				SELECT
						channel_id,                       --渠道
						SUM(business_amount) AS gross_amount, --流水
						SUM(transfer_real_amount) AS real_amount, --实收
						SUM(order_count) AS order_count --单数
				FROM base_tickets
				GROUP BY channel_id
			) T
		),
		composition_of_pay AS ( --实收组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'discounts', discounts, --折扣
						'payments', payments, --支付
						'transfer_real_amount_total_payments', transfer_real_amount_total_payments, --支付汇总
					    'transfer_real_amount_total_discounts',transfer_real_amount_total_discounts, --折扣汇总
						'commission', commission, --佣金
						'surcharge_amount', surcharge_amount --附加费
					)
				) AS "data",
				SUM(transfer_real_amount_total_payments+commission+transfer_real_amount_total_discounts+surcharge_amount) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.channel_id,
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
				    COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.transfer_real_amount_total_payments, 0) AS transfer_real_amount_total_payments, --设默认值
				    COALESCE(bd.transfer_real_amount_total_discounts, 0) AS transfer_real_amount_total_discounts, --设默认值
					bt.commission                                        AS commission,                           --佣金
				    bt.surcharge_amount                                  AS surcharge_amount                      --附加费
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON  bt.channel_id = bp.channel_id
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    ON bt.channel_id = bd.channel_id
			) T
		),
		composition_of_discount AS ( --优惠组成
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_name', '', --渠道类型名称
						'send_fee_for_merchant', send_fee_for_merchant, --配送费商家承担
						'other_fee', other_fee, --其他费
						'payments', payments, --支付
					    'discounts', discounts, --折扣
						'discount_contribute_total_payments', discount_contribute_total_payments, --支付汇总
					    'discount_contribute_total_discounts', discount_contribute_total_discounts --折扣汇总
					)
				) AS "data",
				SUM(discount_contribute_total_payments + send_fee_for_merchant + other_fee + discount_contribute_total_discounts) AS real_amount_total --实收金额（实收组成）汇总
			FROM (
				SELECT
					bt.channel_id,
					bt.discount_amount AS discount_amount, --折扣
					bt.send_fee_for_merchant AS send_fee_for_merchant, --配送费商家承担
					bt.other_fee AS other_fee, --其他费
					COALESCE(bp.data, '[]'::json) AS payments, --支付方式
					COALESCE(bd.data, '[]'::json) AS discounts, --折扣方式
					COALESCE(bp.discount_contribute_total_payments, 0) AS discount_contribute_total_payments,
					COALESCE(bd.discount_contribute_total_discounts, 0) AS discount_contribute_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.channel_id = bp.channel_id
						LEFT JOIN base_discounts_by_store_and_channel bd
			                ON bt.channel_id=bd.channel_id
			) T
		),
		composition_of_meal_segment as (
			SELECT
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'meal_segment_name', meal_segment_name,
						'gross_amount', gross_amount,
						'real_amount', real_amount,
						'valid_order_count', valid_order_count
					)
				) AS "data"
			FROM (
				SELECT
					meal_segment_name, --餐段
					SUM(business_amount) AS gross_amount, --流水
					SUM(transfer_real_amount) AS real_amount, --实收
					SUM(order_count) AS valid_order_count --单数
				FROM base_tickets
				where meal_segment_name is not null and meal_segment_name != ''
				GROUP BY meal_segment_name
			) T
		)
		SELECT
			'' AS bus_date,
			0 AS region_id, --店铺ID
			0 as branch_region_id,
			COALESCE(bt.business_amount, 0) AS business_amount, --流水金额（营业流水）
			COALESCE(bt.discount_amount1 - bpns.discount_contribute, 0) AS discount_amount,               --优惠金额（优惠组成）
			COALESCE(bt.real_amount, 0) AS real_amount,--实收金额（实收组成）
			COALESCE(bt.real_amount, 0) / 1.1                AS real_amount_without_gst,--实收不含税
			COALESCE(bt.order_count, 0) AS valid_order_count, --账单数
			coalesce(bt.people, 0) as people, -- 客流
			coalesce(bt.tip, 0) as tip, -- 小费
			coalesce(bt.tax_fee,0) as tax_fee, --税费
			coalesce(bt.bowl_num, 0) as bowl_num, -- 碗数
			COALESCE((CASE WHEN bt.people = 0 THEN 0 ELSE bt.real_amount / bt.people END), 0) AS real_amount_per_capita, --人均消费
			COALESCE((CASE WHEN bt.order_count = 0 THEN 0 ELSE bt.real_amount / bt.order_count END), 0) AS customer_price, --单均消费
			-- 日均客流 客流/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.people / bd.days END), 0) AS customer_count_per_day,
			-- 日均实收: 实收金额/营业天数
			COALESCE((CASE WHEN bd.days = 0 THEN 0 ELSE bt.real_amount / bd.days END), 0) AS real_amount_per_day,
			COALESCE((CASE WHEN bt.bowl_num = 0 THEN 0 ELSE bt.real_amount / bt.bowl_num END), 0) AS real_amount_per_bowl,

			COALESCE(coby.data, '[]'::json) AS composition_of_bill_types, --账单类型组成
			COALESCE(cota.data, '[]'::json) AS composition_of_take_away, --外卖渠道组成

			COALESCE(copi.data, '[]'::json) AS composition_of_paid_in, --实收组成
			COALESCE(copi.real_amount_total, 0) AS composition_of_paid_in_total, --实收组成小计

			COALESCE(cod.data, '[]'::json) AS composition_of_discount, --优惠组成
			COALESCE(cod.real_amount_total, 0) AS composition_of_discount_total, --优惠组成小计

			COALESCE(coms.data, '[]'::json) AS composition_of_meal_segment, --餐段组成
			bt.total AS total --summary时的汇总条数
		FROM
			base_tickets_by_store bt,
			business_days bd,
			composition_of_bill_types coby,
			composition_of_take_away cota,
			composition_of_pay copi,
			composition_of_discount cod,
			composition_of_meal_segment coms,
			base_payments_non_sales bpns
		`
	}

	whereSQL := generateWhereSQLForFinancialReport(condition)
	whereTicketSQL := generateWhereTicketSQLForFinancialReport(condition)
	regionSQL := generateRegionSQLForFinancialReport(condition)
	limitSQL := generateLimitOffsetSQLForFinancialReport(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE_TICKET}", whereTicketSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE_TICKET}", whereTicketSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForFinancialReport(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": //加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForFinancialReport(condition *model.RepoCondition) string {
	regionIdsSQL := repo.GenerateRegionWhereSQL(condition)                    // 查询区域ids
	storeIdsSQL := ""                                                         // 门店ids
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeIdsSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeIdsSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	storeType := "" // 门店类型
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeType = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeType = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	openStatus := "" // 开店状态
	if len(condition.OpenStatus) > 0 {
		openStatus = fmt.Sprintf(
			"AND store_caches.open_status = '%s'",
			condition.OpenStatus,
		)
	}
	dateSQL := "" // 日期
	if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = DATE '%s'
		`, condition.Start.Format("2006-01-02"))
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= DATE '%s'
			AND fact.bus_date <= DATE '%s'
		`, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"))
	}
	storeTagSql := "" // 门店标签
	if len(condition.StoreTags) > 0 {
		storeTagSql = "and ("
		for i, tag := range condition.StoreTags {
			if i > 0 {
				storeTagSql += " or "
			}
			storeTagSql += fmt.Sprintf(` fact.store_tags @> '{"%s"}' `, tag)
		}
		storeTagSql += ")"
	}

	regionCodesSql := "" // 省市区
	if len(condition.RegionCodes) > 0 {
		regionCodesSql = "and ("
		for i, regionCode := range condition.RegionCodes {
			if i > 0 {
				regionCodesSql += " or "
			}
			regionCodesSql += fmt.Sprintf(` store_caches.regions @> '{"%s"}' `, regionCode)
		}
		regionCodesSql += ")"
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		AND fact.scope_id = %d
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, dateSQL, regionIdsSQL, storeType, openStatus, storeIdsSQL, storeTagSql, regionCodesSql)
	return whereSQL
}

func generateWhereTicketSQLForFinancialReport(condition *model.RepoCondition) string {
	if !config.EnableIncludeRealAmountZero[condition.PartnerId] {
		return ""
	}
	// 不包括实收金额为0的数据
	if !condition.IncludeRealAmountZero {
		return `and (fact.amount_0-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount) != 0)`
	}
	return ""
}

func generateLimitOffsetSQLForFinancialReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
