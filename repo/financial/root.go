package financial

import (
	"database/sql"
	"encoding/json"

	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

type FinancialRepositoryPG struct {
	DB *sql.DB
}

func QueryDBWithChan(trackId int64, db *sql.DB, sqlStr string, ch chan []*model.Financial) {
	ch <- QueryDB(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func QueryDB(trackId int64, db *sql.DB, sqlStr string) []*model.Financial {
	results := make([]*model.Financial, 0)

	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(model.Financial)
		var value1, value2, value3, value4, mealSegment json.RawMessage
		if err := r.<PERSON>an(
			&f.Bus<PERSON>ate,
			&f.RegionId,
			&f.BranchRegionId,
			&f.BusinessAmount,
			&f.DiscountAmount,
			&f.RealAmount,
			&f.RealAmountWithoutGST,
			&f.ValidOrderCount,
			&f.CustomerCount,
			&f.Tip,
			&f.TaxFee,
			&f.BowlCount,
			&f.RealAmountPerCapita,
			&f.CustomerPrice,
			&f.CustomerCountPerDay,
			&f.RealAmountPerDay,
			&f.RealAmountPerBowl,
			&value1,
			&value2,
			&value3,
			&f.CompositionOfPaidInTotal,
			&value4,
			&f.CompositionOfDiscountTotal,
			&mealSegment,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(value1, &f.CompositionOfBillTypes)
		if err != nil {
			logger.Pre().Error("财务报表结构体解析失败")
			return results
		}
		err = json.Unmarshal(value2, &f.CompositionOfTakeAway)
		if err != nil {
			logger.Pre().Error("财务报表结构体解析失败")
			return results
		}
		err = json.Unmarshal(value3, &f.CompositionOfPaidIn)
		if err != nil {
			logger.Pre().Error("财务报表结构体解析失败")
			return results
		}
		err = json.Unmarshal(value4, &f.CompositionOfDiscount)
		if err != nil {
			logger.Pre().Error("财务报表结构体解析失败")
			return results
		}
		err = json.Unmarshal(mealSegment, &f.MealSegments)
		if err != nil {
			logger.Pre().Error("财务报表结构体解析失败")
			return results
		}
		results = append(results, f)
	}
	return results
}
