package shopkeeper

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) Detail(ctx context.Context, condition *model.RepoCondition) (*report.DetailForDiscountAndRealAmountResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForDetail(trackId, condition)
	ch := make(chan []*report.Detail, 1)
	go queryDBWithChanForDetail(trackId, k.DB, rowSQL, ch)
	rows := <-ch
	close(ch)
	return &report.DetailForDiscountAndRealAmountResponse{Rows: rows}, nil
}

func queryDBWithChanForDetail(trackId int64, db *sql.DB, sql string, ch chan []*report.Detail) {
	ch <- queryDBForDetail(trackId, db, sql)
}

func queryDBForDetail(trackId int64, db *sql.DB, sqlStr string) []*report.Detail {
	res := make([]*report.Detail, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.Detail)
		var value, val json.RawMessage
		if err := r.Scan(
			&f.StoreId,
			&f.StoreCode,
			&f.StoreName,
			&f.DiscountAmount,
			&f.RealAmount,
			&value,
			&val,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		err := json.Unmarshal(value, &f.CompositionOfPay)
		if err != nil {
			logger.Pre().Error("实收金额CompositionOfPay结构体解析失败：", err)
			return res
		}
		err = json.Unmarshal(val, &f.CompositionOfDiscount)
		if err != nil {
			logger.Pre().Error("优惠组成CompositionOfDiscount结构体解析失败：", err)
			return res
		}
		res = append(res, f)
	}
	return res

}

func getSQLForDetail(trackId int64, condition *model.RepoCondition) string {
	rowSQL := `
		--小掌柜优惠组成和实收金额详细
		WITH base_tickets AS (
			SELECT
				fact.store_id,
				fact.channel_id, --渠道
				fact.order_type, --账单类型
		
				SUM(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount) AS discount_amount1, --优惠金额（优惠组成）[前面的]
				SUM(fact.amount_0-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount)) AS real_amount, --实收金额（实收组成）
		
				SUM(fact.commission) AS commission, --佣金
				SUM(fact.merchant_send_fee) AS send_fee_for_merchant, --配送费商家承担
				SUM(fact.other_fee+fact.rounding) AS other_fee --其他费

			FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches dimension
						ON fact.store_id = dimension.id
			WHERE
				{WHERE}
			GROUP BY fact.store_id, fact.channel_id, fact.order_type
		),
		base_payments AS (
			SELECT
				fact.store_id,
				cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
				fact.payment_id, --支付方式
				COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,--支付编号
                COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,--支付名称
				SUM(fact.merchant_allowance-fact.overflow_amount) AS discount_contribute, --支付优惠组成金额
				SUM(CASE WHEN fact.finance_pay_amount_used THEN fact.finance_pay_amount ELSE fact.tp_allowance + fact.cost END) AS amount
			FROM
				sales_payment_amounts fact
					LEFT JOIN store_caches dimension
						ON fact.store_id = dimension.id
			WHERE
				{WHERE}
			GROUP BY fact.store_id, fact.channel_id, fact.payment_id
		),
		base_discounts AS (
			SELECT
				fact.store_id,
				fact.promotion_id AS discount_id, --折扣
			    fact.channel_id,
				SUM(fact.tp_allowance + fact.cost) AS transfer_real_amount, --折扣实收组成金额
				SUM(fact.merchant_allowance) AS discount_contribute --折扣优惠组成金额
			FROM
				sales_discount_amounts fact
					LEFT JOIN store_caches dimension
						ON fact.store_id = dimension.id
			WHERE
				{WHERE}
				AND (fact.combo_type = 0 OR fact.combo_type = 1)
			GROUP BY fact.store_id, fact.channel_id, fact.promotion_id
		),
		base_payments_by_store_and_channel AS (
			SELECT
				store_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'payment_id', payment_id, --支付方式ID
						'payment_code', payment_code, --支付方式code
						'payment_name', payment_name, --支付方式名称
						'real_amount', amount, --支付实收组成金额
						'discount_contribute', discount_contribute --支付优惠组成金额
					)
				) AS "data",
				sum(amount) as real_amount_total, --支付实收组成金额汇总
				SUM(discount_contribute) AS discount_contribute_total --支付优惠组成金额汇总
			FROM base_payments
			GROUP BY store_id, channel_id
		),
		base_discounts_by_store_and_channel AS (
			SELECT
				store_id,
				channel_id, --渠道类型ID
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'discount_id', discount_id, --折扣方式ID
						'discount_code', '', --折扣方式code
						'discount_name', '', --折扣方式名称
						'real_amount', transfer_real_amount, --折扣实收组成金额
						'discount_contribute', discount_contribute --折扣优惠组成金额
					)
				) AS "data",
				sum(transfer_real_amount) as real_amount_total, --折扣实收组成金额汇总
				SUM(discount_contribute) AS discount_contribute_total --折扣优惠组成金额汇总
			FROM base_discounts
			GROUP BY store_id, channel_id
		),
		base_payments_by_store_and_channel_and_payment AS (
			SELECT
				store_id,
				channel_id, --渠道类型ID
				payment_id, --渠道类型ID
				SUM(amount) AS amount
			FROM base_payments
			GROUP BY store_id, channel_id, payment_id
		),
		base_tickets_by_store AS (
			SELECT
				COUNT(1) AS total, --总条数
				store_id,
				SUM(discount_amount1) AS discount_amount1, --流水金额（营业流水）
				SUM(real_amount) AS real_amount--实收金额（实收组成）
			FROM
				base_tickets
			GROUP BY store_id
		),
		base_tickets_by_store_and_channel AS (
			SELECT
				store_id,
				channel_id,
				SUM(commission) AS commission, --佣金
				SUM(send_fee_for_merchant) AS send_fee_for_merchant, --配送费商家承担
				SUM(other_fee) AS other_fee --其他费
			FROM
				base_tickets
			GROUP BY store_id, channel_id
		),
		composition_of_pay AS ( --实收组成
			SELECT
				store_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_code', '', --渠道code
						'channel_name', '', --渠道类型名称
						'payments', payments, --支付
					    'discounts', discounts,--折扣
					    'real_amount_total_payments', real_amount_total_payments, --支付汇总
					    'real_amount_total_discounts',real_amount_total_discounts --折扣汇总
					)
				) AS "data"
			FROM (
				SELECT
					bt.store_id,
					bt.channel_id,
					coalesce(bp.data, '[]') AS payments, --支付方式
				    coalesce(bd.data, '[]') AS discounts, --折扣方式

					coalesce(bp.real_amount_total, 0) AS real_amount_total_payments,
				    coalesce(bd.real_amount_total, 0) AS real_amount_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.store_id = bp.store_id AND bt.channel_id = bp.channel_id
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    on bd.store_id = bt.store_id AND bt.channel_id = bd.channel_id
			) T
			GROUP BY store_id
		),
		composition_of_discount AS ( --优惠组成
			SELECT
				store_id,
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'channel_id', channel_id, --渠道类型ID
						'channel_code', '', --渠道类型code
						'channel_name', '', --渠道类型名称
						'commission', commission, --佣金
						'send_fee_for_merchant', send_fee_for_merchant, --配送费商家承担
						'other_fee', other_fee, --其他费
						'payments', payments, --支付
					    'discounts', discounts,--折扣
						'discount_contribute_total_payments', discount_contribute_total_payments, --支付汇总
					    'discount_contribute_total_discounts',discount_contribute_total_discounts --折扣汇总
					)
				) AS "data"
			FROM (
				SELECT
					bt.store_id,
					bt.channel_id,
					bt.commission AS commission, --佣金
					bt.send_fee_for_merchant AS send_fee_for_merchant, --配送费商家承担
					bt.other_fee AS other_fee, --其他费
					coalesce(bp.data, '[]') AS payments, --支付方式
				    coalesce(bd.data, '[]') AS discounts, --折扣方式

					coalesce(bp.discount_contribute_total, 0) AS discount_contribute_total_payments,
				    coalesce(bd.discount_contribute_total, 0) AS discount_contribute_total_discounts
				FROM
					base_tickets_by_store_and_channel bt
						LEFT JOIN base_payments_by_store_and_channel bp
							ON bt.store_id = bp.store_id AND bt.channel_id = bp.channel_id
			                LEFT JOIN base_discounts_by_store_and_channel bd
			                    on bd.store_id = bt.store_id AND bt.channel_id = bd.channel_id
			) T
			GROUP BY store_id
		)
		SELECT
			bt.store_id AS store_id, --店铺ID
		    '' AS store_code,--门店code
			'' AS store_name, --店铺名称

			COALESCE(bt.discount_amount1, 0) AS discount_amount, --优惠金额（优惠组成）
			COALESCE(bt.real_amount, 0) AS real_amount,--实收金额（实收组成）

			COALESCE(copi.data, '[]'::json) AS composition_of_paid_in, --实收组成
			COALESCE(cod.data, '[]'::json) AS composition_of_discount --优惠组成
		FROM
			base_tickets_by_store bt
				LEFT JOIN composition_of_pay copi ON bt.store_id = copi.store_id
				LEFT JOIN composition_of_discount cod ON bt.store_id = cod.store_id
 	`
	whereSQL := getWhereSQLForDetail(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getWhereSQLForDetail(condition *model.RepoCondition) string {
	storeIdSQL := ""
	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id in (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL)
	return whereSQL
}
