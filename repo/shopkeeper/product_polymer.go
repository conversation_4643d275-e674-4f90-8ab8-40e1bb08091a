package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) ProductPolymer(ctx context.Context, condition *model.RepoCondition) (*report.ProductPolymerResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForProductPolymer(trackId, condition)
	ch1 := make(chan []*report.ProductPolymer, 1)
	ch2 := make(chan []*report.ProductPolymer, 1)
	go queryDBWithChanForProductPolymer(trackId, k.<PERSON>, rowSQL, ch1)
	go queryDBWithChanForProductPolymer(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductPolymer
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.ProductPolymerResponse{Rows: rows, Summary: summary}, nil
}

func queryDBWithChanForProductPolymer(trackId int64, db *sql.DB, sql string, ch chan []*report.ProductPolymer) {
	ch <- queryDBWithForProductPolymer(trackId, db, sql)
}

func queryDBWithForProductPolymer(trackId int64, db *sql.DB, s string) []*report.ProductPolymer {
	res := make([]*report.ProductPolymer, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.ProductPolymer)
		if err := r.Scan(
			&f.ChannelId,
			&f.ItemCount,
			&f.GrossAmount,
			&f.NetAmount,
			&f.WeightCount,
			&f.Unit,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func getSQLForProductPolymer(trackId int64, condition *model.RepoCondition) (string, string) {
	rowSQL := `
		--小掌柜商品渠道统计 ROWS
		SELECT
            channel_id,
			COALESCE(SUM(qty), 0) AS item_count, --商品数量
			COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
			COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
			COALESCE(SUM(weight),0) AS weight_count,
			coalesce(unit, '') as unit --单位
		FROM
			sales_product_amounts fact
		WHERE
             {WHERE}
        GROUP BY channel_id,unit
	`
	summarySQL := `
	--小掌柜商品渠道统计 SUMMARY
		SELECT
            0 AS channel_id,
			COALESCE(SUM(qty), 0) AS item_count, --商品数量
			COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
			COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
			COALESCE(SUM(weight),0) AS weight_count,
   			'' AS unit
		FROM
			sales_product_amounts fact
		WHERE
             {WHERE}
 		`
	whereSQL := getWhereSQLForProductPolymer(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.getSQLForProductPolymer row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.getSQLForProductPolymer summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getWhereSQLForProductPolymer(condition *model.RepoCondition) string {
	var (
		productIdSQL string // 商品id筛选条件
	)

	if condition.ProductId != 0 {
		productIdSQL = fmt.Sprintf(`
			AND fact.product_id = %d
        `, condition.ProductId)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), productIdSQL)
	return whereSQL
}
