package shopkeeper

import (
    "context"
    "database/sql"
    "fmt"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/model/report"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "strings"
)

const rowsTplProductPeriodSales = `
with product_hours as (
    select fact.*, extract(hour from fact.order_time) as hours
    from sales_product_amounts fact
        left join store_caches store_caches on fact.store_id = store_caches.id
        left join product_caches product_caches on fact.product_id = product_caches.id
    where {WHERE}
),
product_limit_and_order as (
    select fact.product_id,
           coalesce(fact.unit, '') as unit,
           sum(fact.gross_amount) as gross_amount
    from product_hours fact
    group by fact.product_id, fact.unit
    order by gross_amount desc
    {LIMIT}
),
product_rows as (
    select fact.product_id,
           coalesce(fact.unit, '') as unit,
           fact.hours,
           sum(fact.weight) as weight,
           SUM(fact.gross_amount) AS gross_amount,
           SUM(fact.net_amount) AS net_amount,
           SUM(fact.qty) AS item_count
    from product_hours fact  
        inner join product_limit_and_order pl 
            on fact.product_id = pl.product_id and fact.unit = pl.unit
    group by fact.product_id, fact.unit, fact.hours
    order by gross_amount desc, fact.product_id, fact.unit
)
select t.product_id,
       t.unit,
       t.weight,
       t.hours,
       t.gross_amount,
       t.net_amount,
       t.item_count
from product_rows t
`

const summaryTplProductPeriodSales = `
with product_hours as (
    select fact.*, extract(hour from fact.order_time) as hours
    from sales_product_amounts fact
        left join store_caches store_caches on fact.store_id = store_caches.id
        left join product_caches product_caches on fact.product_id = product_caches.id
    where {WHERE}
)
 select 0 as product_id,
        '' as unit,
        0 as weight,
        fact.hours as hours,
        SUM(gross_amount) AS gross_amount,
        SUM(net_amount) AS net_amount,
        SUM(qty) AS item_count
 from product_hours fact
 group by fact.hours
`

const totalTplProductPeriodSales = `
with product_hours as (
    select fact.*, extract(hour from fact.order_time) as hours
    from sales_product_amounts fact
        left join store_caches store_caches on fact.store_id = store_caches.id
        left join product_caches product_caches on fact.product_id = product_caches.id
    where {WHERE}
)
select count(1) as total
from (
    select 1
    from product_hours fact
    group by fact.product_id, fact.unit
) t
`

func (k *KeeperRepositoryImpl) ProductPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.ShopkeeperProductPeriodResponse, error) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))
    rowSQL, summarySQL, totalSQL := renderSqlTplForProductPeriodSales(trackId, condition)
    ch1 := make(chan []*report.ShopkeeperProductPeriodRawRow, 1)
    ch2 := make(chan []*report.ShopkeeperProductPeriodRawRow, 1)
    ch3 := make(chan int64, 1)
    go queryDBWithChanForProductPeriod(trackId, k.DB, rowSQL, ch1)
    go queryDBWithChanForProductPeriod(trackId, k.DB, summarySQL, ch2)
    go queryDBWithChanForProductPeriodTotal(trackId, k.DB, totalSQL, ch3)
    rows := <-ch1
    summarys := <-ch2
    total := <-ch3
    close(ch1)
    close(ch2)
    close(ch3)
    return buildResponseForProductPeriod(rows, summarys, total), nil
}

func buildResponseForProductPeriod(rawRows []*report.ShopkeeperProductPeriodRawRow, rawSummarys []*report.ShopkeeperProductPeriodRawRow, total int64) *report.ShopkeeperProductPeriodResponse {

    // 计算 rows
    // key: product_id + unit
    rowsMap := make(map[string]*report.ShopkeeperProductPeriodRow)
    // 为了保持顺序
    rows := make([]*report.ShopkeeperProductPeriodRow, 0)
    for _, r := range rawRows {
        key := fmt.Sprintf("%d-%s", r.ProductId, r.Unit)
        var row *report.ShopkeeperProductPeriodRow
        if v, exists := rowsMap[key]; exists {
            row = v
        } else {
            rowPeriods := make([]*report.ShopkeeperProductPeriodItem, 0)
            for i := 0; i < 24; i++ {
                rowPeriods = append(rowPeriods, &report.ShopkeeperProductPeriodItem{})
            }
            row = &report.ShopkeeperProductPeriodRow{
                ProductId:   r.ProductId,
                ProductCode: "",
                ProductName: "",
                Periods:     rowPeriods,
                RowSummary:  report.ShopkeeperProductPeriodItem{},
            }
            rowsMap[key] = row
            rows = append(rows, row)
        }
        accumulateForProductPeriod(row.Periods[r.Hour], r)
        accumulateForProductPeriod(&row.RowSummary, r)
    }

    // 计算summary
    periodSummary := make([]*report.ShopkeeperProductPeriodItem, 0)
    for i := 0; i < 24; i++ {
        periodSummary = append(periodSummary, &report.ShopkeeperProductPeriodItem{})
    }
    summary := report.ShopkeeperProductPeriodItem{}

    for _, rawSummary := range rawSummarys {
        accumulateForProductPeriod(periodSummary[rawSummary.Hour], rawSummary)
        accumulateForProductPeriod(&summary, rawSummary)
    }

    return &report.ShopkeeperProductPeriodResponse{
        Rows:          rows,
        PeriodSummary: periodSummary,
        Summary:       &summary,
        TotalRows:     total,
    }
}

func accumulateForProductPeriod(item *report.ShopkeeperProductPeriodItem, row *report.ShopkeeperProductPeriodRawRow) {
    item.Unit = row.Unit
    item.Weight = item.Weight + row.Weight
    item.GrossAmount = item.GrossAmount + row.GrossAmount
    item.NetAmount = item.NetAmount + row.NetAmount
    item.ItemCount = item.ItemCount + row.ItemCount

    if item.Unit != "" || item.Weight != 0 {
        // 两个字段任意一个有值才计算weightCount
        item.WeightCount = fmt.Sprintf("%v %s", item.Weight, item.Unit)
    } else {
        item.WeightCount = ""
    }
}

func queryDBWithChanForProductPeriodTotal(id int64, db *sql.DB, sql string, ch chan int64) {
    ch <- queryDBForProductPeriodTotal(id, db, sql)
}

func queryDBForProductPeriodTotal(trackId int64, db *sql.DB, sqlStr string) int64 {
    var res int64 = 0
    r := db.QueryRow(sqlStr)
    r.Scan(&res)
    return res
}

func queryDBWithChanForProductPeriod(id int64, db *sql.DB, sql string, ch chan []*report.ShopkeeperProductPeriodRawRow) {
    ch <- queryDBForProductPeriod(id, db, sql)
}

func queryDBForProductPeriod(trackId int64, db *sql.DB, sqlStr string) []*report.ShopkeeperProductPeriodRawRow {
    results := make([]*report.ShopkeeperProductPeriodRawRow, 0)
    r, err := db.Query(sqlStr)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        return results
    }
    defer r.Close()

    for r.Next() {
        f := new(report.ShopkeeperProductPeriodRawRow)
        if err := r.Scan(
            &f.ProductId,
            &f.Unit,
            &f.Weight,
            &f.Hour,
            &f.GrossAmount,
            &f.NetAmount,
            &f.ItemCount,
        ); err != nil {
            logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
            return results
        }
        results = append(results, f)
    }
    return results

}

func renderSqlTplForProductPeriodSales(trackId int64, condition *model.RepoCondition) (string, string, string) {
    where := generateWhereForProductPeriodSales(condition)
    limit := generateLimitForProductPeriodSales(condition)

    rows := strings.ReplaceAll(rowsTplProductPeriodSales, "{WHERE}", where)
    rows = strings.ReplaceAll(rows, "{LIMIT}", limit)

    summary := strings.ReplaceAll(summaryTplProductPeriodSales, "{WHERE}", where)
    total := strings.ReplaceAll(totalTplProductPeriodSales, "{WHERE}", where)

    logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl Row SQL: `%s`", trackId, rows)
    logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl Summary SQL: `%s`", trackId, summary)
    logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl Total SQL: `%s`", trackId, total)

    return rows, summary, total
}

func generateLimitForProductPeriodSales(condition *model.RepoCondition) string {
    limitOffsetSQL := ""
    if condition.Limit == -1 {
        return limitOffsetSQL
    }
    if condition.Limit < 0 {
        condition.Limit = 10
    }
    if condition.Offset < 0 {
        condition.Offset = 0
    }
    limitOffsetSQL = fmt.Sprintf(
        "LIMIT %d OFFSET %d",
        condition.Limit, condition.Offset,
    )
    return limitOffsetSQL
}

func generateWhereForProductPeriodSales(condition *model.RepoCondition) string {
    var (
        regionIdsSQL          string // 区域筛选条件
        productIdsSQL         string // 商品筛选条件
        productCategoryIdsSQL string // 类别筛选条件
        storeTypeSQL          string // 门店类型筛选条件
        openStatusSQL         string // 开店类型筛选条件
        periodSQL             string // 时段筛选条件
        comboSQL              string // 套餐类型筛选
    )
    // 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
    if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
        comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
    } else { // 不统计套餐时，仅统计非套餐商品和套餐子项
        comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
    }
    regionIdsSQL = GenerateRegionWhereSQL(condition)
    if len(condition.ProductIds) > 0 {
        productIdsSQL = fmt.Sprintf(
            "AND product_caches.id IN (%s)",
            helpers.JoinInt64(condition.ProductIds, ","),
        )
    }
    if len(condition.ProductCategoryIds) > 0 {
        productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
        )
    }

    // 门店类型支持多选
    if len(condition.StoreTypes) > 0 {
        if len(condition.StoreTypes) == 1 {
            storeTypeSQL = fmt.Sprintf(
                "AND store_caches.store_type = '%s'",
                condition.StoreTypes[0],
            )
        } else {
            storeTypeSQL = fmt.Sprintf(
                "AND store_caches.store_type in (%s)",
                helpers.JoinString(condition.StoreTypes, ","),
            )
        }
    }
    if condition.OpenStatus != "" {
        openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
    }
    if len(condition.Period) != 0 {
        periodSQL = fmt.Sprintf(`
			 AND extract(hour from fact.order_time) IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
    }
    var storeSQL string
    if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
        if len(condition.StoreIDs) == 1 {
            storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
        } else {
            storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
        }
    }
    channelIdSQL := ""
    if condition.ChannelId != 0 {
        channelIdSQL = fmt.Sprintf(
            "AND fact.channel_id = %d",
            condition.ChannelId,
        )
    }
    whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
        condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
        productCategoryIdsSQL, storeTypeSQL, openStatusSQL, periodSQL, storeSQL, comboSQL, channelIdSQL)
    return whereSQL
}
