package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) PeriodNew(ctx context.Context, condition *model.RepoCondition) (*report.PeriodNewResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForPeriodNew(trackId, condition)
	ch1 := make(chan []*report.PeriodNewRow, 1)
	go queryDBWithChanForPeriodNew(trackId, k.<PERSON>, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.PeriodNewResponse{Rows: rows}, nil
}

func queryDBWithChanForPeriodNew(trackId int64, db *sql.DB, sql string, ch chan []*report.PeriodNewRow) {
	ch <- queryDBWithForPeriodNew(trackId, db, sql)
}

func queryDBWithForPeriodNew(trackId int64, db *sql.DB, s string) []*report.PeriodNewRow {
	res := make([]*report.PeriodNewRow, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.PeriodNewRow)
		if err := r.Scan(
			&f.PeriodName,
			&f.PeriodTime,
			&f.RealAmount,
			&f.FinanceRealAmount,
			&f.BusinessAmount,
			&f.ValidTicketCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func getSQLForPeriodNew(trackId int64, condition *model.RepoCondition) (string) {
	rowSQL := `
		--大掌柜新时段报表 ROWS
		with base as (
			select
				case
					when extract(hour from order_time) between 6 and 11 then '早市@6:00-12:00@1'
					when extract(hour from order_time) between 12 and 13 then '午市@12:00-14:00@2'
					when extract(hour from order_time) between 14 and 17 then '下午@14:00-18:00@3'
					when extract(hour from order_time) between 18 and 24 or extract(hour from order_time) between 0 and 5 then '晚市@18:00-次日6:00@4'
					end as period_name,
				fact.*
			from sales_ticket_amounts fact
			where {WHERE}
		)
		select
			   split_part(period_name, '@', 1) as period_name,
			   split_part(period_name, '@', 2) as period_time,
			   SUM(amount_2) AS real_amount,
			   sum(amount_0 - (discount_merchant_contribute + commission + other_fee + merchant_send_fee + pay_merchant_contribute + rounding - overflow_amount))
				   as finance_real_amount,
			   sum(amount_0) as business_amount,
			   sum(eticket_count) as valid_ticket_count
		from base
		group by period_name
		order by split_part(period_name, '@', 3)
	`
	whereSQL := getWhereSQLForPeriodNew(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	return rowSQL
}

func getWhereSQLForPeriodNew(condition *model.RepoCondition) string {
	var (
		storeIdSQL   string // 门店筛选条件
		channelIdSQL string // 渠道筛选条件
	)

	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(`
			AND fact.channel_id = %d
        `, condition.ChannelId)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, channelIdSQL)
	return whereSQL
}
