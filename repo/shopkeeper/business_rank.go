package shopkeeper

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (k *KeeperRepositoryImpl) BusinessRank(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	var (
		rowSQL string        // 查询语句
		rows   []interface{} // 查询结果
	)
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL = getSQLForBusinessTop10Rank(trackId, condition)
	rowCh := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, rowCh)
	rows = <-rowCh
	close(rowCh)
	return &model.Response{
		Rows: rows,
	}, nil
}

func getSQLForBusinessTop10Rank(trackId int64, condition *model.RepoCondition) string {
	rowSQL := `
	--小掌柜商品Top10排行
		WITH base_tickets AS (
			SELECT
				store_id,
				SUM(amount_0) AS business_amount, --营业额
				SUM(amount_2 - rounding) AS real_amount, --实收金额
				COALESCE(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS finance_real_amount, --实收金额（财务）
				SUM(discount_amount) AS discount_amount, --折扣金额
				SUM(merchant_discount_amount + store_discount_amount) AS merchant_allowance, --商家补贴
				SUM(eticket_count) AS valid_order_count, --有效订单数
				SUM(commission) AS commission --佣金
			FROM
				sales_ticket_amounts
			WHERE
				{WHERE}
			GROUP BY store_id
		)
		SELECT
			store_id,
			business_amount,
			real_amount,
		    finance_real_amount,
			discount_amount,
			merchant_allowance,
			valid_order_count,
			commission
		FROM base_tickets
		ORDER BY business_amount DESC
`
	whereSQL := getWhereSQLForBusinessTop10Rank(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getWhereSQLForBusinessTop10Rank(condition *model.RepoCondition) string {
	storeIdsSQL := ""
	if len(condition.RegionSearchIds) > 0 {
		storeIdsSQL = fmt.Sprintf(
			"AND store_id IN (%s)",
			helpers.JoinInt64(condition.RegionSearchIds, ","),
		)
	}
	whereSQL := fmt.Sprintf(`
		partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdsSQL)
	return whereSQL
}
