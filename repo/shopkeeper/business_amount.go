package shopkeeper

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
	"time"
)

// BusinessAmount 小掌柜-营业额
func (k *KeeperRepositoryImpl) BusinessAmount(ctx context.Context, condition *model.RepoCondition) (*report.BusinessSituationResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL, rowCompareSQL, rowCompareSameSQL := generateQuerySQLForBusinessAmount(trackId, condition)
	ch1 := make(chan []*report.BusinessSituation, 1)
	ch2 := make(chan []*report.BusinessSituation, 1)
	ch3 := make(chan []*report.BusinessSituation, 1)
	start := time.Now()
	logger.Pre().Infof("查询开始\n")
	go queryDBWithChanForBusinessSituation(trackId, k.DB, rowSQL, ch1)
	go queryDBWithChanForBusinessSituation(trackId, k.DB, rowCompareSQL, ch2)
	go queryDBWithChanForBusinessSituation(trackId, k.DB, rowCompareSameSQL, ch3)
	rows := <-ch1
	comparesRows := <-ch2
	compareSamesRows := <-ch3
	close(ch1)
	close(ch2)
	close(ch3)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	return &report.BusinessSituationResponse{Rows: rows, CompareRows: comparesRows, CompareSameRows: compareSamesRows}, nil
}

func queryDBWithChanForBusinessSituation(id int64, db *sql.DB, sql string, ch chan []*report.BusinessSituation) {
	ch <- queryDBForBusinessSituation(id, db, sql)
}

func queryDBForBusinessSituation(trackId int64, db *sql.DB, s string) []*report.BusinessSituation {
	results := make([]*report.BusinessSituation, 0)
	if s == "" { // sql为空时，返回一个只含有一个初始为零值的结构体的数组
		return []*report.BusinessSituation{new(report.BusinessSituation)}
	}
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.BusinessSituation)
		var value json.RawMessage
		if err := r.Scan(
			&f.StoreNumber,
			&f.ProductCount,
			&f.CupCount,
			&f.BusinessAmount,
			&f.ExpendAmount,
			&f.RealAmount,
			&f.GrossAmount,
			&f.NetAmount,
			&f.DiscountAmount,
			&f.RefundOrderCount,
			&f.ValidOrderCount,
			&f.CustomerPrice,
			&f.MerchantAllowance,
			&f.TransferRealAmount,
			&f.DiscountContribute,
			&f.AverageBusinessAmount,
			&f.AverageValidOrderCount,
			&value,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 浮点数保留两位有效数字
		f.BusinessAmount = report_v2.SaveTwoEffectiveDigits(f.BusinessAmount)
		f.ExpendAmount = report_v2.SaveTwoEffectiveDigits(f.ExpendAmount)
		f.RealAmount = report_v2.SaveTwoEffectiveDigits(f.RealAmount)
		f.GrossAmount = report_v2.SaveTwoEffectiveDigits(f.GrossAmount)
		f.NetAmount = report_v2.SaveTwoEffectiveDigits(f.NetAmount)
		f.DiscountAmount = report_v2.SaveTwoEffectiveDigits(f.DiscountAmount)
		f.CustomerPrice = report_v2.SaveTwoEffectiveDigits(f.CustomerPrice)
		f.MerchantAllowance = report_v2.SaveTwoEffectiveDigits(f.MerchantAllowance)
		f.DiscountContribute = report_v2.SaveTwoEffectiveDigits(f.DiscountContribute)
		f.TransferRealAmount = report_v2.SaveTwoEffectiveDigits(f.TransferRealAmount)
		f.AverageBusinessAmount = report_v2.SaveTwoEffectiveDigits(f.AverageBusinessAmount)
		f.AverageValidOrderCount = report_v2.SaveTwoEffectiveDigits(f.AverageValidOrderCount)
		err = json.Unmarshal(value, &f.Child)
		if err != nil {
			logger.Pre().Error("小掌柜营业概况child结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

// 生成营业状况sql
func generateQuerySQLForBusinessAmount(trackId int64, condition *model.RepoCondition) (string, string, string) {
	var (
		rowSQL            string // 查询
		rowCompareSQL     string // 环比查询
		rowCompareSameSQL string // 同比查询
	)
	querySQL := `
		--小掌柜营业概况
			WITH base_store AS ( --门店数sql
					select
						count(*) AS store_number
					from (
						SELECT
							store_id
						FROM
							sales_ticket_amounts fact
							LEFT JOIN store_caches ON fact.store_id = store_caches.id
							WHERE
                                {WHERE}
							GROUP BY
								fact.store_id
					)T
			), base_products AS ( --商品销售数量sql
				select
					sum(qty) as item_count, --销售数量
					sum(case when product_caches.has_cup then qty else 0 end) as cup_count--杯数
				from sales_product_amounts fact
					left join store_caches on store_caches.id = fact.store_id
					left join product_caches on fact.product_id = product_caches.id
				where
                    {WHERE}
					AND (fact.combo_type = 0 OR fact.combo_type = 2) --统计套餐头(combo_type=1)和单品(combo_type=0)
			), base_tickets as (
			    select
					order_type as order_type, --订单类型 堂食、外卖
			        SUM(amount_0) AS business_amount,--营业额
					SUM(amount_1+rounding) AS expend_amount,--支出交易
					SUM(fact.amount_0-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount)) AS real_amount, --实收金额
					SUM(gross_amount) AS gross_amount,--商品流水
					SUM(net_amount) AS net_amount,--商品实收
					SUM(discount_amount) AS discount_amount,--折扣金额
					SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END) AS refund_order_count,--退单数
					SUM(eticket_count) AS valid_order_count,--有效订单数
					SUM(merchant_discount_amount+store_discount_amount) AS merchant_allowance,--商家补贴
					SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount, --实收组成财务
					SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute --优惠组成财务
			    from
			        sales_ticket_amounts fact
			        left join store_caches on fact.store_id = store_caches.id
			    where
			        {WHERE}
			    group by
			        order_type
            )
			SELECT
				COALESCE(MAX(base_store.store_number), 0) AS  store_number, --门店数
				COALESCE(MAX(base_products.item_count), 0) AS product_count, --商品销售数量
				COALESCE(MAX(base_products.cup_count), 0) AS cup_count, --商品杯数
			    COALESCE(sum(business_amount), 0) as business_amount,--营业额
			    COALESCE(sum(expend_amount), 0) as expend_amount,--支出交易
			    COALESCE(sum(real_amount), 0) as real_amount,--实收金额交易
			    COALESCE(sum(gross_amount), 0) as gross_amount,--商品流水
			    COALESCE(sum(net_amount), 0) as net_amount,--商品实收
			    COALESCE(sum(discount_amount), 0) as discount_amount,--折扣金额
			    COALESCE(sum(refund_order_count), 0) as refund_order_count,--退单数
			    COALESCE(sum(valid_order_count), 0) as valid_order_count,--有效订单数
			    COALESCE((case when sum(valid_order_count) = 0 then 0 else sum(transfer_real_amount)/sum(valid_order_count) end), 0) AS customer_price, --单均价
			    COALESCE(sum(merchant_allowance), 0) as merchant_allowance,--商家补贴
			    COALESCE(sum(transfer_real_amount), 0) as transfer_real_amount, --实收组成财务
			    COALESCE(sum(discount_contribute), 0) as discount_contribute,--优惠组成财务
				COALESCE((case when max(base_store.store_number)=0 then 0 else sum(business_amount)/max(base_store.store_number) end), 0) as average_business_amount, --店均营业额
            	COALESCE((case when max(base_store.store_number)=0 then 0 else sum(valid_order_count)/max(base_store.store_number) end), 0) as average_valid_order_count, --店均订单数

			    COALESCE(json_agg(
			        json_build_object(
			            'order_type', order_type,
			            'business_amount', business_amount,
						'transfer_real_amount', transfer_real_amount
                        )
                    ), '[]'::json) as "child"
			FROM
				base_tickets,
				base_store,base_products;
			`
	whereSQL, whereCompareSQL, whereCompareSameSQL := generateWhereSqlForBusinessAmount(condition)
	rowSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	if condition.IsCompare {
		// 计算环比的sql
		rowCompareSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSQL)
		logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl compare_row. SQL: `%s`", trackId, rowCompareSQL)
	}

	if condition.IsCompareSame {
		// 计算同比的sql
		rowCompareSameSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSameSQL)
		logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl compare_same_row. SQL: `%s`", trackId, rowCompareSameSQL)
	}
	return rowSQL, rowCompareSQL, rowCompareSameSQL
}

// 生成WhereSQL
func generateWhereSqlForBusinessAmount(condition *model.RepoCondition) (string, string, string) {
	var (
		whereSQL            string // 查询语句
		whereCompareSQL     string // 环比查询
		whereCompareSameSQL string // 同比查询
		storeIdSQL          string // 门店过滤条件
		orderTimeSQL        string // 订单时间过滤条件，仅查询今日有效，并精确到时分秒
	)
	if len(condition.RegionSearchIds) != 0 {
		storeIdSQL = fmt.Sprintf(`
			AND	store_caches.id IN (%s)
 		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	// 如果是今日，精确到时分秒
	if condition.IsToday {
		orderTimeSQL = fmt.Sprintf(`
			AND fact.order_time <= '%s'
	   `, condition.End.Format("2006-01-02 15:04:05"))
	}
	whereSQL = fmt.Sprintf(`
		fact.partner_id = %d
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, orderTimeSQL)

	if condition.IsCompare { // 是否计算环比
		var orderTimeCompareSQL string // 环比今日时分秒
		if condition.IsToday {         // 如果跟今日环比要精确到时分秒
			compareStart := strings.Split(condition.CompareStart, "T")[0]
			compareEnd := strings.Split(condition.CompareEnd, "T")[0]
			if compareStart == compareEnd {
				orderTimeCompareSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareEnd)
			}
		}
		whereCompareSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, condition.CompareStart,
			condition.CompareEnd, storeIdSQL, orderTimeCompareSQL)
	}

	if condition.IsCompareSame { // 是否计算同比
		var orderTimeCompareSameSQL string // 同比今日时分秒
		if condition.IsToday {             // 如果跟今日同比要精确到时分秒
			compareSameStart := strings.Split(condition.CompareSameStart, "T")[0]
			compareSameEnd := strings.Split(condition.CompareSameEnd, "T")[0]
			if compareSameStart == compareSameEnd {
				orderTimeCompareSameSQL = fmt.Sprintf(`AND fact.order_time <= '%s'`, condition.CompareSameEnd)
			}
		}
		whereCompareSameSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, condition.CompareSameStart,
			condition.CompareSameEnd, storeIdSQL, orderTimeCompareSameSQL)
	}
	return whereSQL, whereCompareSQL, whereCompareSameSQL
}
