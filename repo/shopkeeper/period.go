package shopkeeper

import (
    "context"
    "fmt"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "strings"
)

func (k *KeeperRepositoryImpl) Period(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
    var (
        rowSQL     string                 // 查询语句
        summarySQL string                 // 汇总语句
        rows       []interface{}          // 查询结果
        summary    map[string]interface{} // 汇总结果
    )
    trackId := cast.ToInt64(ctx.Value(config.TrackId))
    rowSQL, summarySQL = getSQLForPeriod(trackId, condition)
    rowCh := make(chan []interface{}, 1)
    summaryCh := make(chan []interface{}, 1)
    go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, rowCh)
    go helpers.QueryDBWithChan(trackId, k.DB, summarySQL, summaryCh)
    rows = <-rowCh
    summarys := <-summaryCh
    close(rowCh)
    close(summaryCh)
    if len(summarys) > 0 {
        summary = cast.ToStringMap(summarys[0])
    }
    // 根据rows累加数据，并转换成早中晚
    // 早市6:00-12:00；午市12:00-14:00；下午14:00-18:00；晚市18:00-次日6:00
    return &model.Response{
        Rows:    rows,
        Summary: summary,
    }, nil
}

func getSQLForPeriod(trackId int64, condition *model.RepoCondition) (string, string) {
    var (
        selectSQL  string // select
        fromSQL    string // from
        whereSQL   string // where
        groupSQL   string // group
        orderSQL   string // order
        rowSQL     string // rowSQL = select...from...where...group by...order by...limit...offset
        summarySQL string // summarySQL
    )
    selectSQL = getSelectSQLForPeriod(condition)
    fromSQL = getFromSQLForPeriod(condition)
    whereSQL = getWhereSQLForPeriod(condition)
    groupSQL = getGroupSQLForPeriod(condition)
    orderSQL = getOrderSQLForPeriod(condition)
    rowSQL = fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL)
    rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
    logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

    // 组装汇总查询sql
    summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee,
			SUM(tax_fee) AS tax_fee, 
			SUM(other_fee) AS other_fee,
			SUM(order_count) AS order_count,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
		    SUM(merchant_discount_amount_returned) AS merchant_discount_amount_returned,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned, 
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(commission_returned) AS commission_returned,
			SUM(business_amount) AS business_amount,
			SUM(business_amount_returned) AS business_amount_returned,
			SUM(expend_amount) AS expend_amount,
			SUM(expend_amount_returned) AS expend_amount_returned,
			SUM(real_amount) AS real_amount,
			SUM(real_amount_returned) AS real_amount_returned,
			SUM(projected_income) AS projected_income,
			SUM(projected_income_returned) AS projected_income_returned,
			SUM(discount_amount1) AS discount_contribute,
			SUM(transfer_real_amount) AS transfer_real_amount,
		    SUM(product_count) as product_count
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
    summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
    logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
    return rowSQL, summarySQL
}

func getOrderSQLForPeriod(condition *model.RepoCondition) string {
    if condition.IsPre {
        // todo...
    }
    return `
		DATE_PART('hour', order_time) 
`
}

func getGroupSQLForPeriod(condition *model.RepoCondition) string {
    if condition.IsPre {
        // todo...
    }
    return `
		store_caches.id,
		DATE_PART('hour', order_time)
	`
}

func getWhereSQLForPeriod(condition *model.RepoCondition) string {
    storeIdSQL := ""
    if len(condition.RegionSearchIds) > 0 {
        storeIdSQL = fmt.Sprintf(`
			AND store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
    }
    channelIdSQL := ""
    if condition.ChannelId != 0 {
        channelIdSQL = fmt.Sprintf(`
			AND channel_id = %d
		`, condition.ChannelId)
    }
    whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        %s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
        condition.End.Format("2006-01-02"), storeIdSQL, channelIdSQL)
    return whereSQL
}

func getFromSQLForPeriod(condition *model.RepoCondition) string {
    if condition.IsPre {
        // todo...
    }
    // 为了性能这里多加了一个where
    return `
		sales_ticket_amounts fact
			INNER JOIN store_caches ON fact.store_id = store_caches.id
            left join (
				select sum(qty) as sumqty, eticket_id 
				from sales_product_amounts fact
                where (combo_type=0 or combo_type=2) and {WHERE}
				group by eticket_id
			) p_cnt on fact.eticket_id = p_cnt.eticket_id
`
}

func getSelectSQLForPeriod(condition *model.RepoCondition) string {
    if condition.IsPre {
        // todo...
    }
    return `
		store_caches.id AS region_id,
		DATE_PART('hour', order_time) AS hours,
        SUM(gross_amount) AS gross_amount,
		SUM(package_fee) AS package_fee,
		SUM(delivery_fee) AS delivery_fee,
		SUM(tax_fee) AS tax_fee,
		SUM(other_fee) AS other_fee,
		SUM(merchant_discount_amount) AS merchant_discount_amount,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
		SUM(CASE WHEN refunded THEN merchant_discount_amount ELSE 0 END) AS merchant_discount_amount_returned,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
		SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
		SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
		SUM(commission) AS commission,
		SUM(amount_0) AS business_amount,
		SUM(amount_1) AS expend_amount,
		SUM(amount_2) AS real_amount,
		SUM(amount_3) AS projected_income,
		SUM(amount_4) AS amount_4,
		SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS business_amount_returned,
		SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS expend_amount_returned,
		SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS real_amount_returned,
		SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS projected_income_returned,
		SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned,
		SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_amount1,
		SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,
		SUM(p_cnt.sumqty) as product_count
	`
}
