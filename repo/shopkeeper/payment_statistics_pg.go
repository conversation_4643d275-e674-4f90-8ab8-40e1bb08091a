package shopkeeper

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

func (rsmm *KeeperRepositoryImpl) PaymentStatistics(ctx context.Context, condition *model.RepoCondition) (*report.PaymentStatisticsResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForPaymentStatistics(trackId, condition)
	ch1 := make(chan []*report.PaymentStatistics, 1)
	ch2 := make(chan []*report.PaymentStatistics, 1)
	start := time.Now()
	go queryDBWithChanForPaymentStatistics(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForPaymentStatistics(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.PaymentStatistics
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	var total int64
	if summary != nil {
		total = summary.Total
	}
	return &report.PaymentStatisticsResponse{Rows: rows, Summary: summary, Total: total}, nil
}

func queryDBWithChanForPaymentStatistics(trackId int64, db *sql.DB, sql string, ch chan []*report.PaymentStatistics) {
	ch <- queryDBForPaymentStatistics(trackId, db, sql)
}

func queryDBForPaymentStatistics(trackId int64, db *sql.DB, s string) []*report.PaymentStatistics {
	results := make([]*report.PaymentStatistics, 0)
	if s == "" {
		return []*report.PaymentStatistics{new(report.PaymentStatistics)}
	}
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PaymentStatistics)
		var value json.RawMessage
		if err := r.Scan(
			//			&f.Receivable,
			&f.PayAmount,
			&f.TransferRealAmount,
			//&f.PaymentTransferAmount,
			//&f.Cost,
			//&f.TpAllowance,
			//&f.Rounding,
			//&f.OverflowAmount,
			//&f.ChangeAmount,
			&f.ItemCount,
			&f.ItemCountReturned,
			&value,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		//err = json.Unmarshal(value, &f.Child)
		//if err != nil {
		//	logger.Pre().Error("支付统计child结构体解析失败：", err)
		//	return results
		//}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForPaymentStatistics(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 查询ros
		summarySQL string // 查询summary
	)

	rowSQL = `
		--小掌柜支付统计rows
			WITH base_payments AS (
					SELECT
						cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
						COALESCE(fact.payment_id, 0) AS payment_id,--支付
						COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,--支付编号
						COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,--支付名称
						COALESCE(SUM(receivable), 0) AS receivable, --应付金额
						COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
						COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
						COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
						COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
						COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
						COALESCE(SUM(rounding), 0) AS rounding, --抹零
						COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
						COALESCE(SUM(change_amount), 0) AS change_amount, --找零
						COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
						COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数

					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY
						fact.channel_id,fact.payment_id
					ORDER BY
						fact.channel_id
				), base_payments_for_channel_and_payment AS (
					SELECT
						channel_id,--渠道
                        SUM(receivable) AS receivable, --应付金额
                        SUM(pay_amount) AS pay_amount, --实付金额
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                        SUM(cost) AS cost, --用户实际购买金额
                        SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(item_count) AS item_count, --支付次数
                        SUM(item_count_returned) AS item_count_returned, --支付退单数

						JSON_AGG(
							JSON_BUILD_OBJECT(
								'payment_id', payment_id, --支付id
								'payment_code', payment_code, --支付code
							    'payment_name', payment_name, --支付名称
                                'receivable', receivable,
                                'pay_amount', pay_amount,
                                'transfer_real_amount', transfer_real_amount,
                                'payment_transfer_amount', payment_transfer_amount,
                                'cost', cost,
                                'tp_allowance', tp_allowance,
                                'rounding', rounding,
                                'overflow_amount', overflow_amount,
                                'change_amount', change_amount,
                                'item_count', item_count,
                                'item_count_returned', item_count_returned
							)
						) AS "child"
					FROM  base_payments
					GROUP BY
						channel_id
				), base_payments_for_channel AS (
                	SELECT
                       SUM(receivable)              AS receivable,              --应付金额
                       SUM(pay_amount)              AS pay_amount,              --实付金额
                       SUM(transfer_real_amount)    AS transfer_real_amount,    --财务实收金额(转换后)
                       SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                       SUM(cost)                    AS cost,                    --用户实际购买金额
                       SUM(tp_allowance)            AS tp_allowance,            --第三方补贴金额
                       SUM(rounding)                AS rounding,                --抹零
                       SUM(overflow_amount)         AS overflow_amount,         --溢收
                       SUM(change_amount)           AS change_amount,           --找零
                       SUM(item_count)              AS item_count,              --支付次数
                       SUM(item_count_returned)     AS item_count_returned,     --支付退单数

                       JSON_AGG(
                               JSON_BUILD_OBJECT(
                                       'channel_id', channel_id, --渠道id
                                       'channel_code', '', --渠道code
                                       'channel_name', '', --渠道名称
                                       'receivable', receivable,
                                       'pay_amount', pay_amount,
                                       'transfer_real_amount', transfer_real_amount,
                                       'payment_transfer_amount', payment_transfer_amount,
                                       'cost', cost,
                                       'tp_allowance', tp_allowance,
                                       'rounding', rounding,
                                       'overflow_amount', overflow_amount,
                                       'change_amount', change_amount,
                                       'item_count', item_count,
                                       'item_count_returned', item_count_returned,
                                       'child', child
                                   )
                           )                        AS "child"
                FROM base_payments_for_channel_and_payment
            )
			SELECT
					bt.receivable AS receivable, --应付金额
                    bt.pay_amount AS pay_amount, --实付金额
                    bt.transfer_real_amount AS transfer_real_amount, --财务实收金额(转换后)
                    bt.payment_transfer_amount AS payment_transfer_amount, --支付转折扣
                    bt.cost AS cost, --用户实际购买金额
                    bt.tp_allowance AS tp_allowance, --第三方补贴金额
                    bt.rounding AS rounding, --抹零
                    bt.overflow_amount AS overflow_amount, --溢收
                    bt.change_amount AS change_amount, --找零
                    bt.item_count AS item_count, --支付次数
                    bt.item_count_returned AS item_count_returned, --支付退单数
					bt.child, --渠道合计

					0 AS total --Summary时的汇总条数
				FROM
					 base_payments_for_channel bt
 		`
	summarySQL = `
		--小掌柜支付统计summary
			WITH base_payments AS (
					SELECT
						cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
						COALESCE(fact.payment_id, 0) AS payment_id,--支付
						COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,--支付编号
						COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,--支付名称
						COALESCE(SUM(receivable), 0) AS receivable, --应付金额
						COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
						COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
						COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
						COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
						COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
						COALESCE(SUM(rounding), 0) AS rounding, --抹零
						COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
						COALESCE(SUM(change_amount), 0) AS change_amount, --找零
						COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
						COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY
						channel_id,payment_id
					ORDER BY
						channel_id
				)
				SELECT
					SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数

					'[]'::json AS child, --渠道合计

					COUNT(1) AS total --Summary时的汇总条数
				FROM
					base_payments
 		`

	whereSQL := generateWhereSQLForPaymentStatistics(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForPaymentStatistics(condition *model.RepoCondition) string {
	var storeIdSQL string // 门店过滤条件
	if len(condition.RegionSearchIds) != 0 {
		storeIdSQL = fmt.Sprintf(`
			AND	store_caches.id IN (%s)
 		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL)
	return whereSQL
}
