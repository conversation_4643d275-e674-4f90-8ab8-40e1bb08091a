package shopkeeper

import (
    "context"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/model/report"
)

type KeeperInterface interface {
    //  首页接口
    Dashboard(ctx context.Context, condition *model.RepoCondition) (*report.DashBoardResponse, error)
    BusinessAmount(ctx context.Context, condition *model.RepoCondition) (*report.BusinessSituationResponse, error)
    Period(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    Channel(ctx context.Context, condition *model.RepoCondition) (*report.ChannelDistributeResponse, error)
    ProductRank(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    LogisticsRank(ctx context.Context, condition *model.RepoCondition) (*report.LogisticsRankResponse, error)
    PlatformDiscount(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    IncomeTrend(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    //  下钻接口
    BusCategory(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    ChannelTrend(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    LogisticsTrend(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    BusPeriod(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    DiscountCat(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    DiscountChannel(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    OrderAndRefundChannel(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    ChaOrderDetailed(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    // 客流构成
    CustomerForm(ctx context.Context, condition *model.RepoCondition) (*report.CustomerFormResponse, error)
    // 门店营业额Top10排行
    BusinessRank(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
    // 商品分布
    ProductDistribute(ctx context.Context, condition *model.RepoCondition) (*report.ProductDistributeResponse, error)
    Detail(ctx context.Context, condition *model.RepoCondition) (*report.DetailForDiscountAndRealAmountResponse, error)
    // Deprecated: Use ProductTopNRank instead.
    ProductTop20Rank(ctx context.Context, condition *model.RepoCondition) (*report.ProductTop20RankResponse, error)
    ProductTopNRank(ctx context.Context, condition *model.RepoCondition) (*report.ProductTopNRankResponse, error)
    ProductStoreRank(ctx context.Context, condition *model.RepoCondition) (*report.ProductStoreRankResponse, error)
    // 商品属性下钻
    ProductAttribute(ctx context.Context, condition *model.RepoCondition) (*report.ProductAttributeResponse, error)
    // 支付统计
    PaymentStatistics(ctx context.Context, condition *model.RepoCondition) (*report.PaymentStatisticsResponse, error)
    // 统计渠道商品数据
    ProductPolymer(ctx context.Context, condition *model.RepoCondition) (*report.ProductPolymerResponse, error) // 统计渠道商品数据
    // 商品时段
    ProductPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.ShopkeeperProductPeriodResponse, error)
    DiscountTopNRank(ctx context.Context, condition *model.RepoCondition) (*report.DiscountTopNRankResponse, error)
    PeriodNew(ctx context.Context, condition *model.RepoCondition) (*report.PeriodNewResponse, error)
}
