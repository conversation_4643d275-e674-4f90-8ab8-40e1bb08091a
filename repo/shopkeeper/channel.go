package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) Channel(ctx context.Context, condition *model.RepoCondition) (*report.ChannelDistributeResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL, rowCompareSQL, rowCompareSameSQL := getSQLForChannel(trackId, condition)
	ch1 := make(chan []*report.ChannelDistribute, 1) // 查询rows
	ch2 := make(chan []*report.ChannelDistribute, 1) // 查询summary
	ch3 := make(chan []*report.ChannelDistribute, 1) // 环比查询
	ch4 := make(chan []*report.ChannelDistribute, 1) // 同比查询
	go queryDBWithChanForChannel(trackId, k.DB, rowSQL, ch1)
	go queryDBWithChanForChannel(trackId, k.DB, summarySQL, ch2)
	go queryDBWithChanForChannel(trackId, k.DB, rowCompareSQL, ch3)
	go queryDBWithChanForChannel(trackId, k.DB, rowCompareSameSQL, ch4)
	rows := <-ch1
	summarys := <-ch2
	compareRows := <-ch3
	compareSameRows := <-ch4
	close(ch1)
	close(ch2)
	close(ch3)
	close(ch4)
	var summary *report.ChannelDistribute
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.ChannelDistributeResponse{
		Rows:            rows,
		CompareRows:     compareRows,
		CompareSameRows: compareSameRows,
		Summary:         summary,
	}, nil
}

func queryDBWithChanForChannel(trackId int64, db *sql.DB, sql string, ch chan []*report.ChannelDistribute) {
	ch <- queryDBForChannel(trackId, db, sql)
}

func queryDBForChannel(trackId int64, db *sql.DB, sqlStr string) []*report.ChannelDistribute {
	results := make([]*report.ChannelDistribute, 0)
	if sqlStr == "" { // sql为空时，返回一个只含有一个初始为零值的结构体的数组
		return []*report.ChannelDistribute{new(report.ChannelDistribute)}
	}
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ChannelDistribute)
		if err := r.Scan(
			&f.ChannelId,
			&f.BusinessAmount,
			&f.ProportionBusinessAmount,
			&f.RealAmount,
			&f.ProportionRealAmount,
			&f.ValidOrderCount,
			&f.ProportionValidOrderCount,
			&f.RefundOrderCount,
			&f.ProportionRefundOrderCount,
			&f.DiscountContribute,
			&f.ProportionDiscountContribute,
			&f.TransferRealAmount,
			&f.ProportionTransferRealAmount,
			&f.DiscountAmount,
			&f.GrossAmountReturned,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 浮点数保留两位有效数字
		f.BusinessAmount = report_v2.SaveTwoEffectiveDigits(f.BusinessAmount)
		f.RealAmount = report_v2.SaveTwoEffectiveDigits(f.RealAmount)
		f.DiscountAmount = report_v2.SaveTwoEffectiveDigits(f.DiscountAmount)
		f.DiscountContribute = report_v2.SaveTwoEffectiveDigits(f.DiscountContribute)
		f.TransferRealAmount = report_v2.SaveTwoEffectiveDigits(f.TransferRealAmount)
		f.DiscountAmount = report_v2.SaveTwoEffectiveDigits(f.DiscountAmount)
		f.GrossAmountReturned = report_v2.SaveTwoEffectiveDigits(f.GrossAmountReturned)
		results = append(results, f)
	}
	return results
}

func getSQLForChannel(trackId int64, condition *model.RepoCondition) (string, string, string, string) {
	var (
		rowSQL            string // 查询
		rowCompareSQL     string // 环比
		rowCompareSameSQL string // 同比查询
	)
	querySQL := `
		--小掌柜渠道分布 ROWS
			WITH base_tickets AS (
				SELECT
						COALESCE(channel_id, 0) AS channel_id, --渠道id
						COALESCE(SUM(amount_0), 0) AS business_amount, --营业额
						COALESCE(SUM(amount_2-rounding), 0) AS real_amount, --实收金额(交易)
						COALESCE(SUM(discount_amount), 0) AS discount_amount, --折扣
						COALESCE(SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END), 0) AS gross_amount_returned, --退单金额
						COALESCE(SUM(eticket_count), 0) AS valid_order_count, --有效单数
						COALESCE(SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END), 0)  AS refund_order_count, --退单数
						COALESCE(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute, --优惠组成（财务）
						COALESCE(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount --实收金额转换后（财务）
				FROM
				sales_ticket_amounts fact
					LEFT JOIN store_caches ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY channel_id
			), base_tickets_by_window AS (
				SELECT
					channel_id,
					business_amount,
					real_amount,
					discount_amount,
					gross_amount_returned,
					valid_order_count,
					refund_order_count,
					discount_contribute,
					transfer_real_amount,
					SUM(business_amount) OVER() AS business_amount_total,
					SUM(valid_order_count) OVER() AS valid_order_count_total,
					SUM(refund_order_count) OVER() AS refund_order_count_total,
					SUM(transfer_real_amount) OVER() AS transfer_real_amount_total,
					SUM(discount_contribute) OVER() AS discount_contribute_total,
					SUM(real_amount) OVER() AS real_amount_total
				FROM
					base_tickets
			) SELECT
				channel_id,
				business_amount, --营业额
				(CASE WHEN business_amount_total <= 0 OR business_amount < 0 THEN 0 ELSE business_amount / business_amount_total END ) AS proportion_business_amount, --营业额占比
				real_amount, --实收金额交易
				(CASE WHEN real_amount_total <= 0 OR real_amount < 0 THEN 0 ELSE real_amount / real_amount_total END ) AS proportion_real_amount, --实收金额交易占比
				valid_order_count, --有效单数
				(CASE WHEN valid_order_count_total <= 0 OR valid_order_count < 0 THEN 0 ELSE valid_order_count / valid_order_count_total END ) AS proportion_valid_order_count, --有效单数占比
				refund_order_count, --退单数
				(CASE WHEN refund_order_count_total <= 0 OR refund_order_count < 0 THEN 0 ELSE refund_order_count / refund_order_count_total END ) AS proportion_refund_order_count, --退单数占比
				discount_contribute, --优惠组成财务
				(CASE WHEN discount_contribute_total <= 0 OR discount_contribute < 0 THEN 0 ELSE discount_contribute / discount_contribute_total END ) AS proportion_discount_contribute, --优惠组成占比
				transfer_real_amount, --实收财务
				(CASE WHEN transfer_real_amount_total <= 0 OR transfer_real_amount < 0 THEN 0 ELSE transfer_real_amount / transfer_real_amount_total END ) AS proportion_transfer_real_amount, --实收财务占比
				discount_amount, --折扣
				gross_amount_returned --退单金额
			  FROM
				base_tickets_by_window
              order by business_amount desc
		`
	whereSQL, whereCompareSQL, whereCompareSameSQL := getWhereSQLForChannel(condition)
	rowSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	// 汇总查询sql
	summarySQL := fmt.Sprintf(`
		--小掌柜渠道分布summary
		select
			0 AS channel_id, --渠道id
			COALESCE(SUM(amount_0), 0) AS business_amount, --营业额
			1 AS proportion_business_amount,
			COALESCE(SUM(amount_2-rounding), 0) AS real_amount, --实收金额(交易)
			1 AS proportion_real_amount,
			COALESCE(SUM(eticket_count), 0) AS valid_order_count, --有效单数
			1 as proportion_valid_order_count,
			COALESCE(SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END), 0)  AS refund_order_count, --退单数
			1 as proportion_refund_order_count,
			COALESCE(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute, --优惠组成（财务
			1 as proportion_discount_contribute,
			COALESCE(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount, --实收金额转换后（财务）
			1 as proportion_transfer_real_amount,
			COALESCE(SUM(discount_amount), 0) AS discount_amount, --折扣
			COALESCE(SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END), 0) AS gross_amount_returned --退单金额
    
		from
			sales_ticket_amounts fact
				left join store_caches on store_caches.id = fact.store_id
		where
			%s
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)

	// 需要计算环比
	if condition.IsCompare {
		rowCompareSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_row. SQL: `%s`", trackId, rowCompareSQL)
	}
	// 需要计算同比
	if condition.IsCompareSame {
		rowCompareSameSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSameSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_same_row. SQL: `%s`", trackId, rowCompareSameSQL)
	}
	return rowSQL, summarySQL, rowCompareSQL, rowCompareSameSQL
}

func getWhereSQLForChannel(condition *model.RepoCondition) (string, string, string) {
	var (
		dateSQL             string
		storeIdSQL          string
		whereSQL            string
		whereCompareSQL     string
		whereCompareSameSQL string
		orderTimeSQL        string
	)
	if len(condition.RegionSearchIds) > 0 {
		if len(condition.RegionSearchIds) == 1 {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id = %d`,
				condition.RegionSearchIds[0])
		} else {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id in (%s)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","))
		}
	}

	if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = '%s'
		`, condition.Start.Format("2006-01-02"))
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= '%s'
			AND fact.bus_date <= '%s'
		`, condition.Start.Format("2006-01-02"),
			condition.End.Format("2006-01-02"))
	}
	// 如果是今日，精确到时分秒
	if condition.IsToday {
		orderTimeSQL = fmt.Sprintf(`
			AND fact.order_time <= '%s'
	   `, condition.End.Format("2006-01-02 15:04:05"))
	}

	whereSQL = fmt.Sprintf(`
			fact.partner_id = %d
			AND fact.scope_id = %d
			%s
			%s
			%s
		`, condition.PartnerId, condition.ScopeId, dateSQL, storeIdSQL, orderTimeSQL)

	if condition.IsCompare { // 是否计算环比
		var orderTimeCompareSQL string // 环比今日时分秒
		if condition.IsToday {         // 如果跟今日环比要精确到时分秒
			compareStart := strings.Split(condition.CompareStart, "T")[0]
			compareEnd := strings.Split(condition.CompareStart, "T")[0]
			if compareStart == compareEnd {
				orderTimeCompareSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareEnd)
			}
		}
		var dateCompareSQL string
		if condition.CompareStart == condition.CompareEnd {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareStart)
		} else {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareStart, condition.CompareEnd)
		}
		whereCompareSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSQL, storeIdSQL, orderTimeCompareSQL)
	}

	if condition.IsCompareSame { // 是否计算同比
		var orderTimeCompareSameSQL string // 同比今日时分秒
		if condition.IsToday {             // 如果跟今日环比要精确到时分秒
			compareSameStart := strings.Split(condition.CompareSameStart, "T")[0]
			compareSameEnd := strings.Split(condition.CompareSameEnd, "T")[0]
			if compareSameStart == compareSameEnd {
				orderTimeCompareSameSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareSameEnd)
			}
		}
		var dateCompareSameSQL string
		if condition.CompareSameStart == condition.CompareSameEnd {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareSameStart)
		} else {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareSameStart, condition.CompareSameEnd)
		}
		whereCompareSameSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSameSQL, storeIdSQL, orderTimeCompareSameSQL)
	}

	return whereSQL, whereCompareSQL, whereCompareSameSQL
}
