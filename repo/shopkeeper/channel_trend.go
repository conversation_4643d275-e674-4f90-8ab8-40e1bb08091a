package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (k *KeeperRepositoryImpl) ChannelTrend(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	var (
		rowSQL     string                 // 查询语句
		summarySQL string                 // 汇总语句
		rows       []interface{}          // 查询结果
		summary    map[string]interface{} // 汇总结果
	)
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL = getSQLForChannelTrend(trackId, condition)
	rowCh := make(chan []interface{}, 1)
	summaryCh := make(chan []interface{}, 1)
	go helpers.QueryDBWith<PERSON>han(trackId, k.DB, rowSQL, rowCh)
	go helpers.QueryDB<PERSON>ith<PERSON>han(trackId, k.DB, summarySQL, summaryCh)
	rows = <-rowCh
	summarys := <-summaryCh
	close(rowCh)
	close(summaryCh)
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{
		Rows:    rows,
		Summary: summary,
	}, nil
}

func getSQLForChannelTrend(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		selectSQL      string // select
		fromSQL        string // from
		whereSQL       string // where
		groupSQL       string // group by
		orderSQL       string // order by
		limitOffsetSQL string // limit offset
		rowSQL         string // rowSQL = select...from...where...group by...order by...limit...offset
		summarySQL     string // summarySQL
	)
	selectSQL = getSelectSQLForChannelTrend(condition)
	fromSQL = getFromSQLForChannelTrend(condition)
	whereSQL = getWhereSQLForChannelTrend(condition)
	groupSQL = getGroupSQLForChannelTrend(condition)
	orderSQL = getOrderSQLForChannelTrend(condition)
	limitOffsetSQL = getLimitOffsetForChannelTrend(condition)
	rowSQL = fmt.Sprintf(`
		--小掌柜渠道趋势
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee,
			SUM(tax_fee) AS tax_fee, 
			SUM(other_fee) AS other_fee,
			SUM(order_count) AS order_count,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
		    SUM(merchant_discount_amount_returned) AS merchant_discount_amount_returned,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned, 
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(commission_returned) AS commission_returned,
			SUM(business_amount) AS business_amount,
			SUM(business_amount_returned) AS business_amount_returned,
			SUM(expend_amount) AS expend_amount,
			SUM(expend_amount_returned) AS expend_amount_returned,
			SUM(real_amount) AS real_amount,
			SUM(real_amount_returned) AS real_amount_returned,
			SUM(projected_income) AS projected_income,
			SUM(projected_income_returned) AS projected_income_returned
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
			ORDER BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getOrderSQLForChannelTrend(condition *model.RepoCondition) string {
	return `
		channel_id
   `
}

func getLimitOffsetForChannelTrend(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func getGroupSQLForChannelTrend(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	groupSQL := ""
	switch condition.PeriodGroupType {
	case "DAY":
		groupSQL = fmt.Sprintf(`
				store_caches.id,
				channel_id,
				bus_date
		`)
	case "MONTH":
		groupSQL = fmt.Sprintf(`
				store_caches.id,
				channel_id,
				bus_date_month
		`)
	}
	return groupSQL
}

func getWhereSQLForChannelTrend(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        AND store_caches.id in (%s)
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), helpers.JoinInt64(condition.RegionSearchIds, ","))
	return whereSQL
}

func getFromSQLForChannelTrend(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
`
}

func getSelectSQLForChannelTrend(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	baseSQL := ""
	switch condition.PeriodGroupType {
	case "DAY":
		baseSQL = fmt.Sprintf(`
				store_caches.id AS region_id,
				channel_id AS channel_id,
				TO_CHAR(bus_date, 'YYYY-MM-DD') AS bus_date,
		`)
	case "MONTH":
		baseSQL = fmt.Sprintf(`
				store_caches.id AS region_id,
				channel_id AS channel_id,
				date_part('month', bus_date_month) AS bus_date,
		`)
	}
	selectSQL := fmt.Sprintf(`
        SUM(gross_amount) AS gross_amount,
		SUM(discount_amount) AS discount_amount,
		SUM(package_fee) AS package_fee,
		SUM(delivery_fee) AS delivery_fee,
		SUM(tax_fee) AS tax_fee,
		SUM(other_fee) AS other_fee,
		SUM(rounding) AS rounding,
		SUM(overflow_amount) AS overflow_amount,
		SUM(eticket_count) AS order_count,
		SUM(merchant_discount_amount) AS merchant_discount_amount,
		SUM(CASE WHEN refunded THEN merchant_discount_amount ELSE 0 END) AS merchant_discount_amount_returned,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
		SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
		SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
		SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
		SUM(CASE WHEN refunded THEN rounding ELSE 0 END) AS rounding_returned,
		SUM(CASE WHEN refunded THEN overflow_amount ELSE 0 END) AS overflow_amount_returned,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
		SUM(commission) AS commission,
		SUM(amount_0) AS business_amount,
		SUM(amount_1) AS expend_amount,
		SUM(amount_2) AS real_amount,
		SUM(amount_3) AS projected_income,
		SUM(amount_4) AS amount_4,
		SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS business_amount_returned,
		SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS expend_amount_returned,
		SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS real_amount_returned,
		SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS projected_income_returned,
		SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned
	`)
	return baseSQL + selectSQL
}
