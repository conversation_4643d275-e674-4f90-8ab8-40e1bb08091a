package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 小掌柜-平台补贴
func (k *KeeperRepositoryImpl) PlatformDiscount(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForPlatformDiscount(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

func generateQuerySQLForPlatformDiscount(trackId int64, condition *model.RepoCondition) (string, string) {
	selectSQL := generateSelectSQLForPlatformDiscount(condition)
	fromSQL := generateFromSQLForPlatformDiscount(condition)
	whereSQL := generateWhereSQLForPlatformDiscount(condition)
	orderSQL := generateOrderSQLForPlatformDiscount(condition)
	groupSQL := generateGroupSQLForPlatformDiscount(condition)
	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	// 组装汇总查询sql
	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(platform_discount_amount) AS platform_discount_amount,
			SUM(platform_discount_amount_returned) AS platform_discount_amount_returned,
			SUM(gross_amount) AS gross_amount, 
			SUM(net_amount) AS net_amount, 
			SUM(discount_amount) AS discount_amount, 
			SUM(tip) AS tip, 
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee, 
			SUM(service_fee) AS service_fee, 
			SUM(tax_fee) AS tax_fee, 
			SUM(other_fee) AS other_fee, 
			SUM(pay_amount) AS pay_amount, 
			SUM(rounding) AS rounding, 
			SUM(overflow_amount) AS overflow_amount, 
			SUM(change_amount) AS change_amount, 
			SUM(order_count) AS order_count,
			SUM(gross_amount_returned) AS gross_amount_returned, 
			SUM(net_amount_returned) AS net_amount_returned, 
			SUM(discount_amount_returned) AS discount_amount_returned, 
			SUM(tip_returned) AS tip_returned, 
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned, 
			SUM(service_fee_returned) AS service_fee_returned, 
			SUM(tax_fee_returned) AS tax_fee_returned, 
			SUM(other_fee_returned) AS other_fee_returned, 
			SUM(pay_amount_returned) AS pay_amount_returned, 
			SUM(rounding_returned) AS rounding_returned, 
			SUM(overflow_amount_returned) AS overflow_amount_returned, 
			SUM(change_amount_returned) AS change_amount_returned, 
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(commission_returned) AS commission_returned
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForPlatformDiscount(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        AND store_caches.id in (%s)
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), helpers.JoinInt64(condition.RegionSearchIds, ","))
	return whereSQL
}

func generateGroupSQLForPlatformDiscount(condition *model.RepoCondition) string {
	return `
		channel_id,
		store_caches.id
`
}

func generateOrderSQLForPlatformDiscount(condition *model.RepoCondition) string {
	return "store_caches.id"
}

func generateFromSQLForPlatformDiscount(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
        sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
	`
}

func generateSelectSQLForPlatformDiscount(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
        store_caches.id AS region_id,
        channel_id AS channel_id,
		SUM(net_amount) AS net_amount,
     	SUM(platform_discount_amount) AS platform_discount_amount,
        SUM(gross_amount) AS gross_amount,
		SUM(discount_amount) AS discount_amount,
		SUM(tip) AS tip,
		SUM(package_fee) AS package_fee,
		SUM(delivery_fee) AS delivery_fee,
		SUM(service_fee) AS service_fee,
		SUM(tax_fee) AS tax_fee,
		SUM(other_fee) AS other_fee,
		SUM(pay_amount) AS pay_amount,
		SUM(rounding) AS rounding,
		SUM(overflow_amount) AS overflow_amount,
		SUM(change_amount) AS change_amount,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN platform_discount_amount ELSE 0 END) AS platform_discount_amount_returned,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
		SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
		SUM(CASE WHEN refunded THEN tip ELSE 0 END) AS tip_returned,
		SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
		SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
		SUM(CASE WHEN refunded THEN service_fee ELSE 0 END) AS service_fee_returned,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
		SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
		SUM(CASE WHEN refunded THEN pay_amount ELSE 0 END) AS pay_amount_returned,
		SUM(CASE WHEN refunded THEN rounding ELSE 0 END) AS rounding_returned,
		SUM(CASE WHEN refunded THEN overflow_amount ELSE 0 END) AS overflow_amount_returned,
		SUM(CASE WHEN refunded THEN change_amount ELSE 0 END) AS change_amount_returned,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
		SUM(commission) AS commission,
		SUM(amount_0) AS amount_0,
		SUM(amount_1) AS amount_1,
		SUM(amount_2) AS amount_2,
		SUM(amount_3) AS amount_3,
		SUM(amount_4) AS amount_4,
		SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS amount_0_returned,
		SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS amount_1_returned,
		SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS amount_2_returned,
		SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS amount_3_returned,
		SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned
	`
}
