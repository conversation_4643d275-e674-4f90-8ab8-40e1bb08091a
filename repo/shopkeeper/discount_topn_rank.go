package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) DiscountTopNRank(ctx context.Context, condition *model.RepoCondition) (*report.DiscountTopNRankResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForDiscountTopNRank(trackId, condition)
	ch1 := make(chan []*report.DiscountTopNRank, 1)
	ch2 := make(chan []*report.DiscountTopNRank, 1)
	go queryDBWithChanForDiscountTopNRank(trackId, k.DB, rowSQL, ch1)
	go queryDBWithChanForDiscountTopNRank(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.DiscountTopNRank
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.DiscountTopNRankResponse{Rows: rows, Summary: summary}, nil
}

func queryDBWithChanForDiscountTopNRank(trackId int64, db *sql.DB, sql string, ch chan []*report.DiscountTopNRank) {
	ch <- queryDBWithForDiscountTopNRank(trackId, db, sql)
}

func queryDBWithForDiscountTopNRank(trackId int64, db *sql.DB, s string) []*report.DiscountTopNRank {
	res := make([]*report.DiscountTopNRank, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.DiscountTopNRank)
		if err := r.Scan(
			&f.DiscountName,
			&f.DiscountAmount,
			&f.DiscountCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func getSQLForDiscountTopNRank(trackId int64, condition *model.RepoCondition) (string, string) {
	rowSQL := `
		--小掌柜折扣topN排行 ROWS
		with base as (
			select
				fact.eticket_id,
				fact.promotion_id,
				fact.promotion_name,
				sum(fact.discount_amount) as discount_amount,
				max(fact.qty) as discount_count
			from sales_discount_amounts fact
			where {WHERE}
			group by fact.eticket_id, fact.promotion_id, fact.promotion_name
		)
		select 
			   promotion_name,
			   sum(discount_amount) as discount_amount,
			   sum(discount_count) as discount_count
		from base b
		group by promotion_name
		order by {ORDER_BY}
		{LIMIT}
	`
	summarySQL := `
	--小掌柜折扣topN排行 SUMMARY
		with base as (
			select
				fact.eticket_id,
				fact.promotion_id,
				fact.promotion_name,
				sum(fact.discount_amount) as discount_amount,
				max(fact.qty) as discount_count
			from sales_discount_amounts fact
			where {WHERE}
			group by fact.eticket_id, fact.promotion_id, fact.promotion_name
		)
		select 
			   '' as promotion_name,
			   sum(b.discount_amount) as discount_amount,
			   sum(b.discount_count) as discount_count
		from base b
 		`
	whereSQL := getWhereSQLForDiscountTopNRank(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", generateLimitOffsetSQL(condition))

	orderBy := ""
	if condition.Sort != "" {
		orderBy = condition.Sort + " "
		if condition.Desc{
			orderBy += "DESC"
		} else {
			orderBy += "ASC"
		}
	} else {
		orderBy = "discount_amount DESC"
	}
	rowSQL = strings.ReplaceAll(rowSQL, "{ORDER_BY}", orderBy)

	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getWhereSQLForDiscountTopNRank(condition *model.RepoCondition) string {
	var (
		storeIdSQL   string // 门店筛选条件
		comboSQL     string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, comboSQL)
	return whereSQL
}
