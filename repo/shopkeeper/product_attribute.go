package shopkeeper

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
	"time"
)

func (k *KeeperRepositoryImpl) ProductAttribute(ctx context.Context, condition *model.RepoCondition) (*report.ProductAttributeResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	flavorSQL, graphSQL := getSQLForProductAttribute(trackId, condition)
	ch1 := make(chan []*report.ProductAttribute, 1)
	ch2 := make(chan []*report.ProductAttribute, 1)
	start := time.Now()
	go queryDBWithChanForProductAttribute(trackId, k.DB, flavorSQL, ch1)
	go queryDBWithChanForProductAttribute(trackId, k.DB, graphSQL, ch2)
	rows := <-ch1
	graphs := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗时: %v\n", time.Since(start).Seconds())
	return &report.ProductAttributeResponse{Rows: rows, Graphs: graphs}, nil
}

func queryDBWithChanForProductAttribute(trackId int64, db *sql.DB, sql string, ch chan []*report.ProductAttribute) {
	ch <- queryDBWithForProductAttribute(trackId, db, sql)
}

func queryDBWithForProductAttribute(trackId int64, db *sql.DB, s string) []*report.ProductAttribute {
	res := make([]*report.ProductAttribute, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.ProductAttribute)
		var value1, value2 string
		if err := r.Scan(
			&f.ProductId,
			&f.Flavor,
			&value2,
			&value1,
			&f.ProductCount,
			&f.ProductCountTotal,
			&f.PercentProductCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		if value1 != "" {
			// 加料解析成map：key是加料商品名称，value是其数量
			if err = json.Unmarshal([]byte(value1), &f.Accessories); err != nil {
				logger.Pre().Errorf("accessories解析成map出错：%v", err)
				return res
			}
		}
		if value2 != "" {
			// 属性解析成map：key是属性名，value是属性值
			if err = json.Unmarshal([]byte(value2), &f.SkuRemark); err != nil {
				logger.Pre().Errorf("sku_remark解析成map出错：%v", err)
				return res
			}
		}
		res = append(res, f)
	}
	return res
}

func getSQLForProductAttribute(trackId int64, condition *model.RepoCondition) (string, string) {
	flavorSQL := `
		--小掌柜商品属性下钻口味分布
			with base_products as (
				select
					fact.product_id as product_id,
					fact.flavor as flavor,
					sum(qty) as product_count,
					sum(sum(qty)) over(partition by fact.product_id) as product_count_total
				from    sales_product_amounts fact
					left join store_caches on fact.store_id = store_caches.id
					left join product_caches on fact.product_id=product_caches.id
				where
					{WHERE}
				group by fact.product_id, fact.flavor
				order by product_count desc
			)
			select
				bp.product_id,
				bp.flavor,
				'{}' as sku_remark,
				'{}' as accessories,
				product_count, --商品数量
				product_count_total,
				(case when product_count_total <= 0 or product_count <= 0 then 0 else product_count::numeric/product_count_total::numeric end ) as percent_product_count --数量占比
			from base_products bp;
	`
	graphSQL := `
		--小掌柜商品属性下钻图表
			select
				fact.product_id,
				'' as flavor,
				fact.sku_remark,
				fact.accessories,
				sum(qty) as product_count,
				0 as product_count_total,
				1 as percent_product_count
			from sales_product_amounts fact
				left join store_caches on fact.store_id=store_caches.id
				left join product_caches on fact.product_id=product_caches.id
			where
				{WHERE}
			group by fact.product_id, fact.sku_remark, fact.accessories, eticket_id
 		`
	whereSQL := getWhereSQLForProductAttribute(condition)
	flavorSQL = strings.ReplaceAll(flavorSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, flavorSQL)
	graphSQL = strings.ReplaceAll(graphSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, graphSQL)
	return flavorSQL, graphSQL
}

func getWhereSQLForProductAttribute(condition *model.RepoCondition) string {
	var (
		storeIdSQL   string // 门店筛选条件
		channelIdSQL string // 渠道筛选条件
		productIdSQL string // 商品id
	)

	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(`
			AND fact.channel_id = %d
        `, condition.ChannelId)
	}
	if len(condition.ProductIds) == 1 {
		productIdSQL = fmt.Sprintf(`
			AND fact.product_id = %d
		`, condition.ProductIds[0])
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, channelIdSQL, productIdSQL)
	return whereSQL
}
