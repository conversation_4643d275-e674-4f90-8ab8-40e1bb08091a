package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 小掌柜-菜品收入排行
func (k *KeeperRepositoryImpl) ProductRank(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductRank(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)

	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

func generateQuerySQLForProductRank(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		selectSQL      string // select语句
		whereSQL       string // where语句
		fromSQL        string // from语句
		orderSQL       string // order语句
		groupSQL       string // group语句
		limitOffsetSQL string // limitOffset语句
		rowSQL         string // 查询语句
		summarySQL     string // 汇总查询语句
	)

	selectSQL = generateSelectSQLForProductRank(condition)
	fromSQL = generateFromSQLForProductRank(condition)
	whereSQL = generateWhereSQLForProductRank(condition)
	orderSQL = generateOrderSQLForProductRank(condition)
	groupSQL = generateGroupSQLForProductRank(condition)
	limitOffsetSQL = generateLimitOffsetSQLForProductRank(condition)

	rowSQL = fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
			%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(net_amount) AS net_amount,
			SUM(gross_amount) AS gross_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(item_count) AS item_count,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(item_count_returned) AS item_count_returned,
			SUM(tax_fee) AS tax_fee,
			SUM(tax_fee_returned) AS tax_fee_returned
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateLimitOffsetSQLForProductRank(condition *model.RepoCondition) string {
	var limitOffsetSQL string
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateGroupSQLForProductRank(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		store_caches.id,
		product_caches.id
	`
}

func generateOrderSQLForProductRank(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		net_amount DESC
	`
}

func generateWhereSQLForProductRank(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_product_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        AND store_caches.id in (%s)
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), helpers.JoinInt64(condition.RegionSearchIds, ","))
	return whereSQL
}

func generateFromSQLForProductRank(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		sales_product_amounts
			 LEFT JOIN store_caches ON sales_product_amounts.store_id = store_caches.id
 			 LEFT JOIN product_caches ON sales_product_amounts.product_id = product_caches.ID
	`
}

func generateSelectSQLForProductRank(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
			store_caches.id AS region_id,
			product_caches.id AS product_id,
			product_caches.category0 AS product_category_id,
			SUM(net_amount) AS net_amount,
			(CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(net_amount) / SUM(qty) END) AS net_price,
			SUM(gross_amount) AS gross_amount,
			(CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(gross_amount) / SUM(qty) END) AS gross_price,
			SUM(discount_amount) AS discount_amount,
			SUM(qty) AS item_count,
			SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
			SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
			SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
			SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned,
			SUM(tax_fee) AS tax_fee,
			SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned
`
}
