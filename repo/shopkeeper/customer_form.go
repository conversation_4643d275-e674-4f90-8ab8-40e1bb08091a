package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) CustomerForm(ctx context.Context, condition *model.RepoCondition) (*report.CustomerFormResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL, rowCompareSQL, rowCompareSameSQL := getSQLForCustomerForm(trackId, condition)
	ch1 := make(chan []*report.CustomerForm, 1)
	ch2 := make(chan []*report.CustomerForm, 1)
	ch3 := make(chan []*report.CustomerForm, 1)
	ch4 := make(chan []*report.CustomerForm, 1)
	go queryDBWithChanForCustomerForm(trackId, k.DB, rowSQL, ch1)            // 查询rows
	go queryDBWithChanForCustomerForm(trackId, k.DB, summarySQL, ch2)        // 查询summary
	go queryDBWithChanForCustomerForm(trackId, k.DB, rowCompareSQL, ch3)     // 环比查询
	go queryDBWithChanForCustomerForm(trackId, k.DB, rowCompareSameSQL, ch4) // 同比查询
	rows := <-ch1
	summarys := <-ch2
	compareRows := <-ch3
	compareSameRows := <-ch4
	close(ch1)
	close(ch2)
	close(ch3)
	close(ch4)
	var summary *report.CustomerForm
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.CustomerFormResponse{
		Rows:            rows,
		CompareRows:     compareRows,
		CompareSameRows: compareSameRows,
		Summary:         summary,
	}, nil
}

func queryDBWithChanForCustomerForm(id int64, db *sql.DB, sql string, ch chan []*report.CustomerForm) {
	ch <- queryDBForCustomerForm(id, db, sql)
}

func queryDBForCustomerForm(trackId int64, db *sql.DB, sqlStr string) []*report.CustomerForm {
	if sqlStr == "" { // sql为空时，返回一个只含有一个初始为零值的结构体的数组
		return []*report.CustomerForm{new(report.CustomerForm)}
	}
	results := make([]*report.CustomerForm, 0)

	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.CustomerForm)
		if err := r.Scan(
			&f.Member,
			&f.BusinessAmount,
			&f.ProportionBusinessAmount,
			&f.MerchantAllowance,
			&f.ProportionMerchantAllowance,
			&f.ValidOrderCount,
			&f.ProportionValidOrderCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 浮点数保留两位有效数字
		f.BusinessAmount = report_v2.SaveTwoEffectiveDigits(f.BusinessAmount)
		f.MerchantAllowance = report_v2.SaveTwoEffectiveDigits(f.MerchantAllowance)
		results = append(results, f)
	}
	return results
}

func getSQLForCustomerForm(trackId int64, condition *model.RepoCondition) (string, string, string, string) {
	var (
		rowSQL            string // 查询
		rowCompareSQL     string // 环比
		rowCompareSameSQL string // 同比查询
	)

	querySQL := `
		--小掌柜客流构成 ROWS
			with base_tickets as (
				select
				COALESCE(fact.member, false) as member,
				COALESCE(sum(amount_0), 0) as business_amount,
				COALESCE(sum(merchant_discount_amount+store_discount_amount), 0) as merchant_allowance,
				COALESCE(sum(eticket_count), 0) as valid_order_count
			from
				sales_ticket_amounts fact
					left join store_caches on fact.store_id = store_caches.id
			where
				{WHERE}
			group by member
			), base_tickets_by_window as (
				select
					member,
					business_amount,
					merchant_allowance,
					valid_order_count,
					sum(business_amount) over() as business_amount_total,
					sum(merchant_allowance) over() as merchant_allowance_total,
					sum(valid_order_count) over() as valid_order_count_total
				from base_tickets
			) select
				member,
				business_amount ,
				(case when business_amount_total <= 0 or business_amount < 0 then 0 else business_amount / business_amount_total end ) as proportion_business_amount,
				merchant_allowance,
				(case when merchant_allowance_total <= 0 or merchant_allowance < 0 then 0 else merchant_allowance / merchant_allowance_total end ) as proportion_merchant_allowance,
				valid_order_count,
				(case when valid_order_count_total <= 0 or valid_order_count < 0 then 0 else valid_order_count / valid_order_count_total end ) as proportion_valid_order_count
			  from base_tickets_by_window
			  order by business_amount desc;
	`
	whereSQL, whereCompareSQL, whereCompareSameSQL := getWhereSQLForCustomerForm(condition)
	rowSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	// 汇总查询sql
	summarySQL := fmt.Sprintf(`
	--小掌柜客流构成 SUMMARY
		select
			true as member,
			COALESCE(sum(amount_0), 0) as business_amount,
			1 as proportion_business_amount,
			COALESCE(sum(merchant_discount_amount+store_discount_amount), 0) as merchant_allowance,
			1 as proportion_merchant_allowance,
			COALESCE(sum(eticket_count), 0) as valid_order_count,
			1 as proportion_valid_order_count
		from sales_ticket_amounts fact
			left join store_caches on fact.store_id = store_caches.id
		where
			{WHERE}
	`)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)

	// 需要计算环比
	if condition.IsCompare {
		rowCompareSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_row. SQL: `%s`", trackId, rowCompareSQL)
	}
	// 需要计算同比
	if condition.IsCompareSame {
		rowCompareSameSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereCompareSameSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_same_row. SQL: `%s`", trackId, rowCompareSameSQL)
	}
	return rowSQL, summarySQL, rowCompareSQL, rowCompareSameSQL

}

func getOrderSQLForCustomerForm(condition *model.RepoCondition) string {
	return `business_amount`
}

func getGroupSQLForCustomerForm(condition *model.RepoCondition) string {
	return `member`
}

func getWhereSQLForCustomerForm(condition *model.RepoCondition) (string, string, string) {
	var (
		dateSQL             string
		storeIdSQL          string
		whereSQL            string
		whereCompareSQL     string
		whereCompareSameSQL string
		orderTimeSQL        string
	)
	if len(condition.RegionSearchIds) > 0 {
		if len(condition.RegionSearchIds) == 1 {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id = %d`,
				condition.RegionSearchIds[0])
		} else {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id in (%s)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","))
		}
	}

	if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = '%s'
		`, condition.Start.Format("2006-01-02"))
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= '%s'
			AND fact.bus_date <= '%s'
		`, condition.Start.Format("2006-01-02"),
			condition.End.Format("2006-01-02"))
	}
	// 如果是今日，精确到时分秒
	if condition.IsToday {
		orderTimeSQL = fmt.Sprintf(`
			AND fact.order_time <= '%s'
	   `, condition.End.Format("2006-01-02 15:04:05"))
	}

	whereSQL = fmt.Sprintf(`
			fact.partner_id = %d
			AND fact.scope_id = %d
			%s
			%s
			%s
		`, condition.PartnerId, condition.ScopeId, dateSQL, storeIdSQL, orderTimeSQL)

	if condition.IsCompare { // 是否计算环比
		var orderTimeCompareSQL string // 环比今日时分秒
		if condition.IsToday {         // 如果跟今日环比要精确到时分秒
			compareStart := strings.Split(condition.CompareStart, "T")[0]
			compareEnd := strings.Split(condition.CompareStart, "T")[0]
			if compareStart == compareEnd {
				orderTimeCompareSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareEnd)
			}
		}
		var dateCompareSQL string
		if condition.CompareStart == condition.CompareEnd {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareStart)
		} else {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareStart, condition.CompareEnd)
		}
		whereCompareSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSQL, storeIdSQL, orderTimeCompareSQL)
	}

	if condition.IsCompareSame { // 是否计算同比
		var orderTimeCompareSameSQL string // 同比今日时分秒
		if condition.IsToday {             // 如果跟今日环比要精确到时分秒
			compareSameStart := strings.Split(condition.CompareSameStart, "T")[0]
			compareSameEnd := strings.Split(condition.CompareSameEnd, "T")[0]
			if compareSameStart == compareSameEnd {
				orderTimeCompareSameSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareSameEnd)
			}
		}
		var dateCompareSameSQL string
		if condition.CompareSameStart == condition.CompareSameEnd {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareSameStart)
		} else {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareSameStart, condition.CompareSameEnd)
		}
		whereCompareSameSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSameSQL, storeIdSQL, orderTimeCompareSameSQL)
	}

	return whereSQL, whereCompareSQL, whereCompareSameSQL
}

func getFromSQLForCustomerForm(condition *model.RepoCondition) string {
	return `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
 	`
}

func getSelectSQLForCustomerForm(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		member AS member,
		SUM(amount_0) AS business_amount,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS business_amount_returned,
		SUM(merchant_discount_amount) AS merchant_allowance,
		SUM(store_discount_amount) AS store_discount_amount,
		SUM(CASE WHEN refunded THEN merchant_discount_amount ELSE 0 END) AS merchant_allowance_returned,
		SUM(CASE WHEN refunded THEN store_discount_amount ELSE 0 END) AS store_discount_amount_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
		sum(eticket_count) as valid_order_count
 	`
}
