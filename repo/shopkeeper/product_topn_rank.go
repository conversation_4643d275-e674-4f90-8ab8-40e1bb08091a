package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) ProductTopNRank(ctx context.Context, condition *model.RepoCondition) (*report.ProductTopNRankResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForProductTopNRank(trackId, condition)
	ch1 := make(chan []*report.ProductTopNRank, 1)
	ch2 := make(chan []*report.ProductTopNRank, 1)
	go queryDBWithChanForProductTopNRank(trackId, k.DB, rowSQL, ch1)
	go queryDBWithChanForProductTopNRank(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductTopNRank
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.ProductTopNRankResponse{Rows: rows, Summary: summary}, nil
}

func queryDBWithChanForProductTopNRank(trackId int64, db *sql.DB, sql string, ch chan []*report.ProductTopNRank) {
	ch <- queryDBWithForProductTopNRank(trackId, db, sql)
}

func queryDBWithForProductTopNRank(trackId int64, db *sql.DB, s string) []*report.ProductTopNRank {
	res := make([]*report.ProductTopNRank, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.ProductTopNRank)
		if err := r.Scan(
			&f.ProductId,
			&f.ProductCode,
			&f.ProductName,
			&f.GrossAmount,
			&f.GrossAmountPercent,
			&f.NetAmount,
			&f.NetAmountPercent,
			&f.ItemCount,
			&f.ItemCountPercent,
			&f.WeightCount,
			&f.Unit,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func getSQLForProductTopNRank(trackId int64, condition *model.RepoCondition) (string, string) {
	rowSQL := `
		--小掌柜商品topN排行 ROWS
		WITH base_products AS (
			SELECT
				COALESCE(product_id,0) AS product_id,
				COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
				COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
				COALESCE(SUM(qty), 0) AS item_count, --商品数量
				COALESCE(SUM(weight), 0) AS weight_count, --份量
				coalesce(unit, '') as unit --单位
			FROM
				sales_product_amounts fact
			        LEFT JOIN product_caches ON product_caches.id = fact.product_id
			WHERE
				{WHERE}
			GROUP BY product_id,unit
		),base_products_by_window AS(
		    SELECT
		        product_id,
		        gross_amount,
		        net_amount,
		        item_count,
		        weight_count,
		        unit,
		        SUM(gross_amount) OVER() AS gross_amount_total,
		        SUM(net_amount) OVER() AS net_amount_total,
		        SUM(item_count) OVER() AS item_count_total
		    FROM base_products
        )
		SELECT
			product_id,
		    '' AS product_code,
		    '' AS product_name,
			gross_amount,
		    (CASE WHEN gross_amount_total <= 0 OR gross_amount < 0 THEN  0 ELSE gross_amount::NUMERIC / gross_amount_total::NUMERIC END ) AS gross_amount_percent,
			net_amount,
		    (CASE WHEN net_amount_total <= 0 OR net_amount < 0 THEN  0 ELSE net_amount::NUMERIC / net_amount_total::NUMERIC END ) AS net_amount_percent,
			item_count,
		    (CASE WHEN item_count_total <= 0 OR item_count < 0 THEN  0 ELSE item_count::NUMERIC / item_count_total::NUMERIC END ) AS item_count_percent,
			weight_count,
			unit
		FROM base_products_by_window
        ORDER BY {ORDER_BY}
		{LIMIT}
	`
	summarySQL := `
	--小掌柜商品top20排行 SUMMARY
		SELECT
				0 AS product_id,
                '' AS product_code,
                '' AS product_name,
				COALESCE(SUM(gross_amount), 0) AS gross_amount, --商品流水
                0.0 AS gross_amount_percent,
				COALESCE(SUM(net_amount), 0) AS net_amount, --商品实收
                0.0 AS net_amount_percent,
				COALESCE(SUM(qty), 0) AS item_count, --商品数量
                0.0 AS item_count_percent,
				0.0 AS weight_count,
				'' AS unit
			FROM
				sales_product_amounts fact
			WHERE
                {WHERE}
 		`
	whereSQL := getWhereSQLForProductTopNRank(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", generateLimitOffsetSQL(condition))

	orderBy := ""
	if condition.Sort != "" {
		orderBy = condition.Sort + " "
		if condition.Desc{
			orderBy += "DESC"
		} else {
			orderBy += "ASC"
		}
	} else {
		orderBy = "net_amount DESC"
	}
	rowSQL = strings.ReplaceAll(rowSQL, "{ORDER_BY}", orderBy)

	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getWhereSQLForProductTopNRank(condition *model.RepoCondition) string {
	var (
		storeIdSQL   string // 门店筛选条件
		channelIdSQL string // 渠道筛选条件
		comboSQL     string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(`
			AND fact.channel_id = %d
        `, condition.ChannelId)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, channelIdSQL, comboSQL)
	return whereSQL
}

func generateLimitOffsetSQL(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
