package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (k *KeeperRepositoryImpl) OrderAndRefundChannel(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForOrderAndRefundChannel(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, ch1)
	go helpers.QueryDB<PERSON>ith<PERSON>han(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{
		Rows:    rows,
		Summary: summary,
	}, nil
}

func getSQLForOrderAndRefundChannel(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		selectSQL      string // select
		fromSQL        string // from
		whereSQL       string // where
		groupSQL       string // group
		orderSQL       string // order
		limitOffsetSQL string // limit offset
		rowSQL         string // rowSQL = select...from...where...group by...order by...limit...offset
		summarySQL     string // summarySQL
	)
	selectSQL = getSelectSQLForOrderAndRefundChannel(condition)
	fromSQL = getFromSQLForOrderAndRefundChannel(condition)
	whereSQL = getWhereSQLForOrderAndRefundChannel(condition)
	groupSQL = getGroupSQLForOrderAndRefundChannel(condition)
	orderSQL = getOrderSQLForOrderAndRefundChannel(condition)
	limitOffsetSQL = getLimitOffsetForOrderAndRefundChannel(condition)
	rowSQL = fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(order_count) AS order_count,
			SUM(order_count_returned) AS order_count_returned,
			SUM(order_count_sale) AS order_count_sale,
			SUM(order_count_refund) AS order_count_refund,
			SUM(order_count_partialrefund) AS order_count_partialrefund

		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getLimitOffsetForOrderAndRefundChannel(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func getOrderSQLForOrderAndRefundChannel(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		channel_id
`
}

func getGroupSQLForOrderAndRefundChannel(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		store_caches.id,
		channel_id
	`
}

func getWhereSQLForOrderAndRefundChannel(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        AND store_caches.id in (%s)
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), helpers.JoinInt64(condition.RegionSearchIds, ","))
	return whereSQL
}

func getFromSQLForOrderAndRefundChannel(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		sales_ticket_amounts
			JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
`
}

func getSelectSQLForOrderAndRefundChannel(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		store_caches.id AS region_id,
		channel_id AS channel_id,
		channel_name AS channel_name,
		SUM(gross_amount) AS gross_amount,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund
	`
}
