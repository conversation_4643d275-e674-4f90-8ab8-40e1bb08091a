package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

// 小掌柜-物流占比
func (k *KeeperRepositoryImpl) LogisticsRank(ctx context.Context, condition *model.RepoCondition) (*report.LogisticsRankResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL, compareRowSQL, compareSameRowSQL := generateQuerySQLForLogisticsRank(trackId, condition)
	ch1 := make(chan []*report.LogisticsRank, 1)
	ch2 := make(chan []*report.LogisticsRank, 1)
	ch3 := make(chan []*report.LogisticsRank, 1)
	ch4 := make(chan []*report.LogisticsRank, 1)
	go queryDBWithChanForLogisticsRank(trackId, k.DB, rowSQL, ch1)
	go queryDBWithChanForLogisticsRank(trackId, k.DB, summarySQL, ch2)
	go queryDBWithChanForLogisticsRank(trackId, k.DB, compareRowSQL, ch3)
	go queryDBWithChanForLogisticsRank(trackId, k.DB, compareSameRowSQL, ch4)
	rows := <-ch1
	summarys := <-ch2
	compareRows := <-ch3
	compareSameRows := <-ch4
	close(ch1)
	close(ch2)
	close(ch3)
	close(ch4)
	var summary *report.LogisticsRank
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	return &report.LogisticsRankResponse{
		Rows:            rows,
		CompareRows:     compareRows,
		CompareSameRows: compareSameRows,
		Summary:         summary,
	}, nil
}

func queryDBWithChanForLogisticsRank(trackId int64, db *sql.DB, sql string, ch chan []*report.LogisticsRank) {
	ch <- queryDBForLogisticsRank(trackId, db, sql)
}

func queryDBForLogisticsRank(trackId int64, db *sql.DB, s string) []*report.LogisticsRank {
	if s == "" {
		return []*report.LogisticsRank{new(report.LogisticsRank)}
	}
	results := make([]*report.LogisticsRank, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.LogisticsRank)
		if err := r.Scan(
			&f.OrderType,
			&f.BusinessAmount,
			&f.ProportionBusinessAmount,
			&f.RealAmount,
			&f.ProportionRealAmount,
			&f.ValidOrderCount,
			&f.ProportionValidOrderCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 浮点数保留两位有效数字
		f.BusinessAmount = report_v2.SaveTwoEffectiveDigits(f.BusinessAmount)
		f.RealAmount = report_v2.SaveTwoEffectiveDigits(f.RealAmount)
		f.OrderType = strings.ToLower(f.OrderType)
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForLogisticsRank(trackId int64, condition *model.RepoCondition) (string, string, string, string) {
	var (
		rowSQL              string
		summarySQL          string
		rowCompareSQL       string
		rowCompareSameSQL   string
		whereSQL            string
		compareWhereSQL     string
		compareSameWhereSQL string
	)
	querySQL := `
			--物流构成 ROWS
				with base_tickets as (
					select
					COALESCE(order_type, '') as order_type,
					COALESCE(sum(amount_0), 0) as business_amount,
					COALESCE(sum(amount_2-rounding), 0) as real_amount,
					COALESCE(sum(eticket_count), 0) as valid_order_count
				from
					sales_ticket_amounts fact
					left join store_caches on fact.store_id=store_caches.id
				where
					{WHERE}
				group by
					order_type
				), base_tickets_by_window as (
					select
						order_type,
						business_amount,
						real_amount,
						valid_order_count,
						sum(business_amount) over() as business_amount_total,
						sum(real_amount) over() as real_amount_total,
						sum(valid_order_count) over() as valid_order_count_total
					from
						base_tickets
				)
				select
					order_type, --物流类型
					business_amount, --营业额
					(case when business_amount_total <= 0 or business_amount < 0 then 0 else business_amount / business_amount_total end ) as proportion_business_amount, --营业额占比
					real_amount, --实收交易
					(case when real_amount_total <= 0 or real_amount < 0 then 0 else real_amount / real_amount_total end ) as proportion_real_amount, --实收交易占比
					valid_order_count, --有效订单数
					(case when valid_order_count_total <= 0 or valid_order_count < 0 then 0 else valid_order_count / valid_order_count_total end ) as proportion_valid_order_count--有效订单数占比
				from
					base_tickets_by_window
				order by
					business_amount desc;
    `
	whereSQL, compareWhereSQL, compareSameWhereSQL = generateWhereSQLForLogisticsRank(condition)
	rowSQL = strings.ReplaceAll(querySQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	summarySQL = fmt.Sprintf(`
			select
				'' as order_type,
				COALESCE(sum(amount_0)) as business_amount,
				1 as proportion_business_amount,
				COALESCE(sum(amount_2-rounding)) as real_amount,
				1 as proportion_real_amount,
				COALESCE(sum(eticket_count)) as valid_order_count,
				1 as proportion_valid_order_count
			from
				sales_ticket_amounts fact
				left join store_caches on fact.store_id = store_caches.id
			where
				%s

 	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	// 需要计算环比
	if condition.IsCompare {
		rowCompareSQL = strings.ReplaceAll(querySQL, "{WHERE}", compareWhereSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_row. SQL: `%s`", trackId, rowCompareSQL)
	}
	// 需要计算同比
	if condition.IsCompareSame {
		rowCompareSameSQL = strings.ReplaceAll(querySQL, "{WHERE}", compareSameWhereSQL)
		logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl compare_same_row. SQL: `%s`", trackId, rowCompareSameSQL)
	}
	return rowSQL, summarySQL, rowCompareSQL, rowCompareSameSQL
}

func generateWhereSQLForLogisticsRank(condition *model.RepoCondition) (string, string, string) {
	var (
		dateSQL             string
		storeIdSQL          string
		orderTimeSQL        string
		whereSQL            string
		whereCompareSQL     string
		whereCompareSameSQL string
	)
	if len(condition.RegionSearchIds) > 0 {
		if len(condition.RegionSearchIds) == 1 {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id = %d`,
				condition.RegionSearchIds[0])
		} else {
			storeIdSQL = fmt.Sprintf(`AND store_caches.id in (%s)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","))
		}
	}

	if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date = '%s'
		`, condition.Start.Format("2006-01-02"))
	} else {
		dateSQL = fmt.Sprintf(`
			AND fact.bus_date >= '%s'
			AND fact.bus_date <= '%s'
		`, condition.Start.Format("2006-01-02"),
			condition.End.Format("2006-01-02"))
	}

	// 如果是今日，精确到时分秒
	if condition.IsToday {
		orderTimeSQL = fmt.Sprintf(`
			AND fact.order_time <= '%s'
	   `, condition.End.Format("2006-01-02 15:04:05"))
	}

	whereSQL = fmt.Sprintf(`
			fact.partner_id = %d
			AND fact.scope_id = %d
			%s
			%s
			%s
		`, condition.PartnerId, condition.ScopeId, dateSQL, storeIdSQL, orderTimeSQL)

	if condition.IsCompare { // 是否计算环比
		var orderTimeCompareSQL string // 环比今日时分秒
		if condition.IsToday {         // 如果跟今日环比要精确到时分秒
			compareStart := strings.Split(condition.CompareStart, "T")[0]
			compareEnd := strings.Split(condition.CompareStart, "T")[0]
			if compareStart == compareEnd {
				orderTimeCompareSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareEnd)
			}
		}
		var dateCompareSQL string
		if condition.CompareStart == condition.CompareEnd {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareStart)
		} else {
			dateCompareSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareStart, condition.CompareEnd)
		}
		whereCompareSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSQL, storeIdSQL, orderTimeCompareSQL)
	}

	if condition.IsCompareSame { // 是否计算同比
		var orderTimeCompareSameSQL string // 同比今日时分秒
		if condition.IsToday {             // 如果跟今日环比要精确到时分秒
			compareSameStart := strings.Split(condition.CompareSameStart, "T")[0]
			compareSameEnd := strings.Split(condition.CompareSameEnd, "T")[0]
			if compareSameStart == compareSameEnd {
				orderTimeCompareSameSQL = fmt.Sprintf(`
						AND fact.order_time <= '%s'
	   				`, condition.CompareSameEnd)
			}
		}
		var dateCompareSameSQL string
		if condition.CompareSameStart == condition.CompareSameEnd {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
			`, condition.CompareSameStart)
		} else {
			dateCompareSameSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
			`, condition.CompareSameStart, condition.CompareSameEnd)
		}
		whereCompareSameSQL = fmt.Sprintf(`
				fact.partner_id = %d
				AND fact.scope_id = %d
				%s
				%s
				%s
		`, condition.PartnerId, condition.ScopeId, dateCompareSameSQL, storeIdSQL, orderTimeCompareSameSQL)
	}
	return whereSQL, whereCompareSQL, whereCompareSameSQL
}
