package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

func (k *KeeperRepositoryImpl) ProductStoreRank(ctx context.Context, condition *model.RepoCondition) (*report.ProductStoreRankResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL := getSQLForProductStoreRank(trackId, condition)
	ch1 := make(chan []*report.ProductStoreRank, 1)
	go queryDBWithChanForProductStoreRank(trackId, k.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	return &report.ProductStoreRankResponse{Rows: rows}, nil
}

func queryDBWithChanForProductStoreRank(trackId int64, db *sql.DB, sql string, ch chan []*report.ProductStoreRank) {
	ch <- queryDBWithForProductStoreRank(trackId, db, sql)
}

func queryDBWithForProductStoreRank(trackId int64, db *sql.DB, s string) []*report.ProductStoreRank {
	res := make([]*report.ProductStoreRank, 0)
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.ProductStoreRank)
		if err := r.Scan(
			&f.StoreId,
			&f.Unit,
			&f.GrossAmount,
			&f.GrossAmountPercent,
			&f.NetAmount,
			&f.NetAmountPercent,
			&f.ItemCount,
			&f.ItemCountPercent,
			&f.WeightCount,
			&f.WeightCountPercent,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func getSQLForProductStoreRank(trackId int64, condition *model.RepoCondition) string {
	rowSQL := `
		--小掌柜商品门店排行 ROWS
		with base_products as (
			SELECT
				fact.store_id as store_id,
				coalesce(max(fact.unit), '') as unit, --单位
				round(COALESCE(SUM(gross_amount), 0)::numeric, 2) AS gross_amount, --商品流水
				round(COALESCE(SUM(net_amount), 0)::numeric, 2) AS net_amount, --商品实收
				COALESCE(SUM(qty), 0) AS item_count, --商品数量
				round(COALESCE(SUM(weight), 0)::numeric, 2) AS weight_count --份量
			FROM
				sales_product_amounts fact left join store_caches on fact.store_id = store_caches.id
			WHERE
				{WHERE}
			GROUP BY fact.store_id
		), base_products_by_window as (
		    SELECT
		        store_id,
				unit,
		        gross_amount,
		        net_amount,
		        item_count,
		        weight_count,
		        SUM(gross_amount) OVER() AS gross_amount_total,
		        SUM(net_amount) OVER() AS net_amount_total,
		        SUM(item_count) OVER() AS item_count_total,
		        SUM(weight_count) OVER() AS weight_count_total
		    FROM base_products
		)
		select
			store_id,
			unit,
			gross_amount,
		    (CASE WHEN gross_amount_total <= 0 OR gross_amount < 0 THEN  0 ELSE gross_amount::NUMERIC / gross_amount_total::NUMERIC END ) AS gross_amount_percent,
			net_amount,
		    (CASE WHEN net_amount_total <= 0 OR net_amount < 0 THEN  0 ELSE net_amount::NUMERIC / net_amount_total::NUMERIC END ) AS net_amount_percent,
			item_count,
		    (CASE WHEN item_count_total <= 0 OR item_count < 0 THEN  0 ELSE item_count::NUMERIC / item_count_total::NUMERIC END ) AS item_count_percent,
			weight_count,
		    (CASE WHEN weight_count_total <= 0 OR weight_count < 0 THEN 0 ELSE weight_count::NUMERIC / weight_count_total::NUMERIC END ) AS weight_count_percent
		from base_products_by_window
		ORDER BY {ORDER_BY}
		{LIMIT}
	`
	whereSQL := getWhereSQLForProductStoreRank(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", generateLimitOffsetSQL(condition))

	orderBy := ""
	if condition.Sort != "" {
		orderBy = condition.Sort + " "
		if condition.Desc{
			orderBy += "DESC"
		} else {
			orderBy += "ASC"
		}
	} else {
		orderBy = "net_amount DESC"
	}
	rowSQL = strings.ReplaceAll(rowSQL, "{ORDER_BY}", orderBy)

	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)
	return rowSQL
}

func getWhereSQLForProductStoreRank(condition *model.RepoCondition) string {
	var (
		storeIdSQL   string // 门店筛选条件
		productIdSQL string // 商品筛选条件
		channelIdSQL string // 渠道筛选条件
		comboSQL     string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	if len(condition.RegionSearchIds) > 0 {
		storeIdSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	if len(condition.ProductIds) > 0 {
		productIdSQL = fmt.Sprintf(`
			AND fact.product_id IN (%s)
		`, helpers.JoinInt64(condition.ProductIds, ","))
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(`
			AND fact.channel_id = %d
        `, condition.ChannelId)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d	
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL, channelIdSQL, comboSQL, productIdSQL)
	return whereSQL
}
