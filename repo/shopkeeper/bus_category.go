package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (k *KeeperRepositoryImpl) BusCategory(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := getSQLForBusCategory(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, ch1)
	go helpers.QueryDB<PERSON>ith<PERSON>han(trackId, k.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{
		Rows:    rows,
		Summary: summary,
	}, nil
}

func getSQLForBusCategory(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		selectSQL      string // select
		whereSQL       string // where
		fromSQL        string // from
		orderSQL       string // order by
		groupSQL       string // group by
		limitOffsetSQL string // limit...offset...
		rowSQL         string // row = select...from...where...group by...order by...limit...offset...
		summarySQL     string // summary
	)
	selectSQL = getSelectSQLForBusCategory(condition)
	whereSQL = getWhereSQLForBusCategory(condition)
	fromSQL = getFromSQLForBusCategory(condition)
	groupSQL = getGroupSQLForBusCategory(condition)
	orderSQL = getOrderSQLForBusCategory(condition)
	limitOffsetSQL = getLimitOffsetForBusCategory(condition)
	rowSQL = fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(order_count) AS order_count,
			SUM(net_amount) AS net_amount,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(order_count_sale) AS order_count_sale,
			SUM(order_count_refund) AS order_count_refund,
			SUM(order_count_partialrefund) AS order_count_partialrefund,
			SUM(discount_amount) AS discount_amount,
			SUM(item_count_returned) AS item_count_returned,
			SUM(tax_fee) AS tax_fee,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(item_count) AS item_count
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getLimitOffsetForBusCategory(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func getOrderSQLForBusCategory(condition *model.RepoCondition) string {
	return `
		net_amount DESC
  	`
}

func getGroupSQLForBusCategory(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	category0Id := condition.ProductCategory0Id
	category1Id := condition.ProductCategory1Id
	category2Id := condition.ProductCategory2Id
	groupSQL := fmt.Sprintf(`store_caches.id,`)
	if category0Id == 0 {
		groupSQL += fmt.Sprintf(`product_caches.category0`)
	} else if category1Id == 0 {
		groupSQL += fmt.Sprintf(`product_caches.category1`)
	} else if category2Id == 0 {
		groupSQL += fmt.Sprintf(`product_caches.category2`)
	} else {
		groupSQL += fmt.Sprintf(`product_caches.id`)
	}
	return groupSQL
}

func getWhereSQLForBusCategory(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_product_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"))
	regionIds := helpers.JoinInt64(condition.RegionSearchIds, ",")
	if regionIds != "" {
		whereSQL += fmt.Sprintf(`AND store_caches.id in (%s)`, regionIds)
	}
	category0Id := condition.ProductCategory0Id
	category1Id := condition.ProductCategory1Id
	category2Id := condition.ProductCategory2Id
	if category0Id != 0 {
		whereSQL += fmt.Sprintf(`AND product_caches.category0 = %d `, category0Id)
		if category1Id != 0 {
			whereSQL += fmt.Sprintf(`AND product_caches.category1 = %d `, category1Id)
			if category2Id != 0 {
				whereSQL += fmt.Sprintf(`AND product_caches.category2 = %d `, category2Id)
			}
		}
	}
	return whereSQL
}

func getFromSQLForBusCategory(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		sales_product_amounts
			JOIN store_caches ON sales_product_amounts.store_id = store_caches.id
			JOIN product_caches ON sales_product_amounts.product_id = product_caches.id
`
}

func getSelectSQLForBusCategory(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	storeSQL := fmt.Sprintf(`store_caches.id AS region_id,`)
	var selectSQL string
	category0Id := condition.ProductCategory0Id
	category1Id := condition.ProductCategory1Id
	category2Id := condition.ProductCategory2Id
	if category0Id == 0 {
		selectSQL = fmt.Sprintf(`
				product_caches.category0 AS product_category_id,
		`)
	} else if category1Id == 0 {
		selectSQL = fmt.Sprintf(`
				product_caches.category1 AS product_category_id,
		`)
	} else if category2Id == 0 {
		selectSQL = fmt.Sprintf(`
				product_caches.category2 AS product_category_id,
        `)
	} else {
		selectSQL = fmt.Sprintf(`
				product_caches.id AS product_id,
		`)
	}
	return storeSQL + selectSQL + `
		SUM(gross_amount) AS gross_amount,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		(CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(gross_amount) / SUM(qty) END) AS gross_price,
		SUM(net_amount) AS net_amount,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
		(CASE WHEN SUM(qty) = 0 THEN 0 ELSE SUM(net_amount) / SUM(qty) END) AS net_price,
		SUM(discount_amount) AS discount_amount,
		SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned,
		SUM(qty) AS item_count,
		SUM(tax_fee) AS tax_fee,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned
	`
}
