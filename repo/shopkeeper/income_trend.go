package shopkeeper

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// IncomeTrend 收入趋势
func (k *KeeperRepositoryImpl) IncomeTrend(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	var (
		rowSQL     string                 // 查询语句
		summarySQL string                 // 汇总语句
		rows       []interface{}          // 查询结果
		summary    map[string]interface{} // 汇总结果
	)
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL = getSQLForIncome(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, k.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, k.DB, summarySQL, ch2)
	rows = <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{
		Rows:    rows,
		Summary: summary,
	}, nil
}

func getSQLForIncome(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		selectSQL      string // select
		fromSQL        string // from
		whereSQL       string // where
		groupSQL       string // group
		orderSQL       string // order
		limitOffsetSQL string // limit offset
		rowSQL         string // rowSQL = select...from...where...group by...order by...limit...offset
		summarySQL     string // summarySQL
	)
	selectSQL = getSelectSQLForIncome(condition)
	fromSQL = getFromSQLForIncome(condition)
	whereSQL = getWhereSQLForIncome(condition)
	groupSQL = getGroupSQLForIncome(condition)
	orderSQL = getOrderSQLForIncome(condition)
	limitOffsetSQL = getLimitOffsetForIncome(condition)
	rowSQL = fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL = fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(commission) AS commission,
			SUM(commission_returned) AS commission_returned,
			SUM(business_amount) AS business_amount,
			SUM(business_amount_returned) AS business_amount_returned,
			SUM(expend_amount) AS expend_amount,
			SUM(expend_amount_returned) AS expend_amount_returned,
			SUM(real_amount) AS real_amount,
			SUM(real_amount_returned) AS real_amount_returned,
			SUM(projected_income) AS projected_income,
			SUM(projected_income_returned) AS projected_income_returned

		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, selectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.shopkeeper.KeeperRepositoryImpl summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func getLimitOffsetForIncome(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func getOrderSQLForIncome(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		DATE_PART('month', order_time) 
`
}

func getGroupSQLForIncome(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		store_caches.id,
		DATE_PART('month', order_time)
	`
}

func getWhereSQLForIncome(condition *model.RepoCondition) string {
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
        AND store_caches.id in (%s)
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), helpers.JoinInt64(condition.RegionSearchIds, ","))
	return whereSQL
}

func getFromSQLForIncome(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
`
}

func getSelectSQLForIncome(condition *model.RepoCondition) string {
	if condition.IsPre {
		// todo...
	}
	return `
		store_caches.id AS region_id,
		DATE_PART('month', order_time) AS months,
        SUM(gross_amount) AS gross_amount,
		SUM(package_fee) AS package_fee,
		SUM(delivery_fee) AS delivery_fee,
		SUM(tax_fee) AS tax_fee,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
		SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
		SUM(commission) AS commission,
		SUM(amount_0) AS business_amount,
		SUM(amount_1) AS expend_amount,
		SUM(amount_2) AS real_amount,
		SUM(amount_3) AS projected_income,
		SUM(amount_4) AS amount_4,
		SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS business_amount_returned,
		SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS expend_amount_returned,
		SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS real_amount_returned,
		SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS projected_income_returned,
		SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned
	`
}
