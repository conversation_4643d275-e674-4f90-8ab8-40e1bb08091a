package shopkeeper

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/model/report_v2"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

// 小掌柜-首页数据
func (k *KeeperRepositoryImpl) Dashboard(ctx context.Context, condition *model.RepoCondition) (*report.DashBoardResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL := generateQuerySQLForDashBoard(trackId, condition)
	ch := make(chan []*report.DashBoard, 1)
	go queryDBWithChanForDashBoard(trackId, k.DB, rowSQL, ch)
	rows := <-ch
	close(ch)
	return &report.DashBoardResponse{Rows: rows}, nil
}

func queryDBWithChanForDashBoard(trackId int64, db *sql.DB, sql string, ch chan []*report.DashBoard) {
	ch <- queryDBWithForDashBoard(trackId, db, sql)
}

func queryDBWithForDashBoard(trackId int64, db *sql.DB, s string) []*report.DashBoard {
	results := make([]*report.DashBoard, 0)
	if s == "" { // sql为空时，返回一个只含有一个初始为零值的结构体的数组
		return []*report.DashBoard{new(report.DashBoard)}
	}
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.DashBoard)
		if err := r.Scan(
			&f.BusinessAmount,
			&f.ValidOrderCount,
			&f.CustomerValue,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 浮点数保留两位有效数字
		f.BusinessAmount = report_v2.SaveTwoEffectiveDigits(f.BusinessAmount)
		f.CustomerValue = report_v2.SaveTwoEffectiveDigits(f.CustomerValue)
		results = append(results, f)
	}
	return results
}

// 生成营业状况sql
func generateQuerySQLForDashBoard(trackId int64, condition *model.RepoCondition) string {
	rowsSQL := `
		--小掌柜首页面板
		select
			COALESCE(sum(amount_0), 0) as business_amount,
			COALESCE(sum(eticket_count), 0) as valid_order_count,
			case when COALESCE(sum(eticket_count), 0)=0 then 0 else COALESCE(sum(amount_0), 0)/COALESCE(sum(eticket_count), 0) end as customer_price
		from sales_ticket_amounts_all fact
			left join store_caches on fact.store_id = store_caches.id
		where
			{WHERE}
    `

	whereSQL := generateWhereSqlForDashBoard(condition)
	rowsSQL = strings.ReplaceAll(rowsSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.KeeperRepositoryImpl row. SQL: `%s`", trackId, rowsSQL)
	return rowsSQL
}

func generateWhereSqlForDashBoard(condition *model.RepoCondition) string {
	var storeIdSQL string
	if len(condition.RegionSearchIds) != 0 {
		storeIdSQL = fmt.Sprintf(`
			AND	store_caches.id IN (%s)
 		`, helpers.JoinInt64(condition.RegionSearchIds, ","))
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeIdSQL)
	return whereSQL
}
