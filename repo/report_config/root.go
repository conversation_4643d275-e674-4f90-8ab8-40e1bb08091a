package report_config

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gorm.io/gorm"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"time"
)

type ReportConfigRepoPG struct {
	DB *gorm.DB
}

func (pg *ReportConfigRepoPG) QueryByTypeCurrentUser(ctx context.Context, configType string) (*model.ReportConfig, error) {
	result := new(model.ReportConfig)
	err := pg.DB.Model(&model.ReportConfig{}).
		Where("partner_id = ? and type = ? and user_id = ?",
			cast.ToInt64(ctx.Value("partner_id")),
			configType, cast.ToInt64(ctx.Value("user_id")),
		).
		First(result).Error
	return result, err
}

func (pg *ReportConfigRepoPG) SaveByTypeCurrentUser(ctx context.Context, configType string, value string) (*model.ReportConfig, error) {
	// 先查询，如果没有查到则添加，否则更新
	config := new(model.ReportConfig)
	err := pg.DB.Model(&model.ReportConfig{}).
		Where("partner_id = ? and type = ? and user_id = ?",
			cast.ToInt64(ctx.Value("partner_id")),
			configType, cast.ToInt64(ctx.Value("user_id")),
		).
		First(config).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	if config.Id != 0 {
		config.Config = value
		config.Updated = time.Now().UTC()
	} else {
		config = &model.ReportConfig{
			Id:        uuid.GetId(),
			PartnerId: cast.ToInt64(ctx.Value("partner_id")),
			UserId:    cast.ToInt64(ctx.Value("user_id")),
			Type:      configType,
			Config:    value,
			Created:   time.Now().UTC(),
			Updated:   time.Now().UTC(),
		}
	}
	return config, pg.DB.Save(config).Error
}
