package cash_management

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/cash_management"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	repo "gitlab.hexcloud.cn/histore/sales-report/repo/report"
	"gorm.io/gorm"
	"strings"
)

type PettyCashDailySettlementRepoHolo struct {
	DB *gorm.DB
}

func (r *PettyCashDailySettlementRepoHolo) PettyCashDailySettlementReport(ctx context.Context, condition *model.RepoCondition) (*cash_management.PettyCashDailySettlementReportResp, error) {
	rowSQL, totalSQL, summarySql := generateQuerySQLForPettyCashDailySettlementReport(ctx, condition)
	ch1 := make(chan []*cash_management.PettyCashDailySettlement, 1)
	ch2 := make(chan int64, 1)
	ch3 := make(chan *cash_management.PettyCashDailySettlementSummary, 1)
	go queryDBWithChanForPettyCashDailySettlementReport(ctx, r.DB, rowSQL, ch1)
	go queryDBWithChanForPettyCashDailySettlementReportTotal(ctx, r.DB, totalSQL, ch2)
	go queryDBWithChanForPettyCashDailySettlementReportSummary(ctx, r.DB, summarySql, ch3)
	rows := <-ch1
	total := <-ch2
	summary := <-ch3
	close(ch1)
	close(ch2)
	close(ch3)

	response := &cash_management.PettyCashDailySettlementReportResp{
		Rows:    rows,
		Total:   total,
		Summary: summary,
	}
	return response, nil
}

func generateQuerySQLForPettyCashDailySettlementReport(ctx context.Context, condition *model.RepoCondition) (string, string, string) {
	var (
		rowSQL     string
		totalSQL   string
		summarySQL string
	)

	rowSQL = `
				WITH petty_cash_change AS (
			SELECT
				t.partner_id,
				t.business_date,
				t.business_date_timestamp,
				t.org_id,
				t.org_code,
				t.org_name,
				SUM(CASE  WHEN t.type_code='payout' then t.total_amount ELSE 0 END ) as payout_amount,
				SUM(CASE  WHEN t.type_code in('payin','collectPettyCash' )then t.total_amount ELSE 0 END ) as collect_cash_amount,
				SUM(CASE  WHEN t.type_code in('payin')then t.total_amount ELSE 0 END ) as other_amount
			FROM  events t
			INNER JOIN store_caches 
			ON t.org_id::bigint=store_caches.id
			WHERE  t.sub_type_code='pettyCash'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.partner_id,t.business_date,t.business_date_timestamp, t.org_id, t.org_code, t.org_name
		),
		
		petty_cash_start_balance AS (
			SELECT  t.bus_date  as business_date,t.partner_id,store_id,t.initial_petty_cash from cash_account t
			INNER JOIN store_caches
			ON t.store_id=store_caches.id
			WHERE {WHERE_PETTY_CASH_START_BALANCE}
		),
		payout_detail AS(
			SELECT t.org_id,t.business_date,t.trigger_type_name,(sum(total_amount)::decimal/100)  amount 
			FROM events as t 
			INNER JOIN store_caches
			ON t.org_id::bigint=store_caches.id
			WHERE   t.sub_type_code='pettyCash' 
			AND  t.type_code='payout'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.org_id, t.business_date, t.trigger_type_name),

		all_trigger_types AS (
			SELECT DISTINCT trigger_type_name
			FROM payout_detail
		),
		ordered_payout_detail AS (
			SELECT t.org_id,
				   t.business_date,
				   COALESCE(p.trigger_type_name, a.trigger_type_name) AS trigger_type_name,
				   COALESCE(p.amount, 0) AS amount
			FROM (SELECT DISTINCT org_id, business_date FROM payout_detail) t
			CROSS JOIN all_trigger_types a
			LEFT JOIN payout_detail p ON t.org_id = p.org_id
				AND t.business_date = p.business_date
				AND a.trigger_type_name = p.trigger_type_name
			ORDER BY t.org_id, t.business_date, a.trigger_type_name
		),

		payout_json as(
				SELECT t.org_id,
					   t.business_date,
					   JSON_OBJECT_AGG(t.trigger_type_name, t.amount) AS payout_detail
				FROM ordered_payout_detail t
				GROUP BY t.org_id, t.business_date),
		
		result as (
		SELECT
		coalesce(c.partner_id,b.partner_id) as partner_id,
		coalesce(c.business_date,to_char(b.business_date,'yyyy-mm-dd')) AS business_date,
		coalesce(c.business_date_timestamp,EXTRACT(EPOCH FROM b.business_date)-8*3600) as business_date_timestamp,
		coalesce( c.org_id,b.store_id) AS store_id,
		COALESCE(b.initial_petty_cash,0) AS start_balance,
		COALESCE(c.collect_cash_amount,0) AS collect_cash_amount,
		COALESCE(c.other_amount,0)  AS other_amount,
		COALESCE(c.payout_amount,0)  AS payout_amount,
		COALESCE(b.initial_petty_cash,0)+COALESCE(c.collect_cash_amount,0)-COALESCE(c.payout_amount,0) as end_balance
		FROM petty_cash_change  AS  c
		FULL JOIN petty_cash_start_balance  AS b
		ON c.org_id=b.store_id
		and to_date(c.business_date,'yyyy-mm-dd')= date_trunc('day', b.business_date::timestamp)
)
		
		select
			   result.partner_id,
			   result.business_date,
			   result.store_id,
			   store_caches.store_code,
			   store_caches.store_name,
			   result.start_balance,
			   result.collect_cash_amount,
			   result.other_amount,
			   result.payout_amount,
               result.end_balance,
 			   coalesce(payout_json.payout_detail,'{}')as payout_detail
			   FROM result
		INNER JOIN store_caches
		on result.store_id::bigint=store_caches.id
		LEFT JOIN payout_json 
		ON result.store_id::bigint=payout_json.org_id
		AND result.business_date=payout_json.business_date
		WHERE
		{WHERE}
		order by  result.business_date ASC,  result.store_id desc
		{LIMIT}
`

	totalSQL = `
		WITH petty_cash_change AS (
			SELECT
				t.partner_id,
				t.business_date,
				t.business_date_timestamp,
				t.org_id,
				t.org_code,
				t.org_name,
				SUM(CASE  WHEN t.type_code='payout' then t.total_amount ELSE 0 END ) as payout_amount,
				SUM(CASE  WHEN t.type_code in('payin','collectPettyCash' )then t.total_amount ELSE 0 END ) as collect_cash_amount,
				SUM(CASE  WHEN t.type_code in('payin')then t.total_amount ELSE 0 END ) as other_amount
			FROM  events t
			INNER JOIN store_caches
			ON t.org_id::bigint=store_caches.id
			WHERE  t.sub_type_code='pettyCash'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.partner_id,t.business_date, t.business_date_timestamp,t.org_id, t.org_code, t.org_name
		),
		
		petty_cash_start_balance AS (
			SELECT  t.bus_date  as business_date ,t.partner_id,store_id,t.initial_petty_cash from cash_account t
			INNER JOIN store_caches
			ON t.store_id=store_caches.id
			WHERE {WHERE_PETTY_CASH_START_BALANCE}
		),
		payout_detail AS(
			SELECT t.org_id,t.business_date,t.trigger_type_name,(sum(total_amount)::decimal/100)  amount 
			FROM events as t
			INNER JOIN store_caches
			ON t.org_id::bigint=store_caches.id
			WHERE   t.sub_type_code='pettyCash' 
			AND  t.type_code='payout'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.org_id, t.business_date, t.trigger_type_name),

		payout_json as(
                    SELECT  t.org_id,t.business_date,
                            JSON_OBJECT_AGG(t.trigger_type_name,t.amount) as payout_detail 
					FROM  payout_detail t
					
                    GROUP BY t.org_id, t.business_date ),
		
		result as (
		SELECT
		coalesce(c.partner_id,b.partner_id) as partner_id,
		coalesce(c.business_date,to_char(b.business_date,'yyyy-mm-dd')) AS business_date,
		coalesce(c.business_date_timestamp,EXTRACT(EPOCH FROM b.business_date)-8*3600) as business_date_timestamp,
		coalesce( c.org_id,b.store_id) AS store_id,
		COALESCE(b.initial_petty_cash,0) AS start_balance,
		COALESCE(c.collect_cash_amount,0) AS collect_cash_amount,
		COALESCE(c.other_amount,0)  AS other_amount,
		COALESCE(c.payout_amount,0)  AS payout_amount,
		COALESCE(b.initial_petty_cash,0)+COALESCE(c.collect_cash_amount,0)-COALESCE(c.payout_amount,0) as end_balance
		FROM petty_cash_change  AS  c
		FULL JOIN petty_cash_start_balance  AS b
		ON c.org_id=b.store_id
		and to_date(c.business_date,'yyyy-mm-dd')= date_trunc('day', b.business_date::timestamp)
)
		
		select
			 count(*)
			   FROM result
		INNER JOIN store_caches
		on result.store_id::bigint=store_caches.id
		LEFT JOIN payout_json 
		ON result.store_id::bigint=payout_json.org_id
		AND result.business_date=payout_json.business_date
		WHERE
		{WHERE}
`
	summarySQL = `
		WITH petty_cash_change AS (
			SELECT
				t.partner_id,
				t.business_date,
				t.business_date_timestamp,
				t.org_id,
				t.org_code,
				t.org_name,
				SUM(CASE  WHEN t.type_code='payout' then t.total_amount ELSE 0 END ) as payout_amount,
				SUM(CASE  WHEN t.type_code in('payin','collectPettyCash' )then t.total_amount ELSE 0 END ) as collect_cash_amount,
				SUM(CASE  WHEN t.type_code in('payin')then t.total_amount ELSE 0 END ) as other_amount
			FROM  events t
			INNER JOIN store_caches
			ON t.org_id::bigint=store_caches.id
			WHERE  t.sub_type_code='pettyCash'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.partner_id,t.business_date, t.business_date_timestamp,t.org_id, t.org_code, t.org_name
		),
		
		petty_cash_start_balance AS (
			SELECT  t.bus_date  as business_date ,t.partner_id,store_id,t.initial_petty_cash from cash_account t
			INNER JOIN store_caches
			ON t.store_id=store_caches.id
			WHERE {WHERE_PETTY_CASH_START_BALANCE}
		),
		payout_detail AS(
			SELECT t.org_id,t.business_date,t.trigger_type_name,(sum(total_amount)::decimal/100) amount 
			FROM events as t
			INNER JOIN store_caches
			ON t.org_id::bigint=store_caches.id
			WHERE   t.sub_type_code='pettyCash' 
			AND  t.type_code='payout'
			AND  {WHERE_PETTY_CASH_CHANGE}
			GROUP BY t.org_id, t.business_date, t.trigger_type_name),

		payout_json as(
                    SELECT  t.org_id,t.business_date,
                            JSON_OBJECT_AGG(t.trigger_type_name,t.amount) as payout_detail 
					FROM  payout_detail t
					
                    GROUP BY t.org_id, t.business_date ),
		
		result as (
		SELECT
		coalesce(c.partner_id,b.partner_id) as partner_id,
		coalesce(c.business_date,to_char(b.business_date,'yyyy-mm-dd')) AS business_date,
		coalesce(c.business_date_timestamp,EXTRACT(EPOCH FROM b.business_date)-8*3600) as business_date_timestamp,
		coalesce( c.org_id,b.store_id) AS store_id,
		COALESCE(b.initial_petty_cash,0) AS start_balance,
		COALESCE(c.collect_cash_amount,0) AS collect_cash_amount,
		COALESCE(c.other_amount,0)  AS other_amount,
		COALESCE(c.payout_amount,0)  AS payout_amount,
		COALESCE(b.initial_petty_cash,0)+COALESCE(c.collect_cash_amount,0)-COALESCE(c.payout_amount,0) as end_balance
		FROM petty_cash_change  AS  c
		FULL JOIN petty_cash_start_balance  AS b
		ON c.org_id=b.store_id
		and to_date(c.business_date,'yyyy-mm-dd')= date_trunc('day', b.business_date::timestamp)
), 
		payout_summary as(
 		SELECT t.trigger_type_name,sum(t.amount)amount 
		FROM payout_detail t
		GROUP BY t.trigger_type_name
       )
		SELECT json_object_agg(trigger_type_name,amount) as payout_detail_summary,sum(amount)  as payout_amount_summary
		FROM payout_summary
`

	whereSQL := generateWhereSQLForPettyCashDailySettlementReport(condition)
	wherePettyCashChange := generateQuerySubSQLForPettyCashDailySettlementReport(ctx, condition, "petty_cash_change")
	wherePettyCashStartBalance := generateQuerySubSQLForPettyCashDailySettlementReport(ctx, condition, "petty_cash_start_balance")
	limitSQL := generateLimitOffsetSQLForPettyCashDailySettlementReport(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE_PETTY_CASH_CHANGE}", wherePettyCashChange)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE_PETTY_CASH_START_BALANCE}", wherePettyCashStartBalance)

	logger.Pre().Debugf("[%d] repo.cash_management.PettyCashDailySettlementRepoHolo row. SQL: `%s`", rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE_PETTY_CASH_CHANGE}", wherePettyCashChange)
	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE_PETTY_CASH_START_BALANCE}", wherePettyCashStartBalance)

	logger.Pre().Debugf("[%d] repo.cash_management.PettyCashDailySettlementRepoHolo total. SQL: `%s`", totalSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE_PETTY_CASH_CHANGE}", wherePettyCashChange)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE_PETTY_CASH_START_BALANCE}", wherePettyCashStartBalance)

	return rowSQL, totalSQL, summarySQL
}

func queryDBWithChanForPettyCashDailySettlementReport(ctx context.Context, db *gorm.DB, rowSQL string, ch chan []*cash_management.PettyCashDailySettlement) {
	ch <- queryDBForPettyCashDailySettlementReport(ctx, db, rowSQL)
}

func queryDBWithChanForPettyCashDailySettlementReportTotal(ctx context.Context, db *gorm.DB, totalSQL string, ch chan int64) {
	ch <- queryDBForPettyCashDailySettlementReportTotal(ctx, db, totalSQL)
}

func queryDBWithChanForPettyCashDailySettlementReportSummary(ctx context.Context, db *gorm.DB, summarySQL string, ch chan *cash_management.PettyCashDailySettlementSummary) {
	ch <- queryDBForPettyCashDailySettlementReportSummary(ctx, db, summarySQL)
}

func queryDBForPettyCashDailySettlementReport(ctx context.Context, db *gorm.DB, rowSQL string) []*cash_management.PettyCashDailySettlement {
	rows := make([]*cash_management.PettyCashDailySettlement, 0)
	tx := db.WithContext(ctx).Raw(rowSQL).Find(&rows)
	if tx.Error != nil {
		logger.Pre().Errorf("queryDBForPettyCashDailySettlementReport. Err: `%v`", tx.Error)
	}
	return rows
}

func queryDBForPettyCashDailySettlementReportTotal(ctx context.Context, db *gorm.DB, totalSQL string) int64 {
	var total int64
	tx := db.WithContext(ctx).Raw(totalSQL).Count(&total)
	if tx.Error != nil {
		logger.Pre().Errorf("queryDBForPettyCashDailySettlementReport. Err: `%v`", tx.Error)
	}
	return total
}

func queryDBForPettyCashDailySettlementReportSummary(ctx context.Context, db *gorm.DB, totalSQL string) *cash_management.PettyCashDailySettlementSummary {
	summary := &cash_management.PettyCashDailySettlementSummary{}
	tx := db.WithContext(ctx).Raw(totalSQL).Find(&summary)
	if tx.Error != nil {
		logger.Pre().Errorf("queryDBForPettyCashDailySettlementReport. Err: `%v`", tx.Error)
	}
	if summary.PayoutDetailSummary == "" {
		summary.PayoutDetailSummary = "{}"
	}
	return summary
}

func generateWhereSQLForPettyCashDailySettlementReport(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	regionIdsSQL = repo.GenerateRegionWhereSQL(condition)
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		result.partner_id = %d
		AND result.business_date_timestamp >= '%d'
		AND result.business_date_timestamp <= '%d'
		%s
		%s
	`, condition.PartnerId, condition.Start.UTC().Unix(),
		condition.End.UTC().Unix(), regionIdsSQL, storeSQL)
	return whereSQL
}

func generateLimitOffsetSQLForPettyCashDailySettlementReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateQuerySubSQLForPettyCashDailySettlementReport(ctx context.Context, condition *model.RepoCondition, tableName string) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	regionIdsSQL = repo.GenerateRegionWhereSQL(condition)
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := ""
	switch tableName {
	case "petty_cash_change":
		whereSQL = fmt.Sprintf(`
		t.partner_id = %d
		AND t.business_date_timestamp >= '%d'
		AND t.business_date_timestamp <= '%d'
		%s
		%s
	`, condition.PartnerId, condition.Start.UTC().Unix(),
			condition.End.UTC().Unix(), regionIdsSQL, storeSQL)
	case "petty_cash_start_balance":
		whereSQL = fmt.Sprintf(`
		t.partner_id = %d
		AND t.bus_date >= '%s'
		AND t.bus_date <= '%s'
		%s
		%s
	`, condition.PartnerId, condition.Start.Format("2006-01-02"),
			condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL)
	default:
		whereSQL = `1=1`
	}

	return whereSQL
}
