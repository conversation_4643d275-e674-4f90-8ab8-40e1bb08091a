package repo

import (
	"context"
	"fmt"
	log "github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
	"time"
)

type AdjustRepositoryPG struct {
	DB *gorm.DB
}

func (ar *AdjustRepositoryPG) SaveContent(ctx context.Context, userId int64, content *model.SalesAdjustContent) error {
	var err error
	tx := ar.DB.Begin()
	if err = tx.Error; err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Pre().WithFields(log.Fields{
				"err":   err,
				"model": content,
			}).Info("商品报损保存失败")
			tx.Rollback()
		}
	}()

	// 有旧数据，需要标记为删除
	adjustId := content.AdjustId
	var exists []*model.SalesAdjustContent

	err = tx.Debug().Where("adjust_id = ? and deleted = 0", adjustId).Find(&exists).Error
	if err != nil {
		return err
	}

	if len(exists) > 0 {
		ids := make([]int64, 0)
		for _, e := range exists {
			ids = append(ids, e.Id)
		}
		err = tx.Debug().Model(model.SalesAdjustContent{}).
			Where(fmt.Sprintf("id in (%s)", helpers.JoinInt64(ids, ","))).
			Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
		if err != nil {
			return err
		}
	}

	err = tx.Debug().Create(content).Error
	if err != nil {
		return err
	}

	err = tx.Commit().Error
	return err
}

func (ar *AdjustRepositoryPG) FindContent(ctx context.Context, adjustId string) (*model.SalesAdjustContent,error) {
	var content = &model.SalesAdjustContent{}
	err := ar.DB.Debug().Where("adjust_id = ? and deleted = 0", adjustId).First(content).Error
	return content, err
}

func (ar *AdjustRepositoryPG) SaveProducts(ctx context.Context, userId int64, products []*model.SalesAdjustProduct) error {
	if len(products) == 0 {
		return nil
	}
	var err error
	tx := ar.DB.Begin()
	if err = tx.Error; err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Pre().WithFields(log.Fields{
				"err":      err,
				"products": products,
			}).Info("商品报损明细保存失败")
			tx.Rollback()
		}
	}()

	// 有旧数据，需要标记为删除
	adjustId := products[0].AdjustId
	var exists []*model.SalesAdjustProduct

	err = tx.Debug().Where("adjust_id = ? and deleted = 0", adjustId).Find(&exists).Error
	if err != nil {
		return err
	}

	if len(exists) > 0 {
		ids := make([]int64, 0)
		for _, e := range exists {
			ids = append(ids, e.Id)
		}
		err = tx.Debug().Model(model.SalesAdjustProduct{}).
			Where(fmt.Sprintf("id in (%s)", helpers.JoinInt64(ids, ","))).
			Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
		if err != nil {
			return err
		}
	}

	for _, m := range products {
		err = tx.Debug().Create(m).Error
		if err != nil {
			return err
		}
	}

	err = tx.Commit().Error
	return err
}

func (ar *AdjustRepositoryPG) Rollback(ctx context.Context, userId int64, id string) error {
	// 删除 adjust_id 为 id 的报损单和报损商品明细
	err := ar.DB.Debug().Model(model.SalesAdjustContent{}).
		Where("adjust_id = ? and deleted = 0", id).
		Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
	if err != nil {
		logger.Pre().WithFields(log.Fields{
			"err": err,
			"id":  id,
		}).Info("商品报损回滚失败")
	}
	// 删除 adjust_id 为 id 的报损单和报损商品明细
	err = ar.DB.Debug().Model(model.SalesAdjustProduct{}).
		Where("adjust_id = ?", id).
		Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
	if err != nil {
		logger.Pre().WithFields(log.Fields{
			"err": err,
			"id":  id,
		}).Info("商品报损明细回滚失败")
	}
	return err
}
