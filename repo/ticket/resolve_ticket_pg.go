package repo

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
)

type ResolveTicketPG struct {
	DB *gorm.DB
}

// 将拆分好的基础表数据存储到数据库中
func (rtm *ResolveTicketPG) InsertResolveTicket(ctx context.Context, update *model.SalesTicketContent, rs ...[]model.Resolve) (err error) {
	err = rtm.DB.Transaction(func(tx *gorm.DB) (err error) {

		// 删除旧数据
		var fresh1 model.SalesTicketAmount
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh1).Error; err != nil {
			return
		}
		var fresh2 model.SalesProductAmount
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh2).Error; err != nil {
			return
		}
		var fresh3 model.SalesPaymentAmount
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh3).Error; err != nil {
			return
		}
		var fresh4 model.SalesDiscountAmount
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh4).Error; err != nil {
			return
		}
		var fresh5 model.SalesProductSkuRemark
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh5).Error; err != nil {
			return
		}
		var fresh6 model.SalesItemPaymentsAmounts
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Delete(&fresh6).Error; err != nil {
			return
		}

		// rs是基础表模型对象数组
		for _, item := range rs {
			// 遍历每个对象模型数组里面的数据
			for _, each := range item {
				// each是每一个报表模型对象
				if err = tx.Debug().WithContext(ctx).Create(each).Error; err != nil {
					logger.Pre().Infof("插入数据失败。ID: %s", err)
					return
				}
			}
		}
		if err = tx.WithContext(ctx).Updates(update).Error; err != nil {
			return
		}

		return nil
	})

	if err != nil {
		var tk model.Ticket
		if e := json.Unmarshal(*update.ContentModified, &tk); e == nil {
			// 更新状态：系统异常
			update.Status = model.SysError
			tk.ErrorCodes = err.Error()
			err = nil
			bs, _ := json.Marshal(tk)
			cm := json.RawMessage(bs)
			update.ContentModified = &cm
			if err = rtm.DB.Updates(update).Error; err != nil {
				return
			}
		}
	}
	return err
}

//
//func (rtm *ResolveTicketPG) PreDeleteResolveTicket(ctx context.Context, eticketId int64) (err error) {
//	tx := rtm.DB.Begin()
//	if err = tx.Error; err != nil {
//		return
//	}
//	defer func() {
//		if err != nil {
//			tx.Rollback()
//		}
//	}()
//
//	var fresh1 model.SalesTicketAmount
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh1).Error; err != nil {
//
//		return tx.Commit().Error
//	}
//	var fresh2 model.SalesProductAmount
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh2).Error; err != nil {
//
//		return tx.Commit().Error
//	}
//	var fresh3 model.SalesPaymentAmount
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh3).Error; err != nil {
//
//		return tx.Commit().Error
//	}
//	var fresh4 model.SalesDiscountAmount
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh4).Error; err != nil {
//
//		return tx.Commit().Error
//	}
//	var fresh5 model.SalesProductSkuRemark
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh5).Error; err != nil {
//		return tx.Commit().Error
//	}
//	var fresh6 model.SalesItemPaymentsAmounts
//	if err = tx.Unscoped().Where("eticket_id = ?  ", eticketId).Delete(&fresh6).Error; err != nil {
//		return tx.Commit().Error
//	}
//	return tx.Commit().Error
//}
