package repo

import (
	"context"
	"fmt"
	log "github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
	"time"
)

type InboundRepositoryPG struct {
	DB *gorm.DB
}

func (ar *InboundRepositoryPG) SaveContent(ctx context.Context, userId int64, content *model.SalesInboundContent) error {
	var err error
	tx := ar.DB.Begin()
	if err = tx.Error; err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Pre().WithFields(log.Fields{
				"err":   err,
				"model": content,
			}).Info("商品入库保存失败")
			tx.Rollback()
		}
	}()

	// 有旧数据，需要标记为删除
	id := content.InboundId
	var exists []*model.SalesInboundContent

	err = tx.Debug().Where("inbound_id = ? and deleted = 0", id).Find(&exists).Error
	if err != nil {
		return err
	}

	if len(exists) > 0 {
		ids := make([]int64, 0)
		for _, e := range exists {
			ids = append(ids, e.Id)
		}
		err = tx.Debug().Model(model.SalesInboundContent{}).
			Where(fmt.Sprintf("id in (%s)", helpers.JoinInt64(ids, ","))).
			Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
		if err != nil {
			return err
		}
	}

	err = tx.Debug().Create(content).Error
	if err != nil {
		return err
	}

	err = tx.Commit().Error
	return err
}

func (ar *InboundRepositoryPG) SaveProducts(ctx context.Context, userId int64, products []*model.SalesInboundProduct) error {
	if len(products) == 0 {
		return nil
	}
	var err error
	tx := ar.DB.Begin()
	if err = tx.Error; err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Pre().WithFields(log.Fields{
				"err":      err,
				"products": products,
			}).Info("商品入库明细保存失败")
			tx.Rollback()
		}
	}()

	// 有旧数据，需要标记为删除
	id := products[0].InboundId
	var exists []*model.SalesInboundProduct

	err = tx.Debug().Where("inbound_id = ? and deleted = 0", id).Find(&exists).Error
	if err != nil {
		return err
	}

	if len(exists) > 0 {
		ids := make([]int64, 0)
		for _, e := range exists {
			ids = append(ids, e.Id)
		}
		err = tx.Debug().Model(model.SalesInboundProduct{}).
			Where(fmt.Sprintf("id in (%s)", helpers.JoinInt64(ids, ","))).
			Updates(map[string]interface{}{"deleted": 1, "updated": time.Now(), "updated_by": userId}).Error
		if err != nil {
			return err
		}
	}

	for _, m := range products {
		err = tx.Debug().Create(m).Error
		if err != nil {
			return err
		}
	}

	err = tx.Commit().Error
	return err
}
