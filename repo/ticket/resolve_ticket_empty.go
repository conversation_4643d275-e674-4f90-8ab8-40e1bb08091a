package repo

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

// 空实现，不启用holo时使用
type ResolveTicketEmpty struct {
}

func (rtm *ResolveTicketEmpty) InsertResolveTicket(ctx context.Context, update *model.SalesTicketContent, rs ...[]model.Resolve) (err error) {
	return nil
}

func (rtm *ResolveTicketEmpty) PreDeleteResolveTicket(ctx context.Context, eticketId int64) (err error) {
	return nil
}
