package repo

import (
    "context"
    "fmt"
    log "github.com/sirupsen/logrus"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "gorm.io/gorm"
    "time"
)

type MakeTimeRepositoryPG struct {
    DB *gorm.DB
}

func (mt *MakeTimeRepositoryPG) SaveBatch(ctx context.Context, models []*model.SalesProductMakeTime) error {
    if len(models) == 0 {
        return nil
    }
    var err error
    tx := mt.DB.Begin()
    if err = tx.Error; err != nil {
        return err
    }
    defer func() {
        if err != nil {
            logger.Pre().WithFields(log.Fields{
                "err": err,
                "models": models,
            }).Info("商品制作时间保存失败")
            tx.Rollback()
        }
    }()

    // 有旧数据，需要标记为删除
    ticketId := models[0].TicketId
    var exists []*model.SalesProductMakeTime

    err = tx.Debug().Where("ticket_id = ? and deleted = 0", ticketId).Find(&exists).Error;
    if err != nil {
        return err
    }

    if len(exists) > 0 {
        ids := make([]int64, 0)
        for _, e := range exists {
            ids = append(ids, e.Id)
        }
        err = tx.Debug().Model(model.SalesProductMakeTime{}).
            Where(fmt.Sprintf("id in (%s)", helpers.JoinInt64(ids, ","))).
            Updates(map[string]interface{}{"deleted": 1, "updated": time.Now()}).Error
        if err != nil {
            return err
        }
    }

    for _, m := range models {
        err = tx.Debug().Create(m).Error
        if err != nil {
            return err
        }
    }

    err = tx.Commit().Error
    return err
}
