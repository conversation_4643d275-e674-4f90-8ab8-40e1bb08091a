package repo

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gorm.io/gorm"
	"time"
)

// 异常电子小票异常信息实现类
type SalesErrDBPG struct {
	SalesErrDB
	DB *gorm.DB
}

func (s *SalesErrDBPG) InsertSaleErr(ctx context.Context, errmsg *model.SalesTicketErrmsg) (*model.SalesTicketErrmsg, error) {
	var exist model.SalesTicketErrmsg
	err := s.DB.WithContext(ctx).First(&exist, map[string]interface{}{"eticket_id": errmsg.EticketId, "error_msg": errmsg.ErrorMsg}).Error
	if err == gorm.ErrRecordNotFound {
		err = s.DB.WithContext(ctx).Create(errmsg).Error
		exist = *errmsg
	}
	return &exist, err
}

func (s *SalesErrDBPG) UpdateSalesErr(ctx context.Context, errmsg ...*model.SalesTicketErrmsg) error {
	panic("implement me")
}

func (s *SalesErrDBPG) DeleteSalesErr(ctx context.Context, i ...int64) error {
	var exist model.SalesTicketErrmsg
	err := s.DB.WithContext(ctx).Unscoped().Where("eticket_id in (?) ", i).Delete(&exist).Error
	if err != nil {
		return err
	}
	return nil
}

func (s *SalesErrDBPG) FindSalesErr(ctx context.Context, errmsg *model.SalesTicketErrmsg) (*model.SalesTicketErrmsg, error) {
	panic("implement me")
}

func (s *SalesErrDBPG) SearchSalesErr(ctx context.Context, condition *model.SalesTicketErrmsg, timeCondition map[model.SalesTimeKey][]*time.Time, limit, offset int, ignoreZeroStore bool) ([]*model.SalesTicketErrmsg, int64, error) {
	panic("implement me")
}
