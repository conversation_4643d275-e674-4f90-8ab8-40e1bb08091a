package repo

import (
	"context"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/model"
)

// 小票接口：插入，更新，查询
type SalesDB interface {
	InsertSale(context.Context, *model.SalesTicketContent) (*model.SalesTicketContent, error)
	UpdateSales(context.Context, ...*model.SalesTicketContent) error
	UpdateSalesInitBatch(context.Context, []int64) error
	DeleteSales(context.Context, ...int64) error
	FindSales(context.Context, *model.SalesTicketContent) (*model.SalesTicketContent, error)
	SearchSales(ctx context.Context, condition *model.SalesTicketContent, timeCondition map[model.SalesTimeKey][]*time.Time, errReq *model.ErrorTicketRequest, ignoreZeroStore bool) ([]*model.SalesTicketContent, int64, error)
	SearchTaskSales(ctx context.Context) (result []*model.SalesTicketContent, total int64, err error)
	RollbackSalesModified(ctx context.Context) (err error)
	InsertOrUpdateSale(ctx context.Context, fresh *model.SalesTicketContent) (*model.SalesTicketContent, error)
	UpdateTime(ctx context.Context, partnerId int64) (string, error)
	ReinitSales(ctx context.Context, eticketIds []int64) (int64, error)
}

// 异常电子小票：插入，更新，查询
type SalesErrDB interface {
	InsertSaleErr(context.Context, *model.SalesTicketErrmsg) (*model.SalesTicketErrmsg, error)
	UpdateSalesErr(context.Context, ...*model.SalesTicketErrmsg) error
	DeleteSalesErr(context.Context, ...int64) error
	FindSalesErr(context.Context, *model.SalesTicketErrmsg) (*model.SalesTicketErrmsg, error)
	SearchSalesErr(ctx context.Context, condition *model.SalesTicketErrmsg, timeCondition map[model.SalesTimeKey][]*time.Time, limit, offset int, ignoreZeroStore bool) ([]*model.SalesTicketErrmsg, int64, error)
}

// 拆解四张基础表接口
type ResolveTicket interface {
	InsertResolveTicket(ctx context.Context, update *model.SalesTicketContent, rs ...[]model.Resolve) error
}

type MakeTimeRepository interface {
	// 保存数据
	SaveBatch(ctx context.Context, models []*model.SalesProductMakeTime) error
}

type SalesAdjustRepository interface {
	SaveContent(ctx context.Context, userId int64, content *model.SalesAdjustContent) error
	FindContent(ctx context.Context, adjustId string) (*model.SalesAdjustContent, error)
	SaveProducts(ctx context.Context, userId int64, products []*model.SalesAdjustProduct) error
	Rollback(ctx context.Context, userId int64, id string) error
}

type SalesInboundRepository interface {
	SaveContent(ctx context.Context, userId int64, content *model.SalesInboundContent) error
	SaveProducts(ctx context.Context, userId int64, products []*model.SalesInboundProduct) error
}

// 主档缓存接口，用于更新所有数据
type MetadataCache interface {
	UpdateAllStore(ctx context.Context, stores []*model.StoreCache) error
	UpdateStore(ctx context.Context, stores *model.StoreCache) error
	UpdateAllProduct(ctx context.Context, stores []*model.ProductCache) error
	GetStoresByRegionIds(ctx context.Context, ids ...uint64) ([]uint64, error)
	UpdateProduct(ctx context.Context, stores *model.ProductCache) error
	UpdateAllTable(ctx context.Context, partnerId int64, tables []*model.TableCache) error
	UpdateStoreOpenDate(ctx context.Context, g map[string][]int64) error
	SaveStoreGoals(ctx context.Context, goals []*model.StoreGoal) error
	SaveExchangeRates(ctx context.Context, rates []*model.ExchangeRate) error
	GetStoreCacheById(ctx context.Context, id int64) (string, error)
	UpdateAllPayment(ctx context.Context, payments []*model.PaymentCache) error
}
