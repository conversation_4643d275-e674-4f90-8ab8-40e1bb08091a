package repo

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gorm.io/gorm"
)

var salesInfo_model = new(model.SalesTicketContent)

type SalesDBPG struct {
	SalesDB
	DB *gorm.DB
}

// 存储电子小票到数据库
func (sdm *SalesDBPG) InsertSale(ctx context.Context, fresh *model.SalesTicketContent) (*model.SalesTicketContent, error) {
	var exist model.SalesTicketContent
	err := sdm.DB.WithContext(ctx).First(&exist, map[string]interface{}{"eticket_id": fresh.EticketId, "partner_id": fresh.PartnerId}).Error
	if err == gorm.ErrRecordNotFound {
		err = sdm.DB.WithContext(ctx).Debug().Create(fresh).Error
		exist = *fresh
	}
	return &exist, err
}

// 更新小票
func (sdm *SalesDBPG) UpdateSales(ctx context.Context, change ...*model.SalesTicketContent) (err error) {
	session := sdm.DB.Begin()
	defer func() {
		if err != nil {
			session.Rollback()
		}
	}()
	for _, item := range change {
		if err = session.Updates(item).Error; err != nil {
			return
		}
	}
	return session.Commit().Error
}

func (sdm *SalesDBPG) UpdateSalesInitBatch(ctx context.Context, ids []int64) (err error) {
	return sdm.DB.WithContext(ctx).Model(&model.SalesTicketContent{}).Where("id in ?", ids).Updates(map[string]interface{}{
		"status":         model.Init,
		"process_status": model.Process_Init,
		"updated":        time.Now().UTC(),
	}).Error
}

// 删除电子小票
func (sdm *SalesDBPG) DeleteSales(ctx context.Context, ids ...int64) error {
	return nil
}

// 查找电子小票
func (sdm *SalesDBPG) FindSales(ctx context.Context, condition *model.SalesTicketContent) (result *model.SalesTicketContent, err error) {
	result = new(model.SalesTicketContent)
	err = sdm.DB.WithContext(ctx).Where(condition).Find(result).Error
	return
}

// 搜索小票
func (sdm *SalesDBPG) SearchSales(ctx context.Context, condition *model.SalesTicketContent, timeCondition map[model.SalesTimeKey][]*time.Time, errReq *model.ErrorTicketRequest, ignoreZeroStore bool) (result []*model.SalesTicketContent, total int64, err error) {
	// gorm的查询语句，where后面可以跟上数据库模型或map或切片
	db := sdm.DB.WithContext(ctx).Where(condition)
	// 增加partner_id查询条件，很重要
	partnerID := cast.ToInt(ctx.Value("partner_id"))
	if partnerID != 0 {
		db.Where("sales_ticket_contents.partner_id = ?", partnerID)
	}
	// 判断门店id是否存在
	if condition.StoreId != 0 && !ignoreZeroStore {
		// 使用链式查询继续过滤
		db = db.Where("sales_ticket_contents.store_id = ?", condition.StoreId)
	}
	// 查询所有门店异常
	if errReq.ErrMsg != "" || errReq.ChannelId != "" {
		db = db.Joins("left join sales_ticket_errmsgs on sales_ticket_contents.eticket_id = sales_ticket_errmsgs.eticket_id")
	}
	if errReq.ErrMsg != "" {
		// json查询
		db = db.Where("sales_ticket_errmsgs.error_msg = ?", errReq.ErrMsg)
	}
	if errReq.ChannelId != "" {
		db = db.Where("sales_ticket_errmsgs.channel_id = ?", errReq.ChannelId)
	}

	// 将营业时间，订单时间，创建时间，更新时间遍历
	for _, item := range []model.SalesTimeKey{model.BusDate, model.OrderDate, model.CreateDate, model.UpdateDate} {
		if v, in := timeCondition[item]; in {
			// 使用链式查询继续过滤：支持使用原生sql
			db = db.Where(fmt.Sprintf("%s >= ? AND %s < ?", item.String(), item.String()), v[0], v[1])
		}
	}
	// 返回查询结果&result
	err = db.Limit(errReq.Limit).Offset(errReq.Offset).Find(&result).Offset(-1).Limit(-1).Count(&total).Error
	return
}

func (sdm *SalesDBPG) RollbackSalesModified(ctx context.Context) (err error) {
	return sdm.DB.Model(salesInfo_model).Updates(map[string]interface{}{
		//"updated_by":       partnerId,
		//"content_modified": nil,
		"updated": time.Now().UTC(),
	}).Error
}

func (sdm *SalesDBPG) SearchTaskSales(ctx context.Context) (result []*model.SalesTicketContent, total int64, err error) {
	// gorm的查询语句，where后面可以跟上数据库模型或map或切片
	sdm.DB = sdm.DB.Debug()
	sevenDayAgo := time.Now().UTC().Add(-2 * 24 * time.Hour)
	fiveMinAgo := time.Now().UTC().Add(-5 * time.Minute)
	var db *gorm.DB
	if config.DefaultConfig.EnabledCompensate != 0 {
		db = sdm.DB.WithContext(ctx).Where("(status = 'INIT' or status = ? or process_status = 'REINIT') and updated>=? and updated< ? and partner_id=? ", model.SysError, config.DefaultConfig.EnabledCompensate, sevenDayAgo, fiveMinAgo, config.DefaultConfig.EnabledCompensate)
	} else {
		db = sdm.DB.WithContext(ctx).Where("(status = 'INIT' or status = ? or process_status = 'REINIT') and updated>=? and updated< ?", model.SysError, sevenDayAgo, fiveMinAgo)
	}
	// 返回查询结果&result
	err = db.Limit(200).Find(&result).Offset(-1).Limit(-1).Count(&total).Error
	return
}

// 存储电子小票到数据库
func (sdm *SalesDBPG) InsertOrUpdateSale(ctx context.Context, fresh *model.SalesTicketContent) (*model.SalesTicketContent, error) {
	var exist model.SalesTicketContent
	err := sdm.DB.WithContext(ctx).First(&exist, map[string]interface{}{"eticket_id": fresh.EticketId, "partner_id": fresh.PartnerId}).Error
	var rePushed bool
	if err == nil && canRePush(&exist) {
		d_err := sdm.DB.WithContext(ctx).Debug().Unscoped().Delete(&exist).Error
		rePushed = d_err == nil
	}
	if err == gorm.ErrRecordNotFound || rePushed {
		err = sdm.DB.WithContext(ctx).Debug().Create(fresh).Error
		exist = *fresh
	}
	return &exist, err
}

func (sdm *SalesDBPG) UpdateTime(ctx context.Context, partnerId int64) (string, error) {
	now := time.Now().In(time.Local)
	yesterday := now.AddDate(0, 0, -1)
	date := yesterday.Format("2006-01-02")
	orderTimeR := sdm.DB.WithContext(ctx).
		Model(model.SalesTicketContent{}).
		Where("partner_id = ? and bus_date >= ? and status = ? and process_status = ?", partnerId, date, model.Finish, model.Process_Discountsh).
		Select("max(order_time)").Row()
	notFinishR := sdm.DB.WithContext(ctx).
		Model(model.SalesTicketContent{}).
		Where("partner_id = ? and bus_date >= ? and (status != ? or process_status != ?)", partnerId, date, model.Finish, model.Process_Discountsh).
		Select("count(1)").Row()
	var orderTime string
	var notFinish int32
	orderTimeR.Scan(&orderTime)
	notFinishR.Scan(&notFinish)
	if notFinish > 0 {
		ot, err := time.ParseInLocation("2006-01-02T15:04:05Z", orderTime, time.UTC)
		return ot.In(time.Local).Format("2006-01-02 15:04"), err
	} else {
		return now.Format("2006-01-02 15:04"), nil
	}
}

// 更新小票
func (sdm *SalesDBPG) ReinitSales(ctx context.Context, eticketIds []int64) (int64, error) {
	r := sdm.DB.Debug().Model(&model.SalesTicketContent{}).
		Where("eticket_id IN ?", eticketIds).
		Updates(map[string]interface{}{"status": model.Init, "process_status": model.Process_ReInit})

	return r.RowsAffected, r.Error
}

func canRePush(t *model.SalesTicketContent) bool {
	// 终态可以重推
	return t != nil
}
