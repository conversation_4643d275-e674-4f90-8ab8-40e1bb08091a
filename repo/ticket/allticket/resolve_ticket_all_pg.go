package allticket

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
)

type ResolveTicketAllPG struct {
	DB *gorm.DB
}

// 将拆分好的基础表数据存储到数据库中
func (rtm *ResolveTicketAllPG) InsertResolveTicketAll(ctx context.Context, update *model.SalesTicketContentAbnormal, isNormal bool, rs ...[]model.Resolve) (err error) {
	err = rtm.DB.Transaction(func(tx *gorm.DB) (err error) {
		// 删除旧数据
		var fresh5 model.SalesTicketAmountAll
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Debug().Delete(&fresh5).Error; err != nil {
			return
		}
		var fresh6 model.SalesPaymentAmountAll
		if err = tx.Unscoped().WithContext(ctx).Where("eticket_id = ?  ", update.EticketId).Debug().Delete(&fresh6).Error; err != nil {
			return
		}

		// rs是四个基础表模型对象数组
		for _, item := range rs {
			// 遍历每个对象模型数组里面的数据
			for _, each := range item {
				// each是每一个报表模型对象
				// 打印sql
				if err = tx.Debug().WithContext(ctx).Create(each).Error; err != nil {
					logger.Pre().Infof("插入数据失败。ID: %s", err)
					return
				}
			}
		}
		if !isNormal && update != nil {
			if err = tx.WithContext(ctx).Updates(update).Error; err != nil {
				return
			}
		}
		return
	})
	if err != nil {
		if isNormal && update != nil {
			return
		}
		var tk model.Ticket
		if e := json.Unmarshal(*update.ContentModified, &tk); e == nil {
			// 更新状态：系统异常
			update.Status = model.SysError
			tk.ErrorCodes = err.Error()
			err = nil
			bs, _ := json.Marshal(tk)
			cm := json.RawMessage(bs)
			update.ContentModified = &cm
			if err = rtm.DB.WithContext(ctx).Updates(update).Error; err != nil {
				return
			}
		}
	}
	return
}
