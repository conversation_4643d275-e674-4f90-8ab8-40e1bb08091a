package allticket

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

// 异常小票接口：插入，更新，查询
type AbnormalSalesDB interface {
	FindAbnormalSales(context.Context, *model.SalesTicketContentAbnormal) (*model.SalesTicketContentAbnormal, error)
	InsertAbnormalSale(context.Context, *model.SalesTicketContentAbnormal) (*model.SalesTicketContentAbnormal, error)
	UpdateAbnormalSales(context.Context, ...*model.SalesTicketContentAbnormal) error
	UpdateAbnormalInitSalesBatch(ctx context.Context, ids []int64) (err error)
	SearchTaskAbnormalSales(ctx context.Context) (result []*model.SalesTicketContentAbnormal, total int64, err error)
	InsertOrUpdateAbnormalSale(ctx context.Context, fresh *model.SalesTicketContentAbnormal) (*model.SalesTicketContentAbnormal, error)
}

// 拆解2张基础表接口
type ResolveTicketAll interface {
	InsertResolveTicketAll(ctx context.Context, update *model.SalesTicketContentAbnormal, isNormal bool, rs ...[]model.Resolve) error
}
