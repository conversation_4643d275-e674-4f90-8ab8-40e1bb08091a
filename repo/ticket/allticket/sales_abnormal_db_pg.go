package allticket

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gorm.io/gorm"
	"time"
)

var abnormal_salesInfo_model = new(model.SalesTicketContentAbnormal)

type AbnormalSalesDBPG struct {
	AbnormalSalesDB
	DB *gorm.DB
}

// 查找电子小票
func (sdm *AbnormalSalesDBPG) FindAbnormalSales(ctx context.Context, condition *model.SalesTicketContentAbnormal) (result *model.SalesTicketContentAbnormal, err error) {
	result = new(model.SalesTicketContentAbnormal)
	err = sdm.DB.WithContext(ctx).Where(condition).Find(result).Error
	return
}

// 存储异常电子小票到数据库
func (sdm *AbnormalSalesDBPG) InsertAbnormalSale(ctx context.Context, fresh *model.SalesTicketContentAbnormal) (*model.SalesTicketContentAbnormal, error) {
	var exist model.SalesTicketContentAbnormal
	err := sdm.DB.WithContext(ctx).First(&exist, map[string]interface{}{"eticket_id": fresh.EticketId, "partner_id": fresh.PartnerId}).Error
	if err == gorm.ErrRecordNotFound {
		err = sdm.DB.WithContext(ctx).Debug().Create(fresh).Error
		exist = *fresh
	}
	return &exist, err
}

// 更新小票
func (sdm *AbnormalSalesDBPG) UpdateAbnormalSales(ctx context.Context, change ...*model.SalesTicketContentAbnormal) (err error) {
	return sdm.DB.Transaction(func(tx *gorm.DB) error {
		for _, item := range change {
			if err = tx.WithContext(ctx).Updates(item).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

func (sdm *AbnormalSalesDBPG) UpdateAbnormalInitSalesBatch(ctx context.Context, ids []int64) (err error) {
	return sdm.DB.WithContext(ctx).Model(&model.SalesTicketContentAbnormal{}).Where("id in ?", ids).Updates(map[string]interface{}{
		"status":         model.Init,
		"process_status": model.Process_Init,
		"updated":        time.Now().UTC(),
	}).Error

}

func (sdm *AbnormalSalesDBPG) SearchTaskAbnormalSales(ctx context.Context) (result []*model.SalesTicketContentAbnormal, total int64, err error) {
	// gorm的查询语句，where后面可以跟上数据库模型或map或切片
	sdm.DB = sdm.DB.Debug()
	sevenDayAgo := time.Now().UTC().Add(-2 * 24 * time.Hour)
	fiveMinAgo := time.Now().UTC().Add(-5 * time.Minute)
	var db *gorm.DB
	if config.DefaultConfig.EnabledCompensate != 0 {
		db = sdm.DB.WithContext(ctx).Where("(status = 'INIT' or process_status = 'REINIT'  ) and partner_id=? and updated>=? and updated< ? ", config.DefaultConfig.EnabledCompensate, sevenDayAgo, fiveMinAgo)
	} else {
		db = sdm.DB.WithContext(ctx).Where("(status = 'INIT' or process_status = 'REINIT' ) and updated>=? and updated< ?", sevenDayAgo, fiveMinAgo)
	}

	// 返回查询结果&result
	err = db.Limit(200).Find(&result).Offset(-1).Limit(-1).Count(&total).Error
	return
}

// 存储异常电子小票到数据库
func (sdm *AbnormalSalesDBPG) InsertOrUpdateAbnormalSale(ctx context.Context, fresh *model.SalesTicketContentAbnormal) (*model.SalesTicketContentAbnormal, error) {
	var exist model.SalesTicketContentAbnormal
	err := sdm.DB.WithContext(ctx).First(&exist, map[string]interface{}{"eticket_id": fresh.EticketId, "partner_id": fresh.PartnerId}).Error
	var rePushed bool
	if err == nil && canRePush(&exist) {
		d_err := sdm.DB.WithContext(ctx).Debug().Unscoped().Delete(&exist).Error
		rePushed = d_err == nil
	}
	if err == gorm.ErrRecordNotFound || rePushed {
		err = sdm.DB.WithContext(ctx).Debug().Create(fresh).Error
		exist = *fresh
	}
	return &exist, err
}

func canRePush(t *model.SalesTicketContentAbnormal) bool {
	// 终态可以重推
	return t != nil &&
		((t.Status == model.Finish && t.ProcessStatus == model.Process_Init) ||
			(t.Status == model.DataError) ||
			(t.Status == model.SysError))
}
