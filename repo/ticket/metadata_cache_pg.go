package repo

import (
	"context"
	"fmt"
	"gorm.io/gorm/clause"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"github.com/SmallTianTian/go-tools/slice"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"gorm.io/gorm"
)

// 主档缓存结构体
type MetadataCachePG struct {
	DB *gorm.DB
}

func (mcp *MetadataCachePG) SaveStoreGoals(ctx context.Context, goals []*model.StoreGoal) error {
	logger.Pre().Infof("导入门店目标 %v \n", len(goals))
	return mcp.DB.Debug().Transaction(func(tx *gorm.DB) error {
		// 先查询某个门店某天的数据是否已经存在
		args := make([][]interface{}, 0, len(goals))
		for _, goal := range goals {
			args = append(args, []interface{}{goal.StoreId, goal.GoalDate})
		}
		existedGoals := make([]*model.StoreGoal, 0)
		if err := tx.Debug().Model(&model.StoreGoal{}).
			Where("deleted = false and (store_id, goal_date) IN ?", args).
			Find(&existedGoals).Error; err != nil {
			return err
		}
		if len(existedGoals) > 0 {
			logger.Pre().Warnf("门店目标已存在 %v 条", len(existedGoals))
			logger.Pre().Warnf("门店目标已存在 %v", existedGoals)
			args = make([][]interface{}, 0, len(existedGoals))
			for _, goal := range existedGoals {
				args = append(args, []interface{}{goal.StoreId, goal.GoalDate})
			}
			// 删除已存在的数据
			if err := tx.Debug().Model(&model.StoreGoal{}).
				Where("deleted = false and (store_id, goal_date) IN ?", args).
				Updates(map[string]interface{}{"deleted": true, "update_at": time.Now()}).Error; err != nil {
				return err
			}
		}
		return tx.Debug().CreateInBatches(goals, 100).Error
	})
}

func (mcp *MetadataCachePG) SaveExchangeRates(ctx context.Context, rates []*model.ExchangeRate) error {
	logger.Pre().Infof("导入汇率 %v \n", len(rates))
	return mcp.DB.Debug().Transaction(func(tx *gorm.DB) error {
		// 先查询某天的数据是否已经存在
		args := make([][]interface{}, 0, len(rates))
		for _, rate := range rates {
			args = append(args, []interface{}{rate.PartnerId, rate.StartDate})
		}
		existedRates := make([]*model.ExchangeRate, 0)
		if err := tx.Debug().Model(&model.ExchangeRate{}).
			Where("deleted = false and (partner_id, start_date) IN ?", args).
			Find(&existedRates).Error; err != nil {
			return err
		}
		if len(existedRates) > 0 {
			logger.Pre().Warnf("汇率已存在 %v 条", len(existedRates))
			logger.Pre().Warnf("汇率已存在 %v", existedRates)
			args = make([][]interface{}, 0, len(existedRates))
			for _, rate := range existedRates {
				args = append(args, []interface{}{rate.PartnerId, rate.StartDate})
			}
			// 删除已存在的数据
			if err := tx.Debug().Model(&model.ExchangeRate{}).
				Where("deleted = false and (partner_id, start_date) IN ?", args).
				Updates(map[string]interface{}{"deleted": true, "update_at": time.Now()}).Error; err != nil {
				return err
			}
		}
		return tx.Debug().CreateInBatches(rates, 100).Error
	})
}

func (mcp *MetadataCachePG) UpdateStoreOpenDate(ctx context.Context, g map[string][]int64) error {
	if g == nil {
		return nil
	}
	logger.Pre().Infof("更新门店开业时间 %v \n", g)
	// 更新门店开业时间
	for openDate, storeIds := range g {
		// 更新最小营业日作为开业时间
		err := mcp.DB.Table("store_caches").Where("id IN ?", storeIds).Update("open_date", openDate).Error
		if err != nil {
			logger.Pre().Error("StoreOpenDate error", err)
			return err
		}
	}
	return nil
}

func (mcp *MetadataCachePG) GetStoresByRegionIds(ctx context.Context, ids ...uint64) ([]uint64, error) {
	if len(ids) == 0 {
		return []uint64{}, nil
	}
	const tmp = "SELECT DISTINCT(c_id) FROM id2id_caches WHERE id IN (%s)"
	idsStr := make([]string, 0, len(ids))
	for _, id := range ids {
		idsStr = append(idsStr, cast.ToString(id))
	}
	var result []uint64
	err := mcp.DB.Raw(fmt.Sprintf(tmp, strings.Join(idsStr, ", "))).Find(&result).Error
	return result, err
}

// 更新所有门店
func (mcp *MetadataCachePG) UpdateAllStore(ctx context.Context, stores []*model.StoreCache) (err error) {
	// 增加门店所属公司（company_info）  2021-08-03----------------------wangheng
	// 增加门店编码（store_code）  2024-08-01 st
	const tmp = "INSERT INTO store_caches (id, geos, geo0, geo1, geo2, branchs, branch0, branch1, branch2, franchisees, franchisee0, franchisee1, franchisee2, regions, region_0, region_1, region_2, store_type, open_status, store_name, partner_id,company_info,currency,store_code,time_zone) VALUES %s ON CONFLICT (id) DO UPDATE SET geos = EXCLUDED.geos, geo0 = EXCLUDED.geo0, geo1 = EXCLUDED.geo1, geo2 = EXCLUDED.geo2, branchs = EXCLUDED.branchs, branch0 = EXCLUDED.branch0, branch1 = EXCLUDED.branch1, branch2 = EXCLUDED.branch2,franchisees = EXCLUDED.franchisees, franchisee0 = EXCLUDED.franchisee0, franchisee1 = EXCLUDED.franchisee1, franchisee2 = EXCLUDED.franchisee2, regions = EXCLUDED.regions, region_0 = EXCLUDED.region_0, region_1 = EXCLUDED.region_1, region_2 = EXCLUDED.region_2, store_type = EXCLUDED.store_type, open_status = EXCLUDED.open_status, store_name = EXCLUDED.store_name,company_info = EXCLUDED.company_info,currency = EXCLUDED.currency,store_code=EXCLUDED.store_code,time_zone=EXCLUDED.time_zone"
	const id2id_tmp = "INSERT INTO id2id_caches (id, c_id, type) VALUES (%d, %d, %d);"
	db, _ := mcp.DB.DB()
	// 数据库事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
		logger.Pre().Info("store_caches and product_caches updated")
	}()
	// clean table: store_caches(不清空，插入新数据)
	//if _, err = tx.Exec("DELETE FROM store_caches WHERE 1=1;"); err != nil {
	//	return
	//}
	// 将SQL改成批量插入数据的格式
	var storeArray = make([]string, 0)
	for _, item := range stores {
		//logger.Pre().Info("存储的门店ID是：", item.Id, i)
		// 增加门店所属公司 20210803 -------------wangheng
		var valStr = "(%d, '%s', %d, %d, %d, '%s', %d, %d, %d, '%s', %d, %d, %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d,%d,'%s','%s','%s')"
		var value = fmt.Sprintf(valStr, item.Id, utils.Int64Array2PGArrayValue(item.Geos), item.Geo0, item.Geo1, item.Geo2, utils.Int64Array2PGArrayValue(item.Branchs), item.Branch0, item.Branch1, item.Branch2, utils.Int64Array2PGArrayValue(item.Franchisees), item.Franchisee0, item.Franchisee1, item.Franchisee2, utils.StringArray2PGArrayValue(item.Regions), item.Region0, item.Region1, item.Region2, item.StoreType, item.OpenStatus, strings.ReplaceAll(item.StoreName, "'", "''"), item.PartnerId, item.CompanyInfo, strings.ReplaceAll(item.Currency, "'", "''"), strings.ReplaceAll(item.StoreCode, "'", "''"), item.TimeZone)
		storeArray = append(storeArray, value)
	}
	var allValues = strings.Join(storeArray, ",")
	if _, err = tx.Exec(fmt.Sprintf(tmp, allValues)); err != nil {
		if err != nil {
			logger.Pre().Info("store_caches updated err:", err)
		}
		logger.Pre().Info("store_caches updating.......")
		return
	}
	//for _, item := range stores {
	//	//logger.Pre().Info("存储的门店ID是：", item.Id, i)
	//	if _, err = tx.Exec(fmt.Sprintf(tmp, allValues)); err != nil {
	//		if err != nil {
	//			logger.Pre().Info("store_caches updated err:", err)
	//		}
	//		logger.Pre().Info("store_caches updating.......")
	//		return
	//	}
	//}

	// clean table: id2id_caches
	if _, err = tx.Exec("DELETE FROM id2id_caches WHERE 1=1;"); err != nil {
		return
	}
	b_id2id := make(map[int64][]int64)
	g_id2id := make(map[int64][]int64)
	//加盟商
	f_id2id := make(map[int64][]int64)
	for _, item := range stores {
		for _, id := range item.Branchs {
			b_id2id[id] = append(b_id2id[id], item.Id)
		}
		for _, id := range item.Geos {
			g_id2id[id] = append(g_id2id[id], item.Id)
		}
		// 加盟商区域
		for _, id := range item.Franchisees {
			f_id2id[id] = append(f_id2id[id], item.Id)
		}
	}
	for id, cids := range b_id2id {
		cids = slice.DistinctInt64Slice(cids)
		for _, cid := range cids {
			if _, err = tx.Exec(fmt.Sprintf(id2id_tmp, id, cid, model.Branch)); err != nil {
				return
			}
		}
	}
	for id, cids := range g_id2id {
		cids = slice.DistinctInt64Slice(cids)
		for _, cid := range cids {
			if _, err = tx.Exec(fmt.Sprintf(id2id_tmp, id, cid, model.Geo)); err != nil {
				return
			}
		}
	}
	// 加盟商区域
	for id, cids := range f_id2id {
		cids = slice.DistinctInt64Slice(cids)
		for _, cid := range cids {
			if _, err = tx.Exec(fmt.Sprintf(id2id_tmp, id, cid, model.Franchisee)); err != nil {
				return
			}
		}
	}

	return tx.Commit()
}

func (mcp *MetadataCachePG) UpdateStore(ctx context.Context, store *model.StoreCache) (err error) {
	const tmp = "INSERT INTO store_caches (id, geos, geo0, geo1, geo2, branchs, branch0, branch1, branch2) VALUES (%d, '%s', %d, %d, %d, '%s', %d, %d, %d)"
	const id2id_tmp = "INSERT INTO id2id_caches (id, c_id, type) VALUES (%d, %d, %d);"
	db, _ := mcp.DB.DB()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	if _, err = tx.Exec(fmt.Sprintf("DELETE FROM store_caches WHERE id = %d", store.Id)); err != nil {
		return
	}
	if _, err = tx.Exec(fmt.Sprintf(tmp, store.Id, utils.Int64Array2PGArrayValue(store.Geos), store.Geo0, store.Geo1, store.Geo2, utils.Int64Array2PGArrayValue(store.Branchs), store.Branch0, store.Branch1, store.Branch2)); err != nil {
		return
	}
	if _, err = tx.Exec(fmt.Sprintf("DELETE FROM id2id_caches WHERE id = %d", store.Id)); err != nil {
		return
	}
	for _, id := range store.Branchs {
		if _, err = tx.Exec(fmt.Sprintf(id2id_tmp, id, store.Id, model.Branch)); err != nil {
			return
		}
	}
	for _, id := range store.Geos {
		if _, err = tx.Exec(fmt.Sprintf(id2id_tmp, id, store.Id, model.Geo)); err != nil {
			return
		}
	}

	return tx.Commit()
}

func (mcp *MetadataCachePG) UpdateAllProduct(ctx context.Context, products []*model.ProductCache) (err error) {
	const tmp = "INSERT INTO product_caches (id, category ,categories, category0, category1, category2, has_cup, partner_id) VALUES %s ON CONFLICT (id) DO UPDATE SET category = EXCLUDED.category, categories = EXCLUDED.categories, category0 = EXCLUDED.category0, category1 = EXCLUDED.category1, category2 = EXCLUDED.category2, has_cup = EXCLUDED.has_cup"
	db, _ := mcp.DB.DB()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		logger.Pre().Info("缓存商品信息数据完成")
		if err != nil {
			tx.Rollback()
		}
	}()
	// clean table(使用新的方式，存在就不动，不存在就增加)
	//if _, err = tx.Exec("DELETE FROM product_caches WHERE 1=1;"); err != nil {
	//	return
	//}
	// 使用批量插入的方式
	var productArray = make([]string, 0)
	for _, item := range products {
		var valStr = "(%d, %d, '%s', %d, %d, %d, %t, %d)"
		var value = fmt.Sprintf(valStr, item.Id, item.Category, utils.Int64Array2PGArrayValue(item.Categories), item.Category0, item.Category1, item.Category2, item.HasCup, item.PartnerId)
		productArray = append(productArray, value)
	}
	var productStr = strings.Join(productArray, ",")
	if _, err = tx.Exec(fmt.Sprintf(tmp, productStr)); err != nil {
		if err != nil {
			logger.Pre().Error("缓存门店信息出错：", err)
		}
		return
	}
	//for i, item := range products {
	//	logger.Pre().Info("缓存商品信息数据：", item.Id, i)
	//	if _, err = tx.Exec(fmt.Sprintf(tmp, productStr)); err != nil {
	//		if err != nil {
	//			logger.Pre().Error("春村门店信息出错：", err)
	//		}
	//		return
	//	}
	//}

	return tx.Commit()
}

func (mcp *MetadataCachePG) UpdateProduct(ctx context.Context, product *model.ProductCache) (err error) {
	const tmp = "INSERT INTO product_caches(id, categories, category0, category1, category2) VALUES (%d, '%s', %d, %d, %d)"
	db, _ := mcp.DB.DB()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	if _, err = tx.Exec(fmt.Sprintf("DELETE FROM product_caches WHERE id = %d", product.Id)); err != nil {
		return
	}
	if _, err = tx.Exec(fmt.Sprintf(tmp, product.Id, utils.Int64Array2PGArrayValue(product.Categories), product.Category0, product.Category1, product.Category2)); err != nil {
		return
	}
	return tx.Commit()
}

const tableCacheSqlTpl = `INSERT INTO table_caches 
    (id,partner_id,store_id,zone_id,zone_name,table_name,table_seat,table_mode) VALUES %s 
	ON CONFLICT (id) DO UPDATE 
	SET zone_id = EXCLUDED.zone_id, 
	    zone_name = EXCLUDED.zone_name, 
	    table_name = EXCLUDED.table_name, 
	    table_seat = EXCLUDED.table_seat,
	    table_mode = EXCLUDED.table_mode
`

func buildTableCacheSql(tables []*model.TableCache) string {
	// 使用批量插入的方式
	var arr = make([]string, 0)
	for _, item := range tables {
		arr = append(arr, fmt.Sprintf("(%d, %d, %d, %d, '%s', '%s', %d, %d)", item.Id, item.PartnerId, item.StoreId, item.ZoneId,
			strings.ReplaceAll(item.ZoneName, "'", "''"), strings.ReplaceAll(item.TableName, "'", "''"), item.TableSeat, item.TableMode))
	}
	return fmt.Sprintf(tableCacheSqlTpl, strings.Join(arr, ","))
}

func (mcp *MetadataCachePG) UpdateAllTable(ctx context.Context, partnerId int64, tables []*model.TableCache) error {
	if len(tables) == 0 {
		return nil
	}

	db, _ := mcp.DB.DB()
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Pre().Info("缓存桌位信息数据失败", partnerId, err)
			tx.Rollback()
		} else {
			logger.Pre().Info("缓存桌位信息数据完成", partnerId)
		}
	}()

	sql := buildTableCacheSql(tables)
	if _, err = tx.Exec(sql); err != nil {
		return err
	}

	tableIds := make([]string, 0)
	for _, table := range tables {
		tableIds = append(tableIds, cast.ToString(table.Id))
	}

	// 删除不存在的桌位
	deleteSql := fmt.Sprintf(`
		DELETE FROM table_caches WHERE partner_id = %d AND id NOT IN (%s)
	`, partnerId, strings.Join(tableIds, ","))

	if _, err = tx.Exec(deleteSql); err != nil {
		return err
	}

	tx.Commit()
	return nil
}

func (mcp *MetadataCachePG) GetStoreCacheById(ctx context.Context, id int64) (string, error) {
	tmp := struct {
		ID       int64  `gorm:"column:id;primaryKey"`
		TimeZone string `gorm:"column:time_zone"`
	}{}
	// 使用 GORM 的结构体绑定方式查询
	result := mcp.DB.WithContext(ctx).Table("store_caches").Where("id = ?", id).First(&tmp)
	if result.Error != nil {
		return "", result.Error
	}
	return tmp.TimeZone, nil
}

func (mcp *MetadataCachePG) UpdateAllPayment(ctx context.Context, payments []*model.PaymentCache) (err error) {
	tx := mcp.DB.Model(model.PaymentCache{}).Clauses(clause.OnConflict{UpdateAll: true}).CreateInBatches(payments, 100)
	return tx.Error
}
