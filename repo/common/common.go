package common

import (
"bytes"
"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
"text/template"
)

func Create(name, t string) *template.Template {
    return template.Must(template.New(name).Parse(t))
}

func Render(t *template.Template, data map[string]string) string {
    var b bytes.Buffer
    if err := t.Execute(&b, data); err != nil {
        logger.Pre().Errorf("template render error: %v, %v\n", t.Name(), data)
    }
    return b.String()
}

