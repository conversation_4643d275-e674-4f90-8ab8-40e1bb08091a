package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) MealSegments(ctx context.Context, condition *model.RepoCondition) (*report.MealSegmentsResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	sql := generateQuerySQLForMealSegments(trackId, condition)
	rows := queryDBForMealSegments(trackId, rsmm.DB, sql)
	//ss := sort.StringSlice(rows)
	//这个顺序排序 "早餐", "中餐", "午餐", "下午茶", "晚餐", "夜宵"

	sortMap := make(map[string]struct{}, len(rows))
	for _, v := range rows {
		sortMap[v] = struct{}{}
	}
	//遍历排序数组，如果map中存在，则添加到结果数组中
	ss := make([]string, 0)
	for _, v := range config.MealSegmentSort {
		if _, ok := sortMap[v]; ok {
			ss = append(ss, v)
		}
	}

	response := &report.MealSegmentsResponse{
		Rows: ss,
	}
	return response, nil
}

// 查询数据库，并将结果映射为map对象
func queryDBForMealSegments(trackId int64, db *sql.DB, sqlStr string) []string {
	results := make([]string, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		var ms string
		if err := r.Scan(
			&ms,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, ms)
	}
	return results
}

func generateQuerySQLForMealSegments(trackId int64, condition *model.RepoCondition) string {
	rowSQL := `
--餐段 ROWS
select distinct meal_segment_name
from sales_ticket_amounts fact
where meal_segment_name is not null and meal_segment_name != '' and {WHERE}
`
	whereSQL := generateWhereSQLForMealSegments(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	return rowSQL
}

func generateWhereSQLForMealSegments(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.In(config.ShanghaiLocation).Format("2006-01-02"),
		condition.End.In(config.ShanghaiLocation).Format("2006-01-02"), regionIdsSQL, storeSQL)
	return whereSQL
}
