package repo

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (rsmm *SalesRepositoryPG) StoreOpenDate(ctx context.Context, partnerIds []int64) (*report.StoreOpenDateResponse, error) {
	if len(partnerIds) == 0 {
		return &report.StoreOpenDateResponse{Rows: []*report.StoreOpenDate{}}, nil
	}
	partnerIdsStr := helpers.JoinInt64(partnerIds, ",")
	sqlStr := fmt.Sprintf(`
select to_char(min(bus_date), 'YYYY-MM-DD'), store_id
from sales_ticket_amounts
where partner_id in (%s) and store_id in (
    select id from store_caches where partner_id in(%s) and open_date is null
)
group by store_id
`, partnerIdsStr, partnerIdsStr)
	logger.Pre().Infof("StoreOpenDate SQL: %s", sqlStr)
	rows, err := rsmm.DB.Query(sqlStr)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	result := make([]*report.StoreOpenDate, 0)
	for rows.Next() {
		var row report.StoreOpenDate
		err := rows.Scan(&row.OpenDate, &row.StoreId)
		if err != nil {
			logger.Pre().Error("StoreOpenDate error", err)
			break
		}
		result = append(result, &row)
	}

	response := &report.StoreOpenDateResponse{
		Rows: result,
	}
	return response, nil
}
