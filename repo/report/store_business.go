package repo

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

const storeBusinessTpl = `
-- storeBusinessTpl
select
	store_id,
	sum(amount_0) as business_amount
from sales_ticket_amounts
where partner_id = %d and bus_date >= '%s' and bus_date <= '%s'
group by store_id
`

func (rsmm *SalesRepositoryPG) StoreBusiness(ctx context.Context, condition *report.StoreBusinessRequest) (*report.StoreBusinessResponse, error) {
	s := fmt.Sprintf(storeBusinessTpl, condition.PartnerId, condition.StartDate, condition.EndDate)
	logger.Pre().Debug(s)
	rows, err := rsmm.DB.QueryContext(ctx, s)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var resp report.StoreBusinessResponse
	for rows.Next() {
		var item report.StoreBusinessItem
		err = rows.Scan(&item.StoreId, &item.BusinessAmount)
		if err != nil {
			return nil, err
		}
		resp.Rows = append(resp.Rows, &item)
	}

	return &resp, nil
}

