package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductSalesComboDetail(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesComboDetailResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductSalesComboDetail(trackId, condition)
	ch1 := make(chan []*report.ProductSalesComboDetail, 1)
	ch2 := make(chan []*report.ProductSalesComboDetail, 1)
	go queryDBWithChanForProductSalesComboDetail(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductSalesComboDetail(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductSalesComboDetail
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductSalesComboDetailResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductSalesComboDetailTotal(trackId int64, db *sql.DB, sqlStr string, ch chan int64) {
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- 0
		return
	}
	defer r.Close()

	if r.Next() {
		var total int64
		if err := r.Scan(&total); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- 0
		} else {
			ch <- total
		}
	} else {
		ch <- 0
	}
}

func queryDBWithChanForProductSalesComboDetail(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductSalesComboDetail) {
	ch <- queryDBForProductSalesComboDetail(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForProductSalesComboDetail(trackId int64, db *sql.DB, sqlStr string) []*report.ProductSalesComboDetail {
	results := make([]*report.ProductSalesComboDetail, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductSalesComboDetail)
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.BranchRegionId,
			&f.StoreType,
			&f.ComboProductId,
			&f.Unit,
			&f.CategoryId,
			&f.ProductId,
			&f.ChannelId,
			&f.Price,
			&f.Qty,
			&f.GrossAmount,
			&f.DiscountAmount,
			&f.NetAmount,
			&f.AddTime,
			&f.GroupPurchaseChannel,
			&f.OrderType,
			&f.TicketNo,
			&f.ComboQty,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductSalesComboDetail(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string
		totalSQL string
	)
	rowSQL = `
--套餐销售明细表 ROWS
WITH combo_condition as (
Select  distinct fact.combo_product_id,fact.ticket_no
	FROM
    sales_product_amounts fact
        LEFT JOIN store_caches store_caches
                  ON fact.store_id = store_caches.id
        LEFT JOIN product_caches product_caches
                  ON fact.product_id = product_caches.id
WHERE
	{WHEREHEADER}
)
SELECT
    coalesce(to_char(fact.bus_date, 'YYYY-MM-DD'), '') as bus_date,
    coalesce(fact.store_id, 0),
	COALESCE(branchs[array_length(branchs, 1)], 0) AS branch_region_id,
    coalesce(store_caches.store_type, '0') as store_type,
	case when fact.combo_type=1 then coalesce(fact.product_id, 0) else fact.combo_product_id end AS combo_product_id,
    coalesce(fact.unit, '') as unit,
    coalesce(product_caches.category, 0) as category_id,
	case when fact.combo_type IN (2, 3) then coalesce(fact.product_id, 0) else 0  end AS product_id,
    coalesce(fact.channel_id, 0) as channel_id,
   	case when fact.combo_type IN (2, 3) then  coalesce(fact.price, 0) else 0 end as price,
    case when fact.combo_type IN (2, 3) then coalesce(fact.qty, 0) else 0 end as qty,
	case when fact.combo_type IN (2, 3) then  coalesce(fact.gross_amount2, 0) + coalesce(fact.package_amount, 0) + coalesce(fact.send_fee_for_merchant, 0) + coalesce(fact.service_fee, 0) + coalesce(fact.tip, 0) else 0 end as gross_amount,
    case when fact.combo_type IN (2, 3) then  coalesce(fact.pay_merchant_allowance, 0) + coalesce(fact.promotion_merchant_allowance, 0) + coalesce(fact.other_fee, 0) + coalesce(fact.merchant_send_fee, 0) + coalesce(fact.commission, 0) + coalesce(fact.rounding, 0) - coalesce(fact.overflow_amount, 0) else 0 end as discount_amount,
	case when fact.combo_type IN (2, 3) then  (coalesce(fact.gross_amount2, 0) + coalesce(fact.package_amount, 0) + coalesce(fact.send_fee_for_merchant, 0) + coalesce(fact.service_fee, 0) + coalesce(fact.tip, 0)) - (coalesce(fact.pay_merchant_allowance, 0) + coalesce(fact.promotion_merchant_allowance, 0) + coalesce(fact.other_fee, 0) + coalesce(fact.merchant_send_fee, 0) + coalesce(fact.commission, 0) + coalesce(fact.rounding, 0) - coalesce(fact.overflow_amount, 0)) else 0 end as net_amount,
	case when fact.order_time is null or fact.order_time < '1970-01-01' then '0001-01-01 00:00:00' else to_char(fact.order_time, 'YYYY-MM-DD HH24:MI:SS') end as add_time,
    coalesce(fact.coupon_channel_name, '') as coupon_channel_name,
    coalesce(fact.order_type, '') as order_type,
    coalesce(fact.ticket_no, '') as ticket_no,
    case when fact.combo_type=1 then coalesce(fact.qty, 0) else 0 end as combo_qty,
	0 AS total
FROM
    sales_product_amounts fact
        LEFT JOIN store_caches store_caches
                  ON fact.store_id = store_caches.id
        LEFT JOIN product_caches product_caches
                  ON fact.product_id = product_caches.id
		INNER JOIN combo_condition c
					ON (c.combo_product_id = fact.combo_product_id OR
                        (fact.combo_type = 1 AND c.combo_product_id = fact.product_id)) AND c.ticket_no = fact.ticket_no
		Where   {WHERE}
order by fact.bus_date desc, fact.order_time desc, fact.combo_type, fact.product_id, fact.id
{LIMIT}
`
	totalSQL = `
--套餐销售明细表 Summary
WITH combo_condition AS (SELECT DISTINCT fact.combo_product_id, fact.ticket_no
                         FROM sales_product_amounts fact
                                  LEFT JOIN store_caches store_caches
                                            ON fact.store_id = store_caches.id
                                  LEFT JOIN product_caches product_caches
                                            ON fact.product_id = product_caches.id
                         WHERE {WHEREHEADER}
	                    )
SELECT ''                                                                       AS bus_date,
       0                                                                        AS store_id,
       0                                                                        AS branch_region_id,
       '0'                                                                      AS store_type,
       0                                                                        AS combo_product_id,
       ''                                                                       AS unit,
       0                                                                        AS category_id,
       0                                                                        AS product_id,
       0                                                                        AS channel_id,
       0                                                                        AS price,
       SUM(CASE WHEN fact.combo_type IN (2, 3) THEN COALESCE(fact.qty, 0) ELSE 0 END) AS qty,
       SUM(CASE
               WHEN fact.combo_type IN (2, 3) THEN COALESCE(fact.gross_amount2, 0) + COALESCE(fact.package_amount, 0) +
                                             COALESCE(fact.send_fee_for_merchant, 0) + COALESCE(fact.service_fee, 0) +
                                             COALESCE(fact.tip, 0)
               ELSE 0 END)                                                      AS gross_amount,
       SUM(CASE
               WHEN fact.combo_type IN (2, 3) THEN
                   COALESCE(fact.pay_merchant_allowance, 0) + COALESCE(fact.promotion_merchant_allowance, 0) +
                   COALESCE(fact.other_fee, 0) + COALESCE(fact.merchant_send_fee, 0) + COALESCE(fact.commission, 0) +
                   COALESCE(fact.rounding, 0) - COALESCE(fact.overflow_amount, 0)
               ELSE 0 END)                                                      AS discount_amount,
       SUM(CASE
               WHEN fact.combo_type IN (2, 3) THEN (COALESCE(fact.gross_amount2, 0) + COALESCE(fact.package_amount, 0) +
                                              COALESCE(fact.send_fee_for_merchant, 0) + COALESCE(fact.service_fee, 0) +
                                              COALESCE(fact.tip, 0)) - (COALESCE(fact.pay_merchant_allowance, 0) +
                                                                        COALESCE(fact.promotion_merchant_allowance, 0) +
                                                                        COALESCE(fact.other_fee, 0) +
                                                                        COALESCE(fact.merchant_send_fee, 0) +
                                                                        COALESCE(fact.commission, 0) +
                                                                        COALESCE(fact.rounding, 0) -
                                                                        COALESCE(fact.overflow_amount, 0))
               ELSE 0 END)                                                      AS net_amount,
       ''                                                                       AS add_time,
       ''                                                                       AS coupon_channel_name,
       ''                                                                       AS order_type,
       ''                                                                       AS ticket_no,
       SUM(CASE WHEN fact.combo_type = 1 THEN COALESCE(fact.qty, 0) ELSE 0 END) AS combo_qty,
       COUNT(1)                                                                 AS total
FROM sales_product_amounts fact
         LEFT JOIN store_caches store_caches
                   ON fact.store_id = store_caches.id
         LEFT JOIN product_caches product_caches
                   ON fact.product_id = product_caches.id
         INNER JOIN combo_condition c
                    ON (c.combo_product_id = fact.combo_product_id OR
                        (fact.combo_type = 1 AND c.combo_product_id = fact.product_id)) AND c.ticket_no = fact.ticket_no
WHERE {WHERE}
`

	whereSQL := generateWhereSQLForProductSalesComboDetail(condition)
	whereHeaderSQL := generateWhereSQLForProductSalesComboHeader(condition)
	limitSQL := generateLimitOffsetSQLForProductSalesComboDetail(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHEREHEADER}", whereHeaderSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	totalSQL = strings.ReplaceAll(totalSQL, "{WHEREHEADER}", whereHeaderSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateWhereSQLForProductSalesComboDetail(condition *model.RepoCondition) string {
	var (
		regionIdsSQL            string // 区域筛选条件
		storeTypeSQL            string // 区域筛选条件
		productIdsSQL           string // 商品筛选条件
		productCategoryIdsSQL   string // 类别筛选条件
		channelIdsSQL           string // 渠道筛选条件
		comboSQL                string // 套餐商品过滤条件
		groupPurchaseChannelSQL string // 团购渠道
		comboProductIdsSQL      string // 套餐商品过滤条件
	)
	comboSQL = fmt.Sprintf(`AND fact.combo_type in (1,2,3)`)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND (fact.product_id IN (%s)  or exists (select 1 from sales_product_amounts where combo_product_id = fact.product_id and fact.combo_type = 1 and product_id IN (%s)  and ticket_no=fact.ticket_no) )",
			helpers.JoinInt64(condition.ProductIds, ","), helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ComboProductIds) > 0 {
		comboProductIdsSQL = fmt.Sprintf(
			"AND (fact.combo_product_id IN (%s) or ( fact.product_id IN (%s) and fact.combo_type =1 ))",
			helpers.JoinInt64(condition.ComboProductIds, ","), helpers.JoinInt64(condition.ComboProductIds, ","),
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if len(condition.CouponChannels) > 0 {
		groupPurchaseChannelSQL = fmt.Sprintf(
			"AND fact.coupon_channel_code IN (%s)",
			helpers.JoinString(condition.CouponChannels, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	if len(condition.ChannelIds) > 0 {
		channelIdsSQL = fmt.Sprintf(`
			AND fact.channel_id IN (%s)
		`, helpers.JoinInt64(condition.ChannelIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	netAmountSQL := ""
	if !condition.IncludeRealAmountZero {
		netAmountSQL = fmt.Sprintf(`
			AND fact.products_real_amount != 0
		`)
	}
	ticketNoSQL := ""
	if condition.TicketNo != "" {
		ticketNoSQL = fmt.Sprintf(`
			AND fact.ticket_no = '%s'
		`, condition.TicketNo)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, storeSQL, comboSQL, regionCodesSQL, netAmountSQL, channelIdsSQL, storeTypeSQL,
		groupPurchaseChannelSQL, comboProductIdsSQL, ticketNoSQL)
	return whereSQL
}

func generateLimitOffsetSQLForProductSalesComboDetail(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateWhereSQLForProductSalesComboHeader(condition *model.RepoCondition) string {
	var (
		regionIdsSQL            string // 区域筛选条件
		storeTypeSQL            string // 区域筛选条件
		productIdsSQL           string // 商品筛选条件
		productCategoryIdsSQL   string // 类别筛选条件
		channelIdsSQL           string // 渠道筛选条件
		comboSQL                string // 套餐商品过滤条件
		groupPurchaseChannelSQL string // 团购渠道
		comboProductIdsSQL      string // 套餐商品过滤条件
	)
	comboSQL = fmt.Sprintf(`AND fact.combo_type in (2,3)`)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ComboProductIds) > 0 {
		comboProductIdsSQL = fmt.Sprintf(
			"AND fact.combo_product_id IN (%s)",
			helpers.JoinInt64(condition.ComboProductIds, ","),
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if len(condition.CouponChannels) > 0 {
		groupPurchaseChannelSQL = fmt.Sprintf(
			"AND fact.coupon_channel_code IN (%s)",
			helpers.JoinString(condition.CouponChannels, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	if len(condition.ChannelIds) > 0 {
		channelIdsSQL = fmt.Sprintf(`
			AND fact.channel_id IN (%s)
		`, helpers.JoinInt64(condition.ChannelIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	netAmountSQL := ""
	if !condition.IncludeRealAmountZero {
		netAmountSQL = fmt.Sprintf(`
			AND fact.products_real_amount != 0
		`)
	}
	ticketNoSQL := ""
	if condition.TicketNo != "" {
		ticketNoSQL = fmt.Sprintf(`
			AND fact.ticket_no = '%s'
		`, condition.TicketNo)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, storeSQL, comboSQL, regionCodesSQL, netAmountSQL, channelIdsSQL, storeTypeSQL,
		groupPurchaseChannelSQL, comboProductIdsSQL, ticketNoSQL)
	return whereSQL
}
