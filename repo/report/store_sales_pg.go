package repo

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

// 门店销售报表
func (rsmm *SalesRepositoryPG) StoreSales(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL, summarySQL, businessDaySQL := generateQuerySQLForStoreSales(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	ch3 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, summarySQL, ch2)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, businessDaySQL, ch3)
	rows := <-ch1
	summarys := <-ch2
	businessDays := <-ch3
	close(ch1)
	close(ch2)
	close(ch3)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	// 将查询到的营业日期映射到每一行上面
	fmt.Println(businessDays)
	var businessDaysMap = make(map[string]int64)
	for _, bd := range businessDays {
		bdMap := cast.ToStringMap(bd)
		businessDaysMap[cast.ToString(bdMap["store_id"])] = cast.ToInt64(bdMap["business_days"])
	}
	for _, r := range rows {
		rowMap := cast.ToStringMap(r)
		rowMap["business_days"] = businessDaysMap[cast.ToString(rowMap["region_id"])]
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

// 生成sum和sql
func generateAggsSQLForStoreSales(condition *model.RepoCondition) string {
	if condition.IsPre {
		return `
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(order_count) AS order_count,
			SUM(product_count) AS product_count,
			SUM(accessory_count) AS accessory_count,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(tip_returned) AS tip_returned,
			SUM(package_fee_returned) AS package_fee_returned,
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(service_fee_returned) AS service_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(pay_amount_returned) AS pay_amount_returned,
			SUM(rounding_returned) AS rounding_returned,
			SUM(overflow_amount_returned) AS overflow_amount_returned,
			SUM(change_amount_returned) AS change_amount_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_0_returned) AS amount_0_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(amount_4_returned) AS amount_4_returned
		`
	}
	return `
			max(store_caches.store_type) AS store_type,
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(eticket_count) AS order_count,
			SUM(product_count) AS product_count,
			SUM(accessory_count) AS accessory_count,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
			SUM(platform_discount_amount) AS platform_discount_amount,
			SUM(CASE WHEN refunded THEN platform_discount_amount ELSE 0 END) AS platform_discount_amount_returned,
			SUM(CASE WHEN refunded THEN merchant_discount_amount ELSE 0 END) AS merchant_discount_amount_returned,
			SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
			SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
			SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
			SUM(CASE WHEN refunded THEN tip ELSE 0 END) AS tip_returned,
			SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
			SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
			SUM(CASE WHEN refunded THEN service_fee ELSE 0 END) AS service_fee_returned,
			SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
			SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
			SUM(CASE WHEN refunded THEN pay_amount ELSE 0 END) AS pay_amount_returned,
			SUM(CASE WHEN refunded THEN rounding ELSE 0 END) AS rounding_returned,
			SUM(CASE WHEN refunded THEN overflow_amount ELSE 0 END) AS overflow_amount_returned,
			SUM(CASE WHEN refunded THEN change_amount ELSE 0 END) AS change_amount_returned,
			SUM(CASE WHEN refunded THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_returned,
			SUM(CASE WHEN refunded THEN discount_transfer_amount ELSE 0 END) AS discount_transfer_amount_returned,
			SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
       		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
       		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
       		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
			SUM(commission) AS commission,
            SUM(payment_transfer_amount) AS payment_transfer_amount,
            SUM(discount_transfer_amount) AS discount_transfer_amount,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS receivable,
			SUM(merchant_send_fee) AS merchant_send_fee,
			SUM(store_discount_amount) AS store_discount_amount,
			SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
			SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS receivable_returned,
			SUM(CASE WHEN refunded THEN merchant_send_fee ELSE 0 END) AS merchant_send_fee_returned,
			SUM(CASE WHEN refunded THEN store_discount_amount ELSE 0 END) AS store_discount_amount_returned,
			SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS amount_0_returned,
			SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS amount_1_returned,
			SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS amount_2_returned,
			SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS amount_3_returned,
			SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned,
			SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_amount1,
			SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,
			SUM(send_fee) AS send_fee,
			SUM(platform_send_fee) AS platform_send_fee,
			SUM(discount_merchant_contribute) AS discount_merchant_contribute,
			SUM(pay_merchant_contribute) AS pay_merchant_contribute,
			SUM(discount_buyer_contribute+discount_other_contribute) AS real_amount_discount,
			SUM(pay_buyer_contribute+pay_other_contribute) AS real_amount_payment
		`
}

func generateQuerySQLForStoreSales(trackId int64, condition *model.RepoCondition) (string, string, string) {
	// 生成summarySQL的统计项
	aggsSQL := generateAggsSQLForStoreSales(condition)
	// 生成select语句
	selectSQL := generateSelectSQLForStoreSales(condition)
	// 生成查询条件
	whereSQL := generateWhereSQLForStoreSales(condition)
	// 和主档缓存表关联查询
	fromSQL := generateFromSQLForStoreSales(condition)
	// 添加排序类型的过滤
	orderSQL := generateOrderSQLForStoreSales(condition)
	// 按门店和营业日期分组
	groupSQL := generateGroupSQLForStoreSales(condition)
	limitOffsetSQL := generateLimitOffsetSQLForStoreSales(condition)

	// 组装总的rowSQL语句，因为要根据bus_data和store_caches.geo0分组统计，所以用了sum
	rowSQL := fmt.Sprintf(`
	--门店销售rows
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL := fmt.Sprintf(`
	--门店销售summary
		SELECT
			COUNT(1) AS total,
			SUM(order_count_sale)   AS order_count_sale,
			SUM(order_count_partialrefund)   AS order_count_partialrefund,
			SUM(order_count_refund)   AS order_count_refund,
			SUM(gross_amount) AS gross_amount, 
			SUM(net_amount) AS net_amount, 
			SUM(discount_amount) AS discount_amount, 
			SUM(tip) AS tip, 
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee, 
			SUM(service_fee) AS service_fee, 
			SUM(tax_fee) AS tax_fee, 
			SUM(other_fee) AS other_fee, 
			SUM(pay_amount) AS pay_amount, 
			SUM(rounding) AS rounding, 
			SUM(overflow_amount) AS overflow_amount, 
			SUM(change_amount) AS change_amount, 
			SUM(order_count) AS order_count, 
			SUM(product_count) AS product_count, 
			SUM(accessory_count) AS accessory_count, 
			SUM(gross_amount_returned) AS gross_amount_returned, 
			SUM(net_amount_returned) AS net_amount_returned, 
			SUM(discount_amount_returned) AS discount_amount_returned, 
			SUM(tip_returned) AS tip_returned, 
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned, 
			SUM(service_fee_returned) AS service_fee_returned, 
			SUM(tax_fee_returned) AS tax_fee_returned, 
			SUM(other_fee_returned) AS other_fee_returned, 
			SUM(pay_amount_returned) AS pay_amount_returned, 
			SUM(rounding_returned) AS rounding_returned, 
			SUM(overflow_amount_returned) AS overflow_amount_returned, 
			SUM(change_amount_returned) AS change_amount_returned, 
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
            SUM(payment_transfer_amount) AS payment_transfer_amount,
            SUM(discount_transfer_amount) AS discount_transfer_amount,
			SUM(platform_discount_amount) AS platform_allowance,
			SUM(platform_discount_amount_returned) AS platform_discount_amount_returned,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
			SUM(merchant_discount_amount_returned) AS merchant_discount_amount_returned,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_0) AS amount_0,
			SUM(amount_0_returned) AS amount_0_returned,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(receivable) AS receivable,
			SUM(receivable_returned) AS receivable_returned,
			SUM(merchant_send_fee) AS merchant_send_fee,
			SUM(merchant_send_fee_returned) AS merchant_send_fee_returned,
			SUM(store_discount_amount_returned) AS store_discount_amount_returned,
			SUM(store_discount_amount) AS store_discount_amount,
			SUM(discount_amount1) AS discount_contribute,
			SUM(transfer_real_amount) AS transfer_real_amount,
			SUM(send_fee) AS send_fee,
			SUM(platform_send_fee) AS platform_send_fee,
			SUM(discount_merchant_contribute) AS discount_merchant_contribute,
			SUM(pay_merchant_contribute) AS pay_merchant_contribute,
			SUM(real_amount_discount) AS real_amount_discount,
			SUM(real_amount_payment) AS real_amount_payment
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, aggsSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)

	// 查询门店营业日期的sql
	businessDaySQL := generateBusinessDaySql(condition)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG business_days. SQL: `%s`", trackId, businessDaySQL)

	return rowSQL, summarySQL, businessDaySQL
}

func generateBusinessDaySql(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	joinSQL := ""
	if regionIdsSQL != "" {
		joinSQL = fmt.Sprintf(`LEFT JOIN store_caches ON store_caches.id = sales_ticket_amounts.store_id`)
	}
	var businessDaySQL string
	if condition.TagType == "SUMMARY" {
		businessDaySQL = fmt.Sprintf(`
		--营业天数sql
		SELECT 
		store_id, SUM(CASE WHEN business > 0 THEN 1 ELSE 0 END) as business_days
		FROM (
				SELECT store_id, count(1) AS business
				FROM sales_ticket_amounts
				%s
			WHERE
				sales_ticket_amounts.partner_id = %d
				and sales_ticket_amounts.scope_id = %d
				and bus_date >= '%s'
				AND bus_date <= '%s'
				%s
			GROUP BY bus_date, store_id
			) T
		GROUP BY store_id
	`, joinSQL, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"), regionIdsSQL)
	} else {
		businessDaySQL = fmt.Sprintf(`
		--营业天数sql
		SELECT 
		store_id, SUM(CASE WHEN business > 0 THEN 1 ELSE 0 END) as business_days
		FROM (
				SELECT store_id, count(1) AS business
				FROM sales_ticket_amounts
				%s
			WHERE
				sales_ticket_amounts.partner_id = %d
				and sales_ticket_amounts.scope_id = %d
				and bus_date >= '%s'
				AND bus_date <= '%s'
				%s
			GROUP BY store_id
			) T
		GROUP BY store_id
	`, joinSQL, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"), regionIdsSQL)
	}

	return businessDaySQL
}

func generateDateSQLForStoreSales(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	// 作为周期组
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

// 根据区域查询条件生成区域分组sql
func generateRegionSQLForStoreSales(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateSelectSQLForStoreSales(condition *model.RepoCondition) string {
	// 根据查询时间条件生成时间分组语句：按天按周按月
	_, dateSQL := generateDateSQLForStoreSales(condition)
	// 根据区域查询条件生成区域分组sql：区域汇总
	_, regionSQL := generateRegionSQLForStoreSales(condition)
	// 生成统计项头
	aggsSQL := generateAggsSQLForStoreSales(condition)
	var selectSQL string
	// 根据tag标签判断详细/汇总
	if dateSQL != "" {
		selectSQL = fmt.Sprintf(`
		%s,
		%s,
		%s
	`, dateSQL, regionSQL, aggsSQL)
	} else {
		selectSQL = fmt.Sprintf(`
		%s,
		%s
	`, regionSQL, aggsSQL)
	}
	return selectSQL
}

func generateWhereSQLForStoreSales(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	// 	渠道过滤条件
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	// 订单类型过滤条件
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	var storeSQL, orderStatusSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}

	if condition.OrderStatus != "" {
		orderStatusSQL = fmt.Sprintf(`
				AND sales_ticket_amounts.order_status = '%s'
			`, condition.OrderStatus)
	}
	regionId, _ := generateRegionSQLForStoreSales(condition)
	// 增加门店类型和开店类型的过滤
	storeAndOpenSQL := generateStoreOpenWhereSql(condition)
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		AND is_non_sales = FALSE
		%s
		%s
		%s
		%s
		%s
		AND %s > 0
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL, storeAndOpenSQL, storeSQL, regionId, orderStatusSQL)
	return whereSQL
}

// 根据门店类型和开店类型生成sql
func generateStoreOpenWhereSql(condition *model.RepoCondition) string {
	var StoreStatusSql string
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			StoreStatusSql = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			StoreStatusSql = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		StoreStatusSql += fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	return StoreStatusSql
}

func generateFromSQLForStoreSales(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre { // 是否使用预计算，就是是否使用flink计算，如果数据大的话，可以用
		leftJoinSQL = `
			store_sales
				JOIN store_caches ON store_sales.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_ticket_amounts
			JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
	`
	}
	return leftJoinSQL
}

func generateGroupSQLForStoreSales(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForStoreSales(condition)
	regionSQL, _ := generateRegionSQLForStoreSales(condition)
	var groupSQL string
	if dateSQL != "" {
		groupSQL = fmt.Sprintf(`
		%s,
	`, dateSQL)
	}
	groupSQL += fmt.Sprintf(`
		%s
	`, regionSQL)
	return groupSQL
}

func generateOrderSQLForStoreSales(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForStoreSales(condition)
	regionSQL, _ := generateRegionSQLForStoreSales(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
	`, dateSQL)
	}
	orderSQL += fmt.Sprintf(`
		%s
	`, regionSQL)
	return orderSQL
}

func generateLimitOffsetSQLForStoreSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
