package repo

import (
    "context"
    "database/sql"
    "fmt"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/model/report"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "strings"
)

const productMakeTimeRowTpl = `
-- 商品制作时间 rows
with base as (
    select fact.*, {REGION_ID} as region_id, store_caches.store_type as store_type, product_caches.category as category_id
    from sales_product_make_time fact
        left join store_caches on fact.store_id = store_caches.id
        left join product_caches on fact.product_id = product_caches.id
    where fact.deleted=0 and {WHERE}
)
select region_id, 
    max(store_type) as store_type,
    order_hour, 
    product_id, 
    coalesce(category_id, 0) as category_id, 
    count(1) as cup_count, 
    round(sum(make_time)::numeric / count(1)::numeric / 60, 2) as avg_per_cup
from base
group by region_id, product_id, order_hour, category_id
order by region_id, product_id, order_hour, category_id
{LIMIT}
`

const productMakeTimeTotalTpl = `
-- 商品制作时间 total
with base as (
    select fact.*, {REGION_ID} as region_id, product_caches.category as category_id
    from sales_product_make_time fact
        left join store_caches on fact.store_id = store_caches.id
        left join product_caches on fact.product_id = product_caches.id
    where fact.deleted=0 and {WHERE}
),
total as (
    select 1
    from base
    group by region_id, product_id, order_hour, category_id
)
select count(1) as total 
from total
`

func (rsmm *MakeTimeRepositoryPG) ProductMakeTime(ctx context.Context, condition *model.RepoCondition) (*report.ProductMakeTimeResponse, error) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))

    rowSql, totalSql := productMakeTimeSql(ctx, condition)

    rowCh := make(chan []*report.ProductMakeTimeItem, 0)
    totalCh := make(chan int64, 0)

    go productMakeTimeExecuteRow(trackId, rowSql, rsmm.DB, rowCh)
    go productMakeTimeExecuteTotal(trackId, totalSql, rsmm.DB, totalCh)

    resp := &report.ProductMakeTimeResponse{
        Rows:  <-rowCh,
        Total: <-totalCh,
    }
    close(rowCh)
    close(totalCh)
    return resp, nil
}

func productMakeTimeExecuteRow(trackId int64, sql string, db *sql.DB, ch chan []*report.ProductMakeTimeItem) {
    results := make([]*report.ProductMakeTimeItem, 0)
    r, err := db.Query(sql)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        ch <- results
        return
    }
    defer r.Close()

    for r.Next() {
        f := new(report.ProductMakeTimeItem)
        if err := r.Scan(
            &f.RegionId,
            &f.StoreType,
            &f.MakeHour,
            &f.ProductId,
            &f.ProductCategoryId,
            &f.CupCount,
            &f.MakeTimeAvgPerCup,
        ); err != nil {
            logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
            ch <- results
            return
        }
        results = append(results, f)
    }
    ch <- results
    return
}

func productMakeTimeExecuteTotal(trackId int64, sql string, db *sql.DB, ch chan int64) {
    var res int64 = 0
    r := db.QueryRow(sql)
    err := r.Scan(&res)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        ch <- res
        return
    }
    ch <- res
}

func productMakeTimeSql(ctx context.Context, condition *model.RepoCondition) (rowSql string, totalSql string) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))

    whereSQL := productMakeTimeWhere(condition)
    regionSQL := productMakeTimeRegion(condition)
    limitSQL := productMakeTimeLimit(condition)
    rowSql = strings.ReplaceAll(productMakeTimeRowTpl, "{WHERE}", whereSQL)
    rowSql = strings.ReplaceAll(rowSql, "{LIMIT}", limitSQL)
    rowSql = strings.ReplaceAll(rowSql, "{REGION_ID}", regionSQL)

    totalSql = strings.ReplaceAll(productMakeTimeTotalTpl, "{WHERE}", whereSQL)
    totalSql = strings.ReplaceAll(totalSql, "{REGION_ID}", regionSQL)

    logger.Pre().Debugf("[%d] repo.report.MakeTimeRepositoryPG row. SQL: `%s`", trackId, rowSql)
    logger.Pre().Debugf("[%d] repo.report.MakeTimeRepositoryPG total. SQL: `%s`", trackId, totalSql)
    return
}

func productMakeTimeWhere(condition *model.RepoCondition) string {
    var (
        regionIdsSQL          string // 区域筛选条件
        productIdsSQL         string // 商品筛选条件
        productCategoryIdsSQL string // 类别筛选条件
        storeTypeSQL          string // 门店类型筛选条件
        openStatusSQL         string // 开店类型筛选条件
        periodSQL             string // 时段筛选条件
    )
    regionIdsSQL = GenerateRegionWhereSQL(condition)
    if len(condition.ProductIds) > 0 {
        productIdsSQL = fmt.Sprintf(
            "AND product_caches.id IN (%s)",
            helpers.JoinInt64(condition.ProductIds, ","),
        )
    }
    if len(condition.ProductCategoryIds) > 0 {
        productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
        )
    }

    // 门店类型支持多选
    if len(condition.StoreTypes) > 0 {
        if len(condition.StoreTypes) == 1 {
            storeTypeSQL = fmt.Sprintf(
                "AND store_caches.store_type = '%s'",
                condition.StoreTypes[0],
            )
        } else {
            storeTypeSQL = fmt.Sprintf(
                "AND store_caches.store_type in (%s)",
                helpers.JoinString(condition.StoreTypes, ","),
            )
        }
    }
    if condition.OpenStatus != "" {
        openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
    }
    if len(condition.Period) != 0 {
        periodSQL = fmt.Sprintf(`
			 AND fact.order_hour IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
    }
    var storeSQL string
    if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
        if len(condition.StoreIDs) == 1 {
            storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
        } else {
            storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
        }
    }
    channelSQL := ""
    if len(condition.Channels) != 0 {
        tpNames := make([]string, 0)
        for _, ch := range condition.Channels {
            tpNames = append(tpNames, channelCodeToTpName(ch))
        }
        channelSQL = fmt.Sprintf("AND fact.channel_name = '%s'", strings.Join(tpNames, "','"))
    }
    whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.Start.Format("2006-01-02"),
        condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
        productCategoryIdsSQL, storeTypeSQL, openStatusSQL, periodSQL, storeSQL, channelSQL)
    return whereSQL
}

func productMakeTimeRegion(condition *model.RepoCondition) string {
    regionSQL := "fact.store_id"
    switch condition.RegionGroupType {
    case "GEO_REGION": // 地理区域
        switch condition.RegionGroupLevel {
        case 0, 1, 2:
            regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
        }
    case "BRANCH_REGION": // 管理区域
        switch condition.RegionGroupLevel {
        case 0, 1, 2:
            regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
        }
    case "FRANCHISEE_REGION": //加盟商区域
        switch condition.RegionGroupLevel {
        case 0, 1, 2:
            regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
        }
    case "COMPANY": // 按公司
        regionSQL = fmt.Sprintf("store_caches.company_info")
    }
    return regionSQL
}

func productMakeTimeLimit(condition *model.RepoCondition) string {
    limitOffsetSQL := ""
    if condition.Limit == -1 {
        return limitOffsetSQL
    }
    if condition.Limit < 0 {
        condition.Limit = 10
    }
    if condition.Offset < 0 {
        condition.Offset = 0
    }
    limitOffsetSQL = fmt.Sprintf(
        "LIMIT %d OFFSET %d",
        condition.Limit, condition.Offset,
    )
    return limitOffsetSQL
}
