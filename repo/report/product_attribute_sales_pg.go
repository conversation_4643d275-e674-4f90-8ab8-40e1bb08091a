package repo

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductAttributeSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductAttributeSalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductAttributeSales(trackId, condition)
	ch1 := make(chan []*report.ProductAttributeSalesRow, 1)
	ch2 := make(chan []*report.ProductAttributeSalesRow, 1)
	start := time.Now()
	go queryDBWithChanForProductAttributeSales(ctx, rsmm.GormDB, rowSQL, ch1)
	go queryDBWithChanForProductAttributeSales(ctx, rsmm.GormDB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var total int64
	if len(summarys) > 0 {
		total = summarys[0].Total
	}
	response := &report.ProductAttributeSalesResponse{
		Rows:  rows,
		Total: total,
	}
	return response, nil
}

func queryDBWithChanForProductAttributeSales(ctx context.Context, db *gorm.DB, sqlStr string, ch chan []*report.ProductAttributeSalesRow) {
	ch <- queryDBForProductAttributeSales(ctx, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForProductAttributeSales(ctx context.Context, db *gorm.DB, sqlStr string) []*report.ProductAttributeSalesRow {
	results := make([]*report.ProductAttributeSalesRow, 0)
	query := db.WithContext(ctx).Raw(sqlStr).Find(&results)
	if query.Error != nil {
		logger.Pre().WithContext(ctx).Errorf("pkg.helpers.QueryDB. Err: `%v`", query.Error)
		return results
	}
	return results
}

func generateQuerySQLForProductAttributeSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)

	rowSQL = `
		WITH base AS (
			SELECT
				 fact.store_id as store_id,

					fact.value_code as  attribute_code,
					fact.code as  attribute_group_code,
    				sum(qty) as qty
				FROM  sales_product_sku_remark fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
				 fact.store_id, fact.value_code, fact.code
		), 
		base_limit as (
		select * from  base as base_limit
		ORDER BY	{ORDER_BY}
			{LIMIT} 	),
		daily_summary as (
			SELECT  fact.store_id,
    				COALESCE(sum(qty),0) as sum_qty 
				FROM  sales_product_sku_remark fact
			WHERE
				{SUB_WHERE}
		 GROUP BY fact.store_id
	),
	base_attribute AS (
			SELECT
				base_limit.store_id,
				base_limit.attribute_code,
    			max(CASE WHEN fact.value_name ~* '[A-Za-z]+|\d+/\d+' THEN COALESCE(fact.value_name) ELSE NULL END) as attribute_name,
				base_limit.attribute_group_code,
				max(CASE WHEN fact.name ~* '[A-Za-z]+|\d+/\d+' THEN COALESCE(fact.name) ELSE NULL END) as attribute_group_name,
				sum(fact.qty) as qty,
				(case When  sum(daily_summary.sum_qty)=0 then 0 else sum(fact.qty::float)/sum(daily_summary.sum_qty)::float end)  as qty_ratio
			FROM
				sales_product_sku_remark fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
					INNER JOIN base_limit
							ON fact.store_id = base_limit.store_id
							AND fact.value_code=base_limit.attribute_code
    						AND fact.code =base_limit.attribute_group_code
					INNER JOIN  daily_summary  ON fact.store_id = daily_summary.store_id
			WHERE
				{WHERE}
			GROUP BY
				base_limit.store_id ,
				base_limit.attribute_code,
				base_limit.attribute_group_code
			ORDER BY {ORDER_BY} )
		select * from  base_attribute
	`
	summarySQL = `
			WITH base AS (
			SELECT
					fact.store_id as store_id,
					fact.value_code,
    				fact.code,
    				sum(qty) as qty
				FROM  sales_product_sku_remark fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
					fact.store_id , fact.value_code, fact.code
		)
		select count(1) as total from  base
	`
	orderBy := "base_limit.attribute_code,base_limit.store_id"
	if condition.Sort != "" {
		orderBy = condition.Sort + " "
		if condition.Desc {
			orderBy += "DESC"
		} else {
			orderBy += "ASC"
		}
	}
	rowSQL = strings.ReplaceAll(rowSQL, "{ORDER_BY}", orderBy)
	subWhereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
	`, condition.PartnerId, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"))
	whereSQL := generateWhereSQLForProductAttributeSales(condition)
	regionSQL := generateRegionSQLForProductAttributeSales(condition)
	limitSQL := generateLimitOffsetSQLForProductAttributeSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{SUB_WHERE}", subWhereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForProductAttributeSales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForProductAttributeSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL      string // 区域筛选条件
		attributeCodesSQL string
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	if len(condition.ProductAttributesCodes) > 0 {
		attributeCodesSQL = fmt.Sprintf(`
			AND fact.code IN (%s)
		`, helpers.JoinString(condition.ProductAttributesCodes, ","))
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, attributeCodesSQL)
	return whereSQL
}

func generateLimitOffsetSQLForProductAttributeSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
