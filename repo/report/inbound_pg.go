package repo

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

const inboundRowTpl = `
-- 入库报表 rows
select
	coalesce(fact.sku_id, 0) as sku_id,
	coalesce(to_char(fact.inbound_time at time zone 'cct', 'YYYY-MM-DD HH24:MI'), '') as inbound_time,
	coalesce(store_caches.store_name, '') as store_name,
	coalesce(fact.operator_name, '') as operator_name,
	coalesce(fact.sku_code, '') as product_code,
	coalesce(fact.sku_name, '') as product_name,
	coalesce(fact.price + fact.acc_price, 0) as price,
	coalesce(fact.qty, 0) as qty,
	coalesce((fact.price + fact.acc_price) * fact.qty, 0) as amount,
	sales_inbound_content.content as content
from sales_inbound_product fact
	left join store_caches on fact.store_id = store_caches.id
	left join sales_inbound_content on fact.inbound_id = sales_inbound_content.inbound_id
where fact.deleted = 0 and {WHERE}
order by fact.inbound_time desc, fact.created desc, fact.id
{LIMIT}
`

const inboundTotalTpl = `
-- 入库报表 total
select
	count(1) as total
from sales_inbound_product fact
	left join store_caches on fact.store_id = store_caches.id
where deleted = 0 and {WHERE}
`

func (rsmm *InboundRepositoryPG) Inbound(ctx context.Context, condition *model.RepoCondition) (*report.InboundResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	rowSql, totalSql := inboundSql(ctx, condition)

	rowCh := make(chan []*report.InboundRow, 0)
	totalCh := make(chan int64, 0)

	go inboundExecuteRow(trackId, rowSql, rsmm.DB, rowCh)
	go inboundExecuteTotal(trackId, totalSql, rsmm.DB, totalCh)

	resp := &report.InboundResponse{
		Rows:  <-rowCh,
		Total: <-totalCh,
	}
	close(rowCh)
	close(totalCh)
	return resp, nil
}

func inboundExecuteRow(trackId int64, sql string, db *sql.DB, ch chan []*report.InboundRow) {
	results := make([]*report.InboundRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- results
		return
	}
	defer r.Close()

	for r.Next() {
		f := new(report.InboundRow)
		if err := r.Scan(
			&f.ProductId,
			&f.InboundTime,
			&f.StoreName,
			&f.OperatorName,
			&f.ProductCode,
			&f.ProductName,
			&f.Price,
			&f.Qty,
			&f.Amount,
			&f.Content,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- results
			return
		}
		results = append(results, f)
	}
	ch <- results
	return
}

func inboundExecuteTotal(trackId int64, sql string, db *sql.DB, ch chan int64) {
	var res int64 = 0
	r := db.QueryRow(sql)
	err := r.Scan(&res)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- res
		return
	}
	ch <- res
}

func inboundSql(ctx context.Context, condition *model.RepoCondition) (rowSql string, totalSql string) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	whereSQL := inboundWhere(condition)
	limitSQL := inboundLimit(condition)
	rowSql = strings.ReplaceAll(inboundRowTpl, "{WHERE}", whereSQL)
	rowSql = strings.ReplaceAll(rowSql, "{LIMIT}", limitSQL)

	totalSql = strings.ReplaceAll(inboundTotalTpl, "{WHERE}", whereSQL)

	logger.Pre().Debugf("[%d] repo.report.InboundRepositoryPG row. SQL: `%s`", trackId, rowSql)
	logger.Pre().Debugf("[%d] repo.report.InboundRepositoryPG total. SQL: `%s`", trackId, totalSql)
	return
}

func inboundWhere(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}

	var productSQL string
	if condition.ProductIds != nil && len(condition.ProductIds) > 0 {
		productSQL = fmt.Sprintf(`
			AND fact.sku_id IN (%s)
		`, helpers.JoinInt64(condition.ProductIds, ","))
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.inbound_time >= ('%sT16:00:00.000000Z'::timestamptz)
		AND fact.inbound_time <= ('%sT15:59:59.999999Z'::timestamptz)
		%s
		%s
		%s
	`, condition.PartnerId, condition.Start.AddDate(0, 0, -1).Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, productSQL)
	return whereSQL
}

func inboundLimit(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
