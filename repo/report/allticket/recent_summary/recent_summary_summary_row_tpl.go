package recent_summary

const (
	SummaryRowSqlTpl = `
-- 近期营业 summary rows
with business_days as (
	select
		region_id, count(1) as days
	from(
		select
			{REGION_ID} as region_id
		from sales_ticket_amounts_all fact
			left join store_caches store_caches
				on store_caches.id = fact.store_id
		where
			{WHERE}
			and {REGION_ID} > 0
		group by
			fact.bus_date, {REGION_ID}
		order by fact.bus_date desc,{REGION_ID}
	)T
	group by region_id
), 
amounts_bychannel as (
    select
        {REGION_ID} as region_id,
        max(store_caches.store_type) as store_type,
        channel_id,
        sum(amount_0) as business_amount,
        sum(amount_0 - (discount_merchant_contribute + commission + other_fee + merchant_send_fee + pay_merchant_contribute + rounding - overflow_amount))
               as finance_real_amount,
        sum(discount_merchant_contribute + commission + other_fee + merchant_send_fee + pay_merchant_contribute + rounding - overflow_amount)
               as finance_expend_amount,
        sum(eticket_count) as valid_order_count
    from sales_ticket_amounts_all as fact 
        left join store_caches on fact.store_id = store_caches.id
    where 
		{WHERE}
    group by {REGION_ID}, fact.channel_id
),
pays_bypayment as (
    select
        {REGION_ID} as region_id,
        payment_id,
        cast (fact.channel_id as bigint) as channel_id,
        string_agg(distinct payment_name, ',') as payment_name,
		sum(case when finance_pay_amount_used then finance_pay_amount else tp_allowance+cost end) as pay_amount,
        sum(case when finance_pay_amount_used then finance_pay_amount else tp_allowance+cost end)
            as transfer_real_amount
    from sales_payment_amounts_all fact 
        left join store_caches on fact.store_id = store_caches.id
    where 
        {WHERE} 
    group by {REGION_ID}, fact.payment_id, fact.channel_id
),
pays_sum as (
    select
        channel_id,
        region_id,
        sum(transfer_real_amount) as transfer_real_amount,
        sum(pay_amount) as pay_amount,
        JSON_AGG(
            JSON_BUILD_OBJECT(
                'payment_id', fact.payment_id, --支付id
                'payment_name', fact.payment_name, --支付名称
                'pay_amount', fact.pay_amount, -- 实付金额
                'transfer_real_amount', fact.transfer_real_amount -- 实付金额
            )
        ) as payments
    from pays_bypayment as fact
    group by region_id, fact.channel_id
),
pays_sum_bypayment as (
    select
        {REGION_ID} as region_id,
        payment_id,
        string_agg(distinct payment_name, ',') as payment_name,
		sum(case when finance_pay_amount_used then finance_pay_amount else tp_allowance+cost end) as pay_amount,
        sum(case when finance_pay_amount_used then finance_pay_amount else tp_allowance+cost end)
            as transfer_real_amount
    from sales_payment_amounts_all fact 
        left join store_caches on fact.store_id = store_caches.id
    where 
        {WHERE} 
    group by {REGION_ID}, fact.payment_id
),
pays_sum_all as (
	 select
		 region_id,
		 sum(transfer_real_amount) as transfer_real_amount,
		 sum(pay_amount) as pay_amount,
		 JSON_AGG(
				 JSON_BUILD_OBJECT(
						 'payment_id', fact.payment_id, --支付id
						 'payment_name', fact.payment_name, --支付名称
						 'pay_amount', fact.pay_amount, -- 实付金额
						 'transfer_real_amount', fact.transfer_real_amount -- 实付金额
					 )
			 ) as payments
	 from pays_sum_bypayment as fact
	 group by region_id
 ),
amounts_sum as (
    select
        fact.region_id,
        max(fact.store_type) as store_type,
        sum(fact.business_amount) as business_amount,
        sum(fact.finance_real_amount) as finance_real_amount,
        sum(fact.finance_expend_amount) as finance_expend_amount,
        sum(fact.valid_order_count) as valid_order_count,
        sum(pays_sum.transfer_real_amount) as transfer_real_amount,
        sum(pays_sum.pay_amount) as pay_amount,
        JSON_AGG(
            JSON_BUILD_OBJECT(
                    'channel_id', fact.channel_id, --渠道id
                    'business_amount', fact.business_amount,
                    'finance_real_amount', fact.finance_real_amount,
                    'finance_expend_amount', fact.finance_expend_amount,
                    'pay_amount', pays_sum.pay_amount,
                    'transfer_real_amount', pays_sum.transfer_real_amount,
                    'valid_order_count', fact.valid_order_count
                )
        ) as "channels"
    from amounts_bychannel as fact left join pays_sum 
        on fact.region_id = pays_sum.region_id and fact.channel_id = pays_sum.channel_id
    group by fact.region_id
)
select
    ''                           				   as bus_date,
    coalesce(business_days.days, 0)                as business_days,
    coalesce(amounts_sum.region_id, 0)             as region_id,
    coalesce(amounts_sum.store_type, '')           as store_type,
    coalesce(amounts_sum.business_amount, 0)       as business_amount,
    coalesce(amounts_sum.finance_real_amount, 0)   as finance_real_amount,
    coalesce(amounts_sum.finance_expend_amount, 0) as finance_expend_amount,
    coalesce(amounts_sum.valid_order_count, 0)     as valid_order_count,
    coalesce(amounts_sum.pay_amount, 0)  		   as pay_amount,
    coalesce(amounts_sum.transfer_real_amount, 0)  as transfer_real_amount,
    coalesce(amounts_sum.channels, '[]'::json)     as child,
    coalesce(pays_sum_all.payments, '[]'::json)    as payments,
    0 as total
from amounts_sum
    left join pays_sum_all on amounts_sum.region_id = pays_sum_all.region_id
    left join business_days on amounts_sum.region_id = business_days.region_id
order by amounts_sum.region_id
{LIMIT}
`
)
