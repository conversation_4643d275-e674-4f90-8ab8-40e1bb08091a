package all_repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	repo "gitlab.hexcloud.cn/histore/sales-report/repo/report"
	"gitlab.hexcloud.cn/histore/sales-report/repo/report/allticket/recent_summary"
	"strings"
)

func (rsmm *AllTicketSalesRepositoryPG) RecentSummary(ctx context.Context, condition *model.RepoCondition) (*report.RecentSummaryResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQL(trackId, condition)
	ch1 := make(chan []*report.RecentSummary, 1)
	ch2 := make(chan []*report.RecentSummary, 1)
	go queryDBWithChan(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChan(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.RecentSummary
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.RecentSummaryResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChan(id int64, db *sql.DB, sql string, ch chan []*report.RecentSummary) {
	ch <- queryDB(id, db, sql)
}

func queryDB(trackId int64, db *sql.DB, sqlStr string) []*report.RecentSummary {
	results := make([]*report.RecentSummary, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.RecentSummary)
		var child, payments json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.BusinessDays,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessAmount,
			&f.FinanceRealAmount,
			&f.FinanceExpendAmount,
			&f.ValidOrderCount,
			&f.PayAmount,
			&f.TransferRealAmount,
			&child,
			&payments,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(child, &f.Child)
		if err != nil {
			logger.Pre().Error("近期营业汇总child结构体解析失败：", err)
			return results
		}
		err = json.Unmarshal(payments, &f.Payments)
		if err != nil {
			logger.Pre().Error("近期营业汇总payments结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results

}

func generateQuerySQL(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)

	whereSQL := generateWhereSQL(condition)
	regionSQL := generateRegionSQL(condition)
	limitSQL := generateLimitOffsetSQL(condition)

	if condition.TagType == "SUMMARY" {
		rowSQL = recent_summary.SummaryRowSqlTpl
		summarySQL = strings.ReplaceAll(recent_summary.SumSqlTpl, "{TOTAL_COUNT_GROUP_BY}", "")
	} else {
		if limitSQL != "" {
			rowSQL = recent_summary.DetailRowSqlTpl
		} else {
			rowSQL = recent_summary.DetailRowNoLimitSqlTpl
		}
		summarySQL = strings.ReplaceAll(recent_summary.SumSqlTpl, "{TOTAL_COUNT_GROUP_BY}", "fact.bus_date,")
	}

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)

	logger.Pre().Debugf("[%d] repo.report.RecentSummary row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)

	logger.Pre().Debugf("[%d] repo.report.RecentSummary Summary SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQL(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQL(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		channelIdSQL  string // 渠道筛选条件
		orderTypeSQL  string // 订单类型筛选条件
		storeTypeSQL  string // 增加门店类型的过滤
		openStatusSQL string // 增加开店类型的过滤
	)
	regionIdsSQL = repo.GenerateRegionWhereSQL(condition)
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = '%d'",
			condition.ChannelId,
		)
	}
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var whereTpl string
	if condition.IsNatural {
		whereTpl = `
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.natural_date >= '%s'
		AND fact.natural_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		`
	} else {
		whereTpl = `
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		`
	}
	whereSQL := fmt.Sprintf(whereTpl, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL, storeTypeSQL, openStatusSQL, storeSQL)
	return whereSQL
}

func generateLimitOffsetSQL(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
