package all_repo

import (
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"testing"
	"time"
)

/**

var a = [
    [{"channel_id" : '4556456586024976384', "business_amount" : 0.0000000000000000, "finance_real_amount" : 0.0000000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 0.0000000000000000, "valid_order_count" : 0}, {"channel_id" : '4556456907761647616', "business_amount" : 69.5200000000000000, "finance_real_amount" : 69.5200000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 69.5200000000000000, "valid_order_count" : 8}],
    [{"channel_id" : '4556456586024976384', "business_amount" : 9.0400000000000000, "finance_real_amount" : 9.0400000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 9.0400000000000000, "valid_order_count" : 1}, {"channel_id" : '4556456907761647616', "business_amount" : 10.0200000000000000, "finance_real_amount" : 15.0000000000000000, "finance_expend_amount" : -4.9800000000000000, "transfer_real_amount" : 15.0000000000000000, "valid_order_count" : 1}],
    [{"channel_id" : '4556456907761647616', "business_amount" : 39.0100000000000000, "finance_real_amount" : 31.0100000000000000, "finance_expend_amount" : 8.0000000000000000, "transfer_real_amount" : 31.0100000000000000, "valid_order_count" : 3}],
    [{"channel_id" : '4556456586024976384', "business_amount" : 0.0300000000000000, "finance_real_amount" : 0.0300000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 0.0300000000000000, "valid_order_count" : 3}, {"channel_id" : '4556456907761647616', "business_amount" : 278.7300000000000000, "finance_real_amount" : 278.7300000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 278.7300000000000000, "valid_order_count" : 20}],
    [{"channel_id" : '4556456907761647616', "business_amount" : 0.0000000000000000, "finance_real_amount" : 0.0000000000000000, "finance_expend_amount" : 0.0000000000000000, "transfer_real_amount" : 0.0000000000000000, "valid_order_count" : 0}]
]

var b = a.reduce((acc0, e0) => {
    return e0.reduce((acc, e) => {
        if (acc[e.channel_id]) {
            acc[e.channel_id].business_amount += e.business_amount
            acc[e.channel_id].finance_real_amount += e.finance_real_amount
            acc[e.channel_id].finance_expend_amount += e.finance_expend_amount
            acc[e.channel_id].transfer_real_amount += e.transfer_real_amount
            acc[e.channel_id].valid_order_count += e.valid_order_count
        } else {
            acc[e.channel_id] = JSON.parse(JSON.stringify(e))
        }
        return acc
    }, acc0)
}, {})

var x = [
    [{"payment_id" : '4557901341011345408', "payment_name" : "现金", "transfer_real_amount" : 10.0200000000000000}, {"payment_id" : '4594028105118613504', "payment_name" : "现金", "transfer_real_amount" : 59.5000000000000000}, {"payment_id" : '4657142411934334976', "payment_name" : "微信支付", "transfer_real_amount" : 0.0000000000000000}],
    [{"payment_id" : '4557901341011345408', "payment_name" : "现金", "transfer_real_amount" : 15.0000000000000000}, {"payment_id" : '4657142411934334976', "payment_name" : "礼品卡支付", "transfer_real_amount" : 9.0400000000000000}],
    [{"payment_id" : '4557901341011345408', "payment_name" : "现金", "transfer_real_amount" : 31.0100000000000000}],
    [{"payment_id" : '4594028105118613504', "payment_name" : "现金", "transfer_real_amount" : 278.7300000000000000}, {"payment_id" : '4657142411934334976', "payment_name" : "微信支付", "transfer_real_amount" : 0.0300000000000000}],
    [{"payment_id" : '4557791781474861056', "payment_name" : "微信支付", "transfer_real_amount" : 0.0000000000000000}, {"payment_id" : '4558605563306508288', "payment_name" : "支付宝", "transfer_real_amount" : 0.0000000000000000}]
]

var y = x.reduce((acc0, e0) => {
    return e0.reduce((acc, e) => {
        if (acc[e.payment_id]) {
            acc[e.payment_id].transfer_real_amount += e.transfer_real_amount
        } else {
            acc[e.payment_id] = JSON.parse(JSON.stringify(e))
        }
        return acc
    }, acc0)
}, {})

*/

func Test_generateQuerySQL(t *testing.T) {
	logger.InitLogger("info", "dev")
	row, summary := generateQuerySQL(1, &model.RepoCondition{
		PartnerId: 1016,
		Limit:     20,
		Offset:    0,
		//TagType:   "SUMMARY",
		TagType:          "DETAILED",
		Start:            time.Date(2022, 12, 26, 0, 0, 0, 0, time.Local),
		End:              time.Date(2022, 12, 27, 0, 0, 0, 0, time.Local),
		IsNatural:        false,
		RegionGroupType:  "STORE",
		RegionGroupLevel: 0,
		//RegionSearchType: "STORE",
		//RegionSearchIds: [],
		//StoreTypes: make([]string, 0),
		//OpenStatus: "",
		//ChannelId: 0,
	})
	fmt.Println(row)
	fmt.Println("---------------- Summary ----------------")
	fmt.Println(summary)
}
