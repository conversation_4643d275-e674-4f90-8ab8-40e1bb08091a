package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductSales(trackId, condition)
	ch1 := make(chan []*report.ProductSales, 1)
	ch2 := make(chan []*report.ProductSales, 1)
	start := time.Now()
	go queryDBWithChanForProductSales(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductSales(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.ProductSales
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductSalesResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductSales(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductSales) {
	ch <- queryDBForProductSales(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForProductSales(trackId int64, db *sql.DB, sqlStr string) []*report.ProductSales {
	results := make([]*report.ProductSales, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductSales)
		var value1 json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.RegionCode,
			&f.RegionName,
			&f.RegionAddress,
			&f.StoreType,
			&f.BusinessDays,
			&f.GrossAmount,
			&f.GrossAmountReturned,
			&f.NetAmount,
			&f.NetAmountReturned,
			&f.DiscountAmount,
			&f.DiscountAmountReturned,
			&f.ItemCount,
			&f.ItemCountReturned,
			&value1,
			&f.TaxFee,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(value1, &f.Data)
		if err != nil {
			logger.Pre().Error("单品销售data结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
		--单品销售报表汇总信息 ROWS
		WITH business_days AS (
			SELECT
				region_id, COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY bus_date,{REGION_ID}
				ORDER BY {REGION_ID}
			) T
			GROUP BY region_id
		), base_limit AS (
			SELECT
				{REGION_ID} AS region_id
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
				{REGION_ID}
			ORDER BY
				{REGION_ID}
			{LIMIT}
		), base_products AS (
			SELECT
				COALESCE({REGION_ID}, 0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(fact.product_id, 0) AS product_id,
				COALESCE(product_caches.category0, 0) AS category0,
				COALESCE(product_caches.category1, 0) AS category1,
				COALESCE(product_caches.category2, 0) AS category2,
				coalesce(fact.unit, '') as unit, --商品单位
				sum(weight) as weight, --商品份量
				SUM(CASE WHEN refunded THEN weight ELSE 0 END) AS weight_returned, --退单商品份量
				SUM(gross_amount) AS gross_amount, --商品流水
				SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
				SUM(net_amount) AS net_amount, --商品实收
				SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
				SUM(discount_amount) AS discount_amount, --折扣金额
				SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
				SUM(qty) AS item_count, --商品数量
				SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
				SUM(tax_fee) AS tax_fee  --税费
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches product_caches
						ON fact.product_id = product_caches.id
					INNER JOIN base_limit
						ON {REGION_ID} = base_limit.region_id
			WHERE
				{WHERE}
			GROUP BY
				{REGION_ID},
				fact.product_id,
				product_caches.category0,
				product_caches.category1,
				product_caches.category2,
				fact.unit
			ORDER BY
				{REGION_ID}
		), base_products_ignore_null AS (
			SELECT
				region_id,
				store_type,
				product_id,
				category0,
				category1,
				category2,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee --税金
			FROM base_products
			GROUP BY
				region_id,
				store_type,
				product_id,
				category0,
				category1,
				category2
		), base_products_for_category2 AS (
			SELECT
				region_id,
				store_type,
				category0,
				category1,
				category2,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, -- 税金
		
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'product_id', product_id, --商品ID
						'product_code', '', --商品编码
						'product_name', '', --商品名称
						'weight', weight,
						'weight_count', concat(weight, unit),
						'weight_returned', weight_returned,
						'refund_weight_count', concat(weight_returned, unit),
					    'unit', unit,
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee', tax_fee
					)
				) AS "data"
			FROM base_products
			GROUP BY
				region_id,
				store_type,
				category0,
				category1,
				category2
		), base_products_for_category1 AS (
			SELECT
				region_id,
				store_type,
				category0,
				category1,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee , --税金
		
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category2', category2, --类别2ID
						'category2_code', '', --类别2编码
						'category2_name', '', --类别2名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee'.tax_fee, --税金
						'data', data --商品数据
					)
				) AS "data"
			FROM base_products_for_category2
			GROUP BY
				region_id,
				store_type,
				category0,
				category1
		), base_products_for_category0 AS (
			SELECT
				region_id,
				store_type,
				category0,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category1', category1, --类别1ID
						'category1_code', '', --类别1编码
						'category1_name', '', --类别1名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
		
						'data', data --类别2数据
					)
				) AS "data"
			FROM base_products_for_category1
			GROUP BY
				region_id,
				store_type,
				category0
		), base_products_for_date_and_store AS (
			SELECT
				region_id,
				max(store_type) as store_type,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category0', category0, --类别0ID
						'category0_code', '', --类别0编码
						'category0_name', '', --类别0名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
		
						'data', data --类别1数据
					)
				) AS "data"
			FROM base_products_for_category0
			GROUP BY
				region_id
		)
		SELECT
			'' AS bus_date,
			bpfdas.region_id,
			'' AS region_code,
			'' AS region_name,
			'' AS region_address,
			bpfdas.store_type,
			bd.days AS business_days,
		
			bpfdas.gross_amount AS gross_amount,
			bpfdas.gross_amount_returned AS gross_amount_returned,
			bpfdas.net_amount AS net_amount,
			bpfdas.net_amount_returned AS net_amount_returned,
			bpfdas.discount_amount AS discount_amount,
			bpfdas.discount_amount_returned AS discount_amount_returned,
			bpfdas.item_count AS item_count,
			bpfdas.item_count_returned AS item_count_returned,
		
			bpfdas.data, --类别0数据
			bpfdas.tax_fee, --税金
			0 AS total --Summary时的汇总条数
		FROM
			base_products_for_date_and_store bpfdas
				LEFT JOIN business_days bd
					ON bpfdas.region_id = bd.region_id
	`
		summarySQL = `
		--单品销售报表汇总信息 SUMMARY
		WITH base_products AS (
			SELECT
				SUM(gross_amount) AS gross_amount, --商品流水
				SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
				SUM(net_amount) AS net_amount, --商品实收
				SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
				SUM(discount_amount) AS discount_amount, --折扣金额
				SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
				SUM(qty) AS item_count, --商品数量
				SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
				SUM(tax_fee) --税金
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches 
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches 
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
				{REGION_ID}
			ORDER BY
				{REGION_ID}
		)
		SELECT
			'' AS bus_date,
			0 AS region_id,
			'' AS region_code,
			'' AS region_name,
			'' AS region_address,
			'' AS store_type,
			0 AS business_days, --营业天数
		
			COALESCE(SUM(gross_amount),0) AS gross_amount,
			COALESCE(SUM(gross_amount_returned),0) AS gross_amount_returned,
			COALESCE(SUM(net_amount),0) AS net_amount,
			COALESCE(SUM(net_amount_returned),0) AS net_amount_returned,
			COALESCE(SUM(discount_amount),0)AS discount_amount,
			COALESCE(SUM(discount_amount_returned),0) AS discount_amount_returned,
			COALESCE(SUM(item_count),0) AS item_count,
			COALESCE(SUM(item_count_returned),0)AS item_count_returned,
		
			'[]'::json AS data, --类别0数据
			COALESCE(SUM(tax_fee),0) AS tax_fee,
			COUNT(1) AS total --Summary时的汇总条数
		FROM
			base_products
	`
	} else {
		rowSQL = `
		--单品销售报表详细信息 ROWS
		WITH business_days AS (
			SELECT
				region_id, COUNT(1) AS days
			FROM (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY {REGION_ID}
			) T
			GROUP BY region_id
		), base_limit AS (
			SELECT
				fact.bus_date AS bus_date,
				{REGION_ID} AS region_id
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
				fact.bus_date,
				{REGION_ID}
			ORDER BY
				fact.bus_date DESC,
				{REGION_ID}
			{LIMIT}
		), base_products AS (
			SELECT
				fact.bus_date AS bus_date,
				COALESCE({REGION_ID}, 0) AS region_id,
				COALESCE(max(store_caches.store_type), '') AS store_type,
				COALESCE(fact.product_id, 0) AS product_id,
				COALESCE(product_caches.category0, 0) AS category0,
				COALESCE(product_caches.category1, 0) AS category1,
				COALESCE(product_caches.category2, 0) AS category2,
				coalesce(fact.unit, '') as unit, --商品单位
				sum(weight) as weight, --商品份量
				SUM(CASE WHEN refunded THEN weight ELSE 0 END) AS weight_returned, --退单商品份量
				SUM(gross_amount) AS gross_amount, --商品流水
				SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
				SUM(net_amount) AS net_amount, --商品实收
				SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
				SUM(discount_amount) AS discount_amount, --折扣金额
				SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
				SUM(qty) AS item_count, --商品数量
				SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
				SUM(tax_fee) AS tax_fee  --税金
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches product_caches
						ON fact.product_id = product_caches.id
					INNER JOIN base_limit
						ON fact.bus_date = base_limit.bus_date
							AND {REGION_ID} = base_limit.region_id
			WHERE
				{WHERE}
			GROUP BY
				fact.bus_date,
				{REGION_ID},
				fact.product_id,
				product_caches.category0,
				product_caches.category1,
				product_caches.category2,
				fact.unit
			ORDER BY
				fact.bus_date DESC,
				{REGION_ID}
		), base_products_ignore_null AS (
			SELECT
				bus_date,
				region_id,
				store_type,
				product_id,
				category0,
				category1,
				category2,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee
			FROM base_products
			GROUP BY
				bus_date,
				region_id,
				store_type,
				product_id,
				category0,
				category1,
				category2
		), base_products_for_category2 AS (
			SELECT
				bus_date,
				region_id,
				store_type,
				category0,
				category1,
				category2,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'product_id', product_id, --商品ID
						'product_code', '', --商品编码
						'product_name', '', --商品名称
						'weight', weight,
						'weight_count', concat(weight, unit),
						'weight_returned', weight_returned,
						'refund_weight_count', concat(weight_returned, unit),
					    'unit', unit,
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee', tax_fee
					)
				) AS "data"
			FROM base_products
			GROUP BY
				bus_date,
				region_id,
				store_type,
				category0,
				category1,
				category2
		), base_products_for_category1 AS (
			SELECT
				bus_date,
				region_id,
				store_type,
				category0,
				category1,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category2', category2, --类别2ID
						'category2_code', '', --类别2编码
						'category2_name', '', --类别2名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee',tax_fee, --税金
						'data', data --商品数据
					)
				) AS "data"
			FROM base_products_for_category2
			GROUP BY
				bus_date,
				region_id,
				store_type,
				category0,
				category1
		), base_products_for_category0 AS (
			SELECT
				bus_date,
				region_id,
				store_type,
				category0,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee, --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category1', category1, --类别1ID
						'category1_code', '', --类别1编码
						'category1_name', '', --类别1名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee', tax_fee, --税金
						'data', data --类别2数据
					)
				) AS "data"
			FROM base_products_for_category1
			GROUP BY
				bus_date,
				region_id,
				store_type,
				category0
		), base_products_for_date_and_store AS (
			SELECT
				bus_date,
				region_id,
				max(store_type) as store_type,
		
				SUM(gross_amount) AS gross_amount,
				SUM(gross_amount_returned) AS gross_amount_returned,
				SUM(net_amount) AS net_amount,
				SUM(net_amount_returned) AS net_amount_returned,
				SUM(discount_amount) AS discount_amount,
				SUM(discount_amount_returned) AS discount_amount_returned,
				SUM(item_count) AS item_count,
				SUM(item_count_returned) AS item_count_returned,
				SUM(tax_fee) AS tax_fee , --税金
				JSON_AGG(
					JSON_BUILD_OBJECT(
						'category0', category0, --类别0ID
						'category0_code', '', --类别0编码
						'category0_name', '', --类别0名称
		
						'gross_amount', gross_amount,
						'gross_amount_returned', gross_amount_returned,
						'net_amount', net_amount,
						'net_amount_returned', net_amount_returned,
						'discount_amount', discount_amount,
						'discount_amount_returned', discount_amount_returned,
						'item_count', item_count,
						'item_count_returned', item_count_returned,
						'tax_fee', tax_fee, --税金
						'data', data --类别1数据
					)
				) AS "data"
			FROM base_products_for_category0
			GROUP BY
				bus_date,
				region_id
		)
		SELECT
			to_char(bpfdas.bus_date,'YYYY-MM-DD') AS bus_date,
			bpfdas.region_id,
			'' AS region_code,
			'' AS region_name,
			'' AS region_address,
			bpfdas.store_type,
			bd.days AS business_days,
		
			bpfdas.gross_amount AS gross_amount,
			bpfdas.gross_amount_returned AS gross_amount_returned,
			bpfdas.net_amount AS net_amount,
			bpfdas.net_amount_returned AS net_amount_returned,
			bpfdas.discount_amount AS discount_amount,
			bpfdas.discount_amount_returned AS discount_amount_returned,
			bpfdas.item_count AS item_count,
			bpfdas.item_count_returned AS item_count_returned,
		
			bpfdas.data, --类别0数据
			bpfdas.tax_fee AS tax_fee, --税金
			0 AS total --Summary时的汇总条数
		FROM
			base_products_for_date_and_store bpfdas
				LEFT JOIN business_days bd
					ON bpfdas.region_id = bd.region_id
		ORDER BY bpfdas.bus_date DESC, bpfdas.region_id
	`
		summarySQL = `
		--单品销售报表详细信息 SUMMARY
		WITH base_products AS (
			SELECT
				SUM(gross_amount) AS gross_amount, --商品流水
				SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
				SUM(net_amount) AS net_amount, --商品实收
				SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
				SUM(discount_amount) AS discount_amount, --折扣金额
				SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
				SUM(qty) AS item_count, --商品数量
				SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
				SUM(tax_fee) AS tax_fee --税金
			FROM
				sales_product_amounts fact
					LEFT JOIN store_caches 
						ON fact.store_id = store_caches.id
					LEFT JOIN product_caches 
						ON fact.product_id = product_caches.id
			WHERE
				{WHERE}
			GROUP BY
				fact.bus_date,
				{REGION_ID}
			ORDER BY
				fact.bus_date DESC,
				{REGION_ID}
		)
		SELECT
			'' AS bus_date,
			0 AS region_id,
			'' AS region_code,
			'' AS region_name,
			'' AS region_address,
			'' AS store_type,
			0 AS business_days, --营业天数
		
			COALESCE(SUM(gross_amount),0) AS gross_amount,
			COALESCE(SUM(gross_amount_returned),0) AS gross_amount_returned,
			COALESCE(SUM(net_amount),0) AS net_amount,
			COALESCE(SUM(net_amount_returned),0) AS net_amount_returned,
			COALESCE(SUM(discount_amount),0)AS discount_amount,
			COALESCE(SUM(discount_amount_returned),0) AS discount_amount_returned,
			COALESCE(SUM(item_count),0) AS item_count,
			COALESCE(SUM(item_count_returned),0)AS item_count_returned,
		
			'[]'::json AS data, --类别0数据
			COALESCE(SUM(tax_fee),0) AS tax_fee,
			COUNT(1) AS total --Summary时的汇总条数
		FROM
			base_products
	`
	}

	whereSQL := generateWhereSQLForProductSales(condition)
	regionSQL := generateRegionSQLForProductSales(condition)
	limitSQL := generateLimitOffsetSQLForProductSales(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForProductSales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForProductSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		priceScopeSQL         string // 价格区间筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		storeTypeSQL          string // 增加门店类型的过滤
		openStatusSQL         string // 增加开店类型的过滤
		comboSQL              string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	priceScopeSQL = generatePriceScopeSQLForProductSales(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, priceScopeSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL)
	return whereSQL
}

func generatePriceScopeSQLForProductSales(condition *model.RepoCondition) string {
	priceScopeSQL := ""
	if len(condition.PriceScope) == 0 {
		return priceScopeSQL
	}
	for i, prices := range condition.PriceScope {
		if len(prices) != 2 {
			return ""
		}
		low, high := prices[0], prices[1]
		if i == 0 {
			priceScopeSQL += fmt.Sprintf(`
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		} else {
			priceScopeSQL += fmt.Sprintf(`
				OR
				(abs(fact.price) >= %f
				AND abs(fact.price) <= %f)
          `, low, high)
		}
	}
	if priceScopeSQL == "" {
		return priceScopeSQL
	} else {
		return fmt.Sprintf(`AND (%s)`, priceScopeSQL)
	}
}

func generateLimitOffsetSQLForProductSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
