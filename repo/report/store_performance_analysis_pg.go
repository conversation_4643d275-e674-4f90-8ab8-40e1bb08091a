package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

var storePerformanceAnalysisRowsTpl = `
-- 门店表现分析 Rows
with table_count as (
    select store_id::bigint, sum(table_seat::numeric) as total_seat
    from table_caches where table_mode::integer != 1
    group by store_id
),
base as (
    select
        fact.store_id,
        fact.bus_date,
        fact.meal_segment_name,
        fact.order_type,
        max(table_count.total_seat) as total_seat,
        coalesce(sum(fact.amount_0), 0) as business_amount,
        coalesce(sum(case when fact.amount_0-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount) = 0 then 0 else fact.bowl_num end), 0) as bowl_num,
		coalesce(sum(fact.eticket_count), 0) as eticket_count
    from sales_ticket_amounts fact left join table_count on fact.store_id = table_count.store_id
		left join store_caches on fact.store_id = store_caches.id
    where {WHERE}
    group by fact.store_id, fact.bus_date, fact.meal_segment_name, fact.order_type
),
summary as (
    select
        store_id,
        max(total_seat) as total_seat,
        sum(business_amount) as business_amount,
        sum(bowl_num) as bowl_num,
		sum(eticket_count) as eticket_count
    from base
    group by store_id
)
{Fragments}
select coalesce(s.store_id, 0) as store_id,
       round(coalesce(s.total_seat, 0)) as total_seat,
       coalesce(s.business_amount, 0) as business_amount,
       coalesce(s.bowl_num, 0) as bowl_num,
       coalesce((case when s.total_seat = 0 then 0 else s.bowl_num / 1.00 / s.total_seat end), 0) as seat_turnover_rate,
       coalesce(s.business_amount / 1.00, 0) as avg_business_amount,
       coalesce((case when s.eticket_count = 0 then 0 else s.business_amount / s.eticket_count end), 0) as avg_sales_per_order,
       coalesce((case when s.bowl_num = 0 then 0 else s.business_amount / s.bowl_num end), 0) as avg_bowl_amount,

       coalesce(wms.business_amount, 0) as weekday_business_amount,
       coalesce(woms.order_type_meal_segment, '[]'::json) as weekday_order_type_meal_segment,
       coalesce(wms.meal_segment, '[]'::json) as weekday_meal_segment,
       coalesce(wot.order_type, '[]'::json) as weekday_order_type,

       coalesce(weot.weekend_business_amount, 0) as weekend_business_amount,
       coalesce(weoms.order_type_meal_segment, '[]'::json) as weekend_order_type_meal_segment,
       coalesce(wems.meal_segment, '[]'::json) as weekend_meal_segment,
       coalesce(weot.order_type, '[]'::json) as weekend_order_type
from summary s
    left join weekday_ordertypes_mealsegments woms on s.store_id = woms.store_id
    left join weekday_mealsegments wms on s.store_id = wms.store_id
    left join weekday_ordertypes wot on s.store_id = wot.store_id
    left join weekend_mealsegments wems on s.store_id = wems.store_id
    left join weekend_ordertypes_mealsegments weoms on s.store_id = weoms.store_id
    left join weekend_ordertypes weot on s.store_id = weot.store_id
{LIMIT}
`

var storePerformanceAnalysisFragmentTpl = `
{Fragment}_base as (
    select
        store_id,
        meal_segment_name,
        (case when order_type in ('TAKEAWAY','TAKEOUT', 'SELFHELP') then order_type else 'DINEIN' end) as order_type,
        max(total_seat) as total_seat,
        sum(business_amount) as business_amount,
        sum(bowl_num) as bowl_num
    from base
    where meal_segment_name is not null and meal_segment_name != '' and extract(dow from bus_date) in {FragmentArg}
    group by store_id, meal_segment_name, order_type
),
{Fragment}_ordertypes_mealsegments as (
    select
        store_id,
        sum(business_amount) as business_amount,
        json_agg(json_build_object('order_type', order_type, 'meal_segment_name', meal_segment_name, 'bowl_turnover', bowl_turnover)) as order_type_meal_segment
    from (
        select store_id,
               order_type,
               meal_segment_name,
               sum(business_amount) as business_amount,
               sum(bowl_num) as bowl_num,
               sum(bowl_num) / {Fragment_DAYS} / max(total_seat) as  bowl_turnover
        from {Fragment}_base
        group by store_id, order_type, meal_segment_name
    ) T
    group by store_id
),
{Fragment}_mealsegments as (
    select
        store_id,
        sum(business_amount) as business_amount,
        json_agg(json_build_object('meal_segment_name', meal_segment_name, 'bowl_turnover', bowl_turnover)) as meal_segment
    from (
        select store_id,
               meal_segment_name,
               sum(business_amount) as business_amount,
               sum(bowl_num) as bowl_num,
               sum(bowl_num) / {Fragment_DAYS} / max(total_seat) as  bowl_turnover
        from {Fragment}_base
        group by store_id, meal_segment_name
    ) T
    group by store_id
),
{Fragment}_ordertypes as (
    select
        store_id,
        sum(business_amount) as {Fragment}_business_amount,
        json_agg(json_build_object('order_type', order_type, 'bowl_turnover', bowl_turnover)) as order_type
    from (
        select store_id,
               order_type,
               sum(business_amount) as business_amount,
               sum(bowl_num) as bowl_num,
               sum(bowl_num) / {Fragment_DAYS} / max(total_seat) as  bowl_turnover
        from {Fragment}_base
        group by store_id, order_type
    ) T
    group by store_id
)
`

func (rsmm *SalesRepositoryPG) StorePerformanceAnalysis(ctx context.Context, condition *model.RepoCondition) (*report.StorePerformanceAnalysisResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, totalSQL := generateQuerySQLForStorePerformanceAnalysis(trackId, condition)
	ch1 := make(chan []*report.StorePerformanceAnalysis, 1)
	ch2 := make(chan int64, 1)
	go queryDBWithChanForStorePerformanceAnalysis(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForStorePerformanceAnalysisTotal(trackId, rsmm.DB, totalSQL, ch2)
	rows := <-ch1
	total := <-ch2
	close(ch1)
	close(ch2)
	response := &report.StorePerformanceAnalysisResponse{
		Rows:    rows,
		Total: total,
	}
	return response, nil
}

func queryDBWithChanForStorePerformanceAnalysis(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.StorePerformanceAnalysis) {
	ch <- queryDBForStorePerformanceAnalysis(trackId, db, sqlStr)
}

func queryDBWithChanForStorePerformanceAnalysisTotal(trackId int64, db *sql.DB, sqlStr string, ch chan int64) {
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- 0
		return
	}
	defer r.Close()

	if r.Next() {
		var total int64
		if err := r.Scan(&total); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- 0
		} else {
			ch <- total
		}
	} else {
		ch <- 0
	}
}

// 查询数据库，并将结果映射为map对象
func queryDBForStorePerformanceAnalysis(trackId int64, db *sql.DB, sqlStr string) []*report.StorePerformanceAnalysis {
	results := make([]*report.StorePerformanceAnalysis, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StorePerformanceAnalysis)
		var orderTypesMealSegments, mealSegment,orderTypes, orderTypesMealSegmentsWeekend, mealSegmentWeekend,orderTypesWeekend json.RawMessage
		if err := r.Scan(
			&f.StoreId,
			&f.SeatCount,
			&f.BusinessAmount,
			&f.BowlCount,
			&f.SeatTurnoverRate,
			&f.AverageDailySales,
			&f.AverageSalesPerOrder,
			&f.AverageBowlSpending,

			&f.WeekdayAverageDailySales,
			&orderTypesMealSegments,
			&mealSegment,
			&orderTypes,

			&f.WeekendAverageDailySales,
			&orderTypesMealSegmentsWeekend,
			&mealSegmentWeekend,
			&orderTypesWeekend,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(orderTypesMealSegments, &f.WeekdayOrderTypeMealSegmentTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(mealSegment, &f.WeekdayMealSegmentTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(orderTypes, &f.WeekdayChannelTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(orderTypesMealSegmentsWeekend, &f.WeekendOrderTypeMealSegmentTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(mealSegmentWeekend, &f.WeekendMealSegmentTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}
		if err := json.Unmarshal(orderTypesWeekend, &f.WeekendChannelTurnovers); err != nil {
			logger.Pre().Errorf("[%d] json.Unmarshal. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForStorePerformanceAnalysis(trackId int64, condition *model.RepoCondition) (string, string) {
	rowSQL := storePerformanceAnalysisRowsTpl
	totalSQL := `
-- 门店表现分析 Total
select count(1) as total
from (
	select
		1 as a
	from sales_ticket_amounts fact left join store_caches on fact.store_id = store_caches.id
	where {WHERE}
	group by fact.store_id
) T
`
	whereSQL := generateWhereSQLForStorePerformanceAnalysis(condition)
	limitSQL := generateLimitOffsetSQLForStorePerformanceAnalysis(condition)
	endTime := condition.End.Truncate(24 * time.Hour).AddDate(0, 0, 1)
	days := endTime.Sub(condition.Start).Hours() / 24
	weekendDays := 0
	for date := condition.Start; date.Before(endTime); date = date.AddDate(0, 0, 1) {
		if date.Weekday() == time.Saturday || date.Weekday() == time.Sunday {
			weekendDays++
		}
	}

	weekDayFragment := strings.ReplaceAll(storePerformanceAnalysisFragmentTpl, "{Fragment}", "weekday")
	weekDayFragment = strings.ReplaceAll(weekDayFragment, "{FragmentArg}", "(1,2,3,4,5)")
	weekDayFragment = strings.ReplaceAll(weekDayFragment, "{Fragment_DAYS}", fmt.Sprintf("%.2f", days - float64(weekendDays)))

	weekendFragment := strings.ReplaceAll(storePerformanceAnalysisFragmentTpl, "{Fragment}", "weekend")
	weekendFragment = strings.ReplaceAll(weekendFragment, "{FragmentArg}", "(0,6)")
	weekendFragment = strings.ReplaceAll(weekendFragment, "{Fragment_DAYS}", fmt.Sprintf("%.2f", float64(weekendDays)))
	rowSQL = strings.ReplaceAll(rowSQL, "{Fragments}", "," + weekDayFragment + "," + weekendFragment)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{DAYS}", fmt.Sprintf("%.2f", days))
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG total. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateWhereSQLForStorePerformanceAnalysis(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
	)

	regionIdsSQL = GenerateRegionWhereSQL(condition)

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL,
		storeSQL, regionCodesSQL)
	return whereSQL
}

func generateLimitOffsetSQLForStorePerformanceAnalysis(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
