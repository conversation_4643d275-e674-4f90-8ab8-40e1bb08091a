package repo

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) DiscountSales(ctx context.Context, condition *model.RepoCondition) (*report.DiscountSalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForDiscountSales(trackId, condition)
	ch1 := make(chan []*report.DiscountSales, 1)
	ch2 := make(chan []*report.DiscountSales, 1)
	start := time.Now()
	go queryDBWithChanForDiscountSales(ctx, rsmm.GormDB, rowSQL, ch1)
	go queryDBWithChanForDiscountSales(ctx, rsmm.GormDB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.DiscountSales
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.DiscountSalesResponse{
		Rows:    rows,
		Summary: summary,
	}
	if summary != nil {
		if summary.GrossAmount == 0 && summary.ProductCount == 0 && summary.DiscountAmount == 0 && summary.DiscountContribute == 0 {
			response.Total = 0
		} else {
			response.Total = summary.Total
		}
	}
	return response, nil
}

func queryDBWithChanForDiscountSales(ctx context.Context, db *gorm.DB, sql string, ch chan []*report.DiscountSales) {
	ch <- queryDBForDiscountSales(ctx, db, sql)
}

func queryDBForDiscountSales(ctx context.Context, db *gorm.DB, sqlStr string) []*report.DiscountSales {
	if sqlStr == "" {
		return []*report.DiscountSales{new(report.DiscountSales)}
	}
	res := make([]*report.DiscountSales, 0)
	q := db.WithContext(ctx).Raw(sqlStr).Scan(&res)
	if q.Error != nil {
		logger.Pre().WithContext(ctx).Errorf(" pkg.helpers.QueryDB. Err: `%v`", q.Error)
		return res
	}
	return res
}

func saveTwoEffectiveDigits(f float64) (float float64) {
	float, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", f), 64)
	return
}

func generateQuerySQLForDiscountSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)

	rowSQL = `
			--交易折扣详细信息ROWS
				WITH base_limit as (
					select
						fact.bus_date as bus_date,
						{REGION_ID} as region_id,
						fact.promotion_id as discount_id
					from sales_discount_amounts fact
						left join store_caches on fact.store_id = store_caches.id
							left join product_caches on product_caches.id = fact.product_id
					where
						{WHERE}
						and {REGION_ID} > 0
					group by fact.bus_date, {REGION_ID}, fact.promotion_id
					order by fact.bus_date desc, {REGION_ID}, fact.promotion_id
					{LIMIT}
				), base_discount_count as (
				    select
						bus_date, --营业日期
						region_id, --门店(或区域或公司)id
						discount_id,--折扣id
					    sum(discount_count) as discount_count --折扣次数
					from
					     (
					         select
					            fact.bus_date as bus_date, --营业日期
                                {REGION_ID} as region_id, --门店(或区域或公司)id
                                promotion_id as discount_id,--折扣id
                                max(qty) as discount_count --折扣次数
					         from sales_discount_amounts fact
						        left join store_caches on fact.store_id = store_caches.id
                                 inner join base_limit bt on fact.bus_date = bt.bus_date
    								and fact.store_id = bt.region_id
    								and fact.promotion_id = bt.discount_id
						     where
                                {WHERE}
                                and fact.store_id > 0
					         group by fact.eticket_id,fact.bus_date, {REGION_ID}, promotion_id
                             )T
					group by bus_date, region_id, discount_id
					order by bus_date desc, region_id, discount_id
                ),
				base_discount as (
					select
						fact.bus_date as bus_date, --营业日期
	    				max(store_caches.branchs[array_length(store_caches.branchs,1)]) AS branch_id,--管理区域
						{REGION_ID} as region_id, --门店(或区域或公司)id
						promotion_id as discount_id,--折扣id
						coalesce(string_agg(distinct promotion_code, ','), '') as discount_code,--促销编号
        				coalesce(string_agg(distinct promotion_name, ','), '') as discount_name,--促销名称
						sum(discount_amount) as discount_amount,--折扣金额(交易)
						sum(merchant_allowance) as discount_contribute,--优惠组成财务
						sum(cost+tp_allowance) as transfer_real_amount--实收组成财务
					from
						sales_discount_amounts fact
						 left join store_caches on fact.store_id = store_caches.id
						 inner join base_limit bt on fact.bus_date = bt.bus_date
								and {REGION_ID} = bt.region_id
								and fact.promotion_id = bt.discount_id
					where
						{WHERE}
						and {REGION_ID} > 0
					group by fact.bus_date, {REGION_ID}, fact.promotion_id
					order by fact.bus_date desc, {REGION_ID}, promotion_id
				)
				select * from  base_discount bd
					left join base_discount_count 
					bdc on bdc.region_id = bd.region_id and bdc.discount_id = bd.discount_id
								and bdc.bus_date=bd.bus_date
				order by  bd.bus_date desc, bd.region_id, bd.discount_id;
		;
		`

	summarySQL = `
				--交易折扣详细信息SUMMARY
					with base_discount_count as (
						select
							sum(discount_count) as discount_count --折扣次数
						from
							 (
								 select
									fact.bus_date as bus_date, --营业日期
									{REGION_ID} as region_id, --门店(或区域或公司)id
									promotion_id as discount_id,--折扣id
									max(qty) as discount_count --折扣次数
								 from sales_discount_amounts fact
									left join store_caches on fact.store_id = store_caches.id
								 where
									{WHERE}
									and {REGION_ID} > 0
								 group by fact.eticket_id,fact.bus_date, {REGION_ID}, promotion_id
	
								 )T
					),
					base_discount as (
						select
							sum(discount_amount) as discount_amount,
							sum(merchant_allowance) as discount_contribute
						from
							sales_discount_amounts fact
								left join store_caches  on fact.store_id = store_caches.id
						where
							{WHERE}
							and {REGION_ID} > 0
						group by fact.bus_date, {REGION_ID}, fact.promotion_id
					)
					select
						'' as bus_date,
						0 as branch_id,
						0 as region_id,
						0 as discount_id,
						'' as discount_code,
						'' as discount_name,
						coalesce(bdc.discount_count, 0) as discount_count,
						coalesce(sum(discount_amount), 0) as discount_amount,
						coalesce(sum(discount_contribute), 0) as discount_contribute,
						count(1) as total
					from
						 base_discount bd, base_discount_count bdc
						group by bdc.discount_count;
		`

	whereSQL, daysWhereSQL := generateWhereSQLForDiscountSales(condition)
	regionSQL := generateRegionSQLForProductSales(condition)
	limitSQL := generateLimitOffsetSQLForProductSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{DAYSWHERE}", daysWhereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForDiscountSales(condition *model.RepoCondition) (string, string) {
	var (
		regionIdsSQL   string // 区域筛选条件
		discountIdsSQL string // 折扣方式筛选条件
		storeTypeSQL   string // 门店类型筛选条件
		openStatusSQL  string // 开店状态筛选条件
		comboSQL       string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}

	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.DiscountIds) > 0 {
		discountIdsSQL = fmt.Sprintf(
			"AND fact.promotion_id IN (%s)",
			helpers.JoinInt64(condition.DiscountIds, ","),
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL, orderStatusSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}

	if condition.OrderStatus != "" {
		orderStatusSQL = fmt.Sprintf(`
				AND fact.order_status = '%s'
			`, condition.OrderStatus)
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, discountIdsSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL, orderStatusSQL)
	businessDaysWhereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeTypeSQL, openStatusSQL, storeSQL, orderStatusSQL)
	return whereSQL, businessDaysWhereSQL
}
