package repo

import (
	"context"
	"database/sql"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

func (rsmm *SalesRepositoryPG) ProductCategorySales(ctx context.Context, condition *model.RepoCondition) (*report.CategorySalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductCategorySales(trackId, condition)
	ch1 := make(chan []*report.CategorySales, 1)
	ch2 := make(chan []*report.CategorySales, 1)
	go queryDBWithChanForCategorySales(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForCategorySales(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)

	var summary *report.CategorySales
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.CategorySalesResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForCategorySales(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.CategorySales) {
	ch <- queryDBForCategorySales(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForCategorySales(trackId int64, db *sql.DB, sqlStr string) []*report.CategorySales {
	results := make([]*report.CategorySales, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.CategorySales)
		if err := r.Scan(
			&f.CategoryId,
			&f.NetAmount,
			&f.ProductCount,
			&f.PercentageOfSales,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductCategorySales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	rowSQL = `
		--商品分类销售收入统计表 ROWS
		WITH base_product AS (SELECT product_caches.category0 AS category_id,
										   SUM(
												   (COALESCE(fact.gross_amount2, 0) +
													COALESCE(fact.package_amount, 0) +
													COALESCE(fact.send_fee_for_merchant, 0) +
													COALESCE(fact.service_fee, 0) +
													COALESCE(fact.tip, 0)) -
												   (COALESCE(fact.pay_merchant_allowance, 0) +
													COALESCE(fact.promotion_merchant_allowance, 0) +
													COALESCE(fact.other_fee, 0) +
													COALESCE(fact.merchant_send_fee, 0) +
													COALESCE(fact.commission, 0) +
													COALESCE(fact.rounding, 0) -
													COALESCE(fact.overflow_amount, 0))
										   )             AS net_amount,
										   SUM(fact.qty) AS item_count
									FROM sales_product_amounts fact
									LEFT JOIN store_caches ON fact.store_id = store_caches.id
         							LEFT JOIN product_caches ON fact.product_id = product_caches.id
									WHERE {BASE_WHERE}
									GROUP BY product_caches.category0),
			 product_net_amount AS (SELECT category_id, net_amount, item_count
									FROM base_product fact
									{WHERE}),
			 total_net_amount AS (SELECT SUM(net_amount) AS total_net_amount
											  FROM base_product)
		SELECT pna.category_id                          AS category_id,
			   pna.net_amount                          AS net_amount,
			   pna.item_count                          AS item_count,
			   (pna.net_amount / tna.total_net_amount) AS net_amount_ratio,
			   0                                       AS total
		FROM product_net_amount pna,
			 total_net_amount tna
		ORDER BY pna.category_id
		 {LIMIT};
`
	summarySQL = `
		--商品分类销售收入统计表 Summary
		WITH base_product AS (SELECT product_caches.category0 AS category_id,
										   SUM(
												   (COALESCE(fact.gross_amount2, 0) +
													COALESCE(fact.package_amount, 0) +
													COALESCE(fact.send_fee_for_merchant, 0) +
													COALESCE(fact.service_fee, 0) +
													COALESCE(fact.tip, 0)) -
												   (COALESCE(fact.pay_merchant_allowance, 0) +
													COALESCE(fact.promotion_merchant_allowance, 0) +
													COALESCE(fact.other_fee, 0) +
													COALESCE(fact.merchant_send_fee, 0) +
													COALESCE(fact.commission, 0) +
													COALESCE(fact.rounding, 0) -
													COALESCE(fact.overflow_amount, 0))
										   )             AS net_amount,
										   SUM(fact.qty) AS item_count
									FROM sales_product_amounts fact
									LEFT JOIN store_caches ON fact.store_id = store_caches.id
         							LEFT JOIN product_caches ON fact.product_id = product_caches.id
									WHERE {BASE_WHERE}
									GROUP BY product_caches.category0),
			product_net_amount AS (SELECT category_id, net_amount, item_count
							FROM base_product fact
							{WHERE}),
			 total_net_amount AS (SELECT SUM(net_amount) AS total_net_amount, SUM(item_count) total_item_count
								  FROM product_net_amount),
			 total_count AS (SELECT COUNT(category_id) AS total_count
							 FROM product_net_amount)
		SELECT 0                                 AS category_id,
			   COALESCE(tna.total_net_amount, 0) AS net_amount,
			   COALESCE(tna.total_item_count, 0) AS item_count,
			   0                                 AS net_amount_ratio,
			   total_count.total_count           AS total
		FROM total_net_amount tna,
			 total_count;
`

	baseWhereSQL := generateBaseWhereSQLForProductCategorySales(condition)
	whereSQL := generateWhereSQLForProductCategorySales(condition)
	regionSQL := generateRegionSQLForProductCategorySales(condition)
	limitSQL := generateLimitOffsetSQLForProductCategorySales(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{BASE_WHERE}", baseWhereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{BASE_WHERE}", baseWhereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForProductCategorySales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateBaseWhereSQLForProductCategorySales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
		comboSQL     string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	comboSQL = fmt.Sprintf(`AND (fact.combo_type != 1)`)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	netAmountSQL := ""
	if !condition.IncludeRealAmountZero {
		netAmountSQL = fmt.Sprintf(`
			AND fact.products_real_amount != 0
		`)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		AND fact.is_non_sales = FALSE
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, comboSQL, regionCodesSQL, netAmountSQL)
	return whereSQL
}

func generateWhereSQLForProductCategorySales(condition *model.RepoCondition) string {
	var (
		productCategoryIdsSQL string // 类别筛选条件
	)
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			WHERE (
				fact.category_id IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}

	whereSQL := fmt.Sprintf(`
		%s
	`, productCategoryIdsSQL)
	return whereSQL
}

func generateLimitOffsetSQLForProductCategorySales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
