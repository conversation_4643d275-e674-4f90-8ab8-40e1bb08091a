package repo

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
	"strings"
)

func (rsmm *SalesRepositoryPG) RefundAnalyse(ctx context.Context, condition *model.RepoCondition) (*report.RefundAnalysisResponse, error) {
	// 在这里实现sql的组装和查询，并封装成Response
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	//生成查询语句
	rowSQL, totalSQL := generateQuerySQLForRefundAnalyse(trackId, condition)
	ch1 := make(chan []*report.RefundAnalysis, 1)
	ch2 := make(chan int64, 1)
	go queryDBWithChanForRefundAnalysis(ctx, rsmm.GormDB, rowSQL, ch1)
	go queryDBWithChanForRefundAnalysisTotal(ctx, rsmm.GormDB, totalSQL, ch2)
	rows := <-ch1
	total := <-ch2
	close(ch1)
	close(ch2)
	response := &report.RefundAnalysisResponse{
		Rows:  rows,
		Total: total,
	}
	return response, nil
}

func queryDBWithChanForRefundAnalysis(ctx context.Context, db *gorm.DB, sql string, ch chan []*report.RefundAnalysis) {
	ch <- queryDBForRefundAnalysis(ctx, db, sql)
}

func queryDBWithChanForRefundAnalysisTotal(ctx context.Context, db *gorm.DB, sql string, ch chan int64) {
	ch <- queryDBForRefundAnalysisTotal(ctx, db, sql)
}

func queryDBForRefundAnalysis(ctx context.Context, db *gorm.DB, s string) []*report.RefundAnalysis {
	results := make([]*report.RefundAnalysis, 0)
	q := db.WithContext(ctx).Raw(s).Scan(&results)
	if q.Error != nil {
		logger.Pre().WithContext(ctx).Errorf(" pkg.helpers.QueryDB. Err: `%v`", q.Error)
		return results
	}
	return results
}

func queryDBForRefundAnalysisTotal(ctx context.Context, db *gorm.DB, s string) int64 {
	total := int64(0)
	q := db.WithContext(ctx).Raw(s).Scan(&total)
	if q.Error != nil {
		logger.Pre().WithContext(ctx).Errorf(" pkg.helpers.QueryDB. Err: `%v`", q.Error)
		return total
	}
	return total
}

func generateQuerySQLForRefundAnalyse(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string
		totalSQL string
	)

	rowSQL = `
			--退单原因汇总信息rows
			WITH base_limit AS (
					SELECT
							fact.eticket_id,
					        pay.payment_id AS payment_id
					FROM
						sales_ticket_amounts fact
						INNER JOIN  sales_payment_amounts pay
						    on pay.eticket_id=fact.eticket_id
						    and pay.refunded AND pay.is_non_sales = FALSE
						LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						and fact.refunded
						and fact.refunded
						AND fact.is_non_sales = FALSE
					ORDER BY
						{REGION_ID},fact.bus_date,fact.channel_id,
						fact.operator_name,fact.refund_code,fact.refund_reason,payment_id,fact.eticket_id
					{LIMIT}
				), base_tickets AS (
					SELECT
						fact.bus_date AS bus_date,
						fact.eticket_id,
						COALESCE(fact.store_id, 0) AS region_id,
						COALESCE(fact.channel_id, 0) AS channel_id,--渠道
						COALESCE((store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
						COALESCE(fact.operator_name, '') AS operator_name, --操作人
						COALESCE(fact.operator_name, '') AS refund_side, --操作人
						COALESCE(fact.refund_code, '') AS refund_code, --退单原因code
						COALESCE(fact.refund_reason,'') AS refund_reason, --退单原因
						pay.payment_id AS payment_id,
						COALESCE((CASE WHEN pay.refunded THEN gross_amount ELSE 0 END), 0) AS gross_amount_returned, --退单商品流水
						COALESCE((CASE WHEN pay.refunded THEN pay.eticket_count ELSE 0 END), 0) AS order_count_returned, --退单数
						COALESCE((CASE WHEN pay.refunded THEN pay.pay_amount+pay.sur_charge_amount ELSE 0 END), 0) AS pay_amount_returned,
						COALESCE( pay.payment_code, '') AS payment_code,--支付编号
						COALESCE( pay.payment_name, '') AS payment_name--支付名称
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
							INNER JOIN sales_payment_amounts pay
								ON pay.eticket_id=fact.eticket_id
								AND pay.refunded AND pay.is_non_sales = FALSE
							INNER JOIN base_limit
								ON fact.eticket_id=base_limit.eticket_id
								 and pay.payment_id=base_limit.payment_id 
					WHERE
						{WHERE}
						AND fact.is_non_sales = FALSE
					ORDER BY
						{REGION_ID}
				)
				SELECT
					*
				FROM
					 base_tickets bt
				ORDER BY bt.region_id,bt.bus_date,bt.channel_id,bt.operator_name,bt.refund_code,bt.refund_reason,bt.payment_id,bt.eticket_id;
		`
	totalSQL = `
					WITH base_limit AS (
					SELECT
							fact.eticket_id,
					        pay.payment_id AS payment_id
					FROM
						sales_ticket_amounts fact
						INNER JOIN  sales_payment_amounts pay
						    on pay.eticket_id=fact.eticket_id
						    and pay.refunded AND pay.is_non_sales = FALSE
						LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						and fact.refunded
						and fact.refunded
						AND fact.is_non_sales = FALSE
				)
			select  count(*) as cnt from  base_limit;
		`

	whereSQL := generateWhereSQLForRefundAnalyse(condition)
	regionSQL := generateRegionSQLForRefundAnalysis(condition)
	limitSQL := generateLimitOffsetSQLForProductSales(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	totalSQL = strings.ReplaceAll(totalSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, totalSQL)

	return rowSQL, totalSQL
}

func generateWhereSQLForRefundAnalyse(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 根据查询区域类型
		storeTypeSQL  string // 根据门店类型
		openStatusSQL string // 根据开店状态
		channelSQL    string // 渠道过滤
		refundSideSQL string // 退单方过滤
		refundCodeSQL string // 退单原因过滤
	)

	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if condition.ChannelId != 0 {
		channelSQL = fmt.Sprintf("AND fact.channel_id = %d \n", condition.ChannelId)
	}
	// 判断退单方和退单原因是不是全部
	if condition.RefundSide != "" {
		refundSideSQL = fmt.Sprintf("AND fact.refund_side = '%s' \n", condition.RefundSide)
	}
	if condition.RefundCode != "" {
		refundCodeSQL = fmt.Sprintf("AND fact.refund_code = '%s' \n", condition.RefundCode)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, storeTypeSQL, openStatusSQL, refundSideSQL, refundCodeSQL, channelSQL)

	return whereSQL

}

func generateRegionSQLForRefundAnalysis(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}
