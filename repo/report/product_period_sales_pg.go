package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductPeriodResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductPeriodSales(trackId, condition)
	ch1 := make(chan []*report.ProductPeriod, 1)
	ch2 := make(chan []*report.ProductPeriod, 1)
	go queryDBWithChanForProductPeriod(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductPeriod(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductPeriod
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductPeriodResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductPeriod(id int64, db *sql.DB, sql string, ch chan []*report.ProductPeriod) {
	ch <- queryDBForProductPeriod(id, db, sql)
}

func queryDBForProductPeriod(trackId int64, db *sql.DB, sqlStr string) []*report.ProductPeriod {
	results := make([]*report.ProductPeriod, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductPeriod)
		var value, val json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.RegionCode,
			&f.RegionName,
			&f.GrossAmount,
			&f.NetAmount,
			&f.ItemCount,
			&f.StoreType,
			&f.BusinessDays,
			&val,
			&value,
			&f.TaxFee,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(val, &f.Data)
		if err != nil {
			logger.Pre().Error("单品时段data结构体解析失败：", err)
			return results
		}
		err = json.Unmarshal(value, &f.Child)
		if err != nil {
			logger.Pre().Error("单品时段child结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results

}

func generateQuerySQLForProductPeriodSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	if condition.TagType == "SUMMARY" {
		rowSQL = `
			--单品时段报表汇总信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,{REGION_ID}
					ORDER BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					{REGION_ID} AS region_id
					{UNIT_SELECT}
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID} {UNIT_GROUP_BY}
				ORDER BY
					{REGION_ID}
					{UNIT_ORDER_BY}
				{LIMIT}
			), base_products AS (
				SELECT
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) AS product_id,
					COALESCE(product_caches.category, 0) AS product_category_id,
					coalesce(fact.unit, '') as unit, --商品单位
				    sum(weight) as weight, --商品份量
					extract(hour from fact.order_time) AS hours,
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(qty) AS item_count, --商品数量
					SUM(tax_fee) AS tax_fee
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
						INNER JOIN base_limit
							ON {REGION_ID} = base_limit.region_id
							{UNIT_JOIN}
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID},
					fact.product_id,
					product_caches.category,
					fact.unit,
					fact.has_weight,
					fact.order_time
				ORDER BY
					{REGION_ID}
				), base_products_for_period AS (
						SELECT
							region_id,
							store_type,
							product_id,
							product_category_id,
							unit, --商品单位
				    		sum(weight) as weight, --商品份量
							SUM(gross_amount) AS gross_amount,
							SUM(net_amount) AS net_amount,
							SUM(item_count) AS item_count,
							SUM(tax_fee) AS tax_fee,
					
							SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
							SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
							SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
							SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
							SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
							SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
							SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
							SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
							SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
							SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
							SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
							SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
							SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
							SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
							SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
							SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
							SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
							SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
							SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
							SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
							SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
							SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
							SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
							SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
					
							SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
							SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
							SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
							SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
							SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
							SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
							SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
							SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
							SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
							SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
							SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
							SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
							SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
							SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
							SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
							SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
							SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
							SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
							SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
							SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
							SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
							SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
							SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
							SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
					
							SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
							SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
							SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
							SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
							SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
							SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
							SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
							SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
							SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
							SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
							SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
							SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
							SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
							SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
							SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
							SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
							SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
							SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
							SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
							SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
							SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
							SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
							SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
							SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,
							SUM(CASE WHEN hours = 0 THEN weight ELSE 0 END) AS weight_00,
							SUM(CASE WHEN hours = 1 THEN weight ELSE 0 END) AS weight_01,
							SUM(CASE WHEN hours = 2 THEN weight ELSE 0 END) AS weight_02,
							SUM(CASE WHEN hours = 3 THEN weight ELSE 0 END) AS weight_03,
							SUM(CASE WHEN hours = 4 THEN weight ELSE 0 END) AS weight_04,
							SUM(CASE WHEN hours = 5 THEN weight ELSE 0 END) AS weight_05,
							SUM(CASE WHEN hours = 6 THEN weight ELSE 0 END) AS weight_06,
							SUM(CASE WHEN hours = 7 THEN weight ELSE 0 END) AS weight_07,
							SUM(CASE WHEN hours = 8 THEN weight ELSE 0 END) AS weight_08,
							SUM(CASE WHEN hours = 9 THEN weight ELSE 0 END) AS weight_09,
							SUM(CASE WHEN hours = 10 THEN weight ELSE 0 END) AS weight_10,
							SUM(CASE WHEN hours = 11 THEN weight ELSE 0 END) AS weight_11,
							SUM(CASE WHEN hours = 12 THEN weight ELSE 0 END) AS weight_12,
							SUM(CASE WHEN hours = 13 THEN weight ELSE 0 END) AS weight_13,
							SUM(CASE WHEN hours = 14 THEN weight ELSE 0 END) AS weight_14,
							SUM(CASE WHEN hours = 15 THEN weight ELSE 0 END) AS weight_15,
							SUM(CASE WHEN hours = 16 THEN weight ELSE 0 END) AS weight_16,
							SUM(CASE WHEN hours = 17 THEN weight ELSE 0 END) AS weight_17,
							SUM(CASE WHEN hours = 18 THEN weight ELSE 0 END) AS weight_18,
							SUM(CASE WHEN hours = 19 THEN weight ELSE 0 END) AS weight_19,
							SUM(CASE WHEN hours = 20 THEN weight ELSE 0 END) AS weight_20,
							SUM(CASE WHEN hours = 21 THEN weight ELSE 0 END) AS weight_21,
							SUM(CASE WHEN hours = 22 THEN weight ELSE 0 END) AS weight_22,
							SUM(CASE WHEN hours = 23 THEN weight ELSE 0 END) AS weight_23,
							
							SUM(CASE WHEN hours = 0 THEN tax_fee ELSE 0 END) AS tax_fee_00,
							SUM(CASE WHEN hours = 1 THEN tax_fee ELSE 0 END) AS tax_fee_01,
							SUM(CASE WHEN hours = 2 THEN tax_fee ELSE 0 END) AS tax_fee_02,
							SUM(CASE WHEN hours = 3 THEN tax_fee ELSE 0 END) AS tax_fee_03,
							SUM(CASE WHEN hours = 4 THEN tax_fee ELSE 0 END) AS tax_fee_04,
							SUM(CASE WHEN hours = 5 THEN tax_fee ELSE 0 END) AS tax_fee_05,
							SUM(CASE WHEN hours = 6 THEN tax_fee ELSE 0 END) AS tax_fee_06,
							SUM(CASE WHEN hours = 7 THEN tax_fee ELSE 0 END) AS tax_fee_07,
							SUM(CASE WHEN hours = 8 THEN tax_fee ELSE 0 END) AS tax_fee_08,
							SUM(CASE WHEN hours = 9 THEN tax_fee ELSE 0 END) AS tax_fee_09,
							SUM(CASE WHEN hours = 10 THEN tax_fee ELSE 0 END) AS tax_fee_10,
							SUM(CASE WHEN hours = 11 THEN tax_fee ELSE 0 END) AS tax_fee_11,
							SUM(CASE WHEN hours = 12 THEN tax_fee ELSE 0 END) AS tax_fee_12,
							SUM(CASE WHEN hours = 13 THEN tax_fee ELSE 0 END) AS tax_fee_13,
							SUM(CASE WHEN hours = 14 THEN tax_fee ELSE 0 END) AS tax_fee_14,
							SUM(CASE WHEN hours = 15 THEN tax_fee ELSE 0 END) AS tax_fee_15,
							SUM(CASE WHEN hours = 16 THEN tax_fee ELSE 0 END) AS tax_fee_16,
							SUM(CASE WHEN hours = 17 THEN tax_fee ELSE 0 END) AS tax_fee_17,
							SUM(CASE WHEN hours = 18 THEN tax_fee ELSE 0 END) AS tax_fee_18,
							SUM(CASE WHEN hours = 19 THEN tax_fee ELSE 0 END) AS tax_fee_19,
							SUM(CASE WHEN hours = 20 THEN tax_fee ELSE 0 END) AS tax_fee_20,
							SUM(CASE WHEN hours = 21 THEN tax_fee ELSE 0 END) AS tax_fee_21,
							SUM(CASE WHEN hours = 22 THEN tax_fee ELSE 0 END) AS tax_fee_22,
							SUM(CASE WHEN hours = 23 THEN tax_fee ELSE 0 END) AS tax_fee_23
						FROM base_products
						GROUP BY
							region_id,
							store_type,
							product_id,
							product_category_id,
							unit
				), base_products_by_date_and_region_for_product AS (
				SELECT
					region_id,
					max(store_type) as store_type,
					unit,
				    sum(weight) as weight,
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					SUM(tax_fee) AS tax_fee,
					JSON_BUILD_OBJECT(
                        'h00', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_00),
                            'gross_amount', SUM(gross_amount_00),
                            'net_amount', SUM(net_amount_00),
                            'item_count', SUM(item_count_00),
							'tax_fee',SUM(tax_fee_00)
                        ),
                        'h01', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_01),
                            'gross_amount', SUM(gross_amount_01),
                            'net_amount', SUM(net_amount_01),
                            'item_count', SUM(item_count_01),
							'tax_fee', SUM(tax_fee_01)
                        ),
                        'h02', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_02),
                            'gross_amount', SUM(gross_amount_02),
                            'net_amount', SUM(net_amount_02),
                            'item_count', SUM(item_count_02),
							'tax_fee', SUM(tax_fee_02)
                        ),
                        'h03', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_03),
                            'gross_amount', SUM(gross_amount_03),
                            'net_amount', SUM(net_amount_03),
                            'item_count', SUM(item_count_03),
							'tax_fee', SUM(tax_fee_03)
                        ),
                        'h04', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_04),
                            'gross_amount', SUM(gross_amount_04),
                            'net_amount', SUM(net_amount_04),
                            'item_count', SUM(item_count_04),
							'tax_fee', SUM(tax_fee_04)
                        ),
                        'h05', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_05),
                            'gross_amount', SUM(gross_amount_05),
                            'net_amount', SUM(net_amount_05),
                            'item_count', SUM(item_count_05),
							'tax_fee', SUM(tax_fee_05)
                        ),
                        'h06', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_06),
                            'gross_amount', SUM(gross_amount_06),
                            'net_amount', SUM(net_amount_06),
                            'item_count', SUM(item_count_06),
							'tax_fee', SUM(tax_fee_06)
                        ),
                        'h07', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_07),
                            'gross_amount', SUM(gross_amount_07),
                            'net_amount', SUM(net_amount_07),
                            'item_count', SUM(item_count_07),
							'tax_fee', SUM(tax_fee_07)
                        ),
                        'h08', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_08),
                            'gross_amount', SUM(gross_amount_08),
                            'net_amount', SUM(net_amount_08),
                            'item_count', SUM(item_count_08),
							'tax_fee', SUM(tax_fee_08)
                        ),
                        'h09', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_09),
                            'gross_amount', SUM(gross_amount_09),
                            'net_amount', SUM(net_amount_09),
                            'item_count', SUM(item_count_09),
							'tax_fee', SUM(tax_fee_09)
                        ),
                        'h10', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_10),
                            'gross_amount', SUM(gross_amount_10),
                            'net_amount', SUM(net_amount_10),
                            'item_count', SUM(item_count_10),
							'tax_fee', SUM(tax_fee_10)
                        ),
                        'h11', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_11),
                            'gross_amount', SUM(gross_amount_11),
                            'net_amount', SUM(net_amount_11),
                            'item_count', SUM(item_count_11),
							'tax_fee', SUM(tax_fee_11)
                        ),
                        'h12', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_12),
                            'gross_amount', SUM(gross_amount_12),
                            'net_amount', SUM(net_amount_12),
                            'item_count', SUM(item_count_12),
							'tax_fee', SUM(tax_fee_12)
                        ),
                        'h13', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_13),
                            'gross_amount', SUM(gross_amount_13),
                            'net_amount', SUM(net_amount_13),
                            'item_count', SUM(item_count_13),
							'tax_fee', SUM(tax_fee_13)
                        ),
                        'h14', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_14),
                            'gross_amount', SUM(gross_amount_14),
                            'net_amount', SUM(net_amount_14),
                            'item_count', SUM(item_count_14)
                        ),
                        'h15', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_15),
                            'gross_amount', SUM(gross_amount_15),
                            'net_amount', SUM(net_amount_15),
                            'item_count', SUM(item_count_15),
							'tax_fee', SUM(tax_fee_15)
                        ),
                        'h16', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_16),
                            'gross_amount', SUM(gross_amount_16),
                            'net_amount', SUM(net_amount_16),
                            'item_count', SUM(item_count_16),
							'tax_fee', SUM(tax_fee_16)
                        ),
                        'h17', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_17),
                            'gross_amount', SUM(gross_amount_17),
                            'net_amount', SUM(net_amount_17),
                            'item_count', SUM(item_count_17),
							'tax_fee', SUM(tax_fee_17)
                        ),
                        'h18', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_18),
                            'gross_amount', SUM(gross_amount_18),
                            'net_amount', SUM(net_amount_18),
                            'item_count', SUM(item_count_18),
							'tax_fee', SUM(tax_fee_18)
                        ),
                        'h19', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_19),
                            'gross_amount', SUM(gross_amount_19),
                            'net_amount', SUM(net_amount_19),
                            'item_count', SUM(item_count_19),
							'tax_fee', SUM(tax_fee_19)
                        ),
                        'h20', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_20),
                            'gross_amount', SUM(gross_amount_20),
                            'net_amount', SUM(net_amount_20),
                            'item_count', SUM(item_count_20),
							'tax_fee', SUM(tax_fee_20)
                        ),
                        'h21', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_21),
                            'gross_amount', SUM(gross_amount_21),
                            'net_amount', SUM(net_amount_21),
                            'item_count', SUM(item_count_21),
							'tax_fee', SUM(tax_fee_21)
                        ),
                        'h22', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_22),
                            'gross_amount', SUM(gross_amount_22),
                            'net_amount', SUM(net_amount_22),
                            'item_count', SUM(item_count_22),
							'tax_fee', SUM(tax_fee_22)
                        ),
                        'h23', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_23),
                            'gross_amount', SUM(gross_amount_23),
                            'net_amount', SUM(net_amount_23),
                            'item_count', SUM(item_count_23),
							'tax_fee', SUM(tax_fee_23)
                        )
                    ) AS "data",
					JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'product_id', product_id,
                            'product_code', '',
                            'product_name', '',
                            'product_category_id', product_category_id,
                            'product_category_code', '',
                            'product_category_name', '',
                            'unit', unit,
                            'h00', JSON_BUILD_OBJECT(
                                'weight', weight_00,
                                'weight_count', concat(weight_00,unit),
                                'gross_amount', gross_amount_00,
                                'net_amount', net_amount_00,
                                'item_count', item_count_00,
								'tax_fee', tax_fee_00
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                'weight', weight_01,
                                'weight_count', concat(weight_01,unit),
                                'gross_amount', gross_amount_01,
                                'net_amount', net_amount_01,
                                'item_count', item_count_01,
								'tax_fee', tax_fee_01
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                'weight', weight_02,
                                'weight_count', concat(weight_02,unit),
                                'gross_amount', gross_amount_02,
                                'net_amount', net_amount_02,
                                'item_count', item_count_02,
								'tax_fee', tax_fee_02
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                'weight', weight_03,
                                'weight_count', concat(weight_03,unit),
                                'gross_amount', gross_amount_03,
                                'net_amount', net_amount_03,
                                'item_count', item_count_03,
								'tax_fee', tax_fee_03
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                'weight', weight_04,
                                'weight_count', concat(weight_04,unit),
                                'gross_amount', gross_amount_04,
                                'net_amount', net_amount_04,
                                'item_count', item_count_04,
								'tax_fee', tax_fee_04
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                'weight', weight_05,
                                'weight_count', concat(weight_05,unit),
                                'gross_amount', gross_amount_05,
                                'net_amount', net_amount_05,
                                'item_count', item_count_05,
								'tax_fee', tax_fee_05
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                'weight', weight_06,
                                'weight_count', concat(weight_06,unit),
                                'gross_amount', gross_amount_06,
                                'net_amount', net_amount_06,
                                'item_count', item_count_06,
								'tax_fee', tax_fee_06
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                'weight', weight_07,
                                'weight_count', concat(weight_07,unit),
                                'gross_amount', gross_amount_07,
                                'net_amount', net_amount_07,
                                'item_count', item_count_07,
								'tax_fee', tax_fee_07
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                'weight', weight_08,
                                'weight_count', concat(weight_08,unit),
                                'gross_amount', gross_amount_08,
                                'net_amount', net_amount_08,
                                'item_count', item_count_08,
								'tax_fee', tax_fee_08
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                'weight', weight_09,
                                'weight_count', concat(weight_09,unit),
                                'gross_amount', gross_amount_09,
                                'net_amount', net_amount_09,
                                'item_count', item_count_09,
								'tax_fee', tax_fee_09
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                'weight', weight_10,
                                'weight_count', concat(weight_10,unit),
                                'gross_amount', gross_amount_10,
                                'net_amount', net_amount_10,
                                'item_count', item_count_10,
								'tax_fee', tax_fee_10
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                'weight', weight_11,
                                'weight_count', concat(weight_11,unit),
                                'gross_amount', gross_amount_11,
                                'net_amount', net_amount_11,
                                'item_count', item_count_11,
								'tax_fee', tax_fee_11
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                'weight', weight_12,
                                'weight_count', concat(weight_12,unit),
                                'gross_amount', gross_amount_12,
                                'net_amount', net_amount_12,
                                'item_count', item_count_12,
								'tax_fee', tax_fee_12
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                'weight', weight_13,
                                'weight_count', concat(weight_13,unit),
                                'gross_amount', gross_amount_13,
                                'net_amount', net_amount_13,
                                'item_count', item_count_13,
								'tax_fee', tax_fee_13
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                'weight', weight_14,
                                'weight_count', concat(weight_14,unit),
                                'gross_amount', gross_amount_14,
                                'net_amount', net_amount_14,
                                'item_count', item_count_14,
								'tax_fee', tax_fee_14
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                'weight', weight_15,
                                'weight_count', concat(weight_15,unit),
                                'gross_amount', gross_amount_15,
                                'net_amount', net_amount_15,
                                'item_count', item_count_15,
								'tax_fee', tax_fee_15
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                'weight', weight_16,
                                'weight_count', concat(weight_16,unit),
                                'gross_amount', gross_amount_16,
                                'net_amount', net_amount_16,
                                'item_count', item_count_16,
								'tax_fee', tax_fee_16
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                'weight', weight_17,
                                'weight_count', concat(weight_17,unit),
                                'gross_amount', gross_amount_17,
                                'net_amount', net_amount_17,
                                'item_count', item_count_17,
								'tax_fee', tax_fee_17
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                'weight', weight_18,
                                'weight_count', concat(weight_18,unit),
                                'gross_amount', gross_amount_18,
                                'net_amount', net_amount_18,
                                'item_count', item_count_18,
								'tax_fee', tax_fee_18,
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                'weight', weight_19,
                                'weight_count', concat(weight_19,unit),
                                'gross_amount', gross_amount_19,
                                'net_amount', net_amount_19,
                                'item_count', item_count_19,
								'tax_fee', tax_fee_19
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                'weight', weight_20,
                                'weight_count', concat(weight_20,unit),
                                'gross_amount', gross_amount_20,
                                'net_amount', net_amount_20,
                                'item_count', item_count_20,
								'tax_fee', tax_fee_20
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                'weight', weight_21,
                                'weight_count', concat(weight_21,unit),
                                'gross_amount', gross_amount_21,
                                'net_amount', net_amount_21,
                                'item_count', item_count_21,
								'tax_fee', tax_fee_21
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                'weight', weight_22,
                                'weight_count', concat(weight_22,unit),
                                'gross_amount', gross_amount_22,
                                'net_amount', net_amount_22,
                                'item_count', item_count_22,
								'tax_fee', tax_fee_22
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                'weight', weight_23,
                                'weight_count', concat(weight_23,unit),
                                'gross_amount', gross_amount_23,
                                'net_amount', net_amount_23,
                                'item_count', item_count_23,
								'tax_fee', tax_fee_23
                            )
                        )
                    ) AS "child"
				FROM base_products_for_period
				GROUP BY
					region_id,
					unit
			)
			SELECT
				'' AS bus_date,
				for_product.region_id AS region_id,
				'' AS region_code,
				'' AS region_name,
			
				for_product.gross_amount AS gross_amount,
				for_product.net_amount AS net_amount,
				for_product.item_count AS item_count,

				for_product.store_type AS store_type,
				bd.days AS business_days,
				COALESCE(for_product.data, '{}'::json) AS data,
				COALESCE(for_product.child, '[]'::json) AS child,
				for_product.tax_fee AS tax_fee,
			
				0 AS total --Summary时的汇总条数
			FROM
				base_products_by_date_and_region_for_product for_product
					LEFT JOIN business_days bd
						ON for_product.region_id = bd.region_id
			ORDER BY for_product.region_id {UNIT_ORDER_BY}
		`
		summarySQL = `
				--单品时段报表汇总信息 SUMMARY
				WITH base_products AS (
					SELECT
						COALESCE({REGION_ID}, 0) AS region_id,
				
						extract(hour from fact.order_time) AS hours,
				
						SUM(gross_amount) AS gross_amount, --商品流水
						SUM(net_amount) AS net_amount, --商品实收
						SUM(qty) AS item_count, --商品数量
						SUM(tax_fee) AS tax_fee -- 税费
						{UNIT_SELECT}
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY
						{REGION_ID},
						fact.order_time
						{UNIT_GROUP_BY}
					ORDER BY
						{REGION_ID}
				), base_products_for_period AS (
					SELECT
						region_id,
				
						SUM(gross_amount) AS gross_amount,
						SUM(net_amount) AS net_amount,
						SUM(item_count) AS item_count,
				
						SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
						SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
						SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
						SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
						SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
						SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
						SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
						SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
						SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
						SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
						SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
						SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
						SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
						SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
						SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
						SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
						SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
						SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
						SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
						SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
						SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
						SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
						SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
						SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
				
						SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
						SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
						SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
						SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
						SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
						SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
						SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
						SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
						SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
						SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
						SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
						SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
						SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
						SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
						SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
						SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
						SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
						SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
						SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
						SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
						SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
						SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
						SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
						SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
				
						SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
						SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
						SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
						SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
						SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
						SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
						SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
						SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
						SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
						SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
						SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
						SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
						SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
						SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
						SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
						SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
						SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
						SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
						SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
						SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
						SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
						SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
						SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
						SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

						SUM(CASE WHEN hours = 0 THEN tax_fee ELSE 0 END) AS tax_fee_00,
						SUM(CASE WHEN hours = 1 THEN tax_fee ELSE 0 END) AS tax_fee_01,
						SUM(CASE WHEN hours = 2 THEN tax_fee ELSE 0 END) AS tax_fee_02,
						SUM(CASE WHEN hours = 3 THEN tax_fee ELSE 0 END) AS tax_fee_03,
						SUM(CASE WHEN hours = 4 THEN tax_fee ELSE 0 END) AS tax_fee_04,
						SUM(CASE WHEN hours = 5 THEN tax_fee ELSE 0 END) AS tax_fee_05,
						SUM(CASE WHEN hours = 6 THEN tax_fee ELSE 0 END) AS tax_fee_06,
						SUM(CASE WHEN hours = 7 THEN tax_fee ELSE 0 END) AS tax_fee_07,
						SUM(CASE WHEN hours = 8 THEN tax_fee ELSE 0 END) AS tax_fee_08,
						SUM(CASE WHEN hours = 9 THEN tax_fee ELSE 0 END) AS tax_fee_09,
						SUM(CASE WHEN hours = 10 THEN tax_fee ELSE 0 END) AS tax_fee_10,
						SUM(CASE WHEN hours = 11 THEN tax_fee ELSE 0 END) AS tax_fee_11,
						SUM(CASE WHEN hours = 12 THEN tax_fee ELSE 0 END) AS tax_fee_12,
						SUM(CASE WHEN hours = 13 THEN tax_fee ELSE 0 END) AS tax_fee_13,
						SUM(CASE WHEN hours = 14 THEN tax_fee ELSE 0 END) AS tax_fee_14,
						SUM(CASE WHEN hours = 15 THEN tax_fee ELSE 0 END) AS tax_fee_15,
						SUM(CASE WHEN hours = 16 THEN tax_fee ELSE 0 END) AS tax_fee_16,
						SUM(CASE WHEN hours = 17 THEN tax_fee ELSE 0 END) AS tax_fee_17,
						SUM(CASE WHEN hours = 18 THEN tax_fee ELSE 0 END) AS tax_fee_18,
						SUM(CASE WHEN hours = 19 THEN tax_fee ELSE 0 END) AS tax_fee_19,
						SUM(CASE WHEN hours = 20 THEN tax_fee ELSE 0 END) AS tax_fee_20,
						SUM(CASE WHEN hours = 21 THEN tax_fee ELSE 0 END) AS tax_fee_21,
						SUM(CASE WHEN hours = 22 THEN tax_fee ELSE 0 END) AS tax_fee_22,
						SUM(CASE WHEN hours = 23 THEN tax_fee ELSE 0 END) AS tax_fee_23
					FROM base_products
					GROUP BY
						region_id
						{UNIT_GROUP_BY}
				),base_products_for_json AS (
					SELECT
						SUM(gross_amount) AS gross_amount,
						SUM(net_amount) AS net_amount,
						SUM(item_count) AS item_count,
						SUM(tax_fee) AS tax_fee,
				
						JSON_BUILD_OBJECT(
                            'product_id', 0,
                            'product_code', '',
                            'product_name', '',
                            'product_category_id', 0,
                            'product_category_code', '',
                            'product_category_name', '',
                            'h00', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_00),
                                'net_amount', SUM(net_amount_00),
                                'item_count', SUM(item_count_00),
								'tax_fee', SUM(tax_fee_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_01),
                                'net_amount', SUM(net_amount_01),
                                'item_count', SUM(item_count_01),
								'tax_fee', SUM(tax_fee_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_02),
                                'net_amount', SUM(net_amount_02),
                                'item_count', SUM(item_count_02),
								'tax_fee', SUM(tax_fee_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_03),
                                'net_amount', SUM(net_amount_03),
                                'item_count', SUM(item_count_03),
								'tax_fee', SUM(tax_fee_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_04),
                                'net_amount', SUM(net_amount_04),
                                'item_count', SUM(item_count_04),
								'tax_fee', SUM(tax_fee_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_05),
                                'net_amount', SUM(net_amount_05),
                                'item_count', SUM(item_count_05),
								'tax_fee', SUM(tax_fee_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_06),
                                'net_amount', SUM(net_amount_06),
                                'item_count', SUM(item_count_06),
								'tax_fee', SUM(tax_fee_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_07),
                                'net_amount', SUM(net_amount_07),
                                'item_count', SUM(item_count_07),
								'tax_fee', SUM(tax_fee_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_08),
                                'net_amount', SUM(net_amount_08),
                                'item_count', SUM(item_count_08),
								'tax_fee', SUM(tax_fee_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_09),
                                'net_amount', SUM(net_amount_09),
                                'item_count', SUM(item_count_09),
								'tax_fee', SUM(tax_fee_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_10),
                                'net_amount', SUM(net_amount_10),
                                'item_count', SUM(item_count_10),
								'tax_fee', SUM(tax_fee_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_11),
                                'net_amount', SUM(net_amount_11),
                                'item_count', SUM(item_count_11),
								'tax_fee', SUM(tax_fee_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_12),
                                'net_amount', SUM(net_amount_12),
                                'item_count', SUM(item_count_12),
								'tax_fee', SUM(tax_fee_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_13),
                                'net_amount', SUM(net_amount_13),
                                'item_count', SUM(item_count_13),
								'tax_fee', SUM(tax_fee_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_14),
                                'net_amount', SUM(net_amount_14),
                                'item_count', SUM(item_count_14),
								'tax_fee', SUM(tax_fee_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_15),
                                'net_amount', SUM(net_amount_15),
                                'item_count', SUM(item_count_15),
								'tax_fee', SUM(tax_fee_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_16),
                                'net_amount', SUM(net_amount_16),
                                'item_count', SUM(item_count_16),
								'tax_fee', SUM(tax_fee_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_17),
                                'net_amount', SUM(net_amount_17),
                                'item_count', SUM(item_count_17),
								'tax_fee', SUM(tax_fee_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_18),
                                'net_amount', SUM(net_amount_18),
                                'item_count', SUM(item_count_18),
								'tax_fee', SUM(tax_fee_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_19),
                                'net_amount', SUM(net_amount_19),
                                'item_count', SUM(item_count_19),
								'tax_fee', SUM(tax_fee_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_20),
                                'net_amount', SUM(net_amount_20),
                                'item_count', SUM(item_count_20),
								'tax_fee', SUM(tax_fee_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_21),
                                'net_amount', SUM(net_amount_21),
                                'item_count', SUM(item_count_21),
								'tax_fee', SUM(tax_fee_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_22),
                                'net_amount', SUM(net_amount_22),
                                'item_count', SUM(item_count_22),
								'tax_fee', SUM(tax_fee_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_23),
                                'net_amount', SUM(net_amount_23),
                                'item_count', SUM(item_count_23),
								'tax_fee', SUM(tax_fee_23)
                            )
                        ) AS "child",
						COUNT(1) AS total --Summary时的汇总条数
					FROM base_products_for_period
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS region_code,
					'' AS region_name,
				
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					'' AS store_type,
					0 AS business_days, --营业天数,
					'{}'::json AS data,
					JSON_AGG(child) AS child,
					SUM(tax_fee) AS tax_fee,
					SUM(total) AS total

				FROM
					base_products_for_json
		`
	} else {
		rowSQL = `
			--单品时段报表详细信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					fact.bus_date AS bus_date,
					{REGION_ID} AS region_id
					{UNIT_SELECT}
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID}
					{UNIT_GROUP_BY}
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
					{UNIT_ORDER_BY}
				{LIMIT}
			), base_products AS (
				SELECT
					fact.bus_date AS bus_date,
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) AS product_id,
					COALESCE(product_caches.category, 0) AS product_category_id,
					coalesce(fact.unit, '') as unit, --商品单位
				    sum(weight) as weight, --商品份量
					extract(hour from fact.order_time) AS hours,
			
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(qty) AS item_count, --商品数量
					SUM(tax_fee) AS tax_fee --税费
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
						INNER JOIN base_limit
							ON fact.bus_date = base_limit.bus_date
								AND {REGION_ID} = base_limit.region_id
								{UNIT_JOIN}
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID},
					fact.product_id,
					product_caches.category,
					fact.unit,
				    fact.has_weight,
					fact.order_time
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
			), base_products_for_period AS (
				SELECT
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit, --商品单位
				    sum(weight) as weight, --商品份量
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					SUM(tax_fee) AS tax_fee,
			
					SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
					SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
					SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
					SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
					SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
					SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
					SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
					SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
					SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
					SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
					SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
					SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
					SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
					SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
					SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
					SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
					SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
					SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
					SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
					SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
					SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
					SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
					SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
					SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
					SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
					SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
					SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
					SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
					SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
					SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
					SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
					SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
					SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
					SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
					SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
					SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
					SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
					SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
					SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
					SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
					SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
					SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
					SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
					SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
					SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
					SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
					SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
			
					SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
					SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
					SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
					SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
					SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
					SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
					SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
					SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
					SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
					SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
					SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
					SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
					SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
					SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
					SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
					SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
					SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
					SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
					SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
					SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
					SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
					SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
					SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
					SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,
					SUM(CASE WHEN hours = 0 THEN weight ELSE 0 END) AS weight_00,
                    SUM(CASE WHEN hours = 1 THEN weight ELSE 0 END) AS weight_01,
                    SUM(CASE WHEN hours = 2 THEN weight ELSE 0 END) AS weight_02,
                    SUM(CASE WHEN hours = 3 THEN weight ELSE 0 END) AS weight_03,
                    SUM(CASE WHEN hours = 4 THEN weight ELSE 0 END) AS weight_04,
                    SUM(CASE WHEN hours = 5 THEN weight ELSE 0 END) AS weight_05,
                    SUM(CASE WHEN hours = 6 THEN weight ELSE 0 END) AS weight_06,
                    SUM(CASE WHEN hours = 7 THEN weight ELSE 0 END) AS weight_07,
                    SUM(CASE WHEN hours = 8 THEN weight ELSE 0 END) AS weight_08,
                    SUM(CASE WHEN hours = 9 THEN weight ELSE 0 END) AS weight_09,
                    SUM(CASE WHEN hours = 10 THEN weight ELSE 0 END) AS weight_10,
                    SUM(CASE WHEN hours = 11 THEN weight ELSE 0 END) AS weight_11,
                    SUM(CASE WHEN hours = 12 THEN weight ELSE 0 END) AS weight_12,
                    SUM(CASE WHEN hours = 13 THEN weight ELSE 0 END) AS weight_13,
                    SUM(CASE WHEN hours = 14 THEN weight ELSE 0 END) AS weight_14,
                    SUM(CASE WHEN hours = 15 THEN weight ELSE 0 END) AS weight_15,
                    SUM(CASE WHEN hours = 16 THEN weight ELSE 0 END) AS weight_16,
                    SUM(CASE WHEN hours = 17 THEN weight ELSE 0 END) AS weight_17,
                    SUM(CASE WHEN hours = 18 THEN weight ELSE 0 END) AS weight_18,
                    SUM(CASE WHEN hours = 19 THEN weight ELSE 0 END) AS weight_19,
                    SUM(CASE WHEN hours = 20 THEN weight ELSE 0 END) AS weight_20,
                    SUM(CASE WHEN hours = 21 THEN weight ELSE 0 END) AS weight_21,
                    SUM(CASE WHEN hours = 22 THEN weight ELSE 0 END) AS weight_22,
                    SUM(CASE WHEN hours = 23 THEN weight ELSE 0 END) AS weight_23,

					SUM(CASE WHEN hours = 0 THEN tax_fee ELSE 0 END) AS tax_fee_00,
                    SUM(CASE WHEN hours = 1 THEN tax_fee ELSE 0 END) AS tax_fee_01,
                    SUM(CASE WHEN hours = 2 THEN tax_fee ELSE 0 END) AS tax_fee_02,
                    SUM(CASE WHEN hours = 3 THEN tax_fee ELSE 0 END) AS tax_fee_03,
                    SUM(CASE WHEN hours = 4 THEN tax_fee ELSE 0 END) AS tax_fee_04,
                    SUM(CASE WHEN hours = 5 THEN tax_fee ELSE 0 END) AS tax_fee_05,
                    SUM(CASE WHEN hours = 6 THEN tax_fee ELSE 0 END) AS tax_fee_06,
                    SUM(CASE WHEN hours = 7 THEN tax_fee ELSE 0 END) AS tax_fee_07,
                    SUM(CASE WHEN hours = 8 THEN tax_fee ELSE 0 END) AS tax_fee_08,
                    SUM(CASE WHEN hours = 9 THEN tax_fee ELSE 0 END) AS tax_fee_09,
                    SUM(CASE WHEN hours = 10 THEN tax_fee ELSE 0 END) AS tax_fee_10,
                    SUM(CASE WHEN hours = 11 THEN tax_fee ELSE 0 END) AS tax_fee_11,
                    SUM(CASE WHEN hours = 12 THEN tax_fee ELSE 0 END) AS tax_fee_12,
                    SUM(CASE WHEN hours = 13 THEN tax_fee ELSE 0 END) AS tax_fee_13,
                    SUM(CASE WHEN hours = 14 THEN tax_fee ELSE 0 END) AS tax_fee_14,
                    SUM(CASE WHEN hours = 15 THEN tax_fee ELSE 0 END) AS tax_fee_15,
                    SUM(CASE WHEN hours = 16 THEN tax_fee ELSE 0 END) AS tax_fee_16,
                    SUM(CASE WHEN hours = 17 THEN tax_fee ELSE 0 END) AS tax_fee_17,
                    SUM(CASE WHEN hours = 18 THEN tax_fee ELSE 0 END) AS tax_fee_18,
                    SUM(CASE WHEN hours = 19 THEN tax_fee ELSE 0 END) AS tax_fee_19,
                    SUM(CASE WHEN hours = 20 THEN tax_fee ELSE 0 END) AS tax_fee_20,
                    SUM(CASE WHEN hours = 21 THEN tax_fee ELSE 0 END) AS tax_fee_21,
                    SUM(CASE WHEN hours = 22 THEN tax_fee ELSE 0 END) AS tax_fee_22,
                    SUM(CASE WHEN hours = 23 THEN tax_fee ELSE 0 END) AS tax_fee_23
				FROM base_products
				GROUP BY
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit
			), base_products_by_date_and_region_for_product AS (
				SELECT
					bus_date,
					region_id,
					max(store_type) as store_type,
					unit,
				    sum(weight) as weight,
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					SUM(tax_fee) AS tax_fee,
					JSON_BUILD_OBJECT(
                        'h00', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_00),
                            'gross_amount', SUM(gross_amount_00),
                            'net_amount', SUM(net_amount_00),
                            'item_count', SUM(item_count_00),
							'tax_fee', SUM(tax_fee_00)
                        ),
                        'h01', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_01),
                            'gross_amount', SUM(gross_amount_01),
                            'net_amount', SUM(net_amount_01),
                            'item_count', SUM(item_count_01),
							'tax_fee', SUM(tax_fee_01)
                        ),
                        'h02', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_02),
                            'gross_amount', SUM(gross_amount_02),
                            'net_amount', SUM(net_amount_02),
                            'item_count', SUM(item_count_02),
							'tax_fee', SUM(tax_fee_02)
                        ),
                        'h03', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_03),
                            'gross_amount', SUM(gross_amount_03),
                            'net_amount', SUM(net_amount_03),
                            'item_count', SUM(item_count_03),
							'tax_fee', SUM(tax_fee_03)
                        ),
                        'h04', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_04),
                            'gross_amount', SUM(gross_amount_04),
                            'net_amount', SUM(net_amount_04),
                            'item_count', SUM(item_count_04),
							'tax_fee', SUM(tax_fee_04)
                        ),
                        'h05', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_05),
                            'gross_amount', SUM(gross_amount_05),
                            'net_amount', SUM(net_amount_05),
                            'item_count', SUM(item_count_05),
							'tax_fee', SUM(tax_fee_05)
                        ),
                        'h06', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_06),
                            'gross_amount', SUM(gross_amount_06),
                            'net_amount', SUM(net_amount_06),
                            'item_count', SUM(item_count_06),
							'tax_fee', SUM(tax_fee_06)
                        ),
                        'h07', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_07),
                            'gross_amount', SUM(gross_amount_07),
                            'net_amount', SUM(net_amount_07),
                            'item_count', SUM(item_count_07),
							'tax_fee', SUM(tax_fee_07)
                        ),
                        'h08', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_08),
                            'gross_amount', SUM(gross_amount_08),
                            'net_amount', SUM(net_amount_08),
                            'item_count', SUM(item_count_08),
							'tax_fee', SUM(tax_fee_08)
                        ),
                        'h09', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_09),
                            'gross_amount', SUM(gross_amount_09),
                            'net_amount', SUM(net_amount_09),
                            'item_count', SUM(item_count_09),
							'tax_fee', SUM(tax_fee_09)
                        ),
                        'h10', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_10),
                            'gross_amount', SUM(gross_amount_10),
                            'net_amount', SUM(net_amount_10),
                            'item_count', SUM(item_count_10),
							'tax_fee', SUM(tax_fee_10)
                        ),
                        'h11', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_11),
                            'gross_amount', SUM(gross_amount_11),
                            'net_amount', SUM(net_amount_11),
                            'item_count', SUM(item_count_11),
							'tax_fee', SUM(tax_fee_11)
                        ),
                        'h12', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_12),
                            'gross_amount', SUM(gross_amount_12),
                            'net_amount', SUM(net_amount_12),
                            'item_count', SUM(item_count_12),
							'tax_fee', SUM(tax_fee_12)
                        ),
                        'h13', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_13),
                            'gross_amount', SUM(gross_amount_13),
                            'net_amount', SUM(net_amount_13),
                            'item_count', SUM(item_count_13),
							'tax_fee', SUM(tax_fee_13)
                        ),
                        'h14', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_14),
                            'gross_amount', SUM(gross_amount_14),
                            'net_amount', SUM(net_amount_14),
                            'item_count', SUM(item_count_14),
							'tax_fee', SUM(tax_fee_14)
                        ),
                        'h15', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_15),
                            'gross_amount', SUM(gross_amount_15),
                            'net_amount', SUM(net_amount_15),
                            'item_count', SUM(item_count_15),
							'tax_fee', SUM(tax_fee_15)
                        ),
                        'h16', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_16),
                            'gross_amount', SUM(gross_amount_16),
                            'net_amount', SUM(net_amount_16),
                            'item_count', SUM(item_count_16),
							'tax_fee', SUM(tax_fee_16)
                        ),
                        'h17', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_17),
                            'gross_amount', SUM(gross_amount_17),
                            'net_amount', SUM(net_amount_17),
                            'item_count', SUM(item_count_17),
							'tax_fee', SUM(tax_fee_17)
                        ),
                        'h18', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_18),
                            'gross_amount', SUM(gross_amount_18),
                            'net_amount', SUM(net_amount_18),
                            'item_count', SUM(item_count_18),
							'tax_fee', SUM(tax_fee_18)
                        ),
                        'h19', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_19),
                            'gross_amount', SUM(gross_amount_19),
                            'net_amount', SUM(net_amount_19),
                            'item_count', SUM(item_count_19),
							'tax_fee', SUM(tax_fee_19)
                        ),
                        'h20', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_20),
                            'gross_amount', SUM(gross_amount_20),
                            'net_amount', SUM(net_amount_20),
                            'item_count', SUM(item_count_20),
							'tax_fee', SUM(tax_fee_20)
                        ),
                        'h21', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_21),
                            'gross_amount', SUM(gross_amount_21),
                            'net_amount', SUM(net_amount_21),
                            'item_count', SUM(item_count_21),
							'tax_fee', SUM(tax_fee_21)
                        ),
                        'h22', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_22),
                            'gross_amount', SUM(gross_amount_22),
                            'net_amount', SUM(net_amount_22),
                            'item_count', SUM(item_count_22),
							'tax_fee', SUM(tax_fee_22)
                        ),
                        'h23', JSON_BUILD_OBJECT(
                            'weight', SUM(weight_23),
                            'gross_amount', SUM(gross_amount_23),
                            'net_amount', SUM(net_amount_23),
                            'item_count', SUM(item_count_23),
							'tax_fee', SUM(tax_fee_23)
                        )
                    ) AS "data",
					JSON_AGG(
                        JSON_BUILD_OBJECT(
                            'product_id', product_id,
                            'product_code', '',
                            'product_name', '',
                            'product_category_id', product_category_id,
                            'product_category_code', '',
                            'product_category_name', '',
                            'unit', unit,
                            'h00', JSON_BUILD_OBJECT(
                                'weight', weight_00,
                                'weight_count', concat(weight_00,unit),
                                'gross_amount', gross_amount_00,
                                'net_amount', net_amount_00,
                                'item_count', item_count_00,
								'tax_fee', tax_fee_00
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                'weight', weight_01,
                                'weight_count', concat(weight_01,unit),
                                'gross_amount', gross_amount_01,
                                'net_amount', net_amount_01,
                                'item_count', item_count_01,
								'tax_fee', tax_fee_01
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                'weight', weight_02,
                                'weight_count', concat(weight_02,unit),
                                'gross_amount', gross_amount_02,
                                'net_amount', net_amount_02,
                                'item_count', item_count_02,
								'tax_fee', tax_fee_02
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                'weight', weight_03,
                                'weight_count', concat(weight_03,unit),
                                'gross_amount', gross_amount_03,
                                'net_amount', net_amount_03,
                                'item_count', item_count_03,
								'tax_fee', tax_fee_03
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                'weight', weight_04,
                                'weight_count', concat(weight_04,unit),
                                'gross_amount', gross_amount_04,
                                'net_amount', net_amount_04,
                                'item_count', item_count_04,
								'tax_fee', tax_fee_04
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                'weight', weight_05,
                                'weight_count', concat(weight_05,unit),
                                'gross_amount', gross_amount_05,
                                'net_amount', net_amount_05,
                                'item_count', item_count_05,
								'tax_fee', tax_fee_05
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                'weight', weight_06,
                                'weight_count', concat(weight_06,unit),
                                'gross_amount', gross_amount_06,
                                'net_amount', net_amount_06,
                                'item_count', item_count_06,
								'tax_fee', tax_fee_06
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                'weight', weight_07,
                                'weight_count', concat(weight_07,unit),
                                'gross_amount', gross_amount_07,
                                'net_amount', net_amount_07,
                                'item_count', item_count_07,
								'tax_fee', tax_fee_07
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                'weight', weight_08,
                                'weight_count', concat(weight_08,unit),
                                'gross_amount', gross_amount_08,
                                'net_amount', net_amount_08,
                                'item_count', item_count_08,
								'tax_fee', tax_fee_08
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                'weight', weight_09,
                                'weight_count', concat(weight_09,unit),
                                'gross_amount', gross_amount_09,
                                'net_amount', net_amount_09,
                                'item_count', item_count_09,
								'tax_fee', tax_fee_09
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                'weight', weight_10,
                                'weight_count', concat(weight_10,unit),
                                'gross_amount', gross_amount_10,
                                'net_amount', net_amount_10,
                                'item_count', item_count_10,
								'tax_fee', tax_fee_10
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                'weight', weight_11,
                                'weight_count', concat(weight_11,unit),
                                'gross_amount', gross_amount_11,
                                'net_amount', net_amount_11,
                                'item_count', item_count_11,
								'tax_fee', tax_fee_11
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                'weight', weight_12,
                                'weight_count', concat(weight_12,unit),
                                'gross_amount', gross_amount_12,
                                'net_amount', net_amount_12,
                                'item_count', item_count_12,
								'tax_fee', tax_fee_12
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                'weight', weight_13,
                                'weight_count', concat(weight_13,unit),
                                'gross_amount', gross_amount_13,
                                'net_amount', net_amount_13,
                                'item_count', item_count_13,
								'tax_fee', tax_fee_13
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                'weight', weight_14,
                                'weight_count', concat(weight_14,unit),
                                'gross_amount', gross_amount_14,
                                'net_amount', net_amount_14,
                                'item_count', item_count_14,
								'tax_fee', tax_fee_14
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                'weight', weight_15,
                                'weight_count', concat(weight_15,unit),
                                'gross_amount', gross_amount_15,
                                'net_amount', net_amount_15,
                                'item_count', item_count_15,
								'tax_fee', tax_fee_15
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                'weight', weight_16,
                                'weight_count', concat(weight_16,unit),
                                'gross_amount', gross_amount_16,
                                'net_amount', net_amount_16,
                                'item_count', item_count_16,
								'tax_fee', tax_fee_16
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                'weight', weight_17,
                                'weight_count', concat(weight_17,unit),
                                'gross_amount', gross_amount_17,
                                'net_amount', net_amount_17,
                                'item_count', item_count_17,
								'tax_fee', tax_fee_17
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                'weight', weight_18,
                                'weight_count', concat(weight_18,unit),
                                'gross_amount', gross_amount_18,
                                'net_amount', net_amount_18,
                                'item_count', item_count_18,
								'tax_fee', tax_fee_18
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                'weight', weight_19,
                                'weight_count', concat(weight_19,unit),
                                'gross_amount', gross_amount_19,
                                'net_amount', net_amount_19,
                                'item_count', item_count_19,
								'tax_fee', tax_fee_19
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                'weight', weight_20,
                                'weight_count', concat(weight_20,unit),
                                'gross_amount', gross_amount_20,
                                'net_amount', net_amount_20,
                                'item_count', item_count_20,
								'tax_fee', tax_fee_20
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                'weight', weight_21,
                                'weight_count', concat(weight_21,unit),
                                'gross_amount', gross_amount_21,
                                'net_amount', net_amount_21,
                                'item_count', item_count_21,
								'tax_fee', tax_fee_21
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                'weight', weight_22,
                                'weight_count', concat(weight_22,unit),
                                'gross_amount', gross_amount_22,
                                'net_amount', net_amount_22,
                                'item_count', item_count_22,
								'tax_fee', tax_fee_22
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                'weight', weight_23,
                                'weight_count', concat(weight_23,unit),
                                'gross_amount', gross_amount_23,
                                'net_amount', net_amount_23,
                                'item_count', item_count_23,
								'tax_fee', tax_fee_23
                            )
                        )
                    ) AS "child"
				FROM base_products_for_period
				GROUP BY
					bus_date,
					region_id,
					unit
			)
			SELECT
				to_char(for_product.bus_date,'YYYY-MM-DD') AS bus_date,
				for_product.region_id AS region_id,
				'' AS region_code,
				'' AS region_name,
			
				for_product.gross_amount AS gross_amount,
				for_product.net_amount AS net_amount,
				for_product.item_count AS item_count,
				for_product.store_type AS store_type,
				bd.days AS business_days,
			
				COALESCE(for_product.data, '{}'::json) AS data,
				COALESCE(for_product.child, '[]'::json) AS child,	
				for_product.tax_fee as tax_fee,
				0 AS total --Summary时的汇总条数
			FROM
				base_products_by_date_and_region_for_product for_product
					LEFT JOIN business_days bd
						ON for_product.region_id = bd.region_id
			ORDER BY for_product.bus_date DESC, for_product.region_id {UNIT_ORDER_BY}
		`
		summarySQL = `
				--单品时段报表详细信息 SUMMARY
				WITH base_products AS (
					SELECT
						fact.bus_date AS bus_date,
						{REGION_ID} AS region_id,
				
						extract(hour from fact.order_time) AS hours,
				
						SUM(gross_amount) AS gross_amount, --商品流水
						SUM(net_amount) AS net_amount, --商品实收
						SUM(qty) AS item_count, --商品数量
						SUM(tax_fee) AS tax_fee --税费
						{UNIT_SELECT}
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN product_caches
								ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY
						fact.bus_date,
						{REGION_ID},
						fact.order_time
						{UNIT_GROUP_BY}
					ORDER BY
						fact.bus_date DESC,
						{REGION_ID}
				), base_products_for_period AS (
					SELECT
						bus_date,
						region_id,
				
						SUM(gross_amount) AS gross_amount,
						SUM(net_amount) AS net_amount,
						SUM(item_count) AS item_count,
						SUM(tax_fee) AS tax_fee,
				
						SUM(CASE WHEN hours = 0 THEN gross_amount ELSE 0 END) AS gross_amount_00,
						SUM(CASE WHEN hours = 1 THEN gross_amount ELSE 0 END) AS gross_amount_01,
						SUM(CASE WHEN hours = 2 THEN gross_amount ELSE 0 END) AS gross_amount_02,
						SUM(CASE WHEN hours = 3 THEN gross_amount ELSE 0 END) AS gross_amount_03,
						SUM(CASE WHEN hours = 4 THEN gross_amount ELSE 0 END) AS gross_amount_04,
						SUM(CASE WHEN hours = 5 THEN gross_amount ELSE 0 END) AS gross_amount_05,
						SUM(CASE WHEN hours = 6 THEN gross_amount ELSE 0 END) AS gross_amount_06,
						SUM(CASE WHEN hours = 7 THEN gross_amount ELSE 0 END) AS gross_amount_07,
						SUM(CASE WHEN hours = 8 THEN gross_amount ELSE 0 END) AS gross_amount_08,
						SUM(CASE WHEN hours = 9 THEN gross_amount ELSE 0 END) AS gross_amount_09,
						SUM(CASE WHEN hours = 10 THEN gross_amount ELSE 0 END) AS gross_amount_10,
						SUM(CASE WHEN hours = 11 THEN gross_amount ELSE 0 END) AS gross_amount_11,
						SUM(CASE WHEN hours = 12 THEN gross_amount ELSE 0 END) AS gross_amount_12,
						SUM(CASE WHEN hours = 13 THEN gross_amount ELSE 0 END) AS gross_amount_13,
						SUM(CASE WHEN hours = 14 THEN gross_amount ELSE 0 END) AS gross_amount_14,
						SUM(CASE WHEN hours = 15 THEN gross_amount ELSE 0 END) AS gross_amount_15,
						SUM(CASE WHEN hours = 16 THEN gross_amount ELSE 0 END) AS gross_amount_16,
						SUM(CASE WHEN hours = 17 THEN gross_amount ELSE 0 END) AS gross_amount_17,
						SUM(CASE WHEN hours = 18 THEN gross_amount ELSE 0 END) AS gross_amount_18,
						SUM(CASE WHEN hours = 19 THEN gross_amount ELSE 0 END) AS gross_amount_19,
						SUM(CASE WHEN hours = 20 THEN gross_amount ELSE 0 END) AS gross_amount_20,
						SUM(CASE WHEN hours = 21 THEN gross_amount ELSE 0 END) AS gross_amount_21,
						SUM(CASE WHEN hours = 22 THEN gross_amount ELSE 0 END) AS gross_amount_22,
						SUM(CASE WHEN hours = 23 THEN gross_amount ELSE 0 END) AS gross_amount_23,
				
						SUM(CASE WHEN hours = 0 THEN net_amount ELSE 0 END) AS net_amount_00,
						SUM(CASE WHEN hours = 1 THEN net_amount ELSE 0 END) AS net_amount_01,
						SUM(CASE WHEN hours = 2 THEN net_amount ELSE 0 END) AS net_amount_02,
						SUM(CASE WHEN hours = 3 THEN net_amount ELSE 0 END) AS net_amount_03,
						SUM(CASE WHEN hours = 4 THEN net_amount ELSE 0 END) AS net_amount_04,
						SUM(CASE WHEN hours = 5 THEN net_amount ELSE 0 END) AS net_amount_05,
						SUM(CASE WHEN hours = 6 THEN net_amount ELSE 0 END) AS net_amount_06,
						SUM(CASE WHEN hours = 7 THEN net_amount ELSE 0 END) AS net_amount_07,
						SUM(CASE WHEN hours = 8 THEN net_amount ELSE 0 END) AS net_amount_08,
						SUM(CASE WHEN hours = 9 THEN net_amount ELSE 0 END) AS net_amount_09,
						SUM(CASE WHEN hours = 10 THEN net_amount ELSE 0 END) AS net_amount_10,
						SUM(CASE WHEN hours = 11 THEN net_amount ELSE 0 END) AS net_amount_11,
						SUM(CASE WHEN hours = 12 THEN net_amount ELSE 0 END) AS net_amount_12,
						SUM(CASE WHEN hours = 13 THEN net_amount ELSE 0 END) AS net_amount_13,
						SUM(CASE WHEN hours = 14 THEN net_amount ELSE 0 END) AS net_amount_14,
						SUM(CASE WHEN hours = 15 THEN net_amount ELSE 0 END) AS net_amount_15,
						SUM(CASE WHEN hours = 16 THEN net_amount ELSE 0 END) AS net_amount_16,
						SUM(CASE WHEN hours = 17 THEN net_amount ELSE 0 END) AS net_amount_17,
						SUM(CASE WHEN hours = 18 THEN net_amount ELSE 0 END) AS net_amount_18,
						SUM(CASE WHEN hours = 19 THEN net_amount ELSE 0 END) AS net_amount_19,
						SUM(CASE WHEN hours = 20 THEN net_amount ELSE 0 END) AS net_amount_20,
						SUM(CASE WHEN hours = 21 THEN net_amount ELSE 0 END) AS net_amount_21,
						SUM(CASE WHEN hours = 22 THEN net_amount ELSE 0 END) AS net_amount_22,
						SUM(CASE WHEN hours = 23 THEN net_amount ELSE 0 END) AS net_amount_23,
				
						SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
						SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
						SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
						SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
						SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
						SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
						SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
						SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
						SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
						SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
						SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
						SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
						SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
						SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
						SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
						SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
						SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
						SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
						SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
						SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
						SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
						SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
						SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
						SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,
						
						SUM(CASE WHEN hours = 0 THEN tax_fee ELSE 0 END) AS tax_fee_00,
						SUM(CASE WHEN hours = 1 THEN tax_fee ELSE 0 END) AS tax_fee_01,
						SUM(CASE WHEN hours = 2 THEN tax_fee ELSE 0 END) AS tax_fee_02,
						SUM(CASE WHEN hours = 3 THEN tax_fee ELSE 0 END) AS tax_fee_03,
						SUM(CASE WHEN hours = 4 THEN tax_fee ELSE 0 END) AS tax_fee_04,
						SUM(CASE WHEN hours = 5 THEN tax_fee ELSE 0 END) AS tax_fee_05,
						SUM(CASE WHEN hours = 6 THEN tax_fee ELSE 0 END) AS tax_fee_06,
						SUM(CASE WHEN hours = 7 THEN tax_fee ELSE 0 END) AS tax_fee_07,
						SUM(CASE WHEN hours = 8 THEN tax_fee ELSE 0 END) AS tax_fee_08,
						SUM(CASE WHEN hours = 9 THEN tax_fee ELSE 0 END) AS tax_fee_09,
						SUM(CASE WHEN hours = 10 THEN tax_fee ELSE 0 END) AS tax_fee_10,
						SUM(CASE WHEN hours = 11 THEN tax_fee ELSE 0 END) AS tax_fee_11,
						SUM(CASE WHEN hours = 12 THEN tax_fee ELSE 0 END) AS tax_fee_12,
						SUM(CASE WHEN hours = 13 THEN tax_fee ELSE 0 END) AS tax_fee_13,
						SUM(CASE WHEN hours = 14 THEN tax_fee ELSE 0 END) AS tax_fee_14,
						SUM(CASE WHEN hours = 15 THEN tax_fee ELSE 0 END) AS tax_fee_15,
						SUM(CASE WHEN hours = 16 THEN tax_fee ELSE 0 END) AS tax_fee_16,
						SUM(CASE WHEN hours = 17 THEN tax_fee ELSE 0 END) AS tax_fee_17,
						SUM(CASE WHEN hours = 18 THEN tax_fee ELSE 0 END) AS tax_fee_18,
						SUM(CASE WHEN hours = 19 THEN tax_fee ELSE 0 END) AS tax_fee_19,
						SUM(CASE WHEN hours = 20 THEN tax_fee ELSE 0 END) AS tax_fee_20,
						SUM(CASE WHEN hours = 21 THEN tax_fee ELSE 0 END) AS tax_fee_21,
						SUM(CASE WHEN hours = 22 THEN tax_fee ELSE 0 END) AS tax_fee_22,
						SUM(CASE WHEN hours = 23 THEN tax_fee ELSE 0 END) AS tax_fee_23
					FROM base_products
					GROUP BY
						bus_date,
						region_id
						{UNIT_GROUP_BY}
				),base_products_for_json AS (
					SELECT
						SUM(gross_amount) AS gross_amount,
						SUM(net_amount) AS net_amount,
						SUM(item_count) AS item_count,
						SUM(tax_fee) AS tax_fee,
				
						JSON_BUILD_OBJECT(
                            'product_id', 0,
                            'product_code', '',
                            'product_name', '',
                            'product_category_id', 0,
                            'product_category_code', '',
                            'product_category_name', '',
                            'h00', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_00),
                                'net_amount', SUM(net_amount_00),
                                'item_count', SUM(item_count_00),
								'tax_fee', SUM(tax_fee_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_01),
                                'net_amount', SUM(net_amount_01),
                                'item_count', SUM(item_count_01),
								'tax_fee', SUM(tax_fee_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_02),
                                'net_amount', SUM(net_amount_02),
                                'item_count', SUM(item_count_02),
								'tax_fee', SUM(tax_fee_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_03),
                                'net_amount', SUM(net_amount_03),
                                'item_count', SUM(item_count_03),
								'tax_fee', SUM(tax_fee_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_04),
                                'net_amount', SUM(net_amount_04),
                                'item_count', SUM(item_count_04),
								'tax_fee', SUM(tax_fee_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_05),
                                'net_amount', SUM(net_amount_05),
                                'item_count', SUM(item_count_05),
								'tax_fee', SUM(tax_fee_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_06),
                                'net_amount', SUM(net_amount_06),
                                'item_count', SUM(item_count_06),
								'tax_fee', SUM(tax_fee_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_07),
                                'net_amount', SUM(net_amount_07),
                                'item_count', SUM(item_count_07),
								'tax_fee', SUM(tax_fee_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_08),
                                'net_amount', SUM(net_amount_08),
                                'item_count', SUM(item_count_08),
								'tax_fee', SUM(tax_fee_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_09),
                                'net_amount', SUM(net_amount_09),
                                'item_count', SUM(item_count_09),
								'tax_fee', SUM(tax_fee_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_10),
                                'net_amount', SUM(net_amount_10),
                                'item_count', SUM(item_count_10),
								'tax_fee', SUM(tax_fee_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_11),
                                'net_amount', SUM(net_amount_11),
                                'item_count', SUM(item_count_11),
								'tax_fee', SUM(tax_fee_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_12),
                                'net_amount', SUM(net_amount_12),
                                'item_count', SUM(item_count_12),
								'tax_fee', SUM(tax_fee_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_13),
                                'net_amount', SUM(net_amount_13),
                                'item_count', SUM(item_count_13),
								'tax_fee', SUM(tax_fee_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_14),
                                'net_amount', SUM(net_amount_14),
                                'item_count', SUM(item_count_14),
								'tax_fee', SUM(tax_fee_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_15),
                                'net_amount', SUM(net_amount_15),
                                'item_count', SUM(item_count_15),
								'tax_fee', SUM(tax_fee_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_16),
                                'net_amount', SUM(net_amount_16),
                                'item_count', SUM(item_count_16),
								'tax_fee', SUM(tax_fee_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_17),
                                'net_amount', SUM(net_amount_17),
                                'item_count', SUM(item_count_17),
								'tax_fee', SUM(tax_fee_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_18),
                                'net_amount', SUM(net_amount_18),
                                'item_count', SUM(item_count_18),
								'tax_fee', SUM(tax_fee_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_19),
                                'net_amount', SUM(net_amount_19),
                                'item_count', SUM(item_count_19),
								'tax_fee', SUM(tax_fee_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_20),
                                'net_amount', SUM(net_amount_20),
                                'item_count', SUM(item_count_20),
								'tax_fee', SUM(tax_fee_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_21),
                                'net_amount', SUM(net_amount_21),
                                'item_count', SUM(item_count_21),
								'tax_fee', SUM(tax_fee_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_22),
                                'net_amount', SUM(net_amount_22),
                                'item_count', SUM(item_count_22),
								'tax_fee', SUM(tax_fee_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                'weight', 0,
                                'weight_count', '',
                                'gross_amount', SUM(gross_amount_23),
                                'net_amount', SUM(net_amount_23),
                                'item_count', SUM(item_count_23),
								'tax_fee', SUM(tax_fee_23)
                            )
                        ) AS "child",
						COUNT(1) AS total --Summary时的汇总条数
					FROM base_products_for_period
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS region_code,
					'' AS region_name,
				
					SUM(gross_amount) AS gross_amount,
					SUM(net_amount) AS net_amount,
					SUM(item_count) AS item_count,
					'' AS store_type,
					0 AS business_days, --营业天数,
					'{}'::json AS data,
					JSON_AGG(child) AS child,
					SUM(tax_fee) AS tax_fee,
					SUM(total) AS total

				FROM
					base_products_for_json
		`
	}
	whereSQL := generateWhereSQLForProductPeriodSales(condition)
	regionSQL := generateRegionSQLForProductSales(condition)
	limitSQL := generateLimitOffsetSQLForProductSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	if condition.LimitByUnit {
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_SELECT}", ",coalesce(fact.unit, '') as unit")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_JOIN}", "and fact.unit = base_limit.unit")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_GROUP_BY}", ",unit")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_ORDER_BY}", ",unit")
	} else {
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_SELECT}", "")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_JOIN}", "")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_GROUP_BY}", "")
		rowSQL = strings.ReplaceAll(rowSQL, "{UNIT_ORDER_BY}", "")
	}
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	if condition.LimitByUnit {
		summarySQL = strings.ReplaceAll(summarySQL, "{UNIT_SELECT}", ",coalesce(fact.unit, '') as unit")
		summarySQL = strings.ReplaceAll(summarySQL, "{UNIT_GROUP_BY}", ",unit")
	} else {
		summarySQL = strings.ReplaceAll(summarySQL, "{UNIT_SELECT}", "")
		summarySQL = strings.ReplaceAll(summarySQL, "{UNIT_GROUP_BY}", "")
	}
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG Summary SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateWhereSQLForProductPeriodSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		priceScopeSQL         string //  价格区间筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		storeTypeSQL          string // 门店类型筛选条件
		openStatusSQL         string // 开店类型筛选条件
		periodSQL             string // 时段筛选条件
		comboSQL              string // 套餐类型筛选
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	priceScopeSQL = generatePriceScopeSQLForProductSales(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND product_caches.id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}

	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	if len(condition.Period) != 0 {
		periodSQL = fmt.Sprintf(`
			 AND extract(hour from fact.order_time) IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = %d",
			condition.ChannelId,
		)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, priceScopeSQL, storeTypeSQL, openStatusSQL, periodSQL, storeSQL, comboSQL, channelIdSQL)
	return whereSQL
}
