package repo

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"math"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

const promotionRowTpl = `
-- 促销报表 detail rows
with promotion_base as (
	select 
		fact.eticket_id, 
		fact.promotion_name, 
		fact.promotion_cate_name, 
		fact.refunded,
		sum(fact.discount_amount) as discount_amount
	from sales_discount_amounts fact
	where {WHERE}
	group by fact.eticket_id, fact.promotion_name, fact.promotion_cate_name, fact.refunded
)
select 
	coalesce(fact.promotion_name, '') as promotion_name,
	coalesce(fact.promotion_cate_name, '') as promotion_cate_name,
	sum(fact.discount_amount) as discount,
	sum(case when fact.refunded then -1 else 1 end) as usage_count
from promotion_base fact 
group by fact.promotion_name, fact.promotion_cate_name
order by discount desc, promotion_name, promotion_cate_name
`

func (rsmm *SalesRepositoryPG) PosPromotion(ctx context.Context, condition *model.RepoCondition) (*report.PosPromotionResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	rowSql := promotionSql(ctx, condition)

	rowCh := make(chan []*report.PosPromotionRow, 0)

	go promotionExecuteRow(trackId, rowSql, rsmm.DB, rowCh)

	rows := <-rowCh

	return promotionBuild(rows), nil
}

func promotionBuild(rows []*report.PosPromotionRow) *report.PosPromotionResponse {
	summary := &report.PosPromotionRow{}
	for _, row := range rows {
		summary.UsageCount += row.UsageCount
		summary.Discount += row.Discount
	}
	summary.Discount = math.Round(summary.Discount*100) / 100
	summary.DiscountPercent = 100
	sumPercent := 0.0
	for i, row := range rows {
		if summary.Discount != 0 {
			if i == len(rows)-1 {
				row.DiscountPercent = math.Round((100-sumPercent)*100) / 100
			} else {
				row.DiscountPercent = math.Round(row.Discount*10000/summary.Discount) / 100
				sumPercent += row.DiscountPercent
			}
		}
	}

	if len(rows) == 0 || summary.Discount == 0 {
		summary.DiscountPercent = 0
	}

	return &report.PosPromotionResponse{
		Rows:    rows,
		Summary: summary,
	}
}

func promotionExecuteRow(trackId int64, sql string, db *sql.DB, ch chan []*report.PosPromotionRow) {
	results := make([]*report.PosPromotionRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- results
		return
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PosPromotionRow)
		if err := r.Scan(
			&f.PromotionName,
			&f.PromotionCateName,
			&f.Discount,
			&f.UsageCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- results
			return
		}
		results = append(results, f)
	}
	ch <- results
	return
}

func promotionSql(ctx context.Context, condition *model.RepoCondition) (rowSql string) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	whereSQL := promotionWhere(condition)
	rowSql = strings.ReplaceAll(promotionRowTpl, "{WHERE}", whereSQL)
	rowSql = strings.ReplaceAll(rowSql, "{DAYS}", "1")

	logger.Pre().Debugf("[%d] repo.report.TableOrderQueryRepositoryPG row. SQL: `%s`", trackId, rowSql)
	return
}

func promotionWhere(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	if len(condition.RegionSearchIds) > 0 {
		if len(condition.RegionSearchIds) == 1 {
			regionIdsSQL = fmt.Sprintf(
				"AND fact.store_id = %d",
				condition.RegionSearchIds[0],
			)
		} else {
			regionIdsSQL = fmt.Sprintf(
				"AND fact.store_id IN (%s)",
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		}
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL)
	return whereSQL
}
