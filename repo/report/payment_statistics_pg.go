package repo

import (
	"context"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gorm.io/gorm"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) PaymentStatistics(ctx context.Context, condition *model.RepoCondition) (*report.PaymentStatisticsResponse, error) {

	rowSQL, summarySQL := generateQuerySQLForPaymentStatistics(ctx, condition)
	ch1 := make(chan []*report.PaymentStatistics, 1)
	ch2 := make(chan []*report.PaymentStatistics, 1)
	start := time.Now()
	go queryDBWithChanForPaymentStatistics(ctx, rsmm.GormDB, rowSQL, ch1)
	go queryDBWithChanForPaymentStatistics(ctx, rsmm.GormDB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.PaymentStatistics
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	var total int64
	if summary != nil {
		total = summary.Total
	}
	return &report.PaymentStatisticsResponse{Rows: rows, Summary: summary, Total: total}, nil
}

func queryDBWithChanForPaymentStatistics(ctx context.Context, db *gorm.DB, sql string, ch chan []*report.PaymentStatistics) {
	ch <- queryDBForPaymentStatistics(ctx, db, sql)
}

func queryDBForPaymentStatistics(ctx context.Context, db *gorm.DB, s string) []*report.PaymentStatistics {
	results := make([]*report.PaymentStatistics, 0)
	if s == "" {
		return []*report.PaymentStatistics{new(report.PaymentStatistics)}
	}
	q := db.Raw(s).Scan(&results)
	if q.Error != nil {
		logger.Pre().WithContext(ctx).Errorf("pkg.helpers.QueryDB. Err: `%v`", q.Error)
		return results
	}
	return results
}

func generateQuerySQLForPaymentStatistics(ctx context.Context, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 查询ros
		summarySQL string // 查询summary
	)

	rowSQL = `
		--支付统计详细信息rows
			WITH  base_limit AS ( --limit
					SELECT
						fact.bus_date AS bus_date,
						{REGION_ID} AS region_id,
						fact.payment_id as payment_id
						
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN  payment_caches 
								ON fact.payment_id=payment_caches.id
					WHERE
						{WHERE}
						AND fact.is_non_sales=false
					GROUP BY
						fact.bus_date,
						{REGION_ID},fact.payment_id 
					ORDER BY
						fact.bus_date DESC,
						{REGION_ID}
					{LIMIT} 
				), base_payments AS (
					SELECT
						fact.bus_date AS bus_date,  --营业日期
						COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id, -- 管理区域
						COALESCE({REGION_ID}, 0) AS region_id, 
						cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
						COALESCE(payment_caches.cooperation_code,'') AS cooperation_code, -- 支付供应商
						COALESCE(payment_caches.payment_category_id,0) AS payment_category_id,-- 支付类型ID
						COALESCE(payment_caches.payment_category_name,'') AS payment_category_name,-- 支付类型名称
						COALESCE(payment_caches.payment_category_code,'') AS payment_category_code,-- 支付类型编码
						COALESCE(fact.payment_id, 0) AS payment_id,--支付
						COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,--支付编号
						COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,--支付名称
						COALESCE(SUM(CASE WHEN refunded  THEN 0 else pay_amount END ), 0) AS pay_amount, --实付金额
						COALESCE(SUM(CASE WHEN fact.finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0)::float AS transfer_real_amount, --财务实收金额(转换后)
						COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
						
						COALESCE(SUM(CASE WHEN refunded  THEN pay_amount else 0 END ), 0) AS pay_amount_returned, --实付金额(退)
						COALESCE(SUM( CASE WHEN refunded  THEN (CASE WHEN fact.finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END)  ELSE 0 END), 0)::float AS transfer_real_amount_returned, --财务实收金额(转换后)(退）
						COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned, --支付退单数
						0  AS deposit_amount,
						0  AS deposit_item_count,
						Sum( ABS(COALESCE(qty,0))) as  all_item_count

					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
							INNER JOIN base_limit
								ON fact.bus_date = base_limit.bus_date
									AND {REGION_ID} = base_limit.region_id
									AND fact.payment_id = base_limit.payment_id

							LEFT JOIN  payment_caches 
								ON fact.payment_id=payment_caches.id
					WHERE
						{WHERE}
					AND fact.is_non_sales=false
					GROUP BY
						fact.bus_date, {REGION_ID}, fact.channel_id,fact.payment_id,payment_caches.cooperation_code, payment_caches.payment_category_id, payment_caches.payment_category_name, payment_caches.payment_category_code

					ORDER BY
						fact.bus_date DESC,
						{REGION_ID}
				)
			SELECT * FROM  base_payments  as  bt
				ORDER BY bt.bus_date DESC, bt.region_id;
 		`
	summarySQL = `
		--支付统计详细信息summary
					WITH  base_limit AS ( --limit
					SELECT
						fact.bus_date AS bus_date,
						{REGION_ID} AS region_id,
						fact.payment_id as payment_id
						
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
							LEFT JOIN  payment_caches 
								ON fact.payment_id=payment_caches.id
					WHERE
						{WHERE}
						AND  fact.is_non_sales=false
					GROUP BY
						fact.bus_date,
						{REGION_ID},fact.payment_id 
				), base_payments AS (
					SELECT
						fact.bus_date AS bus_date,  --营业日期
						COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id, -- 管理区域
						COALESCE({REGION_ID}, 0) AS region_id, 
						cast(coalesce(nullif(fact.channel_id,''),'0') as bigint) AS channel_id, --渠道
						COALESCE(payment_caches.cooperation_code,'') AS cooperation_code, -- 支付供应商
						COALESCE(payment_caches.payment_category_id,0) AS payment_category_id,-- 支付类型ID
						COALESCE(payment_caches.payment_category_name,'') AS payment_category_name,-- 支付类型名称
						COALESCE(payment_caches.payment_category_code,'') AS payment_category_code,-- 支付类型编码
						COALESCE(fact.payment_id, 0) AS payment_id,--支付
						COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,--支付编号
						COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name,--支付名称
						COALESCE(SUM(CASE WHEN refunded  THEN 0 else pay_amount END ), 0) AS pay_amount, --实付金额
						COALESCE(SUM(CASE WHEN fact.finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0)::float AS transfer_real_amount, --财务实收金额(转换后)
						COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
						
						COALESCE(SUM(CASE WHEN refunded  THEN pay_amount else 0 END ), 0) AS pay_amount_returned, --实付金额(退)
						COALESCE(SUM( CASE WHEN refunded  THEN (CASE WHEN fact.finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END)  ELSE 0 END), 0)::float AS transfer_real_amount_returned, --财务实收金额(转换后)(退）
						COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned, --支付退单数
						0  AS deposit_amount,
						0  AS deposit_item_count,
						Sum( ABS(COALESCE(qty,0))) as  all_item_count

					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches store_caches
								ON fact.store_id = store_caches.id
							INNER JOIN base_limit
								ON fact.bus_date = base_limit.bus_date
									AND {REGION_ID} = base_limit.region_id
									AND fact.payment_id = base_limit.payment_id
							LEFT JOIN  payment_caches 
								ON fact.payment_id=payment_caches.id
					WHERE
						{WHERE}
						AND fact.is_non_sales=false
					GROUP BY
						fact.bus_date, {REGION_ID}, fact.channel_id,fact.payment_id,payment_caches.cooperation_code, payment_caches.payment_category_id, payment_caches.payment_category_name, payment_caches.payment_category_code
				)
			SELECT 	sum(bt.pay_amount) as pay_amount, 
					sum(bt.transfer_real_amount) as transfer_real_amount,
					sum(bt.item_count) as item_count,
					sum(bt.pay_amount_returned) as pay_amount_returned,
					sum(bt.transfer_real_amount_returned) as transfer_real_amount_returned,
					sum(bt.item_count_returned) as item_count_returned,
					sum(bt.deposit_amount) as deposit_amount,
					sum(bt.deposit_item_count) as  deposit_item_count,
					sum(bt.all_item_count) as all_item_count,
					count(*) as total
			FROM  base_payments  as  bt
 		`

	whereSQL := generateWhereSQLForPaymentStatistics(condition)
	regionSQL := generateRegionSQLForPaymentStatistics(condition)
	limitSQL := generateLimitOffsetSQLForPaymentStatistics(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().WithContext(ctx).Debugf("repo.report.SalesRepositoryPG row. SQL: `%s`", rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().WithContext(ctx).Debugf("repo.report.SalesRepositoryPG summary. SQL: `%s`", summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForPaymentStatistics(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForPaymentStatistics(condition *model.RepoCondition) string {
	var (
		regionIdsSQL        string // 区域筛选条件
		paymentMethodIdsSQL string // 支付方式筛选条件
		channelSQL          string // 渠道筛选条件
		storeTypeSQL        string // 增加门店类型的过滤
		openStatusSQL       string // 增加开店类型的过滤
		orderStatusSQL      string // 订单状态
	)

	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.PaymentIds) > 0 {
		paymentMethodIdsSQL = fmt.Sprintf(
			"AND fact.payment_id IN (%s)",
			helpers.JoinInt64(condition.PaymentIds, ","),
		)
	}
	if condition.ChannelId != 0 {
		channelSQL = fmt.Sprintf(
			"AND fact.channel_id = '%d'",
			condition.ChannelId)
	}
	var storeSQL string                                                       // 门店过滤
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}

	if condition.OrderStatus != "" {
		orderStatusSQL = fmt.Sprintf(`
				AND fact.order_status = '%s'
			`, condition.OrderStatus)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentMethodIdsSQL, channelSQL, storeTypeSQL, openStatusSQL, storeSQL, orderStatusSQL)
	return whereSQL
}

func generateLimitOffsetSQLForPaymentStatistics(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
