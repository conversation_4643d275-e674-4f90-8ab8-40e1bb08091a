package repo

import (
	"context"
	"fmt"
	"sync"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) Dashboard(ctx context.Context, condition *model.RepoDashboardCondition) (
	*model.DashboardResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	funcMaps := map[string]func(trackId int64, condition *model.RepoDashboardCondition) string{
		"summary":      generateQuerySQLForDashboardWithSummary,
		"line_chart":   generateQuerySQLForDashboardWithLineChart,
		"store_rank":   generateQuerySQLForDashboardWithStoreRank,
		"channel_rank": generateQuerySQLForDashboardWithChannelRank,
		"product_rank": generateQuerySQLForDashboardWithProductRank,
	}
	resultMaps := sync.Map{}
	wg := sync.WaitGroup{}
	for _, item := range condition.Panel {
		if f, ok := funcMaps[item]; ok {
			sql := f(trackId, condition)
			wg.Add(1)
			go func(sql, item string) {
				resultMaps.Store(item, helpers.QueryDB(trackId, rsmm.DB, sql))
				wg.Done()
			}(sql, item)
		}
	}
	wg.Wait()

	resp := &model.DashboardResponse{}
	if r, ok := resultMaps.Load("summary"); ok {
		resp.Summary = cast.ToStringMap(cast.ToSlice(r)[0])
	}
	if r, ok := resultMaps.Load("line_chart"); ok {
		resp.LineChart = cast.ToStringMap(cast.ToSlice(r)[0])
	}
	if r, ok := resultMaps.Load("store_rank"); ok {
		resp.StoreRank = cast.ToSlice(r)
	}
	if r, ok := resultMaps.Load("channel_rank"); ok {
		resp.ChannelRank = cast.ToSlice(r)
	}
	if r, ok := resultMaps.Load("product_rank"); ok {
		resp.ProductRank = cast.ToSlice(r)
	}
	return resp, nil
}

func generateQuerySQLForDashboardWithSummary(trackId int64, condition *model.RepoDashboardCondition) string {
	whereSQL := generateWhereSQLForDashboard(condition)
	summarySQL := fmt.Sprintf(`
		SELECT
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(qty) AS item_count,
			SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
			sum(eticket_count) AS order_count
		FROM
			sales_product_amounts
		WHERE
			%s
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return summarySQL
}

func generateQuerySQLForDashboardWithLineChart(trackId int64, condition *model.RepoDashboardCondition) string {
	whereSQL := generateWhereSQLForDashboard(condition)
	lineChartSQL := fmt.Sprintf(`
		SELECT
			SUM(net_amount_00) AS net_amount_00,
			SUM(net_amount_01) AS net_amount_01,
			SUM(net_amount_02) AS net_amount_02,
			SUM(net_amount_03) AS net_amount_03,
			SUM(net_amount_04) AS net_amount_04,
			SUM(net_amount_05) AS net_amount_05,
			SUM(net_amount_06) AS net_amount_06,
			SUM(net_amount_07) AS net_amount_07,
			SUM(net_amount_08) AS net_amount_08,
			SUM(net_amount_09) AS net_amount_09,
			SUM(net_amount_10) AS net_amount_10,
			SUM(net_amount_11) AS net_amount_11,
			SUM(net_amount_12) AS net_amount_12,
			SUM(net_amount_13) AS net_amount_13,
			SUM(net_amount_14) AS net_amount_14,
			SUM(net_amount_15) AS net_amount_15,
			SUM(net_amount_16) AS net_amount_16,
			SUM(net_amount_17) AS net_amount_17,
			SUM(net_amount_18) AS net_amount_18,
			SUM(net_amount_19) AS net_amount_19,
			SUM(net_amount_20) AS net_amount_20,
			SUM(net_amount_21) AS net_amount_21,
			SUM(net_amount_22) AS net_amount_22,
			SUM(net_amount_23) AS net_amount_23,
			SUM(order_count_00) AS order_count_00,
			SUM(order_count_01) AS order_count_01,
			SUM(order_count_02) AS order_count_02,
			SUM(order_count_03) AS order_count_03,
			SUM(order_count_04) AS order_count_04,
			SUM(order_count_05) AS order_count_05,
			SUM(order_count_06) AS order_count_06,
			SUM(order_count_07) AS order_count_07,
			SUM(order_count_08) AS order_count_08,
			SUM(order_count_09) AS order_count_09,
			SUM(order_count_10) AS order_count_10,
			SUM(order_count_11) AS order_count_11,
			SUM(order_count_12) AS order_count_12,
			SUM(order_count_13) AS order_count_13,
			SUM(order_count_14) AS order_count_14,
			SUM(order_count_15) AS order_count_15,
			SUM(order_count_16) AS order_count_16,
			SUM(order_count_17) AS order_count_17,
			SUM(order_count_18) AS order_count_18,
			SUM(order_count_19) AS order_count_19,
			SUM(order_count_20) AS order_count_20,
			SUM(order_count_21) AS order_count_21,
			SUM(order_count_22) AS order_count_22,
			SUM(order_count_23) AS order_count_23
		FROM
			payment_period_sales
		WHERE
			%s
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG lineChart. SQL: `%s`", trackId, lineChartSQL)
	return lineChartSQL
}

func generateQuerySQLForDashboardWithStoreRank(trackId int64, condition *model.RepoDashboardCondition) string {
	whereSQL := generateWhereSQLForDashboard(condition)
	storeRankSQL := fmt.Sprintf(`
		SELECT
			store_id,
			SUM(net_amount) AS net_amount,
			sum(order_count) AS order_count
		FROM
			store_sales
		WHERE
			%s
		GROUP BY
			store_id
		ORDER BY
			SUM(net_amount) DESC
		LIMIT 5
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG storeRank. SQL: `%s`", trackId, storeRankSQL)
	return storeRankSQL
}

func generateQuerySQLForDashboardWithChannelRank(trackId int64, condition *model.RepoDashboardCondition) string {
	whereSQL := generateWhereSQLForDashboard(condition)
	channelRankSQL := fmt.Sprintf(`
		SELECT
			channel_id,
			SUM(net_amount) AS net_amount,
			sum(order_count) AS order_count
		FROM
			store_channel_sales
		WHERE
			%s
		GROUP BY
			channel_id
		ORDER BY
			SUM(net_amount) DESC
		LIMIT 5
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG channelRank. SQL: `%s`", trackId, channelRankSQL)
	return channelRankSQL
}

func generateQuerySQLForDashboardWithProductRank(trackId int64, condition *model.RepoDashboardCondition) string {
	whereSQL := generateWhereSQLForDashboard(condition)
	productRankSQL := fmt.Sprintf(`
		SELECT
			product_id,
			SUM(net_amount) AS net_amount,
			sum(item_count) AS item_count
		FROM
			product_sales
		WHERE
			%s
		GROUP BY
			product_id
		ORDER BY
			SUM(net_amount) DESC
		LIMIT 5
	`, whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG productRank. SQL: `%s`", trackId, productRankSQL)
	return productRankSQL
}

func generateWhereSQLForDashboard(condition *model.RepoDashboardCondition) string {
	regionIdsSQL := ""
	if len(condition.RegionSearchIds) > 0 {
		regionIdsSQL = fmt.Sprintf(
			"AND store_id IN (%s)",
			helpers.JoinInt64(condition.RegionSearchIds, ","),
		)
	}
	whereSQL := fmt.Sprintf(`
		partner_id = %d
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL)
	return whereSQL
}
