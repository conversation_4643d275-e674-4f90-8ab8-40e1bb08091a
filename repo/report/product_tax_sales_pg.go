package repo

import (
    "context"
    "database/sql"
    "fmt"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "strings"
    "time"

    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model/report"
)

func (rsmm *SalesRepositoryPG) ProductTaxSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductTaxSalesResponse, error) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))
    rowSQL, totalSQL, sendSQL, packageSQL := generateQuerySQLForProductTaxSales(trackId, condition)
    ch1 := make(chan []*report.ProductTaxSales, 1)
    ch2 := make(chan []*report.ProductTaxSales, 1)
    ch3 := make(chan []*report.ProductTaxSales, 1)
    ch4 := make(chan []*report.ProductTaxSales, 1)
    start := time.Now()
    go queryDBWithChanForProductTaxSales(trackId, rsmm.DB, rowSQL, ch1)
    go queryDBWithChanForProductTaxSales(trackId, rsmm.DB, totalSQL, ch2)
    go queryDBWithChanForProductTaxSales(trackId, rsmm.DB, sendSQL, ch3)
    go queryDBWithChanForProductTaxSales(trackId, rsmm.DB, packageSQL, ch4)
    rows := <-ch1
    summarys := <-ch2
    sendFees := <-ch3
    packageFees := <-ch4
    close(ch1)
    close(ch2)
    close(ch3)
    close(ch4)
    logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
    var summary *report.ProductTaxSales
    if len(summarys) > 0 {
        summary = summarys[0]
    }
    response := &report.ProductTaxSalesResponse{
        Summary:     summary,
        Rows:        rows,
        SendFees:    sendFees,
        PackageFees: packageFees,
    }
    if summary != nil {
        response.Total = summary.Total
    }
    return response, nil
}

// 商品属性销售
func queryDBWithChanForProductTaxSales(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductTaxSales) {
    ch <- queryDBForProductTaxSales(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为struct对象
func queryDBForProductTaxSales(trackId int64, db *sql.DB, sqlStr string) []*report.ProductTaxSales {
    results := make([]*report.ProductTaxSales, 0)
    if sqlStr == "" {
        return []*report.ProductTaxSales{new(report.ProductTaxSales)}
    }
    r, err := db.Query(sqlStr)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        return results
    }
    defer r.Close()

    for r.Next() {
        f := new(report.ProductTaxSales)
        if err := r.Scan(
            &f.StoreId,
            &f.CompanyId,
            &f.StoreType,
            &f.BusinessDays,
            &f.CategoryId,
            &f.ProductId,
            &f.ChannelId,
            &f.ProductCount,
            &f.GrossAmount,
            &f.DiscountAmount,
            &f.FinanceRealAmount,
            &f.TransferAmount,
            &f.DiscountTransferAmount,
            &f.ProductTax,
            &f.InvoiceAmount,
            &f.Total,
        ); err != nil {
            logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
            return results
        }
        results = append(results, f)
    }
    return results
}

func generateQuerySQLForProductTaxSales(trackId int64, condition *model.RepoCondition) (string, string, string, string) {
    var (
        rowSQL        string
        summarySQL    string
        sendFeeSQL    string
        packageFeeSQL string
    )
    sendFeeSQL = `
        --商品计税表 sendFeeSQL
		with business_days as (
			SELECT
				store_id, COUNT(1) AS days
			FROM (
					SELECT
						 store_id
					FROM
						sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
					WHERE
						{WHERESEND}
					GROUP BY store_id, bus_date
					) T
			GROUP BY store_id
			)
		select
			coalesce(fact.store_id, 0) as store_id, -- 门店id
			coalesce(max(store_caches.company_info), 0) as company_id, --公司名称
			coalesce(max(store_caches.store_type), '') as store_type, --门店类型
			bd.days as business_days,
			0 as category, --类目
			0 as product_id, --商品id
			coalesce(fact.channel_id, 0) as channel_id, --渠道id
			0 as product_count, --商品数量
			0 as gross_amount, --商品流水
			0 as discount_amount, --商品折扣
			0 as finance_real_amount, --财务实收
			0 as transfer_amount, --支付转折扣
			0 as discount_transfer_amount, --折扣转支付
			send_fee_rate as product_tax, --商品税率
			coalesce(sum(fact.delivery_fee), 0) as invoice_amount, --开票金额
			0 as total
		from sales_ticket_amounts fact
		LEFT JOIN store_caches ON fact.store_id = store_caches.id
		left join business_days bd on fact.store_id = bd.store_id
		where
			{WHERESEND}
		group by fact.store_id,fact.channel_id, send_fee_rate, bd.days
		{LIMIT};
	`

    packageFeeSQL = `
        --商品计税表 packageFeeSQL
		with business_days as (
			SELECT
				store_id, COUNT(1) AS days
			FROM (
					SELECT
						 store_id
					FROM
						sales_ticket_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
					WHERE
						{WHERESEND}
					GROUP BY store_id, bus_date
					) T
			GROUP BY store_id
			)
		select
			coalesce(fact.store_id, 0) as store_id, -- 门店id
			coalesce(max(store_caches.company_info), 0) as company_id, --公司名称
			coalesce(max(store_caches.store_type), '') as store_type, --门店类型
			bd.days as business_days,
			0 as category, --类目
			0 as product_id, --商品id
			coalesce(fact.channel_id, 0) as channel_id, --渠道id
			0 as product_count, --商品数量
			0 as gross_amount, --商品流水
			0 as discount_amount, --商品折扣
			0 as finance_real_amount, --财务实收
			0 as transfer_amount, --支付转折扣
			0 as discount_transfer_amount, --折扣转支付
			package_fee_rate as product_tax, --商品税率
			coalesce(sum(fact.package_fee), 0) as invoice_amount, --开票金额
			0 as total
		from sales_ticket_amounts fact
		LEFT JOIN store_caches ON fact.store_id = store_caches.id
		left join business_days bd on fact.store_id = bd.store_id
		where
			{WHERESEND}
		group by fact.store_id,fact.channel_id, package_fee_rate, bd.days
		{LIMIT};
`
    rowSQL = `
        --商品计税表 row
		with business_days as (
			SELECT
				store_id, COUNT(1) AS days
			FROM (
					 SELECT
						 store_id
					 FROM
						 sales_product_amounts fact
							 LEFT JOIN store_caches
									   ON fact.store_id = store_caches.id
							 LEFT JOIN product_caches
									   ON fact.product_id = product_caches.id
					 WHERE
						 {WHERE}
					 GROUP BY store_id, bus_date
				 ) T
			GROUP BY store_id
		), real_amounts as (
			select
				eticket_id,
				amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) as real_amount
			from sales_ticket_amounts fact
			where {TICKET_WHERE}
		),
         base_share as (
             select fact.*,
                    row_number() over (partition by fact.eticket_id order by fact.id) rn
             from sales_product_amounts fact
                      LEFT JOIN product_caches ON fact.product_id = product_caches.id
             where {SHARE_WHERE}
               and fact.gross_amount != 0
         ),
         sum_base as (
             select
                 eticket_id,
                 sum(base_share.gross_amount) sum_all
             from base_share
             group by eticket_id
         ),
         share_amt as (
             select b.*, round((ra.real_amount*b.gross_amount/sb.sum_all)::numeric, 2) as share_amount
             from base_share b inner join sum_base sb on b.eticket_id = sb.eticket_id and sb.sum_all != 0
                               inner join real_amounts ra on ra.eticket_id = b.eticket_id
         ),
         share_sum as (
             select eticket_id,
                    round(sum(case when rn = 1 then 0 else share_amount end)::numeric,2) share_amount_sum_exclude_first
             from share_amt
             group by eticket_id
         ),
         final_share as (
             select s1.*,
                    (case when s1.rn = 1 then round((ra.real_amount - s2.share_amount_sum_exclude_first)::numeric, 2) else s1.share_amount end) as final_share_amount
             from share_amt s1 left join share_sum s2 on s1.eticket_id = s2.eticket_id
                               inner join real_amounts ra on s1.eticket_id = ra.eticket_id
         )
		select
			store_id,
			company_id,
			store_type,
			business_days,
			category,
			product_id,
			channel_id,
			product_count,
			gross_amount,
			discount_amount,
			finance_real_amount,
			transfer_amount,
			discount_transfer_amount,
			product_tax,
			invoice_amount,
			total
		from    (
					select
						coalesce(fact.store_id, 0) as store_id, -- 门店id
						coalesce(store_caches.company_info, 0) as company_id, --公司名称
						coalesce(max(store_caches.store_type), '') as store_type, --门店类型
						coalesce(bd.days, 0) as business_days, --营业天数
						coalesce(product_caches.category, 0) as category, --类目
						coalesce(fact.product_id, 0) as product_id, --商品id
						coalesce(fact.channel_id, 0) as channel_id, --渠道id
						coalesce(sum(fact.qty), 0) as product_count, --商品数量
						coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
						coalesce(sum(fact.discount_amount), 0) as discount_amount, --商品折扣
						coalesce(sum(final_share.final_share_amount), 0) as finance_real_amount, --财务实收
						coalesce(sum(fact.transfer_amount), 0) as transfer_amount, --支付转折扣
						coalesce(sum(fact.discount_transfer_amount), 0) as discount_transfer_amount, --折扣转支付
						coalesce(fact.product_tax, 0) as product_tax, --商品税率
						coalesce(sum(fact.invoice_amount), 0) as invoice_amount, --开票金额
						0 as total
					from sales_product_amounts fact
                             left join final_share on fact.id = final_share.id
							 left join store_caches on fact.store_id=store_caches.id
							 left join product_caches on fact.product_id=product_caches.id
							 left join business_days bd on bd.store_id = fact.store_id
					where
						{WHERE}
					group by fact.store_id, store_caches.company_info, bd.days, product_caches.category, fact.product_id, fact.channel_id, fact.product_tax
					order by fact.store_id, fact.product_id, fact.channel_id, fact.product_tax desc
				)T
		where product_count != 0 or gross_amount != 0 or discount_amount != 0 or finance_real_amount != 0 or transfer_amount != 0 or invoice_amount != 0
			{LIMIT}
		;
	`

    summarySQL = `
        --商品计税表summary
        with base_product as (
            select
                product_count,
                gross_amount,
                discount_amount,
                finance_real_amount,
                transfer_amount,
                discount_transfer_amount,
                invoice_amount
            from (
                     select
                         coalesce(sum(fact.qty), 0) as product_count, --商品数量
                         coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
                         coalesce(sum(fact.discount_amount), 0) as discount_amount, --商品折扣
                         coalesce(sum(fact.finance_real_amount), 0) as finance_real_amount, --财务实收
                         coalesce(sum(fact.transfer_amount), 0) as transfer_amount, --支付转折扣
                         coalesce(sum(fact.discount_transfer_amount), 0) as discount_transfer_amount, --折扣转支付
                         coalesce(sum(fact.invoice_amount), 0) as invoice_amount --开票金额
                     from sales_product_amounts fact
                              left join store_caches on fact.store_id=store_caches.id
                              left join product_caches on fact.product_id=product_caches.id
                     where
                         {WHERE}
                     group by fact.store_id, store_caches.company_info, product_caches.category, fact.product_id, fact.channel_id, fact.product_tax
                 )T
            where
                    product_count != 0 or gross_amount != 0 or discount_amount != 0 or finance_real_amount != 0 or transfer_amount != 0 or invoice_amount != 0
        ), base_ticket as (
            select
                coalesce(sum(fact.package_fee), 0) as package_fee, --开票金额
                coalesce(sum(fact.send_fee), 0) as send_fee, --开票金额
                SUM(fact.amount_0-(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount)) AS real_amount --实收金额（实收组成）
            from sales_ticket_amounts fact
                     LEFT JOIN store_caches ON fact.store_id = store_caches.id
            where
                {WHERESEND}
        ),
        real_amounts as (
			select
				eticket_id,
				amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) as real_amount
			from sales_ticket_amounts fact
			where {TICKET_WHERE}
		),
         base_share as (
             select fact.*,
                    row_number() over (partition by fact.eticket_id order by fact.id) rn
             from sales_product_amounts fact
                    LEFT JOIN product_caches ON fact.product_id = product_caches.id
             where {SHARE_WHERE}
               and fact.gross_amount != 0
         ),
         sum_base as (
             select
                 eticket_id,
                 sum(base_share.gross_amount) sum_all
             from base_share
             group by eticket_id
         ),
         share_amt as (
             select b.*, round((ra.real_amount*b.gross_amount/sb.sum_all)::numeric, 2) as share_amount
             from base_share b inner join sum_base sb on b.eticket_id = sb.eticket_id and sb.sum_all != 0
                               inner join real_amounts ra on ra.eticket_id = b.eticket_id
         ),
         share_sum as (
             select eticket_id,
                    round(sum(case when rn = 1 then 0 else share_amount end)::numeric,2) share_amount_sum_exclude_first
             from share_amt
             group by eticket_id
         ),
		 final_share_sum as (
             select
					sum(case when fact.rn = 1 then round((ra.real_amount - s2.share_amount_sum_exclude_first)::numeric, 2) else fact.share_amount end) as final_share_amount
			 from share_amt fact left join share_sum s2 on fact.eticket_id = s2.eticket_id
							   inner join real_amounts ra on fact.eticket_id = ra.eticket_id
                               LEFT JOIN product_caches ON fact.product_id = product_caches.id
             where {WHERE}
		 )
        select
            0 as store_id, -- 门店id
            0 as company_id, --公司名称
            '' as store_type, --门店类型
            0 as business_days,
            0 as category, --类目
            0 as product_id, --商品id
            0 as channel_id, --渠道id
            coalesce(sum(product_count), 0) as product_count, --商品数量
            coalesce(sum(gross_amount), 0) as gross_amount, --商品流水
            coalesce(sum(discount_amount), 0) as discount_amount, --商品折扣
            coalesce(max(fss.final_share_amount), 0) as finance_real_amount, --财务实收
            coalesce(sum(transfer_amount), 0) as transfer_amount, --支付转折扣
            coalesce(sum(discount_transfer_amount), 0) as discount_transfer_amount, --折扣转支付
            0 as product_tax,
            coalesce(sum(invoice_amount)+max(bt.package_fee)+max(bt.send_fee), 0) as invoice_amount, --开票金额
            count(1) as total
        from base_product, base_ticket bt, final_share_sum fss

	`
    whereSQL, whereSendSQL, ticketWhereSQL, shareWhereSQL := generateWhereSQLForProductTaxSales(condition)
    limitSQL := generateLimitOffsetSQLForProductTaxSales(condition)
    rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
    rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
    rowSQL = strings.ReplaceAll(rowSQL, "{TICKET_WHERE}", ticketWhereSQL)
    rowSQL = strings.ReplaceAll(rowSQL, "{SHARE_WHERE}", shareWhereSQL)
    logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

    sendFeeSQL = strings.ReplaceAll(sendFeeSQL, "{WHERESEND}", whereSendSQL)
    sendFeeSQL = strings.ReplaceAll(sendFeeSQL, "{LIMIT}", limitSQL)
    logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, sendFeeSQL)

    packageFeeSQL = strings.ReplaceAll(packageFeeSQL, "{WHERESEND}", whereSendSQL)
    packageFeeSQL = strings.ReplaceAll(packageFeeSQL, "{LIMIT}", limitSQL)
    logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, packageFeeSQL)

    summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
    summarySQL = strings.ReplaceAll(summarySQL, "{WHERESEND}", whereSendSQL)
    summarySQL = strings.ReplaceAll(summarySQL, "{TICKET_WHERE}", ticketWhereSQL)
    summarySQL = strings.ReplaceAll(summarySQL, "{SHARE_WHERE}", shareWhereSQL)
    logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summay. SQL: `%s`", trackId, summarySQL)
    return rowSQL, summarySQL, sendFeeSQL, packageFeeSQL
}

func generateWhereSQLForProductTaxSales(condition *model.RepoCondition) (string, string, string, string) {
    var (
        productIdsSQL         string // 商品筛选条件
        comboSQL              string // 套餐商品过滤条件
        taxSQL                string // 商品税率
        channelIdSQL          string // 渠道筛选条件
        productCategoryIdsSQL string // 类别筛选条件
    )
    if len(condition.ProductCategoryIds) > 0 {
        productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
            helpers.JoinInt64(condition.ProductCategoryIds, ","),
        )
    }
    if len(condition.ChannelIds) > 0 {
        channelIdSQL = fmt.Sprintf(
            "AND fact.channel_id IN (%s)",
            helpers.JoinInt64(condition.ChannelIds, ","),
        )
    }
    // 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
    if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
        comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
    } else { // 不统计套餐时，仅统计非套餐商品和套餐子项
        comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
    }
    if len(condition.Taxes) > 0 {
        taxSQL = fmt.Sprintf(
            "AND fact.product_tax IN (%s)",
            helpers.JoinFloat64(condition.Taxes, ","),
        )
    }

    if len(condition.ProductIds) > 0 {
        if len(condition.ProductIds) == 1 {
            productIdsSQL = fmt.Sprintf(
                "AND fact.product_id = %d",
                condition.ProductIds[0],
            )
        } else {
            productIdsSQL = fmt.Sprintf(
                "AND fact.product_id IN (%s)",
                helpers.JoinInt64(condition.ProductIds, ","),
            )
        }
    }
    storeSQL := ""
    if len(condition.RegionSearchIds) > 0 {
        storeSQL = fmt.Sprintf(`
			AND fact.store_id IN (%s)
			`, helpers.JoinInt64(condition.RegionSearchIds, ","))
    }

    busDateSQL := ""
    if !condition.IsNatural { // 营业日
        if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
            busDateSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
		`, condition.Start.Format("2006-01-02"))
        } else {
            busDateSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
		`, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"))
        }
    } else {
        if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
            busDateSQL = fmt.Sprintf(`
				AND fact.natural_date = '%s'
		`, condition.Start.Format("2006-01-02"))
        } else {
            busDateSQL = fmt.Sprintf(`
				AND fact.natural_date >= '%s'
				AND fact.natural_date <= '%s'
		`, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"))
        }
    }

    whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, busDateSQL, productIdsSQL, storeSQL, comboSQL, taxSQL, channelIdSQL, productCategoryIdsSQL)

    var whereSendSQL string
    if len(condition.ProductIds) > 0 || len(condition.ProductCategoryIds) > 0 || len(condition.Taxes) > 0 {
        whereSendSQL = `
			fact.id = 0
		`
    } else {
        whereSendSQL = fmt.Sprintf(`
			fact.partner_id = %d 
			AND fact.scope_id = %d
			%s
			%s
			%s
		`, condition.PartnerId, condition.ScopeId, busDateSQL, storeSQL, channelIdSQL)
    }

    ticketWhereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		%s
		%s
		%s
	`, condition.PartnerId, busDateSQL, storeSQL, channelIdSQL)

    shareWhereSQL := fmt.Sprintf(`
		%s
		%s
	`, ticketWhereSQL, comboSQL)

    return whereSQL, whereSendSQL, ticketWhereSQL, shareWhereSQL
}

func generateLimitOffsetSQLForProductTaxSales(condition *model.RepoCondition) string {
    limitOffsetSQL := ""
    if condition.Limit == -1 {
        return limitOffsetSQL
    }
    if condition.Limit < 0 {
        condition.Limit = 10
    }
    if condition.Offset < 0 {
        condition.Offset = 0
    }
    limitOffsetSQL = fmt.Sprintf(
        "LIMIT %d OFFSET %d",
        condition.Limit, condition.Offset,
    )
    return limitOffsetSQL
}
