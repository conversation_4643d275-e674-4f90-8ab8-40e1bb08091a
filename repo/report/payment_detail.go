package repo

import (
	"context"
	"database/sql"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (r *SalesRepositoryPG) PaymentDetail(ctx context.Context, condition *model.RepoCondition) (*report.PaymentDetailResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, totalSQL := generateQuerySQLForPaymentDetail(trackId, condition)
	ch1 := make(chan []*report.PaymentDetail, 1)
	ch2 := make(chan int64, 1)
	start := time.Now()
	go queryDBWithChanForPaymentDetail(trackId, r.DB, rowSQL, ch1)
	go queryDBWithChanForPaymentDetailTotal(trackId, r.DB, totalSQL, ch2)
	rows := <-ch1
	total := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())

	return &report.PaymentDetailResponse{Rows: rows, Total: total}, nil
}

func queryDBWithChanForPaymentDetail(trackId int64, db *sql.DB, sql string, ch chan []*report.PaymentDetail) {
	ch <- queryDBForPaymentDetail(trackId, db, sql)
}

func queryDBWithChanForPaymentDetailTotal(trackId int64, db *sql.DB, sql string, ch chan int64) {
	ch <- queryDBForPaymentDetailTotal(trackId, db, sql)
}

func queryDBForPaymentDetail(trackId int64, db *sql.DB, s string) []*report.PaymentDetail {
	results := make([]*report.PaymentDetail, 0)
	if s == "" {
		return []*report.PaymentDetail{new(report.PaymentDetail)}
	}
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PaymentDetail)
		if err := r.Scan(
			&f.BusDate,
			&f.StoreId,
			&f.StoreCode,
			&f.StoreName,
			&f.OperatorCode,
			&f.TicketNo,
			&f.PaymentId,
			&f.PaymentCode,
			&f.PaymentName,
			&f.PhoneNo,
			&f.PlateNo,
			&f.PayTime,
			&f.PayAmount,
			&f.ServiceFee,
			&f.PaymentTransferAmount,
			&f.SurChargeAmount,
			&f.CardNo,
			&f.RealAmount,
			&f.NonSalesAmount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForPaymentDetail(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string // 查询ros
		totalSQL string // 查询总数
	)

	rowSQL = `
		--支付明细rows
					SELECT
						fact.bus_date,
						fact.store_id,
						store_caches.store_code,
						store_caches.store_name,
						coalesce(t.operator_code,'') AS operator_name ,	--员工号
						coalesce(t.real_ticket_no,'') AS ticket_no ,
						fact.payment_id,
						fact.payment_code,--支付编号
						fact.payment_name,--支付名称
						coalesce(t.phone_no,'') AS phone_no , --帐单号码
						coalesce(t.plate_no,'') AS plate_no , --外送号码（飞盘号）
						coalesce(fact.pay_time,fact.order_time) AS pay_time, --支付时间
						fact.pay_amount-fact.merchant_allowance, --实付金额
						CASE 
						WHEN fact.is_non_sales = true THEN 0
						WHEN t.amount_4 = 0 THEN 0
						ELSE (fact.receivable / NULLIF(t.amount_4, 0)) * t.service_fee 
						END AS service_fee,  --服务费
 						CASE  WHEN fact.is_non_sales = true THEN 0 else (
						CASE
							WHEN t.amount_4 = 0  THEN 0
							ELSE (fact.receivable / NULLIF(t.amount_4, 0)) * COALESCE(t.discount_amount, 0)
						END 
						+ 
						COALESCE(fact.transfer_amount, 0) - COALESCE(fact.overflow_amount, 0)) END AS payment_transfer_amount,
						 --支付转折扣
						coalesce(fact.sur_charge_amount,0) as sur_charge_amount,	--手续费
						coalesce(fact.card_no,'') AS  card_no, --卡号
						fact.real_amount-fact.merchant_allowance as real_amount,
						CASE  WHEN fact.is_non_sales = false THEN 0 else (
						CASE
							WHEN t.amount_4 = 0  THEN 0
							ELSE (fact.receivable / NULLIF(t.amount_4, 0)) * COALESCE(t.discount_amount, 0)
						END 
						+ 
						COALESCE(fact.transfer_amount, 0) - COALESCE(fact.overflow_amount, 0)) END AS non_sales_amount  -- 非销售金额
					FROM
						sales_payment_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
					        INNER JOIN  sales_ticket_amounts t
					        ON fact.eticket_id=t.eticket_id
					WHERE
						{WHERE}
					ORDER BY
						fact.bus_date DESC,
						fact.pay_time,
						store_caches.store_code
						{LIMIT}
 		`
	totalSQL = `
		--支付明细rows
					SELECT
					count(1) as total
					FROM
						sales_payment_amounts fact
						LEFT JOIN store_caches store_caches
						ON fact.store_id = store_caches.id
					        INNER JOIN  sales_ticket_amounts t
					        ON fact.eticket_id=t.eticket_id
					WHERE
						{WHERE}  
 		`

	whereSQL := generateWhereSQLForPaymentDetail(condition)
	limitSQL := generateLimitOffsetSQLForPaymentDetail(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)

	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateRegionSQLForPaymentDetail(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForPaymentDetail(condition *model.RepoCondition) string {
	var (
		regionIdsSQL        string // 区域筛选条件
		paymentMethodIdsSQL string // 支付方式筛选条件
		channelSQL          string // 渠道筛选条件
		storeTypeSQL        string // 增加门店类型的过滤
		openStatusSQL       string // 增加开店类型的过滤
		orderStatusSQL      string // 订单状态
	)

	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.PaymentIds) > 0 {
		paymentMethodIdsSQL = fmt.Sprintf(
			"AND fact.payment_id IN (%s)",
			helpers.JoinInt64(condition.PaymentIds, ","),
		)
	}
	if condition.ChannelId != 0 {
		channelSQL = fmt.Sprintf(
			"AND fact.channel_id = '%d'",
			condition.ChannelId)
	}
	var storeSQL string                                                       // 门店过滤
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}

	if condition.OrderStatus != "" {
		orderStatusSQL = fmt.Sprintf(`
				AND fact.order_status = '%s'
			`, condition.OrderStatus)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		AND t.is_non_sales = FALSE
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentMethodIdsSQL, channelSQL, storeTypeSQL, openStatusSQL, storeSQL, orderStatusSQL)
	return whereSQL
}

func generateLimitOffsetSQLForPaymentDetail(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func queryDBForPaymentDetailTotal(trackId int64, db *sql.DB, totalSQL string) int64 {
	var total int64
	r, err := db.Query(totalSQL)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return total
	}
	defer r.Close()
	if r.Next() {
		err = r.Scan(&total)
		if err != nil {
			return total
		}
	}

	return total
}
