package repo

import (
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"testing"
)
//
//const req = `
//{"is_natural":false,"start":"2023-07-18T00:00:00+08:00","end":"2023-07-18T23:59:59+08:00","is_pre":false,"region_search_type":"STORE","region_search_ids":["4695184470116958208"],"region_group_type":"STORE","channel_ids":["4688726709053194240"],"is_combo":false,"product_category_ids":["4736276772545495040"],"product_ids":["4696930776111972352"],"taxes":[0.06],"taxRules":["4774965255421952000"],"compare_start":"2023-07-17T00:00:00+08:00","compare_end":"2023-07-17T23:59:59+08:00","region_group_level":0,"dateType":"YESTERDAY","limit":10,"offset":0,"lan":"zh-CN"}
//`

const req = `
{"is_natural":false,"start":"2023-07-18T00:00:00+08:00","end":"2023-07-18T23:59:59+08:00","is_pre":false,"region_search_type":"STORE","region_search_ids":[4695184470116958208],"region_group_type":"STORE","channel_ids":[],"is_combo":false,"product_category_ids":[],"product_ids":[],"taxes":[],"taxRules":[],"compare_start":"2023-07-17T00:00:00+08:00","compare_end":"2023-07-17T23:59:59+08:00","region_group_level":0,"dateType":"YESTERDAY","limit":10,"offset":0,"lan":"zh-CN"}
`

func Test_generateQuerySQLForProductTaxSales(t *testing.T) {
	logger.InitLogger("debug", "prod")
	condition := &model.RepoCondition{}
	json.Unmarshal([]byte(req), condition)
	condition.PartnerId = 531
	condition.ProductIds = []int64{4749115155793674240}

	rowSQL, totalSQL, sendSQL, packageSQL := generateQuerySQLForProductTaxSales(0, condition)
	fmt.Println(rowSQL)
	fmt.Println(totalSQL)
	fmt.Println(sendSQL)
	fmt.Println(packageSQL)

}
