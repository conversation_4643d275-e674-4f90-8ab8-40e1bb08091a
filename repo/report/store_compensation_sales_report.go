package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) StoreCompensationSalesReport(ctx context.Context, condition *model.RepoCondition) (*report.StoreCompensationSalesReportResp, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	response := &report.StoreCompensationSalesReportResp{}
	// 利用condition中是否summary来判断是否需要查询汇总数据
	if condition.IncludeSummary {
		rowSummarySQL, totalSummarySQL := generateQuerySQLForStoreCompensationSalesSummaryReport(trackId, condition)
		chSummaryRows := make(chan []*report.StoreCompensationSalesReport, 1)
		chSummaryTotal := make(chan int64, 1)
		go queryDBWithChanForStoreCompensationSalesSummaryReport(trackId, rsmm.DB, rowSummarySQL, chSummaryRows)
		go queryDBWithChanForStoreCompensationSalesReportSummaryTotal(trackId, rsmm.DB, totalSummarySQL, chSummaryTotal)
		summaryRows := <-chSummaryRows
		summaryTotal := <-chSummaryTotal
		response.SummaryTotal = summaryTotal
		response.SummaryRows = summaryRows
		return response, nil
	} else {
		// 默认查询明细
		rowSQL, totalSQL := generateQuerySQLForStoreCompensationSalesReport(trackId, condition)
		chDetailRows := make(chan []*report.StoreCompensationSalesItemReport, 1)
		chDetailTotal := make(chan int64, 1)
		go queryDBWithChanForStoreCompensationSalesReport(trackId, rsmm.DB, rowSQL, chDetailRows)
		go queryDBWithChanForStoreCompensationSalesReportTotal(trackId, rsmm.DB, totalSQL, chDetailTotal)
		rows := <-chDetailRows
		total := <-chDetailTotal
		close(chDetailRows)
		close(chDetailTotal)
		response.Total = total
		response.Rows = rows
		return response, nil
	}
}

func queryDBWithChanForStoreCompensationSalesReportTotal(trackId int64, db *sql.DB, sqlStr string, ch chan int64) {
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- 0
		return
	}
	defer r.Close()

	if r.Next() {
		var total int64
		if err := r.Scan(&total); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- 0
		} else {
			ch <- total
		}
	} else {
		ch <- 0
	}
}

func queryDBWithChanForStoreCompensationSalesReportSummaryTotal(trackId int64, db *sql.DB, sqlStr string, ch chan int64) {
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- 0
		return
	}
	defer r.Close()

	if r.Next() {
		var total int64
		if err := r.Scan(&total); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- 0
		} else {
			ch <- total
		}
	} else {
		ch <- 0
	}
}

func queryDBWithChanForStoreCompensationSalesReport(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.StoreCompensationSalesItemReport) {
	ch <- queryDBForStoreCompensationSalesReport(trackId, db, sqlStr)
}

func queryDBWithChanForStoreCompensationSalesSummaryReport(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.StoreCompensationSalesReport) {
	ch <- queryDBForStoreCompensationSalesSummaryReport(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象detail
func queryDBForStoreCompensationSalesReport(trackId int64, db *sql.DB, sqlStr string) []*report.StoreCompensationSalesItemReport {
	results := make([]*report.StoreCompensationSalesItemReport, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()
	for r.Next() {
		f := new(report.StoreCompensationSalesItemReport)
		if err := r.Scan(
			&f.BusDate,
			&f.StoreId,
			&f.StoreCode,
			&f.StoreName,
			&f.TicketNo,
			&f.EndTime,
			&f.CompensationCategoryId,
			&f.CompensationCategoryCode,
			&f.CompensationCategoryDesc,

			&f.MainSeq,
			&f.Seq,
			&f.ParentId,
			&f.ProductID,
			&f.ProductName,
			&f.ProductCode,
			&f.Qty,
			&f.NetAmount,
			&f.NonSalesAmount,
			&f.ItemAmount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

// 查询数据库，并将结果映射为map对象summary
func queryDBForStoreCompensationSalesSummaryReport(trackId int64, db *sql.DB, sqlStr string) []*report.StoreCompensationSalesReport {
	results := make([]*report.StoreCompensationSalesReport, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StoreCompensationSalesReport)
		if err := r.Scan(
			&f.BusDate,
			&f.StoreId,
			&f.StoreCode,
			&f.StoreName,
			&f.TicketNo,
			&f.EndTime,
			&f.CompensationCategoryId,
			&f.CompensationCategoryCode,
			&f.CompensationCategoryDesc,
			&f.NetAmount,
			&f.NonSalesAmount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForStoreCompensationSalesReport(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string
		totalSQL string
	)
	rowSQL = `
	SELECT t.bus_date, t.store_id, store_caches.store_code, store_caches.store_name, coalesce(t.take_meal_no,'') as ticket_no, 
	t.end_time, t.trans_id as payment_id, t.trans_code as payment_code,t.trans_name as payment_name,
	t.main_seq,t.seq,
	t.parent_item_id as parent_id,t.item_id as product_id,t.item_name as product_name,
    t.item_code as product_code,t.qty as qty,t.order_paid_total,t.trans_amount AS non_sales_amount,
    T.trans_amount AS item_amount 
    FROM sales_item_payments_amounts t
	INNER JOIN store_caches ON t.store_id = store_caches.id
	{WHERE}
	order by T.bus_date desc,T.end_time desc,T.take_meal_no desc,T.main_seq asc,T.seq asc
	{LIMIT};
`
	totalSQL = `
    SELECT count(1) FROM sales_item_payments_amounts T 
    INNER JOIN store_caches ON T.store_id = store_caches.id 
    {WHERE};
`
	whereSQL := generateWhereSQLForStoreCompensationSalesReport(condition)
	limitSQL := generateLimitOffsetDetailSQLForStoreCompensationSalesReport(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG detail row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG detail total. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateQuerySQLForStoreCompensationSalesSummaryReport(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string
		totalSQL string
	)
	rowSQL = `
	SELECT t.bus_date, t.store_id, store_caches.store_code, store_caches.store_name, coalesce(t.take_meal_no,'') as ticket_no, 
	t.end_time, t.trans_id as payment_id, t.trans_code as payment_code,
	t.trans_name as payment_name,t.order_paid_total,SUM(t.trans_amount) AS non_sales_amount
    FROM sales_item_payments_amounts t
	INNER JOIN store_caches ON t.store_id = store_caches.id
	{WHERE}
	{GROUP}
	order by t.bus_date desc,t.end_time desc,t.take_meal_no desc
	{LIMIT}
	;
`
	totalSQL = `
    SELECT count(1) FROM(
		SELECT 1 FROM sales_item_payments_amounts T 
		INNER JOIN store_caches ON T.store_id = store_caches.id 
		{WHERE}
		{GROUP}
    ) as subquery
    ;
`
	whereSQL := generateWhereSQLForStoreCompensationSalesSummaryReport(condition)
	groupSQL := " group by t.bus_date,t.store_id,store_caches.store_code,store_caches.store_name,t.take_meal_no,t.end_time,t.trans_id,t.trans_code,t.trans_name,t.order_paid_total,t.non_sales_pay_amount"
	limitSQL := generateLimitOffsetSummarySQLForStoreCompensationSalesReport(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{GROUP}", groupSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	totalSQL = strings.ReplaceAll(totalSQL, "{GROUP}", groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary total. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateLimitOffsetDetailSQLForStoreCompensationSalesReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateLimitOffsetSummarySQLForStoreCompensationSalesReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateWhereSQLForStoreCompensationSalesSummaryReport(condition *model.RepoCondition) string {
	var (
		regionIdsSQL            string // 区域筛选条件
		storeTypeSQL            string // 区域筛选条件
		productIdsSQL           string // 商品筛选条件
		productCategoryIdsSQL   string // 类别筛选条件
		channelIdsSQL           string // 渠道筛选条件
		comboSQL                string // 套餐商品过滤条件
		groupPurchaseChannelSQL string // 团购渠道
		comboProductIdsSQL      string // 套餐商品过滤条件
		paymentIdsSQL           string // 支付方式过滤条件
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if len(condition.ChannelIds) > 0 {
		channelIdsSQL = fmt.Sprintf(`
			AND fact.channel_id IN (%s)
		`, helpers.JoinInt64(condition.ChannelIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	if len(condition.PaymentIds) > 0 {
		paymentIdsSQL = fmt.Sprintf(` AND t.trans_id in (%s)`, helpers.JoinInt64(condition.PaymentIds, ","))
	}
	nonSalesSQL := fmt.Sprintf(` AND t.trans_attr = '%s'`, "NON_SALES")
	location, _ := time.LoadLocation("Asia/Shanghai")
	localStart := condition.Start.In(location)
	localEnd := condition.End.In(location)
	whereSQL := fmt.Sprintf(`
		where t.partner_id = %d
		AND t.bus_date >= '%s'
		AND t.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, localStart.Format("2006-01-02"),
		localEnd.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, storeSQL, comboSQL, regionCodesSQL, paymentIdsSQL, nonSalesSQL, channelIdsSQL, storeTypeSQL,
		groupPurchaseChannelSQL, comboProductIdsSQL)
	return whereSQL
}

func generateWhereSQLForStoreCompensationSalesReport(condition *model.RepoCondition) string {
	var (
		regionIdsSQL            string // 区域筛选条件
		storeTypeSQL            string // 区域筛选条件
		productIdsSQL           string // 商品筛选条件
		productCategoryIdsSQL   string // 类别筛选条件
		channelIdsSQL           string // 渠道筛选条件
		comboSQL                string // 套餐商品过滤条件
		groupPurchaseChannelSQL string // 团购渠道
		comboProductIdsSQL      string // 套餐商品过滤条件
		paymentIdsSQL           string // 支付方式过滤条件
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if len(condition.ChannelIds) > 0 {
		channelIdsSQL = fmt.Sprintf(`
			AND fact.channel_id IN (%s)
		`, helpers.JoinInt64(condition.ChannelIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	if len(condition.PaymentIds) > 0 {
		paymentIdsSQL = fmt.Sprintf(` AND t.trans_id in (%s)`, helpers.JoinInt64(condition.PaymentIds, ","))
	}
	nonSalesSQL := fmt.Sprintf(` AND t.trans_attr = '%s'`, "NON_SALES")
	location, _ := time.LoadLocation("Asia/Shanghai")
	localStart := condition.Start.In(location)
	localEnd := condition.End.In(location)
	whereSQL := fmt.Sprintf(`
		where t.partner_id = %d
		AND t.bus_date >= '%s'
		AND t.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, localStart.Format("2006-01-02"),
		localEnd.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, storeSQL, comboSQL, regionCodesSQL, paymentIdsSQL, nonSalesSQL, channelIdsSQL, storeTypeSQL,
		groupPurchaseChannelSQL, comboProductIdsSQL)
	return whereSQL
}
