package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductChannelSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductChannelResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductChannelSales(trackId, condition)
	ch1 := make(chan []*report.ProductChannel, 1)
	ch2 := make(chan []*report.ProductChannel, 1)
	go queryDBWithChanForProductChannel(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductChannel(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductChannel
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductChannelResponse{
		Summary: summary,
		Rows:    rows,
		Total:   0,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductChannel(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductChannel) {
	ch <- queryDBForProductChannel(trackId, db, sqlStr)
}

func queryDBForProductChannel(trackId int64, db *sql.DB, sqlStr string) []*report.ProductChannel {
	res := make([]*report.ProductChannel, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return res
	}
	defer r.Close()
	for r.Next() {
		f := new(report.ProductChannel)
		var value json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.RegionCode,
			&f.RegionName,
			&f.RegionAddress,
			&f.StoreType,
			&f.BusinessDays,
			&f.ProductAveragePrice,
			&f.GrossAmount,
			&f.GrossAmountReturned,
			&f.NetAmount,
			&f.NetAmountReturned,
			&f.DiscountAmount,
			&f.DiscountAmountReturned,
			&f.ItemCount,
			&f.ItemCountReturned,
			&value,
			&f.TaxFee,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return res
		}
		err = json.Unmarshal(value, &f.Child)
		if err != nil {
			logger.Pre().Error("单品渠道child结构体解析失败：", err)
			return res
		}
		res = append(res, f)
	}
	return res
}

func generateQuerySQLForProductChannelSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)

	if condition.TagType == "SUMMARY" {
		rowSQL = `
			--单品渠道报表汇总信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date,{REGION_ID}
					ORDER BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID}
				ORDER BY
					{REGION_ID}
				{LIMIT}
			), base_products AS (
				SELECT
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) AS product_id,
					COALESCE(product_caches.category,0) AS product_category_id,
					COALESCE(fact.channel_id, 0) AS channel_id,
					COALESCE(fact.order_type, '') AS order_type,
					coalesce(fact.unit, '') as unit, --商品单位
				    sum(weight) as weight, --商品份量
				    SUM(CASE WHEN refunded THEN weight ELSE 0 END) AS weight_returned, --退单商品份量
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					SUM(fact.tax_fee) as tax_fee
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches product_caches
							ON fact.product_id = product_caches.id
						INNER JOIN base_limit
							ON  {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID},
					fact.product_id,
					fact.has_weight,
				    fact.unit,
					product_caches.category,
					fact.channel_id,
					fact.order_type
				ORDER BY
					{REGION_ID}
			), base_products_by_date_store_product_category_channel AS (
				SELECT
					region_id,
					store_type,
					product_id,
					product_category_id,
					channel_id,
					unit,
				    sum(weight) as weight,
				    sum(weight_returned) as weight_returned,
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) AS tax_fee, 
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'order_type', order_type, --订单类型
							'order_type_name', '', --订单类型名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
						    'unit', unit,
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee
						)
					) AS "child"
				FROM base_products
				GROUP BY
					region_id,
					store_type,
					product_id,
					product_category_id,
					channel_id,
					unit
			),base_products_by_date_store_product_category AS (
				SELECT
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit,
				    sum(weight) as weight,
				    sum(weight_returned) as weight_returned,
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) AS tax_fee, --税费
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'channel_id', channel_id, --渠道ID
							'channel_code', '', --渠道code
							'channel_name', '', --渠道名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
						    'unit', unit,
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee,
							'child', child --订单类型child
						)
					) AS "child"
				FROM base_products_by_date_store_product_category_channel
				GROUP BY
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit
			),base_products_by_date_store AS (
				SELECT
					region_id,
					store_type,
			
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) AS tax_fee,
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'product_id', product_id, --产品ID
							'product_code', '', --产品code
							'product_name', '', --产品名称
							'product_category_id', product_category_id, --产品类别ID
							'product_category_code', '', --产品类别code
							'product_category_name', '', --产品类别名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
						    'unit', unit,
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee,
							'child', child --渠道child
						)
					) AS "child"
				FROM base_products_by_date_store_product_category
				GROUP BY
					region_id,
					store_type
			)
			SELECT
				'' AS bus_date,
				bpbdas.region_id,
				'' AS region_code,
				'' AS region_name,
				'' AS region_address,
				bpbdas.store_type,
				bd.days AS business_days,
				0 AS product_average_price,
				COALESCE(bpbdas.gross_amount, 0) AS gross_amount,
				COALESCE(bpbdas.gross_amount_returned, 0) AS gross_amount_returned,
				COALESCE(bpbdas.net_amount, 0) AS net_amount,
				COALESCE(bpbdas.net_amount_returned, 0) AS net_amount_returned,
				COALESCE(bpbdas.discount_amount, 0) AS discount_amount,
				COALESCE(bpbdas.discount_amount_returned, 0) AS discount_amount_returned,
				COALESCE(bpbdas.item_count, 0) AS item_count,
				COALESCE(bpbdas.item_count_returned, 0) AS item_count_returned,
				COALESCE(bpbdas.child, '[]'::json) AS child, --产品/类别数据
				COALESCE(bpbdas.tax_fee, 0) AS tax_fee, --税费
			
				0 AS total --Summary时的汇总条数
			FROM
				base_products_by_date_store bpbdas
					LEFT JOIN business_days bd
						ON bpbdas.region_id = bd.region_id
			ORDER BY
				bpbdas.region_id
		`
		summarySQL = `
			-- 单品渠道报表汇总信息 SUMMARY
			SELECT
				'' AS bus_date,
				0 AS region_id,
				'' AS region_code,
				'' AS region_name,
				'' AS region_address,
				'' AS store_type,
				0 AS business_days, --营业天数,
				0 AS product_average_price,
			
				COALESCE(SUM(gross_amount),0) AS gross_amount,
				COALESCE(SUM(gross_amount_returned),0) AS gross_amount_returned,
				COALESCE(SUM(net_amount),0) AS net_amount,
				COALESCE(SUM(net_amount_returned),0) AS net_amount_returned,
				COALESCE(SUM(discount_amount),0) AS discount_amount,
				COALESCE(SUM(discount_amount_returned),0) AS discount_amount_returned,
				COALESCE(SUM(item_count),0) AS item_count,
				COALESCE(SUM(item_count_returned),0) AS item_count_returned,
				'[]' ::json AS child,
				COALESCE(SUM(tax_fee), 0) AS tax_fee, --税费
				COUNT(1) AS total --Summary时的汇总条数
			FROM (
				SELECT
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					SUM(tax_fee) AS tax_fee --税费
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID}
				ORDER BY
					{REGION_ID}
			 ) T
		`
	} else {
		rowSQL = `
			--单品渠道报表详细信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_product_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
								LEFT JOIN product_caches
									ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					fact.bus_date AS bus_date,
					{REGION_ID} AS region_id
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID}
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
				{LIMIT}
			), base_products AS (
				SELECT
					fact.bus_date AS bus_date,
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) AS product_id,
					COALESCE(product_caches.category,0) AS product_category_id,
					COALESCE(fact.channel_id, 0) AS channel_id,
					COALESCE(fact.order_type, '') AS order_type,
					coalesce(fact.unit, '') as unit, --商品单位
				    sum(weight) as weight, --商品份量
				    SUM(CASE WHEN refunded THEN weight ELSE 0 END) AS weight_returned, --退单商品份量
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					SUM(tax_fee) as tax_fee --税费
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches product_caches
							ON fact.product_id = product_caches.id
						INNER JOIN base_limit
							ON fact.bus_date = base_limit.bus_date
								AND {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID},
					fact.product_id,
					fact.has_weight,
				    fact.unit,
					product_caches.category,
					fact.channel_id,
					fact.order_type
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
			), base_products_by_date_store_product_category_channel AS (
				SELECT
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					channel_id,
					unit,
				    sum(weight) as weight,
				    sum(weight_returned) as weight_returned,
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) as tax_fee, --税费
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'order_type', order_type, --订单类型
							'order_type_name', '', --订单类型名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
						    'unit', unit,
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee
						)
					) AS "child"
				FROM base_products
				GROUP BY
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					channel_id,
					unit
			),base_products_by_date_store_product_category AS (
				SELECT
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit,
				    sum(weight) as weight,
				    sum(weight_returned) as weight_returned,
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) as tax_fee, --税费
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'channel_id', channel_id, --渠道ID
							'channel_code', '', --渠道code
							'channel_name', '', --渠道名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
						    'unit', unit,
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee,
							'child', child --订单类型child
						)
					) AS "child"
				FROM base_products_by_date_store_product_category_channel
				GROUP BY
					bus_date,
					region_id,
					store_type,
					product_id,
					product_category_id,
					unit
			),base_products_by_date_store AS (
				SELECT
					bus_date,
					region_id,
					max(store_type) as store_type,
			
					SUM(gross_amount) AS gross_amount,
					SUM(gross_amount_returned) AS gross_amount_returned,
					SUM(net_amount) AS net_amount,
					SUM(net_amount_returned) AS net_amount_returned,
					SUM(discount_amount) AS discount_amount,
					SUM(discount_amount_returned) AS discount_amount_returned,
					SUM(item_count) AS item_count,
					SUM(item_count_returned) AS item_count_returned,
					SUM(tax_fee) AS tax_fee, --税费
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'product_id', product_id, --产品ID
							'product_code', '', --产品code
							'product_name', '', --产品名称
							'product_category_id', product_category_id, --产品类别ID
							'product_category_code', '', --产品类别code
							'product_category_name', '', --产品类别名称
							'weight', weight,
						    'weight_count', concat(weight, unit),
						    'weight_returned', weight_returned,
						    'refund_weight_count', concat(weight_returned, unit),
							'gross_amount', gross_amount,
							'gross_amount_returned', gross_amount_returned,
							'net_amount', net_amount,
							'net_amount_returned', net_amount_returned,
							'discount_amount', discount_amount,
							'discount_amount_returned', discount_amount_returned,
							'item_count', item_count,
							'item_count_returned', item_count_returned,
							'tax_fee',tax_fee,
							'child', child --渠道child
						)
					) AS "child"
				FROM base_products_by_date_store_product_category
				GROUP BY
					bus_date,
					region_id
			)
			SELECT
				to_char(bpbdas.bus_date,'YYYY-MM-DD') AS bus_date,
				bpbdas.region_id,
				'' AS region_code,
				'' AS region_name,
				'' AS region_address,
				bpbdas.store_type AS store_type,
				bd.days AS business_days,
				0 AS product_average_price,
				COALESCE(bpbdas.gross_amount, 0) AS gross_amount,
				COALESCE(bpbdas.gross_amount_returned, 0) AS gross_amount_returned,
				COALESCE(bpbdas.net_amount, 0) AS net_amount,
				COALESCE(bpbdas.net_amount_returned, 0) AS net_amount_returned,
				COALESCE(bpbdas.discount_amount, 0) AS discount_amount,
				COALESCE(bpbdas.discount_amount_returned, 0) AS discount_amount_returned,
				COALESCE(bpbdas.item_count, 0) AS item_count,
				COALESCE(bpbdas.item_count_returned, 0) AS item_count_returned,
				COALESCE(bpbdas.child, '[]'::json) AS child, --产品/类别数据
				COALESCE(bpbdas.tax_fee, 0) AS tax_fee, --税费
				0 AS total --Summary时的汇总条数
			FROM
				base_products_by_date_store bpbdas
					LEFT JOIN business_days bd
						ON bpbdas.region_id = bd.region_id
			ORDER BY
				bpbdas.bus_date DESC,
				bpbdas.region_id
		`
		summarySQL = `
			-- 单品渠道报表详细信息 SUMMARY
			SELECT
				'' AS bus_date,
				0 AS region_id,
				'' AS region_code,
				'' AS region_name,
				'' AS region_address,
				'' AS store_type,
				0 AS business_days, --营业天数,
				0 AS product_average_price,
			
				COALESCE(SUM(gross_amount),0) AS gross_amount,
				COALESCE(SUM(gross_amount_returned),0) AS gross_amount_returned,
				COALESCE(SUM(net_amount),0) AS net_amount,
				COALESCE(SUM(net_amount_returned),0) AS net_amount_returned,
				COALESCE(SUM(discount_amount),0) AS discount_amount,
				COALESCE(SUM(discount_amount_returned),0) AS discount_amount_returned,
				COALESCE(SUM(item_count),0) AS item_count,
				COALESCE(SUM(item_count_returned),0) AS item_count_returned,
				'[]' ::json AS child,
				COALESCE(SUM(tax_fee), 0) AS tax_fee, --税费
				COUNT(1) AS total --Summary时的汇总条数
			FROM (
				SELECT
					SUM(gross_amount) AS gross_amount, --商品流水
					SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned, --退单商品流水
					SUM(net_amount) AS net_amount, --商品实收
					SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned, --退单商品实收
					SUM(discount_amount) AS discount_amount, --折扣金额
					SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned, --退单折扣金额
					SUM(qty) AS item_count, --商品数量
					SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned, --退单商品数量
					SUM(tax_fee) AS tax_fee
				FROM
					sales_product_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						LEFT JOIN product_caches product_caches
							ON fact.product_id = product_caches.id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID}
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
			 ) T
		`
	}
	whereSQL := generateWhereSQLForProductChannelSales(condition)
	regionSQL := generateRegionSQLForProductSales(condition)
	limitSQL := generateLimitOffsetSQLForProductSales(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)

	return rowSQL, summarySQL
}

func generateWhereSQLForProductChannelSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		channelIdSQL          string // 渠道筛选条件
		orderTypeSQL          string // 订单类型筛选条件
		openStatusSQL         string // 开店类型筛选条件
		storeTypeSQL          string // 门店类型筛选条件
		comboSQL              string // 套餐商品筛选条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = %d",
			condition.ChannelId,
		)
	}
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, channelIdSQL, orderTypeSQL, storeTypeSQL, openStatusSQL, storeSQL, comboSQL)
	return whereSQL
}
