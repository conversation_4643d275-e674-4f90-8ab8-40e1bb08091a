package repo

import (
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"testing"
)

func TestSalesRepositoryPG_ProductFlavorSales(t *testing.T) {
	rsmm := &SalesRepositoryPG{}
	rowSQL := ""
	ch1 := make(chan []*report.ProductFlavorSales, 1)
	go queryDBWithChanForProductFlavorSales(0, rsmm.DB, rowSQL, ch1)
	rows := <-ch1
	close(ch1)
	//var total int64
	//if rowSQL == "" {
	//	total = 0
	//}
	fmt.Println(len(rows))
	//fmt.Println(total)
}
