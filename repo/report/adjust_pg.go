package repo

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
)

const adjustRowTpl = `
-- 报损报表 rows
select
	coalesce(fact.sku_id, 0) as sku_id,
	coalesce(to_char(fact.operate_time at time zone 'cct', 'YYYY-MM-DD HH24:MI'), '') as operate_time,
	coalesce(store_caches.store_name, '') as store_name,
	coalesce(fact.operator_name, '') as operator_name,
	coalesce(fact.sku_code, '') as product_code,
	coalesce(fact.sku_name, '') as product_name,
	coalesce(fact.reason_code, '') as reason_code,
	coalesce(fact.reason_name, '') as reason_name,
	coalesce(fact.price + fact.acc_price, 0) as price,
	coalesce(fact.qty, 0) as qty,
	coalesce((fact.price + fact.acc_price) * fact.qty, 0) as amount
from sales_adjust_product fact
	left join store_caches on fact.store_id = store_caches.id
where deleted = 0 and {WHERE}
order by fact.operate_time desc, fact.created desc, fact.id
{LIMIT}
`

const adjustTotalTpl = `
-- 报损报表 total
select
	count(1) as total
from sales_adjust_product fact
	left join store_caches on fact.store_id = store_caches.id
where deleted = 0 and {WHERE}
`

func (rsmm *AdjustRepositoryPG) Adjust(ctx context.Context, condition *model.RepoCondition) (*report.AdjustResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	rowSql, totalSql := adjustSql(ctx, condition)

	rowCh := make(chan []*report.AdjustRow, 0)
	totalCh := make(chan int64, 0)

	go adjustExecuteRow(trackId, rowSql, rsmm.DB, rowCh)
	go adjustExecuteTotal(trackId, totalSql, rsmm.DB, totalCh)

	resp := &report.AdjustResponse{
		Rows:  <-rowCh,
		Total: <-totalCh,
	}
	close(rowCh)
	close(totalCh)
	return resp, nil
}

func adjustExecuteRow(trackId int64, sql string, db *sql.DB, ch chan []*report.AdjustRow) {
	results := make([]*report.AdjustRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- results
		return
	}
	defer r.Close()

	for r.Next() {
		f := new(report.AdjustRow)
		if err := r.Scan(
			&f.SkuId,
			&f.OperateTime,
			&f.StoreName,
			&f.OperatorName,
			&f.ProductCode,
			&f.ProductName,
			&f.ReasonCode,
			&f.ReasonName,
			&f.Price,
			&f.Qty,
			&f.Amount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- results
			return
		}
		results = append(results, f)
	}
	ch <- results
	return
}

func adjustExecuteTotal(trackId int64, sql string, db *sql.DB, ch chan int64) {
	var res int64 = 0
	r := db.QueryRow(sql)
	err := r.Scan(&res)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- res
		return
	}
	ch <- res
}

func adjustSql(ctx context.Context, condition *model.RepoCondition) (rowSql string, totalSql string) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	whereSQL := adjustWhere(condition)
	limitSQL := adjustLimit(condition)
	rowSql = strings.ReplaceAll(adjustRowTpl, "{WHERE}", whereSQL)
	rowSql = strings.ReplaceAll(rowSql, "{LIMIT}", limitSQL)

	totalSql = strings.ReplaceAll(adjustTotalTpl, "{WHERE}", whereSQL)

	logger.Pre().Debugf("[%d] repo.report.AdjustRepositoryPG row. SQL: `%s`", trackId, rowSql)
	logger.Pre().Debugf("[%d] repo.report.AdjustRepositoryPG total. SQL: `%s`", trackId, totalSql)
	return
}

func adjustWhere(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}

	var productSQL string
	if condition.ProductIds != nil && len(condition.ProductIds) > 0 {
		productSQL = fmt.Sprintf(`
			AND fact.sku_id IN (%s)
		`, helpers.JoinInt64(condition.ProductIds, ","))
	}

	var reasonSQL string
	if condition.Code != "" {
		reasonSQL = fmt.Sprintf(`
			AND fact.reason_code = '%s'
		`, condition.Code)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.operate_time >= ('%sT16:00:00.000000Z'::timestamptz)
		AND fact.operate_time <= ('%sT15:59:59.999999Z'::timestamptz)
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.Start.AddDate(0, 0, -1).Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, productSQL, reasonSQL)
	return whereSQL
}

func adjustRegion(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": //加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func adjustLimit(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
