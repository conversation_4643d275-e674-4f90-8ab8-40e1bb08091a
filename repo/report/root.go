package repo

import (
	"database/sql"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gorm.io/gorm"
)

type SalesRepositoryPG struct {
	DB     *sql.DB
	GormDB *gorm.DB
}

type MakeTimeRepositoryPG struct {
	DB     *sql.DB
	GormDB *gorm.DB
}

type AdjustRepositoryPG struct {
	DB     *sql.DB
	GormDB *gorm.DB
}

type InboundRepositoryPG struct {
	DB     *sql.DB
	GormDB *gorm.DB
}

func GenerateRegionWhereSQL(condition *model.RepoCondition) string {
	var regionWhereSQL string
	// 如果查询门店id为空，则不加门店过滤语句
	if len(condition.RegionSearchIds) > 0 {
		switch condition.RegionSearchType {
		case "GEO_REGION": //按地理区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 0
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "BRANCH_REGION": //按管理区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 1
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "FRANCHISEE_REGION": //按加盟商区域查询
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 2
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "COMPANY": // 按公司查询
			regionWhereSQL = fmt.Sprintf(`
					AND store_caches.company_info IN (%s)
		 `, helpers.JoinInt64(condition.RegionSearchIds, ","))
		default:
			if len(condition.RegionSearchIds) > 0 {
				if len(condition.RegionSearchIds) == 1 {
					regionWhereSQL = fmt.Sprintf(
						"AND store_caches.id = %d",
						condition.RegionSearchIds[0],
					)
				} else {
					regionWhereSQL = fmt.Sprintf(
						"AND store_caches.id IN (%s)",
						helpers.JoinInt64(condition.RegionSearchIds, ","),
					)
				}
			}
		}
	}
	return regionWhereSQL
}

func GenerateRegionCodesWhereSQL(condition *model.RepoCondition) string {
	regionCodesSql := "" // 省市区
	if len(condition.RegionCodes) > 0 {
		regionCodesSql = "and ("
		for i, regionCode := range condition.RegionCodes {
			if i > 0 {
				regionCodesSql += " or "
			}
			regionCodesSql += fmt.Sprintf(` store_caches.regions @> '{"%s"}' `, regionCode)
		}
		regionCodesSql += ")"
	}
	return regionCodesSql
}
