package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) StoreChannelSales(ctx context.Context, condition *model.RepoCondition) (*report.StoreChannelSalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForStoreChannelSales(trackId, condition)
	ch1 := make(chan []*report.StoreChannelSales, 1)
	ch2 := make(chan []*report.StoreChannelSales, 1)
	start := time.Now()
	go queryDBWithChanForStoreChannelSales(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForStoreChannelSales(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.StoreChannelSales
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	var total int64
	if summary != nil {
		total = summary.Total
	}
	return &report.StoreChannelSalesResponse{
		Rows:    rows,
		Summary: summary,
		Total:   total,
	}, nil
}

func queryDBWithChanForStoreChannelSales(trackId int64, db *sql.DB, sql string, ch chan []*report.StoreChannelSales) {
	ch <- queryDBForStoreChannelSales(trackId, db, sql)
}

func queryDBForStoreChannelSales(trackId int64, db *sql.DB, s string) []*report.StoreChannelSales {
	results := make([]*report.StoreChannelSales, 0)
	if s == "" {
		return []*report.StoreChannelSales{new(report.StoreChannelSales)}
	}
	r, err := db.Query(s)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.StoreChannelSales)
		var value json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.BusinessAmount,
			&f.RealAmount,
			&f.ExpendAmount,
			&f.CustomerPrice,
			&f.ValidOrderCount,
			&f.GrossAmount,
			&f.NetAmount,
			&f.DiscountAmount,
			&f.MerchantAllowance,
			&f.PackageFee,
			&f.DeliveryFee,
			&f.SendFee,
			&f.MerchantSendFee,
			&f.PlatformSendFee,
			&f.Commission,
			&f.ServiceFee,
			&f.Tip,
			&f.Receivable,
			&f.PayAmount,
			&f.Rounding,
			&f.OverflowAmount,
			&f.ChangeAmount,
			&f.TransferRealAmount,
			&f.DiscountContribute,
			&f.DiscountMerchantContribute,
			&f.PayMerchantContribute,
			&f.RealAmountDiscount,
			&f.RealAmountPayment,
			&f.TaxFee,
			&value,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(value, &f.Child)
		if err != nil {
			logger.Pre().Error("门店渠道child结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForStoreChannelSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string // 查询rows
		summarySQL string // 查询summary
	)
	if condition.TagType == "SUMMARY" {
		rowSQL = `
            --门店渠道汇总信息rows
                with business_days as (
                    select
                        region_id,count(1) as days
                    from(
                        select
							{REGION_ID} as region_id
                        from sales_ticket_amounts fact
                            left join store_caches store_caches
                                on store_caches.id = fact.store_id
                        where
                            {WHERE}
							and {REGION_ID} > 0
                        group by
                            fact.bus_date, {REGION_ID}
                        order by fact.bus_date desc,{REGION_ID}
                    )T
                    group by region_id
                ), base_limit as(
                    select
                        {REGION_ID} as region_id
                    from sales_ticket_amounts fact
                        left join store_caches store_caches on fact.store_id = store_caches.id
                    where
                        {WHERE}
						and {REGION_ID} > 0
                    group by {REGION_ID}
                    order by {REGION_ID}
                    {LIMIT}
                ),base_tickets as (
                    select
                        coalesce({REGION_ID}, 0) as region_id,
                        COALESCE(max(store_caches.store_type), '') AS store_type,
                        coalesce(fact.channel_id, 0) as channel_id,
                        coalesce(max(fact.order_type), '') as order_type,
                        coalesce(sum(fact.amount_0), 0) as business_amount, --营业额
                        coalesce(sum(fact.amount_2-fact.rounding), 0) as real_amount, --实收金额(交易)
                        coalesce(sum(fact.amount_1+fact.rounding), 0) as expend_amount, --支出(交易)
                        case when coalesce(sum(eticket_count), 0)=0 then 0 else coalesce(sum(amount_0), 0)/coalesce(sum(eticket_count), 0) end as customer_price, --单均价
                        coalesce(sum(fact.eticket_count), 0) as valid_order_count, --有效订单数
                        coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
                        coalesce(sum(fact.net_amount), 0) as net_amount, --商品实收
                        coalesce(sum(fact.discount_amount), 0) as discount_amount, --折扣金额
                        coalesce(sum(fact.merchant_discount_amount+fact.store_discount_amount), 0) as merchant_allowance, --商家活动补贴
                        coalesce(sum(fact.package_fee), 0) as package_fee, --包装费
                        coalesce(sum(fact.delivery_fee), 0) as delivery_fee, --配送费原价
                        coalesce(sum(fact.send_fee), 0) as send_fee, --配送费用户实际支付
                        coalesce(sum(fact.merchant_send_fee), 0) as merchant_send_fee, --配送费商家承担
                        coalesce(sum(fact.platform_send_fee), 0) as platform_send_fee, --配送费平台承担
                        coalesce(sum(fact.commission), 0) as commission, --佣金
                        coalesce(sum(fact.service_fee), 0) as service_fee, --服务费
                        coalesce(sum(fact.tip), 0) as tip, --小费
                        coalesce(sum(fact.amount_4), 0) as receivable, --应付金额
                        coalesce(sum(fact.pay_amount), 0) as pay_amount, --实付金额
                        coalesce(sum(fact.rounding), 0) as rounding, --抹零
                        coalesce(sum(fact.overflow_amount), 0) as overflow_amount, --溢收
                        coalesce(sum(fact.change_amount), 0) as change_amount, --找零
                        coalesce(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                        coalesce(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute,--优惠组成(财务)
                        coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
                        coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
                        coalesce(SUM(discount_buyer_contribute+discount_other_contribute), 0) AS real_amount_discount, --实收组成-折扣（财务）
                        coalesce(SUM(pay_buyer_contribute+pay_other_contribute), 0) AS real_amount_payment, --实收组成-支付（财务）
						coalesce(SUM(fact.tax_fee), 0) as tax_fee --	税费
            
                    from sales_ticket_amounts fact
                      left join store_caches store_caches
                        on fact.store_id = store_caches.id
                      inner join base_limit
                        on {REGION_ID} = base_limit.region_id
                    where
                        {WHERE}
						and {REGION_ID} > 0
                    group by {REGION_ID}, fact.channel_id, fact.order_type
                    order by {REGION_ID}
                ), base_tickets_for_channel_and_order_type AS (
					SELECT
						region_id,
						store_type,
						channel_id,--渠道
                        SUM(business_amount) AS business_amount, --营业额
                        SUM(real_amount) AS real_amount, --实收金额(交易)
                        SUM(expend_amount) AS expend_amount, --支出(交易)
					    case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end as customer_price, --单均价
                        SUM(valid_order_count) AS valid_order_count, --有效订单数
                        SUM(gross_amount) AS gross_amount, --商品流水
                        SUM(net_amount) AS net_amount, --商品实收
                        SUM(discount_amount) AS discount_amount, --折扣金额
                        SUM(merchant_allowance) AS merchant_allowance, --商家活动补贴
                        SUM(package_fee) AS package_fee, --包装费
                        SUM(delivery_fee) AS delivery_fee, --配送费原价
                        SUM(send_fee) AS send_fee, --配送费用户实际支付
                        SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
                        SUM(platform_send_fee) AS platform_send_fee, --配送费平台承担
                        SUM(commission) AS commission, --佣金
                        SUM(service_fee) AS service_fee, --服务费
                        SUM(tip) AS tip, --小费
                        SUM(receivable) AS receivable, --应付
                        SUM(pay_amount) AS pay_amount, --实付
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(discount_contribute) AS discount_contribute, --优惠组成
                        SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		                SUM(pay_merchant_contribute) AS pay_merchant_contribute, --优惠组成-支付
		                SUM(real_amount_discount) AS real_amount_discount, --实收组成-折扣（财务）
		                SUM(real_amount_payment) AS real_amount_payment, --实收组成-支付（财务）

						JSON_AGG(
							JSON_BUILD_OBJECT(
							       'order_type', order_type, --物流类型
                                   'business_amount', business_amount, --营业额
                                   'real_amount', real_amount, --实收金额(交易)
                                   'expend_amount', expend_amount, --支出(交易)
							        'customer_price', customer_price, --单均价
                                   'valid_order_count', valid_order_count, --有效订单数
                                   'gross_amount', gross_amount, --商品流水
                                   'net_amount', net_amount, --商品实收
                                   'discount_amount', discount_amount, --折扣金额
                                   'merchant_allowance', merchant_allowance, --商家活动补贴
                                   'package_fee', package_fee, --包装费
                                   'delivery_fee', delivery_fee, --配送费原价
                                   'send_fee', send_fee, --配送费用户实际支付
                                   'merchant_send_fee', merchant_send_fee, --配送费商家承担
                                   'platform_send_fee', platform_send_fee, --配送费平台承担
                                   'commission', commission, --佣金
                                   'service_fee', service_fee, --服务费
                                   'tip', tip, --小费
                                   'receivable', receivable, --应付
                                   'pay_amount', pay_amount, --实付
                                   'rounding', rounding, --抹零
                                   'overflow_amount', overflow_amount, --溢收
                                   'change_amount', change_amount, --找零
                                   'transfer_real_amount', transfer_real_amount, --财务实收金额(转换后)
                                   'discount_contribute', discount_contribute, --优惠组成
                                   'discount_merchant_contribute', discount_merchant_contribute, --优惠组成-折扣（财务）
                                   'pay_merchant_contribute', pay_merchant_contribute, --优惠组成-支付
                                   'real_amount_discount', real_amount_discount, --实收组成-折扣（财务）
                                   'real_amount_payment', real_amount_payment, --实收组成-支付（财务）
									'tax_fee',tax_fee   --税费
							)
						) AS "child"
					FROM  base_tickets
					GROUP BY
						region_id, store_type, channel_id
				), base_tickets_for_channel AS (
                	SELECT 
                        region_id,
                        max(store_type) as store_type,
                        SUM(business_amount) AS business_amount, --营业额
                        SUM(real_amount) AS real_amount, --实收金额(交易)
                        SUM(expend_amount) AS expend_amount, --支出(交易)
					    case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end as customer_price, --单均价
                        SUM(valid_order_count) AS valid_order_count, --有效订单数
                        SUM(gross_amount) AS gross_amount, --商品流水
                        SUM(net_amount) AS net_amount, --商品实收
                        SUM(discount_amount) AS discount_amount, --折扣金额
                        SUM(merchant_allowance) AS merchant_allowance, --商家活动补贴
                        SUM(package_fee) AS package_fee, --包装费
                        SUM(delivery_fee) AS delivery_fee, --配送费原价
                        SUM(send_fee) AS send_fee, --配送费用户实际支付
                        SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
                        SUM(platform_send_fee) AS platform_send_fee, --配送费平台承担
                        SUM(commission) AS commission, --佣金
                        SUM(service_fee) AS service_fee, --服务费
                        SUM(tip) AS tip, --小费
                        SUM(receivable) AS receivable, --应付
                        SUM(pay_amount) AS pay_amount, --实付
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(discount_contribute) AS discount_contribute, --优惠组成
                        SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		                SUM(pay_merchant_contribute) AS pay_merchant_contribute, --优惠组成-支付
		                SUM(real_amount_discount) AS real_amount_discount, --实收组成-折扣（财务）
		                SUM(real_amount_payment) AS real_amount_payment, --实收组成-支付（财务）
						SUM(tax_fee) AS tax_fee, --税费

                       JSON_AGG(
                               JSON_BUILD_OBJECT(
                                       'channel_id', channel_id, --渠道id
                                       'channel_code', '', --渠道code
                                       'channel_name', '', --渠道名称
                                       'business_amount', business_amount, --营业额
                                       'real_amount', real_amount, --实收金额(交易)
                                       'expend_amount', expend_amount, --支出(交易)
                                       'customer_price', customer_price, --单均价
                                       'valid_order_count', valid_order_count, --有效订单数
                                       'gross_amount', gross_amount, --商品流水
                                       'net_amount', net_amount, --商品实收
                                       'discount_amount', discount_amount, --折扣金额
                                       'merchant_allowance', merchant_allowance, --商家活动补贴
                                       'package_fee', package_fee, --包装费
                                       'delivery_fee', delivery_fee, --配送费原价
                                       'send_fee', send_fee, --配送费用户实际支付
                                       'merchant_send_fee', merchant_send_fee, --配送费商家承担
                                       'platform_send_fee', platform_send_fee, --配送费平台承担
                                       'commission', commission, --佣金
                                       'service_fee', service_fee, --服务费
                                       'tip', tip, --小费
                                       'receivable', receivable, --应付
                                       'pay_amount', pay_amount, --实付
                                       'rounding', rounding, --抹零
                                       'overflow_amount', overflow_amount, --溢收
                                       'change_amount', change_amount, --找零
                                       'transfer_real_amount', transfer_real_amount, --财务实收金额(转换后)
                                       'discount_contribute', discount_contribute, --优惠组成
                                       'discount_merchant_contribute', discount_merchant_contribute, --优惠组成-折扣（财务）
                                       'pay_merchant_contribute', pay_merchant_contribute, --优惠组成-支付
                                       'real_amount_discount', real_amount_discount, --实收组成-折扣（财务）
                                       'real_amount_payment', real_amount_payment, --实收组成-支付（财务）
										'tax_fee',tax_fee
                                       'child', child
                                   )
                           ) AS "child"
                FROM base_tickets_for_channel_and_order_type
                GROUP BY region_id
            )
			SELECT
					'' AS bus_date,
					bt.region_id,
					bt.store_type,
					bd.days AS business_days,

                    bt.business_amount AS business_amount, --营业额
                    bt.real_amount AS real_amount, --实收金额(交易)
                    bt.expend_amount AS expend_amount, --支出(交易)
                    bt.customer_price AS customer_price, --单均价
                    bt.valid_order_count AS valid_order_count, --有效订单数
                    bt.gross_amount AS gross_amount, --商品流水
                    bt.net_amount AS net_amount, --商品实收
                    bt.discount_amount AS discount_amount, --折扣金额
                    bt.merchant_allowance AS merchant_allowance, --商家活动补贴
                    bt.package_fee AS package_fee, --包装费
                    bt.delivery_fee AS delivery_fee, --配送费原价
                    bt.send_fee AS send_fee, --配送费用户实际支付
                    bt.merchant_send_fee AS merchant_send_fee, --配送费商家承担
                    bt.platform_send_fee AS platform_send_fee, --配送费平台承担
                    bt.commission AS commission, --佣金
                    bt.service_fee AS service_fee, --服务费
                    bt.tip AS tip, --小费
                    bt.receivable AS receivable, --应付
                    bt.pay_amount AS pay_amount, --实付
                    bt.rounding AS rounding, --抹零
                    bt.overflow_amount AS overflow_amount, --溢收
                    bt.change_amount AS change_amount, --找零
                    bt.transfer_real_amount AS transfer_real_amount, --财务实收金额(转换后)
                    bt.discount_contribute AS discount_contribute, --优惠组成
                    bt.discount_merchant_contribute AS discount_merchant_contribute, --优惠组成-折扣（财务）
                    bt.pay_merchant_contribute AS pay_merchant_contribute, --优惠组成-支付
                    bt.real_amount_discount AS real_amount_discount, --实收组成-折扣（财务）
                    bt.real_amount_payment AS real_amount_payment, --实收组成-支付（财务）
					bt.child, --渠道合计
					bt.tax_fee, --税费
					0 AS total --Summary时的汇总条数
				FROM
					 base_tickets_for_channel bt
						LEFT JOIN business_days bd
							ON bt.region_id = bd.region_id
				ORDER BY bt.region_id;
		`
		summarySQL = `
 		--门店渠道汇总信息summary
			WITH base_tickets AS (
					SELECT
						coalesce(sum(fact.amount_0), 0) as business_amount, --营业额
                        coalesce(sum(fact.amount_2-fact.rounding), 0) as real_amount, --实收金额(交易)
                        coalesce(sum(fact.amount_1+fact.rounding), 0) as expend_amount, --支出(交易)
                        case when coalesce(sum(eticket_count), 0)=0 then 0 else coalesce(sum(amount_0), 0)/coalesce(sum(eticket_count), 0) end as customer_price, --单均价
                        coalesce(sum(fact.eticket_count), 0) as valid_order_count, --有效订单数
                        coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
                        coalesce(sum(fact.net_amount), 0) as net_amount, --商品实收
                        coalesce(sum(fact.discount_amount), 0) as discount_amount, --折扣金额
                        coalesce(sum(fact.merchant_discount_amount+fact.store_discount_amount), 0) as merchant_allowance, --商家活动补贴
                        coalesce(sum(fact.package_fee), 0) as package_fee, --包装费
                        coalesce(sum(fact.delivery_fee), 0) as delivery_fee, --配送费原价
                        coalesce(sum(fact.send_fee), 0) as send_fee, --配送费用户实际支付
                        coalesce(sum(fact.merchant_send_fee), 0) as merchant_send_fee, --配送费商家承担
                        coalesce(sum(fact.platform_send_fee), 0) as platform_send_fee, --配送费平台承担
                        coalesce(sum(fact.commission), 0) as commission, --佣金
                        coalesce(sum(fact.service_fee), 0) as service_fee, --服务费
                        coalesce(sum(fact.tip), 0) as tip, --小费
                        coalesce(sum(fact.amount_4), 0) as receivable, --应付金额
                        coalesce(sum(fact.pay_amount), 0) as pay_amount, --实付金额
                        coalesce(sum(fact.rounding), 0) as rounding, --抹零
                        coalesce(sum(fact.overflow_amount), 0) as overflow_amount, --溢收
                        coalesce(sum(fact.change_amount), 0) as change_amount, --找零
                        coalesce(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                        coalesce(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute,--优惠组成(财务)
                        coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
                        coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
                        coalesce(SUM(discount_buyer_contribute+discount_other_contribute), 0) AS real_amount_discount, --实收组成-折扣（财务）
                        coalesce(SUM(pay_buyer_contribute+pay_other_contribute), 0) AS real_amount_payment, --实收组成-支付（财务）
						coalesce(SUM(fact.tax_fee), 0) as tax_fee --	税费
					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						and {REGION_ID} > 0
					GROUP BY
						{REGION_ID}
					ORDER BY
						{REGION_ID}
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数

					coalesce(SUM(business_amount), 0) AS business_amount, --营业额
                    coalesce(SUM(real_amount), 0) AS real_amount, --实收金额(交易)
                    coalesce(SUM(expend_amount), 0) AS expend_amount, --支出(交易)
				    coalesce(case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end, 0) as customer_price, --单均价
                    coalesce(SUM(valid_order_count), 0) AS valid_order_count, --有效订单数
                    coalesce(SUM(gross_amount), 0) AS gross_amount, --商品流水
                    coalesce(SUM(net_amount), 0) AS net_amount, --商品实收
                    coalesce(SUM(discount_amount), 0) AS discount_amount, --折扣金额
                    coalesce(SUM(merchant_allowance), 0) AS merchant_allowance, --商家活动补贴
                    coalesce(SUM(package_fee), 0) AS package_fee, --包装费
                    coalesce(SUM(delivery_fee), 0) AS delivery_fee, --配送费原价
                    coalesce(SUM(send_fee), 0) AS send_fee, --配送费用户实际支付
                    coalesce(SUM(merchant_send_fee), 0) AS merchant_send_fee, --配送费商家承担
                    coalesce(SUM(platform_send_fee), 0) AS platform_send_fee, --配送费平台承担
                    coalesce(SUM(commission), 0) AS commission, --佣金
                    coalesce(SUM(service_fee), 0) AS service_fee, --服务费
                    coalesce(SUM(tip), 0) AS tip, --小费
                    coalesce(SUM(receivable), 0) AS receivable, --应付
                    coalesce(SUM(pay_amount), 0) AS pay_amount, --实付
                    coalesce(SUM(rounding), 0) AS rounding, --抹零
                    coalesce(SUM(overflow_amount), 0) AS overflow_amount, --溢收
                    coalesce(SUM(change_amount), 0) AS change_amount, --找零
                    coalesce(SUM(transfer_real_amount), 0) AS transfer_real_amount, --财务实收金额(转换后)
                    coalesce(SUM(discount_contribute), 0) AS discount_contribute, --优惠组成
                    coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		            coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
	                coalesce(SUM(real_amount_discount), 0) AS real_amount_discount, --实收组成-折扣（财务）
	                coalesce(SUM(real_amount_payment), 0) AS real_amount_payment, --实收组成-支付（财务）
					coalesce(SUM(tax_fee), 0) as tax_fee, --	税费

					'[]'::json AS child, --渠道合计

					COUNT(1) AS total --Summary时的汇总条数
				FROM
					base_tickets
		`
	} else {
		rowSQL = `
            --门店渠道详细信息rows
                with business_days as (
                    select
                        region_id,count(1) as days
                    from(
                        select
							{REGION_ID} as region_id
                        from sales_ticket_amounts fact
                            left join store_caches store_caches
                                on store_caches.id = fact.store_id
                        where
                            {WHERE}
							and {REGION_ID} > 0
                        group by
                            {REGION_ID}
                    )T
                    group by region_id
                ), base_limit as(
                    select
                        fact.bus_date as bus_date,
                        {REGION_ID} as region_id
                    from sales_ticket_amounts fact
                        left join store_caches store_caches on fact.store_id = store_caches.id
                    where
                        {WHERE}
						and {REGION_ID} > 0
                    group by fact.bus_date, {REGION_ID}
                    order by fact.bus_date desc, {REGION_ID}
                    {LIMIT}
                ),base_tickets as (
                    select
                        fact.bus_date as bus_date,
                        coalesce({REGION_ID}, 0) as region_id,
                        COALESCE(max(store_caches.store_type), '') AS store_type,
                        coalesce(fact.channel_id, 0) as channel_id,
                        coalesce(fact.order_type, '') as order_type,
                        coalesce(sum(fact.amount_0), 0) as business_amount, --营业额
                        coalesce(sum(fact.amount_2-fact.rounding), 0) as real_amount, --实收金额(交易)
                        coalesce(sum(fact.amount_1+fact.rounding), 0) as expend_amount, --支出(交易)
                        case when coalesce(sum(eticket_count), 0)=0 then 0 else coalesce(sum(amount_0), 0)/coalesce(sum(eticket_count), 0) end as customer_price, --单均价
                        coalesce(sum(fact.eticket_count), 0) as valid_order_count, --有效订单数
                        coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
                        coalesce(sum(fact.net_amount), 0) as net_amount, --商品实收
                        coalesce(sum(fact.discount_amount), 0) as discount_amount, --折扣金额
                        coalesce(sum(fact.merchant_discount_amount+fact.store_discount_amount), 0) as merchant_allowance, --商家活动补贴
                        coalesce(sum(fact.package_fee), 0) as package_fee, --包装费
                        coalesce(sum(fact.delivery_fee), 0) as delivery_fee, --配送费原价
                        coalesce(sum(fact.send_fee), 0) as send_fee, --配送费用户实际支付
                        coalesce(sum(fact.merchant_send_fee), 0) as merchant_send_fee, --配送费商家承担
                        coalesce(sum(fact.platform_send_fee), 0) as platform_send_fee, --配送费平台承担
                        coalesce(sum(fact.commission), 0) as commission, --佣金
                        coalesce(sum(fact.service_fee), 0) as service_fee, --服务费
                        coalesce(sum(fact.tip), 0) as tip, --小费
                        coalesce(sum(fact.amount_4), 0) as receivable, --应付金额
                        coalesce(sum(fact.pay_amount), 0) as pay_amount, --实付金额
                        coalesce(sum(fact.rounding), 0) as rounding, --抹零
                        coalesce(sum(fact.overflow_amount), 0) as overflow_amount, --溢收
                        coalesce(sum(fact.change_amount), 0) as change_amount, --找零
                        coalesce(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                        coalesce(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute,--优惠组成(财务)
                        coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
                        coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
                        coalesce(SUM(discount_buyer_contribute+discount_other_contribute), 0) AS real_amount_discount, --实收组成-折扣（财务）
                        coalesce(SUM(pay_buyer_contribute+pay_other_contribute), 0) AS real_amount_payment, --实收组成-支付（财务）
						coalesce(SUM(fact.tax_fee), 0) as tax_fee --	税费
            
                    from sales_ticket_amounts fact
                      left join store_caches store_caches
                        on fact.store_id = store_caches.id
                      inner join base_limit
                        on fact.bus_date = base_limit.bus_date and {REGION_ID} = base_limit.region_id
                    where
                        {WHERE}
						and {REGION_ID} > 0
                    group by fact.bus_date, {REGION_ID}, fact.channel_id, fact.order_type
                    order by fact.bus_date desc, {REGION_ID}
                ), base_tickets_for_channel_and_order_type AS (
					SELECT
						bus_date,
						region_id,
						store_type,
						channel_id,--渠道
                        SUM(business_amount) AS business_amount, --营业额
                        SUM(real_amount) AS real_amount, --实收金额(交易)
                        SUM(expend_amount) AS expend_amount, --支出(交易)
					    case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end as customer_price, --单均价
                        SUM(valid_order_count) AS valid_order_count, --有效订单数
                        SUM(gross_amount) AS gross_amount, --商品流水
                        SUM(net_amount) AS net_amount, --商品实收
                        SUM(discount_amount) AS discount_amount, --折扣金额
                        SUM(merchant_allowance) AS merchant_allowance, --商家活动补贴
                        SUM(package_fee) AS package_fee, --包装费
                        SUM(delivery_fee) AS delivery_fee, --配送费原价
                        SUM(send_fee) AS send_fee, --配送费用户实际支付
                        SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
                        SUM(platform_send_fee) AS platform_send_fee, --配送费平台承担
                        SUM(commission) AS commission, --佣金
                        SUM(service_fee) AS service_fee, --服务费
                        SUM(tip) AS tip, --小费
                        SUM(receivable) AS receivable, --应付
                        SUM(pay_amount) AS pay_amount, --实付
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(discount_contribute) AS discount_contribute, --优惠组成
                        SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		                SUM(pay_merchant_contribute) AS pay_merchant_contribute, --优惠组成-支付
		                SUM(real_amount_discount) AS real_amount_discount, --实收组成-折扣（财务）
		                SUM(real_amount_payment) AS real_amount_payment, --实收组成-支付（财务）
						SUM(tax_fee) AS tax_fee, --税费
						JSON_AGG(
							JSON_BUILD_OBJECT(
							       'order_type', order_type, --物流类型
                                   'business_amount', business_amount, --营业额
                                   'real_amount', real_amount, --实收金额(交易)
                                   'expend_amount', expend_amount, --支出(交易)
							        'customer_price', customer_price, --单均价
                                   'valid_order_count', valid_order_count, --有效订单数
                                   'gross_amount', gross_amount, --商品流水
                                   'net_amount', net_amount, --商品实收
                                   'discount_amount', discount_amount, --折扣金额
                                   'merchant_allowance', merchant_allowance, --商家活动补贴
                                   'package_fee', package_fee, --包装费
                                   'delivery_fee', delivery_fee, --配送费原价
                                   'send_fee', send_fee, --配送费用户实际支付
                                   'merchant_send_fee', merchant_send_fee, --配送费商家承担
                                   'platform_send_fee', platform_send_fee, --配送费平台承担
                                   'commission', commission, --佣金
                                   'service_fee', service_fee, --服务费
                                   'tip', tip, --小费
                                   'receivable', receivable, --应付
                                   'pay_amount', pay_amount, --实付
                                   'rounding', rounding, --抹零
                                   'overflow_amount', overflow_amount, --溢收
                                   'change_amount', change_amount, --找零
                                   'transfer_real_amount', transfer_real_amount, --财务实收金额(转换后)
                                   'discount_contribute', discount_contribute, --优惠组成
                                   'discount_merchant_contribute', discount_merchant_contribute, --优惠组成-折扣（财务）
                                   'pay_merchant_contribute', pay_merchant_contribute, --优惠组成-支付
                                   'real_amount_discount', real_amount_discount, --实收组成-折扣（财务）
                                   'real_amount_payment', real_amount_payment, --实收组成-支付（财务）
									'tax_fee',tax_fee -- 税费
							)
						) AS "child"
					FROM  base_tickets
					GROUP BY
						bus_date, region_id, store_type, channel_id
				), base_tickets_for_channel AS (
                SELECT bus_date,
                        region_id,
                        max(store_type) as store_type,
                        SUM(business_amount) AS business_amount, --营业额
                        SUM(real_amount) AS real_amount, --实收金额(交易)
                        SUM(expend_amount) AS expend_amount, --支出(交易)
					    case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end as customer_price, --单均价
                        SUM(valid_order_count) AS valid_order_count, --有效订单数
                        SUM(gross_amount) AS gross_amount, --商品流水
                        SUM(net_amount) AS net_amount, --商品实收
                        SUM(discount_amount) AS discount_amount, --折扣金额
                        SUM(merchant_allowance) AS merchant_allowance, --商家活动补贴
                        SUM(package_fee) AS package_fee, --包装费
                        SUM(delivery_fee) AS delivery_fee, --配送费原价
                        SUM(send_fee) AS send_fee, --配送费用户实际支付
                        SUM(merchant_send_fee) AS merchant_send_fee, --配送费商家承担
                        SUM(platform_send_fee) AS platform_send_fee, --配送费平台承担
                        SUM(commission) AS commission, --佣金
                        SUM(service_fee) AS service_fee, --服务费
                        SUM(tip) AS tip, --小费
                        SUM(receivable) AS receivable, --应付
                        SUM(pay_amount) AS pay_amount, --实付
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(discount_contribute) AS discount_contribute, --优惠组成
                        SUM(discount_merchant_contribute) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		                SUM(pay_merchant_contribute) AS pay_merchant_contribute, --优惠组成-支付
		                SUM(real_amount_discount) AS real_amount_discount, --实收组成-折扣（财务）
		                SUM(real_amount_payment) AS real_amount_payment, --实收组成-支付（财务）
						SUM(tax_fee) AS tax_fee, --税费
                       JSON_AGG(
                               JSON_BUILD_OBJECT(
                                       'channel_id', channel_id, --渠道id
                                       'channel_code', '', --渠道code
                                       'channel_name', '', --渠道名称
                                       'business_amount', business_amount, --营业额
                                       'real_amount', real_amount, --实收金额(交易)
                                       'expend_amount', expend_amount, --支出(交易)
                                       'customer_price', customer_price, --单均价
                                       'valid_order_count', valid_order_count, --有效订单数
                                       'gross_amount', gross_amount, --商品流水
                                       'net_amount', net_amount, --商品实收
                                       'discount_amount', discount_amount, --折扣金额
                                       'merchant_allowance', merchant_allowance, --商家活动补贴
                                       'package_fee', package_fee, --包装费
                                       'delivery_fee', delivery_fee, --配送费原价
                                       'send_fee', send_fee, --配送费用户实际支付
                                       'merchant_send_fee', merchant_send_fee, --配送费商家承担
                                       'platform_send_fee', platform_send_fee, --配送费平台承担
                                       'commission', commission, --佣金
                                       'service_fee', service_fee, --服务费
                                       'tip', tip, --小费
                                       'receivable', receivable, --应付
                                       'pay_amount', pay_amount, --实付
                                       'rounding', rounding, --抹零
                                       'overflow_amount', overflow_amount, --溢收
                                       'change_amount', change_amount, --找零
                                       'transfer_real_amount', transfer_real_amount, --财务实收金额(转换后)
                                       'discount_contribute', discount_contribute, --优惠组成
                                       'discount_merchant_contribute', discount_merchant_contribute, --优惠组成-折扣（财务）
                                       'pay_merchant_contribute', pay_merchant_contribute, --优惠组成-支付
                                       'real_amount_discount', real_amount_discount, --实收组成-折扣（财务）
                                       'real_amount_payment', real_amount_payment, --实收组成-支付（财务）
										'tax_fee',tax_fee, --税费
                                       'child', child
                                   )
                           ) AS "child"
                FROM base_tickets_for_channel_and_order_type
                GROUP BY bus_date, region_id
            )
			SELECT
					to_char(bt.bus_date,'YYYY-MM-DD') AS bus_date,
					bt.region_id,
					bt.store_type,
					bd.days AS business_days,

                    bt.business_amount AS business_amount, --营业额
                    bt.real_amount AS real_amount, --实收金额(交易)
                    bt.expend_amount AS expend_amount, --支出(交易)
                    bt.customer_price AS customer_price, --单均价
                    bt.valid_order_count AS valid_order_count, --有效订单数
                    bt.gross_amount AS gross_amount, --商品流水
                    bt.net_amount AS net_amount, --商品实收
                    bt.discount_amount AS discount_amount, --折扣金额
                    bt.merchant_allowance AS merchant_allowance, --商家活动补贴
                    bt.package_fee AS package_fee, --包装费
                    bt.delivery_fee AS delivery_fee, --配送费原价
                    bt.send_fee AS send_fee, --配送费用户实际支付
                    bt.merchant_send_fee AS merchant_send_fee, --配送费商家承担
                    bt.platform_send_fee AS platform_send_fee, --配送费平台承担
                    bt.commission AS commission, --佣金
                    bt.service_fee AS service_fee, --服务费
                    bt.tip AS tip, --小费
                    bt.receivable AS receivable, --应付
                    bt.pay_amount AS pay_amount, --实付
                    bt.rounding AS rounding, --抹零
                    bt.overflow_amount AS overflow_amount, --溢收
                    bt.change_amount AS change_amount, --找零
                    bt.transfer_real_amount AS transfer_real_amount, --财务实收金额(转换后)
                    bt.discount_contribute AS discount_contribute, --优惠组成
                    bt.discount_merchant_contribute AS discount_merchant_contribute, --优惠组成-折扣（财务）
                    bt.pay_merchant_contribute AS pay_merchant_contribute, --优惠组成-支付
                    bt.real_amount_discount AS real_amount_discount, --实收组成-折扣（财务）
                    bt.real_amount_payment AS real_amount_payment, --实收组成-支付（财务）
					bt.tax_fee  AS tax_fee, --税费
					bt.child, --渠道合计
					0 AS total --Summary时的汇总条数
				FROM
					 base_tickets_for_channel bt
						LEFT JOIN business_days bd
							ON bt.region_id = bd.region_id
				ORDER BY bt.bus_date DESC, bt.region_id;
		`
		summarySQL = `
 		--门店渠道详细信息summary
			WITH base_tickets AS (
					SELECT
						coalesce(sum(fact.amount_0), 0) as business_amount, --营业额
                        coalesce(sum(fact.amount_2-fact.rounding), 0) as real_amount, --实收金额(交易)
                        coalesce(sum(fact.amount_1+fact.rounding), 0) as expend_amount, --支出(交易)
                        case when coalesce(sum(eticket_count), 0)=0 then 0 else coalesce(sum(amount_0), 0)/coalesce(sum(eticket_count), 0) end as customer_price, --单均价
                        coalesce(sum(fact.eticket_count), 0) as valid_order_count, --有效订单数
                        coalesce(sum(fact.gross_amount), 0) as gross_amount, --商品流水
                        coalesce(sum(fact.net_amount), 0) as net_amount, --商品实收
                        coalesce(sum(fact.discount_amount), 0) as discount_amount, --折扣金额
                        coalesce(sum(fact.merchant_discount_amount+fact.store_discount_amount), 0) as merchant_allowance, --商家活动补贴
                        coalesce(sum(fact.package_fee), 0) as package_fee, --包装费
                        coalesce(sum(fact.delivery_fee), 0) as delivery_fee, --配送费原价
                        coalesce(sum(fact.send_fee), 0) as send_fee, --配送费用户实际支付
                        coalesce(sum(fact.merchant_send_fee), 0) as merchant_send_fee, --配送费商家承担
                        coalesce(sum(fact.platform_send_fee), 0) as platform_send_fee, --配送费平台承担
                        coalesce(sum(fact.commission), 0) as commission, --佣金
                        coalesce(sum(fact.service_fee), 0) as service_fee, --服务费
                        coalesce(sum(fact.tip), 0) as tip, --小费
                        coalesce(sum(fact.amount_4), 0) as receivable, --应付金额
                        coalesce(sum(fact.pay_amount), 0) as pay_amount, --实付金额
                        coalesce(sum(fact.rounding), 0) as rounding, --抹零
                        coalesce(sum(fact.overflow_amount), 0) as overflow_amount, --溢收
                        coalesce(sum(fact.change_amount), 0) as change_amount, --找零
                        coalesce(SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)), 0) AS transfer_real_amount,--实收金额(财务)
                        coalesce(SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount), 0) AS discount_contribute,--优惠组成(财务)
                        coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
                        coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
                        coalesce(SUM(discount_buyer_contribute+discount_other_contribute), 0) AS real_amount_discount, --实收组成-折扣（财务）
                        coalesce(SUM(pay_buyer_contribute+pay_other_contribute), 0) AS real_amount_payment, --实收组成-支付（财务）
						coalesce(SUM(fact.tax_fee), 0) as tax_fee --	税费

					FROM
						sales_ticket_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
						and {REGION_ID} > 0
					GROUP BY
						fact.bus_date,
						{REGION_ID}
					ORDER BY
						fact.bus_date DESC,
						{REGION_ID}
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数

					coalesce(SUM(business_amount), 0) AS business_amount, --营业额
                    coalesce(SUM(real_amount), 0) AS real_amount, --实收金额(交易)
                    coalesce(SUM(expend_amount), 0) AS expend_amount, --支出(交易)
				    coalesce(case when sum(valid_order_count)=0 then 0 else sum(business_amount)/sum(valid_order_count) end, 0) as customer_price, --单均价
                    coalesce(SUM(valid_order_count), 0) AS valid_order_count, --有效订单数
                    coalesce(SUM(gross_amount), 0) AS gross_amount, --商品流水
                    coalesce(SUM(net_amount), 0) AS net_amount, --商品实收
                    coalesce(SUM(discount_amount), 0) AS discount_amount, --折扣金额
                    coalesce(SUM(merchant_allowance), 0) AS merchant_allowance, --商家活动补贴
                    coalesce(SUM(package_fee), 0) AS package_fee, --包装费
                    coalesce(SUM(delivery_fee), 0) AS delivery_fee, --配送费原价
                    coalesce(SUM(send_fee), 0) AS send_fee, --配送费用户实际支付
                    coalesce(SUM(merchant_send_fee), 0) AS merchant_send_fee, --配送费商家承担
                    coalesce(SUM(platform_send_fee), 0) AS platform_send_fee, --配送费平台承担
                    coalesce(SUM(commission), 0) AS commission, --佣金
                    coalesce(SUM(service_fee), 0) AS service_fee, --服务费
                    coalesce(SUM(tip), 0) AS tip, --小费
                    coalesce(SUM(receivable), 0) AS receivable, --应付
                    coalesce(SUM(pay_amount), 0) AS pay_amount, --实付
                    coalesce(SUM(rounding), 0) AS rounding, --抹零
                    coalesce(SUM(overflow_amount), 0) AS overflow_amount, --溢收
                    coalesce(SUM(change_amount), 0) AS change_amount, --找零
                    coalesce(SUM(transfer_real_amount), 0) AS transfer_real_amount, --财务实收金额(转换后)
                    coalesce(SUM(discount_contribute), 0) AS discount_contribute, --优惠组成
                    coalesce(SUM(discount_merchant_contribute), 0) AS discount_merchant_contribute, --优惠组成-折扣（财务）
		            coalesce(SUM(pay_merchant_contribute), 0) AS pay_merchant_contribute, --优惠组成-支付
	                coalesce(SUM(real_amount_discount), 0) AS real_amount_discount, --实收组成-折扣（财务）
	                coalesce(SUM(real_amount_payment), 0) AS real_amount_payment, --实收组成-支付（财务）
					coalesce(SUM(tax_fee), 0) as tax_fee, --	税费

					'[]'::json AS child, --渠道合计

					COUNT(1) AS total --Summary时的汇总条数
				FROM
					base_tickets
		`
	}
	whereSQL := generateWhereSQLForStoreChannelSales(condition)
	regionSQL := generateRegionSQLForStoreChannelSales(condition)
	limitSQL := generateLimitOffsetSQLForStoreChannelSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateDateSQLForStoreChannelSales(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	// 作为周期组
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

func generateRegionSQLForStoreChannelSales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForStoreChannelSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL   string // 区域筛选条件
		channelIdSQL   string // 渠道筛选条件
		orderTypeSQL   string // 订单类型筛选条件
		storeTypeSQL   string // 增加门店类型的过滤
		openStatusSQL  string // 增加开店类型的过滤
		orderStatusSQL string // 增加开店类型的过滤
	)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND fact.channel_id = %d",
			condition.ChannelId,
		)
	}
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND fact.order_type = '%s'",
			condition.OrderType,
		)
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}

	if condition.OrderStatus != "" {
		orderStatusSQL = fmt.Sprintf(`
				AND fact.order_status = '%s'
			`, condition.OrderStatus)
	}

	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL, storeTypeSQL, openStatusSQL, storeSQL, orderStatusSQL)
	return whereSQL
}

func generateLimitOffsetSQLForStoreChannelSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
