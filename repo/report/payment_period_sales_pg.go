package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) PaymentPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.PaymentPeriodResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForPaymentPeriodSales(trackId, condition)
	ch1 := make(chan []*report.PaymentPeriod, 1)
	ch2 := make(chan []*report.PaymentPeriod, 1)
	start := time.Now()
	go queryDBWithChanForPaymentPeriod(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForPaymentPeriod(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.PaymentPeriod
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	var total int64
	if summary != nil {
		total = summary.Total
	}
	return &report.PaymentPeriodResponse{
		Rows:    rows,
		Summary: summary,
		Total:   total,
	}, nil
}

func queryDBWithChanForPaymentPeriod(trackId int64, db *sql.DB, sql string, ch chan []*report.PaymentPeriod) {
	ch <- queryDBForPaymentPeriod(trackId, db, sql)
}

func queryDBForPaymentPeriod(trackId int64, db *sql.DB, sqlStr string) []*report.PaymentPeriod {
	results := make([]*report.PaymentPeriod, 0)
	if sqlStr == "" {
		return []*report.PaymentPeriod{new(report.PaymentPeriod)}
	}

	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.PaymentPeriod)
		var value, val json.RawMessage
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.StoreType,
			&f.BusinessDays,
			&f.Receivable,
			&f.PayAmount,
			&f.TransferRealAmount,
			&f.PaymentTransferAmount,
			&f.Cost,
			&f.TpAllowance,
			&f.Rounding,
			&f.OverflowAmount,
			&f.ChangeAmount,
			&f.ItemCount,
			&f.ItemCountReturned,
			&val,
			&value,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		err = json.Unmarshal(val, &f.Data)
		if err != nil {
			logger.Pre().Error("支付时段data结构体解析失败：", err)
			return results
		}
		err = json.Unmarshal(value, &f.Child)
		if err != nil {
			logger.Pre().Error("支付时段child结构体解析失败：", err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForPaymentPeriodSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	if condition.TagType == "SUMMARY" {
		rowSQL = `
		--支付时段报表汇总信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY bus_date, {REGION_ID}
					ORDER BY bus_date DESC, {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					{REGION_ID} AS region_id
				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID}
				ORDER BY
					{REGION_ID}
				{LIMIT}
			),	base as (
			    select
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
					COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name
			    from sales_payment_amounts fact
			    left join store_caches on fact.store_id = store_caches.id
			    where
			        {WHERE}
			    group by fact.store_id, fact.payment_id
            ),	base_payments AS (
				SELECT
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					base.payment_code AS payment_code,
					base.payment_name AS payment_name,
					extract(hour from fact.order_time) AS hours,
			
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
					COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
					COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
					COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
					COALESCE(SUM(rounding), 0) AS rounding, --抹零
					COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(SUM(change_amount), 0) AS change_amount, --找零
					COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
					COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数

				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						left join base on base.region_id= {REGION_ID} 
							and base.payment_id=fact.payment_id
						INNER JOIN base_limit
							ON {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
				GROUP BY
					{REGION_ID},
				    fact.payment_id,
					base.payment_code,
				    base.payment_name,
					fact.order_time
				ORDER BY
					{REGION_ID}
			), base_payments_for_period AS (
				SELECT
					region_id,
					store_type,
					payment_id,
					payment_code,
					payment_name,

					SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数
			
                    SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                    SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                    SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                    SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                    SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                    SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                    SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                    SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                    SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                    SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                    SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                    SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                    SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                    SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                    SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                    SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                    SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                    SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                    SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                    SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                    SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                    SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                    SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                    SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

                    SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
                    SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
                    SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
                    SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
                    SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
                    SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
                    SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
                    SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
                    SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
                    SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
                    SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
                    SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
                    SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
                    SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
                    SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
                    SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
                    SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
                    SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
                    SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
                    SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
                    SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
                    SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
                    SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
                    SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

                    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

                    SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
                    SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
                    SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
                    SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
                    SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
                    SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
                    SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
                    SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
                    SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
                    SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
                    SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
                    SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
                    SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
                    SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
                    SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
                    SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
                    SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
                    SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
                    SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
                    SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
                    SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
                    SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
                    SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
                    SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

                    SUM(CASE WHEN hours = 0 THEN cost ELSE 0 END) AS cost_00,
                    SUM(CASE WHEN hours = 1 THEN cost ELSE 0 END) AS cost_01,
                    SUM(CASE WHEN hours = 2 THEN cost ELSE 0 END) AS cost_02,
                    SUM(CASE WHEN hours = 3 THEN cost ELSE 0 END) AS cost_03,
                    SUM(CASE WHEN hours = 4 THEN cost ELSE 0 END) AS cost_04,
                    SUM(CASE WHEN hours = 5 THEN cost ELSE 0 END) AS cost_05,
                    SUM(CASE WHEN hours = 6 THEN cost ELSE 0 END) AS cost_06,
                    SUM(CASE WHEN hours = 7 THEN cost ELSE 0 END) AS cost_07,
                    SUM(CASE WHEN hours = 8 THEN cost ELSE 0 END) AS cost_08,
                    SUM(CASE WHEN hours = 9 THEN cost ELSE 0 END) AS cost_09,
                    SUM(CASE WHEN hours = 10 THEN cost ELSE 0 END) AS cost_10,
                    SUM(CASE WHEN hours = 11 THEN cost ELSE 0 END) AS cost_11,
                    SUM(CASE WHEN hours = 12 THEN cost ELSE 0 END) AS cost_12,
                    SUM(CASE WHEN hours = 13 THEN cost ELSE 0 END) AS cost_13,
                    SUM(CASE WHEN hours = 14 THEN cost ELSE 0 END) AS cost_14,
                    SUM(CASE WHEN hours = 15 THEN cost ELSE 0 END) AS cost_15,
                    SUM(CASE WHEN hours = 16 THEN cost ELSE 0 END) AS cost_16,
                    SUM(CASE WHEN hours = 17 THEN cost ELSE 0 END) AS cost_17,
                    SUM(CASE WHEN hours = 18 THEN cost ELSE 0 END) AS cost_18,
                    SUM(CASE WHEN hours = 19 THEN cost ELSE 0 END) AS cost_19,
                    SUM(CASE WHEN hours = 20 THEN cost ELSE 0 END) AS cost_20,
                    SUM(CASE WHEN hours = 21 THEN cost ELSE 0 END) AS cost_21,
                    SUM(CASE WHEN hours = 22 THEN cost ELSE 0 END) AS cost_22,
                    SUM(CASE WHEN hours = 23 THEN cost ELSE 0 END) AS cost_23,

                    SUM(CASE WHEN hours = 0 THEN tp_allowance ELSE 0 END) AS tp_allowance_00,
                    SUM(CASE WHEN hours = 1 THEN tp_allowance ELSE 0 END) AS tp_allowance_01,
                    SUM(CASE WHEN hours = 2 THEN tp_allowance ELSE 0 END) AS tp_allowance_02,
                    SUM(CASE WHEN hours = 3 THEN tp_allowance ELSE 0 END) AS tp_allowance_03,
                    SUM(CASE WHEN hours = 4 THEN tp_allowance ELSE 0 END) AS tp_allowance_04,
                    SUM(CASE WHEN hours = 5 THEN tp_allowance ELSE 0 END) AS tp_allowance_05,
                    SUM(CASE WHEN hours = 6 THEN tp_allowance ELSE 0 END) AS tp_allowance_06,
                    SUM(CASE WHEN hours = 7 THEN tp_allowance ELSE 0 END) AS tp_allowance_07,
                    SUM(CASE WHEN hours = 8 THEN tp_allowance ELSE 0 END) AS tp_allowance_08,
                    SUM(CASE WHEN hours = 9 THEN tp_allowance ELSE 0 END) AS tp_allowance_09,
                    SUM(CASE WHEN hours = 10 THEN tp_allowance ELSE 0 END) AS tp_allowance_10,
                    SUM(CASE WHEN hours = 11 THEN tp_allowance ELSE 0 END) AS tp_allowance_11,
                    SUM(CASE WHEN hours = 12 THEN tp_allowance ELSE 0 END) AS tp_allowance_12,
                    SUM(CASE WHEN hours = 13 THEN tp_allowance ELSE 0 END) AS tp_allowance_13,
                    SUM(CASE WHEN hours = 14 THEN tp_allowance ELSE 0 END) AS tp_allowance_14,
                    SUM(CASE WHEN hours = 15 THEN tp_allowance ELSE 0 END) AS tp_allowance_15,
                    SUM(CASE WHEN hours = 16 THEN tp_allowance ELSE 0 END) AS tp_allowance_16,
                    SUM(CASE WHEN hours = 17 THEN tp_allowance ELSE 0 END) AS tp_allowance_17,
                    SUM(CASE WHEN hours = 18 THEN tp_allowance ELSE 0 END) AS tp_allowance_18,
                    SUM(CASE WHEN hours = 19 THEN tp_allowance ELSE 0 END) AS tp_allowance_19,
                    SUM(CASE WHEN hours = 20 THEN tp_allowance ELSE 0 END) AS tp_allowance_20,
                    SUM(CASE WHEN hours = 21 THEN tp_allowance ELSE 0 END) AS tp_allowance_21,
                    SUM(CASE WHEN hours = 22 THEN tp_allowance ELSE 0 END) AS tp_allowance_22,
                    SUM(CASE WHEN hours = 23 THEN tp_allowance ELSE 0 END) AS tp_allowance_23,

                    SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
                    SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
                    SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
                    SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
                    SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
                    SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
                    SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
                    SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
                    SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
                    SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
                    SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
                    SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
                    SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
                    SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
                    SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
                    SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
                    SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
                    SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
                    SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
                    SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
                    SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
                    SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
                    SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
                    SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

                    SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
                    SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
                    SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
                    SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
                    SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
                    SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
                    SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
                    SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
                    SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
                    SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
                    SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
                    SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
                    SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
                    SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
                    SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
                    SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
                    SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
                    SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
                    SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
                    SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
                    SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
                    SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
                    SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
                    SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

                    SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
                    SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
                    SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
                    SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
                    SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
                    SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
                    SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
                    SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
                    SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
                    SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
                    SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
                    SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
                    SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
                    SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
                    SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
                    SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
                    SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
                    SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
                    SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
                    SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
                    SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
                    SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
                    SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
                    SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23,

                    SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
                    SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
                    SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
                    SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
                    SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
                    SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
                    SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
                    SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
                    SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
                    SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
                    SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
                    SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
                    SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
                    SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
                    SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
                    SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
                    SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
                    SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
                    SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
                    SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
                    SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
                    SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
                    SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
                    SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

                    SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
                    SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
                    SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
                    SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
                    SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
                    SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
                    SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
                    SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
                    SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
                    SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
                    SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
                    SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
                    SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
                    SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
                    SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
                    SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
                    SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
                    SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
                    SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
                    SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
                    SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
                    SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
                    SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
                    SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23

				FROM base_payments
				GROUP BY
					region_id,
					store_type,
					payment_id,
					payment_code,
					payment_name
			), base_payments_by_date_and_region_for_payment AS (
				SELECT
					region_id,
					max(store_type) as store_type,
			
					SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数

                    JSON_BUILD_OBJECT(
                            'h00', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_00),
                                    'pay_amount', SUM(pay_amount_00),
                                    'transfer_real_amount', SUM(transfer_real_amount_00),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_00),
                                    'cost', SUM(cost_00),
                                    'tp_allowance', SUM(tp_allowance_00),
                                    'rounding', SUM(rounding_00),
                                    'overflow_amount', SUM(overflow_amount_00),
                                    'change_amount', SUM(change_amount_00),
                                    'item_count', SUM(item_count_00),
                                    'item_count_returned', SUM(item_count_returned_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_01),
                                    'pay_amount', SUM(pay_amount_01),
                                    'transfer_real_amount', SUM(transfer_real_amount_01),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_01),
                                    'cost', SUM(cost_01),
                                    'tp_allowance', SUM(tp_allowance_01),
                                    'rounding', SUM(rounding_01),
                                    'overflow_amount', SUM(overflow_amount_01),
                                    'change_amount', SUM(change_amount_01),
                                    'item_count', SUM(item_count_01),
                                    'item_count_returned', SUM(item_count_returned_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_02),
                                    'pay_amount', SUM(pay_amount_02),
                                    'transfer_real_amount', SUM(transfer_real_amount_02),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_02),
                                    'cost', SUM(cost_02),
                                    'tp_allowance', SUM(tp_allowance_02),
                                    'rounding', SUM(rounding_02),
                                    'overflow_amount', SUM(overflow_amount_02),
                                    'change_amount', SUM(change_amount_02),
                                    'item_count', SUM(item_count_02),
                                    'item_count_returned', SUM(item_count_returned_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_03),
                                    'pay_amount', SUM(pay_amount_03),
                                    'transfer_real_amount', SUM(transfer_real_amount_03),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_03),
                                    'cost', SUM(cost_03),
                                    'tp_allowance', SUM(tp_allowance_03),
                                    'rounding', SUM(rounding_03),
                                    'overflow_amount', SUM(overflow_amount_03),
                                    'change_amount', SUM(change_amount_03),
                                    'item_count', SUM(item_count_03),
                                    'item_count_returned', SUM(item_count_returned_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_04),
                                    'pay_amount', SUM(pay_amount_04),
                                    'transfer_real_amount', SUM(transfer_real_amount_04),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_04),
                                    'cost', SUM(cost_04),
                                    'tp_allowance', SUM(tp_allowance_04),
                                    'rounding', SUM(rounding_04),
                                    'overflow_amount', SUM(overflow_amount_04),
                                    'change_amount', SUM(change_amount_04),
                                    'item_count', SUM(item_count_04),
                                    'item_count_returned', SUM(item_count_returned_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_05),
                                    'pay_amount', SUM(pay_amount_05),
                                    'transfer_real_amount', SUM(transfer_real_amount_05),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_05),
                                    'cost', SUM(cost_05),
                                    'tp_allowance', SUM(tp_allowance_05),
                                    'rounding', SUM(rounding_05),
                                    'overflow_amount', SUM(overflow_amount_05),
                                    'change_amount', SUM(change_amount_05),
                                    'item_count', SUM(item_count_05),
                                    'item_count_returned', SUM(item_count_returned_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_06),
                                    'pay_amount', SUM(pay_amount_06),
                                    'transfer_real_amount', SUM(transfer_real_amount_06),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_06),
                                    'cost', SUM(cost_06),
                                    'tp_allowance', SUM(tp_allowance_06),
                                    'rounding', SUM(rounding_06),
                                    'overflow_amount', SUM(overflow_amount_06),
                                    'change_amount', SUM(change_amount_06),
                                    'item_count', SUM(item_count_06),
                                    'item_count_returned', SUM(item_count_returned_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_07),
                                    'pay_amount', SUM(pay_amount_07),
                                    'transfer_real_amount', SUM(transfer_real_amount_07),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_07),
                                    'cost', SUM(cost_07),
                                    'tp_allowance', SUM(tp_allowance_07),
                                    'rounding', SUM(rounding_07),
                                    'overflow_amount', SUM(overflow_amount_07),
                                    'change_amount', SUM(change_amount_07),
                                    'item_count', SUM(item_count_07),
                                    'item_count_returned', SUM(item_count_returned_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_08),
                                    'pay_amount', SUM(pay_amount_08),
                                    'transfer_real_amount', SUM(transfer_real_amount_08),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_08),
                                    'cost', SUM(cost_08),
                                    'tp_allowance', SUM(tp_allowance_08),
                                    'rounding', SUM(rounding_08),
                                    'overflow_amount', SUM(overflow_amount_08),
                                    'change_amount', SUM(change_amount_08),
                                    'item_count', SUM(item_count_08),
                                    'item_count_returned', SUM(item_count_returned_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_09),
                                    'pay_amount', SUM(pay_amount_09),
                                    'transfer_real_amount', SUM(transfer_real_amount_09),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_09),
                                    'cost', SUM(cost_09),
                                    'tp_allowance', SUM(tp_allowance_09),
                                    'rounding', SUM(rounding_09),
                                    'overflow_amount', SUM(overflow_amount_09),
                                    'change_amount', SUM(change_amount_09),
                                    'item_count', SUM(item_count_09),
                                    'item_count_returned', SUM(item_count_returned_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_10),
                                    'pay_amount', SUM(pay_amount_10),
                                    'transfer_real_amount', SUM(transfer_real_amount_10),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_10),
                                    'cost', SUM(cost_10),
                                    'tp_allowance', SUM(tp_allowance_10),
                                    'rounding', SUM(rounding_10),
                                    'overflow_amount', SUM(overflow_amount_10),
                                    'change_amount', SUM(change_amount_10),
                                    'item_count', SUM(item_count_10),
                                    'item_count_returned', SUM(item_count_returned_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_11),
                                    'pay_amount', SUM(pay_amount_11),
                                    'transfer_real_amount', SUM(transfer_real_amount_11),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_11),
                                    'cost', SUM(cost_11),
                                    'tp_allowance', SUM(tp_allowance_11),
                                    'rounding', SUM(rounding_11),
                                    'overflow_amount', SUM(overflow_amount_11),
                                    'change_amount', SUM(change_amount_11),
                                    'item_count', SUM(item_count_11),
                                    'item_count_returned', SUM(item_count_returned_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_12),
                                    'pay_amount', SUM(pay_amount_12),
                                    'transfer_real_amount', SUM(transfer_real_amount_12),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_12),
                                    'cost', SUM(cost_12),
                                    'tp_allowance', SUM(tp_allowance_12),
                                    'rounding', SUM(rounding_12),
                                    'overflow_amount', SUM(overflow_amount_12),
                                    'change_amount', SUM(change_amount_12),
                                    'item_count', SUM(item_count_12),
                                    'item_count_returned', SUM(item_count_returned_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_13),
                                    'pay_amount', SUM(pay_amount_13),
                                    'transfer_real_amount', SUM(transfer_real_amount_13),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_13),
                                    'cost', SUM(cost_13),
                                    'tp_allowance', SUM(tp_allowance_13),
                                    'rounding', SUM(rounding_13),
                                    'overflow_amount', SUM(overflow_amount_13),
                                    'change_amount', SUM(change_amount_13),
                                    'item_count', SUM(item_count_13),
                                    'item_count_returned', SUM(item_count_returned_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_14),
                                    'pay_amount', SUM(pay_amount_14),
                                    'transfer_real_amount', SUM(transfer_real_amount_14),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_14),
                                    'cost', SUM(cost_14),
                                    'tp_allowance', SUM(tp_allowance_14),
                                    'rounding', SUM(rounding_14),
                                    'overflow_amount', SUM(overflow_amount_14),
                                    'change_amount', SUM(change_amount_14),
                                    'item_count', SUM(item_count_14),
                                    'item_count_returned', SUM(item_count_returned_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_15),
                                    'pay_amount', SUM(pay_amount_15),
                                    'transfer_real_amount', SUM(transfer_real_amount_15),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_15),
                                    'cost', SUM(cost_15),
                                    'tp_allowance', SUM(tp_allowance_15),
                                    'rounding', SUM(rounding_15),
                                    'overflow_amount', SUM(overflow_amount_15),
                                    'change_amount', SUM(change_amount_15),
                                    'item_count', SUM(item_count_15),
                                    'item_count_returned', SUM(item_count_returned_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_16),
                                    'pay_amount', SUM(pay_amount_16),
                                    'transfer_real_amount', SUM(transfer_real_amount_16),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_16),
                                    'cost', SUM(cost_16),
                                    'tp_allowance', SUM(tp_allowance_16),
                                    'rounding', SUM(rounding_16),
                                    'overflow_amount', SUM(overflow_amount_16),
                                    'change_amount', SUM(change_amount_16),
                                    'item_count', SUM(item_count_16),
                                    'item_count_returned', SUM(item_count_returned_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_17),
                                    'pay_amount', SUM(pay_amount_17),
                                    'transfer_real_amount', SUM(transfer_real_amount_17),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_17),
                                    'cost', SUM(cost_17),
                                    'tp_allowance', SUM(tp_allowance_17),
                                    'rounding', SUM(rounding_17),
                                    'overflow_amount', SUM(overflow_amount_17),
                                    'change_amount', SUM(change_amount_17),
                                    'item_count', SUM(item_count_17),
                                    'item_count_returned', SUM(item_count_returned_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_18),
                                    'pay_amount', SUM(pay_amount_18),
                                    'transfer_real_amount', SUM(transfer_real_amount_18),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_18),
                                    'cost', SUM(cost_18),
                                    'tp_allowance', SUM(tp_allowance_18),
                                    'rounding', SUM(rounding_18),
                                    'overflow_amount', SUM(overflow_amount_18),
                                    'change_amount', SUM(change_amount_18),
                                    'item_count', SUM(item_count_18),
                                    'item_count_returned', SUM(item_count_returned_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_19),
                                    'pay_amount', SUM(pay_amount_19),
                                    'transfer_real_amount', SUM(transfer_real_amount_19),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_19),
                                    'cost', SUM(cost_19),
                                    'tp_allowance', SUM(tp_allowance_19),
                                    'rounding', SUM(rounding_19),
                                    'overflow_amount', SUM(overflow_amount_19),
                                    'change_amount', SUM(change_amount_19),
                                    'item_count', SUM(item_count_19),
                                    'item_count_returned', SUM(item_count_returned_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_20),
                                    'pay_amount', SUM(pay_amount_20),
                                    'transfer_real_amount', SUM(transfer_real_amount_20),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_20),
                                    'cost', SUM(cost_20),
                                    'tp_allowance', SUM(tp_allowance_20),
                                    'rounding', SUM(rounding_20),
                                    'overflow_amount', SUM(overflow_amount_20),
                                    'change_amount', SUM(change_amount_20),
                                    'item_count', SUM(item_count_20),
                                    'item_count_returned', SUM(item_count_returned_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_21),
                                    'pay_amount', SUM(pay_amount_21),
                                    'transfer_real_amount', SUM(transfer_real_amount_21),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_21),
                                    'cost', SUM(cost_21),
                                    'tp_allowance', SUM(tp_allowance_21),
                                    'rounding', SUM(rounding_21),
                                    'overflow_amount', SUM(overflow_amount_21),
                                    'change_amount', SUM(change_amount_21),
                                    'item_count', SUM(item_count_21),
                                    'item_count_returned', SUM(item_count_returned_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_22),
                                    'pay_amount', SUM(pay_amount_22),
                                    'transfer_real_amount', SUM(transfer_real_amount_22),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_22),
                                    'cost', SUM(cost_22),
                                    'tp_allowance', SUM(tp_allowance_22),
                                    'rounding', SUM(rounding_22),
                                    'overflow_amount', SUM(overflow_amount_22),
                                    'change_amount', SUM(change_amount_22),
                                    'item_count', SUM(item_count_22),
                                    'item_count_returned', SUM(item_count_returned_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_23),
                                    'pay_amount', SUM(pay_amount_23),
                                    'transfer_real_amount', SUM(transfer_real_amount_23),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_23),
                                    'cost', SUM(cost_23),
                                    'tp_allowance', SUM(tp_allowance_23),
                                    'rounding', SUM(rounding_23),
                                    'overflow_amount', SUM(overflow_amount_23),
                                    'change_amount', SUM(change_amount_23),
                                    'item_count', SUM(item_count_23),
                                    'item_count_returned', SUM(item_count_returned_23)
                            )
                    ) AS "data",
			

                    JSON_AGG(
                            JSON_BUILD_OBJECT(
                                    'payment_id', payment_id,
                                    'payment_code', payment_code,
                                    'payment_name', payment_name,
                                    'h00', JSON_BUILD_OBJECT(
                                            'receivable', receivable_00,
                                            'pay_amount', pay_amount_00,
                                            'transfer_real_amount', transfer_real_amount_00,
                                            'payment_transfer_amount', payment_transfer_amount_00,
                                            'cost', cost_00,
                                            'tp_allowance', tp_allowance_00,
                                            'rounding', rounding_00,
                                            'overflow_amount', overflow_amount_00,
                                            'change_amount', change_amount_00,
                                            'item_count', item_count_00,
                                            'item_count_returned', item_count_returned_00
                                    ),
                                    'h01', JSON_BUILD_OBJECT(
                                            'receivable', receivable_01,
                                            'pay_amount', pay_amount_01,
                                            'transfer_real_amount', transfer_real_amount_01,
                                            'payment_transfer_amount', payment_transfer_amount_01,
                                            'cost', cost_01,
                                            'tp_allowance', tp_allowance_01,
                                            'rounding', rounding_01,
                                            'overflow_amount', overflow_amount_01,
                                            'change_amount', change_amount_01,
                                            'item_count', item_count_01,
                                            'item_count_returned', item_count_returned_01
                                    ),
                                    'h02', JSON_BUILD_OBJECT(
                                            'receivable', receivable_02,
                                            'pay_amount', pay_amount_02,
                                            'transfer_real_amount', transfer_real_amount_02,
                                            'payment_transfer_amount', payment_transfer_amount_02,
                                            'cost', cost_02,
                                            'tp_allowance', tp_allowance_02,
                                            'rounding', rounding_02,
                                            'overflow_amount', overflow_amount_02,
                                            'change_amount', change_amount_02,
                                            'item_count', item_count_02,
                                            'item_count_returned', item_count_returned_02
                                    ),
                                    'h03', JSON_BUILD_OBJECT(
                                            'receivable', receivable_03,
                                            'pay_amount', pay_amount_03,
                                            'transfer_real_amount', transfer_real_amount_03,
                                            'payment_transfer_amount', payment_transfer_amount_03,
                                            'cost', cost_03,
                                            'tp_allowance', tp_allowance_03,
                                            'rounding', rounding_03,
                                            'overflow_amount', overflow_amount_03,
                                            'change_amount', change_amount_03,
                                            'item_count', item_count_03,
                                            'item_count_returned', item_count_returned_03
                                    ),
                                    'h04', JSON_BUILD_OBJECT(
                                            'receivable', receivable_04,
                                            'pay_amount', pay_amount_04,
                                            'transfer_real_amount', transfer_real_amount_04,
                                            'payment_transfer_amount', payment_transfer_amount_04,
                                            'cost', cost_04,
                                            'tp_allowance', tp_allowance_04,
                                            'rounding', rounding_04,
                                            'overflow_amount', overflow_amount_04,
                                            'change_amount', change_amount_04,
                                            'item_count', item_count_04,
                                            'item_count_returned', item_count_returned_04
                                    ),
                                    'h05', JSON_BUILD_OBJECT(
                                            'receivable', receivable_05,
                                            'pay_amount', pay_amount_05,
                                            'transfer_real_amount', transfer_real_amount_05,
                                            'payment_transfer_amount', payment_transfer_amount_05,
                                            'cost', cost_05,
                                            'tp_allowance', tp_allowance_05,
                                            'rounding', rounding_05,
                                            'overflow_amount', overflow_amount_05,
                                            'change_amount', change_amount_05,
                                            'item_count', item_count_05,
                                            'item_count_returned', item_count_returned_05
                                    ),
                                    'h06', JSON_BUILD_OBJECT(
                                            'receivable', receivable_06,
                                            'pay_amount', pay_amount_06,
                                            'transfer_real_amount', transfer_real_amount_06,
                                            'payment_transfer_amount', payment_transfer_amount_06,
                                            'cost', cost_06,
                                            'tp_allowance', tp_allowance_06,
                                            'rounding', rounding_06,
                                            'overflow_amount', overflow_amount_06,
                                            'change_amount', change_amount_06,
                                            'item_count', item_count_06,
                                            'item_count_returned', item_count_returned_06
                                    ),
                                    'h07', JSON_BUILD_OBJECT(
                                            'receivable', receivable_07,
                                            'pay_amount', pay_amount_07,
                                            'transfer_real_amount', transfer_real_amount_07,
                                            'payment_transfer_amount', payment_transfer_amount_07,
                                            'cost', cost_07,
                                            'tp_allowance', tp_allowance_07,
                                            'rounding', rounding_07,
                                            'overflow_amount', overflow_amount_07,
                                            'change_amount', change_amount_07,
                                            'item_count', item_count_07,
                                            'item_count_returned', item_count_returned_07
                                    ),
                                    'h08', JSON_BUILD_OBJECT(
                                            'receivable', receivable_08,
                                            'pay_amount', pay_amount_08,
                                            'transfer_real_amount', transfer_real_amount_08,
                                            'payment_transfer_amount', payment_transfer_amount_08,
                                            'cost', cost_08,
                                            'tp_allowance', tp_allowance_08,
                                            'rounding', rounding_08,
                                            'overflow_amount', overflow_amount_08,
                                            'change_amount', change_amount_08,
                                            'item_count', item_count_08,
                                            'item_count_returned', item_count_returned_08
                                    ),
                                    'h09', JSON_BUILD_OBJECT(
                                            'receivable', receivable_09,
                                            'pay_amount', pay_amount_09,
                                            'transfer_real_amount', transfer_real_amount_09,
                                            'payment_transfer_amount', payment_transfer_amount_09,
                                            'cost', cost_09,
                                            'tp_allowance', tp_allowance_09,
                                            'rounding', rounding_09,
                                            'overflow_amount', overflow_amount_09,
                                            'change_amount', change_amount_09,
                                            'item_count', item_count_09,
                                            'item_count_returned', item_count_returned_09
                                    ),
                                    'h10', JSON_BUILD_OBJECT(
                                            'receivable', receivable_10,
                                            'pay_amount', pay_amount_10,
                                            'transfer_real_amount', transfer_real_amount_10,
                                            'payment_transfer_amount', payment_transfer_amount_10,
                                            'cost', cost_10,
                                            'tp_allowance', tp_allowance_10,
                                            'rounding', rounding_10,
                                            'overflow_amount', overflow_amount_10,
                                            'change_amount', change_amount_10,
                                            'item_count', item_count_10,
                                            'item_count_returned', item_count_returned_10
                                    ),
                                    'h11', JSON_BUILD_OBJECT(
                                            'receivable', receivable_11,
                                            'pay_amount', pay_amount_11,
                                            'transfer_real_amount', transfer_real_amount_11,
                                            'payment_transfer_amount', payment_transfer_amount_11,
                                            'cost', cost_11,
                                            'tp_allowance', tp_allowance_11,
                                            'rounding', rounding_11,
                                            'overflow_amount', overflow_amount_11,
                                            'change_amount', change_amount_11,
                                            'item_count', item_count_11,
                                            'item_count_returned', item_count_returned_11
                                    ),
                                    'h12', JSON_BUILD_OBJECT(
                                            'receivable', receivable_12,
                                            'pay_amount', pay_amount_12,
                                            'transfer_real_amount', transfer_real_amount_12,
                                            'payment_transfer_amount', payment_transfer_amount_12,
                                            'cost', cost_12,
                                            'tp_allowance', tp_allowance_12,
                                            'rounding', rounding_12,
                                            'overflow_amount', overflow_amount_12,
                                            'change_amount', change_amount_12,
                                            'item_count', item_count_12,
                                            'item_count_returned', item_count_returned_12
                                    ),
                                    'h13', JSON_BUILD_OBJECT(
                                            'receivable', receivable_13,
                                            'pay_amount', pay_amount_13,
                                            'transfer_real_amount', transfer_real_amount_13,
                                            'payment_transfer_amount', payment_transfer_amount_13,
                                            'cost', cost_13,
                                            'tp_allowance', tp_allowance_13,
                                            'rounding', rounding_13,
                                            'overflow_amount', overflow_amount_13,
                                            'change_amount', change_amount_13,
                                            'item_count', item_count_13,
                                            'item_count_returned', item_count_returned_13
                                    ),
                                    'h14', JSON_BUILD_OBJECT(
                                            'receivable', receivable_14,
                                            'pay_amount', pay_amount_14,
                                            'transfer_real_amount', transfer_real_amount_14,
                                            'payment_transfer_amount', payment_transfer_amount_14,
                                            'cost', cost_14,
                                            'tp_allowance', tp_allowance_14,
                                            'rounding', rounding_14,
                                            'overflow_amount', overflow_amount_14,
                                            'change_amount', change_amount_14,
                                            'item_count', item_count_14,
                                            'item_count_returned', item_count_returned_14
                                    ),
                                    'h15', JSON_BUILD_OBJECT(
                                            'receivable', receivable_15,
                                            'pay_amount', pay_amount_15,
                                            'transfer_real_amount', transfer_real_amount_15,
                                            'payment_transfer_amount', payment_transfer_amount_15,
                                            'cost', cost_15,
                                            'tp_allowance', tp_allowance_15,
                                            'rounding', rounding_15,
                                            'overflow_amount', overflow_amount_15,
                                            'change_amount', change_amount_15,
                                            'item_count', item_count_15,
                                            'item_count_returned', item_count_returned_15
                                    ),
                                    'h16', JSON_BUILD_OBJECT(
                                            'receivable', receivable_16,
                                            'pay_amount', pay_amount_16,
                                            'transfer_real_amount', transfer_real_amount_16,
                                            'payment_transfer_amount', payment_transfer_amount_16,
                                            'cost', cost_16,
                                            'tp_allowance', tp_allowance_16,
                                            'rounding', rounding_16,
                                            'overflow_amount', overflow_amount_16,
                                            'change_amount', change_amount_16,
                                            'item_count', item_count_16,
                                            'item_count_returned', item_count_returned_16
                                    ),
                                    'h17', JSON_BUILD_OBJECT(
                                            'receivable', receivable_17,
                                            'pay_amount', pay_amount_17,
                                            'transfer_real_amount', transfer_real_amount_17,
                                            'payment_transfer_amount', payment_transfer_amount_17,
                                            'cost', cost_17,
                                            'tp_allowance', tp_allowance_17,
                                            'rounding', rounding_17,
                                            'overflow_amount', overflow_amount_17,
                                            'change_amount', change_amount_17,
                                            'item_count', item_count_17,
                                            'item_count_returned', item_count_returned_17
                                    ),
                                    'h18', JSON_BUILD_OBJECT(
                                            'receivable', receivable_18,
                                            'pay_amount', pay_amount_18,
                                            'transfer_real_amount', transfer_real_amount_18,
                                            'payment_transfer_amount', payment_transfer_amount_18,
                                            'cost', cost_18,
                                            'tp_allowance', tp_allowance_18,
                                            'rounding', rounding_18,
                                            'overflow_amount', overflow_amount_18,
                                            'change_amount', change_amount_18,
                                            'item_count', item_count_18,
                                            'item_count_returned', item_count_returned_18
                                    ),
                                    'h19', JSON_BUILD_OBJECT(
                                            'receivable', receivable_19,
                                            'pay_amount', pay_amount_19,
                                            'transfer_real_amount', transfer_real_amount_19,
                                            'payment_transfer_amount', payment_transfer_amount_19,
                                            'cost', cost_19,
                                            'tp_allowance', tp_allowance_19,
                                            'rounding', rounding_19,
                                            'overflow_amount', overflow_amount_19,
                                            'change_amount', change_amount_19,
                                            'item_count', item_count_19,
                                            'item_count_returned', item_count_returned_19
                                    ),
                                    'h20', JSON_BUILD_OBJECT(
                                            'receivable', receivable_20,
                                            'pay_amount', pay_amount_20,
                                            'transfer_real_amount', transfer_real_amount_20,
                                            'payment_transfer_amount', payment_transfer_amount_20,
                                            'cost', cost_20,
                                            'tp_allowance', tp_allowance_20,
                                            'rounding', rounding_20,
                                            'overflow_amount', overflow_amount_20,
                                            'change_amount', change_amount_20,
                                            'item_count', item_count_20,
                                            'item_count_returned', item_count_returned_20
                                    ),
                                    'h21', JSON_BUILD_OBJECT(
                                            'receivable', receivable_21,
                                            'pay_amount', pay_amount_21,
                                            'transfer_real_amount', transfer_real_amount_21,
                                            'payment_transfer_amount', payment_transfer_amount_21,
                                            'cost', cost_21,
                                            'tp_allowance', tp_allowance_21,
                                            'rounding', rounding_21,
                                            'overflow_amount', overflow_amount_21,
                                            'change_amount', change_amount_21,
                                            'item_count', item_count_21,
                                            'item_count_returned', item_count_returned_21
                                    ),
                                    'h22', JSON_BUILD_OBJECT(
                                            'receivable', receivable_22,
                                            'pay_amount', pay_amount_22,
                                            'transfer_real_amount', transfer_real_amount_22,
                                            'payment_transfer_amount', payment_transfer_amount_22,
                                            'cost', cost_22,
                                            'tp_allowance', tp_allowance_22,
                                            'rounding', rounding_22,
                                            'overflow_amount', overflow_amount_22,
                                            'change_amount', change_amount_22,
                                            'item_count', item_count_22,
                                            'item_count_returned', item_count_returned_22
                                    ),
                                    'h23', JSON_BUILD_OBJECT(
                                            'receivable', receivable_23,
                                            'pay_amount', pay_amount_23,
                                            'transfer_real_amount', transfer_real_amount_23,
                                            'payment_transfer_amount', payment_transfer_amount_23,
                                            'cost', cost_23,
                                            'tp_allowance', tp_allowance_23,
                                            'rounding', rounding_23,
                                            'overflow_amount', overflow_amount_23,
                                            'change_amount', change_amount_23,
                                            'item_count', item_count_23,
                                            'item_count_returned', item_count_returned_23
                                    )
                            )
                    ) AS "child"
				FROM base_payments_for_period
				GROUP BY
					region_id
			)
			SELECT
				'' AS bus_date,
				for_payment.region_id AS region_id,
				for_payment.store_type AS store_type,
				bd.days AS business_days,

			    for_payment.receivable AS receivable,
                for_payment.pay_amount AS pay_amount,
                for_payment.transfer_real_amount AS transfer_real_amount,
                for_payment.payment_transfer_amount AS payment_transfer_amount,
                for_payment.cost AS cost,
                for_payment.tp_allowance AS tp_allowance,
                for_payment.rounding AS rounding,
                for_payment.overflow_amount AS overflow_amount,
                for_payment.change_amount AS change_amount,
                for_payment.item_count AS item_count,
                for_payment.item_count_returned AS item_count_returned,
				COALESCE(for_payment.data, '{}'::json) AS data,
				COALESCE(for_payment.child, '[]'::json) AS child,

				0 AS total --Summary时的汇总条数
			FROM
				base_payments_by_date_and_region_for_payment for_payment
					LEFT JOIN business_days bd
						ON for_payment.region_id = bd.region_id
			ORDER BY for_payment.region_id;
 		`
		summarySQL = `
		--支付时段报表汇总信息 SUMMARY
				WITH base_payments AS (
					SELECT
						{REGION_ID} AS region_id,
				
						extract(hour from fact.order_time) AS hours,
				
						COALESCE(SUM(receivable), 0) AS receivable, --应付金额
                        COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
                        COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
                        COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
                        COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
                        COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
                        COALESCE(SUM(rounding), 0) AS rounding, --抹零
                        COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
                        COALESCE(SUM(change_amount), 0) AS change_amount, --找零
                        COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
                        COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY
						{REGION_ID},
						fact.order_time
					ORDER BY
						{REGION_ID}
				), base_payments_for_period AS (
					SELECT
						region_id,
				
						SUM(receivable) AS receivable, --应付金额
                        SUM(pay_amount) AS pay_amount, --实付金额
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                        SUM(cost) AS cost, --用户实际购买金额
                        SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(item_count) AS item_count, --支付次数
                        SUM(item_count_returned) AS item_count_returned, --支付退单数
				
						SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                        SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                        SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                        SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                        SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                        SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                        SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                        SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                        SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                        SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                        SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                        SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                        SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                        SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                        SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                        SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                        SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                        SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                        SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                        SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                        SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                        SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                        SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                        SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

                        SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
                        SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
                        SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
                        SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
                        SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
                        SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
                        SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
                        SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
                        SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
                        SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
                        SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
                        SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
                        SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
                        SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
                        SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
                        SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
                        SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
                        SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
                        SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
                        SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
                        SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
                        SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
                        SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
                        SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

                        SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                        SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                        SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                        SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                        SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                        SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                        SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                        SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                        SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                        SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                        SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                        SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                        SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                        SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                        SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                        SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                        SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                        SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                        SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                        SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                        SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                        SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                        SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                        SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

                        SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
                        SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
                        SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
                        SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
                        SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
                        SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
                        SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
                        SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
                        SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
                        SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
                        SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
                        SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
                        SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
                        SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
                        SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
                        SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
                        SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
                        SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
                        SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
                        SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
                        SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
                        SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
                        SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
                        SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

                        SUM(CASE WHEN hours = 0 THEN cost ELSE 0 END) AS cost_00,
                        SUM(CASE WHEN hours = 1 THEN cost ELSE 0 END) AS cost_01,
                        SUM(CASE WHEN hours = 2 THEN cost ELSE 0 END) AS cost_02,
                        SUM(CASE WHEN hours = 3 THEN cost ELSE 0 END) AS cost_03,
                        SUM(CASE WHEN hours = 4 THEN cost ELSE 0 END) AS cost_04,
                        SUM(CASE WHEN hours = 5 THEN cost ELSE 0 END) AS cost_05,
                        SUM(CASE WHEN hours = 6 THEN cost ELSE 0 END) AS cost_06,
                        SUM(CASE WHEN hours = 7 THEN cost ELSE 0 END) AS cost_07,
                        SUM(CASE WHEN hours = 8 THEN cost ELSE 0 END) AS cost_08,
                        SUM(CASE WHEN hours = 9 THEN cost ELSE 0 END) AS cost_09,
                        SUM(CASE WHEN hours = 10 THEN cost ELSE 0 END) AS cost_10,
                        SUM(CASE WHEN hours = 11 THEN cost ELSE 0 END) AS cost_11,
                        SUM(CASE WHEN hours = 12 THEN cost ELSE 0 END) AS cost_12,
                        SUM(CASE WHEN hours = 13 THEN cost ELSE 0 END) AS cost_13,
                        SUM(CASE WHEN hours = 14 THEN cost ELSE 0 END) AS cost_14,
                        SUM(CASE WHEN hours = 15 THEN cost ELSE 0 END) AS cost_15,
                        SUM(CASE WHEN hours = 16 THEN cost ELSE 0 END) AS cost_16,
                        SUM(CASE WHEN hours = 17 THEN cost ELSE 0 END) AS cost_17,
                        SUM(CASE WHEN hours = 18 THEN cost ELSE 0 END) AS cost_18,
                        SUM(CASE WHEN hours = 19 THEN cost ELSE 0 END) AS cost_19,
                        SUM(CASE WHEN hours = 20 THEN cost ELSE 0 END) AS cost_20,
                        SUM(CASE WHEN hours = 21 THEN cost ELSE 0 END) AS cost_21,
                        SUM(CASE WHEN hours = 22 THEN cost ELSE 0 END) AS cost_22,
                        SUM(CASE WHEN hours = 23 THEN cost ELSE 0 END) AS cost_23,

                        SUM(CASE WHEN hours = 0 THEN tp_allowance ELSE 0 END) AS tp_allowance_00,
                        SUM(CASE WHEN hours = 1 THEN tp_allowance ELSE 0 END) AS tp_allowance_01,
                        SUM(CASE WHEN hours = 2 THEN tp_allowance ELSE 0 END) AS tp_allowance_02,
                        SUM(CASE WHEN hours = 3 THEN tp_allowance ELSE 0 END) AS tp_allowance_03,
                        SUM(CASE WHEN hours = 4 THEN tp_allowance ELSE 0 END) AS tp_allowance_04,
                        SUM(CASE WHEN hours = 5 THEN tp_allowance ELSE 0 END) AS tp_allowance_05,
                        SUM(CASE WHEN hours = 6 THEN tp_allowance ELSE 0 END) AS tp_allowance_06,
                        SUM(CASE WHEN hours = 7 THEN tp_allowance ELSE 0 END) AS tp_allowance_07,
                        SUM(CASE WHEN hours = 8 THEN tp_allowance ELSE 0 END) AS tp_allowance_08,
                        SUM(CASE WHEN hours = 9 THEN tp_allowance ELSE 0 END) AS tp_allowance_09,
                        SUM(CASE WHEN hours = 10 THEN tp_allowance ELSE 0 END) AS tp_allowance_10,
                        SUM(CASE WHEN hours = 11 THEN tp_allowance ELSE 0 END) AS tp_allowance_11,
                        SUM(CASE WHEN hours = 12 THEN tp_allowance ELSE 0 END) AS tp_allowance_12,
                        SUM(CASE WHEN hours = 13 THEN tp_allowance ELSE 0 END) AS tp_allowance_13,
                        SUM(CASE WHEN hours = 14 THEN tp_allowance ELSE 0 END) AS tp_allowance_14,
                        SUM(CASE WHEN hours = 15 THEN tp_allowance ELSE 0 END) AS tp_allowance_15,
                        SUM(CASE WHEN hours = 16 THEN tp_allowance ELSE 0 END) AS tp_allowance_16,
                        SUM(CASE WHEN hours = 17 THEN tp_allowance ELSE 0 END) AS tp_allowance_17,
                        SUM(CASE WHEN hours = 18 THEN tp_allowance ELSE 0 END) AS tp_allowance_18,
                        SUM(CASE WHEN hours = 19 THEN tp_allowance ELSE 0 END) AS tp_allowance_19,
                        SUM(CASE WHEN hours = 20 THEN tp_allowance ELSE 0 END) AS tp_allowance_20,
                        SUM(CASE WHEN hours = 21 THEN tp_allowance ELSE 0 END) AS tp_allowance_21,
                        SUM(CASE WHEN hours = 22 THEN tp_allowance ELSE 0 END) AS tp_allowance_22,
                        SUM(CASE WHEN hours = 23 THEN tp_allowance ELSE 0 END) AS tp_allowance_23,

                        SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
                        SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
                        SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
                        SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
                        SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
                        SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
                        SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
                        SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
                        SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
                        SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
                        SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
                        SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
                        SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
                        SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
                        SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
                        SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
                        SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
                        SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
                        SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
                        SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
                        SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
                        SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
                        SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
                        SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

                        SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
                        SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
                        SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
                        SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
                        SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
                        SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
                        SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
                        SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
                        SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
                        SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
                        SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
                        SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
                        SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
                        SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
                        SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
                        SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
                        SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
                        SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
                        SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
                        SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
                        SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
                        SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
                        SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
                        SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

                        SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
                        SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
                        SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
                        SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
                        SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
                        SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
                        SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
                        SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
                        SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
                        SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
                        SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
                        SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
                        SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
                        SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
                        SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
                        SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
                        SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
                        SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
                        SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
                        SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
                        SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
                        SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
                        SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
                        SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23,

                        SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
                        SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
                        SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
                        SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
                        SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
                        SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
                        SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
                        SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
                        SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
                        SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
                        SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
                        SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
                        SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
                        SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
                        SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
                        SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
                        SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
                        SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
                        SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
                        SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
                        SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
                        SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
                        SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
                        SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

                        SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
                        SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
                        SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
                        SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
                        SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
                        SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
                        SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
                        SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
                        SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
                        SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
                        SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
                        SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
                        SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
                        SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
                        SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
                        SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
                        SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
                        SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
                        SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
                        SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
                        SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
                        SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
                        SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
                        SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23
					FROM base_payments
					GROUP BY
						region_id
				),base_payments_for_json AS (
					SELECT
						SUM(receivable) AS receivable, --应付金额
                        SUM(pay_amount) AS pay_amount, --实付金额
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                        SUM(cost) AS cost, --用户实际购买金额
                        SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(item_count) AS item_count, --支付次数
                        SUM(item_count_returned) AS item_count_returned, --支付退单数
				
						JSON_BUILD_OBJECT(
							'payment_id', 0,
							'payment_code', '',
							'payment_name', '',

							'h00', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_00),
                                    'pay_amount', SUM(pay_amount_00),
                                    'transfer_real_amount', SUM(transfer_real_amount_00),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_00),
                                    'cost', SUM(cost_00),
                                    'tp_allowance', SUM(tp_allowance_00),
                                    'rounding', SUM(rounding_00),
                                    'overflow_amount', SUM(overflow_amount_00),
                                    'change_amount', SUM(change_amount_00),
                                    'item_count', SUM(item_count_00),
                                    'item_count_returned', SUM(item_count_returned_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_01),
                                    'pay_amount', SUM(pay_amount_01),
                                    'transfer_real_amount', SUM(transfer_real_amount_01),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_01),
                                    'cost', SUM(cost_01),
                                    'tp_allowance', SUM(tp_allowance_01),
                                    'rounding', SUM(rounding_01),
                                    'overflow_amount', SUM(overflow_amount_01),
                                    'change_amount', SUM(change_amount_01),
                                    'item_count', SUM(item_count_01),
                                    'item_count_returned', SUM(item_count_returned_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_02),
                                    'pay_amount', SUM(pay_amount_02),
                                    'transfer_real_amount', SUM(transfer_real_amount_02),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_02),
                                    'cost', SUM(cost_02),
                                    'tp_allowance', SUM(tp_allowance_02),
                                    'rounding', SUM(rounding_02),
                                    'overflow_amount', SUM(overflow_amount_02),
                                    'change_amount', SUM(change_amount_02),
                                    'item_count', SUM(item_count_02),
                                    'item_count_returned', SUM(item_count_returned_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_03),
                                    'pay_amount', SUM(pay_amount_03),
                                    'transfer_real_amount', SUM(transfer_real_amount_03),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_03),
                                    'cost', SUM(cost_03),
                                    'tp_allowance', SUM(tp_allowance_03),
                                    'rounding', SUM(rounding_03),
                                    'overflow_amount', SUM(overflow_amount_03),
                                    'change_amount', SUM(change_amount_03),
                                    'item_count', SUM(item_count_03),
                                    'item_count_returned', SUM(item_count_returned_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_04),
                                    'pay_amount', SUM(pay_amount_04),
                                    'transfer_real_amount', SUM(transfer_real_amount_04),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_04),
                                    'cost', SUM(cost_04),
                                    'tp_allowance', SUM(tp_allowance_04),
                                    'rounding', SUM(rounding_04),
                                    'overflow_amount', SUM(overflow_amount_04),
                                    'change_amount', SUM(change_amount_04),
                                    'item_count', SUM(item_count_04),
                                    'item_count_returned', SUM(item_count_returned_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_05),
                                    'pay_amount', SUM(pay_amount_05),
                                    'transfer_real_amount', SUM(transfer_real_amount_05),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_05),
                                    'cost', SUM(cost_05),
                                    'tp_allowance', SUM(tp_allowance_05),
                                    'rounding', SUM(rounding_05),
                                    'overflow_amount', SUM(overflow_amount_05),
                                    'change_amount', SUM(change_amount_05),
                                    'item_count', SUM(item_count_05),
                                    'item_count_returned', SUM(item_count_returned_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_06),
                                    'pay_amount', SUM(pay_amount_06),
                                    'transfer_real_amount', SUM(transfer_real_amount_06),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_06),
                                    'cost', SUM(cost_06),
                                    'tp_allowance', SUM(tp_allowance_06),
                                    'rounding', SUM(rounding_06),
                                    'overflow_amount', SUM(overflow_amount_06),
                                    'change_amount', SUM(change_amount_06),
                                    'item_count', SUM(item_count_06),
                                    'item_count_returned', SUM(item_count_returned_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_07),
                                    'pay_amount', SUM(pay_amount_07),
                                    'transfer_real_amount', SUM(transfer_real_amount_07),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_07),
                                    'cost', SUM(cost_07),
                                    'tp_allowance', SUM(tp_allowance_07),
                                    'rounding', SUM(rounding_07),
                                    'overflow_amount', SUM(overflow_amount_07),
                                    'change_amount', SUM(change_amount_07),
                                    'item_count', SUM(item_count_07),
                                    'item_count_returned', SUM(item_count_returned_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_08),
                                    'pay_amount', SUM(pay_amount_08),
                                    'transfer_real_amount', SUM(transfer_real_amount_08),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_08),
                                    'cost', SUM(cost_08),
                                    'tp_allowance', SUM(tp_allowance_08),
                                    'rounding', SUM(rounding_08),
                                    'overflow_amount', SUM(overflow_amount_08),
                                    'change_amount', SUM(change_amount_08),
                                    'item_count', SUM(item_count_08),
                                    'item_count_returned', SUM(item_count_returned_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_09),
                                    'pay_amount', SUM(pay_amount_09),
                                    'transfer_real_amount', SUM(transfer_real_amount_09),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_09),
                                    'cost', SUM(cost_09),
                                    'tp_allowance', SUM(tp_allowance_09),
                                    'rounding', SUM(rounding_09),
                                    'overflow_amount', SUM(overflow_amount_09),
                                    'change_amount', SUM(change_amount_09),
                                    'item_count', SUM(item_count_09),
                                    'item_count_returned', SUM(item_count_returned_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_10),
                                    'pay_amount', SUM(pay_amount_10),
                                    'transfer_real_amount', SUM(transfer_real_amount_10),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_10),
                                    'cost', SUM(cost_10),
                                    'tp_allowance', SUM(tp_allowance_10),
                                    'rounding', SUM(rounding_10),
                                    'overflow_amount', SUM(overflow_amount_10),
                                    'change_amount', SUM(change_amount_10),
                                    'item_count', SUM(item_count_10),
                                    'item_count_returned', SUM(item_count_returned_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_11),
                                    'pay_amount', SUM(pay_amount_11),
                                    'transfer_real_amount', SUM(transfer_real_amount_11),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_11),
                                    'cost', SUM(cost_11),
                                    'tp_allowance', SUM(tp_allowance_11),
                                    'rounding', SUM(rounding_11),
                                    'overflow_amount', SUM(overflow_amount_11),
                                    'change_amount', SUM(change_amount_11),
                                    'item_count', SUM(item_count_11),
                                    'item_count_returned', SUM(item_count_returned_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_12),
                                    'pay_amount', SUM(pay_amount_12),
                                    'transfer_real_amount', SUM(transfer_real_amount_12),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_12),
                                    'cost', SUM(cost_12),
                                    'tp_allowance', SUM(tp_allowance_12),
                                    'rounding', SUM(rounding_12),
                                    'overflow_amount', SUM(overflow_amount_12),
                                    'change_amount', SUM(change_amount_12),
                                    'item_count', SUM(item_count_12),
                                    'item_count_returned', SUM(item_count_returned_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_13),
                                    'pay_amount', SUM(pay_amount_13),
                                    'transfer_real_amount', SUM(transfer_real_amount_13),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_13),
                                    'cost', SUM(cost_13),
                                    'tp_allowance', SUM(tp_allowance_13),
                                    'rounding', SUM(rounding_13),
                                    'overflow_amount', SUM(overflow_amount_13),
                                    'change_amount', SUM(change_amount_13),
                                    'item_count', SUM(item_count_13),
                                    'item_count_returned', SUM(item_count_returned_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_14),
                                    'pay_amount', SUM(pay_amount_14),
                                    'transfer_real_amount', SUM(transfer_real_amount_14),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_14),
                                    'cost', SUM(cost_14),
                                    'tp_allowance', SUM(tp_allowance_14),
                                    'rounding', SUM(rounding_14),
                                    'overflow_amount', SUM(overflow_amount_14),
                                    'change_amount', SUM(change_amount_14),
                                    'item_count', SUM(item_count_14),
                                    'item_count_returned', SUM(item_count_returned_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_15),
                                    'pay_amount', SUM(pay_amount_15),
                                    'transfer_real_amount', SUM(transfer_real_amount_15),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_15),
                                    'cost', SUM(cost_15),
                                    'tp_allowance', SUM(tp_allowance_15),
                                    'rounding', SUM(rounding_15),
                                    'overflow_amount', SUM(overflow_amount_15),
                                    'change_amount', SUM(change_amount_15),
                                    'item_count', SUM(item_count_15),
                                    'item_count_returned', SUM(item_count_returned_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_16),
                                    'pay_amount', SUM(pay_amount_16),
                                    'transfer_real_amount', SUM(transfer_real_amount_16),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_16),
                                    'cost', SUM(cost_16),
                                    'tp_allowance', SUM(tp_allowance_16),
                                    'rounding', SUM(rounding_16),
                                    'overflow_amount', SUM(overflow_amount_16),
                                    'change_amount', SUM(change_amount_16),
                                    'item_count', SUM(item_count_16),
                                    'item_count_returned', SUM(item_count_returned_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_17),
                                    'pay_amount', SUM(pay_amount_17),
                                    'transfer_real_amount', SUM(transfer_real_amount_17),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_17),
                                    'cost', SUM(cost_17),
                                    'tp_allowance', SUM(tp_allowance_17),
                                    'rounding', SUM(rounding_17),
                                    'overflow_amount', SUM(overflow_amount_17),
                                    'change_amount', SUM(change_amount_17),
                                    'item_count', SUM(item_count_17),
                                    'item_count_returned', SUM(item_count_returned_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_18),
                                    'pay_amount', SUM(pay_amount_18),
                                    'transfer_real_amount', SUM(transfer_real_amount_18),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_18),
                                    'cost', SUM(cost_18),
                                    'tp_allowance', SUM(tp_allowance_18),
                                    'rounding', SUM(rounding_18),
                                    'overflow_amount', SUM(overflow_amount_18),
                                    'change_amount', SUM(change_amount_18),
                                    'item_count', SUM(item_count_18),
                                    'item_count_returned', SUM(item_count_returned_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_19),
                                    'pay_amount', SUM(pay_amount_19),
                                    'transfer_real_amount', SUM(transfer_real_amount_19),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_19),
                                    'cost', SUM(cost_19),
                                    'tp_allowance', SUM(tp_allowance_19),
                                    'rounding', SUM(rounding_19),
                                    'overflow_amount', SUM(overflow_amount_19),
                                    'change_amount', SUM(change_amount_19),
                                    'item_count', SUM(item_count_19),
                                    'item_count_returned', SUM(item_count_returned_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_20),
                                    'pay_amount', SUM(pay_amount_20),
                                    'transfer_real_amount', SUM(transfer_real_amount_20),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_20),
                                    'cost', SUM(cost_20),
                                    'tp_allowance', SUM(tp_allowance_20),
                                    'rounding', SUM(rounding_20),
                                    'overflow_amount', SUM(overflow_amount_20),
                                    'change_amount', SUM(change_amount_20),
                                    'item_count', SUM(item_count_20),
                                    'item_count_returned', SUM(item_count_returned_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_21),
                                    'pay_amount', SUM(pay_amount_21),
                                    'transfer_real_amount', SUM(transfer_real_amount_21),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_21),
                                    'cost', SUM(cost_21),
                                    'tp_allowance', SUM(tp_allowance_21),
                                    'rounding', SUM(rounding_21),
                                    'overflow_amount', SUM(overflow_amount_21),
                                    'change_amount', SUM(change_amount_21),
                                    'item_count', SUM(item_count_21),
                                    'item_count_returned', SUM(item_count_returned_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_22),
                                    'pay_amount', SUM(pay_amount_22),
                                    'transfer_real_amount', SUM(transfer_real_amount_22),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_22),
                                    'cost', SUM(cost_22),
                                    'tp_allowance', SUM(tp_allowance_22),
                                    'rounding', SUM(rounding_22),
                                    'overflow_amount', SUM(overflow_amount_22),
                                    'change_amount', SUM(change_amount_22),
                                    'item_count', SUM(item_count_22),
                                    'item_count_returned', SUM(item_count_returned_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_23),
                                    'pay_amount', SUM(pay_amount_23),
                                    'transfer_real_amount', SUM(transfer_real_amount_23),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_23),
                                    'cost', SUM(cost_23),
                                    'tp_allowance', SUM(tp_allowance_23),
                                    'rounding', SUM(rounding_23),
                                    'overflow_amount', SUM(overflow_amount_23),
                                    'change_amount', SUM(change_amount_23),
                                    'item_count', SUM(item_count_23),
                                    'item_count_returned', SUM(item_count_returned_23)
                            )
						) AS "child",
						COUNT(1) AS total --Summary时的汇总条数
					FROM base_payments_for_period
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数,
				    SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数
					'{}'::json AS data,
					JSON_AGG(child) AS child,
					SUM(total) AS total

				FROM
					base_payments_for_json
 		`
	} else {
		rowSQL = `
		--支付时段报表详细信息 ROWS
			WITH business_days AS (
				SELECT
					region_id, COUNT(1) AS days
				FROM (
					SELECT
						{REGION_ID} AS region_id
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
									ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY {REGION_ID}
				) T
				GROUP BY region_id
			), base_limit AS (
				SELECT
					fact.bus_date AS bus_date,
					{REGION_ID} AS region_id
				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches
							ON fact.store_id = store_caches.id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID}
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
				{LIMIT}
			), base as (
			    select
			        fact.bus_date AS bus_date,
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					COALESCE(string_agg(distinct fact.payment_code, ','), '') AS payment_code,
					COALESCE(string_agg(distinct fact.payment_name, ','), '') AS payment_name
			    from sales_payment_amounts fact
			    left join store_caches on fact.store_id = store_caches.id
			    where
			        {WHERE}
			    group by fact.bus_date, fact.store_id, fact.payment_id
            ), base_payments AS (
				SELECT
					fact.bus_date AS bus_date,
					COALESCE({REGION_ID}, 0) AS region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.payment_id, 0) AS payment_id,
					base.payment_code AS payment_code,
					base.payment_name AS payment_name,

					extract(hour from fact.order_time) AS hours,
			
					COALESCE(SUM(receivable), 0) AS receivable, --应付金额
					COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
					COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
					COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
					COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
					COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
					COALESCE(SUM(rounding), 0) AS rounding, --抹零
					COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
					COALESCE(SUM(change_amount), 0) AS change_amount, --找零
					COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
					COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数

				FROM
					sales_payment_amounts fact
						LEFT JOIN store_caches store_caches
							ON fact.store_id = store_caches.id
						left join base on base.bus_date = fact.bus_date 
							and base.region_id = {REGION_ID} and base.payment_id = fact.payment_id
						INNER JOIN base_limit
							ON fact.bus_date = base_limit.bus_date
								AND {REGION_ID} = base_limit.region_id
				WHERE
					{WHERE}
				GROUP BY
					fact.bus_date,
					{REGION_ID},
				    fact.payment_id,
					base.payment_code,
				    base.payment_name,
					fact.order_time
				ORDER BY
					fact.bus_date DESC,
					{REGION_ID}
			), base_payments_for_period AS (
				SELECT
					bus_date,
					region_id,
					store_type,
					payment_id,
					payment_code,
					payment_name,

					SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数
			
                    SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                    SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                    SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                    SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                    SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                    SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                    SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                    SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                    SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                    SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                    SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                    SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                    SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                    SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                    SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                    SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                    SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                    SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                    SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                    SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                    SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                    SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                    SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                    SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

                    SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
                    SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
                    SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
                    SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
                    SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
                    SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
                    SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
                    SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
                    SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
                    SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
                    SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
                    SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
                    SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
                    SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
                    SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
                    SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
                    SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
                    SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
                    SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
                    SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
                    SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
                    SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
                    SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
                    SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

                    SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                    SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                    SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                    SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                    SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                    SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                    SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                    SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                    SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                    SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                    SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                    SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                    SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                    SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                    SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                    SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                    SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                    SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                    SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                    SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                    SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                    SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                    SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                    SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

                    SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
                    SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
                    SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
                    SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
                    SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
                    SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
                    SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
                    SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
                    SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
                    SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
                    SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
                    SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
                    SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
                    SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
                    SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
                    SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
                    SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
                    SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
                    SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
                    SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
                    SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
                    SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
                    SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
                    SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

                    SUM(CASE WHEN hours = 0 THEN cost ELSE 0 END) AS cost_00,
                    SUM(CASE WHEN hours = 1 THEN cost ELSE 0 END) AS cost_01,
                    SUM(CASE WHEN hours = 2 THEN cost ELSE 0 END) AS cost_02,
                    SUM(CASE WHEN hours = 3 THEN cost ELSE 0 END) AS cost_03,
                    SUM(CASE WHEN hours = 4 THEN cost ELSE 0 END) AS cost_04,
                    SUM(CASE WHEN hours = 5 THEN cost ELSE 0 END) AS cost_05,
                    SUM(CASE WHEN hours = 6 THEN cost ELSE 0 END) AS cost_06,
                    SUM(CASE WHEN hours = 7 THEN cost ELSE 0 END) AS cost_07,
                    SUM(CASE WHEN hours = 8 THEN cost ELSE 0 END) AS cost_08,
                    SUM(CASE WHEN hours = 9 THEN cost ELSE 0 END) AS cost_09,
                    SUM(CASE WHEN hours = 10 THEN cost ELSE 0 END) AS cost_10,
                    SUM(CASE WHEN hours = 11 THEN cost ELSE 0 END) AS cost_11,
                    SUM(CASE WHEN hours = 12 THEN cost ELSE 0 END) AS cost_12,
                    SUM(CASE WHEN hours = 13 THEN cost ELSE 0 END) AS cost_13,
                    SUM(CASE WHEN hours = 14 THEN cost ELSE 0 END) AS cost_14,
                    SUM(CASE WHEN hours = 15 THEN cost ELSE 0 END) AS cost_15,
                    SUM(CASE WHEN hours = 16 THEN cost ELSE 0 END) AS cost_16,
                    SUM(CASE WHEN hours = 17 THEN cost ELSE 0 END) AS cost_17,
                    SUM(CASE WHEN hours = 18 THEN cost ELSE 0 END) AS cost_18,
                    SUM(CASE WHEN hours = 19 THEN cost ELSE 0 END) AS cost_19,
                    SUM(CASE WHEN hours = 20 THEN cost ELSE 0 END) AS cost_20,
                    SUM(CASE WHEN hours = 21 THEN cost ELSE 0 END) AS cost_21,
                    SUM(CASE WHEN hours = 22 THEN cost ELSE 0 END) AS cost_22,
                    SUM(CASE WHEN hours = 23 THEN cost ELSE 0 END) AS cost_23,

                    SUM(CASE WHEN hours = 0 THEN tp_allowance ELSE 0 END) AS tp_allowance_00,
                    SUM(CASE WHEN hours = 1 THEN tp_allowance ELSE 0 END) AS tp_allowance_01,
                    SUM(CASE WHEN hours = 2 THEN tp_allowance ELSE 0 END) AS tp_allowance_02,
                    SUM(CASE WHEN hours = 3 THEN tp_allowance ELSE 0 END) AS tp_allowance_03,
                    SUM(CASE WHEN hours = 4 THEN tp_allowance ELSE 0 END) AS tp_allowance_04,
                    SUM(CASE WHEN hours = 5 THEN tp_allowance ELSE 0 END) AS tp_allowance_05,
                    SUM(CASE WHEN hours = 6 THEN tp_allowance ELSE 0 END) AS tp_allowance_06,
                    SUM(CASE WHEN hours = 7 THEN tp_allowance ELSE 0 END) AS tp_allowance_07,
                    SUM(CASE WHEN hours = 8 THEN tp_allowance ELSE 0 END) AS tp_allowance_08,
                    SUM(CASE WHEN hours = 9 THEN tp_allowance ELSE 0 END) AS tp_allowance_09,
                    SUM(CASE WHEN hours = 10 THEN tp_allowance ELSE 0 END) AS tp_allowance_10,
                    SUM(CASE WHEN hours = 11 THEN tp_allowance ELSE 0 END) AS tp_allowance_11,
                    SUM(CASE WHEN hours = 12 THEN tp_allowance ELSE 0 END) AS tp_allowance_12,
                    SUM(CASE WHEN hours = 13 THEN tp_allowance ELSE 0 END) AS tp_allowance_13,
                    SUM(CASE WHEN hours = 14 THEN tp_allowance ELSE 0 END) AS tp_allowance_14,
                    SUM(CASE WHEN hours = 15 THEN tp_allowance ELSE 0 END) AS tp_allowance_15,
                    SUM(CASE WHEN hours = 16 THEN tp_allowance ELSE 0 END) AS tp_allowance_16,
                    SUM(CASE WHEN hours = 17 THEN tp_allowance ELSE 0 END) AS tp_allowance_17,
                    SUM(CASE WHEN hours = 18 THEN tp_allowance ELSE 0 END) AS tp_allowance_18,
                    SUM(CASE WHEN hours = 19 THEN tp_allowance ELSE 0 END) AS tp_allowance_19,
                    SUM(CASE WHEN hours = 20 THEN tp_allowance ELSE 0 END) AS tp_allowance_20,
                    SUM(CASE WHEN hours = 21 THEN tp_allowance ELSE 0 END) AS tp_allowance_21,
                    SUM(CASE WHEN hours = 22 THEN tp_allowance ELSE 0 END) AS tp_allowance_22,
                    SUM(CASE WHEN hours = 23 THEN tp_allowance ELSE 0 END) AS tp_allowance_23,

                    SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
                    SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
                    SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
                    SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
                    SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
                    SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
                    SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
                    SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
                    SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
                    SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
                    SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
                    SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
                    SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
                    SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
                    SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
                    SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
                    SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
                    SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
                    SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
                    SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
                    SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
                    SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
                    SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
                    SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

                    SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
                    SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
                    SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
                    SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
                    SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
                    SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
                    SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
                    SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
                    SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
                    SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
                    SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
                    SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
                    SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
                    SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
                    SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
                    SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
                    SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
                    SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
                    SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
                    SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
                    SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
                    SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
                    SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
                    SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

                    SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
                    SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
                    SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
                    SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
                    SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
                    SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
                    SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
                    SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
                    SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
                    SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
                    SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
                    SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
                    SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
                    SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
                    SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
                    SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
                    SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
                    SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
                    SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
                    SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
                    SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
                    SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
                    SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
                    SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23,

                    SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
                    SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
                    SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
                    SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
                    SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
                    SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
                    SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
                    SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
                    SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
                    SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
                    SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
                    SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
                    SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
                    SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
                    SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
                    SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
                    SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
                    SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
                    SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
                    SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
                    SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
                    SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
                    SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
                    SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

                    SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
                    SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
                    SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
                    SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
                    SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
                    SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
                    SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
                    SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
                    SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
                    SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
                    SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
                    SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
                    SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
                    SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
                    SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
                    SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
                    SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
                    SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
                    SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
                    SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
                    SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
                    SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
                    SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
                    SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23

				FROM base_payments
				GROUP BY
					bus_date,
					region_id,
					store_type,
					payment_id,
					payment_code,
					payment_name
			), base_payments_by_date_and_region_for_payment AS (
				SELECT
					bus_date,
					region_id,
					max(store_type) as store_type,
			
					SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数

                    JSON_BUILD_OBJECT(
                            'h00', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_00),
                                    'pay_amount', SUM(pay_amount_00),
                                    'transfer_real_amount', SUM(transfer_real_amount_00),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_00),
                                    'cost', SUM(cost_00),
                                    'tp_allowance', SUM(tp_allowance_00),
                                    'rounding', SUM(rounding_00),
                                    'overflow_amount', SUM(overflow_amount_00),
                                    'change_amount', SUM(change_amount_00),
                                    'item_count', SUM(item_count_00),
                                    'item_count_returned', SUM(item_count_returned_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_01),
                                    'pay_amount', SUM(pay_amount_01),
                                    'transfer_real_amount', SUM(transfer_real_amount_01),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_01),
                                    'cost', SUM(cost_01),
                                    'tp_allowance', SUM(tp_allowance_01),
                                    'rounding', SUM(rounding_01),
                                    'overflow_amount', SUM(overflow_amount_01),
                                    'change_amount', SUM(change_amount_01),
                                    'item_count', SUM(item_count_01),
                                    'item_count_returned', SUM(item_count_returned_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_02),
                                    'pay_amount', SUM(pay_amount_02),
                                    'transfer_real_amount', SUM(transfer_real_amount_02),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_02),
                                    'cost', SUM(cost_02),
                                    'tp_allowance', SUM(tp_allowance_02),
                                    'rounding', SUM(rounding_02),
                                    'overflow_amount', SUM(overflow_amount_02),
                                    'change_amount', SUM(change_amount_02),
                                    'item_count', SUM(item_count_02),
                                    'item_count_returned', SUM(item_count_returned_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_03),
                                    'pay_amount', SUM(pay_amount_03),
                                    'transfer_real_amount', SUM(transfer_real_amount_03),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_03),
                                    'cost', SUM(cost_03),
                                    'tp_allowance', SUM(tp_allowance_03),
                                    'rounding', SUM(rounding_03),
                                    'overflow_amount', SUM(overflow_amount_03),
                                    'change_amount', SUM(change_amount_03),
                                    'item_count', SUM(item_count_03),
                                    'item_count_returned', SUM(item_count_returned_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_04),
                                    'pay_amount', SUM(pay_amount_04),
                                    'transfer_real_amount', SUM(transfer_real_amount_04),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_04),
                                    'cost', SUM(cost_04),
                                    'tp_allowance', SUM(tp_allowance_04),
                                    'rounding', SUM(rounding_04),
                                    'overflow_amount', SUM(overflow_amount_04),
                                    'change_amount', SUM(change_amount_04),
                                    'item_count', SUM(item_count_04),
                                    'item_count_returned', SUM(item_count_returned_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_05),
                                    'pay_amount', SUM(pay_amount_05),
                                    'transfer_real_amount', SUM(transfer_real_amount_05),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_05),
                                    'cost', SUM(cost_05),
                                    'tp_allowance', SUM(tp_allowance_05),
                                    'rounding', SUM(rounding_05),
                                    'overflow_amount', SUM(overflow_amount_05),
                                    'change_amount', SUM(change_amount_05),
                                    'item_count', SUM(item_count_05),
                                    'item_count_returned', SUM(item_count_returned_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_06),
                                    'pay_amount', SUM(pay_amount_06),
                                    'transfer_real_amount', SUM(transfer_real_amount_06),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_06),
                                    'cost', SUM(cost_06),
                                    'tp_allowance', SUM(tp_allowance_06),
                                    'rounding', SUM(rounding_06),
                                    'overflow_amount', SUM(overflow_amount_06),
                                    'change_amount', SUM(change_amount_06),
                                    'item_count', SUM(item_count_06),
                                    'item_count_returned', SUM(item_count_returned_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_07),
                                    'pay_amount', SUM(pay_amount_07),
                                    'transfer_real_amount', SUM(transfer_real_amount_07),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_07),
                                    'cost', SUM(cost_07),
                                    'tp_allowance', SUM(tp_allowance_07),
                                    'rounding', SUM(rounding_07),
                                    'overflow_amount', SUM(overflow_amount_07),
                                    'change_amount', SUM(change_amount_07),
                                    'item_count', SUM(item_count_07),
                                    'item_count_returned', SUM(item_count_returned_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_08),
                                    'pay_amount', SUM(pay_amount_08),
                                    'transfer_real_amount', SUM(transfer_real_amount_08),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_08),
                                    'cost', SUM(cost_08),
                                    'tp_allowance', SUM(tp_allowance_08),
                                    'rounding', SUM(rounding_08),
                                    'overflow_amount', SUM(overflow_amount_08),
                                    'change_amount', SUM(change_amount_08),
                                    'item_count', SUM(item_count_08),
                                    'item_count_returned', SUM(item_count_returned_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_09),
                                    'pay_amount', SUM(pay_amount_09),
                                    'transfer_real_amount', SUM(transfer_real_amount_09),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_09),
                                    'cost', SUM(cost_09),
                                    'tp_allowance', SUM(tp_allowance_09),
                                    'rounding', SUM(rounding_09),
                                    'overflow_amount', SUM(overflow_amount_09),
                                    'change_amount', SUM(change_amount_09),
                                    'item_count', SUM(item_count_09),
                                    'item_count_returned', SUM(item_count_returned_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_10),
                                    'pay_amount', SUM(pay_amount_10),
                                    'transfer_real_amount', SUM(transfer_real_amount_10),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_10),
                                    'cost', SUM(cost_10),
                                    'tp_allowance', SUM(tp_allowance_10),
                                    'rounding', SUM(rounding_10),
                                    'overflow_amount', SUM(overflow_amount_10),
                                    'change_amount', SUM(change_amount_10),
                                    'item_count', SUM(item_count_10),
                                    'item_count_returned', SUM(item_count_returned_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_11),
                                    'pay_amount', SUM(pay_amount_11),
                                    'transfer_real_amount', SUM(transfer_real_amount_11),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_11),
                                    'cost', SUM(cost_11),
                                    'tp_allowance', SUM(tp_allowance_11),
                                    'rounding', SUM(rounding_11),
                                    'overflow_amount', SUM(overflow_amount_11),
                                    'change_amount', SUM(change_amount_11),
                                    'item_count', SUM(item_count_11),
                                    'item_count_returned', SUM(item_count_returned_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_12),
                                    'pay_amount', SUM(pay_amount_12),
                                    'transfer_real_amount', SUM(transfer_real_amount_12),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_12),
                                    'cost', SUM(cost_12),
                                    'tp_allowance', SUM(tp_allowance_12),
                                    'rounding', SUM(rounding_12),
                                    'overflow_amount', SUM(overflow_amount_12),
                                    'change_amount', SUM(change_amount_12),
                                    'item_count', SUM(item_count_12),
                                    'item_count_returned', SUM(item_count_returned_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_13),
                                    'pay_amount', SUM(pay_amount_13),
                                    'transfer_real_amount', SUM(transfer_real_amount_13),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_13),
                                    'cost', SUM(cost_13),
                                    'tp_allowance', SUM(tp_allowance_13),
                                    'rounding', SUM(rounding_13),
                                    'overflow_amount', SUM(overflow_amount_13),
                                    'change_amount', SUM(change_amount_13),
                                    'item_count', SUM(item_count_13),
                                    'item_count_returned', SUM(item_count_returned_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_14),
                                    'pay_amount', SUM(pay_amount_14),
                                    'transfer_real_amount', SUM(transfer_real_amount_14),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_14),
                                    'cost', SUM(cost_14),
                                    'tp_allowance', SUM(tp_allowance_14),
                                    'rounding', SUM(rounding_14),
                                    'overflow_amount', SUM(overflow_amount_14),
                                    'change_amount', SUM(change_amount_14),
                                    'item_count', SUM(item_count_14),
                                    'item_count_returned', SUM(item_count_returned_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_15),
                                    'pay_amount', SUM(pay_amount_15),
                                    'transfer_real_amount', SUM(transfer_real_amount_15),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_15),
                                    'cost', SUM(cost_15),
                                    'tp_allowance', SUM(tp_allowance_15),
                                    'rounding', SUM(rounding_15),
                                    'overflow_amount', SUM(overflow_amount_15),
                                    'change_amount', SUM(change_amount_15),
                                    'item_count', SUM(item_count_15),
                                    'item_count_returned', SUM(item_count_returned_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_16),
                                    'pay_amount', SUM(pay_amount_16),
                                    'transfer_real_amount', SUM(transfer_real_amount_16),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_16),
                                    'cost', SUM(cost_16),
                                    'tp_allowance', SUM(tp_allowance_16),
                                    'rounding', SUM(rounding_16),
                                    'overflow_amount', SUM(overflow_amount_16),
                                    'change_amount', SUM(change_amount_16),
                                    'item_count', SUM(item_count_16),
                                    'item_count_returned', SUM(item_count_returned_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_17),
                                    'pay_amount', SUM(pay_amount_17),
                                    'transfer_real_amount', SUM(transfer_real_amount_17),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_17),
                                    'cost', SUM(cost_17),
                                    'tp_allowance', SUM(tp_allowance_17),
                                    'rounding', SUM(rounding_17),
                                    'overflow_amount', SUM(overflow_amount_17),
                                    'change_amount', SUM(change_amount_17),
                                    'item_count', SUM(item_count_17),
                                    'item_count_returned', SUM(item_count_returned_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_18),
                                    'pay_amount', SUM(pay_amount_18),
                                    'transfer_real_amount', SUM(transfer_real_amount_18),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_18),
                                    'cost', SUM(cost_18),
                                    'tp_allowance', SUM(tp_allowance_18),
                                    'rounding', SUM(rounding_18),
                                    'overflow_amount', SUM(overflow_amount_18),
                                    'change_amount', SUM(change_amount_18),
                                    'item_count', SUM(item_count_18),
                                    'item_count_returned', SUM(item_count_returned_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_19),
                                    'pay_amount', SUM(pay_amount_19),
                                    'transfer_real_amount', SUM(transfer_real_amount_19),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_19),
                                    'cost', SUM(cost_19),
                                    'tp_allowance', SUM(tp_allowance_19),
                                    'rounding', SUM(rounding_19),
                                    'overflow_amount', SUM(overflow_amount_19),
                                    'change_amount', SUM(change_amount_19),
                                    'item_count', SUM(item_count_19),
                                    'item_count_returned', SUM(item_count_returned_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_20),
                                    'pay_amount', SUM(pay_amount_20),
                                    'transfer_real_amount', SUM(transfer_real_amount_20),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_20),
                                    'cost', SUM(cost_20),
                                    'tp_allowance', SUM(tp_allowance_20),
                                    'rounding', SUM(rounding_20),
                                    'overflow_amount', SUM(overflow_amount_20),
                                    'change_amount', SUM(change_amount_20),
                                    'item_count', SUM(item_count_20),
                                    'item_count_returned', SUM(item_count_returned_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_21),
                                    'pay_amount', SUM(pay_amount_21),
                                    'transfer_real_amount', SUM(transfer_real_amount_21),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_21),
                                    'cost', SUM(cost_21),
                                    'tp_allowance', SUM(tp_allowance_21),
                                    'rounding', SUM(rounding_21),
                                    'overflow_amount', SUM(overflow_amount_21),
                                    'change_amount', SUM(change_amount_21),
                                    'item_count', SUM(item_count_21),
                                    'item_count_returned', SUM(item_count_returned_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_22),
                                    'pay_amount', SUM(pay_amount_22),
                                    'transfer_real_amount', SUM(transfer_real_amount_22),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_22),
                                    'cost', SUM(cost_22),
                                    'tp_allowance', SUM(tp_allowance_22),
                                    'rounding', SUM(rounding_22),
                                    'overflow_amount', SUM(overflow_amount_22),
                                    'change_amount', SUM(change_amount_22),
                                    'item_count', SUM(item_count_22),
                                    'item_count_returned', SUM(item_count_returned_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_23),
                                    'pay_amount', SUM(pay_amount_23),
                                    'transfer_real_amount', SUM(transfer_real_amount_23),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_23),
                                    'cost', SUM(cost_23),
                                    'tp_allowance', SUM(tp_allowance_23),
                                    'rounding', SUM(rounding_23),
                                    'overflow_amount', SUM(overflow_amount_23),
                                    'change_amount', SUM(change_amount_23),
                                    'item_count', SUM(item_count_23),
                                    'item_count_returned', SUM(item_count_returned_23)
                            )
                    ) AS "data",
			

                    JSON_AGG(
                            JSON_BUILD_OBJECT(
                                    'payment_id', payment_id,
                                    'payment_code', payment_code,
                                    'payment_name', payment_name,
                                    'h00', JSON_BUILD_OBJECT(
                                            'receivable', receivable_00,
                                            'pay_amount', pay_amount_00,
                                            'transfer_real_amount', transfer_real_amount_00,
                                            'payment_transfer_amount', payment_transfer_amount_00,
                                            'cost', cost_00,
                                            'tp_allowance', tp_allowance_00,
                                            'rounding', rounding_00,
                                            'overflow_amount', overflow_amount_00,
                                            'change_amount', change_amount_00,
                                            'item_count', item_count_00,
                                            'item_count_returned', item_count_returned_00
                                    ),
                                    'h01', JSON_BUILD_OBJECT(
                                            'receivable', receivable_01,
                                            'pay_amount', pay_amount_01,
                                            'transfer_real_amount', transfer_real_amount_01,
                                            'payment_transfer_amount', payment_transfer_amount_01,
                                            'cost', cost_01,
                                            'tp_allowance', tp_allowance_01,
                                            'rounding', rounding_01,
                                            'overflow_amount', overflow_amount_01,
                                            'change_amount', change_amount_01,
                                            'item_count', item_count_01,
                                            'item_count_returned', item_count_returned_01
                                    ),
                                    'h02', JSON_BUILD_OBJECT(
                                            'receivable', receivable_02,
                                            'pay_amount', pay_amount_02,
                                            'transfer_real_amount', transfer_real_amount_02,
                                            'payment_transfer_amount', payment_transfer_amount_02,
                                            'cost', cost_02,
                                            'tp_allowance', tp_allowance_02,
                                            'rounding', rounding_02,
                                            'overflow_amount', overflow_amount_02,
                                            'change_amount', change_amount_02,
                                            'item_count', item_count_02,
                                            'item_count_returned', item_count_returned_02
                                    ),
                                    'h03', JSON_BUILD_OBJECT(
                                            'receivable', receivable_03,
                                            'pay_amount', pay_amount_03,
                                            'transfer_real_amount', transfer_real_amount_03,
                                            'payment_transfer_amount', payment_transfer_amount_03,
                                            'cost', cost_03,
                                            'tp_allowance', tp_allowance_03,
                                            'rounding', rounding_03,
                                            'overflow_amount', overflow_amount_03,
                                            'change_amount', change_amount_03,
                                            'item_count', item_count_03,
                                            'item_count_returned', item_count_returned_03
                                    ),
                                    'h04', JSON_BUILD_OBJECT(
                                            'receivable', receivable_04,
                                            'pay_amount', pay_amount_04,
                                            'transfer_real_amount', transfer_real_amount_04,
                                            'payment_transfer_amount', payment_transfer_amount_04,
                                            'cost', cost_04,
                                            'tp_allowance', tp_allowance_04,
                                            'rounding', rounding_04,
                                            'overflow_amount', overflow_amount_04,
                                            'change_amount', change_amount_04,
                                            'item_count', item_count_04,
                                            'item_count_returned', item_count_returned_04
                                    ),
                                    'h05', JSON_BUILD_OBJECT(
                                            'receivable', receivable_05,
                                            'pay_amount', pay_amount_05,
                                            'transfer_real_amount', transfer_real_amount_05,
                                            'payment_transfer_amount', payment_transfer_amount_05,
                                            'cost', cost_05,
                                            'tp_allowance', tp_allowance_05,
                                            'rounding', rounding_05,
                                            'overflow_amount', overflow_amount_05,
                                            'change_amount', change_amount_05,
                                            'item_count', item_count_05,
                                            'item_count_returned', item_count_returned_05
                                    ),
                                    'h06', JSON_BUILD_OBJECT(
                                            'receivable', receivable_06,
                                            'pay_amount', pay_amount_06,
                                            'transfer_real_amount', transfer_real_amount_06,
                                            'payment_transfer_amount', payment_transfer_amount_06,
                                            'cost', cost_06,
                                            'tp_allowance', tp_allowance_06,
                                            'rounding', rounding_06,
                                            'overflow_amount', overflow_amount_06,
                                            'change_amount', change_amount_06,
                                            'item_count', item_count_06,
                                            'item_count_returned', item_count_returned_06
                                    ),
                                    'h07', JSON_BUILD_OBJECT(
                                            'receivable', receivable_07,
                                            'pay_amount', pay_amount_07,
                                            'transfer_real_amount', transfer_real_amount_07,
                                            'payment_transfer_amount', payment_transfer_amount_07,
                                            'cost', cost_07,
                                            'tp_allowance', tp_allowance_07,
                                            'rounding', rounding_07,
                                            'overflow_amount', overflow_amount_07,
                                            'change_amount', change_amount_07,
                                            'item_count', item_count_07,
                                            'item_count_returned', item_count_returned_07
                                    ),
                                    'h08', JSON_BUILD_OBJECT(
                                            'receivable', receivable_08,
                                            'pay_amount', pay_amount_08,
                                            'transfer_real_amount', transfer_real_amount_08,
                                            'payment_transfer_amount', payment_transfer_amount_08,
                                            'cost', cost_08,
                                            'tp_allowance', tp_allowance_08,
                                            'rounding', rounding_08,
                                            'overflow_amount', overflow_amount_08,
                                            'change_amount', change_amount_08,
                                            'item_count', item_count_08,
                                            'item_count_returned', item_count_returned_08
                                    ),
                                    'h09', JSON_BUILD_OBJECT(
                                            'receivable', receivable_09,
                                            'pay_amount', pay_amount_09,
                                            'transfer_real_amount', transfer_real_amount_09,
                                            'payment_transfer_amount', payment_transfer_amount_09,
                                            'cost', cost_09,
                                            'tp_allowance', tp_allowance_09,
                                            'rounding', rounding_09,
                                            'overflow_amount', overflow_amount_09,
                                            'change_amount', change_amount_09,
                                            'item_count', item_count_09,
                                            'item_count_returned', item_count_returned_09
                                    ),
                                    'h10', JSON_BUILD_OBJECT(
                                            'receivable', receivable_10,
                                            'pay_amount', pay_amount_10,
                                            'transfer_real_amount', transfer_real_amount_10,
                                            'payment_transfer_amount', payment_transfer_amount_10,
                                            'cost', cost_10,
                                            'tp_allowance', tp_allowance_10,
                                            'rounding', rounding_10,
                                            'overflow_amount', overflow_amount_10,
                                            'change_amount', change_amount_10,
                                            'item_count', item_count_10,
                                            'item_count_returned', item_count_returned_10
                                    ),
                                    'h11', JSON_BUILD_OBJECT(
                                            'receivable', receivable_11,
                                            'pay_amount', pay_amount_11,
                                            'transfer_real_amount', transfer_real_amount_11,
                                            'payment_transfer_amount', payment_transfer_amount_11,
                                            'cost', cost_11,
                                            'tp_allowance', tp_allowance_11,
                                            'rounding', rounding_11,
                                            'overflow_amount', overflow_amount_11,
                                            'change_amount', change_amount_11,
                                            'item_count', item_count_11,
                                            'item_count_returned', item_count_returned_11
                                    ),
                                    'h12', JSON_BUILD_OBJECT(
                                            'receivable', receivable_12,
                                            'pay_amount', pay_amount_12,
                                            'transfer_real_amount', transfer_real_amount_12,
                                            'payment_transfer_amount', payment_transfer_amount_12,
                                            'cost', cost_12,
                                            'tp_allowance', tp_allowance_12,
                                            'rounding', rounding_12,
                                            'overflow_amount', overflow_amount_12,
                                            'change_amount', change_amount_12,
                                            'item_count', item_count_12,
                                            'item_count_returned', item_count_returned_12
                                    ),
                                    'h13', JSON_BUILD_OBJECT(
                                            'receivable', receivable_13,
                                            'pay_amount', pay_amount_13,
                                            'transfer_real_amount', transfer_real_amount_13,
                                            'payment_transfer_amount', payment_transfer_amount_13,
                                            'cost', cost_13,
                                            'tp_allowance', tp_allowance_13,
                                            'rounding', rounding_13,
                                            'overflow_amount', overflow_amount_13,
                                            'change_amount', change_amount_13,
                                            'item_count', item_count_13,
                                            'item_count_returned', item_count_returned_13
                                    ),
                                    'h14', JSON_BUILD_OBJECT(
                                            'receivable', receivable_14,
                                            'pay_amount', pay_amount_14,
                                            'transfer_real_amount', transfer_real_amount_14,
                                            'payment_transfer_amount', payment_transfer_amount_14,
                                            'cost', cost_14,
                                            'tp_allowance', tp_allowance_14,
                                            'rounding', rounding_14,
                                            'overflow_amount', overflow_amount_14,
                                            'change_amount', change_amount_14,
                                            'item_count', item_count_14,
                                            'item_count_returned', item_count_returned_14
                                    ),
                                    'h15', JSON_BUILD_OBJECT(
                                            'receivable', receivable_15,
                                            'pay_amount', pay_amount_15,
                                            'transfer_real_amount', transfer_real_amount_15,
                                            'payment_transfer_amount', payment_transfer_amount_15,
                                            'cost', cost_15,
                                            'tp_allowance', tp_allowance_15,
                                            'rounding', rounding_15,
                                            'overflow_amount', overflow_amount_15,
                                            'change_amount', change_amount_15,
                                            'item_count', item_count_15,
                                            'item_count_returned', item_count_returned_15
                                    ),
                                    'h16', JSON_BUILD_OBJECT(
                                            'receivable', receivable_16,
                                            'pay_amount', pay_amount_16,
                                            'transfer_real_amount', transfer_real_amount_16,
                                            'payment_transfer_amount', payment_transfer_amount_16,
                                            'cost', cost_16,
                                            'tp_allowance', tp_allowance_16,
                                            'rounding', rounding_16,
                                            'overflow_amount', overflow_amount_16,
                                            'change_amount', change_amount_16,
                                            'item_count', item_count_16,
                                            'item_count_returned', item_count_returned_16
                                    ),
                                    'h17', JSON_BUILD_OBJECT(
                                            'receivable', receivable_17,
                                            'pay_amount', pay_amount_17,
                                            'transfer_real_amount', transfer_real_amount_17,
                                            'payment_transfer_amount', payment_transfer_amount_17,
                                            'cost', cost_17,
                                            'tp_allowance', tp_allowance_17,
                                            'rounding', rounding_17,
                                            'overflow_amount', overflow_amount_17,
                                            'change_amount', change_amount_17,
                                            'item_count', item_count_17,
                                            'item_count_returned', item_count_returned_17
                                    ),
                                    'h18', JSON_BUILD_OBJECT(
                                            'receivable', receivable_18,
                                            'pay_amount', pay_amount_18,
                                            'transfer_real_amount', transfer_real_amount_18,
                                            'payment_transfer_amount', payment_transfer_amount_18,
                                            'cost', cost_18,
                                            'tp_allowance', tp_allowance_18,
                                            'rounding', rounding_18,
                                            'overflow_amount', overflow_amount_18,
                                            'change_amount', change_amount_18,
                                            'item_count', item_count_18,
                                            'item_count_returned', item_count_returned_18
                                    ),
                                    'h19', JSON_BUILD_OBJECT(
                                            'receivable', receivable_19,
                                            'pay_amount', pay_amount_19,
                                            'transfer_real_amount', transfer_real_amount_19,
                                            'payment_transfer_amount', payment_transfer_amount_19,
                                            'cost', cost_19,
                                            'tp_allowance', tp_allowance_19,
                                            'rounding', rounding_19,
                                            'overflow_amount', overflow_amount_19,
                                            'change_amount', change_amount_19,
                                            'item_count', item_count_19,
                                            'item_count_returned', item_count_returned_19
                                    ),
                                    'h20', JSON_BUILD_OBJECT(
                                            'receivable', receivable_20,
                                            'pay_amount', pay_amount_20,
                                            'transfer_real_amount', transfer_real_amount_20,
                                            'payment_transfer_amount', payment_transfer_amount_20,
                                            'cost', cost_20,
                                            'tp_allowance', tp_allowance_20,
                                            'rounding', rounding_20,
                                            'overflow_amount', overflow_amount_20,
                                            'change_amount', change_amount_20,
                                            'item_count', item_count_20,
                                            'item_count_returned', item_count_returned_20
                                    ),
                                    'h21', JSON_BUILD_OBJECT(
                                            'receivable', receivable_21,
                                            'pay_amount', pay_amount_21,
                                            'transfer_real_amount', transfer_real_amount_21,
                                            'payment_transfer_amount', payment_transfer_amount_21,
                                            'cost', cost_21,
                                            'tp_allowance', tp_allowance_21,
                                            'rounding', rounding_21,
                                            'overflow_amount', overflow_amount_21,
                                            'change_amount', change_amount_21,
                                            'item_count', item_count_21,
                                            'item_count_returned', item_count_returned_21
                                    ),
                                    'h22', JSON_BUILD_OBJECT(
                                            'receivable', receivable_22,
                                            'pay_amount', pay_amount_22,
                                            'transfer_real_amount', transfer_real_amount_22,
                                            'payment_transfer_amount', payment_transfer_amount_22,
                                            'cost', cost_22,
                                            'tp_allowance', tp_allowance_22,
                                            'rounding', rounding_22,
                                            'overflow_amount', overflow_amount_22,
                                            'change_amount', change_amount_22,
                                            'item_count', item_count_22,
                                            'item_count_returned', item_count_returned_22
                                    ),
                                    'h23', JSON_BUILD_OBJECT(
                                            'receivable', receivable_23,
                                            'pay_amount', pay_amount_23,
                                            'transfer_real_amount', transfer_real_amount_23,
                                            'payment_transfer_amount', payment_transfer_amount_23,
                                            'cost', cost_23,
                                            'tp_allowance', tp_allowance_23,
                                            'rounding', rounding_23,
                                            'overflow_amount', overflow_amount_23,
                                            'change_amount', change_amount_23,
                                            'item_count', item_count_23,
                                            'item_count_returned', item_count_returned_23
                                    )
                            )
                    ) AS "child"
				FROM base_payments_for_period
				GROUP BY
					bus_date,
					region_id
			)
			SELECT
				to_char(for_payment.bus_date,'YYYY-MM-DD') AS bus_date,
				for_payment.region_id AS region_id,
				for_payment.store_type AS store_type,
				bd.days AS business_days,

			    for_payment.receivable AS receivable,
                for_payment.pay_amount AS pay_amount,
                for_payment.transfer_real_amount AS transfer_real_amount,
                for_payment.payment_transfer_amount AS payment_transfer_amount,
                for_payment.cost AS cost,
                for_payment.tp_allowance AS tp_allowance,
                for_payment.rounding AS rounding,
                for_payment.overflow_amount AS overflow_amount,
                for_payment.change_amount AS change_amount,
                for_payment.item_count AS item_count,
                for_payment.item_count_returned AS item_count_returned,
				COALESCE(for_payment.data, '{}'::json) AS data,
				COALESCE(for_payment.child, '[]'::json) AS child,

				0 AS total --Summary时的汇总条数
			FROM
				base_payments_by_date_and_region_for_payment for_payment
					LEFT JOIN business_days bd
						ON for_payment.region_id = bd.region_id
			ORDER BY for_payment.bus_date DESC, for_payment.region_id;
 		`
		summarySQL = `
		--支付时段报表详细信息 SUMMARY
				WITH base_payments AS (
					SELECT
						fact.bus_date AS bus_date,
						{REGION_ID} AS region_id,
				
						extract(hour from fact.order_time) AS hours,
				
						COALESCE(SUM(receivable), 0) AS receivable, --应付金额
                        COALESCE(SUM(pay_amount), 0) AS pay_amount, --实付金额
                        COALESCE(SUM(CASE WHEN finance_pay_amount_used THEN finance_pay_amount ELSE tp_allowance+cost END), 0) AS transfer_real_amount, --财务实收金额(转换后)
                        COALESCE(SUM(transfer_amount-overflow_amount), 0) AS payment_transfer_amount, --支付转折扣
                        COALESCE(SUM(cost), 0) AS cost, --用户实际购买金额
                        COALESCE(SUM(tp_allowance), 0) AS tp_allowance, --第三方补贴金额
                        COALESCE(SUM(rounding), 0) AS rounding, --抹零
                        COALESCE(SUM(overflow_amount), 0) AS overflow_amount, --溢收
                        COALESCE(SUM(change_amount), 0) AS change_amount, --找零
                        COALESCE(SUM(case when refunded then 0 else qty end ), 0) AS item_count, --支付次数
                        COALESCE(SUM(CASE WHEN refunded THEN qty ELSE 0 END), 0) AS item_count_returned --支付退单数
					FROM
						sales_payment_amounts fact
							LEFT JOIN store_caches
								ON fact.store_id = store_caches.id
					WHERE
						{WHERE}
					GROUP BY
						fact.bus_date,
						{REGION_ID},
						fact.order_time
					ORDER BY
						fact.bus_date DESC,
						{REGION_ID}
				), base_payments_for_period AS (
					SELECT
						bus_date,
						region_id,
				
						SUM(receivable) AS receivable, --应付金额
                        SUM(pay_amount) AS pay_amount, --实付金额
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                        SUM(cost) AS cost, --用户实际购买金额
                        SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(item_count) AS item_count, --支付次数
                        SUM(item_count_returned) AS item_count_returned, --支付退单数
				
						SUM(CASE WHEN hours = 0 THEN receivable ELSE 0 END) AS receivable_00,
                        SUM(CASE WHEN hours = 1 THEN receivable ELSE 0 END) AS receivable_01,
                        SUM(CASE WHEN hours = 2 THEN receivable ELSE 0 END) AS receivable_02,
                        SUM(CASE WHEN hours = 3 THEN receivable ELSE 0 END) AS receivable_03,
                        SUM(CASE WHEN hours = 4 THEN receivable ELSE 0 END) AS receivable_04,
                        SUM(CASE WHEN hours = 5 THEN receivable ELSE 0 END) AS receivable_05,
                        SUM(CASE WHEN hours = 6 THEN receivable ELSE 0 END) AS receivable_06,
                        SUM(CASE WHEN hours = 7 THEN receivable ELSE 0 END) AS receivable_07,
                        SUM(CASE WHEN hours = 8 THEN receivable ELSE 0 END) AS receivable_08,
                        SUM(CASE WHEN hours = 9 THEN receivable ELSE 0 END) AS receivable_09,
                        SUM(CASE WHEN hours = 10 THEN receivable ELSE 0 END) AS receivable_10,
                        SUM(CASE WHEN hours = 11 THEN receivable ELSE 0 END) AS receivable_11,
                        SUM(CASE WHEN hours = 12 THEN receivable ELSE 0 END) AS receivable_12,
                        SUM(CASE WHEN hours = 13 THEN receivable ELSE 0 END) AS receivable_13,
                        SUM(CASE WHEN hours = 14 THEN receivable ELSE 0 END) AS receivable_14,
                        SUM(CASE WHEN hours = 15 THEN receivable ELSE 0 END) AS receivable_15,
                        SUM(CASE WHEN hours = 16 THEN receivable ELSE 0 END) AS receivable_16,
                        SUM(CASE WHEN hours = 17 THEN receivable ELSE 0 END) AS receivable_17,
                        SUM(CASE WHEN hours = 18 THEN receivable ELSE 0 END) AS receivable_18,
                        SUM(CASE WHEN hours = 19 THEN receivable ELSE 0 END) AS receivable_19,
                        SUM(CASE WHEN hours = 20 THEN receivable ELSE 0 END) AS receivable_20,
                        SUM(CASE WHEN hours = 21 THEN receivable ELSE 0 END) AS receivable_21,
                        SUM(CASE WHEN hours = 22 THEN receivable ELSE 0 END) AS receivable_22,
                        SUM(CASE WHEN hours = 23 THEN receivable ELSE 0 END) AS receivable_23,

                        SUM(CASE WHEN hours = 0 THEN pay_amount ELSE 0 END) AS pay_amount_00,
                        SUM(CASE WHEN hours = 1 THEN pay_amount ELSE 0 END) AS pay_amount_01,
                        SUM(CASE WHEN hours = 2 THEN pay_amount ELSE 0 END) AS pay_amount_02,
                        SUM(CASE WHEN hours = 3 THEN pay_amount ELSE 0 END) AS pay_amount_03,
                        SUM(CASE WHEN hours = 4 THEN pay_amount ELSE 0 END) AS pay_amount_04,
                        SUM(CASE WHEN hours = 5 THEN pay_amount ELSE 0 END) AS pay_amount_05,
                        SUM(CASE WHEN hours = 6 THEN pay_amount ELSE 0 END) AS pay_amount_06,
                        SUM(CASE WHEN hours = 7 THEN pay_amount ELSE 0 END) AS pay_amount_07,
                        SUM(CASE WHEN hours = 8 THEN pay_amount ELSE 0 END) AS pay_amount_08,
                        SUM(CASE WHEN hours = 9 THEN pay_amount ELSE 0 END) AS pay_amount_09,
                        SUM(CASE WHEN hours = 10 THEN pay_amount ELSE 0 END) AS pay_amount_10,
                        SUM(CASE WHEN hours = 11 THEN pay_amount ELSE 0 END) AS pay_amount_11,
                        SUM(CASE WHEN hours = 12 THEN pay_amount ELSE 0 END) AS pay_amount_12,
                        SUM(CASE WHEN hours = 13 THEN pay_amount ELSE 0 END) AS pay_amount_13,
                        SUM(CASE WHEN hours = 14 THEN pay_amount ELSE 0 END) AS pay_amount_14,
                        SUM(CASE WHEN hours = 15 THEN pay_amount ELSE 0 END) AS pay_amount_15,
                        SUM(CASE WHEN hours = 16 THEN pay_amount ELSE 0 END) AS pay_amount_16,
                        SUM(CASE WHEN hours = 17 THEN pay_amount ELSE 0 END) AS pay_amount_17,
                        SUM(CASE WHEN hours = 18 THEN pay_amount ELSE 0 END) AS pay_amount_18,
                        SUM(CASE WHEN hours = 19 THEN pay_amount ELSE 0 END) AS pay_amount_19,
                        SUM(CASE WHEN hours = 20 THEN pay_amount ELSE 0 END) AS pay_amount_20,
                        SUM(CASE WHEN hours = 21 THEN pay_amount ELSE 0 END) AS pay_amount_21,
                        SUM(CASE WHEN hours = 22 THEN pay_amount ELSE 0 END) AS pay_amount_22,
                        SUM(CASE WHEN hours = 23 THEN pay_amount ELSE 0 END) AS pay_amount_23,

                        SUM(CASE WHEN hours = 0 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_00,
                        SUM(CASE WHEN hours = 1 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_01,
                        SUM(CASE WHEN hours = 2 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_02,
                        SUM(CASE WHEN hours = 3 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_03,
                        SUM(CASE WHEN hours = 4 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_04,
                        SUM(CASE WHEN hours = 5 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_05,
                        SUM(CASE WHEN hours = 6 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_06,
                        SUM(CASE WHEN hours = 7 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_07,
                        SUM(CASE WHEN hours = 8 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_08,
                        SUM(CASE WHEN hours = 9 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_09,
                        SUM(CASE WHEN hours = 10 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_10,
                        SUM(CASE WHEN hours = 11 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_11,
                        SUM(CASE WHEN hours = 12 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_12,
                        SUM(CASE WHEN hours = 13 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_13,
                        SUM(CASE WHEN hours = 14 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_14,
                        SUM(CASE WHEN hours = 15 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_15,
                        SUM(CASE WHEN hours = 16 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_16,
                        SUM(CASE WHEN hours = 17 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_17,
                        SUM(CASE WHEN hours = 18 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_18,
                        SUM(CASE WHEN hours = 19 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_19,
                        SUM(CASE WHEN hours = 20 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_20,
                        SUM(CASE WHEN hours = 21 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_21,
                        SUM(CASE WHEN hours = 22 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_22,
                        SUM(CASE WHEN hours = 23 THEN transfer_real_amount ELSE 0 END) AS transfer_real_amount_23,

                        SUM(CASE WHEN hours = 0 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_00,
                        SUM(CASE WHEN hours = 1 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_01,
                        SUM(CASE WHEN hours = 2 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_02,
                        SUM(CASE WHEN hours = 3 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_03,
                        SUM(CASE WHEN hours = 4 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_04,
                        SUM(CASE WHEN hours = 5 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_05,
                        SUM(CASE WHEN hours = 6 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_06,
                        SUM(CASE WHEN hours = 7 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_07,
                        SUM(CASE WHEN hours = 8 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_08,
                        SUM(CASE WHEN hours = 9 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_09,
                        SUM(CASE WHEN hours = 10 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_10,
                        SUM(CASE WHEN hours = 11 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_11,
                        SUM(CASE WHEN hours = 12 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_12,
                        SUM(CASE WHEN hours = 13 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_13,
                        SUM(CASE WHEN hours = 14 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_14,
                        SUM(CASE WHEN hours = 15 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_15,
                        SUM(CASE WHEN hours = 16 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_16,
                        SUM(CASE WHEN hours = 17 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_17,
                        SUM(CASE WHEN hours = 18 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_18,
                        SUM(CASE WHEN hours = 19 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_19,
                        SUM(CASE WHEN hours = 20 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_20,
                        SUM(CASE WHEN hours = 21 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_21,
                        SUM(CASE WHEN hours = 22 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_22,
                        SUM(CASE WHEN hours = 23 THEN payment_transfer_amount ELSE 0 END) AS payment_transfer_amount_23,

                        SUM(CASE WHEN hours = 0 THEN cost ELSE 0 END) AS cost_00,
                        SUM(CASE WHEN hours = 1 THEN cost ELSE 0 END) AS cost_01,
                        SUM(CASE WHEN hours = 2 THEN cost ELSE 0 END) AS cost_02,
                        SUM(CASE WHEN hours = 3 THEN cost ELSE 0 END) AS cost_03,
                        SUM(CASE WHEN hours = 4 THEN cost ELSE 0 END) AS cost_04,
                        SUM(CASE WHEN hours = 5 THEN cost ELSE 0 END) AS cost_05,
                        SUM(CASE WHEN hours = 6 THEN cost ELSE 0 END) AS cost_06,
                        SUM(CASE WHEN hours = 7 THEN cost ELSE 0 END) AS cost_07,
                        SUM(CASE WHEN hours = 8 THEN cost ELSE 0 END) AS cost_08,
                        SUM(CASE WHEN hours = 9 THEN cost ELSE 0 END) AS cost_09,
                        SUM(CASE WHEN hours = 10 THEN cost ELSE 0 END) AS cost_10,
                        SUM(CASE WHEN hours = 11 THEN cost ELSE 0 END) AS cost_11,
                        SUM(CASE WHEN hours = 12 THEN cost ELSE 0 END) AS cost_12,
                        SUM(CASE WHEN hours = 13 THEN cost ELSE 0 END) AS cost_13,
                        SUM(CASE WHEN hours = 14 THEN cost ELSE 0 END) AS cost_14,
                        SUM(CASE WHEN hours = 15 THEN cost ELSE 0 END) AS cost_15,
                        SUM(CASE WHEN hours = 16 THEN cost ELSE 0 END) AS cost_16,
                        SUM(CASE WHEN hours = 17 THEN cost ELSE 0 END) AS cost_17,
                        SUM(CASE WHEN hours = 18 THEN cost ELSE 0 END) AS cost_18,
                        SUM(CASE WHEN hours = 19 THEN cost ELSE 0 END) AS cost_19,
                        SUM(CASE WHEN hours = 20 THEN cost ELSE 0 END) AS cost_20,
                        SUM(CASE WHEN hours = 21 THEN cost ELSE 0 END) AS cost_21,
                        SUM(CASE WHEN hours = 22 THEN cost ELSE 0 END) AS cost_22,
                        SUM(CASE WHEN hours = 23 THEN cost ELSE 0 END) AS cost_23,

                        SUM(CASE WHEN hours = 0 THEN tp_allowance ELSE 0 END) AS tp_allowance_00,
                        SUM(CASE WHEN hours = 1 THEN tp_allowance ELSE 0 END) AS tp_allowance_01,
                        SUM(CASE WHEN hours = 2 THEN tp_allowance ELSE 0 END) AS tp_allowance_02,
                        SUM(CASE WHEN hours = 3 THEN tp_allowance ELSE 0 END) AS tp_allowance_03,
                        SUM(CASE WHEN hours = 4 THEN tp_allowance ELSE 0 END) AS tp_allowance_04,
                        SUM(CASE WHEN hours = 5 THEN tp_allowance ELSE 0 END) AS tp_allowance_05,
                        SUM(CASE WHEN hours = 6 THEN tp_allowance ELSE 0 END) AS tp_allowance_06,
                        SUM(CASE WHEN hours = 7 THEN tp_allowance ELSE 0 END) AS tp_allowance_07,
                        SUM(CASE WHEN hours = 8 THEN tp_allowance ELSE 0 END) AS tp_allowance_08,
                        SUM(CASE WHEN hours = 9 THEN tp_allowance ELSE 0 END) AS tp_allowance_09,
                        SUM(CASE WHEN hours = 10 THEN tp_allowance ELSE 0 END) AS tp_allowance_10,
                        SUM(CASE WHEN hours = 11 THEN tp_allowance ELSE 0 END) AS tp_allowance_11,
                        SUM(CASE WHEN hours = 12 THEN tp_allowance ELSE 0 END) AS tp_allowance_12,
                        SUM(CASE WHEN hours = 13 THEN tp_allowance ELSE 0 END) AS tp_allowance_13,
                        SUM(CASE WHEN hours = 14 THEN tp_allowance ELSE 0 END) AS tp_allowance_14,
                        SUM(CASE WHEN hours = 15 THEN tp_allowance ELSE 0 END) AS tp_allowance_15,
                        SUM(CASE WHEN hours = 16 THEN tp_allowance ELSE 0 END) AS tp_allowance_16,
                        SUM(CASE WHEN hours = 17 THEN tp_allowance ELSE 0 END) AS tp_allowance_17,
                        SUM(CASE WHEN hours = 18 THEN tp_allowance ELSE 0 END) AS tp_allowance_18,
                        SUM(CASE WHEN hours = 19 THEN tp_allowance ELSE 0 END) AS tp_allowance_19,
                        SUM(CASE WHEN hours = 20 THEN tp_allowance ELSE 0 END) AS tp_allowance_20,
                        SUM(CASE WHEN hours = 21 THEN tp_allowance ELSE 0 END) AS tp_allowance_21,
                        SUM(CASE WHEN hours = 22 THEN tp_allowance ELSE 0 END) AS tp_allowance_22,
                        SUM(CASE WHEN hours = 23 THEN tp_allowance ELSE 0 END) AS tp_allowance_23,

                        SUM(CASE WHEN hours = 0 THEN rounding ELSE 0 END) AS rounding_00,
                        SUM(CASE WHEN hours = 1 THEN rounding ELSE 0 END) AS rounding_01,
                        SUM(CASE WHEN hours = 2 THEN rounding ELSE 0 END) AS rounding_02,
                        SUM(CASE WHEN hours = 3 THEN rounding ELSE 0 END) AS rounding_03,
                        SUM(CASE WHEN hours = 4 THEN rounding ELSE 0 END) AS rounding_04,
                        SUM(CASE WHEN hours = 5 THEN rounding ELSE 0 END) AS rounding_05,
                        SUM(CASE WHEN hours = 6 THEN rounding ELSE 0 END) AS rounding_06,
                        SUM(CASE WHEN hours = 7 THEN rounding ELSE 0 END) AS rounding_07,
                        SUM(CASE WHEN hours = 8 THEN rounding ELSE 0 END) AS rounding_08,
                        SUM(CASE WHEN hours = 9 THEN rounding ELSE 0 END) AS rounding_09,
                        SUM(CASE WHEN hours = 10 THEN rounding ELSE 0 END) AS rounding_10,
                        SUM(CASE WHEN hours = 11 THEN rounding ELSE 0 END) AS rounding_11,
                        SUM(CASE WHEN hours = 12 THEN rounding ELSE 0 END) AS rounding_12,
                        SUM(CASE WHEN hours = 13 THEN rounding ELSE 0 END) AS rounding_13,
                        SUM(CASE WHEN hours = 14 THEN rounding ELSE 0 END) AS rounding_14,
                        SUM(CASE WHEN hours = 15 THEN rounding ELSE 0 END) AS rounding_15,
                        SUM(CASE WHEN hours = 16 THEN rounding ELSE 0 END) AS rounding_16,
                        SUM(CASE WHEN hours = 17 THEN rounding ELSE 0 END) AS rounding_17,
                        SUM(CASE WHEN hours = 18 THEN rounding ELSE 0 END) AS rounding_18,
                        SUM(CASE WHEN hours = 19 THEN rounding ELSE 0 END) AS rounding_19,
                        SUM(CASE WHEN hours = 20 THEN rounding ELSE 0 END) AS rounding_20,
                        SUM(CASE WHEN hours = 21 THEN rounding ELSE 0 END) AS rounding_21,
                        SUM(CASE WHEN hours = 22 THEN rounding ELSE 0 END) AS rounding_22,
                        SUM(CASE WHEN hours = 23 THEN rounding ELSE 0 END) AS rounding_23,

                        SUM(CASE WHEN hours = 0 THEN overflow_amount ELSE 0 END) AS overflow_amount_00,
                        SUM(CASE WHEN hours = 1 THEN overflow_amount ELSE 0 END) AS overflow_amount_01,
                        SUM(CASE WHEN hours = 2 THEN overflow_amount ELSE 0 END) AS overflow_amount_02,
                        SUM(CASE WHEN hours = 3 THEN overflow_amount ELSE 0 END) AS overflow_amount_03,
                        SUM(CASE WHEN hours = 4 THEN overflow_amount ELSE 0 END) AS overflow_amount_04,
                        SUM(CASE WHEN hours = 5 THEN overflow_amount ELSE 0 END) AS overflow_amount_05,
                        SUM(CASE WHEN hours = 6 THEN overflow_amount ELSE 0 END) AS overflow_amount_06,
                        SUM(CASE WHEN hours = 7 THEN overflow_amount ELSE 0 END) AS overflow_amount_07,
                        SUM(CASE WHEN hours = 8 THEN overflow_amount ELSE 0 END) AS overflow_amount_08,
                        SUM(CASE WHEN hours = 9 THEN overflow_amount ELSE 0 END) AS overflow_amount_09,
                        SUM(CASE WHEN hours = 10 THEN overflow_amount ELSE 0 END) AS overflow_amount_10,
                        SUM(CASE WHEN hours = 11 THEN overflow_amount ELSE 0 END) AS overflow_amount_11,
                        SUM(CASE WHEN hours = 12 THEN overflow_amount ELSE 0 END) AS overflow_amount_12,
                        SUM(CASE WHEN hours = 13 THEN overflow_amount ELSE 0 END) AS overflow_amount_13,
                        SUM(CASE WHEN hours = 14 THEN overflow_amount ELSE 0 END) AS overflow_amount_14,
                        SUM(CASE WHEN hours = 15 THEN overflow_amount ELSE 0 END) AS overflow_amount_15,
                        SUM(CASE WHEN hours = 16 THEN overflow_amount ELSE 0 END) AS overflow_amount_16,
                        SUM(CASE WHEN hours = 17 THEN overflow_amount ELSE 0 END) AS overflow_amount_17,
                        SUM(CASE WHEN hours = 18 THEN overflow_amount ELSE 0 END) AS overflow_amount_18,
                        SUM(CASE WHEN hours = 19 THEN overflow_amount ELSE 0 END) AS overflow_amount_19,
                        SUM(CASE WHEN hours = 20 THEN overflow_amount ELSE 0 END) AS overflow_amount_20,
                        SUM(CASE WHEN hours = 21 THEN overflow_amount ELSE 0 END) AS overflow_amount_21,
                        SUM(CASE WHEN hours = 22 THEN overflow_amount ELSE 0 END) AS overflow_amount_22,
                        SUM(CASE WHEN hours = 23 THEN overflow_amount ELSE 0 END) AS overflow_amount_23,

                        SUM(CASE WHEN hours = 0 THEN change_amount ELSE 0 END) AS change_amount_00,
                        SUM(CASE WHEN hours = 1 THEN change_amount ELSE 0 END) AS change_amount_01,
                        SUM(CASE WHEN hours = 2 THEN change_amount ELSE 0 END) AS change_amount_02,
                        SUM(CASE WHEN hours = 3 THEN change_amount ELSE 0 END) AS change_amount_03,
                        SUM(CASE WHEN hours = 4 THEN change_amount ELSE 0 END) AS change_amount_04,
                        SUM(CASE WHEN hours = 5 THEN change_amount ELSE 0 END) AS change_amount_05,
                        SUM(CASE WHEN hours = 6 THEN change_amount ELSE 0 END) AS change_amount_06,
                        SUM(CASE WHEN hours = 7 THEN change_amount ELSE 0 END) AS change_amount_07,
                        SUM(CASE WHEN hours = 8 THEN change_amount ELSE 0 END) AS change_amount_08,
                        SUM(CASE WHEN hours = 9 THEN change_amount ELSE 0 END) AS change_amount_09,
                        SUM(CASE WHEN hours = 10 THEN change_amount ELSE 0 END) AS change_amount_10,
                        SUM(CASE WHEN hours = 11 THEN change_amount ELSE 0 END) AS change_amount_11,
                        SUM(CASE WHEN hours = 12 THEN change_amount ELSE 0 END) AS change_amount_12,
                        SUM(CASE WHEN hours = 13 THEN change_amount ELSE 0 END) AS change_amount_13,
                        SUM(CASE WHEN hours = 14 THEN change_amount ELSE 0 END) AS change_amount_14,
                        SUM(CASE WHEN hours = 15 THEN change_amount ELSE 0 END) AS change_amount_15,
                        SUM(CASE WHEN hours = 16 THEN change_amount ELSE 0 END) AS change_amount_16,
                        SUM(CASE WHEN hours = 17 THEN change_amount ELSE 0 END) AS change_amount_17,
                        SUM(CASE WHEN hours = 18 THEN change_amount ELSE 0 END) AS change_amount_18,
                        SUM(CASE WHEN hours = 19 THEN change_amount ELSE 0 END) AS change_amount_19,
                        SUM(CASE WHEN hours = 20 THEN change_amount ELSE 0 END) AS change_amount_20,
                        SUM(CASE WHEN hours = 21 THEN change_amount ELSE 0 END) AS change_amount_21,
                        SUM(CASE WHEN hours = 22 THEN change_amount ELSE 0 END) AS change_amount_22,
                        SUM(CASE WHEN hours = 23 THEN change_amount ELSE 0 END) AS change_amount_23,

                        SUM(CASE WHEN hours = 0 THEN item_count ELSE 0 END) AS item_count_00,
                        SUM(CASE WHEN hours = 1 THEN item_count ELSE 0 END) AS item_count_01,
                        SUM(CASE WHEN hours = 2 THEN item_count ELSE 0 END) AS item_count_02,
                        SUM(CASE WHEN hours = 3 THEN item_count ELSE 0 END) AS item_count_03,
                        SUM(CASE WHEN hours = 4 THEN item_count ELSE 0 END) AS item_count_04,
                        SUM(CASE WHEN hours = 5 THEN item_count ELSE 0 END) AS item_count_05,
                        SUM(CASE WHEN hours = 6 THEN item_count ELSE 0 END) AS item_count_06,
                        SUM(CASE WHEN hours = 7 THEN item_count ELSE 0 END) AS item_count_07,
                        SUM(CASE WHEN hours = 8 THEN item_count ELSE 0 END) AS item_count_08,
                        SUM(CASE WHEN hours = 9 THEN item_count ELSE 0 END) AS item_count_09,
                        SUM(CASE WHEN hours = 10 THEN item_count ELSE 0 END) AS item_count_10,
                        SUM(CASE WHEN hours = 11 THEN item_count ELSE 0 END) AS item_count_11,
                        SUM(CASE WHEN hours = 12 THEN item_count ELSE 0 END) AS item_count_12,
                        SUM(CASE WHEN hours = 13 THEN item_count ELSE 0 END) AS item_count_13,
                        SUM(CASE WHEN hours = 14 THEN item_count ELSE 0 END) AS item_count_14,
                        SUM(CASE WHEN hours = 15 THEN item_count ELSE 0 END) AS item_count_15,
                        SUM(CASE WHEN hours = 16 THEN item_count ELSE 0 END) AS item_count_16,
                        SUM(CASE WHEN hours = 17 THEN item_count ELSE 0 END) AS item_count_17,
                        SUM(CASE WHEN hours = 18 THEN item_count ELSE 0 END) AS item_count_18,
                        SUM(CASE WHEN hours = 19 THEN item_count ELSE 0 END) AS item_count_19,
                        SUM(CASE WHEN hours = 20 THEN item_count ELSE 0 END) AS item_count_20,
                        SUM(CASE WHEN hours = 21 THEN item_count ELSE 0 END) AS item_count_21,
                        SUM(CASE WHEN hours = 22 THEN item_count ELSE 0 END) AS item_count_22,
                        SUM(CASE WHEN hours = 23 THEN item_count ELSE 0 END) AS item_count_23,

                        SUM(CASE WHEN hours = 0 THEN item_count_returned ELSE 0 END) AS item_count_returned_00,
                        SUM(CASE WHEN hours = 1 THEN item_count_returned ELSE 0 END) AS item_count_returned_01,
                        SUM(CASE WHEN hours = 2 THEN item_count_returned ELSE 0 END) AS item_count_returned_02,
                        SUM(CASE WHEN hours = 3 THEN item_count_returned ELSE 0 END) AS item_count_returned_03,
                        SUM(CASE WHEN hours = 4 THEN item_count_returned ELSE 0 END) AS item_count_returned_04,
                        SUM(CASE WHEN hours = 5 THEN item_count_returned ELSE 0 END) AS item_count_returned_05,
                        SUM(CASE WHEN hours = 6 THEN item_count_returned ELSE 0 END) AS item_count_returned_06,
                        SUM(CASE WHEN hours = 7 THEN item_count_returned ELSE 0 END) AS item_count_returned_07,
                        SUM(CASE WHEN hours = 8 THEN item_count_returned ELSE 0 END) AS item_count_returned_08,
                        SUM(CASE WHEN hours = 9 THEN item_count_returned ELSE 0 END) AS item_count_returned_09,
                        SUM(CASE WHEN hours = 10 THEN item_count_returned ELSE 0 END) AS item_count_returned_10,
                        SUM(CASE WHEN hours = 11 THEN item_count_returned ELSE 0 END) AS item_count_returned_11,
                        SUM(CASE WHEN hours = 12 THEN item_count_returned ELSE 0 END) AS item_count_returned_12,
                        SUM(CASE WHEN hours = 13 THEN item_count_returned ELSE 0 END) AS item_count_returned_13,
                        SUM(CASE WHEN hours = 14 THEN item_count_returned ELSE 0 END) AS item_count_returned_14,
                        SUM(CASE WHEN hours = 15 THEN item_count_returned ELSE 0 END) AS item_count_returned_15,
                        SUM(CASE WHEN hours = 16 THEN item_count_returned ELSE 0 END) AS item_count_returned_16,
                        SUM(CASE WHEN hours = 17 THEN item_count_returned ELSE 0 END) AS item_count_returned_17,
                        SUM(CASE WHEN hours = 18 THEN item_count_returned ELSE 0 END) AS item_count_returned_18,
                        SUM(CASE WHEN hours = 19 THEN item_count_returned ELSE 0 END) AS item_count_returned_19,
                        SUM(CASE WHEN hours = 20 THEN item_count_returned ELSE 0 END) AS item_count_returned_20,
                        SUM(CASE WHEN hours = 21 THEN item_count_returned ELSE 0 END) AS item_count_returned_21,
                        SUM(CASE WHEN hours = 22 THEN item_count_returned ELSE 0 END) AS item_count_returned_22,
                        SUM(CASE WHEN hours = 23 THEN item_count_returned ELSE 0 END) AS item_count_returned_23
					FROM base_payments
					GROUP BY
						bus_date,
						region_id
				),base_payments_for_json AS (
					SELECT
						SUM(receivable) AS receivable, --应付金额
                        SUM(pay_amount) AS pay_amount, --实付金额
                        SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                        SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                        SUM(cost) AS cost, --用户实际购买金额
                        SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                        SUM(rounding) AS rounding, --抹零
                        SUM(overflow_amount) AS overflow_amount, --溢收
                        SUM(change_amount) AS change_amount, --找零
                        SUM(item_count) AS item_count, --支付次数
                        SUM(item_count_returned) AS item_count_returned, --支付退单数
				
						JSON_BUILD_OBJECT(
							'payment_id', 0,
							'payment_code', '',
							'payment_name', '',

							'h00', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_00),
                                    'pay_amount', SUM(pay_amount_00),
                                    'transfer_real_amount', SUM(transfer_real_amount_00),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_00),
                                    'cost', SUM(cost_00),
                                    'tp_allowance', SUM(tp_allowance_00),
                                    'rounding', SUM(rounding_00),
                                    'overflow_amount', SUM(overflow_amount_00),
                                    'change_amount', SUM(change_amount_00),
                                    'item_count', SUM(item_count_00),
                                    'item_count_returned', SUM(item_count_returned_00)
                            ),
                            'h01', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_01),
                                    'pay_amount', SUM(pay_amount_01),
                                    'transfer_real_amount', SUM(transfer_real_amount_01),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_01),
                                    'cost', SUM(cost_01),
                                    'tp_allowance', SUM(tp_allowance_01),
                                    'rounding', SUM(rounding_01),
                                    'overflow_amount', SUM(overflow_amount_01),
                                    'change_amount', SUM(change_amount_01),
                                    'item_count', SUM(item_count_01),
                                    'item_count_returned', SUM(item_count_returned_01)
                            ),
                            'h02', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_02),
                                    'pay_amount', SUM(pay_amount_02),
                                    'transfer_real_amount', SUM(transfer_real_amount_02),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_02),
                                    'cost', SUM(cost_02),
                                    'tp_allowance', SUM(tp_allowance_02),
                                    'rounding', SUM(rounding_02),
                                    'overflow_amount', SUM(overflow_amount_02),
                                    'change_amount', SUM(change_amount_02),
                                    'item_count', SUM(item_count_02),
                                    'item_count_returned', SUM(item_count_returned_02)
                            ),
                            'h03', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_03),
                                    'pay_amount', SUM(pay_amount_03),
                                    'transfer_real_amount', SUM(transfer_real_amount_03),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_03),
                                    'cost', SUM(cost_03),
                                    'tp_allowance', SUM(tp_allowance_03),
                                    'rounding', SUM(rounding_03),
                                    'overflow_amount', SUM(overflow_amount_03),
                                    'change_amount', SUM(change_amount_03),
                                    'item_count', SUM(item_count_03),
                                    'item_count_returned', SUM(item_count_returned_03)
                            ),
                            'h04', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_04),
                                    'pay_amount', SUM(pay_amount_04),
                                    'transfer_real_amount', SUM(transfer_real_amount_04),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_04),
                                    'cost', SUM(cost_04),
                                    'tp_allowance', SUM(tp_allowance_04),
                                    'rounding', SUM(rounding_04),
                                    'overflow_amount', SUM(overflow_amount_04),
                                    'change_amount', SUM(change_amount_04),
                                    'item_count', SUM(item_count_04),
                                    'item_count_returned', SUM(item_count_returned_04)
                            ),
                            'h05', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_05),
                                    'pay_amount', SUM(pay_amount_05),
                                    'transfer_real_amount', SUM(transfer_real_amount_05),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_05),
                                    'cost', SUM(cost_05),
                                    'tp_allowance', SUM(tp_allowance_05),
                                    'rounding', SUM(rounding_05),
                                    'overflow_amount', SUM(overflow_amount_05),
                                    'change_amount', SUM(change_amount_05),
                                    'item_count', SUM(item_count_05),
                                    'item_count_returned', SUM(item_count_returned_05)
                            ),
                            'h06', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_06),
                                    'pay_amount', SUM(pay_amount_06),
                                    'transfer_real_amount', SUM(transfer_real_amount_06),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_06),
                                    'cost', SUM(cost_06),
                                    'tp_allowance', SUM(tp_allowance_06),
                                    'rounding', SUM(rounding_06),
                                    'overflow_amount', SUM(overflow_amount_06),
                                    'change_amount', SUM(change_amount_06),
                                    'item_count', SUM(item_count_06),
                                    'item_count_returned', SUM(item_count_returned_06)
                            ),
                            'h07', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_07),
                                    'pay_amount', SUM(pay_amount_07),
                                    'transfer_real_amount', SUM(transfer_real_amount_07),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_07),
                                    'cost', SUM(cost_07),
                                    'tp_allowance', SUM(tp_allowance_07),
                                    'rounding', SUM(rounding_07),
                                    'overflow_amount', SUM(overflow_amount_07),
                                    'change_amount', SUM(change_amount_07),
                                    'item_count', SUM(item_count_07),
                                    'item_count_returned', SUM(item_count_returned_07)
                            ),
                            'h08', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_08),
                                    'pay_amount', SUM(pay_amount_08),
                                    'transfer_real_amount', SUM(transfer_real_amount_08),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_08),
                                    'cost', SUM(cost_08),
                                    'tp_allowance', SUM(tp_allowance_08),
                                    'rounding', SUM(rounding_08),
                                    'overflow_amount', SUM(overflow_amount_08),
                                    'change_amount', SUM(change_amount_08),
                                    'item_count', SUM(item_count_08),
                                    'item_count_returned', SUM(item_count_returned_08)
                            ),
                            'h09', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_09),
                                    'pay_amount', SUM(pay_amount_09),
                                    'transfer_real_amount', SUM(transfer_real_amount_09),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_09),
                                    'cost', SUM(cost_09),
                                    'tp_allowance', SUM(tp_allowance_09),
                                    'rounding', SUM(rounding_09),
                                    'overflow_amount', SUM(overflow_amount_09),
                                    'change_amount', SUM(change_amount_09),
                                    'item_count', SUM(item_count_09),
                                    'item_count_returned', SUM(item_count_returned_09)
                            ),
                            'h10', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_10),
                                    'pay_amount', SUM(pay_amount_10),
                                    'transfer_real_amount', SUM(transfer_real_amount_10),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_10),
                                    'cost', SUM(cost_10),
                                    'tp_allowance', SUM(tp_allowance_10),
                                    'rounding', SUM(rounding_10),
                                    'overflow_amount', SUM(overflow_amount_10),
                                    'change_amount', SUM(change_amount_10),
                                    'item_count', SUM(item_count_10),
                                    'item_count_returned', SUM(item_count_returned_10)
                            ),
                            'h11', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_11),
                                    'pay_amount', SUM(pay_amount_11),
                                    'transfer_real_amount', SUM(transfer_real_amount_11),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_11),
                                    'cost', SUM(cost_11),
                                    'tp_allowance', SUM(tp_allowance_11),
                                    'rounding', SUM(rounding_11),
                                    'overflow_amount', SUM(overflow_amount_11),
                                    'change_amount', SUM(change_amount_11),
                                    'item_count', SUM(item_count_11),
                                    'item_count_returned', SUM(item_count_returned_11)
                            ),
                            'h12', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_12),
                                    'pay_amount', SUM(pay_amount_12),
                                    'transfer_real_amount', SUM(transfer_real_amount_12),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_12),
                                    'cost', SUM(cost_12),
                                    'tp_allowance', SUM(tp_allowance_12),
                                    'rounding', SUM(rounding_12),
                                    'overflow_amount', SUM(overflow_amount_12),
                                    'change_amount', SUM(change_amount_12),
                                    'item_count', SUM(item_count_12),
                                    'item_count_returned', SUM(item_count_returned_12)
                            ),
                            'h13', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_13),
                                    'pay_amount', SUM(pay_amount_13),
                                    'transfer_real_amount', SUM(transfer_real_amount_13),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_13),
                                    'cost', SUM(cost_13),
                                    'tp_allowance', SUM(tp_allowance_13),
                                    'rounding', SUM(rounding_13),
                                    'overflow_amount', SUM(overflow_amount_13),
                                    'change_amount', SUM(change_amount_13),
                                    'item_count', SUM(item_count_13),
                                    'item_count_returned', SUM(item_count_returned_13)
                            ),
                            'h14', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_14),
                                    'pay_amount', SUM(pay_amount_14),
                                    'transfer_real_amount', SUM(transfer_real_amount_14),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_14),
                                    'cost', SUM(cost_14),
                                    'tp_allowance', SUM(tp_allowance_14),
                                    'rounding', SUM(rounding_14),
                                    'overflow_amount', SUM(overflow_amount_14),
                                    'change_amount', SUM(change_amount_14),
                                    'item_count', SUM(item_count_14),
                                    'item_count_returned', SUM(item_count_returned_14)
                            ),
                            'h15', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_15),
                                    'pay_amount', SUM(pay_amount_15),
                                    'transfer_real_amount', SUM(transfer_real_amount_15),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_15),
                                    'cost', SUM(cost_15),
                                    'tp_allowance', SUM(tp_allowance_15),
                                    'rounding', SUM(rounding_15),
                                    'overflow_amount', SUM(overflow_amount_15),
                                    'change_amount', SUM(change_amount_15),
                                    'item_count', SUM(item_count_15),
                                    'item_count_returned', SUM(item_count_returned_15)
                            ),
                            'h16', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_16),
                                    'pay_amount', SUM(pay_amount_16),
                                    'transfer_real_amount', SUM(transfer_real_amount_16),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_16),
                                    'cost', SUM(cost_16),
                                    'tp_allowance', SUM(tp_allowance_16),
                                    'rounding', SUM(rounding_16),
                                    'overflow_amount', SUM(overflow_amount_16),
                                    'change_amount', SUM(change_amount_16),
                                    'item_count', SUM(item_count_16),
                                    'item_count_returned', SUM(item_count_returned_16)
                            ),
                            'h17', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_17),
                                    'pay_amount', SUM(pay_amount_17),
                                    'transfer_real_amount', SUM(transfer_real_amount_17),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_17),
                                    'cost', SUM(cost_17),
                                    'tp_allowance', SUM(tp_allowance_17),
                                    'rounding', SUM(rounding_17),
                                    'overflow_amount', SUM(overflow_amount_17),
                                    'change_amount', SUM(change_amount_17),
                                    'item_count', SUM(item_count_17),
                                    'item_count_returned', SUM(item_count_returned_17)
                            ),
                            'h18', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_18),
                                    'pay_amount', SUM(pay_amount_18),
                                    'transfer_real_amount', SUM(transfer_real_amount_18),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_18),
                                    'cost', SUM(cost_18),
                                    'tp_allowance', SUM(tp_allowance_18),
                                    'rounding', SUM(rounding_18),
                                    'overflow_amount', SUM(overflow_amount_18),
                                    'change_amount', SUM(change_amount_18),
                                    'item_count', SUM(item_count_18),
                                    'item_count_returned', SUM(item_count_returned_18)
                            ),
                            'h19', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_19),
                                    'pay_amount', SUM(pay_amount_19),
                                    'transfer_real_amount', SUM(transfer_real_amount_19),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_19),
                                    'cost', SUM(cost_19),
                                    'tp_allowance', SUM(tp_allowance_19),
                                    'rounding', SUM(rounding_19),
                                    'overflow_amount', SUM(overflow_amount_19),
                                    'change_amount', SUM(change_amount_19),
                                    'item_count', SUM(item_count_19),
                                    'item_count_returned', SUM(item_count_returned_19)
                            ),
                            'h20', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_20),
                                    'pay_amount', SUM(pay_amount_20),
                                    'transfer_real_amount', SUM(transfer_real_amount_20),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_20),
                                    'cost', SUM(cost_20),
                                    'tp_allowance', SUM(tp_allowance_20),
                                    'rounding', SUM(rounding_20),
                                    'overflow_amount', SUM(overflow_amount_20),
                                    'change_amount', SUM(change_amount_20),
                                    'item_count', SUM(item_count_20),
                                    'item_count_returned', SUM(item_count_returned_20)
                            ),
                            'h21', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_21),
                                    'pay_amount', SUM(pay_amount_21),
                                    'transfer_real_amount', SUM(transfer_real_amount_21),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_21),
                                    'cost', SUM(cost_21),
                                    'tp_allowance', SUM(tp_allowance_21),
                                    'rounding', SUM(rounding_21),
                                    'overflow_amount', SUM(overflow_amount_21),
                                    'change_amount', SUM(change_amount_21),
                                    'item_count', SUM(item_count_21),
                                    'item_count_returned', SUM(item_count_returned_21)
                            ),
                            'h22', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_22),
                                    'pay_amount', SUM(pay_amount_22),
                                    'transfer_real_amount', SUM(transfer_real_amount_22),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_22),
                                    'cost', SUM(cost_22),
                                    'tp_allowance', SUM(tp_allowance_22),
                                    'rounding', SUM(rounding_22),
                                    'overflow_amount', SUM(overflow_amount_22),
                                    'change_amount', SUM(change_amount_22),
                                    'item_count', SUM(item_count_22),
                                    'item_count_returned', SUM(item_count_returned_22)
                            ),
                            'h23', JSON_BUILD_OBJECT(
                                    'receivable', SUM(receivable_23),
                                    'pay_amount', SUM(pay_amount_23),
                                    'transfer_real_amount', SUM(transfer_real_amount_23),
                                    'payment_transfer_amount', SUM(payment_transfer_amount_23),
                                    'cost', SUM(cost_23),
                                    'tp_allowance', SUM(tp_allowance_23),
                                    'rounding', SUM(rounding_23),
                                    'overflow_amount', SUM(overflow_amount_23),
                                    'change_amount', SUM(change_amount_23),
                                    'item_count', SUM(item_count_23),
                                    'item_count_returned', SUM(item_count_returned_23)
                            )
						) AS "child",
						COUNT(1) AS total --Summary时的汇总条数
					FROM base_payments_for_period
				)
				SELECT
					'' AS bus_date,
					0 AS region_id,
					'' AS store_type,
					0 AS business_days, --营业天数,
				    SUM(receivable) AS receivable, --应付金额
                    SUM(pay_amount) AS pay_amount, --实付金额
                    SUM(transfer_real_amount) AS transfer_real_amount, --财务实收金额(转换后)
                    SUM(payment_transfer_amount) AS payment_transfer_amount, --支付转折扣
                    SUM(cost) AS cost, --用户实际购买金额
                    SUM(tp_allowance) AS tp_allowance, --第三方补贴金额
                    SUM(rounding) AS rounding, --抹零
                    SUM(overflow_amount) AS overflow_amount, --溢收
                    SUM(change_amount) AS change_amount, --找零
                    SUM(item_count) AS item_count, --支付次数
                    SUM(item_count_returned) AS item_count_returned, --支付退单数
					'{}'::json AS data,
					JSON_AGG(child) AS child,
					SUM(total) AS total

				FROM
					base_payments_for_json
 		`
	}
	whereSQL := generateWhereSQLForPaymentPeriodSales(condition)
	regionSQL := generateRegionSQLForPaymentPeriodSales(condition)
	limitSQL := generateLimitOffsetSQLForPaymentPeriodSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG Summary SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForPaymentPeriodSales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateWhereSQLForPaymentPeriodSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		paymentSQL    string // 支付方式筛选条件
		storeTypeSQL  string // 增加门店类型的过滤
		openStatusSQL string // 增加开店类型的过滤
	)

	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.PaymentIds) > 0 {
		paymentSQL = fmt.Sprintf("AND fact.payment_id IN (%s)",
			helpers.JoinInt64(condition.PaymentIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentSQL, storeTypeSQL, openStatusSQL, storeSQL)
	return whereSQL
}

func generateLimitOffsetSQLForPaymentPeriodSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
