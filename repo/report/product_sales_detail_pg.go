package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductSalesDetail(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesDetailResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductSalesDetail(trackId, condition)
	ch1 := make(chan []*report.ProductSalesDetail, 1)
	ch2 := make(chan []*report.ProductSalesDetail, 1)
	go queryDBWithChanForProductSalesDetail(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductSalesDetail(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary *report.ProductSalesDetail
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductSalesDetailResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductSalesDetail(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductSalesDetail) {
	ch <- queryDBForProductSalesDetail(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForProductSalesDetail(trackId int64, db *sql.DB, sqlStr string) []*report.ProductSalesDetail {
	results := make([]*report.ProductSalesDetail, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductSalesDetail)
		if err := r.Scan(
			&f.BusDate,
			&f.RegionId,
			&f.BranchRegionId,
			&f.StoreType,
			&f.CategoryId,
			&f.ProductId,
			&f.ChannelId,
			&f.DiningMethodCode,
			&f.Price,
			&f.ProductCount,
			&f.BowlCount,
			&f.GrossAmount,
			&f.DiscountAmount,
			&f.NetAmount,
			&f.ProductWeight,
			&f.Unit,
			&f.MealSegmentName,
			&f.ZoneName,
			&f.TableNo,
			&f.OrderUser,
			&f.OpenTime,
			&f.OrderTime,
			&f.CashierName,
			&f.EndTime,
			&f.OrderType,
			&f.GroupPurchaseChannel,
			&f.TicketNo,
			&f.Remark,
			&f.SpicyCode,
			&f.AccCount,
			&f.ProductAccCount,
			&f.ProductionDepartmentId,
			&f.ProductionDepartmentName,
			&f.IncomeSubjectId,
			&f.IncomeSubjectName,
			&f.Flags,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductSalesDetail(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL   string
		totalSQL string
	)
	rowSQL = `
--菜品销售明细表 ROWS
SELECT
    coalesce(to_char(fact.bus_date, 'YYYY-MM-DD'), '') as bus_date,
    coalesce(fact.store_id, 0),
    COALESCE(branchs[array_length(branchs, 1)], 0) AS branch_region_id,
    coalesce(store_caches.store_type, '0') as store_type,
    COALESCE(product_caches.category, 0) AS category,
    coalesce(fact.product_id, 0) as product_id,
    coalesce(fact.channel_id, 0) as channel_id,
    coalesce(fact.order_type, '0') as order_type,
    CASE
           WHEN fact.qty = 0 THEN 0
           ELSE (COALESCE(fact.gross_amount2, 0) + COALESCE(fact.package_amount, 0) +
                 COALESCE(fact.send_fee_for_merchant, 0) +
                 COALESCE(fact.service_fee, 0) + COALESCE(fact.tip, 0)) / fact.qty END AS price,
    coalesce(fact.qty, 0) as qty,
    coalesce(fact.bowl_num, 0) as bowl_num,
    coalesce(fact.gross_amount2, 0) + coalesce(fact.package_amount, 0) + coalesce(fact.send_fee_for_merchant, 0) + coalesce(fact.service_fee, 0) + coalesce(fact.tip, 0) as gross_amount,
    coalesce(fact.pay_merchant_allowance, 0) + coalesce(fact.promotion_merchant_allowance, 0) + coalesce(fact.other_fee, 0) + coalesce(fact.merchant_send_fee, 0) + coalesce(fact.commission, 0) + coalesce(fact.rounding, 0) - coalesce(fact.overflow_amount, 0) as discount_amount,
    (coalesce(fact.gross_amount2, 0) + coalesce(fact.package_amount, 0) + coalesce(fact.send_fee_for_merchant, 0) + coalesce(fact.service_fee, 0) + coalesce(fact.tip, 0)) - (coalesce(fact.pay_merchant_allowance, 0) + coalesce(fact.promotion_merchant_allowance, 0) + coalesce(fact.other_fee, 0) + coalesce(fact.merchant_send_fee, 0) + coalesce(fact.commission, 0) + coalesce(fact.rounding, 0) - coalesce(fact.overflow_amount, 0)) as net_amount,
    coalesce(fact.weight, 0) as weight,
    coalesce(fact.unit, '') as unit,
    coalesce(fact.meal_segment_name, '') as meal_segment_name,
    coalesce(fact.zone_name, '') as zone_name,
    coalesce(fact.table_no, '') as table_no,
    coalesce(fact.open_table_by, '') as open_table_by,
	case when fact.open_table_at is null or fact.open_table_at < '1970-01-01' then '0001-01-01 00:00:00' else to_char(fact.open_table_at, 'YYYY-MM-DD HH24:MI:SS') end as open_table_at,
	case when fact.order_time is null or fact.order_time < '1970-01-01' then '0001-01-01 00:00:00' else to_char(fact.order_time, 'YYYY-MM-DD HH24:MI:SS') end as order_time,
    coalesce(fact.operator_name, '') as operator_name,
	case when fact.end_time is null or fact.end_time < '1970-01-01' then '0001-01-01 00:00:00' else to_char(fact.end_time, 'YYYY-MM-DD HH24:MI:SS') end as end_time,
    coalesce(fact.order_status, '') as order_status,
    coalesce(fact.coupon_channel_name, '') as coupon_channel_name,
    coalesce(fact.ticket_no, '') as ticket_no,
    coalesce(fact.remark, '') as remark,
	coalesce(r.value_code, '') as spicy_code,
	coalesce(round(abs(fact.acc_count))::integer, 0) as acc_count,
	coalesce(round(abs(fact.product_acc_count))::integer, 0) as product_acc_count,
 	coalesce(fact.production_department_id, 0)                        as production_department_id,
	coalesce(fact.production_department_name, '')                     as production_department_name,
	coalesce(fact.income_subject_id, 0)                               as income_subject_id,
	coalesce(fact.income_subject_name, '')                            as income_subject_name,
	coalesce(fact.flags, '')                            as flags,
	0 AS total
FROM
    sales_product_amounts fact
        LEFT JOIN store_caches store_caches
                  ON fact.store_id = store_caches.id
        LEFT JOIN product_caches product_caches
                  ON fact.product_id = product_caches.id
		left join sales_product_sku_remark r on fact.id = r.pid and r.code = '{SPICY_CODE}'
WHERE
	{WHERE}
order by fact.bus_date desc, fact.order_time desc, fact.product_id, fact.id
{LIMIT}
`
	totalSQL = `
--菜品销售明细表 Summary
SELECT ''                                                                    AS bus_date,
       0                                                                     AS store_id,
       0                                                                     AS branch_region_id,
       '0'                                                                   AS store_type,
       0                                                                     AS category,
       0                                                                     AS product_id,
       0                                                                     AS channel_id,
       '0'                                                                   AS order_type,
       0                                                                     AS price,
       COALESCE(SUM(fact.qty), 0)                                            AS qty,
       SUM(COALESCE(fact.bowl_num, 0))                                       AS bowl_num,
       SUM(COALESCE(fact.gross_amount2, 0) + COALESCE(fact.package_amount, 0) +
           COALESCE(fact.send_fee_for_merchant, 0) +
           COALESCE(fact.service_fee, 0) + COALESCE(fact.tip, 0))            AS gross_amount,
       SUM(COALESCE(fact.pay_merchant_allowance, 0) + COALESCE(fact.promotion_merchant_allowance, 0) +
           COALESCE(fact.other_fee, 0) + COALESCE(fact.merchant_send_fee, 0) + COALESCE(fact.commission, 0) +
           COALESCE(fact.rounding, 0) - COALESCE(fact.overflow_amount, 0))   AS discount_amount,
       SUM((COALESCE(fact.gross_amount2, 0) + COALESCE(fact.package_amount, 0) +
            COALESCE(fact.send_fee_for_merchant, 0) +
            COALESCE(fact.service_fee, 0) + COALESCE(fact.tip, 0)) -
           (COALESCE(fact.pay_merchant_allowance, 0) + COALESCE(fact.promotion_merchant_allowance, 0) +
            COALESCE(fact.other_fee, 0) + COALESCE(fact.merchant_send_fee, 0) + COALESCE(fact.commission, 0) +
            COALESCE(fact.rounding, 0) - COALESCE(fact.overflow_amount, 0))) AS net_amount,
       0                                                                     AS weight,
       ''                                                                    AS unit,
       ''                                                                    AS meal_segment_name,
       ''                                                                    AS zone_name,
       ''                                                                    AS table_no,
       ''                                                                    AS open_table_by,
       ''                                                                    AS open_table_at,
       ''                                                                    AS order_time,
       ''                                                                    AS operator_name,
       ''                                                                    AS end_time,
       ''                                                                    AS order_status,
       ''                                                                    AS coupon_channel_name,
       ''                                                                    AS ticket_no,
       ''                                                                    AS remark,
       ''                                                                    AS spicy_code,
       SUM(COALESCE(ROUND(ABS(fact.acc_count))::integer, 0))                 AS acc_count,
       SUM(COALESCE(ROUND(ABS(fact.product_acc_count))::integer, 0))         AS product_acc_count,
       0                                                                     AS production_department_id,
       ''                                                                    AS production_department_name,
       0                                                                     AS income_subject_id,
       ''                                                                    AS income_subject_name,
       ''                                                                    AS flags,
       COUNT(1)                                                              AS total
FROM sales_product_amounts fact
         LEFT JOIN store_caches store_caches
                   ON fact.store_id = store_caches.id
         LEFT JOIN product_caches product_caches ON fact.product_id = product_caches.id
WHERE 
	{WHERE}
`

	whereSQL := generateWhereSQLForProductSalesDetail(condition)
	limitSQL := generateLimitOffsetSQLForProductSalesDetail(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{SPICY_CODE}", config.SpicyCode)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateWhereSQLForProductSalesDetail(condition *model.RepoCondition) string {
	var (
		regionIdsSQL          string // 区域筛选条件
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
		channelIdsSQL         string // 渠道筛选条件
		comboSQL              string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	//if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
	comboSQL = fmt.Sprintf(`AND (fact.combo_type != 1)`)
	//} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
	//	comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	//}
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			"AND fact.product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}
	if len(condition.ProductCategoryIds) > 0 {
		productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
			helpers.JoinInt64(condition.ProductCategoryIds, ","),
		)
	}
	if len(condition.ChannelIds) > 0 {
		channelIdsSQL = fmt.Sprintf(`
			AND fact.channel_id IN (%s)
		`, helpers.JoinInt64(condition.ChannelIds, ","))
	}
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	netAmountSQL := ""
	if !condition.IncludeRealAmountZero {
		netAmountSQL = fmt.Sprintf(`
			AND fact.products_real_amount != 0
		`)
	}
	ticketNoSQL := ""
	if condition.TicketNo != "" {
		ticketNoSQL = fmt.Sprintf(`
			AND fact.ticket_no = '%s'
		`, condition.TicketNo)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, productIdsSQL,
		productCategoryIdsSQL, storeSQL, comboSQL, regionCodesSQL, netAmountSQL, channelIdsSQL, ticketNoSQL)
	return whereSQL
}

func generateLimitOffsetSQLForProductSalesDetail(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
