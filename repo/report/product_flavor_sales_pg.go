package repo

import (
	"context"
	"database/sql"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
)

func (rsmm *SalesRepositoryPG) ProductFlavorSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductFlavorSalesResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, totalSQL := generateQuerySQLForProductFlavorSales(trackId, condition)
	ch1 := make(chan []*report.ProductFlavorSales, 1)
	ch2 := make(chan []interface{}, 1)
	start := time.Now()
	go queryDBWithChanForProductFlavorSales(trackId, rsmm.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, totalSQL, ch2)
	rows := <-ch1
	totals := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var total int64
	if len(totals) > 0 {
		totalMap := cast.ToStringMap(totals[0])
		total = cast.ToInt64(totalMap["total"])
	}
	return &report.ProductFlavorSalesResponse{
		Rows:  rows,
		Total: total,
	}, nil
}

// 商品属性销售
func queryDBWithChanForProductFlavorSales(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductFlavorSales) {
	ch <- queryDBForProductFlavorSales(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为struct对象
func queryDBForProductFlavorSales(trackId int64, db *sql.DB, sqlStr string) []*report.ProductFlavorSales {
	results := make([]*report.ProductFlavorSales, 0)
	if sqlStr == "" {
		return []*report.ProductFlavorSales{new(report.ProductFlavorSales)}
	}
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductFlavorSales)
		if err := r.Scan(
			&f.BusDate,
			&f.GeoId,
			&f.BranchId,
			&f.CompanyId,
			&f.RegionId,
			&f.StoreType,
			&f.ProductId,
			&f.ProductCategoryId1,
			&f.ProductCategoryId2,
			&f.ProductCategoryId3,
			&f.ProductCategoryId4,
			&f.Flavor,
			&f.SkuRemark,
			&f.Accessories,
			&f.ProductCount,
			&f.ProductCountTotal,
			&f.PercentProductCount,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductFlavorSales(trackId int64, condition *model.RepoCondition) (string, string) {
	var rowSQL string
	var totalSQL string
	switch condition.TagTypes {
	case "TASTE": // 口味分布
		rowSQL = `
		--商品属性.口味分布 ROWS
			WITH base_products AS (
				SELECT
				    to_char(fact.bus_date, 'YYYY-MM-DD') as bus_date,
				    COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
                    COALESCE({REGION_ID}, 0) as region_id,
                    COALESCE(max(store_caches.store_type), '') AS store_type,
                    COALESCE(fact.product_id, 0) as product_id,
                    COALESCE(fact.flavor, '') as flavor,
                    COALESCE(sum(qty), 0) as product_count
			
				FROM
				  sales_product_amounts fact
					LEFT JOIN store_caches
					  ON fact.store_id = store_caches.id
					LEFT JOIN product_caches
					  ON fact.product_id = product_caches.id
				WHERE
				    {WHERE}
				GROUP BY
					fact.bus_date, company_info, {REGION_ID}, fact.product_id,fact.flavor
				ORDER BY
					fact.bus_date DESC, {REGION_ID}, fact.product_id
				{LIMIT}
			 ), base_products_by_window as (
			     SELECT
					bus_date,
			        geo_id,
			        branch_id,
			        company_id,
					region_id,
					store_type,
				    product_id,
				    flavor,
				    product_count,
					SUM(product_count) OVER(partition by bus_date,region_id,store_type,product_id order by product_count desc) AS product_count_total
				FROM
					base_products
			),base_products_for_date_store_product as (
			    SELECT
			        bus_date,
			        geo_id,
			        branch_id,
			        company_id,
			        region_id,
			        max(store_type) as store_type,
			        product_id,
			        max(product_count_total) as product_count_total
			    FROM
			        base_products_by_window
			    GROUP BY
			        bus_date, geo_id, branch_id, company_id, region_id, product_id
            )
			SELECT
			    bw.bus_date,
			    bw.geo_id,
			    bw.branch_id,
			    bw.company_id,
			    bw.region_id,
			    bw.store_type,
			    bw.product_id,
				COALESCE(product_caches.categories[1],0) AS product_category_id1,
				COALESCE(product_caches.categories[2],0) AS product_category_id2,
				COALESCE(product_caches.categories[3],0) AS product_category_id3,
				COALESCE(product_caches.categories[4],0) AS product_category_id4,
			    bw.flavor,
				'' as sku_remark,
				'' as accessories,
			    bw.product_count,
			    bdsp.product_count_total AS product_count_total,
			    case when bdsp.product_count_total<=0 or bw.product_count<0 then 0 else bw.product_count/bdsp.product_count_total end AS percent_product_count
			FROM
			    base_products_by_window bw
			     inner join base_products_for_date_store_product bdsp
			        on bw.bus_date = bdsp.bus_date and bw.region_id = bdsp.region_id and bw.product_id = bdsp.product_id
				LEFT JOIN product_caches
					ON bw.product_id = product_caches.id
            ORDER BY bus_date desc,region_id,product_id,percent_product_count desc;
	`
		totalSQL = `
           select
				count(*) as total
		   from
				(SELECT
						to_char(fact.bus_date, 'YYYY-MM-DD') as bus_date,
						COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
						COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
						COALESCE(store_caches.company_info,0) AS company_id,
						COALESCE({REGION_ID}, 0) as region_id,
						COALESCE(max(store_caches.store_type), '') AS store_type,
						COALESCE(fact.product_id, 0) as product_id,
						COALESCE(fact.flavor, '') as flavor,
						COALESCE(sum(qty), 0) as product_count
					FROM
					  sales_product_amounts fact
						LEFT JOIN store_caches
						  ON fact.store_id = store_caches.id
						LEFT JOIN product_caches
						  ON fact.product_id = product_caches.id
					WHERE
						{WHERE}
					GROUP BY
						fact.bus_date, company_info, {REGION_ID}, fact.product_id,fact.flavor)T
			`
	case "FEED": // 加料
		rowSQL = `
			--商品属性.加料 ROWS
				select
					to_char(fact.bus_date, 'YYYY-MM-DD') as bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID}, 0) as region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) as product_id,
					COALESCE(product_caches.categories[1],0) AS product_category_id1,
					COALESCE(product_caches.categories[2],0) AS product_category_id2,
					COALESCE(product_caches.categories[3],0) AS product_category_id3,
					COALESCE(product_caches.categories[4],0) AS product_category_id4,
					'' as flavor,
					'' as sku_remark,
					COALESCE(fact.accessories, '') as accessories,
					COALESCE(sum(qty), 0) as product_count,
					0 as product_count_total,
					1 as percent_product_count
				from
					sales_product_amounts fact
						left join store_caches on fact.store_id = store_caches.id
						left join product_caches on fact.product_id = product_caches.id
				where
					{WHERE}
				group by fact.bus_date, company_info, {REGION_ID}, product_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4, accessories,eticket_id
				order by fact.bus_date desc, {REGION_ID};
        `
	case "ATTRIBUTE": // 属性
		rowSQL = `
			--商品属性.sku属性 ROWS
				select
					to_char(fact.bus_date, 'YYYY-MM-DD') as bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID}, 0) as region_id,
					COALESCE(max(store_caches.store_type), '') AS store_type,
					COALESCE(fact.product_id, 0) as product_id,
					COALESCE(product_caches.categories[1],0) AS product_category_id1,
					COALESCE(product_caches.categories[2],0) AS product_category_id2,
					COALESCE(product_caches.categories[3],0) AS product_category_id3,
					COALESCE(product_caches.categories[4],0) AS product_category_id4,
					'' as flavor,
					COALESCE(fact.sku_remark, '') as sku_remark,
					'' as accessories,
					COALESCE(sum(qty), 0) as product_count,
					0 as product_count_total,
					1 as percent_product_count
				from
					sales_product_amounts fact
						left join store_caches on fact.store_id = store_caches.id
						left join product_caches on fact.product_id = product_caches.id
				where
					{WHERE}
				group by fact.bus_date, company_info, {REGION_ID}, product_id,product_category_id1,product_category_id2,product_category_id3,product_category_id4, sku_remark
				order by fact.bus_date desc, {REGION_ID};
		`
	case "GRAPH":
		rowSQL = `
			--商品属性柱状图
				select
					to_char(fact.bus_date, 'YYYY-MM-DD') as bus_date,
					COALESCE(max(store_caches.geos[array_length(store_caches.geos,1)]),0) AS geo_id,
					COALESCE(max(store_caches.branchs[array_length(store_caches.branchs,1)]),0) AS branch_id,
					COALESCE(store_caches.company_info,0) AS company_id,
					COALESCE({REGION_ID}, 0) as region_id,
					COALESCE(max(store_caches.store_type), '') as store_type,
					COALESCE(fact.product_id, 0) as product_id,
					'0' AS product_category_id1,
					'0' AS product_category_id2,
					'0' AS product_category_id3,
					'0' AS product_category_id4,
					'' as flavor,
					COALESCE(fact.sku_remark, '') as sku_remark,
					COALESCE(accessories, '') as accessories,
					COALESCE(sum(qty), 0) as product_count,
					0 as product_count_total,
					1 as percent_product_count
				from sales_product_amounts fact
					left join store_caches on fact.store_id=store_caches.id
					left join product_caches on fact.product_id=product_caches.id
				where
					{WHERE}
				group by bus_date, company_info, {REGION_ID}, product_id, sku_remark, accessories, eticket_id
		`
	}
	whereSQL := generateWhereSQLForProductFlavorSales(condition)
	regionSQL := generateRegionSQLForProductFlavorSales(condition)
	limitSQL := generateLimitOffsetSQLForProductFlavorSales(condition)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	totalSQL = strings.ReplaceAll(totalSQL, "{WHERE}", whereSQL)
	totalSQL = strings.ReplaceAll(totalSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG total. SQL: `%s`", trackId, totalSQL)
	return rowSQL, totalSQL
}

func generateWhereSQLForProductFlavorSales(condition *model.RepoCondition) string {
	var (
		regionIdsSQL  string // 区域筛选条件
		productIdsSQL string // 商品筛选条件
		storeTypeSQL  string // 门店类型的过滤
		openStatusSQL string // 开店类型的过滤
		comboSQL      string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	if condition.IsCombo { // 统计套餐时，仅统计非套餐商品和套餐头
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 1)`)
	} else { // 不统计套餐时，仅统计非套餐商品和套餐子项
		comboSQL = fmt.Sprintf(`AND (fact.combo_type = 0 OR fact.combo_type = 2)`)
	}
	regionIdsSQL = GenerateRegionWhereSQL(condition)

	if len(condition.ProductIds) > 0 {
		if len(condition.ProductIds) == 1 {
			productIdsSQL = fmt.Sprintf(
				"AND fact.product_id = %d",
				condition.ProductIds[0],
			)
		} else {
			productIdsSQL = fmt.Sprintf(
				"AND fact.product_id IN (%s)",
				helpers.JoinInt64(condition.ProductIds, ","),
			)
		}
	}

	// 门店类型支持多选
	if len(condition.StoreTypes) > 0 {
		if len(condition.StoreTypes) == 1 {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type = '%s'",
				condition.StoreTypes[0],
			)
		} else {
			storeTypeSQL = fmt.Sprintf(
				"AND store_caches.store_type in (%s)",
				helpers.JoinString(condition.StoreTypes, ","),
			)
		}
	}
	if condition.OpenStatus != "" {
		openStatusSQL = fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}

	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
			AND store_caches.id = %d
		`, condition.StoreIDs[0])
		} else {
			storeSQL = fmt.Sprintf(`
			AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}

	busDateSQL := ""
	if condition.Start.Format("2006-01-02") == condition.End.Format("2006-01-02") {
		busDateSQL = fmt.Sprintf(`
				AND fact.bus_date = '%s'
		`, condition.Start.Format("2006-01-02"))
	} else {
		busDateSQL = fmt.Sprintf(`
				AND fact.bus_date >= '%s'
				AND fact.bus_date <= '%s'
		`, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"))
	}

	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.is_accessory = false
		%s
		%s
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, busDateSQL, regionIdsSQL, productIdsSQL, storeTypeSQL,
		openStatusSQL, storeSQL, comboSQL)
	return whereSQL
}

func generateRegionSQLForProductFlavorSales(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateLimitOffsetSQLForProductFlavorSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
