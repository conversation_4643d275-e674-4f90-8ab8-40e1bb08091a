package repo

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	"strings"
	//protobuf_struct "github.com/golang/protobuf/ptypes/struct"
)

const posinboundRowTpl = `
-- Pos入库报表 rows
select
	coalesce(to_char(fact.inbound_time at time zone 'cct', 'YYYY-MM-DD HH24:MI'), '') as inbound_time,
	coalesce(fact.product_names, '') as product_names,
	coalesce(fact.product_qty, 0) as qty,
	coalesce(fact.operator_name, '') as operator_name,
	coalesce(fact.content::varchar, '{}') as content
from sales_inbound_content fact
where deleted = 0 and {WHERE}
order by fact.inbound_time desc, fact.created desc, fact.id
{LIMIT}
`

const posinboundTotalTpl = `
-- 入库报表 total
select
	count(1) as total
from sales_inbound_content fact
where deleted = 0 and {WHERE}
`

func (rsmm *InboundRepositoryPG) PosInbound(ctx context.Context, condition *model.RepoCondition) (*report.PosInboundResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	rowSql, totalSql := posinboundSql(ctx, condition)

	rowCh := make(chan []*report.PosInboundRow, 0)
	totalCh := make(chan int64, 0)

	go posinboundExecuteRow(trackId, rowSql, rsmm.DB, rowCh)
	go posinboundExecuteTotal(trackId, totalSql, rsmm.DB, totalCh)

	resp := &report.PosInboundResponse{
		Rows:  <-rowCh,
		Total: <-totalCh,
	}
	close(rowCh)
	close(totalCh)
	return resp, nil
}

func posinboundExecuteRow(trackId int64, sql string, db *sql.DB, ch chan []*report.PosInboundRow) {
	results := make([]*report.PosInboundRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- results
		return
	}
	defer r.Close()
	var contentStr string
	for r.Next() {
		f := new(report.PosInboundRow)
		if err := r.Scan(
			&f.InboundTime,
			&f.ProductNames,
			&f.ProductQty,
			&f.OperatorName,
			&contentStr,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- results
			return
		}
		content := map[string]interface{}{}
		err := json.Unmarshal([]byte(contentStr),&content)
		if err == nil {
			f.Content,err = utils.Map2Struct(content)
			if err != nil {
				logger.Pre().Errorln("Unmarshal Err: ", err)
			}
		}else{
			logger.Pre().Errorln("Unmarshal Err: ", err)
		}
		results = append(results, f)
	}
	ch <- results
	return
}

func posinboundExecuteTotal(trackId int64, sql string, db *sql.DB, ch chan int64) {
	var res int64 = 0
	r := db.QueryRow(sql)
	err := r.Scan(&res)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- res
		return
	}
	ch <- res
}

func posinboundSql(ctx context.Context, condition *model.RepoCondition) (rowSql string, totalSql string) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))

	whereSQL := posinboundWhere(condition)
	limitSQL := posinboundLimit(condition)
	rowSql = strings.ReplaceAll(posinboundRowTpl, "{WHERE}", whereSQL)
	rowSql = strings.ReplaceAll(rowSql, "{LIMIT}", limitSQL)

	totalSql = strings.ReplaceAll(posinboundTotalTpl, "{WHERE}", whereSQL)

	logger.Pre().Debugf("[%d] repo.report.PosInboundRepositoryPG row. SQL: `%s`", trackId, rowSql)
	logger.Pre().Debugf("[%d] repo.report.PosInboundRepositoryPG total. SQL: `%s`", trackId, totalSql)
	return
}

func posinboundWhere(condition *model.RepoCondition) string {
	var store_id int64
	if len(condition.StoreIDs) > 0 {
		store_id = condition.StoreIDs[0]
	}
	storeSQL := fmt.Sprintf(`
				AND fact.store_id = '%d'	
			`, store_id)

	var productSQL string
	if condition.Search != "" {
		productSQL = fmt.Sprintf(`
			AND fact.product_names like '%%%s%%'
		`, condition.Search)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.inbound_time >= ('%sT16:00:00.000000Z'::timestamptz)
		AND fact.inbound_time <= ('%sT15:59:59.999999Z'::timestamptz)
		%s
		%s
	`, condition.PartnerId, condition.Start.AddDate(0, 0, -1).Format("2006-01-02"),
		condition.End.Format("2006-01-02"), storeSQL, productSQL)
	return whereSQL
}

func posinboundLimit(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
