package repo

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) StorePeriodSales(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL, totalSQL, businessDaySQL := generateQuerySQLForStorePeriodSales(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	ch3 := make(chan []interface{}, 1)
	ch4 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, summarySQL, ch2)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, totalSQL, ch3)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, businessDaySQL, ch4)
	rows := <-ch1
	summarys := <-ch2
	totals := <-ch3
	days := <-ch4
	close(ch1)
	close(ch2)
	close(ch3)
	close(ch4)

	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	var total int64
	if len(totals) > 0 {
		total = cast.ToInt64(cast.ToStringMap(totals[0])["total"])
	}

	var businessDaysMap = make(map[string]int64)
	for _, bd := range days {
		bdMap := cast.ToStringMap(bd)
		businessDaysMap[cast.ToString(bdMap["store_id"])] = cast.ToInt64(bdMap["business_days"])
	}
	for _, r := range rows {
		rowMap := cast.ToStringMap(r)
		rowMap["business_days"] = businessDaysMap[cast.ToString(rowMap["region_id"])]
	}

	return &model.Response{Rows: rows, Summary: summary, Total: total}, nil
}

func generateQuerySQLForStorePeriodSales(trackId int64, condition *model.RepoCondition) (string, string, string, string) {
	innerSelectSQL, innerSelectSQLOfSummary := generateInnerSelectSQLForStorePeriodSales(condition)
	outerSelectSQL, outerSelectSQLOfSummary := generateOuterSelectSQLForStorePeriodSales(condition)
	whereSQL := generateWhereSQLForStorePeriodSales(condition)
	fromSQL := generateFromSQLForStorePeriodSales(condition)
	innerGroupSQL, innerGroupSQLOfSummary := generateInnerGroupSQLForStorePeriodSales(condition)
	outerGroupSQL, _ := generateOuterGroupSQLForStorePeriodSales(condition)
	outerOrderSQL := generateOuterOrderSQLForStorePeriodSales(condition)
	limitOffsetSQL := generateLimitOffsetSQLForStorePeriodSales(condition)

	rowSQL := fmt.Sprintf(`
	--门店时段 ROWS
		SELECT
			%s
		FROM(
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
			) T
		GROUP BY
			%s
		ORDER BY
			%s
			%s
	`, outerSelectSQL, innerSelectSQL, fromSQL, whereSQL, innerGroupSQL,
		outerGroupSQL, outerOrderSQL, limitOffsetSQL)

	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG Row SQL: `%s`", trackId, rowSQL)

	summarySQL := fmt.Sprintf(`
	--门店时段 SUMMARY
		SELECT
			%s
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, outerSelectSQLOfSummary, innerSelectSQLOfSummary, fromSQL, whereSQL,
		innerGroupSQLOfSummary)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG Summary SQL: `%s`", trackId, summarySQL)

	totalSQL := fmt.Sprintf(`
		SELECT 
			count(*) AS total
		FROM
			(SELECT
				count(*)
			FROM(
				SELECT
					%s
				FROM
					%s
				WHERE
					%s
				GROUP BY
					%s
				) T
			GROUP BY
				%s) OutT
	`, innerSelectSQL, fromSQL, whereSQL, innerGroupSQL,
		outerGroupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG Total SQL: `%s`", trackId, totalSQL)
	businessDaySQL := generateBusinessDaySql(condition)
	return rowSQL, summarySQL, totalSQL, businessDaySQL
}

func generateDateSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	var dateSql = ""
	var datePartSql = ""
	if condition.TagType == "DETAILED" {
		dateSql = "bus_date AS bus_date,"
		datePartSql = "bus_date,"
	}
	dateSql += "DATE_PART('hour', order_time AT TIME ZONE store_caches.time_zone) AS hours"
	datePartSql += "DATE_PART('hour', order_time AT TIME ZONE store_caches.time_zone)"
	return datePartSql, dateSql
}

func generateRegionSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateInnerSelectSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	_, regionSQL := generateRegionSQLForStorePeriodSales(condition)
	_, dateSQL := generateDateSQLForStorePeriodSales(condition)
	//var selectSql = ""
	//if condition.TagType == "DETAILED" {
	//	selectSql = ""
	//}
	selectSQL := fmt.Sprintf(`
		%s,
		%s,
		max(store_type) as store_type,
		SUM(amount_0) AS business_amount,
		SUM(amount_1+rounding) AS expend_amount,
		SUM(amount_2-rounding) AS real_amount,
		SUM(eticket_count) AS order_count,
		SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,
		SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,
		SUM(tax_fee) as tax_fee,
        SUM(bowl_num)                         as bowl_num --碗数
	`, regionSQL, dateSQL)
	// exclude region
	selectSQLOfSummary := fmt.Sprintf(`
		DATE_PART('hour', order_time AT TIME ZONE store_caches.time_zone) AS hours,
		SUM(amount_0) AS business_amount,
		SUM(amount_1+rounding) AS expend_amount,
		SUM(amount_2-rounding) AS real_amount,
		SUM(eticket_count) AS order_count,
		SUM(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount) AS discount_contribute,
		SUM(amount_0-(discount_merchant_contribute+merchant_send_fee+other_fee+commission+pay_merchant_contribute+rounding-overflow_amount)) AS transfer_real_amount,
        SUM(tax_fee) as tax_fee,
        SUM(bowl_num)                 as bowl_num --碗数
	`)
	return selectSQL, selectSQLOfSummary
}

func generateOuterSelectSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	// 根据详细和汇总组装查询条件
	var selectSql = ""
	if condition.TagType == "DETAILED" {
		selectSql = "TO_CHAR(bus_date, 'YYYY-MM-DD') AS period_group,"
	}
	selectSql += `
		region_id,
		max(store_type) as store_type,
		JSON_BUILD_OBJECT(
			'hours_list', JSON_AGG(hours),
			'business_amount_list', JSON_AGG(business_amount),
			'expend_amount_list', JSON_AGG(expend_amount),
			'real_amount_list', JSON_AGG(real_amount),
			'order_count_list', JSON_AGG(order_count),
			'transfer_real_amount_list', JSON_AGG(transfer_real_amount),
			'discount_contribute_list', JSON_AGG(discount_contribute),
			'tax_fee_list', JSON_AGG(tax_fee),
		    'bowl_num_list', JSON_AGG(bowl_num)
		) AS "data"
	`
	// exclude region
	selectSQLOfSummary := `
		JSON_BUILD_OBJECT(
			'hours_list', JSON_AGG(hours),
			'business_amount_list', JSON_AGG(business_amount),
			'expend_amount_list', JSON_AGG(expend_amount),
			'real_amount_list', JSON_AGG(real_amount),
			'order_count_list', JSON_AGG(order_count),
			'transfer_real_amount_list', JSON_AGG(transfer_real_amount),
			'discount_contribute_list', JSON_AGG(discount_contribute),
			'tax_fee_list', JSON_AGG(tax_fee),
		    'bowl_num_list', JSON_AGG(bowl_num)
		) AS "data"
	`
	return selectSql, selectSQLOfSummary
}

func generateWhereSQLForStorePeriodSales(condition *model.RepoCondition) string {
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	periodSQL := ""
	if len(condition.Period) != 0 {
		periodSQL = fmt.Sprintf(`
			 AND extract(hour from sales_ticket_amounts.order_time AT TIME ZONE store_caches.time_zone) IN (%s)
		`, helpers.JoinInt64(condition.Period, ","))
	}

	// 增加门店类型和开店类型的过滤
	storeAndOpenSQL := generateStoreOpenWhereSql(condition)
	regionId, _ := generateRegionSQLForStorePeriodSales(condition)
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d 
		AND sales_ticket_amounts.scope_id = %d
		AND sales_ticket_amounts.bus_date >= '%s'
		AND sales_ticket_amounts.bus_date <= '%s'
		%s
		%s
		%s
		%s
		AND %s > 0
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, periodSQL, storeAndOpenSQL, storeSQL, regionId)
	return whereSQL
}

func generateFromSQLForStorePeriodSales(condition *model.RepoCondition) string {
	var leftJoinSQL = `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
	`
	return leftJoinSQL
}

func generateInnerGroupSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	dateSQL, _ := generateDateSQLForStorePeriodSales(condition)
	regionSQL, _ := generateRegionSQLForStorePeriodSales(condition)
	groupSQL := fmt.Sprintf(`
		%s,
		%s,
		store_type
	`, regionSQL, dateSQL)
	groupSQLOfSummary := fmt.Sprintf(`
		DATE_PART('hour', order_time AT TIME ZONE store_caches.time_zone)
	`)
	return groupSQL, groupSQLOfSummary
}

func generateOuterGroupSQLForStorePeriodSales(condition *model.RepoCondition) (string, string) {
	regionSQL, _ := generateRegionSQLForStorePeriodSales(condition)
	var groupSQL = ""
	if condition.TagType == "DETAILED" {
		groupSQL = "bus_date,"
	}
	groupSQL += "region_id"
	groupSQLOfTotal := fmt.Sprintf(
		"%s",
		regionSQL,
	)
	return groupSQL, groupSQLOfTotal
}

func generateOuterOrderSQLForStorePeriodSales(condition *model.RepoCondition) string {
	var outerOrderSql = ""
	if condition.TagType == "DETAILED" {
		outerOrderSql = "bus_date DESC,"
	}
	outerOrderSql += "region_id"
	return outerOrderSql
}

func generateLimitOffsetSQLForStorePeriodSales(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
