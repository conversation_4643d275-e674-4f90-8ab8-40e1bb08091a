package repo

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) ProductSalesSummary(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesSummaryResponse, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForProductSalesSummary(trackId, condition)
	ch1 := make(chan []*report.ProductSalesSummary, 1)
	ch2 := make(chan []*report.ProductSalesSummary, 1)
	start := time.Now()
	go queryDBWithChanForProductSalesSummary(trackId, rsmm.DB, rowSQL, ch1)
	go queryDBWithChanForProductSalesSummary(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	logger.Pre().Infof("查询结束，耗费时间%v\n", time.Since(start).Seconds())
	var summary *report.ProductSalesSummary
	if len(summarys) > 0 {
		summary = summarys[0]
	}
	response := &report.ProductSalesSummaryResponse{
		Summary: summary,
		Rows:    rows,
	}
	if summary != nil {
		response.Total = summary.Total
	}
	return response, nil
}

func queryDBWithChanForProductSalesSummary(trackId int64, db *sql.DB, sqlStr string, ch chan []*report.ProductSalesSummary) {
	ch <- queryDBForProductSalesSummary(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func queryDBForProductSalesSummary(trackId int64, db *sql.DB, sqlStr string) []*report.ProductSalesSummary {
	results := make([]*report.ProductSalesSummary, 0)
	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()

	for r.Next() {
		f := new(report.ProductSalesSummary)
		if err := r.Scan(
			&f.ProductId,
			&f.CategoryId,
			&f.NetAmount,
			&f.ProductCount,
			&f.PercentageOfSales,
			&f.Total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}

		results = append(results, f)
	}
	return results
}

func generateQuerySQLForProductSalesSummary(trackId int64, condition *model.RepoCondition) (string, string) {
	var (
		rowSQL     string
		summarySQL string
	)
	rowSQL = `
		--菜品销售汇总表 ROWS
		WITH base_product AS (SELECT fact.product_id AS product_id,
								product_caches.category AS category_id,
										   SUM(
												   (COALESCE(fact.gross_amount2, 0) +
													COALESCE(fact.package_amount, 0) +
													COALESCE(fact.send_fee_for_merchant, 0) +
													COALESCE(fact.service_fee, 0) +
													COALESCE(fact.tip, 0)) -
												   (COALESCE(fact.pay_merchant_allowance, 0) +
													COALESCE(fact.promotion_merchant_allowance, 0) +
													COALESCE(fact.other_fee, 0) +
													COALESCE(fact.merchant_send_fee, 0) +
													COALESCE(fact.commission, 0) +
													COALESCE(fact.rounding, 0) -
													COALESCE(fact.overflow_amount, 0))
										   )             AS net_amount,
										   SUM(fact.qty) AS item_count
									FROM sales_product_amounts fact
									LEFT JOIN store_caches ON fact.store_id = store_caches.id
         							LEFT JOIN product_caches ON fact.product_id = product_caches.id
									WHERE {BASE_WHERE}
									GROUP BY fact.product_id, product_caches.category),
			 product_net_amount AS (SELECT product_id, category_id, net_amount, item_count
									FROM base_product fact
										LEFT JOIN product_caches ON fact.product_id = product_caches.id
									{WHERE}),
			 total_net_amount AS (SELECT SUM(net_amount) AS total_net_amount
											  FROM base_product)
		SELECT pna.product_id                          AS product_id,
               pna.category_id                         AS category_id,
			   pna.net_amount                          AS net_amount,
			   pna.item_count                          AS item_count,
			   (pna.net_amount / tna.total_net_amount) AS net_amount_ratio,
			   0                                       AS total
		FROM product_net_amount pna,
			 total_net_amount tna
		ORDER BY pna.product_id
		 {LIMIT};
`
	summarySQL = `
		--菜品销售汇总表 Summary
		WITH base_product AS (SELECT fact.product_id AS product_id,
								product_caches.category AS category_id,
										   SUM(
												   (COALESCE(fact.gross_amount2, 0) +
													COALESCE(fact.package_amount, 0) +
													COALESCE(fact.send_fee_for_merchant, 0) +
													COALESCE(fact.service_fee, 0) +
													COALESCE(fact.tip, 0)) -
												   (COALESCE(fact.pay_merchant_allowance, 0) +
													COALESCE(fact.promotion_merchant_allowance, 0) +
													COALESCE(fact.other_fee, 0) +
													COALESCE(fact.merchant_send_fee, 0) +
													COALESCE(fact.commission, 0) +
													COALESCE(fact.rounding, 0) -
													COALESCE(fact.overflow_amount, 0))
										   )             AS net_amount,
										   SUM(fact.qty) AS item_count
									FROM sales_product_amounts fact
									LEFT JOIN store_caches ON fact.store_id = store_caches.id
         							LEFT JOIN product_caches ON fact.product_id = product_caches.id
									WHERE {BASE_WHERE}
									GROUP BY fact.product_id, product_caches.category),
			product_net_amount AS (SELECT product_id, category_id, net_amount, item_count
							FROM base_product fact
										LEFT JOIN product_caches ON fact.product_id = product_caches.id
							{WHERE}),
			 total_net_amount AS (SELECT SUM(net_amount) AS total_net_amount, SUM(item_count) total_item_count
								  FROM product_net_amount),
			 total_count AS (SELECT COUNT(product_id) AS total_count
							 FROM product_net_amount)
		SELECT 0                                 AS product_id,
               0                                 AS category_id,
			   COALESCE(tna.total_net_amount, 0) AS net_amount,
			   COALESCE(tna.total_item_count, 0) AS item_count,
			   0                                 AS net_amount_ratio,
			   total_count.total_count           AS total
		FROM total_net_amount tna,
			 total_count;
`

	baseWhereSQL := generateBaseWhereSQLForProductSalesSummary(condition)
	whereSQL := generateWhereSQLForProductSalesSummary(condition)
	regionSQL := generateRegionSQLForProductSalesSummary(condition)
	limitSQL := generateLimitOffsetSQLForProductSalesSummary(condition)

	rowSQL = strings.ReplaceAll(rowSQL, "{BASE_WHERE}", baseWhereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{WHERE}", whereSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{LIMIT}", limitSQL)
	rowSQL = strings.ReplaceAll(rowSQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG row. SQL: `%s`", trackId, rowSQL)

	summarySQL = strings.ReplaceAll(summarySQL, "{BASE_WHERE}", baseWhereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{WHERE}", whereSQL)
	summarySQL = strings.ReplaceAll(summarySQL, "{REGION_ID}", regionSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateRegionSQLForProductSalesSummary(condition *model.RepoCondition) string {
	regionSQL := "fact.store_id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	case "FRANCHISEE_REGION": // 加盟商区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.franchisee%d", condition.RegionGroupLevel)
		}
	case "COMPANY": // 按公司
		regionSQL = fmt.Sprintf("store_caches.company_info")
	}
	return regionSQL
}

func generateBaseWhereSQLForProductSalesSummary(condition *model.RepoCondition) string {
	var (
		regionIdsSQL string // 区域筛选条件
		comboSQL     string // 套餐商品过滤条件
	)
	// 商品的套餐类型由combo_type决定：0:非套餐商品；1:套餐头;2:套餐子项
	comboSQL = fmt.Sprintf(`AND (fact.combo_type != 1)`)
	regionIdsSQL = GenerateRegionWhereSQL(condition)
	var storeSQL string
	if condition.RegionSearchType != "STORE" && len(condition.StoreIDs) > 0 { // 查询类型不是门店时，根据数据校验获取的门店ids拼接查询条件
		if len(condition.StoreIDs) == 1 {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id = %d	
			`, condition.StoreIDs)
		} else {
			storeSQL = fmt.Sprintf(`
				AND store_caches.id IN (%s)
			`, helpers.JoinInt64(condition.StoreIDs, ","))
		}
	}
	regionCodesSQL := GenerateRegionCodesWhereSQL(condition)
	netAmountSQL := ""
	if !condition.IncludeRealAmountZero {
		netAmountSQL = fmt.Sprintf(`
			AND fact.products_real_amount != 0
		`)
	}
	whereSQL := fmt.Sprintf(`
		fact.partner_id = %d 
		AND fact.scope_id = %d
		AND fact.bus_date >= '%s'
		AND fact.bus_date <= '%s'
		AND fact.is_non_sales = FALSE
		%s
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, storeSQL, comboSQL, regionCodesSQL, netAmountSQL)
	return whereSQL
}

func generateWhereSQLForProductSalesSummary(condition *model.RepoCondition) string {
	var (
		productIdsSQL         string // 商品筛选条件
		productCategoryIdsSQL string // 类别筛选条件
	)
	if len(condition.ProductIds) > 0 {
		productIdsSQL = fmt.Sprintf(
			" WHERE product_id IN (%s)",
			helpers.JoinInt64(condition.ProductIds, ","),
		)
	}

	if len(condition.ProductCategoryIds) > 0 {
		if productIdsSQL != "" {
			productCategoryIdsSQL = fmt.Sprintf(`
			AND (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
			)
		} else {
			productCategoryIdsSQL = fmt.Sprintf(`
			WHERE (
				product_caches.category0 IN (%s)
				OR product_caches.category1 IN (%s)
				OR product_caches.category2 IN (%s)
			)`,
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
				helpers.JoinInt64(condition.ProductCategoryIds, ","),
			)
		}
	}

	whereSQL := fmt.Sprintf(`
		%s
		%s
	`, productIdsSQL, productCategoryIdsSQL)
	return whereSQL
}

func generateLimitOffsetSQLForProductSalesSummary(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
