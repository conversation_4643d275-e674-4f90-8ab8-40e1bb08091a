package repo

import (
	"context"

	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/model/report"
)

type MakeTimeQueryRepository interface {
	// ProductMakeTime 单杯出杯时间
	ProductMakeTime(ctx context.Context, condition *model.RepoCondition) (*report.ProductMakeTimeResponse, error)
	// StoreMakeTime 订单完成时间
	StoreMakeTime(ctx context.Context, condition *model.RepoCondition) (*report.StoreMakeTimeResponse, error)
}

type SalesAdjustQueryRepository interface {
	Adjust(ctx context.Context, condition *model.RepoCondition) (*report.AdjustResponse, error)
}

type SalesInboundQueryRepository interface {
	Inbound(ctx context.Context, condition *model.RepoCondition) (*report.InboundResponse, error)
	PosInbound(ctx context.Context, condition *model.RepoCondition) (*report.PosInboundResponse, error)
}

type SalesRepository interface {
	Dashboard(ctx context.Context, condition *model.RepoDashboardCondition) (*model.DashboardResponse, error)
	// StoreSales 门店销售
	StoreSales(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	// StoreChannelSales 门店渠道
	StoreChannelSales(ctx context.Context, condition *model.RepoCondition) (*report.StoreChannelSalesResponse, error)
	// StorePeriodSales 门店时段
	StorePeriodSales(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	StorePeriodSalesForPre(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	// ProductSales 单品销售
	ProductSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesResponse, error)
	// ProductChannelSales 单品渠道
	ProductChannelSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductChannelResponse, error)
	// ProductPeriodSales 单品时段
	ProductPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductPeriodResponse, error)

	ProductCategorySales(ctx context.Context, condition *model.RepoCondition) (*report.CategorySalesResponse, error)

	// PaymentStatistics 支付统计
	PaymentStatistics(ctx context.Context, condition *model.RepoCondition) (*report.PaymentStatisticsResponse, error)
	// DiscountSales 交易折扣
	DiscountSales(ctx context.Context, condition *model.RepoCondition) (*report.DiscountSalesResponse, error)
	// PaymentPeriodSales 支付时段
	PaymentPeriodSales(ctx context.Context, condition *model.RepoCondition) (*report.PaymentPeriodResponse, error)
	PaymentPeriodSalesForPre(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	// RefundAnalyse 退单原因分析
	RefundAnalyse(ctx context.Context, condition *model.RepoCondition) (*report.RefundAnalysisResponse, error)
	// 商品属性销售表
	ProductFlavorSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductFlavorSalesResponse, error)
	// 商品计税表
	ProductTaxSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductTaxSalesResponse, error)
	StoreBusiness(ctx context.Context, condition *report.StoreBusinessRequest) (*report.StoreBusinessResponse, error)
	PosPromotion(ctx context.Context, condition *model.RepoCondition) (*report.PosPromotionResponse, error)
	ProductSalesSummary(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesSummaryResponse, error)
	ProductSalesDetail(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesDetailResponse, error)
	MealSegments(ctx context.Context, condition *model.RepoCondition) (*report.MealSegmentsResponse, error)
	StorePerformanceAnalysis(ctx context.Context, condition *model.RepoCondition) (*report.StorePerformanceAnalysisResponse, error)
	ProductSalesComboSummary(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesComboSummaryResponse, error)
	ProductSalesComboDetail(ctx context.Context, condition *model.RepoCondition) (*report.ProductSalesComboDetailResponse, error)
	StoreOpenDate(ctx context.Context, partnerIds []int64) (*report.StoreOpenDateResponse, error)
	// PaymentStatistics 支付统计
	PaymentDetail(ctx context.Context, condition *model.RepoCondition) (*report.PaymentDetailResponse, error)
	ProductAttributeSales(ctx context.Context, condition *model.RepoCondition) (*report.ProductAttributeSalesResponse, error)
	// 非营业销售报表
	StoreCompensationSalesReport(ctx context.Context, condition *model.RepoCondition) (*report.StoreCompensationSalesReportResp, error)
}
