package repo

import (
	"context"
	"fmt"

	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

func (rsmm *SalesRepositoryPG) PaymentPeriodSalesForPre(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForPaymentPeriodSalesForPre(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, rsmm.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)

	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

func generateQuerySQLForPaymentPeriodSalesForPre(trackId int64, condition *model.RepoCondition) (string, string) {
	baseSelectSQL := generateBaseSelectSQLForPaymentPeriodSalesForPre(condition)
	selectSQL := generateSelectSQLForPaymentPeriodSalesForPre(condition)
	whereSQL := generateWhereSQLForPaymentPeriodSalesForPre(condition)
	fromSQL := generateFromSQLForPaymentPeriodSalesForPre(condition)
	orderSQL := generateOrderSQLForPaymentPeriodSalesForPre(condition)
	groupSQL := generateGroupSQLForPaymentPeriodSalesForPre(condition)
	limitOffsetSQL := generateLimitOffsetSQLForPaymentPeriodSalesForPre(condition)

	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)

	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			%s
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, baseSelectSQL, baseSelectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateDateSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

func generateRegionSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateBaseSelectSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	return `
		SUM(net_amount_00) AS net_amount_00,
		SUM(net_amount_01) AS net_amount_01,
		SUM(net_amount_02) AS net_amount_02,
		SUM(net_amount_03) AS net_amount_03,
		SUM(net_amount_04) AS net_amount_04,
		SUM(net_amount_05) AS net_amount_05,
		SUM(net_amount_06) AS net_amount_06,
		SUM(net_amount_07) AS net_amount_07,
		SUM(net_amount_08) AS net_amount_08,
		SUM(net_amount_09) AS net_amount_09,
		SUM(net_amount_10) AS net_amount_10,
		SUM(net_amount_11) AS net_amount_11,
		SUM(net_amount_12) AS net_amount_12,
		SUM(net_amount_13) AS net_amount_13,
		SUM(net_amount_14) AS net_amount_14,
		SUM(net_amount_15) AS net_amount_15,
		SUM(net_amount_16) AS net_amount_16,
		SUM(net_amount_17) AS net_amount_17,
		SUM(net_amount_18) AS net_amount_18,
		SUM(net_amount_19) AS net_amount_19,
		SUM(net_amount_20) AS net_amount_20,
		SUM(net_amount_21) AS net_amount_21,
		SUM(net_amount_22) AS net_amount_22,
		SUM(net_amount_23) AS net_amount_23,
		SUM(order_count_00) AS order_count_00,
		SUM(order_count_01) AS order_count_01,
		SUM(order_count_02) AS order_count_02,
		SUM(order_count_03) AS order_count_03,
		SUM(order_count_04) AS order_count_04,
		SUM(order_count_05) AS order_count_05,
		SUM(order_count_06) AS order_count_06,
		SUM(order_count_07) AS order_count_07,
		SUM(order_count_08) AS order_count_08,
		SUM(order_count_09) AS order_count_09,
		SUM(order_count_10) AS order_count_10,
		SUM(order_count_11) AS order_count_11,
		SUM(order_count_12) AS order_count_12,
		SUM(order_count_13) AS order_count_13,
		SUM(order_count_14) AS order_count_14,
		SUM(order_count_15) AS order_count_15,
		SUM(order_count_16) AS order_count_16,
		SUM(order_count_17) AS order_count_17,
		SUM(order_count_18) AS order_count_18,
		SUM(order_count_19) AS order_count_19,
		SUM(order_count_20) AS order_count_20,
		SUM(order_count_21) AS order_count_21,
		SUM(order_count_22) AS order_count_22,
		SUM(order_count_23) AS order_count_23
	`
}

func generateSelectSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	_, dateSQL := generateDateSQLForPaymentPeriodSalesForPre(condition)
	_, regionSQL := generateRegionSQLForPaymentPeriodSalesForPre(condition)
	baseSelectSQL := generateBaseSelectSQLForPaymentPeriodSalesForPre(condition)
	var selectSQL string
	if dateSQL != "" {
		selectSQL = fmt.Sprintf(`
		%s,
	`, dateSQL)
	}
	selectSQL += fmt.Sprintf(`
		%s,
		payment_id,
		%s
	`, regionSQL, baseSelectSQL)
	return selectSQL
}

func generateWhereSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	regionIdsSQL := GenerateRegionWhereSQL(condition)

	whereSQL := fmt.Sprintf(`
		sales_payment_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL)
	return whereSQL
}

func generateFromSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	var leftJoinSQL = `
		payment_period_sales
			LEFT JOIN store_caches ON payment_period_sales.store_id = store_caches.id
	`
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenWhereSql(condition)
	return leftJoinSQL
}

func generateGroupSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForPaymentPeriodSalesForPre(condition)
	regionSQL, _ := generateRegionSQLForPaymentPeriodSalesForPre(condition)
	var groupSQL string
	if dateSQL != "" {
		groupSQL = fmt.Sprintf(`
		%s,
	`, dateSQL)
	}
	groupSQL += fmt.Sprintf(`
		%s,
		payment_id
	`, regionSQL)
	return groupSQL
}

func generateOrderSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForPaymentPeriodSalesForPre(condition)
	regionSQL, _ := generateRegionSQLForPaymentPeriodSalesForPre(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
	`, dateSQL)
	}
	orderSQL += fmt.Sprintf(`
		%s
	`, regionSQL)
	return orderSQL
}

func generateLimitOffsetSQLForPaymentPeriodSalesForPre(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
