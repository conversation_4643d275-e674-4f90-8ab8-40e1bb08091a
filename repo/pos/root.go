package pos

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gorm.io/gorm"
)

type PosRepoPG struct {
	DB *gorm.DB
}

// 查询门店订单
func (pr *PosRepoPG) QueryStoreTickets(ctx context.Context, partnerId int64, storeId int64, start, end string) ([]*model.Ticket, error) {
	tickets, err := QueryStoreTickets(ctx, pr.DB, "sales_ticket_contents", partnerId, storeId, start, end)
	if err != nil {
		tickets = make([]*model.Ticket, 0)
		logger.Pre().Error("QueryStoreTickets error: ", err)
		err = nil
	}
	abnormalTickets, err := QueryStoreTickets(ctx, pr.DB, "sales_ticket_contents_abnormal", partnerId, storeId, start, end)
	if err != nil {
		logger.Pre().Error("QueryStoreTickets error: ", err)
	}
	// 已经有正常单的情况下，取正常单
	mp := make(map[string]*model.Ticket)
	for _, ticket := range tickets {
		mp[ticket.TicketId] = ticket
	}
	for _, ticket := range abnormalTickets {
		if _, ok := mp[ticket.TicketId]; !ok {
			tickets = append(tickets, ticket)
		}
	}
	return tickets, nil
}

func QueryStoreTickets(ctx context.Context, db *gorm.DB, table string, partnerId int64, storeId int64, start, end string) ([]*model.Ticket, error) {
	rs, err := db.Debug().WithContext(ctx).Raw(
		`SELECT content FROM `+table+` where partner_id = ? and store_id = ? and bus_date >= ? and bus_date <= ?`,
		partnerId, storeId, start, end).Rows()
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	tickets := make([]*model.Ticket, 0)
	for rs.Next() {
		var content string
		rs.Scan(&content)
		var ticket model.Ticket
		json.Unmarshal([]byte(content), &ticket)
		tickets = append(tickets, &ticket)
	}
	return tickets, nil
}

// 查询门店订单
func (pr *PosRepoPG) QueryStoreTicketsByShiftNumber(ctx context.Context, storeId int64, shiftNumber string) ([]*model.Ticket, error) {
	tickets, err := QueryStoreTicketsByShiftNumber(ctx, pr.DB, "sales_ticket_contents", storeId, shiftNumber)
	if err != nil {
		tickets = make([]*model.Ticket, 0)
		logger.Pre().Error("QueryStoreTickets error: ", err)
		err = nil
	}
	abnormalTickets, err := QueryStoreTicketsByShiftNumber(ctx, pr.DB, "sales_ticket_contents_abnormal", storeId, shiftNumber)
	if err != nil {
		logger.Pre().Error("QueryStoreTickets error: ", err)
	}
	// 已经有正常单的情况下，取正常单
	mp := make(map[string]*model.Ticket)
	for _, ticket := range tickets {
		mp[ticket.TicketId] = ticket
	}
	for _, ticket := range abnormalTickets {
		if _, ok := mp[ticket.TicketId]; !ok {
			tickets = append(tickets, ticket)
		}
	}
	return tickets, nil
}

func QueryStoreTicketsByShiftNumber(ctx context.Context, db *gorm.DB, table string, storeId int64, shiftNumber string) ([]*model.Ticket, error) {
	rs, err := db.Debug().WithContext(ctx).Raw(
		`SELECT content FROM `+table+` where store_id = ? and shift_number = ?`,
		storeId, shiftNumber).Rows()
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	tickets := make([]*model.Ticket, 0)
	for rs.Next() {
		var content string
		rs.Scan(&content)
		var ticket model.Ticket
		json.Unmarshal([]byte(content), &ticket)
		tickets = append(tickets, &ticket)
	}
	return tickets, nil
}

// 查询门店订单
func (pr *PosRepoPG) QueryTicketById(ctx context.Context, ticketId string) (*model.Ticket, error) {
	ticket, err := queryTicketById(ctx, pr.DB, "sales_ticket_contents", ticketId)
	if err != nil {
		logger.Pre().Error("QueryStoreTickets error: ", err)
		err = nil
	} else {
		return ticket, nil
	}
	abnormalTicket, err := queryTicketById(ctx, pr.DB, "sales_ticket_contents_abnormal", ticketId)
	if err != nil {
		logger.Pre().Error("QueryStoreTickets error: ", err)
		return nil, err
	}
	return abnormalTicket, nil
}

func queryTicketById(ctx context.Context, db *gorm.DB, table string, ticketId string) (*model.Ticket, error) {
	rs, err := db.Debug().WithContext(ctx).Raw(
		`SELECT content FROM `+table+` where ticket_id = ? limit 1`, ticketId).Rows()
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	for rs.Next() {
		var content string
		rs.Scan(&content)
		var ticket model.Ticket
		json.Unmarshal([]byte(content), &ticket)
		return &ticket, nil
	}
	return nil, gorm.ErrRecordNotFound
}

// 批量查询商品分类id
func (pr *PosRepoPG) QueryProductsCategory(ctx context.Context, partnerId int64, skuIds []int64) (map[int64]int64, error) {
	if len(skuIds) == 0 {
		return nil, nil
	}
	rs, err := pr.DB.Debug().WithContext(ctx).Raw("select id, category from product_caches where partner_id=? and id in (?)", partnerId, skuIds).Rows()
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	categorys := make(map[int64]int64)
	for rs.Next() {
		var id, category int64
		rs.Scan(&id, &category)
		categorys[id] = category
	}
	return categorys, nil
}

func (pr *PosRepoPG) QueryProductsCategoryMap(ctx context.Context, partnerId int64) (map[int64]int64, error) {
	rs, err := pr.DB.Debug().WithContext(ctx).Raw("select id, category from product_caches where partner_id=?", partnerId).Rows()
	if err != nil {
		return nil, err
	}
	defer rs.Close()
	categorys := make(map[int64]int64)
	for rs.Next() {
		var id, category int64
		rs.Scan(&id, &category)
		categorys[id] = category
	}
	return categorys, nil
}
