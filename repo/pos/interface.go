package pos

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

type PosRepo interface {

	// 查询门店订单
	QueryStoreTickets(ctx context.Context, partnerId int64, storeId int64, start, end string) ([]*model.Ticket, error)

	QueryStoreTicketsByShiftNumber(ctx context.Context, storeId int64, shiftNumber string) ([]*model.Ticket, error)

	// 查询订单
	QueryTicketById(ctx context.Context, ticketId string) (*model.Ticket, error)

	// 批量查询商品分类id
	QueryProductsCategory(ctx context.Context, partnerId int64, skuIds []int64) (map[int64]int64, error)
	// 查询全部商品分类
	QueryProductsCategoryMap(ctx context.Context, partnerId int64) (map[int64]int64, error)
}
