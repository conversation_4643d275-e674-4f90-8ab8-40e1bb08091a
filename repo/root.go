package repo

import (
	"gitlab.hexcloud.cn/histore/sales-report/repo/cash_management"
	"gitlab.hexcloud.cn/histore/sales-report/repo/diff"
	"gitlab.hexcloud.cn/histore/sales-report/repo/finance"
	"gitlab.hexcloud.cn/histore/sales-report/repo/financial"
	"gitlab.hexcloud.cn/histore/sales-report/repo/new_pos_report"
	"gitlab.hexcloud.cn/histore/sales-report/repo/open"
	"gitlab.hexcloud.cn/histore/sales-report/repo/pos"
	all_repo "gitlab.hexcloud.cn/histore/sales-report/repo/report/allticket"
	"gitlab.hexcloud.cn/histore/sales-report/repo/report_config"
	"gitlab.hexcloud.cn/histore/sales-report/repo/shopkeeper"
	abnormal "gitlab.hexcloud.cn/histore/sales-report/repo/ticket/allticket"

	report "gitlab.hexcloud.cn/histore/sales-report/repo/report"
	ticket "gitlab.hexcloud.cn/histore/sales-report/repo/ticket"
)

var (
	DefaultSalesDB                  ticket.SalesDB                     // 小票db
	DefaultAbnormalSalesDB          abnormal.AbnormalSalesDB           // 小票db
	DefaultSalesErrDB               ticket.SalesErrDB                  // 异常小票db
	DefaultResolveTicket            ticket.ResolveTicket               // 拆解db
	DefaultResolveTicketAll         abnormal.ResolveTicketAll          // 拆解db
	DefaultMetadataCache            ticket.MetadataCache               // 默认缓存db
	DefaultSalesRepository          report.SalesRepository             // 报表db
	DefaultAllTicketSalesRepository all_repo.AllTicketSalesRepository  // 全量小票报表db
	DefaultOpenRepository           open.OpenRepository                // open报表db
	DefaultMakeTimeRepository       ticket.MakeTimeRepository          // 制作时间数据修改db
	DefaultMakeTimeQueryRepository  report.MakeTimeQueryRepository     // 制作时间报表查询db
	DefaultFinancialRepository      financial.FinancialRepository      // 财务报表接口
	DefaultFinanceRepo              finance.FinRepository              // 财务报表接口
	DefaultShopKeeper               shopkeeper.KeeperInterface         // 小掌柜接口
	DefaultDiffRepo                 diff.DiffRepo                      // diff
	DefaultReportConfigRepo         report_config.ReportConfigRepo     // 报表配置
	DefaultPosRepo                  pos.PosRepo                        // pos
	DefaultNewPosRepo               new_pos_report.NewPosRepo          // new_pos
	DefaultAdjustDB                 ticket.SalesAdjustRepository       // 报损修改db
	DefaultAdjustQueryDB            report.SalesAdjustQueryRepository  // 报损修改db
	DefaultInboundDB                ticket.SalesInboundRepository      // 入库修改db
	DefaultInboundQueryDB           report.SalesInboundQueryRepository // 入库查询db
	DefaultCashManagementDB         cash_management.CashManagementRepository
)
