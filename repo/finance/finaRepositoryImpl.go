package finance

import (
	"database/sql"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
)

// 因为在别的go文件中实现了接口
type FinaRepositoryImpl struct {
	DB *sql.DB
}

func GenerateRegionWhereSQL(condition *model.RepoCondition) string {
	var regionWhereSQL string
	// 如果查询门店id为空，则不加门店过滤语句
	if len(condition.RegionSearchIds) > 0 {
		switch condition.RegionSearchType {
		case "GEO_REGION":
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 0
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		case "BRANCH_REGION":
			regionWhereSQL = fmt.Sprintf(
				`AND store_caches.id IN (
					SELECT c_id FROM id2id_caches WHERE id IN (%s) AND type = 1
				)`,
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		default:
			regionWhereSQL = fmt.Sprintf(
				"AND store_caches.id IN (%s)",
				helpers.JoinInt64(condition.RegionSearchIds, ","),
			)
		}
	}
	return regionWhereSQL
}
