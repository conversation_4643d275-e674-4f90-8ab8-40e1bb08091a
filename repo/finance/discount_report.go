package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 财务报表的优惠组成
func (f *FinaRepositoryImpl) DiscountReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerySQLForDiscountReport(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go helpers.QueryDB<PERSON>ith<PERSON>han(trackId, f.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

func generateQuerySQLForDiscountReport(trackId int64, condition *model.RepoCondition) (string, string) {
	baseSelectSQL := generateBaseSelectSQLForDiscountReport(condition)
	selectSQL := generateSelectSQLForDiscountReport(condition)
	whereSQL := generateWhereSQLForDiscountReport(condition)
	fromSQL := generateFromSQLForDiscountReport(condition)
	orderSQL := generateOrderSQLForDiscountReport(condition)
	groupSQL := generateGroupSQLForDiscountReport(condition)
	limitOffsetSQL := generateLimitOffsetSQLForDiscountReport(condition)

	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)

	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(amount) AS discount_amount,
			SUM(real_amount) AS real_amount,
			SUM(amount_returned) AS discount_amount_returned,
			SUM(real_amount_returned) AS real_amount_returned,
			SUM(item_count) AS item_count,
			SUM(item_count_returned) AS item_count_returned,
			SUM(eticket_count) AS eticket_count,
			SUM(eticket_count_returned) AS eticket_count_returned
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, baseSelectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateDateSQLForDiscountReport(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

func generateRegionSQLForDiscountReport(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateBaseSelectSQLForDiscountReport(condition *model.RepoCondition) string {
	if condition.IsPre {
		return `
		SUM(amount) AS amount,
		SUM(real_amount) AS real_amount,
		SUM(CASE WHEN refunded THEN amount ELSE 0 END) AS amount_returned,
		SUM(CASE WHEN refunded THEN real_amount ELSE 0 END) AS real_amount_returned,
		SUM(item_count) AS item_count,
		SUM(item_count_returned) AS item_count_returned,
		SUM(eticket_count) AS eticket_count,
		SUM(eticket_count_returned) AS eticket_count_returned
		`
	}
	return `
		SUM(amount) AS amount,
		SUM(real_amount) AS real_amount,
		SUM(CASE WHEN refunded THEN amount ELSE 0 END) AS amount_returned,
		SUM(CASE WHEN refunded THEN real_amount ELSE 0 END) AS real_amount_returned,
		SUM(qty) AS item_count,
		SUM(CASE WHEN refunded THEN qty ELSE 0 END) AS item_count_returned,
		SUM(eticket_count) AS eticket_count,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS eticket_count_returned
	`
}

func generateSelectSQLForDiscountReport(condition *model.RepoCondition) string {
	_, dateSQL := generateDateSQLForDiscountReport(condition)
	_, regionSQL := generateRegionSQLForDiscountReport(condition)
	baseSelectSQL := generateBaseSelectSQLForDiscountReport(condition)

	var selectSQL string
	if dateSQL != "" {
		selectSQL = fmt.Sprintf(`
		%s,
	`, dateSQL)
	}
	selectSQL += fmt.Sprintf(`
		%s,
		promotion_id,
		%s
	`, regionSQL, baseSelectSQL)
	return selectSQL
}

func generateWhereSQLForDiscountReport(condition *model.RepoCondition) string {
	regionIdsSQL := GenerateRegionWhereSQL(condition)

	paymentMethodIdsSQL := ""
	if len(condition.DiscountIds) > 0 {
		paymentMethodIdsSQL = fmt.Sprintf(
			"AND promotion_id IN (%s)",
			helpers.JoinInt64(condition.DiscountIds, ","),
		)
	}
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	whereSQL := fmt.Sprintf(`
		sales_discount_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, paymentMethodIdsSQL, channelIdSQL, orderTypeSQL)
	return whereSQL
}

func generateFromSQLForDiscountReport(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre {
		leftJoinSQL = `
			discount_sales
				LEFT JOIN store_caches ON discount_sales.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_discount_amounts
			LEFT JOIN store_caches ON sales_discount_amounts.store_id = store_caches.id
	`
	}
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenWhereSql(condition)
	return leftJoinSQL
}

func generateGroupSQLForDiscountReport(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForDiscountReport(condition)
	regionSQL, _ := generateRegionSQLForDiscountReport(condition)
	var groupSQL string
	if dateSQL != "" {
		groupSQL = fmt.Sprintf(`
		%s,
	`, dateSQL)
	}
	groupSQL += fmt.Sprintf(`
		%s,
		promotion_id
	`, regionSQL)
	return groupSQL
}

func generateOrderSQLForDiscountReport(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForDiscountReport(condition)
	regionSQL, _ := generateRegionSQLForDiscountReport(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
	`, dateSQL)
	}
	orderSQL += fmt.Sprintf(`
		%s
	`, regionSQL)
	return orderSQL
}

func generateLimitOffsetSQLForDiscountReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
