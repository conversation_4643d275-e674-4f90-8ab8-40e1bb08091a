package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 财务报表：实收组成统计
func (f *FinaRepositoryImpl) NetReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL, summaryColSQL := generateQuerySQLForNetReport(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, f.DB, summaryColSQL, ch2)
	rows := <-ch1
	summaryCol := <-ch2
	close(ch1)
	close(ch2)
	// 将行数据转为map合计返回给前端
	var summay = make(map[string]interface{})
	if len(summaryCol) > 0 {
		for _, pay := range summaryCol {
			payMap := pay.(map[string]interface{})
			paymentId := cast.ToString(payMap["payment_id"])
			summay[paymentId] = cast.ToFloat64(payMap["real_amount"])
		}
	}
	return &model.Response{Rows: rows, Summary: summay}, nil
}

func generateQuerySQLForNetReport(trackId int64, condition *model.RepoCondition) (string, string) {
	// 生成summarySQL统计项
	//aggsSQL := generateAggsSQLForNetReport(condition)
	// 生成select语句
	selectSQL := generateSelectSQLForNetReport(condition)
	// 生成查询条件
	whereSQL := generateWhereSQLForNetReport(condition)
	// 和主档缓存表关联查询
	fromSQL := generateFromSQLForNetReport(condition)
	// 添加排序类型的过滤
	orderSQL := generateOrderSQLForNetReport(condition)
	// 按门店和营业日期分组
	groupSQL := generateGroupSQLForNetReport(condition)
	// 组装总的rowSQL语句，因为要根据bus_data和store_caches.geo0分组统计，所以用了sum
	limitOffsetSQL := generateLimitOffsetSQLForNetReport(condition)

	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)
	// 不同门店统一行组装汇总查询sql
	summaryColSQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			payment_id,
			SUM(real_amount) AS real_amount
		FROM (
			%s
		) T
		GROUP BY payment_id
	`, rowSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summaryColSQL)

	return rowSQL, summaryColSQL
}

func generateLimitOffsetSQLForNetReport(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}

func generateGroupSQLForNetReport(condition *model.RepoCondition) string {
	return `
		store_caches.id,
		payment_id
		`
}

func generateDateSQLForNetReport(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	// 作为周期组
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总,
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

// 根据区域查询条件生成区域分组sql
func generateRegionSQLForNetReport(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateOrderSQLForNetReport(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForNetReport(condition)
	regionSQL, _ := generateRegionSQLForNetReport(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
		%s
	`, dateSQL, regionSQL)
	} else {
		orderSQL = fmt.Sprintf(`
		%s
	`, regionSQL)
	}
	return orderSQL
}

func generateFromSQLForNetReport(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre { // 是否使用预计算，就是是否使用flink计算，如果数据大的话，可以用
		leftJoinSQL = `
			store_sales
				LEFT JOIN store_caches ON sales_payment.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_payment_amounts
			LEFT JOIN store_caches ON sales_payment_amounts.store_id = store_caches.id
		`
	}
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenSqlForNetReport(condition)
	return leftJoinSQL
}

// 根据门店类型和开店类型生成sql
func generateStoreOpenSqlForNetReport(condition *model.RepoCondition) string {
	var StoreStatusSql string
	if condition.StoreType != "" {
		StoreStatusSql = fmt.Sprintf(`
		AND store_caches.store_type = %s
	`, condition.StoreType)
	}
	if condition.OpenStatus != "" {
		StoreStatusSql += fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	return StoreStatusSql
}

func generateWhereSQLForNetReport(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	whereSQL := fmt.Sprintf(`
		sales_payment_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL)
	return whereSQL
}

func generateSelectSQLForNetReport(condition *model.RepoCondition) string {
	aggsSql := generateAggsSQLForNetReport(condition)
	return fmt.Sprintf(`
			store_caches.id AS region_id,
			payment_id,
			%s
			`, aggsSql)
}

func generateAggsSQLForNetReport(condition *model.RepoCondition) string {
	return "sum(real_amount) as real_amount"
}
