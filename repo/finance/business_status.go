package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 营业状态数据
func (f *FinaRepositoryImpl) BusinessStatus(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL, summarySQL, businessDaySQL := generateQuerySQLForBusinessStatus(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	ch3 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, f.DB, summarySQL, ch2)
	go helpers.QueryDBWithChan(trackId, f.DB, businessDaySQL, ch3)
	rows := <-ch1
	summarys := <-ch2
	businessDays := <-ch3
	close(ch1)
	close(ch2)
	close(ch3)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	// 将查询到的营业日期映射到每一行上面
	for _, r := range rows {
		rowMap := r.(map[string]interface{})
		for _, bd := range businessDays {
			bdMap := cast.ToStringMap(bd)
			if rowMap["region_id"] == bdMap["store_id"] {
				value := bdMap["business_days"]
				rowMap["business_days"] = value
			}
		}
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

// 生成sum和sql
func generateAggsSQLForBusinessStatus(condition *model.RepoCondition) string {
	if condition.IsPre {
		return `
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(merchant_send_fee) AS merchant_send_fee,
			SUM(send_fee_for_merchant) AS send_fee_for_merchant,
			SUM(discount_merchant_contribute) AS discount_merchant_contribute,
			SUM(pay_merchant_contribute) AS pay_merchant_contribute,
			SUM(order_count) AS order_count,
			SUM(product_count) AS product_count,
			SUM(accessory_count) AS accessory_count,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(tip_returned) AS tip_returned,
			SUM(package_fee_returned) AS package_fee_returned,
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(service_fee_returned) AS service_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(pay_amount_returned) AS pay_amount_returned,
			SUM(rounding_returned) AS rounding_returned,
			SUM(overflow_amount_returned) AS overflow_amount_returned,
			SUM(change_amount_returned) AS change_amount_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_0_returned) AS amount_0_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(amount_4_returned) AS amount_4_returned,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
			SUM(platform_discount_amount) AS platform_discount_amount,
			SUM(payment_transfer_amount) AS payment_transfer_amount,
			SUM(discount_transfer_amount) AS discount_transfer_amount
		`
	}
	return `
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(eticket_count) AS order_count,
			SUM(product_count) AS product_count,
			SUM(accessory_count) AS accessory_count,
			SUM(merchant_send_fee) AS merchant_send_fee,
			SUM(send_fee_for_merchant) AS send_fee_for_merchant,
			SUM(discount_merchant_contribute) AS discount_merchant_contribute,
			SUM(pay_merchant_contribute) AS pay_merchant_contribute,
			SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
			SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
			SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
			SUM(CASE WHEN refunded THEN tip ELSE 0 END) AS tip_returned,
			SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
			SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
			SUM(CASE WHEN refunded THEN service_fee ELSE 0 END) AS service_fee_returned,
			SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
			SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
			SUM(CASE WHEN refunded THEN pay_amount ELSE 0 END) AS pay_amount_returned,
			SUM(CASE WHEN refunded THEN rounding ELSE 0 END) AS rounding_returned,
			SUM(CASE WHEN refunded THEN merchant_send_fee ELSE 0 END) AS merchant_send_fee_returned,
			SUM(CASE WHEN refunded THEN send_fee_for_merchant ELSE 0 END) AS send_fee_for_merchant_returned,
			SUM(CASE WHEN refunded THEN discount_merchant_contribute ELSE 0 END) AS discount_merchant_contribute_returned,
			SUM(CASE WHEN refunded THEN pay_merchant_contribute ELSE 0 END) AS pay_merchant_contribute_returned,
			SUM(CASE WHEN refunded THEN overflow_amount ELSE 0 END) AS overflow_amount_returned,
			SUM(CASE WHEN refunded THEN change_amount ELSE 0 END) AS change_amount_returned,
			SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
       		SUM(CASE WHEN order_status = 'SALE' THEN eticket_count ELSE 0 END)   AS order_count_sale,
       		SUM(CASE WHEN order_status = 'PARTIALREFUND' THEN eticket_count ELSE 0 END)   AS order_count_partialrefund,
       		SUM(CASE WHEN order_status = 'REFUND' THEN eticket_count ELSE 0 END)   AS order_count_refund,
			SUM(commission) AS commission,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
			SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS amount_0_returned,
			SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS amount_1_returned,
			SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS amount_2_returned,
			SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS amount_3_returned,
			SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned,
			SUM(merchant_discount_amount) AS merchant_discount_amount,
			SUM(platform_discount_amount) AS platform_discount_amount,
			SUM(payment_transfer_amount) AS payment_transfer_amount,
			SUM(discount_transfer_amount) AS discount_transfer_amount
		`
}

func generateQuerySQLForBusinessStatus(trackId int64, condition *model.RepoCondition) (string, string, string) {
	// 生成summarySQL的统计项
	aggsSQL := generateAggsSQLForBusinessStatus(condition)
	// 生成select语句
	selectSQL := generateSelectSQLForBusinessStatus(condition)
	// 生成查询条件
	whereSQL := generateWhereSQLForBusinessStatus(condition)
	// 和主档缓存表关联查询
	fromSQL := generateFromSQLForBusinessStatus(condition)
	// 添加排序类型的过滤
	orderSQL := generateOrderSQLForBusinessStatus(condition)
	// 按门店和营业日期分组
	groupSQL := generateGroupSQLForBusinessStatus(condition)
	limitOffsetSQL := generateLimitOffsetSQLForBusinessStatus(condition)

	// 组装总的rowSQL语句，因为要根据bus_data和store_caches.geo0分组统计，所以用了sum
	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount, 
			SUM(net_amount) AS net_amount, 
			SUM(discount_amount) AS discount_amount, 
			SUM(tip) AS tip, 
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee, 
			SUM(service_fee) AS service_fee, 
			SUM(tax_fee) AS tax_fee, 
			SUM(other_fee) AS other_fee, 
			SUM(pay_amount) AS pay_amount, 
			SUM(rounding) AS rounding, 
			SUM(overflow_amount) AS overflow_amount, 
			SUM(change_amount) AS change_amount, 
			SUM(order_count) AS order_count, 
			SUM(product_count) AS product_count, 
			SUM(accessory_count) AS accessory_count,
			SUM(merchant_send_fee) AS merchant_send_fee,
			SUM(send_fee_for_merchant) AS send_fee_for_merchant,
			SUM(discount_merchant_contribute) AS discount_merchant_contribute,
			SUM(pay_merchant_contribute) AS pay_merchant_contribute,
			SUM(merchant_send_fee_returned) AS merchant_send_fee_returned,
			SUM(send_fee_for_merchant_returned) AS send_fee_for_merchant_returned,
			SUM(discount_merchant_contribute_returned) AS discount_merchant_contribute_returned,
			SUM(pay_merchant_contribute_returned) AS pay_merchant_contribute_returned,
			SUM(gross_amount_returned) AS gross_amount_returned, 
			SUM(net_amount_returned) AS net_amount_returned, 
			SUM(discount_amount_returned) AS discount_amount_returned, 
			SUM(tip_returned) AS tip_returned, 
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned, 
			SUM(service_fee_returned) AS service_fee_returned, 
			SUM(tax_fee_returned) AS tax_fee_returned, 
			SUM(other_fee_returned) AS other_fee_returned, 
			SUM(pay_amount_returned) AS pay_amount_returned, 
			SUM(rounding_returned) AS rounding_returned, 
			SUM(overflow_amount_returned) AS overflow_amount_returned, 
			SUM(change_amount_returned) AS change_amount_returned, 
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(amount_0) AS real_amount,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_0_returned) AS real_amount_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(amount_4_returned) AS amount_4_returned
			
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, aggsSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)

	// 查询门店营业日期的sql
	businessDaySQL := generateBusinessDaySql(condition)

	return rowSQL, summarySQL, businessDaySQL
}

func generateBusinessDaySql(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	joinSQL := ""
	if regionIdsSQL != "" {
		joinSQL = fmt.Sprintf(`LEFT JOIN store_caches ON store_caches.id = sales_ticket_amounts.store_id`)
	}
	var businessDaySQL = fmt.Sprintf(`
		SELECT 
		store_id, SUM(CASE WHEN business > 0 THEN 1 ELSE 0 END) as business_days
		FROM (
				SELECT store_id, count(1) AS business
				FROM sales_ticket_amounts
				%s
			WHERE
				bus_date >= '%s'
				AND bus_date <= '%s'
				%s
			GROUP BY bus_date, store_id
			) T
		GROUP BY store_id
	`, joinSQL, condition.Start.Format("2006-01-02"), condition.End.Format("2006-01-02"), regionIdsSQL)
	return businessDaySQL
}

func generateDateSQLForBusinessStatus(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	// 作为周期组
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总,
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

// 根据区域查询条件生成区域分组sql
func generateRegionSQLForBusinessStatus(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateSelectSQLForBusinessStatus(condition *model.RepoCondition) string {
	// 根据查询时间条件生成时间分组语句：按天按周按月
	_, dateSQL := generateDateSQLForBusinessStatus(condition)
	// 根据区域查询条件生成区域分组sql：区域汇总
	_, regionSQL := generateRegionSQLForBusinessStatus(condition)
	// 生成统计项头
	aggsSQL := generateAggsSQLForBusinessStatus(condition)
	var selectSQL string
	// 根据tag标签判断详细/汇总
	if dateSQL != "" {
		selectSQL = fmt.Sprintf(`
		%s,
		%s,
		%s
	`, dateSQL, regionSQL, aggsSQL)
	} else {
		selectSQL = fmt.Sprintf(`
		%s,
		%s
	`, regionSQL, aggsSQL)
	}
	return selectSQL
}

func generateWhereSQLForBusinessStatus(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL)
	return whereSQL
}

// 根据门店类型和开店类型生成sql
func generateStoreOpenWhereSql(condition *model.RepoCondition) string {
	var StoreStatusSql string
	if condition.StoreType != "" {
		StoreStatusSql = fmt.Sprintf(`
		AND store_caches.store_type = %s
	`, condition.StoreType)
	}
	if condition.OpenStatus != "" {
		StoreStatusSql += fmt.Sprintf(`
		AND store_caches.open_status = '%s'
	`, condition.OpenStatus)
	}
	return StoreStatusSql
}

func generateFromSQLForBusinessStatus(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre { // 是否使用预计算，就是是否使用flink计算，如果数据大的话，可以用
		leftJoinSQL = `
			store_sales
				LEFT JOIN store_caches ON store_sales.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
		`
	}
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenWhereSql(condition)
	return leftJoinSQL
}

func generateGroupSQLForBusinessStatus(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForBusinessStatus(condition)
	regionSQL, _ := generateRegionSQLForBusinessStatus(condition)
	var groupSQL string
	if dateSQL != "" {
		groupSQL = fmt.Sprintf(`
		%s,
		%s
	`, dateSQL, regionSQL)
	} else {
		groupSQL = fmt.Sprintf(`
		%s
	`, regionSQL)
	}
	return groupSQL
}

func generateOrderSQLForBusinessStatus(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForBusinessStatus(condition)
	regionSQL, _ := generateRegionSQLForBusinessStatus(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
		%s
	`, dateSQL, regionSQL)
	} else {
		orderSQL = fmt.Sprintf(`
		%s
	`, regionSQL)
	}
	return orderSQL
}

func generateLimitOffsetSQLForBusinessStatus(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
