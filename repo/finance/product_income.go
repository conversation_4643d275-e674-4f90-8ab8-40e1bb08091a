package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

// 财务报表.菜品收入
func (f *FinaRepositoryImpl) ProductIncome(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	// 生成查询语句
	rowSQL, summarySQL := generateQuerySQLForProductIncome(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, f.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)
	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

// 生成sum和sql
func generateAggsSQLForProductIncome(condition *model.RepoCondition) string {
	if condition.IsPre {
		return `
			SUM(gross_amount) AS gross_amount,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(package_fee_returned) AS package_fee_returned,
			SUM(delivery_fee_returned) AS delivery_fee_returned
		`
	}
	return `
			SUM(gross_amount) AS gross_amount,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
			SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
			SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned
		`
}

func generateQuerySQLForProductIncome(trackId int64, condition *model.RepoCondition) (string, string) {
	// 生成summarySQL的统计项
	aggsSQL := generateAggsSQLForProductIncome(condition)
	// 生成select语句
	selectSQL := generateSelectSQLForProductIncome(condition)
	// 生成查询条件
	whereSQL := generateWhereSQLForProductIncome(condition)
	// 和主档缓存表关联查询
	fromSQL := generateFromSQLForProductIncome(condition)
	// 添加排序类型的过滤
	orderSQL := generateOrderSQLForProductIncome(condition)
	// 按门店和营业日期分组
	groupSQL := generateGroupSQLForProductIncome(condition)
	limitOffsetSQL := generateLimitOffsetSQLForProductIncome(condition)

	// 组装总的rowSQL语句，因为要根据bus_data和store_caches.geo0分组统计，所以用了sum
	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)

	// 组装汇总查询sql
	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount, 
			SUM(package_fee) AS package_fee, 
			SUM(delivery_fee) AS delivery_fee, 
			SUM(gross_amount_returned) AS gross_amount_returned, 
			SUM(package_fee_returned) AS package_fee_returned, 
			SUM(delivery_fee_returned) AS delivery_fee_returned
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, aggsSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)

	return rowSQL, summarySQL
}

func generateDateSQLForProductIncome(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	// 作为周期组
	sdateSQL := fmt.Sprintf("TO_CHAR(%s, 'YYYY-MM-DD') AS period_group", dateSQL)
	// 根据tag标签决定使用详细还是汇总,
	if condition.TagType == "SUMMARY" {
		dateSQL = ""
		sdateSQL = ""
	}
	return dateSQL, sdateSQL
}

// 根据区域查询条件生成区域分组sql
func generateRegionSQLForProductIncome(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateSelectSQLForProductIncome(condition *model.RepoCondition) string {
	// 根据查询时间条件生成时间分组语句：按天按周按月
	_, dateSQL := generateDateSQLForProductIncome(condition)
	// 根据区域查询条件生成区域分组sql：区域汇总
	_, regionSQL := generateRegionSQLForProductIncome(condition)
	// 生成统计项头
	aggsSQL := generateAggsSQLForProductIncome(condition)
	var selectSQL string
	// 根据tag标签判断详细/汇总
	if dateSQL != "" {
		selectSQL = fmt.Sprintf(`
		%s,
		%s,
		%s
	`, dateSQL, regionSQL, aggsSQL)
	} else {
		selectSQL = fmt.Sprintf(`
		%s,
		%s
	`, regionSQL, aggsSQL)
	}
	return selectSQL
}

func generateWhereSQLForProductIncome(condition *model.RepoCondition) string {
	// 根据查询区域类型，生成查询语句
	regionIdsSQL := GenerateRegionWhereSQL(condition)
	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL)
	return whereSQL
}

func generateFromSQLForProductIncome(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre { // 是否使用预计算，就是是否使用flink计算，如果数据大的话，可以用
		leftJoinSQL = `
			store_sales
				LEFT JOIN store_caches ON store_sales.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
		`
	}
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenWhereSql(condition)
	return leftJoinSQL
}

func generateGroupSQLForProductIncome(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForProductIncome(condition)
	regionSQL, _ := generateRegionSQLForProductIncome(condition)
	var groupSQL string
	if dateSQL != "" {
		groupSQL = fmt.Sprintf(`
		%s,
		%s
	`, dateSQL, regionSQL)
	} else {
		groupSQL = fmt.Sprintf(`
		%s
	`, regionSQL)
	}
	return groupSQL
}

func generateOrderSQLForProductIncome(condition *model.RepoCondition) string {
	dateSQL, _ := generateDateSQLForProductIncome(condition)
	regionSQL, _ := generateRegionSQLForProductIncome(condition)
	var orderSQL string
	if dateSQL != "" {
		orderSQL = fmt.Sprintf(`
		%s DESC, 
		%s
	`, dateSQL, regionSQL)
	} else {
		orderSQL = fmt.Sprintf(`
		%s
	`, regionSQL)
	}
	return orderSQL
}

func generateLimitOffsetSQLForProductIncome(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
