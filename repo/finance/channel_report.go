package finance

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func (f *FinaRepositoryImpl) ChannelReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	rowSQL, summarySQL := generateQuerSQLForTakeChannel(trackId, condition)
	ch1 := make(chan []interface{}, 1)
	ch2 := make(chan []interface{}, 1)
	go helpers.QueryDBWithChan(trackId, f.DB, rowSQL, ch1)
	go helpers.QueryDBWithChan(trackId, f.DB, summarySQL, ch2)
	rows := <-ch1
	summarys := <-ch2
	close(ch1)
	close(ch2)

	var summary map[string]interface{}
	if len(summarys) > 0 {
		summary = cast.ToStringMap(summarys[0])
	}
	return &model.Response{Rows: rows, Summary: summary}, nil
}

// 将过滤渠道类型去掉，然后按照订单类型分组
func generateQuerSQLForTakeChannel(trackId int64, condition *model.RepoCondition) (string, string) {
	baseSelectSQL := generateBaseSelectSQLForTakeChannel(condition)
	selectSQL := generateSelectSQLForTakeChannel(condition)
	whereSQL := generateWhereSQLForTakeChannel(condition)
	fromSQL := generateFromSQLForTakeChannel(condition)
	groupSQL := generateGroupSQLForTakeChannel(condition)
	orderSQL := generateOrderSQLForTakeChannel(condition)
	limitOffsetSQL := generateLimitOffsetSQLForTakeChannel(condition)

	rowSQL := fmt.Sprintf(`
		SELECT
			%s
		FROM
			%s
		WHERE
			%s
		GROUP BY
			%s
		ORDER BY
			%s
		%s
	`, selectSQL, fromSQL, whereSQL, groupSQL, orderSQL, limitOffsetSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, rowSQL)

	summarySQL := fmt.Sprintf(`
		SELECT
			COUNT(1) AS total,
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(order_count) AS order_count,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(tip_returned) AS tip_returned,
			SUM(package_fee_returned) AS package_fee_returned,
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(service_fee_returned) AS service_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(pay_amount_returned) AS pay_amount_returned,
			SUM(rounding_returned) AS rounding_returned,
			SUM(overflow_amount_returned) AS overflow_amount_returned,
			SUM(change_amount_returned) AS change_amount_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_0_returned) AS amount_0_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(amount_4_returned) AS amount_4_returned
		FROM (
			SELECT
				%s
			FROM
				%s
			WHERE
				%s
			GROUP BY
				%s
		) T
	`, baseSelectSQL, fromSQL, whereSQL, groupSQL)
	logger.Pre().Debugf("[%d] repo.report.SalesRepositoryPG summary. SQL: `%s`", trackId, summarySQL)
	return rowSQL, summarySQL
}

func generateDateSQLForTakeChannel(condition *model.RepoCondition) (string, string) {
	dateSQL := "bus_date"
	switch condition.PeriodGroupType {
	case model.Week:
		dateSQL = "bus_date_week"
	case model.Month:
		dateSQL = "bus_date_month"
	case model.Year:
		dateSQL = "bus_date_year"
	}
	var stateSQL string
	return dateSQL, stateSQL
}

func generateRegionSQLForTakeChannel(condition *model.RepoCondition) (string, string) {
	regionSQL := "store_caches.id"
	switch condition.RegionGroupType {
	case "GEO_REGION": // 地理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.geo%d", condition.RegionGroupLevel)
		}
	case "BRANCH_REGION": // 管理区域
		switch condition.RegionGroupLevel {
		case 0, 1, 2:
			regionSQL = fmt.Sprintf("store_caches.branch%d", condition.RegionGroupLevel)
		}
	}
	sregionSQL := fmt.Sprintf("%s AS region_id", regionSQL)
	return regionSQL, sregionSQL
}

func generateBaseSelectSQLForTakeChannel(condition *model.RepoCondition) string {
	if condition.IsPre {
		return `
			SUM(gross_amount) AS gross_amount,
			SUM(net_amount) AS net_amount,
			SUM(discount_amount) AS discount_amount,
			SUM(tip) AS tip,
			SUM(package_fee) AS package_fee,
			SUM(delivery_fee) AS delivery_fee,
			SUM(service_fee) AS service_fee,
			SUM(tax_fee) AS tax_fee,
			SUM(other_fee) AS other_fee,
			SUM(pay_amount) AS pay_amount,
			SUM(rounding) AS rounding,
			SUM(overflow_amount) AS overflow_amount,
			SUM(change_amount) AS change_amount,
			SUM(order_count) AS order_count,
			SUM(gross_amount_returned) AS gross_amount_returned,
			SUM(net_amount_returned) AS net_amount_returned,
			SUM(discount_amount_returned) AS discount_amount_returned,
			SUM(tip_returned) AS tip_returned,
			SUM(package_fee_returned) AS package_fee_returned,
			SUM(delivery_fee_returned) AS delivery_fee_returned,
			SUM(service_fee_returned) AS service_fee_returned,
			SUM(tax_fee_returned) AS tax_fee_returned,
			SUM(other_fee_returned) AS other_fee_returned,
			SUM(pay_amount_returned) AS pay_amount_returned,
			SUM(rounding_returned) AS rounding_returned,
			SUM(overflow_amount_returned) AS overflow_amount_returned,
			SUM(change_amount_returned) AS change_amount_returned,
			SUM(order_count_returned) AS order_count_returned,
			SUM(commission) AS commission,
			SUM(amount_0) AS amount_0,
			SUM(amount_1) AS amount_1,
			SUM(amount_2) AS amount_2,
			SUM(amount_3) AS amount_3,
			SUM(amount_4) AS amount_4,
			SUM(commission_returned) AS commission_returned,
			SUM(amount_0_returned) AS amount_0_returned,
			SUM(amount_1_returned) AS amount_1_returned,
			SUM(amount_2_returned) AS amount_2_returned,
			SUM(amount_3_returned) AS amount_3_returned,
			SUM(amount_4_returned) AS amount_4_returned
		`
	}
	return `
		SUM(gross_amount) AS gross_amount,
		SUM(net_amount) AS net_amount,
		SUM(discount_amount) AS discount_amount,
		SUM(tip) AS tip,
		SUM(package_fee) AS package_fee,
		SUM(delivery_fee) AS delivery_fee,
		SUM(service_fee) AS service_fee,
		SUM(tax_fee) AS tax_fee,
		SUM(other_fee) AS other_fee,
		SUM(pay_amount) AS pay_amount,
		SUM(rounding) AS rounding,
		SUM(overflow_amount) AS overflow_amount,
		SUM(change_amount) AS change_amount,
		SUM(eticket_count) AS order_count,
		SUM(CASE WHEN refunded THEN gross_amount ELSE 0 END) AS gross_amount_returned,
		SUM(CASE WHEN refunded THEN net_amount ELSE 0 END) AS net_amount_returned,
		SUM(CASE WHEN refunded THEN discount_amount ELSE 0 END) AS discount_amount_returned,
		SUM(CASE WHEN refunded THEN tip ELSE 0 END) AS tip_returned,
		SUM(CASE WHEN refunded THEN package_fee ELSE 0 END) AS package_fee_returned,
		SUM(CASE WHEN refunded THEN delivery_fee ELSE 0 END) AS delivery_fee_returned,
		SUM(CASE WHEN refunded THEN service_fee ELSE 0 END) AS service_fee_returned,
		SUM(CASE WHEN refunded THEN tax_fee ELSE 0 END) AS tax_fee_returned,
		SUM(CASE WHEN refunded THEN other_fee ELSE 0 END) AS other_fee_returned,
		SUM(CASE WHEN refunded THEN pay_amount ELSE 0 END) AS pay_amount_returned,
		SUM(CASE WHEN refunded THEN rounding ELSE 0 END) AS rounding_returned,
		SUM(CASE WHEN refunded THEN overflow_amount ELSE 0 END) AS overflow_amount_returned,
		SUM(CASE WHEN refunded THEN change_amount ELSE 0 END) AS change_amount_returned,
		SUM(CASE WHEN refunded THEN eticket_count ELSE 0 END) AS order_count_returned,
		SUM(commission) AS commission,
		SUM(amount_0) AS amount_0,
		SUM(amount_1) AS amount_1,
		SUM(amount_2) AS amount_2,
		SUM(amount_3) AS amount_3,
		SUM(amount_4) AS amount_4,
		SUM(CASE WHEN refunded THEN commission ELSE 0 END) AS commission_returned,
		SUM(CASE WHEN refunded THEN amount_0 ELSE 0 END) AS amount_0_returned,
		SUM(CASE WHEN refunded THEN amount_1 ELSE 0 END) AS amount_1_returned,
		SUM(CASE WHEN refunded THEN amount_2 ELSE 0 END) AS amount_2_returned,
		SUM(CASE WHEN refunded THEN amount_3 ELSE 0 END) AS amount_3_returned,
		SUM(CASE WHEN refunded THEN amount_4 ELSE 0 END) AS amount_4_returned
	`
}

func generateSelectSQLForTakeChannel(condition *model.RepoCondition) string {
	_, regionSQL := generateRegionSQLForTakeChannel(condition)
	baseSelectSQL := generateBaseSelectSQLForTakeChannel(condition)

	selectSQL := fmt.Sprintf(`
		%s,
		channel_name AS channel_name,
		%s
	`, regionSQL, baseSelectSQL)
	return selectSQL
}

func generateWhereSQLForTakeChannel(condition *model.RepoCondition) string {
	regionIdsSQL := GenerateRegionWhereSQL(condition)

	channelIdSQL := ""
	if condition.ChannelId != 0 {
		channelIdSQL = fmt.Sprintf(
			"AND channel_id = %d",
			condition.ChannelId,
		)
	}
	orderTypeSQL := ""
	if len(condition.OrderType) > 0 {
		orderTypeSQL = fmt.Sprintf(
			"AND order_type = '%s'",
			condition.OrderType,
		)
	}
	whereSQL := fmt.Sprintf(`
		sales_ticket_amounts.partner_id = %d 
		AND scope_id = %d
		AND bus_date >= '%s'
		AND bus_date <= '%s'
		%s
		%s
		%s
	`, condition.PartnerId, condition.ScopeId, condition.Start.Format("2006-01-02"),
		condition.End.Format("2006-01-02"), regionIdsSQL, channelIdSQL, orderTypeSQL)
	return whereSQL
}

func generateFromSQLForTakeChannel(condition *model.RepoCondition) string {
	var leftJoinSQL = ""
	if condition.IsPre {
		leftJoinSQL = `
			store_channel_sales
				LEFT JOIN store_caches ON store_channel_sales.store_id = store_caches.id
		`
	} else {
		leftJoinSQL = `
		sales_ticket_amounts
			LEFT JOIN store_caches ON sales_ticket_amounts.store_id = store_caches.id
		`
	}
	// 增加门店类型和开店类型的过滤
	leftJoinSQL += generateStoreOpenWhereSql(condition)
	return leftJoinSQL
}

// 只过滤订单类型
func generateGroupSQLForTakeChannel(condition *model.RepoCondition) string {
	regionSQL, _ := generateRegionSQLForTakeChannel(condition)
	groupSQL := fmt.Sprintf(`
		%s,
		channel_name
	`, regionSQL)
	return groupSQL
}

func generateOrderSQLForTakeChannel(condition *model.RepoCondition) string {
	regionSQL, _ := generateRegionSQLForTakeChannel(condition)
	orderSQL := fmt.Sprintf(`
		%s
	`, regionSQL)
	return orderSQL
}

func generateLimitOffsetSQLForTakeChannel(condition *model.RepoCondition) string {
	limitOffsetSQL := ""
	if condition.Limit == -1 {
		return limitOffsetSQL
	}
	if condition.Limit < 0 {
		condition.Limit = 10
	}
	if condition.Offset < 0 {
		condition.Offset = 0
	}
	limitOffsetSQL = fmt.Sprintf(
		"LIMIT %d OFFSET %d",
		condition.Limit, condition.Offset,
	)
	return limitOffsetSQL
}
