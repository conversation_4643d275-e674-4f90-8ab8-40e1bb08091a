package finance

import (
	"context"

	"gitlab.hexcloud.cn/histore/sales-report/model"
)

//财务报表接口
type FinRepository interface {
	BusinessStatus(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	LogisticsReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	ChannelReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	NetReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	DiscountReport(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
	ProductIncome(ctx context.Context, condition *model.RepoCondition) (*model.Response, error)
}
