package open

import (
    "context"
    "database/sql"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model/open"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "gitlab.hexcloud.cn/histore/sales-report/repo/common"
    "text/template"
)

// 所在城市、门店经营类型、门店编号、门店名称、营业天数、总营业额、总优惠组成、有效订单数
var openFinancialTpl = common.Create("openFinancialTpl", `
-- openFinancialTpl
select
    to_char(fact.bus_date,'YYYY-MM-DD'),
    fact.store_id,
    max(store_caches.store_type) as store_type,
    1 as business_days,
    coalesce(sum(fact.amount_0), 0) as business_amount,
    coalesce(sum(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount), 0) AS discount_amount,
    coalesce(sum(fact.eticket_count), 0) as valid_ticket_count
from sales_ticket_amounts fact 
    left join store_caches on fact.store_id = store_caches.id 
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
group by fact.bus_date, fact.store_id
order by fact.bus_date desc, fact.store_id
`)

var openFinancialSummaryTpl = common.Create("openFinancialSummaryTpl", `
-- openFinancialSummaryTpl
select
    '' as bus_date,
    fact.store_id,
    max(store_caches.store_type) as store_type,
    count(distinct bus_date) as business_days,
    coalesce(sum(fact.amount_0), 0) as business_amount,
    coalesce(sum(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount), 0) AS discount_amount,
    coalesce(sum(fact.eticket_count), 0) as valid_ticket_count
from sales_ticket_amounts fact 
    left join store_caches on fact.store_id = store_caches.id 
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
group by fact.store_id
order by fact.store_id
`)

func (db *OpenRepositoryPG) OpenFinancialReport(ctx context.Context, req *open.FinancialReportReq) (*open.FinancialReportResp, error) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))

    var tpl *template.Template
    if req.Summary {
        tpl = openFinancialSummaryTpl
    } else {
        tpl = openFinancialTpl
    }
    sql := common.Render(tpl, map[string]string{
        "partnerId": cast.ToString(ctx.Value("partner_id")),
        "startDate": req.StartDate,
        "endDate": req.EndDate,
    })
    logger.Pre().Infof("[%d] open.OpenFinancialReport Sql: `%v` ", trackId, sql)

    rows := executeQueryOpenFinancialReport(trackId, sql, db.DB)
    return &open.FinancialReportResp{
        Rows: rows,
    }, nil;

}

func executeQueryOpenFinancialReport(trackId int64, sql string, db *sql.DB) []*open.FinancialReportStore {
    rows := make([]*open.FinancialReportStore, 0)
    r, err := db.Query(sql)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        return rows
    }
    defer r.Close()
    for r.Next() {
        f := new(open.FinancialReportStore)
        if err := r.Scan(
            &f.BusDate,
            &f.StoreId,
            &f.StoreType,
            &f.BusinessDays,
            &f.BusinessAmount,
            &f.DiscountAmount,
            &f.ValidTicketCount,
        ); err != nil {
            logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
            return rows
        }
        rows = append(rows, f)
    }
    return rows
}
