package open

import (
    "context"
    "gitlab.hexcloud.cn/histore/sales-report/model/open"
)

type OpenRepository interface {
    // OpenFinancialReport 收银汇总数据
    OpenFinancialReport(ctx context.Context, condition *open.FinancialReportReq) (*open.FinancialReportResp, error)
    OpenProductReport(ctx context.Context, req *open.ProductReportReq) (*open.ProductReportResp, error)

    SyncStoreReport(ctx context.Context, condition *open.SyncStoreReportReq) (*open.SyncStoreReportResp, error)

    SyncProductReport(ctx context.Context, condition *open.SyncProductReportReq) (*open.SyncProductReportResp, error)
}
