package open

import (
    "context"
    "database/sql"
    "github.com/spf13/cast"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/model/open"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "gitlab.hexcloud.cn/histore/sales-report/repo/common"
)

var querySyncStoreTpl = common.Create("querySyncStoreTpl", `
-- querySyncStoreTpl
select
    to_char(fact.bus_date,'YYYY-MM-DD') as bus_date,
    fact.store_id,
    fact.channel_id,
    coalesce(sum(fact.amount_0), 0) as business_amount,
    coalesce(sum(fact.discount_merchant_contribute+fact.merchant_send_fee+fact.other_fee+fact.commission+fact.pay_merchant_contribute+fact.rounding-fact.overflow_amount), 0) AS discount_amount,
    sum(fact.eticket_count) as valid_ticket_count,
    coalesce(max(sc.currency), '') as currency
from sales_ticket_amounts fact left join store_caches sc on fact.store_id = sc.id
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
{{if ne .storeId "0"}} and fact.store_id = {{.storeId}} {{end}}
group by fact.bus_date, fact.store_id, fact.channel_id
order by fact.bus_date, fact.store_id, fact.channel_id
`)

func (db *OpenRepositoryPG) SyncStoreReport(ctx context.Context, req *open.SyncStoreReportReq) (*open.SyncStoreReportResp, error) {
    trackId := cast.ToInt64(ctx.Value(config.TrackId))
    sql := common.Render(querySyncStoreTpl, map[string]string{
        "partnerId": cast.ToString(req.PartnerId),
        "startDate": req.StartDate,
        "endDate": req.EndDate,
        "storeId":   cast.ToString(req.StoreId),
    })
    logger.Pre().Infof("[%d] open.SyncStoreReport Sql: `%v` ", trackId, sql)

    rows := executeQuerySyncStoreReport(trackId, sql, db.DB)
    return &open.SyncStoreReportResp{
        Rows: rows,
    }, nil;

}

func executeQuerySyncStoreReport(trackId int64, sql string, db *sql.DB) []*open.SyncStoreReportRow {
    rows := make([]*open.SyncStoreReportRow, 0)
    r, err := db.Query(sql)
    if err != nil {
        logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
        return rows
    }
    defer r.Close()
    for r.Next() {
        f := new(open.SyncStoreReportRow)
        if err := r.Scan(
            &f.BusDate,
            &f.StoreId,
            &f.ChannelId,
            &f.BusinessAmount,
            &f.DiscountAmount,
            &f.ValidTicketCount,
            &f.Currency,
        ); err != nil {
            logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
            return rows
        }
        rows = append(rows, f)
    }
    return rows
}
