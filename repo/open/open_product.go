package open

import (
	"context"
	"database/sql"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo/common"
)

var openProductTpl = common.Create("openProductTpl", `
-- openProductTpl
select
    id,
    to_char(fact.bus_date,'YYYY-MM-DD'),
    fact.store_id,
    fact.channel_id,
    fact.product_id,
    fact.gross_amount,
    fact.net_amount,
    fact.qty,
    coalesce(fact.sku_remark, '{}'),
    to_char(fact.created, 'YYYY-MM-DD HH24:MI:SS'),
    to_char(fact.created, 'YYYY-MM-DD HH24:MI:SS')
from sales_product_amounts fact 
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
{{if ne .storeId "0"}} and fact.store_id = {{.storeId}} {{end}}
{{if ne .productId "0"}} and fact.product_id = {{.productId}} {{end}}
{{if ne .channelId "0"}} and fact.channel_id = {{.channelId}} {{end}}
order by fact.bus_date, fact.id
{{if ne .limit "0"}} limit {{.limit}} {{end}}
{{if ne .offset "0"}} offset {{.offset}} {{end}}
`)

var openProductTotalTpl = common.Create("openProductTotalTpl", `
-- openProductTotalTpl
select
    count(1) as total
from sales_product_amounts fact 
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
{{if ne .storeId "0"}} and fact.store_id = {{.storeId}} {{end}}
{{if ne .productId "0"}} and fact.product_id = {{.productId}} {{end}}
{{if ne .channelId "0"}} and fact.channel_id = {{.channelId}} {{end}}
`)

func (db *OpenRepositoryPG) OpenProductReport(ctx context.Context, req *open.ProductReportReq) (*open.ProductReportResp, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	if req.Limit == 0 {
		req.Limit = 10000
	}
	sql := common.Render(openProductTpl, map[string]string{
		"partnerId": cast.ToString(ctx.Value("partner_id")),
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"storeId":   cast.ToString(req.StoreId),
		"productId": cast.ToString(req.ProductId),
		"channelId": cast.ToString(req.ChannelId),
		"limit":     cast.ToString(req.Limit),
		"offset":    cast.ToString(req.Offset),
	})

	totalSql := common.Render(openProductTotalTpl, map[string]string{
		"partnerId": cast.ToString(ctx.Value("partner_id")),
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"storeId":   cast.ToString(req.StoreId),
		"productId": cast.ToString(req.ProductId),
		"channelId": cast.ToString(req.ChannelId),
		"limit":     cast.ToString(req.Limit),
		"offset":    cast.ToString(req.Offset),
	})
	logger.Pre().Infof("[%d] open.OpenProductReport Sql: `%v` ", trackId, sql)
	rowsCh := make(chan []*open.ProductReportRow, 1)
	totalCh := make(chan int32, 1)
	defer close(rowsCh)
	defer close(totalCh)
	go executeQueryOpenProductReport(trackId, sql, db.DB, rowsCh)
	if req.Total {
		go executeQueryOpenProductTotal(trackId, totalSql, db.DB, totalCh)
	} else {
		totalCh <- 0
	}
	rows := <-rowsCh
	total := <-totalCh
	return &open.ProductReportResp{
		Rows:  rows,
		Total: total,
	}, nil

}

func executeQueryOpenProductReport(trackId int64, sql string, db *sql.DB, ch chan []*open.ProductReportRow) {
	rows := make([]*open.ProductReportRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- rows
		return
	}
	defer r.Close()
	for r.Next() {
		f := new(open.ProductReportRow)
		if err := r.Scan(
			&f.Id,
			&f.BusDate,
			&f.StoreId,
			&f.ChannelId,
			&f.ProductId,
			&f.GrossAmount,
			&f.NetAmount,
			&f.Qty,
			&f.SkuRemark,
			&f.Created,
			&f.Updated,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- rows
			return
		}
		rows = append(rows, f)
	}
	ch <- rows
	return
}

func executeQueryOpenProductTotal(trackId int64, sql string, db *sql.DB, ch chan int32) {
	var total int32
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		ch <- total
		return
	}
	defer r.Close()
	for r.Next() {
		if err := r.Scan(
			&total,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			ch <- total
			return
		}
	}
	ch <- total
	return
}
