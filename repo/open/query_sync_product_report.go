package open

import (
	"context"
	"database/sql"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model/open"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo/common"
)

var querySyncProductTpl = common.Create("querySyncProductTpl", `
-- querySyncProductTpl
select
    to_char(fact.bus_date,'YYYY-MM-DD') as bus_date,
    fact.store_id,
    fact.channel_id,
	fact.product_id,
    coalesce(sum(fact.gross_amount), 0) as gross_amount,
	coalesce(sum(fact.net_amount), 0) as real_amount,
    coalesce(sum(fact.qty), 0) as qty,
	coalesce(max(sc.currency), '') as currency
from sales_product_amounts fact left join store_caches sc on fact.store_id = sc.id
where fact.partner_id = {{.partnerId}} and fact.bus_date >= '{{.startDate}}' and bus_date <= '{{.endDate}}'
    and fact.combo_type in (0, 2)
{{if ne .storeId "0"}} and fact.store_id = {{.storeId}} {{end}}
group by fact.bus_date, fact.store_id, fact.channel_id, fact.product_id
order by fact.bus_date, fact.store_id, fact.channel_id, fact.product_id
`)

func (db *OpenRepositoryPG) SyncProductReport(ctx context.Context, req *open.SyncProductReportReq) (*open.SyncProductReportResp, error) {
	trackId := cast.ToInt64(ctx.Value(config.TrackId))
	sql := common.Render(querySyncProductTpl, map[string]string{
		"partnerId": cast.ToString(req.PartnerId),
		"startDate": req.StartDate,
		"endDate":   req.EndDate,
		"storeId":   cast.ToString(req.StoreId),
	})
	logger.Pre().Infof("[%d] open.SyncProductReport Sql: `%v` ", trackId, sql)

	rows := executeQuerySyncProductReport(trackId, sql, db.DB)
	return &open.SyncProductReportResp{
		Rows: rows,
	}, nil

}

func executeQuerySyncProductReport(trackId int64, sql string, db *sql.DB) []*open.SyncProductReportRow {
	rows := make([]*open.SyncProductReportRow, 0)
	r, err := db.Query(sql)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return rows
	}
	defer r.Close()
	for r.Next() {
		f := new(open.SyncProductReportRow)
		if err := r.Scan(
			&f.BusDate,
			&f.StoreId,
			&f.ChannelId,
			&f.ProductId,
			&f.GrossAmount,
			&f.RealAmount,
			&f.Qty,
			&f.Currency,
		); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return rows
		}
		rows = append(rows, f)
	}
	return rows
}
