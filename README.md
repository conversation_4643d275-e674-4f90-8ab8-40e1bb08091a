# sales-report

小票销售报表。

### 启动命令
```
go run main.go base
```

### 帮助命令
```
go run main.go -h
```

### 刷新主档
```bash
curl -d '{"partner_id":"4183192445833445399", "user_id": "4371272018445729794", "scope_id": "0"}' http://nsqd:4151/pub?topic=report_metadata_cache
```

### 定时任务
```
https://hipos.hexcloud.cn/chronos.html
code: 0000001
data: {"partner_ids":["100","110","200","201","206","211","221","231","236", "241","251","256","261","266"], "user_id": "104", "scope_id": "0"}
```
{"partner_ids":["100","110","200","201","206","211","221","231","236", "241","251","256","261","266","292","311","336","366","381"], "user_id": "104", "scope_id": "0"}
curl -d '{"partner_ids":["100","110","200","201","206","211","221","231","236", "241","251","256","261","266","292","311","336","366","381","392"], "user_id": "104", "scope_id": "0"}' http://nsqd:4151/pub?topic=report_metadata_cache