// Auto generate code. Help should mail to ['<EMAIL>']
package config

import (
	"fmt"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/spf13/viper"
)

const (
	SuccessCode                = 0
	ErrorCode                  = 1
	WarningCode                = 2
	DateFormat                 = "2006-01-02"
	DateTimeFormat             = "2006-01-02 15:04:05"
	TrackId                    = "track-id"
	WaitCheckTopic             = "tanzai_ticket_check"
	ResolveTicketTopic         = "tanzai_ticket_resolve"
	ResolveAbnormalTicketTopic = "tanzai_abnormal_ticket_resolve"
	DiscountApportionTopic     = "tanzai_discount_apportion"
)

var (
	MaxYearDate       = time.Date(2099, time.January, 1, 0, 0, 0, 0, time.UTC)
	BeijinZone        = time.FixedZone("CST", 3600*8)
	ShanghaiLocation  = time.FixedZone("Asia/Shanghai", 8*60*60)
	AllOrderTypes     = []string{"DINEIN", "TAKEAWAY", "SELFHELP", "TAKEOUT"}
	AllOrderTypeNames = []string{"堂食", "外带", "自提", "外卖"}
	OrderTypeNameMap  = map[string]string{
		"DINEIN":   "堂食",
		"TAKEAWAY": "外带",
		"SELFHELP": "自提",
		"TAKEOUT":  "外卖",
	}
	OrderTypeMap = map[string]string{
		"堂食": "DINEIN",
		"外带": "TAKEAWAY",
		"自提": "SELFHELP",
		"外卖": "TAKEOUT",
	}
	MealSegmentSort = []string{"早餐", "中餐", "午餐", "下午茶", "晚餐", "夜宵"}
)

var (
	DefaultConfig               Config
	BohEnabled                  map[int64]bool = make(map[int64]bool)
	BohDisabled                 map[int64]bool = make(map[int64]bool)
	EnableIncludeRealAmountZero map[int64]bool = make(map[int64]bool)
	SpicyCode                   string
	SpicyMap                    map[string]string = make(map[string]string)
	SpicyValues                 []string
	PartnerConfs                map[int64]*PartnerConf = make(map[int64]*PartnerConf)
)

type PartnerConf struct {
	MealSegments   []string               `json:"meal_segments"`
	Channels       []PartnerChannel       `json:"channels"`
	CouponChannels []PartnerCouponChannel `json:"coupon_channels"`
}

type PartnerChannel struct {
	ChannelId   int64  `json:"channel_id"`
	ChannelName string `json:"channel_name"`
}

type PartnerCouponChannel struct {
	ChannelCode string `json:"channel_code"`
	ChannelName string `json:"channel_name"`
}

func UpdateMealSegments(m map[int64]map[string]struct{}) {
	for pid, pm := range m {
		pc, ok := PartnerConfs[pid]
		if !ok {
			pc = &PartnerConf{}
			PartnerConfs[pid] = pc
		}
		pc.MealSegments = make([]string, 0, len(pm))
		for k, _ := range pm {
			pc.MealSegments = append(pc.MealSegments, k)
		}
		sort.Strings(pc.MealSegments)
	}
	fmt.Printf("UpdateMealSegments Config: %v\n", m)
}

func UpdateChannels(m map[int64][]PartnerChannel) {
	for pid, pm := range m {
		pc, ok := PartnerConfs[pid]
		if !ok {
			pc = &PartnerConf{}
			PartnerConfs[pid] = pc
		}
		pc.Channels = pm
	}
	fmt.Printf("UpdateChannels Config: %v\n", m)
}

func UpdateCouponChannels(m map[int64][]PartnerCouponChannel) {
	for pid, pm := range m {
		pc, ok := PartnerConfs[pid]
		if !ok {
			pc = &PartnerConf{}
			PartnerConfs[pid] = pc
		}
		pc.CouponChannels = pm
	}
	fmt.Printf("UpdateCouponChannels Config: %v\n", m)
}

type Config struct {
	Logger struct {
		Level string
		Env   string
	}
	Grpc struct {
		Port           string
		Proxy          string
		Maxsendmsgsize int
	}
	Http struct {
		Prefix string
		Port   int
	}
	Mq struct {
		Channel         string
		Metadatachannel string
		Concurrency     int
		MaxInFlight     int
	}
	ConnStr struct {
		OAuth    string
		Uuid     string
		Mq       string
		Metadata string
		Irons    string
		Ironsmq  string
		Oms      string
		Supply   string
		Basics   string
		Cam      string
	}
	Trace TraceConfig
	Oauth struct {
		Enable     bool
		Host       string
		Timeout    int
		ThirdParty string
	}
	HexAuth struct {
		Address string
	}
	ProdName struct {
		Name string
	}
	Xxl struct {
		Disabled    bool
		RegistryKey string
		Addr        string
		Port        string
		Token       string
	}
	RocketMq RocketMQ
	DB       struct {
		UseHolo bool
		Pg      DBConfig
		Holo    DBConfig
		Linlee  DBConfig
	}
	Apollo     Apollo
	TableOrder struct {
		Timeout      int
		ApiTableList string
	}
	DownloadTopic     string
	EnabledCompensate int
}

type RocketMQ struct {
	Endpoint   string
	Accesskey  string
	SecretKey  string
	InstanceId string
	Consumers  []RMQConsumerGroup
	Producer   RMQProducer
}

type RMQConsumerGroup struct {
	Topic       string
	Expr        string
	GroupId     string
	Concurrence int
}
type RMQProducer struct {
	GroupId string
	Retry   int
}

type DBConfig struct {
	Url         string
	MaxOpenConn int
	MaxIdleConn int
}

type Apollo struct {
	AppID          string
	Cluster        string
	Url            string
	NamespaceName  string
	IsBackupConfig bool
	Secret         string
}

func InitConfig() *Config {
	// load config file
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(os.Getenv("CONFIG_PATH"))
	viper.AddConfigPath("config")
	viper.AddConfigPath(".")

	err := viper.ReadInConfig()
	if err != nil {
		panic(err)
	}
	// bind config value from env
	viper.SetEnvPrefix("sales-report")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	if err := viper.Unmarshal(&DefaultConfig); err != nil {
		fmt.Println(err)
		panic("Init config failed.")
	}
	if DefaultConfig.Oauth.Timeout == 0 {
		DefaultConfig.Oauth.Timeout = 5
	}
	if DefaultConfig.Mq.Concurrency == 0 {
		DefaultConfig.Mq.Concurrency = 10
	}
	if DefaultConfig.Mq.MaxInFlight == 0 {
		DefaultConfig.Mq.MaxInFlight = 10
	}
	fmt.Println("Use config:", viper.ConfigFileUsed())
	fmt.Printf("Final Config: %+v\n", DefaultConfig)
	return &DefaultConfig
}

func GetConfig() *Config {
	return &DefaultConfig
}

type TraceConfig struct {
	Address                   string
	ServiceName               string
	SimpleType                string
	SimpleValue               interface{}
	ReportQueueSize           int
	ReportBufferFlushInterval int
}
