# Auto generate code. Help should mail to ['<EMAIL>']
logger:
    level: debug
    env: dev
grpc:
    port: 5002
    proxy: 8080
    maxsendmsgsize: 10
http:
    port: 8082
    prefix: sales-report
db:
    useholo: true
    pg:
        url: postgresql://omni_pos_test:<EMAIL>:1921/saas_qa_pos_sales_report
        maxOpenConn: 30
        maxIdleConn: 20
    holo:
        url: postgresql://BASIC$saas_report_dml_dev:saas$<EMAIL>:80/bi_saas_dev
        maxOpenConn: 50
        maxIdleConn: 10
connstr:
    # omni_dev
#    db: postgresql://seesaw_boh_dev:<EMAIL>:1433/seesaw_boh_dev_report
    # saas_dev
#    db: postgresql://seesaw_boh_test:<EMAIL>:1433/saas_pos_sales_report_dev
    # saas_qa
    dbinsert: postgresql://omni_pos_test:<EMAIL>:5432/saas_qa_pos_sales_report
    db: postgresql://omni_pos_test:<EMAIL>:1921/saas_qa_pos_sales_report
#     saas_pro
#    db: postgresql://saas:<EMAIL>:1921/saas_pos_sales_report
#    db: postgresql://saas:<EMAIL>:1921/saas_pos_sales_report
    oauth: oauth:9002
    uuid: uuid:6171
    mq: 127.0.0.1:4150
    ironsmq: localhost:4150

#    mq: ************:4150
    metadata: ************:32440
#    metadata: *************:5012
    irons: localhost:5001
    oms: localhost:8089
mq:
    channel: sales-report
    metadatachannel: METADATA_ENTITY_UPDATED_REPORT
oauth:
    enable: true
    host: http://localhost:8088
hexauth:
    address: "************:32676"
prodname:
    name: "sales-report-default"
xxl:
    disabled: false
    registryKey: sales-report
    addr: https://xxljob-qa.hexcloud.cn/xxl-job-admin
    port: 9999
    token: default_token
trace:
    ServiceName: "sales_report_qa"
    Address: "http://tracing-analysis-dc-sh.aliyuncs.com/adapt_fa4dixclnb@450d6e1708b1d65_fa4dixclnb@53df7ad2afe8301/api/traces"
    SimpleType: "const"
    SimpleValue: true
    ReportQueueSize: 100
