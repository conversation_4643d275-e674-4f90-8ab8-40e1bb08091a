wdy-gen:
	 @ protoc -I. --proto_path=${GOPATH}/src/lib/googleapis  --go_out ./ --go_opt paths=source_relative --go-grpc_out ./ --go-grpc_opt paths=source_relative    grpc/proto/sales_report.proto

	 @echo "generate pb success"
	 @protoc --proto_path=. \
					--proto_path=${GOPATH}/src/lib/googleapis \
							--proto_path=${GOPATH}/src \
							 --proto_path=.\
					--openapiv2_out . \
					--openapiv2_opt Mexample.proto=. \
					--openapiv2_opt logtostderr=true \
					--openapiv2_opt json_names_for_fields=false \
					 grpc/proto/sales_report.proto
	 @echo "generate Swagger success"

gen:
	 @ protoc -I. --proto_path=${GOPATH}/src/github.com/googleapis  --go_out ./ --go_opt paths=source_relative --go-grpc_out ./ --go-grpc_opt paths=source_relative    grpc/proto/sales_report.proto

	 @echo "generate pb success"
	 @protoc --proto_path=. \
					--proto_path=${GOPATH}/src/github.com/googleapis \
							--proto_path=${GOPATH}/src \
							 --proto_path=.\
					--openapiv2_out . \
					--openapiv2_opt Mexample.proto=. \
					--openapiv2_opt logtostderr=true \
					--openapiv2_opt json_names_for_fields=false \
					 grpc/proto/sales_report.proto
	 @echo "generate Swagger success"



