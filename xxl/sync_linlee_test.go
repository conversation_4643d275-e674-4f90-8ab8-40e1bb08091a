package xxl

import (
	"context"
	"github.com/xxl-job/xxl-job-executor-go"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/db"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/repo/open"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"testing"
)

func TestSyncLinlee(t *testing.T) {
	logger.InitLogger("info", "prod")
	uuid.InitDefaultRollBackIdGenerator(&uuid.RollBackOption{
	})
	config.DefaultConfig.DB.Linlee.Url = "linlee:Linlee100%BI@tcp(159.75.225.81:3306)/hekuo?charset=utf8mb4&parseTime=true"
	config.DefaultConfig.DB.Linlee.MaxOpenConn = 1
	config.DefaultConfig.DB.Linlee.MaxIdleConn = 1
	_, dbObj, _ := db.CreateDb(&config.DBConfig{
		Url:         "**********************************************************************************************************************/bi_saas",
		MaxOpenConn: 1,
		MaxIdleConn: 1,
	})
	repo.DefaultOpenRepository = &open.OpenRepositoryPG{DB: dbObj}
	SyncLinlee(context.Background(), &xxl.RunReq{
		ExecutorParams: `{"partner_id": 676, "user_id": 1, "store_id": 123, "days_ago": 1, "start": "2023-11-01", "end": "2023-11-01"}`,
	})
}
