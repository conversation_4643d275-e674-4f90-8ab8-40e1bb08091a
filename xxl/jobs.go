package xxl

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"github.com/xxl-job/xxl-job-executor-go"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/cam"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/table_order"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"gitlab.hexcloud.cn/histore/sales-report/server"
	"gitlab.hexcloud.cn/histore/sales-report/services/open"
	"hexcloud.cn/histore/hex-pkg/uuid"
	"time"
)

func SalesTicketCompensation(ctx context.Context, param *xxl.RunReq) (msg string) {
	logger.Pre().Infof("开始扫描定时任务, param: %v\n", param)

	ctx, cancel := context.WithTimeout(ctx, 20*time.Second)
	defer cancel()

	var cnt int64
	tracing.ModuleTracing(ctx, "task.SalesTicketCompensation", func(ctx2 context.Context) error {
		cnt = server.SalesTicketCompensation(ctx2)
		tracing.SetSpanTag(ctx, "count", cast.ToString(cnt))
		return nil
	})

	logger.Pre().Infof("本次扫描数量: %v\n", cnt)

	return fmt.Sprintf("本次扫描数量: %v", cnt)
}

func AbnormalTicketCompensation(ctx context.Context, param *xxl.RunReq) (msg string) {
	logger.Pre().Infof("开始扫描异常小票定时任务, param: %v\n", param)

	ctx, cancel := context.WithTimeout(ctx, 20*time.Second)

	defer cancel()
	var cnt int64
	tracing.ModuleTracing(ctx, "task.SalesTicketCompensation", func(ctx2 context.Context) error {
		cnt = server.AbnormalTicketCompensation(ctx2)
		tracing.SetSpanTag(ctx, "count", cast.ToString(cnt))
		return nil
	})

	logger.Pre().Infof("本次扫描异常小票数量: %v\n", cnt)

	return fmt.Sprintf("本次扫描异常小票数量: %v", cnt)
}

func SyncLinlee(ctx context.Context, param *xxl.RunReq) (msg string) {
	logger.Pre().Infof("开始同步linlee, param: %v\n", param)
	var info struct {
		PartnerId uint64 `json:"partner_id"`
		UserId    uint64 `json:"user_id"`
		StoreId   int64  `json:"store_id"`
		DaysAgo   int32  `json:"days_ago"`
		Start     string `json:"start"`
		End       string `json:"end"`
	}
	if param.ExecutorParams == "" {
		logger.Pre().Error("同步linlee任务参数为空")
		return "同步linlee任务参数为空, 任务执行失败"
	}
	if err := json.Unmarshal([]byte(param.ExecutorParams), &info); err != nil {
		logger.Pre().Error("解析同步linlee任务参数出错:", err)
		return fmt.Sprintf("解析同步linlee任务参数出错, 任务执行失败: %v", param.ExecutorParams)
	}
	if info.PartnerId == 0 || info.UserId == 0 {
		logger.Pre().Error("同步linlee任务参数错误")
		return "同步linlee任务参数错误, 任务执行失败"
	}
	if info.Start == "" || info.End == "" {
		// 获取前DaysAgo天日期, yyyy-mm-dd
		info.Start = time.Now().Add(-time.Duration(info.DaysAgo) * time.Hour * 24).In(config.ShanghaiLocation).Format("2006-01-02")
		info.End = info.Start
	}
	logger.Pre().Infof("同步linlee参数: %+v\n", info)
	ctx = context.WithValue(ctx, config.TrackId, uuid.GetId())
	ctx = context.WithValue(ctx, oauth.UserInfo, &oauth.UserAccount{
		PartnerID: info.PartnerId,
		ID:        info.UserId,
	})
	ctx = context.WithValue(ctx, oauth.PartnerID, fmt.Sprintf("%d", info.PartnerId))
	ctx = context.WithValue(ctx, oauth.UserID, fmt.Sprintf("%d", info.UserId))
	err := open.Sync(ctx, info.Start, info.End, info.StoreId)
	if err != nil {
		logger.Pre().Infof("同步linlee失败, err: %v\n", err)
		return err.Error()
	}
	logger.Pre().Infof("同步linlee成功\n")
	return "同步linlee成功"
}
func MetadataCache(ctx context.Context, param *xxl.RunReq) (msg string) {
	tracing.ModuleTracing(ctx, "task.MetadataCache", func(ctx2 context.Context) error {
		msg = metadataCache(ctx, param)
		return nil
	})
	return
}

func metadataCache(ctx context.Context, param *xxl.RunReq) (msg string) {
	var info struct {
		PartnerIDs []string `json:"partner_ids"`
		ScopeID    string   `json:"scope_id"`
		UserID     string   `json:"user_id"`
	}
	if err := json.Unmarshal([]byte(param.ExecutorParams), &info); err != nil {
		logger.Pre().Error("拆解主档缓存任务结构体出错:", err)
		msg += fmt.Sprintf("拆解主档缓存任务结构体出错: %v", param.ExecutorParams)
		return msg
	} else {
		logger.Pre().Infof("主档信息: %v", info)
	}
	partnerIds := make([]int64, len(info.PartnerIDs))
	for i, _ := range info.PartnerIDs {
		partnerIds[i] = cast.ToInt64(info.PartnerIDs[i])
	}

	// 调用主档-门店，设置超时5min
	ctxStore, cancelStore := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancelStore()
	if err := entity.GetAllStoreInfo(ctxStore, info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存主档门店数据出错:", err)
		msg += fmt.Sprintf("缓存主档门店数据出错: %v", err)
	}

	// 调用主档-商品，设置超时5min
	ctxProduct, cancelProduct := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancelProduct()
	if err := entity.GetAllProductInfo(ctxProduct, info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存主档商品数据出错:", err)
		msg += fmt.Sprintf("缓存主档商品数据出错: %v", err)
	}

	// 调用主档-渠道，设置超时1min
	ctxChannel, cancelChannel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancelChannel()
	if err := entity.GetAllChannelInfo(ctxChannel, info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存渠道数据出错:", err)
		msg += fmt.Sprintf("缓存渠道数据出错: %v", err)
	}

	// 更新内存map，无io操作，不限制时间
	if err := cam.UpdateCouponChannels(context.Background(), info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存卡券渠道数据出错:", err)
		msg += fmt.Sprintf("缓存卡券渠道数据出错: %v", err)
	}

	// 查DB，不限制时间
	g, err := queryStoreOpenDate(context.Background(), partnerIds)
	if err != nil {
		logger.Pre().Error("查询门店开业时间数据出错:", err)
		msg += fmt.Sprintf("查询门店开业时间数据出错: %v", err)
	}

	// 更新DB，不限制时间
	if err := repo.DefaultMetadataCache.UpdateStoreOpenDate(context.Background(), g); err != nil {
		logger.Pre().Error("更新门店开业时间数据出错:", err)
		msg += fmt.Sprintf("更新门店开业时间数据出错: %v", err)
	}

	// 更新DB，不限制时间
	if err := table_order.UpdateAllTableInfos(context.Background(), info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存主档桌位数据出错:", err)
		msg += fmt.Sprintf("缓存主档桌位数据出错: %v", err)
	}

	// 调用主档-支付渠道，设置超时1min
	ctxPaymentChannelInfo, cancelPaymentChannelInfo := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancelPaymentChannelInfo()
	if err := entity.GetAllPaymentChannelInfo(ctxPaymentChannelInfo, info.PartnerIDs, info.ScopeID, info.UserID); err != nil {
		logger.Pre().Error("缓存支付渠道数据出错:", err)
		msg += fmt.Sprintf("缓存支付渠道数据出错: %v", err)
	}

	return fmt.Sprintf("arg=[%v], msg=[%v]", info, msg)
}

func queryStoreOpenDate(ctx context.Context, partnerIds []int64) (map[string][]int64, error) {
	// 查询租户下没有开业时间的门店
	resp, err := repo.DefaultSalesRepository.StoreOpenDate(ctx, partnerIds)
	if err != nil {
		return nil, err
	}
	if len(resp.Rows) == 0 {
		return nil, nil
	}
	g := make(map[string][]int64)
	for _, row := range resp.Rows {
		if _, ok := g[row.OpenDate]; !ok {
			g[row.OpenDate] = make([]int64, 0)
		}
		g[row.OpenDate] = append(g[row.OpenDate], row.StoreId)
	}
	return g, nil
}
