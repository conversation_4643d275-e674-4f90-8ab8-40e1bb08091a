package xxl

import (
	"github.com/sirupsen/logrus"
	"github.com/xxl-job/xxl-job-executor-go"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func InitXxl() {
	if config.DefaultConfig.Xxl.Disabled {
		logger.Pre().Infof("未启用 xxl-job!\n")
		return
	}
	exec := xxl.NewExecutor(
		xxl.ServerAddr(config.DefaultConfig.Xxl.Addr),
		xxl.AccessToken(config.DefaultConfig.Xxl.Token), //请求令牌(默认为空)
		//xxl.ExecutorIp("127.0.0.1"),    //可自动获取
		xxl.ExecutorPort(config.DefaultConfig.Xxl.Port),       //默认9999（非必填）
		xxl.RegistryKey(config.DefaultConfig.Xxl.RegistryKey), //执行器名称
		xxl.SetLogger(&XxlLogger{log: logger.Pre()}),          //自定义日志
	)
	exec.Init()
	//设置日志查看handler
	exec.LogHandler(func(req *xxl.LogReq) *xxl.LogRes {
		return &xxl.LogRes{Code: xxl.SuccessCode, Msg: "", Content: xxl.LogResContent{
			FromLineNum: req.FromLineNum,
			ToLineNum:   2,
			LogContent:  "这个是自定义日志handler",
			IsEnd:       true,
		}}
	})
	//注册任务handler
	exec.RegTask("compensation.salesticket", SalesTicketCompensation)
	exec.RegTask("compensation.abnormalticket", AbnormalTicketCompensation)
	exec.RegTask("metadatacache", MetadataCache)
	exec.RegTask("sync_linlee", SyncLinlee)
	logger.Pre().Fatal(exec.Run())
}

type XxlLogger struct {
	log *logrus.Entry
}

func (l *XxlLogger) Info(format string, a ...interface{}) {
	l.log.Infof("xxl-job => "+format, a)
}

func (l *XxlLogger) Error(format string, a ...interface{}) {
	l.log.Errorf("xxl-job => "+format, a)
}
