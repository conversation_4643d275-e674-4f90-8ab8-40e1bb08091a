-- saas销售报表pg库

-- 添加餐段字段（MealSegmentName）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN meal_segment_name VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.meal_segment_name IS '餐段';

-- 添加用餐人数字段（People）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN people NUMERIC(38,16);
COMMENT ON COLUMN sales_ticket_amounts.people IS '用餐人数';

-- 添加门店标签字段（StoreTags）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN store_tags text[];
COMMENT ON COLUMN sales_ticket_amounts.store_tags IS '门店标签';

-- 添加门店标签名称字段（StoreTagNames）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN store_tag_names text[];
COMMENT ON COLUMN sales_ticket_amounts.store_tag_names IS '门店标签';

-- 添加卡券渠道编码字段（CouponChannelCode）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN coupon_channel_code VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.coupon_channel_code IS '卡券渠道编码';

-- 添加卡券渠道名称字段（CouponChannelName）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN coupon_channel_name VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.coupon_channel_name IS '卡券渠道名称';

-- 添加区域名称字段（ZoneName）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN zone_name VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.zone_name IS '区域名称';

-- 添加桌号字段（TableNo）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN table_no VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.table_no IS '桌号';

-- 添加点餐人字段（OpenTableBy）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN open_table_by VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.open_table_by IS '点餐人';

-- 添加收银员字段（OperatorName）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN operator_name VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.operator_name IS '收银员';

-- 添加开台时间字段（OpenTableAt）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN open_table_at TIMESTAMP;
COMMENT ON COLUMN sales_ticket_amounts.open_table_at IS '开台时间';

-- 添加结账时间字段（EndTime）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN end_time TIMESTAMP;
COMMENT ON COLUMN sales_ticket_amounts.end_time IS '结账时间';

-- 添加交易号字段（TicketNo）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN ticket_no VARCHAR(255);
COMMENT ON COLUMN sales_ticket_amounts.ticket_no IS '交易号，根据业务规则生成';

-- 添加碗数字段（BowlNum）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN bowl_num NUMERIC(38,16);
COMMENT ON COLUMN sales_ticket_amounts.bowl_num IS '碗数';

-- 添加消费时长字段（ConsumptionTime）
ALTER TABLE sales_ticket_amounts
    ADD COLUMN consumption_time NUMERIC(38,16);
COMMENT ON COLUMN sales_ticket_amounts.consumption_time IS '消费时长';

/*
	CreateTime			   time.Time `gorm:"type:timestamp;not null" json:"create_time"`                   // 创建时间
	BowlNum                int32     `json:"bowl_num"`
*/


-- 添加加菜时间
ALTER TABLE sales_product_amounts
    ADD COLUMN create_time timestamp;
COMMENT ON COLUMN sales_product_amounts.create_time IS '加菜时间';

-- 添加碗数字段（BowlNum）
ALTER TABLE sales_product_amounts
    ADD COLUMN bowl_num NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.bowl_num IS '碗数';

-- 添加餐段字段（MealSegmentName）
ALTER TABLE sales_product_amounts
    ADD COLUMN meal_segment_name VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.meal_segment_name IS '餐段';

-- 添加卡券渠道编码字段（CouponChannelCode）
ALTER TABLE sales_product_amounts
    ADD COLUMN coupon_channel_code VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.coupon_channel_code IS '卡券渠道编码';

-- 添加卡券渠道名称字段（CouponChannelName）
ALTER TABLE sales_product_amounts
    ADD COLUMN coupon_channel_name VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.coupon_channel_name IS '卡券渠道名称';

-- 添加区域名称字段（ZoneName）
ALTER TABLE sales_product_amounts
    ADD COLUMN zone_name VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.zone_name IS '区域名称';

-- 添加桌号字段（TableNo）
ALTER TABLE sales_product_amounts
    ADD COLUMN table_no VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.table_no IS '桌号';

-- 添加点餐人字段（OpenTableBy）
ALTER TABLE sales_product_amounts
    ADD COLUMN open_table_by VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.open_table_by IS '点餐人';

-- 添加收银员字段（OperatorName）
ALTER TABLE sales_product_amounts
    ADD COLUMN operator_name VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.operator_name IS '收银员';

-- 添加开台时间字段（OpenTableAt）
ALTER TABLE sales_product_amounts
    ADD COLUMN open_table_at TIMESTAMP;
COMMENT ON COLUMN sales_product_amounts.open_table_at IS '开台时间';

-- 添加结账时间字段（EndTime）
ALTER TABLE sales_product_amounts
    ADD COLUMN end_time TIMESTAMP;
COMMENT ON COLUMN sales_product_amounts.end_time IS '结账时间';

-- 添加交易号字段（TicketNo）
ALTER TABLE sales_product_amounts
    ADD COLUMN ticket_no VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.ticket_no IS '交易号';


-- 添加门店标签字段（StoreTags）
ALTER TABLE sales_product_amounts
    ADD COLUMN store_tags text[];
COMMENT ON COLUMN sales_product_amounts.store_tags IS '门店标签';

-- 添加门店标签名称字段（StoreTagNames）
ALTER TABLE sales_product_amounts
    ADD COLUMN store_tag_names text[];
COMMENT ON COLUMN sales_product_amounts.store_tag_names IS '门店标签';


ALTER TABLE sales_product_amounts
    ADD COLUMN remark VARCHAR(255);
COMMENT ON COLUMN sales_product_amounts.remark IS '商品备注';


ALTER TABLE sales_product_amounts
    ADD COLUMN combo_product_id bigint;
COMMENT ON COLUMN sales_product_amounts.combo_product_id IS '套餐头商品id';

ALTER TABLE sales_product_amounts
    ADD COLUMN products_real_amount NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.products_real_amount IS '订单维度财务实收的分摊';

ALTER TABLE sales_product_amounts
    ADD COLUMN acc_count NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.acc_count IS '小料数量';

ALTER TABLE sales_product_amounts
    ADD COLUMN product_acc_count NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.product_acc_count IS '配料数';

ALTER TABLE sales_product_amounts
    ADD COLUMN merchant_discount_amount NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.merchant_discount_amount IS '商家折扣承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN gross_amount2 NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.gross_amount2 IS '分摊后商品原价';

ALTER TABLE sales_product_amounts
    ADD COLUMN package_amount NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.package_amount IS '包装费';

ALTER TABLE sales_product_amounts
    ADD COLUMN service_fee NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.service_fee IS '服务费';

ALTER TABLE sales_product_amounts
    ADD COLUMN tip NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.tip IS '小费';

ALTER TABLE sales_product_amounts
    ADD COLUMN other_fee NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.other_fee IS '其他费用';

ALTER TABLE sales_product_amounts
    ADD COLUMN rounding NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.rounding IS '抹零';

ALTER TABLE sales_product_amounts
    ADD COLUMN overflow_amount NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.overflow_amount IS '溢收';

ALTER TABLE sales_product_amounts
    ADD COLUMN merchant_send_fee NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.merchant_send_fee IS '商家承担配送费';

ALTER TABLE sales_product_amounts
    ADD COLUMN platform_send_fee NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.platform_send_fee IS '平台承担配送费';


ALTER TABLE sales_product_amounts
    ADD COLUMN send_fee_for_merchant NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.send_fee_for_merchant IS '用户支付给商家的配送费';

ALTER TABLE sales_product_amounts
    ADD COLUMN send_fee_for_platform NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.send_fee_for_platform IS '用户支付给平台的配送费';


ALTER TABLE sales_product_amounts
    ADD COLUMN pay_cost NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.pay_cost IS '用户实际支付';

ALTER TABLE sales_product_amounts
    ADD COLUMN pay_tp_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.pay_tp_allowance IS '支付三方承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN pay_merchant_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.pay_merchant_allowance IS '商家支付承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN pay_platform_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.pay_platform_allowance IS '平台支付承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN promotion_cost NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.promotion_cost IS '折扣用户实际支付';

ALTER TABLE sales_product_amounts
    ADD COLUMN promotion_tp_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.promotion_tp_allowance IS '折扣三方承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN promotion_merchant_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.promotion_merchant_allowance IS '商家优惠承担';

ALTER TABLE sales_product_amounts
    ADD COLUMN promotion_platform_allowance NUMERIC(38,16);
COMMENT ON COLUMN sales_product_amounts.promotion_platform_allowance IS '平台优惠承担';


-- 添加门店标签字段（StoreTags）
ALTER TABLE sales_payment_amounts
    ADD COLUMN store_tags text[];
COMMENT ON COLUMN sales_payment_amounts.store_tags IS '门店标签';

-- 添加门店标签名称字段（StoreTagNames）
ALTER TABLE sales_payment_amounts
    ADD COLUMN store_tag_names text[];
COMMENT ON COLUMN sales_payment_amounts.store_tag_names IS '门店标签';

-- 添加门店标签字段（StoreTags）
ALTER TABLE sales_discount_amounts
    ADD COLUMN store_tags text[];
COMMENT ON COLUMN sales_discount_amounts.store_tags IS '门店标签';

-- 添加门店标签名称字段（StoreTagNames）
ALTER TABLE sales_discount_amounts
    ADD COLUMN store_tag_names text[];
COMMENT ON COLUMN sales_discount_amounts.store_tag_names IS '门店标签';


-- 添加卡券渠道编码字段（CouponChannelCode）
ALTER TABLE sales_discount_amounts
    ADD COLUMN coupon_channel_code VARCHAR(255);
COMMENT ON COLUMN sales_discount_amounts.coupon_channel_code IS '卡券渠道编码';

-- 添加卡券渠道名称字段（CouponChannelName）
ALTER TABLE sales_discount_amounts
    ADD COLUMN coupon_channel_name VARCHAR(255);
COMMENT ON COLUMN sales_discount_amounts.coupon_channel_name IS '卡券渠道名称';

alter table store_caches
    add regions text[];
comment on column store_caches.regions is '地理位置编码';

alter table store_caches
    add region_0 text;
comment on column store_caches.region_0 is '省';
alter table store_caches
    add region_1 text;
comment on column store_caches.region_1 is '市';
alter table store_caches
    add region_2 text;
comment on column store_caches.region_2 is '区';

alter table store_caches
    add open_date date;
comment on column store_caches.open_date is '开店时间';

-- drop table table_caches;

-- auto-generated definition
create sequence table_caches_id_seq1;
create table table_caches
(
    id         bigint default nextval('table_caches_id_seq1'::regclass) not null
        primary key,
    partner_id bigint                                                   not null,
    store_id   bigint                                                   not null,
    zone_id    bigint,
    zone_name  varchar(100),
    table_name varchar(100),
    table_seat integer,
    table_mode integer
);

comment on column table_caches.id is '桌位id';

comment on column table_caches.partner_id is 'partner_id';

comment on column table_caches.store_id is 'store_id';

comment on column table_caches.zone_id is '分区id';

comment on column table_caches.zone_name is '分区名称';

comment on column table_caches.table_name is '桌位名称';

comment on column table_caches.table_seat is '座位数量';
comment on column table_caches.table_mode is '是否培训桌位；0：否，1：是';

alter table sales_ticket_contents
    add shift_number varchar(255);
comment on column sales_ticket_contents.shift_number is '班次号';

create index idx_sales_ticket_contents_store_id_shift_number
    on sales_ticket_contents (store_id, shift_number);

alter table sales_ticket_contents_abnormal
    add shift_number varchar(255);
comment on column sales_ticket_contents_abnormal.shift_number is '班次号';

create index idx_sales_ticket_contents_abnormal_store_id_shift_number
    on sales_ticket_contents_abnormal (store_id, shift_number);

-- auto-generated definition
create sequence store_goal_id_seq;
create table store_goal
(
    id         bigint default nextval('store_goal_id_seq'::regclass) not null
        primary key,
    partner_id bigint                                                not null,
    store_id   bigint                                                not null,
    goal       numeric(38, 16),
    goal_date  date,
    deleted    boolean,
    create_at  timestamp,
    update_at  timestamp
);

comment on table store_goal is '门店目标表';

create index idx_store_goal_store_id_goal_date
    on store_goal (store_id, goal_date);


-- auto-generated definition
create table exchange_rate
(
    id            bigint not null
        primary key,
    partner_id    bigint not null,
    from_currency varchar(20),
    to_currency   varchar(20),
    rate          numeric(38, 16),
    start_date    date,
    end_date      date,
    deleted       boolean,
    create_at     timestamp,
    update_at     timestamp
);

comment on table exchange_rate is '汇率表';


