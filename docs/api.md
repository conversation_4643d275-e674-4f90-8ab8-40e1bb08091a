# 项目使用 `API` 列表

本项目支持配置地址前缀，默认为：`sales-report`，即所有地址前需要加上：`/sales-report`

## 一. 电子小票部分

### 1. 电子小票存储

将电子小票信息存储下来

* Request

```http
POST /api/v1/ticket HTTP/1.1
Authorization: Bearer 9RNuiN6sNyOKS-Hb8D14yw
Content-type:  application/json

<json-ticket-data>
```

* Response

```json
{
  "payload":{
    "id":"4376331755901190144"
  },
  "status_code":0
}
```

### 2. 电子小票查询

查询已存储电子小票。

*Request

```http
GET /api/v1/tickets?store_id=123&bus_date=2020-07-01T15%3a04%3a05Z%3b2020-07-02T15%3a04%3a05Z HTTP/1.1
Authorization: Bearer 9RNuiN6sNyOKS-Hb8D14yw
```

[参数文档链接](http://docs.hexcloud.org/hipos-api/#/api%E6%96%87%E6%A1%A3/%E9%94%80%E5%94%AE%E6%8A%A5%E8%A1%A8/%E7%94%B5%E5%AD%90%E5%B0%8F%E7%A5%A8%E6%A0%A1%E9%AA%8C?id=_1-%e6%9f%a5%e8%af%a2)

|参数名称|参数含义|示例值|
|--|--|--|
|store_id|门店 ID  |123|
|bus_date|营业日期(时间必须是成对出现，开始在前，结束在后，以`;`分割开，参数需要经过 `URL` 编码下同)|2006-01-02T15:04:05Z;2020-01-02T15:04:05Z|
|order_date|订单时间|2006-01-02T15:04:05Z;2020-01-02T15:04:05Z|
|create_date|接收时间(创建时间)|2006-01-02T15:04:05Z;2020-01-02T15:04:05Z|
|update_date|更新时间|2006-01-02T15:04:05Z;2020-01-02T15:04:05Z|
|status|状态|INIT|
|limit|限制|10|
|offset|偏移|10|

* Response

```json
{
  "payload":{
    "rows":[
      {
        "id":4376239384618369024,
        "eticket_id":"123",
        "partner_id":2,
        "store_id":123,
        "bus_date":"2020-06-30T00:00:00Z",
        "order_time":"2020-07-02T02:52:55.889Z",
        "created":"2020-07-02T02:52:55.889Z",
        "updated":"2020-07-02T02:52:55.889Z",
        "updated_by":4201196624908648450,
        "status":"INIT",
        "process_status":"INIT",
        "content":{
          "store":{
            "id":"123"
          },
          "bus_date":"2020-06-30",
          "end_time":"2020-06 12:20:20",
          "ticket_id":"123455"
        },
        "content_modified":{
          "store":{
            "id":"123"
          },
          "bus_date":"2020-06-30",
          "end_time":"2020-06 12:20:20",
          "ticket_id":"123455"
        }
      }],
    "total":3
  },
  "status_code":0
}
```

### 3. 电子小票更新

更新小票 `Json` 和状态。
**限制**：

* 只有status为DATAERROR才允许修改

[文档参见](http://docs.hexcloud.org/hipos-api/#/api%E6%96%87%E6%A1%A3/%E9%94%80%E5%94%AE%E6%8A%A5%E8%A1%A8/%E7%94%B5%E5%AD%90%E5%B0%8F%E7%A5%A8%E6%A0%A1%E9%AA%8C?id=_2-%e6%9b%b4%e6%96%b0%e6%95%b0%e6%8d%ae%e5%86%85%e5%ae%b9)

*Request

```http
PUT /api/v1/tickets HTTP/1.1
Authorization: Bearer 9RNuiN6sNyOKS-Hb8D14yw

{"id":"1277902968402477056", "init_status": false, "content_modified": "{}"}
```

* Response

```json
{
    "payload": {
        "operate_status": true
    },
    "status_code": 0
}
```

### 3. 电子小票更新信息回滚

回滚小票 `Json`。
**限制**：

* 只有status为DATAERROR/INIT才允许修改

[文档参见](http://docs.hexcloud.org/hipos-api/#/api%E6%96%87%E6%A1%A3/%E9%94%80%E5%94%AE%E6%8A%A5%E8%A1%A8/%E7%94%B5%E5%AD%90%E5%B0%8F%E7%A5%A8%E6%A0%A1%E9%AA%8C?id=_3-%e5%9b%9e%e6%bb%9a%e6%95%b0%e6%8d%ae%e5%86%85%e5%ae%b9)

*Request

```http
PUT /api/v1/tickets/rollback HTTP/1.1
Authorization: Bearer 9RNuiN6sNyOKS-Hb8D14yw

{"id":"1277902968402477056"}
```

* Response

```json
{
    "payload": {
        "operate_status": true
    },
    "status_code": 0
}
```

## 二、接口文档
### 1、财务报表(收银汇总表)
统计财务维度销售情况
* Url
```
POST /api/v1/report/financial
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|business_amount|double|营业额|
|discount_amount|double|折扣金额|
|real_amount|double|实收金额|
|valid_order_count|int|有效订单数|
|customer_price|double|单均价|
|composition_of_bill_types|array[json]|账单类型组成|
|composition_of_take_away|array[json]|外卖渠道组成|
|composition_of_paid_in|array[json]|实收组成|
|composition_of_paid_in_total|double|实收组成小计|
|composition_of_discount|array[json]|优惠组成|
|composition_of_discount_total|double|优惠组成小计|
|package_fee|double|餐盒费收入流水|
|merchant_send_fee|double|配送费收入流水|
|sales_fee|double|商品销售收入流水|
|dish_income_account_total|double|菜品收入科目小计|
|total|int|行数|
|Tag|string|导出标识|

##### composition_of_bill_types
|参数|类型|说明|
|:----|:----|:----|
|order_type|string|账单类型|
|order_name|string|账单类型名称|
|gross_amount|double|商品流水|
|order_count|int|订单数|

##### composition_of_take_away
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_name|string|渠道名称|
|gross_amount|double|商品流水|
|order_count|int|订单数|

##### composition_of_paid_in
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_name|string|渠道名称|
|discounts|array[json]|折扣方式实收组成|
|payments|array[json]|支付方式实收组成|
|transfer_real_amount_total_payments|double|支付方式实收组成合计|
|transfer_real_amount_total_discounts|double|折扣方式实收组成合计|

##### discounts
|参数|类型|说明|
|:----|:----|:----|
|discount_id|int|折扣方式id|
|discount_name|string|折扣名称|
|real_amount|double|交易实收|
|discount_contribute|double|财务优惠|
|transfer_real_amount|double|财务实收|

##### payments
|参数|类型|说明|
|:----|:----|:----|
|payment_id|int|支付方式id|
|payment_name|string|支付名称|
|real_amount|double|交易实收|
|discount_contribute|double|财务优惠|
|transfer_real_amount|double|财务实收|

##### composition_of_discount
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_name|string|渠道名称|
|commission|double|佣金|
|send_fee_for_merchant|double|配送费商家承担|
|other_fee|double|其他|
|payments|array[json]|支付方式优惠组成|
|discounts|array[json]|折扣方式优惠组成|

##### discounts
|参数|类型|说明|
|:----|:----|:----|
|discount_id|int|折扣方式id|
|discount_name|string|折扣名称|
|real_amount|double|交易实收|
|discount_contribute|double|财务优惠|
|transfer_real_amount|double|财务实收|

##### payments
|参数|类型|说明|
|:----|:----|:----|
|payment_id|int|支付方式id|
|payment_name|string|支付名称|
|real_amount|double|交易实收|
|discount_contribute|double|财务优惠|
|transfer_real_amount|double|财务实收|

### 2、销售汇总表
#### 2.1、门店销售报表
统计门店销售情况
* Url
```
POST /api/v1/report/sales/store
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|business_amount|double|营业额|
|expend_amount|double|支出(交易)|
|real_amount|double|实收金额(交易)|
|pay_amount|double|实付金额|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|platform_allowance|double|平台活动补贴|
|merchant_send_fee|double|配送费商家承担|
|payment_transfer_amount|double|支付转折扣金额|
|discount_transfer_amount|double|折扣转支付金额|
|commission|double|佣金|
|refund_order_count|double|退单数|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|other_fee|double|其他金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|

#### 2.2、门店渠道报表
按渠道统计门店销售情况
* Url
```
POST /api/v1/report/sales/channel/store
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|channel_id|false|string|渠道id|
|order_type|false|string|订单类型(物流构成)|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|
|child|array[json]|渠道|
##### child
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_code|string|渠道编码|
|channel_name|string|渠道名称|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|
|child|array[json]|物流构成|
##### child
|参数|类型|说明|
|:----|:----|:----|
|order_type|string|物流|
|order_type_name|string|物流构成|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|

#### 2.3、门店时段报表
按渠道统计门店销售情况
* Url
```
POST /api/v1/report/sales/period/store
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|period|false|array[int]|时段|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|business_amount_period|json[map]|营业额,key是时段，value是营业额，下类似|
|real_amount_period|json[map]|实收金额(交易)|
|expend_amount_period|json[map]|支出(交易)|
|order_count_period|json[map]|有效订单数|
|transfer_real_amount_period|json[map]|实收组成(财务)|
|discount_contribute_period|json[map]|优惠组成(财务)|

#### 2.4、退单原因分析表
统计退单原因
* Url
```
POST /api/v1/report/sales/refund/analy
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|refund_code|false|string|退单原因编号|
|refund_side|false|int|退单方.0:全部;1:商家;2:用户|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|refund_side|string|退单角色|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|order_count_returned|int|退单数|
|receivable_returned|double|退单应付金额|
|pay_amount_returned|double|退单实付金额|
|discount_amount_returned|double|退单折扣金额|
|merchant_discount_amount_returned|double|退单商品补贴|
|commission|double|退单佣金|
|delivery_fee_returned|double|退单配送费|
|package_fee_returned|double|退单包装费|
|tip_returned|double|退单小费|
|service_returned|double|退单服务费|
|tax_fee_returned|double|退单税额|
|rounding_returned|double|退单抹零|
|overflow_amount_returned|double|退单溢收|
|child|array[json]|渠道|
##### child
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_code|string|渠道编码|
|channel_name|string|渠道名称|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|order_count_returned|int|退单数|
|receivable_returned|double|退单应付金额|
|pay_amount_returned|double|退单实付金额|
|discount_amount_returned|double|退单折扣金额|
|merchant_discount_amount_returned|double|退单商品补贴|
|commission|double|退单佣金|
|delivery_fee_returned|double|退单配送费|
|package_fee_returned|double|退单包装费|
|tip_returned|double|退单小费|
|service_returned|double|退单服务费|
|tax_fee_returned|double|退单税额|
|rounding_returned|double|退单抹零|
|overflow_amount_returned|double|退单溢收|
|child|array[json]|退单原因|
##### child 退单原因
|参数|类型|说明|
|:----|:----|:----|
|refund_code|string|退单原因编码|
|refund_reason|string|退单原因|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|order_count_returned|int|退单数|
|receivable_returned|double|退单应付金额|
|pay_amount_returned|double|退单实付金额|
|discount_amount_returned|double|退单折扣金额|
|merchant_discount_amount_returned|double|退单商品补贴|
|commission|double|退单佣金|
|delivery_fee_returned|double|退单配送费|
|package_fee_returned|double|退单包装费|
|tip_returned|double|退单小费|
|service_returned|double|退单服务费|
|tax_fee_returned|double|退单税额|
|rounding_returned|double|退单抹零|
|overflow_amount_returned|double|退单溢收|

#### 2.5、交易折扣报表 
统计折扣情况
* Url
```
POST /api/v1/report/sales/discount
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|channel_id|false|string|渠道id|
|order_type|false|string|订单类型(物流构成)|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|
|child|array[json]|渠道|
##### child
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_code|string|渠道编码|
|channel_name|string|渠道名称|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|
|child|array[json]|物流构成|
##### child
|参数|类型|说明|
|:----|:----|:----|
|order_type|string|物流|
|order_type_name|string|物流构成|
|business_amount|double|营业额|
|real_amount|double|实收金额(交易)|
|expend_amount|double|支出(交易)|
|customer|double|单均价|
|valid_order_count|int|有效订单数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|merchant_allowance|double|商家活动补贴|
|package_fee|double|包装费|
|delivery_fee|double|配送费原价|
|send_fee|double|配送费用户实际支付|
|merchant_send_fee|double|配送费商家承担|
|platform_send_fee|double|配送费平台承担|
|commission|double|佣金|
|service_fee|double|服务费|
|tip|double|小费|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|change_amount|double|找零|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|transfer_real_amount|double|实收组成(财务)|
|discount_contribute|double|优惠组成(财务)|
|discount_merchant_contribute|double|优惠组成-折扣（财务）|
|pay_merchant_contribute|double|优惠组成-支付|
|real_amount_discount|double|实收组成-折扣（财务|
|real_amount_payment|double|实收组成-支付（财务|

### 3、商品统计表
#### 3.1、单品销售报表
统计商品销售情况
* Url
```
POST /api/v1/report/sales/product
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息
|product_category_ids|false|array[string]|类目ids|
|product_ids|false|array[string]|商品ids|
|price_scope|false|string|价格段，形如:"60-70,50-60"|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|data|array[json]|商品一级类目|
##### data 一级类目
|参数|类型|说明|
|:----|:----|:----|
|category0|int|一级类目id|
|category0_code|string|一级类目编码|
|category0_name|string|一级类目名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|data|array[json]|商品二级类目|
##### data 二级类目
|参数|类型|说明|
|:----|:----|:----|
|category1|int|二级类目id|
|category1_code|string|二级类目编码|
|category1_name|string|二级类目名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|data|array[json]|商品三级类目|
##### data 三级类目
|参数|类型|说明|
|:----|:----|:----|
|category2|int|三级类目id|
|category2_code|string|三级类目编码|
|category2_name|string|三级类目名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|data|array[json]|商品|
##### data 商品
|参数|类型|说明|
|:----|:----|:----|
|product_id|int|商品id|
|product_code|string|商品编码|
|product_name|string|商品名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|

#### 3.2、单品渠道报表 
按渠道统计商品销售情况
* Url
```
POST /api/v1/report/sales/channel/product
```
* Request 

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
|is_pre|false|bool|是否预计算. true:是；false:否 |
|is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息
|product_category_ids|false|array[string]|类目ids|
|product_ids|false|array[string]|商品ids|
|channel_id|false|string|渠道id|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|product_average_price|double|商品均价|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|child|array[json]|商品|
##### child
|参数|类型|说明|
|:----|:----|:----|
|product_category_id|int|类目id|
|product_category_code|string|类目编码|
|product_category_name|string|类目名称|
|product_id|int|商品id|
|product_code|string|商品编码|
|product_name|string|商品名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|product_average_price|double|商品均价|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|child|array[json]|渠道|
##### child
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_code|string|渠道编码|
|channel_name|string|渠道名称|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|product_average_price|double|商品均价|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|
|child|array[json]|物流|
##### child
|参数|类型|说明|
|:----|:----|:----|
|order_type|string|物流|
|order_type_name|string|物流构成|
|gross_amount|double|商品流水|
|net_amount|double|商品实收|
|discount_amount|double|折扣金额|
|item_count|int|商品数量|
|product_average_price|double|商品均价|
|gross_amount_returned|double|退单商品流水|
|net_amount_returned|double|退单商品实收|
|discount_amount_returned|double|退单折扣金额|
|item_count_returned|int|退单商品数量|

#### 3.3、单品时段报表
统计各个时段商品销售情况
* Url
```
POST /api/v1/report/sales/period/product
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|period|false|array[int]|时段|
|product_category_ids|false|array[string]|类目ids|
|product_ids|false|array[string]|商品ids|
|price_scope|false|string|价格段，形如:"60-70,50-60"|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|gross_amount|amount|商品流水|
|net_amount|amount|商品实收|
|item_count|int|商品数量|
|data|array[json]|各个时段合计|
|child|array[json]|商品时段|
##### data 各个时段合计
|参数|类型|说明|
|:----|:----|:----|
|h00|json[map]|0时段,h01到h23分别是1时段到23时段，结构类似|
##### child 商品时段数据
|参数|类型|说明|
|:----|:----|:----|
|product_category_id|int|类目id|
|product_category_code|string|类目编码|
|product_category_name|string|类目名称|
|product_id|int|商品id|
|product_code|string|商品编码|
|product_name|string|商品名称|
|gross_amount|amount|商品流水|
|net_amount|amount|商品实收|
|item_count|int|商品数量|
|h00|json[map]|0时段,h01到h23分别是1时段到23时段，结构类似|
##### h00 0时段统计数据
|参数|类型|说明|
|:----|:----|:----|
|gross_amount|amount|商品流水|
|net_amount|amount|商品实收|
|item_count|int|商品数量|

#### 3.4、商品属性销售表
统计商品属性销售信息
* Url
```
POST /api/v1/report/sales/channel/product
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
|is_pre|false|bool|是否预计算. true:是；false:否 |
|is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|tag_types|true|string|"TASTE":口味分布;"ATTRIBUTE":属性;"FEED":加料;"GRAPH":柱状图|
|product_ids|false|array[string]|商品ids|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|total|int|行数|

##### rows结构
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|accessory_id|string|加料商品id|
|accessory_name|string|加料商品名称|
|attribute_name_code|string|属性名编码|
|attribute_name_name|string|属性名称|
|attribute_value_code|string|属性值编码|
|attribute_value_name|string|属性值名称|
|flavor_distribute|string|口味分布|
|product_count|int|商品数量|
|product_count_total|int|商品数量总计|
|percent_product_count|double|占比|
|accessories|string|加料|

### 4、支付统计表
#### 4.1、支付统计报表
统计支付方式情况
* Url
```
POST /api/v1/report/sales/payment
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息
|payment_ids|false|array[string]|支付方式，多选|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|transfer_real_amount|double|财务实收金额|
|payment_transfer_amount|double|支付转折扣|
|cost|double|用户实际购买金额|
|tp_allowance|double|第三方补贴金额|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|change_amount|double|找零|
|item_count|int|支付次数|
|item_count_returned|int|支付退单数|
|child|array[json]|渠道|
##### child 渠道
|参数|类型|说明|
|:----|:----|:----|
|channel_id|int|渠道id|
|channel_code|string|渠道编码|
|channel_name|string|渠道名称|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|transfer_real_amount|double|财务实收金额|
|payment_transfer_amount|double|支付转折扣|
|cost|double|用户实际购买金额|
|tp_allowance|double|第三方补贴金额|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|change_amount|double|找零|
|item_count|int|支付次数|
|item_count_returned|int|支付退单数|
|child|array[json]|支付|
##### child 支付
|参数|类型|说明|
|:----|:----|:----|
|payment_id|int|支付id|
|payment_code|string|支付编码|
|payment_name|string|支付名称|
|payment_type|string|有偿/无偿|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|transfer_real_amount|double|财务实收金额|
|payment_transfer_amount|double|支付转折扣|
|cost|double|用户实际购买金额|
|tp_allowance|double|第三方补贴金额|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|change_amount|double|找零|
|item_count|int|支付次数|
|item_count_returned|int|支付退单数|

#### 4.2、支付时段报表
统计各个时段支付方式情况
* Url
```
POST /api/v1/report/sales/period/payment
```
* Request

|参数|是否必选|类型|说明|
| :-----| :----: |:----|:----|
| is_pre|false|bool|是否预计算. true:是；false:否 |
| is_today|false|bool|是否今日. true:是；false:否 |
|region_search_type|true|string|查询区域/门店. "STORE":门店；"GEO_REGION":地理区域；"BRANCH_REGION":管理区域；"COMPANY":公司|
|region_search_ids|true|array[string]|查询ids,为空表示查全部|
|open_status|true|string|开店状态."OPENED": 已开店；"CLOSED":已闭店，""空表示全部状态|
|store_types|true|array[string]|门店类型.支持多选|
|region_group_type|true|string|汇总类型 "STORE":按门店汇总；"GEO_REGION":按地理区域汇总;"COMPANY":按公司汇总;"BRANCH_REGION":按管理区域汇总|
|region_group_level|false|int|汇总层级 0:第一层；1:第二层；2:第三层.仅当汇总类型是地理区域或管理区域有效|
|start|true|string|开始时间|
|end|true|string|结束时间|
|limit|true|int|sql语句的limit条件，为-1表示查所有|
|offset|true|int|sql语句的offset，与limit一起使用|
|include_total|true|bool|
|lan|true|string|语言|
|tag_type|true|string|查询tag，"DETAILED":详细信息;"SUMMARY":汇总信息|
|payment_ids|false|array[string]|支付ids|

* Response

|参数|类型|说明|
|:----|:----|:----|
|rows|array[json]|返回记录|
|summary|json|汇总|
|total|int|行数|

##### rows结构，summary类似
|参数|类型|说明|
|:----|:----|:----|
|bus_date|string|营业日期|
|region_id|int|门店(或地理区域或管理区域或公司)id|
|region_code|string|门店(或地理区域或管理区域或公司)编码|
|region_name|string|门店(或地理区域或管理区域或公司)名称|
|region_address|string|门店所在城市|
|region_alias|string|门店别名|
|store_type|string|门店类型id|
|store_type_name|string|门店类型名称|
|business_days|int|营业天数|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|transfer_real_amount|double|财务实收金额|
|payment_transfer_amount|double|支付转折扣|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|change_amount|double|找零|
|item_count|int|支付次数|
|item_count_returned|int|支付退单数|
|data|array[json]|各个时段合计|
|child|array[json]|支付时段|
##### data 各个时段合计
|参数|类型|说明|
|:----|:----|:----|
|h00|json[map]|0时段,h01到h23分别是1时段到23时段，结构类似|
##### child 商品时段数据
|参数|类型|说明|
|:----|:----|:----|
|payment_id|int|支付id|
|payment_code|string|支付编码|
|payment_name|string|支付名称|
|payment_type|string|有偿/无偿|
|h00|json[map]|0时段,h01到h23分别是1时段到23时段，结构类似|
##### h00 0时段统计数据
|参数|类型|说明|
|:----|:----|:----|
|receivable|double|应付金额|
|pay_amount|double|实付金额|
|transfer_real_amount|double|财务实收金额|
|payment_transfer_amount|double|支付转折扣|
|rounding|double|抹零|
|overflow_amount|double|溢收|
|change_amount|double|找零|
|item_count|int|支付次数|
|item_count_returned|int|支付退单数|




  
  
  

