create table sales_inbound_product
(
    id            bigint primary key,
    inbound_id    bigint                   not null,
    partner_id    bigint                   not null,
    store_id      bigint                   not null,
    sku_id        bigint                   not null,
    spu_id        bigint                   not null,
    sku_code      varchar(100),
    sku_name      varchar(100),
    price         numeric(38, 16),
    acc_price     numeric(38, 16),
    qty           numeric(38, 16),
    inbound_time  timestamp with time zone,
    operator_name varchar(100),
    created       timestamp with time zone not null,
    updated       timestamp with time zone,
    updated_by    bigint,
    deleted       integer
);

comment on table sales_inbound_product is '入库商品';

comment on column sales_inbound_product.id is 'ID';
comment on column sales_inbound_product.inbound_id is '入库ID';
comment on column sales_inbound_product.partner_id is '租户ID';
comment on column sales_inbound_product.store_id is '门店ID';
comment on column sales_inbound_product.sku_id is 'SKU ID';
comment on column sales_inbound_product.spu_id is 'SPU ID';
comment on column sales_inbound_product.sku_code is 'SKU 编码';
comment on column sales_inbound_product.sku_name is 'SKU 名称';
comment on column sales_inbound_product.price is '单价';
comment on column sales_inbound_product.acc_price is '小料价格';
comment on column sales_inbound_product.qty is '数量';
comment on column sales_inbound_product.inbound_time is '入库时间';
comment on column sales_inbound_product.operator_name is '操作人';
comment on column sales_inbound_product.created is '创建时间';
comment on column sales_inbound_product.updated is '更新时间';
comment on column sales_inbound_product.updated_by is '更新人';
comment on column sales_inbound_product.deleted is '删除标记';

create index sales_inbound_product_inbound_id on sales_inbound_product (inbound_id);

create index sales_inbound_product_inbound_time_store_id on sales_inbound_product (inbound_time, store_id);

create table sales_inbound_content
(
    id            bigint primary key,
    inbound_id    bigint                   not null,
    partner_id    bigint                   not null,
    store_id      bigint                   not null,
    inbound_time  timestamp with time zone not null,
    product_names varchar(100),
    product_qty   integer,
    operator_name varchar(100),
    content       json,
    created       timestamp with time zone not null,
    updated       timestamp with time zone,
    updated_by    bigint,
    deleted       integer
);

comment on table sales_inbound_content is '入库记录';

comment on column sales_inbound_content.id is 'ID';
comment on column sales_inbound_content.inbound_id is '入库ID';
comment on column sales_inbound_content.partner_id is '租户ID';
comment on column sales_inbound_content.store_id is '门店ID';
comment on column sales_inbound_content.inbound_time is '入库时间';
comment on column sales_inbound_content.product_names is '商品名称';
comment on column sales_inbound_content.product_qty is '商品数量';
comment on column sales_inbound_content.operator_name is '操作人';
comment on column sales_inbound_content.content is '原始数据';
comment on column sales_inbound_content.created is '创建时间';
comment on column sales_inbound_content.updated is '更新时间';
comment on column sales_inbound_content.updated_by is '更新人';
comment on column sales_inbound_content.deleted is '删除标记';

create index sales_inbound_content_inbound_id on sales_inbound_content (inbound_id);

create index sales_inbound_content_inbound_time_store_id_product_names on sales_inbound_content (inbound_time, store_id, product_names);
