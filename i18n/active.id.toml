DataInvalidErr = "Parameter data tidak valid"
DataTypeErr = "Kesalahan tipe data"
TypeAssertsErr = "Gagal asersi tipe"
EditPasswordErr = "Gagal mengubah kata sandi"
DataEmptyErr = "Data kosong"
BusDateEmptyErr = "Tanggal bisnis kosong"
GetMembershipGradeErr = "Tidak menemukan tingkat keanggotaan terkait"
PriorMaxNumErr = "Antrian telah mencapai batas maksimum"
ParseLocalTimeErr = "Kesalahan mengubah string ke format waktu"
StatusCodeErr = "StatusCode tidak sama dengan 200"
StatusCodeNotZeroErr = "Nilai StatusCode yang dikembalikan API tidak sama dengan 0"
UploadTicketSuccessErr = "Gagal mengunggah tiket"
SaleTaxNumErr = "Nomor pajak tidak diset atau kesalahan serupa"
OrderTypeErr = "Tipe OrderType tidak cocok"
TaxAmountErr = "Tidak perlu faktur untuk jumlah pesanan ini"
StoreIdAbsentErr = "ID toko tidak ada"
ProductLabelsAbsentErr = "Label produk tidak ada"
TakeoutLockErr = "Gagal mendapatkan kunci pesanan takeout"
DailyComputeErr = "Toko tidak berhasil menyelesaikan penyelesaian harian"
UpdateMetaDataLockerErr = "Memicu perilaku pengunduhan data induk beberapa kali dalam waktu singkat, kunci pengunduhan data induk sebelumnya belum dilepas"
NetworkFailureErr = "Gagal jaringan"
RemotePrintExistErr = "Permintaan cetak ini sudah diproses"
DataNotFoundErr = "Data tidak ditemukan"
RebootMinutesInvalidErr = "Parameter paksa reboot POS tidak benar"
RebootCounterErr = "Timer sudah ada"
TicketIdNotFoundErr = "ID tiket tidak ditemukan"
PromotionCouponErr = "Promosi dan kupon tidak dapat digunakan bersamaan"
BucketNameNotFoundErr = "Nama bucket tidak ada"
PredetermineOccupiedErr = "Waktu saat ini sudah dipesan"
TakeoutStatusUpdateErr = "Gagal memperbarui status takeout"
UnauthorizedErr = "Tidak berizin"
AuthFailed = "Otentikasi gagal"
ActivateStoreRespErr = "Kesalahan respons aktivasi toko"
DeviceUninstallDone = "Pencopotan perangkat selesai"
TicketPrinter = "Printer tiket"
KitchenPrinter = "Printer dapur"
PrintDone = "Cetak berhasil"
PrintRequestDataInvalid = "Data yang dikirim dalam permintaan cetak tidak benar"
PrintRequestTypeInvalid = "Tipe yang dikirim dalam permintaan cetak tidak benar"
GetDBDataErr = "Gagal mendapatkan data DB"
GetDBOrderErr = "Gagal mendapatkan data pesanan DB"
JsonEncodeErr = "Gagal mengkodekan data ke json"
RoutingErr = "Kegagalan rute"
GetTemplateErr = "Gagal mendapatkan template"
TemplateParseErr = "Gagal mengurai template"
PrintFailed = "Cetak gagal"
NoPrinterAvailableErr = "Tidak ada printer yang tersedia untuk POS"
PrinterIsNotConnected = "Printer tidak terhubung"
ConnectFailed = "Gagal terhubung"
UsbDisconnected = "USB terputus"
DeviceOffline = "Perangkat offline"
UnknownConnection = "Koneksi tidak diketahui"
AbnormalOperation = "Operasi tidak normal"
WorksFine = "Berfungsi dengan baik"
SuccessfullyEstablishedConnectionWithTheBackend = "Berhasil terhubung dengan backend"
LostConnectionToBackend = "Koneksi dengan backend terputus"
TakeoutOrderReminder = "Pengingat pesanan takeout"
GetSystemMetadataFailed = "Gagal mendapatkan metadata sistem"
CreateRequestErr = "Kesalahan membuat permintaan"
HubHasNotStartedYet = "Hub belum dimulai"
NotifyMealPickupFailed = "Gagal memberitahu pengambilan makanan"
ReportAbnormalOrderFailed = "Gagal melaporkan pesanan abnormal"
ConfirmTakemealWithOmsFailed = "Gagal mengonfirmasi pengambilan makanan dengan oms"
UpdateTableFailed = "Gagal memperbarui meja"
PrintKitchen = "Stiker cup"
CookingReceipt = "Tanda terima memasak"
Receipt = "Tanda terima"
OnlyOpenedOrderCanAddProductCurrStatusIs = "Hanya pesanan dengan status 'terbuka' yang dapat menambahkan produk, status pesanan saat ini adalah"
CalculatePromotionErr = "Kesalahan menghitung promosi"
RemoveTradeInProductErr = "Gagal menghapus produk tukar tambah"
OnlyOpenedOrderCanRemoveProductCurrStatusIs = "Hanya pesanan dengan status 'terbuka' yang dapat menghapus produk, status pesanan saat ini adalah"
OnlyOpenedOrderCanBeEditedCurrStatusIs = "Hanya pesanan dengan status 'terbuka' yang dapat diedit, status pesanan saat ini adalah"
CannotFindRelatedProductUsingProvidedShoppingID = "Tidak dapat menemukan produk terkait menggunakan ID belanja yang diberikan"
FailedToCreateRequest = "Gagal membuat permintaan"
GetTokenFailed = "Gagal mendapatkan token"
HelloEric = "Halo, Eric"
AuthTimeout = "Otentikasi timeout"
GetNatsMonitorFailed = "Gagal mendapatkan monitor nats"
ActivationSuccessful = "Aktivasi berhasil"
OpenStoreFailed = "Gagal membuka toko"
TheresNoActivatedStore = "Tidak ada toko yang diaktifkan"
PreOrderNotification = "Notifikasi pemesanan terlebih dahulu"
YouHaveANewPreOrderPleaseProcessItInTime = "Anda memiliki pemesanan terlebih dahulu baru, silakan diproses tepat waktu"
RefundOrderNotification = "Notifikasi pesanan pengembalian"
YouHaveANewRefundOrderPleaseProcessItInTime = "Anda memiliki pesanan pengembalian baru, silakan diproses tepat waktu"
WechatMiniProgram = "Mini Program WeChat"
WechatMiniProgramDiscount = "Diskon Mini Program"
WechatMiniProgramCommissions = "Komisi Mini Program"
WechatMiniProgramDeliveryFee = "Biaya pengiriman Mini Program"
WechatMiniProgramOther = "Lainnya Mini Program"
AlipayMiniProgram = "Mini Program Alipay"
Meituan = "Meituan"
MeituanDiscount = "Diskon Meituan"
MeituanCommissions = "Komisi Meituan"
MeituanDeliveryFee = "Biaya pengiriman Meituan"
MeituanOther = "Lainnya Meituan"
KettaISV = "KettaISV"
KettaISVDiscount = "Diskon KettaISV"
KettaISVCommissions = "Komisi KettaISV"
KettaISVDeliveryFee = "Biaya pengiriman KettaISV"
KettaISVOther = "Lainnya KettaISV"
Eleme = "Eleme"
ElemeDiscount = "Diskon Eleme"
ElemeCommissions = "Komisi Eleme"
ElemeDeliveryFee = "Biaya pengiriman Eleme"
ElemeOther = "Lainnya Eleme"
ElemeISV = "Eleme ISV"
ElemeDiscountISV = "Diskon Eleme ISV"
ElemeCommissionsISV = "Komisi Eleme ISV"
ElemeDeliveryFeeISV = "Biaya pengiriman Eleme ISV"
ElemeOtherISV = "Lainnya Eleme ISV"
MeituanISV = "Meituan ISV"
MeituanDiscountISV = "Diskon Meituan ISV"
MeituanCommissionsISV = "Komisi Meituan ISV"
MeituanDeliveryFeeISV = "Biaya pengiriman Meituan ISV"
MeituanOtherISV = "Lainnya Meituan ISV"
HungryPandaIsv = "HungryPanda ISV"
HungryPandaDiscountISV = "Diskon HungryPanda ISV"
HungryPandaCommissionsISV = "Komisi HungryPanda ISV"
HungryPandaDeliveryFeeISV = "Biaya Pengiriman HungryPanda ISV"
HungryPandaOtherISV = "Lainnya HungryPanda ISV"
WrongRequestData = "Data permintaan salah"
UpdatingMetadataOnProgress = "Pembaruan metadata sedang berlangsung"
ScaleNotMounted = "Timbangan tidak terpasang"
KPrintTicketType = "Tiket"
KPrintKitchenType = "Stiker cup"
KPrintReportType = "Laporan"
KPrintAuxType = "Membuka laci kas"
KPrintReportShift = "Laporan shift"
KPrintReportDaily = "Laporan harian"
KPrintReportHour = "Laporan penjualan per jam"
KPrintReportCategory = "Laporan kategori"
KPrintReportPay = "Laporan pembayaran"
KPrintAuxOpenDrawer = "Membuka laci kas"
KPrintReportPayInOut = "Laporan masuk/keluar uang"
KTakeoutCooking = "Tanda terima memasak"
ProductNotFoundByUsingThisShoppingID = "Produk tidak ditemukan menggunakan ID belanja ini"
HangupIsNotAllowed = "Penangguhan pesanan tidak diizinkan"
OrderVerificationFailed = "Verifikasi kupon gagal"
OrderFailedPleaseRetry = "Pesanan gagal, silakan coba lagi"
CategoryReport = "Statistik laporan kategori"
UnknownCategory = "Kategori tidak diketahui"
TakeoutOrOther = "Takeout/Lainnya"
CountTicket = "Jumlah tiket"
TotalNumberOfPeopleDining = "Total jumlah pelanggan makan"
TotalCateringRevenue = "Total pendapatan katering"
AutoDiningOutIsNotAllowedForThisOrderStatus = "Auto dining out tidak diizinkan untuk status pesanan ini"
PrintErr1001 = "Pencetakan gagal, kesalahan koneksi printer (1001)"
PrintErr1002 = "Pencetakan gagal, kesalahan alokasi tugas cetak (1002)"
PrintErr1003 = "Pencetakan gagal, kesalahan alokasi tugas cetak (1003)"
OfflineUnimplementedErr = "Mode offline tidak mendukung fitur ini"
BusinessCalendar = "Tidak dalam jam kerja"
NoPrintersOnline = "Tidak ada printer online"
NoMatchingRoute = "Tidak ada rute yang cocok"
ProductIsEmpty = "Barang pengembalian tidak boleh kosong"
PartialRefundCheck = "Pengembalian hanya mendukung satu metode pembayaran"
PartialRefundCheck1 = "Melebihi batas waktu pengembalian yang berlaku"
PartialRefundCheck2 = "Voucher produk dan voucher platform saat ini tidak mendukung pengembalian sebagian."
PartialRefundCheck3 = "Pesanan telah sepenuhnya dikembalikan dan tidak dapat dikembalikan lagi."
PartialRefundCheck4 = "Jumlah barang yang dapat dikembalikan tidak boleh melebihi jumlah pembelian."
PartialRefundCheck5 = "Verifikasi pengembalian produk gagal."
PartialRefundCheck6 = "Pesanan pertukaran hadiah tidak dapat dikembalikan sebagian"
Summarychannels = "Saluran Ringkasan"