DataInvalidErr = "Недопустимый параметр данных"
DataTypeErr = "Ошибка типа данных"
TypeAssertsErr = "Ошибка утверждения типа"
EditPasswordErr = "Не удалось изменить пароль"
DataEmptyErr = "Данные отсутствуют"
BusDateEmptyErr = "Бизнес-дата отсутствует"
GetMembershipGradeErr = "Не найден соответствующий уровень членства"
PriorMaxNumErr = "Очередь достигла максимального предела"
ParseLocalTimeErr = "Ошибка преобразования строки в формат времени"
StatusCodeErr = "StatusCode не равен 200"
StatusCodeNotZeroErr = "Значение StatusCode возвращенное API не равно 0"
UploadTicketSuccessErr = "Не удалось загрузить билет"
SaleTaxNumErr = "Номер налога не установлен или аналогичная ошибка"
OrderTypeErr = "Несоответствие типа OrderType"
TaxAmountErr = "Для данной суммы заказа не требуется выставление счета"
StoreIdAbsentErr = "ID магазина отсутствует"
ProductLabelsAbsentErr = "Метка продукта отсутствует"
TakeoutLockErr = "Не удалось получить блокировку заказа на вынос"
DailyComputeErr = "Ежедневный расчет не был успешно завершен в одном из магазинов"
UpdateMetaDataLockerErr = "Многократное вызывание процедуры загрузки основных данных за короткий промежуток времени, предыдущая блокировка загрузки не была снята"
NetworkFailureErr = "Сбой сети"
RemotePrintExistErr = "Этот запрос на печать уже был обработан"
DataNotFoundErr = "Данные не найдены"
RebootMinutesInvalidErr = "Некорректные параметры для принудительной перезагрузки POS"
RebootCounterErr = "Таймер уже существует"
TicketIdNotFoundErr = "ID билета не найден"
PromotionCouponErr = "Акции и купоны не могут использоваться одновременно"
BucketNameNotFoundErr = "Имя корзины не найдено"
PredetermineOccupiedErr = "Текущее время уже зарезервировано"
TakeoutStatusUpdateErr = "Не удалось обновить статус заказа на вынос"
UnauthorizedErr = "Неавторизован"
AuthFailed = "Аутентификация не удалась"
ActivateStoreRespErr = "Ошибка сообщения об активации магазина"
DeviceUninstallDone = "Деинсталляция устройства завершена"
TicketPrinter = "Принтер для чеков"
KitchenPrinter = "Принтер для кухни"
PrintDone = "Печать завершена"
PrintRequestDataInvalid = "Некорректные данные в запросе на печать"
PrintRequestTypeInvalid = "Некорректный тип в запросе на печать"
GetDBDataErr = "Не удалось получить данные из базы данных"
GetDBOrderErr = "Не удалось получить данные заказа из базы данных"
JsonEncodeErr = "Ошибка кодирования в формат JSON"
RoutingErr = "Ошибка маршрутизации"
GetTemplateErr = "Не удалось получить шаблон"
TemplateParseErr = "Ошибка разбора шаблона"
PrintFailed = "Не удалось выполнить печать"
NoPrinterAvailableErr = "Нет доступных принтеров для POS"
PrinterIsNotConnected = "Принтер не подключен"
ConnectFailed = "Не удалось подключиться"
UsbDisconnected = "USB отключен"
DeviceOffline = "Устройство не в сети"
UnknownConnection = "Неизвестное соединение"
AbnormalOperation = "Ненормальная операция"
WorksFine = "Работает нормально"
SuccessfullyEstablishedConnectionWithTheBackend = "Успешное соединение с сервером"
LostConnectionToBackend = "Потеря соединения с сервером"
TakeoutOrderReminder = "Напоминание о заказе на вынос"
GetSystemMetadataFailed = "Не удалось получить системные метаданные"
CreateRequestErr = "Ошибка создания запроса"
HubHasNotStartedYet = "Hub еще не запущен"
NotifyMealPickupFailed = "Не удалось уведомить о получении заказа"
ReportAbnormalOrderFailed = "Не удалось сообщить об аномальном заказе"
ConfirmTakemealWithOmsFailed = "Не удалось подтвердить получение заказа с OMS"
UpdateTableFailed = "Не удалось обновить таблицу"
PrintKitchen = "Наклейка для кухни"
CookingReceipt = "Квитанция о приготовлении"
Receipt = "Чек"
OnlyOpenedOrderCanAddProductCurrStatusIs = "Только открытые заказы могут добавлять продукты, текущий статус заказа"
CalculatePromotionErr = "Ошибка расчета акции"
RemoveTradeInProductErr = "Не удалось удалить товар для обмена"
OnlyOpenedOrderCanRemoveProductCurrStatusIs = "Только открытые заказы могут удалять продукты, текущий статус заказа"
OnlyOpenedOrderCanBeEditedCurrStatusIs = "Только открытые заказы могут быть отредактированы, текущий статус заказа"
CannotFindRelatedProductUsingProvidedShoppingID = "Не удалось найти соответствующий продукт с использованием предоставленного ID покупки"
FailedToCreateRequest = "Не удалось создать запрос"
GetTokenFailed = "Не удалось получить токен"
HelloEric = "Привет, Эрик"
AuthTimeout = "Тайм-аут аутентификации"
GetNatsMonitorFailed = "Не удалось получить монитор Nats"
ActivationSuccessful = "Активация успешна"
OpenStoreFailed = "Не удалось открыть магазин"
TheresNoActivatedStore = "Нет активированных магазинов"
PreOrderNotification = "Уведомление о предварительном заказе"
YouHaveANewPreOrderPleaseProcessItInTime = "У вас есть новый предварительный заказ, пожалуйста, обработайте его вовремя"
RefundOrderNotification = "Уведомление о возврате заказа"
YouHaveANewRefundOrderPleaseProcessItInTime = "У вас есть новый возврат заказа, пожалуйста, обработайте его вовремя"
WechatMiniProgram = "Мини-программа WeChat"
WechatMiniProgramDiscount = "Скидка мини-программы"
WechatMiniProgramCommissions = "Комиссии мини-программы"
WechatMiniProgramDeliveryFee = "Плата за доставку мини-программы"
WechatMiniProgramOther = "Другое по мини-программе"
AlipayMiniProgram = "Мини-программа Alipay"
Meituan = "Meituan"
MeituanDiscount = "Скидка Meituan"
MeituanCommissions = "Комиссии Meituan"
MeituanDeliveryFee = "Плата за доставку Meituan"
MeituanOther = "Другое от Meituan"
KettaISV = "KettaISV"
KettaISVDiscount = "Скидка KettaISV"
KettaISVCommissions = "Комиссии KettaISV"
KettaISVDeliveryFee = "Плата за доставку KettaISV"
KettaISVOther = "Другое от KettaISV"
Eleme = "Eleme"
ElemeDiscount = "Скидка Eleme"
ElemeCommissions = "Комиссии Eleme"
ElemeDeliveryFee = "Плата за доставку Eleme"
ElemeOther = "Другое от Eleme"
ElemeISV = "Eleme ISV"
ElemeDiscountISV = "Скидка Eleme ISV"
ElemeCommissionsISV = "Комиссии Eleme ISV"
ElemeDeliveryFeeISV = "Плата за доставку Eleme ISV"
ElemeOtherISV = "Другое от Eleme ISV"
MeituanISV = "Meituan ISV"
MeituanDiscountISV = "Скидка Meituan ISV"
MeituanCommissionsISV = "Комиссии Meituan ISV"
MeituanDeliveryFeeISV = "Плата за доставку Meituan ISV"
MeituanOtherISV = "Другое от Meituan ISV"
HungryPandaIsv = "ISV HungryPanda"
HungryPandaDiscountISV = "Скидка ISV HungryPanda"
HungryPandaCommissionsISV = "Комиссия ISV HungryPanda"
HungryPandaDeliveryFeeISV = "Плата за доставку ISV HungryPanda"
HungryPandaOtherISV = "Другое ISV HungryPanda"
WrongRequestData = "Неверные данные запроса"
UpdatingMetadataOnProgress = "Обновление метаданных в процессе"
ScaleNotMounted = "Весы не установлены"
KPrintTicketType = "Билет"
KPrintKitchenType = "Наклейка для кухни"
KPrintReportType = "Отчет"
KPrintAuxType = "Открытие денежного ящика"
KPrintReportShift = "Отчет о смене"
KPrintReportDaily = "Ежедневный отчет"
KPrintReportHour = "Отчет о продажах по часам"
KPrintReportCategory = "Отчет по категориям"
KPrintReportPay = "Отчет о платежах"
KPrintAuxOpenDrawer = "Открытие денежного ящика"
KPrintReportPayInOut = "Отчет о взносах/выплатах"
KTakeoutCooking = "Квитанция о приготовлении"
ProductNotFoundByUsingThisShoppingID = "Продукт по данному ID покупки не найден"
HangupIsNotAllowed = "Приостановка заказа не разрешена"
OrderVerificationFailed = "Не удалось проверить заказ"
OrderFailedPleaseRetry = "Не удалось оформить заказ, пожалуйста, попробуйте снова"
CategoryReport = "Отчет по категориям"
UnknownCategory = "Неизвестная категория"
TakeoutOrOther = "На вынос/Другое"
CountTicket = "Количество билетов"
TotalNumberOfPeopleDining = "Общее число посетителей"
TotalCateringRevenue = "Общий доход от кейтеринга"
AutoDiningOutIsNotAllowedForThisOrderStatus = "Автоматическое обслуживание на вынос не разрешено для данного статуса заказа"
PrintErr1001 = "Не удалось выполнить печать, ошибка подключения принтера (1001)."
PrintErr1002 = "Не удалось выполнить печать, ошибка распределения задания печати (1002)."
PrintErr1003 = "Не удалось выполнить печать, ошибка распределения задания печати (1003)."
OfflineUnimplementedErr = "Режим оффлайн не поддерживает эту функцию"
BusinessCalendar = "Не в рабочее время"
NoPrintersOnline = "Нет доступного онлайн-принтера"
NoMatchingRoute = "Не найден подходящий маршрут"
ProductIsEmpty = "Товар для возврата не может быть пустым"
PartialRefundCheck = "Возврат денежных средств поддерживает только один способ оплаты"
PartialRefundCheck1 = "Превышен срок действия возврата"
PartialRefundCheck2 = "Купон на товары и купон платформы временно не поддерживают частичный возврат."
PartialRefundCheck3 = "Заказ уже полностью возвращен, повторный возврат невозможен."
PartialRefundCheck4 = "Количество возвращаемых товаров не может превышать количество покупки."
PartialRefundCheck5 = "Не удалось пройти проверку возвратимого товара."
PartialRefundCheck6 = "Заказы по обмену подарками не могут быть частично возвращены"
Summarychannels = "Каналы сводной информации"