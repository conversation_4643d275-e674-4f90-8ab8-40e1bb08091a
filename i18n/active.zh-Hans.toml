DataInvalidErr = "数据参数不合法"
DataTypeErr = "数据类型错误"
TypeAssertsErr = "类型断言失败"
EditPasswordErr = "修改密码失败"
DataEmptyErr = "数据为空"
BusDateEmptyErr = "营业日为空"
GetMembershipGradeErr = "没查询到相关联的会员等级"
PriorMaxNumErr = "插队已经到达上限"
ParseLocalTimeErr = "字符串转时间格式出错"
StatusCodeErr = "StatusCode不等于200"
StatusCodeNotZeroErr = "接口返回值StatusCode不等于0"
UploadTicketSuccessErr = "上传订单失败"
SaleTaxNumErr = "税号未设置或类似错误"
OrderTypeErr = "OrderType类型不匹配"
TaxAmountErr = "该订单金额，无需开票"
StoreIdAbsentErr = "门店id不存在"
ProductLabelsAbsentErr = "该商品label不存在"
TakeoutLockErr = "获取外卖订单锁失败"
DailyComputeErr = "有门店没有日结计算成功"
UpdateMetaDataLockerErr = "短时间多次触发拉取主档行为，上一次主档下载锁未释放"
NetworkFailureErr = "网络故障"
RemotePrintExistErr = "该打印请求已经处理过"
DataNotFoundErr = "未查询到数据"
RebootMinutesInvalidErr = "强制重启POS参数不正确"
RebootCounterErr = "已经存在一个计时器"
TicketIdNotFoundErr = "没有找到该订单"
PromotionCouponErr = "促销和优惠券不能同时使用"
BucketNameNotFoundErr = "bucket名称不存在"
PredetermineOccupiedErr = "当前时间已有预定"
TakeoutStatusUpdateErr = "更新外卖状态失败"
UnauthorizedErr = "未认证"
AuthFailed = "认证失败"
ActivateStoreRespErr = "激活门店报文错误"
DeviceUninstallDone = "设备卸载完成"
TicketPrinter = "小票打印机"
KitchenPrinter = "厨房打印机"
PrintDone = "打印成功"
PrintRequestDataInvalid = "打印请求发送的数据不正确"
PrintRequestTypeInvalid = "打印请求发送的类型不正确"
GetDBDataErr = "获取db数据失败"
GetDBOrderErr = "获取db订单数据失败"
JsonEncodeErr = "json格式化数据失败"
RoutingErr = "路由失败"
GetTemplateErr = "获取模板失败"
TemplateParseErr = "模板解析失败"
PrintFailed = "打印失败"
NoPrinterAvailableErr = "POS没有可以使用的打印机"
PrinterIsNotConnected = "打印机没有连接"
ConnectFailed = "连接失败"
UsbDisconnected = "USB断开"
DeviceOffline = "设备离线"
UnknownConnection = "未知连接"
AbnormalOperation = "操作异常"
WorksFine = "工作正常"
SuccessfullyEstablishedConnectionWithTheBackend = "成功与后台建立连接"
LostConnectionToBackend = "失去与后台的连接"
TakeoutOrderReminder = "外卖订单提醒"
GetSystemMetadataFailed = "获取系统主档失败"
CreateRequestErr = "创建请求错误"
HubHasNotStartedYet = "hub还没有启动"
NotifyMealPickupFailed = "通知取餐失败"
ReportAbnormalOrderFailed = "上报异常单失败"
ConfirmTakemealWithOmsFailed = "向oms确认取餐失败"
UpdateTableFailed = "更新桌位失败"
PrintKitchen = "杯贴"
CookingReceipt = "出餐小票"
Receipt = "小票"
OnlyOpenedOrderCanAddProductCurrStatusIs = "只有open状态的订单可以添加商品, 当前订单状态为"
CalculatePromotionErr = "计算促销错误"
RemoveTradeInProductErr = "移除换购商品失败"
OnlyOpenedOrderCanRemoveProductCurrStatusIs = "只有open状态的订单可以删除商品, 当前订单状态为"
OnlyOpenedOrderCanBeEditedCurrStatusIs = "只有open状态的订单才可以修改订单, 当前订单状态为"
CannotFindRelatedProductUsingProvidedShoppingID = "根据该 shopping id 没有找到对应的产品"
FailedToCreateRequest = "创建请求失败"
GetTokenFailed = "获取token失败"
HelloEric = "你好，刘如鸿"
AuthTimeout = "认证超时"
GetNatsMonitorFailed = "获取nats monitor失败"
ActivationSuccessful = "激活成功"
OpenStoreFailed = "开店失败"
TheresNoActivatedStore = "没有激活的门店"
PreOrderNotification = "预订单通知"
YouHaveANewPreOrderPleaseProcessItInTime = "您有一笔新的预订单，请及时处理"
RefundOrderNotification = "退单通知"
YouHaveANewRefundOrderPleaseProcessItInTime = "您有一笔新的退单，请及时处理"
WechatMiniProgram = "微信小程序"
WechatMiniProgramDiscount = "小程序折扣"
WechatMiniProgramCommissions = "小程序佣金"
WechatMiniProgramDeliveryFee = "小程序配送费"
WechatMiniProgramOther = "小程序其他"
AlipayMiniProgram = "支付宝小程序"
Meituan = "美团"
MeituanDiscount = "美团折扣"
MeituanCommissions = "美团佣金"
MeituanDeliveryFee = "美团配送费"
MeituanOther = "美团其他"
KettaISV = "KeetaISV"
KettaISVDiscount = "KeetaISV折扣"
KettaISVCommissions = "KeetaISV佣金"
KettaISVDeliveryFee = "KeetaISV送费"
KettaISVOther = "KeetaISV其他"
Eleme = "饿了么"
ElemeDiscount = "饿了么折扣"
ElemeCommissions = "饿了么佣金"
ElemeDeliveryFee = "饿了么配送费"
ElemeOther = "饿了么其他"
ElemeISV = "饿了么ISV"
ElemeDiscountISV = "饿了么ISV折扣"
ElemeCommissionsISV = "饿了么ISV佣金"
ElemeDeliveryFeeISV = "饿了么ISV配送费"
ElemeOtherISV = "饿了么ISV其他"
MeituanISV = "美团ISV"
MeituanDiscountISV = "美团ISV折扣"
MeituanCommissionsISV = "美团ISV佣金"
MeituanDeliveryFeeISV = "美团ISV配送费"
MeituanOtherISV = "美团ISV其他"
HungryPandaIsv = "熊猫ISV"
HungryPandaDiscountISV = "熊猫ISV折扣"
HungryPandaCommissionsISV = "熊猫ISV佣金"
HungryPandaDeliveryFeeISV = "熊猫ISV配送费"
HungryPandaOtherISV = "熊猫ISV其他"
WrongRequestData = "请求报文错误"
UpdatingMetadataOnProgress = "正在更新主档"
ScaleNotMounted = "电子秤设备未加载"
KPrintTicketType = "小票"
KPrintKitchenType = "杯贴"
KPrintReportType = "报表"
KPrintAuxType = "开钱箱"
KPrintReportShift = "交班报表"
KPrintReportDaily = "日结报表"
KPrintReportHour = "时段销售报表"
KPrintReportCategory = "类别报表"
KPrintReportPay = "支付报表"
KPrintAuxOpenDrawer = "打开钱箱"
KPrintReportPayInOut = "取/放钞票报表"
KTakeoutCooking = "出餐小票"
ProductNotFoundByUsingThisShoppingID = "根据该shopping id没有找到该产品"
HangupIsNotAllowed = "不允许挂起订单"
OrderVerificationFailed = "卡券核销失败"
OrderFailedPleaseRetry = "下单失败, 请重试"
CategoryReport = "类表报表数据统计"
UnknownCategory = "未知类别"
TakeoutOrOther = "外卖/其他"
CountTicket = "小票单数"
TotalNumberOfPeopleDining = "就餐人数合计"
TotalCateringRevenue = "餐饮收入合计"
AutoDiningOutIsNotAllowedForThisOrderStatus = "该订单状态不允许进行出餐"
PrintErr1001 = "打印失败,打印机连接错误(1001)"
PrintErr1002 = "打印失败,打印任务分配错误(1002)"
PrintErr1003 = "打印失败,打印任务分配错误(1003)"
OfflineUnimplementedErr = "离线模式不支持此功能"
BusinessCalendar = "未在营业时间"
NoPrintersOnline = "没有在线打印机"
NoMatchingRoute = "没有匹配的路由"
ProductIsEmpty = "退款商品不能为空"
PartialRefundCheck = "退款单只支持单一支付方式"
PartialRefundCheck1 = "超过退款有效期"
PartialRefundCheck2 = "商品券 平台券暂不支持部分退款"
PartialRefundCheck3 = "该订单已经退款完成,不能再退款"
PartialRefundCheck4 = "退款商品数量不能大于购买数量"
PartialRefundCheck5 = "退款商品校验失败"
PartialRefundCheck6 = "赠品换购订单不能部分退款"
Summarychannels = "汇总渠道"