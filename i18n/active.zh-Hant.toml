DataInvalidErr = "資料參數不合法"
DataTypeErr = "資料類型錯誤"
TypeAssertsErr = "類型斷言失敗"
EditPasswordErr = "修改密碼失敗"
DataEmptyErr = "資料為空"
BusDateEmptyErr = "營業日為空"
GetMembershipGradeErr = "未查詢到相關聯的會員等級"
PriorMaxNumErr = "插隊已達到上限"
ParseLocalTimeErr = "字串轉時間格式錯誤"
StatusCodeErr = "StatusCode不等於200"
StatusCodeNotZeroErr = "介面返回值StatusCode不等於0"
UploadTicketSuccessErr = "上傳訂單失敗"
SaleTaxNumErr = "稅號未設置或類似錯誤"
OrderTypeErr = "OrderType類型不匹配"
TaxAmountErr = "該訂單金額，無需開票"
StoreIdAbsentErr = "門店id不存在"
ProductLabelsAbsentErr = "該商品label不存在"
TakeoutLockErr = "獲取外賣訂單鎖失敗"
DailyComputeErr = "有門店沒有日結計算成功"
UpdateMetaDataLockerErr = "短時間多次觸發拉取主檔行為，上一次主檔下載鎖未釋放"
NetworkFailureErr = "網路故障"
RemotePrintExistErr = "該列印請求已處理過"
DataNotFoundErr = "未查詢到資料"
RebootMinutesInvalidErr = "強制重啟POS參數不正確"
RebootCounterErr = "已經存在一個計時器"
TicketIdNotFoundErr = "沒有找到該訂單"
PromotionCouponErr = "促銷和優惠券不能同時使用"
BucketNameNotFoundErr = "bucket名稱不存在"
PredetermineOccupiedErr = "當前時間已有預定"
TakeoutStatusUpdateErr = "更新外賣狀態失敗"
UnauthorizedErr = "未認證"
AuthFailed = "認證失敗"
ActivateStoreRespErr = "啟動門店報文錯誤"
DeviceUninstallDone = "設備卸載完成"
TicketPrinter = "小票列印機"
KitchenPrinter = "廚房列印機"
PrintDone = "列印成功"
PrintRequestDataInvalid = "列印請求發送的資料不正確"
PrintRequestTypeInvalid = "列印請求發送的類型不正確"
GetDBDataErr = "獲取db資料失敗"
GetDBOrderErr = "獲取db訂單資料失敗"
JsonEncodeErr = "json格式化資料失敗"
RoutingErr = "路由失敗"
GetTemplateErr = "獲取模板失敗"
TemplateParseErr = "模板解析失敗"
PrintFailed = "列印失敗"
NoPrinterAvailableErr = "POS沒有可以使用的列印機"
PrinterIsNotConnected = "列印機沒有連接"
ConnectFailed = "連接失敗"
UsbDisconnected = "USB斷開"
DeviceOffline = "設備離線"
UnknownConnection = "未知連接"
AbnormalOperation = "操作異常"
WorksFine = "工作正常"
SuccessfullyEstablishedConnectionWithTheBackend = "成功與後台建立連接"
LostConnectionToBackend = "失去與後台的連接"
TakeoutOrderReminder = "外賣訂單提醒"
GetSystemMetadataFailed = "獲取系統主檔失敗"
CreateRequestErr = "創建請求錯誤"
HubHasNotStartedYet = "hub還沒有啟動"
NotifyMealPickupFailed = "通知取餐失敗"
ReportAbnormalOrderFailed = "上報異常單失敗"
ConfirmTakemealWithOmsFailed = "向oms確認取餐失敗"
UpdateTableFailed = "更新桌位失敗"
PrintKitchen = "杯貼"
CookingReceipt = "出餐小票"
Receipt = "小票"
OnlyOpenedOrderCanAddProductCurrStatusIs = "只有open狀態的訂單可以添加商品, 當前訂單狀態為"
CalculatePromotionErr = "計算促銷錯誤"
RemoveTradeInProductErr = "移除換購商品失敗"
OnlyOpenedOrderCanRemoveProductCurrStatusIs = "只有open狀態的訂單可以刪除商品, 當前訂單狀態為"
OnlyOpenedOrderCanBeEditedCurrStatusIs = "只有open狀態的訂單才可以修改訂單, 當前訂單狀態為"
CannotFindRelatedProductUsingProvidedShoppingID = "根據該 shopping id 沒有找到對應的產品"
FailedToCreateRequest = "創建請求失敗"
GetTokenFailed = "獲取token失敗"
HelloEric = "你好，劉如鴻"
AuthTimeout = "認證超時"
GetNatsMonitorFailed = "獲取nats monitor失敗"
ActivationSuccessful = "激活成功"
OpenStoreFailed = "開店失敗"
TheresNoActivatedStore = "沒有激活的門店"
PreOrderNotification = "預訂單通知"
YouHaveANewPreOrderPleaseProcessItInTime = "您有一筆新的預訂單，請及時處理"
RefundOrderNotification = "退單通知"
YouHaveANewRefundOrderPleaseProcessItInTime = "您有一筆新的退單，請及時處理"
WechatMiniProgram = "微信小程序"
WechatMiniProgramDiscount = "小程序折扣"
WechatMiniProgramCommissions = "小程序佣金"
WechatMiniProgramDeliveryFee = "小程序配送費"
WechatMiniProgramOther = "小程序其他"
AlipayMiniProgram = "支付寶小程序"
Meituan = "美團"
MeituanDiscount = "美團折扣"
MeituanCommissions = "美團佣金"
MeituanDeliveryFee = "美團配送費"
MeituanOther = "美團其他"
KettaISV = "KeetaISV"
KettaISVDiscount = "KeetaISV折扣"
KettaISVCommissions = "KeetaISV佣金"
KettaISVDeliveryFee = "KeetaISV送費"
KettaISVOther = "KeetaISV其他"
Eleme = "餓了麼"
ElemeDiscount = "餓了麼折扣"
ElemeCommissions = "餓了麼佣金"
ElemeDeliveryFee = "餓了麼配送費"
ElemeOther = "餓了麼其他"
ElemeISV = "餓了麼ISV"
ElemeDiscountISV = "餓了麼ISV折扣"
ElemeCommissionsISV = "餓了麼ISV佣金"
ElemeDeliveryFeeISV = "餓了麼ISV配送費"
ElemeOtherISV = "餓了麼ISV其他"
MeituanISV = "美團ISV"
MeituanDiscountISV = "美團ISV折扣"
MeituanCommissionsISV = "美團ISV佣金"
MeituanDeliveryFeeISV = "美團ISV配送費"
MeituanOtherISV = "美團ISV其他"
HungryPandaIsv = "熊猫ISV"
HungryPandaDiscountISV = "熊猫ISV折扣"
HungryPandaCommissionsISV = "熊猫ISV佣金"
HungryPandaDeliveryFeeISV = "熊猫ISV配送費"
HungryPandaOtherISV = "熊猫ISV其他"
WrongRequestData = "請求報文錯誤"
UpdatingMetadataOnProgress = "正在更新主檔"
ScaleNotMounted = "電子秤設備未加載"
KPrintTicketType = "小票"
KPrintKitchenType = "杯貼"
KPrintReportType = "報表"
KPrintAuxType = "開錢箱"
KPrintReportShift = "交班報表"
KPrintReportDaily = "日結報表"
KPrintReportHour = "時段銷售報表"
KPrintReportCategory = "類別報表"
KPrintReportPay = "支付報表"
KPrintAuxOpenDrawer = "打開錢箱"
KPrintReportPayInOut = "取/放鈔票報表"
KTakeoutCooking = "出餐小票"
ProductNotFoundByUsingThisShoppingID = "根據該shopping id沒有找到該產品"
HangupIsNotAllowed = "不允許掛起訂單"
OrderVerificationFailed = "卡券核銷失敗"
OrderFailedPleaseRetry = "下單失敗, 請重試"
CategoryReport = "類表報表資料統計"
UnknownCategory = "未知類別"
TakeoutOrOther = "外賣/其他"
CountTicket = "小票單數"
TotalNumberOfPeopleDining = "就餐人數合計"
TotalCateringRevenue = "餐飲收入合計"
AutoDiningOutIsNotAllowedForThisOrderStatus = "該訂單狀態不允許進行出餐"
PrintErr1001 = "列印失敗,印表機連線錯誤(1001)"
PrintErr1002 = "列印失敗,列印任務分配錯誤(1002)"
PrintErr1003 = "列印失敗,列印任務分配錯誤(1003)"
OfflineUnimplementedErr = "離線模式不支援此功能"
BusinessCalendar = "未在營業時間"
NoPrintersOnline = "沒有線上打印機"
NoMatchingRoute = "没有匹配的路由"
ProductIsEmpty = "退款商品不能為空"
PartialRefundCheck = "退款單僅支援單一支付方式"
PartialRefundCheck1 = "超過退款有效期限"
PartialRefundCheck2 = "商品券 平台券暫不支援部分退款"
PartialRefundCheck3 = "該訂單已經退款完成，不能再退款"
PartialRefundCheck4 = "退款商品數量不能大於購買數量"
PartialRefundCheck5 = "退款商品校驗失敗"
PartialRefundCheck6 = "贈品換購訂單不能部分退"
Summarychannels = "匯總渠道"