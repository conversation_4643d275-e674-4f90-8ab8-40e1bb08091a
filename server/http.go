package server

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	h "gitlab.hexcloud.cn/histore/sales-report/http"
)

func RunHttp() {
	go func() {
		log.Printf("Http server start in `:%d`\n", config.DefaultConfig.Http.Port)
		srv := &http.Server{
			Addr:    fmt.Sprintf(":%d", config.DefaultConfig.Http.Port),
			Handler: h.GetServer(),
			// 读取整个请求头的超时时间（包括请求体）
			ReadTimeout: 30 * time.Second,
			// 写入响应的超时时间
			WriteTimeout: 30 * time.Second,
			// 保持空闲连接的超时时间（Go 1.8+）
			IdleTimeout: 30 * time.Second,
		}
		err := srv.ListenAndServe()
		if err != nil {
			log.Fatalln("Start http failed.", err)
		}
	}()
}
