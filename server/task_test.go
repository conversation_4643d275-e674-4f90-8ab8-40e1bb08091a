package server

import (
	"gitlab.hexcloud.cn/histore/sales-report/test"
	"os"
	"os/signal"
	"testing"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func Test_AsyncScanRunningTask(t *testing.T) {
	test.InitTest()
	//go func() {
	//	//for range time.NewTicker(30 * time.Second).C {
	//	for {
	//		logger.Pre().Info("开始扫描定时任务。")
	//		reTotal := dealRunningTask()
	//		//if !dealRunningTask() {
	//		//	break
	//		//}
	//		if reTotal == 0 {
	//			time.Sleep(time.Second * 5)
	//		}
	//	}
	//	//}
	//}()
	//
	go func() {
		for {
			logger.Pre().Info("开始扫描异常小票定时任务。")
			reTotal := runReInitAbnormalTicketTask()
			if reTotal == 0 {
				time.Sleep(time.Second * 6)
			}
		}
	}()
	flag := make(chan os.Signal)
	signal.Notify(flag, os.Interrupt, os.Kill)
	<-flag
}
