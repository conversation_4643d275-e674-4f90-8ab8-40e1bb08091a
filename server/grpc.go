// Auto generate code. Help should mail to ['<EMAIL>']
package server

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	otgrpc "gitlab.hexcloud.cn/histore/sales-report/pkg/tracing/go-grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/encoding"
	"google.golang.org/grpc/status"
	hexOauth "hexcloud.cn/histore/hex-pkg/hex-oauth"
	grpcOauth "hexcloud.cn/histore/hex-pkg/hex-oauth/grpc-oauth"
	"math"
	"net"
	"runtime/debug"
	"strings"
	"sync"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/middleware"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"

	"github.com/davecgh/go-spew/spew"
	"github.com/facebookgo/stack"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpclogrus "github.com/grpc-ecosystem/go-grpc-middleware/logging/logrus"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	"github.com/pkg/errors"
	grpc_proto "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	grpc_report "gitlab.hexcloud.cn/histore/sales-report/grpc/report"
)

func registerGRPC(gs *grpc.Server) {
	// TODO set yourself protobuf server
	// 在grpc中注册门店报表接口
	grpc_proto.RegisterSalesReportServer(
		gs, &grpc_report.SalesReportServer{},
	)
	// Example: schemaPB.RegisterSchemaServiceServer(grpcServer, &schema.SchemaServer{})
}

func onPanic(p interface{}) error {
	logger.Pre().WithField("values", spew.Sdump(p)).WithField("stack", stack.Callers(1)).Errorln("panicked in grpc")
	return errors.WithStack(errors.New("recovered from grpc panic"))
}

var (
	loggerOpts = []grpclogrus.Option{
		grpclogrus.WithDurationField(func(duration time.Duration) (key string, value interface{}) {
			return "grpc.duration", duration.String()
		}),
	}

	recoveryOpts = []grpc_recovery.Option{
		grpc_recovery.WithRecoveryHandler(onPanic),
	}

	grpcServer *grpc.Server
)

type JsonCodec struct {
}

func (codec *JsonCodec) Name() string {
	return "json"
}

func (codec *JsonCodec) Marshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

func (codec *JsonCodec) Unmarshal(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

func GrpcStart() error {
	port := config.DefaultConfig.Grpc.Port
	encoding.RegisterCodec(&JsonCodec{})
	// init grpc server
	lis, err := net.Listen("tcp", fmt.Sprintf(":%s", port))
	if err != nil {
		return err
	}

	enable := config.DefaultConfig.Oauth.Enable
	if enable {
		logger.Pre().Infoln("grpc using new oauth sever")
		grpcServer = grpc.NewServer(
			grpc_middleware.WithUnaryServerChain(
				UnaryTimeoutInterceptor(20*time.Second),
				grpc_recovery.UnaryServerInterceptor(recoveryOpts...),
				grpclogrus.UnaryServerInterceptor(logger.Pre(), loggerOpts...),
				middleware.UnaryLogDurationServerInterceptor,
				oauth.NewGrpcServerInterceptor("/sales_report.SalesReport/Testing", "/sales_report.SalesReport/StoreBusiness"),
				otgrpc.OpenTracingServerInterceptor(tracing.GetTracer()),
				// 调用新的鉴权服务
				//oauth.NewDebugGrpcServerInterceptor(100, 1),
			),
			grpc_middleware.WithStreamServerChain(
				grpc_recovery.StreamServerInterceptor(recoveryOpts...),
				grpclogrus.StreamServerInterceptor(logger.Pre(), loggerOpts...),
			),
			grpc.MaxRecvMsgSize(math.MaxInt32),
			grpc.MaxSendMsgSize(math.MaxInt32),
			grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
				MinTime:             time.Nanosecond,
				PermitWithoutStream: true,
			}),
		)
	} else {
		// init oauth client func
		logger.Pre().Infoln("grpc using old oauth sever")
		oauthFunc := hexOauth.NewAuthFunc(config.DefaultConfig.ConnStr.OAuth)
		oauthInterceptor := grpcOauth.NewAuthServerInterceptor(oauthFunc)
		grpcServer = grpc.NewServer(
			grpc_middleware.WithUnaryServerChain(
				UnaryTimeoutInterceptor(20*time.Second),
				grpc_recovery.UnaryServerInterceptor(recoveryOpts...),
				grpclogrus.UnaryServerInterceptor(logger.Pre(), loggerOpts...),
				middleware.UnaryLogDurationServerInterceptor,
				otgrpc.OpenTracingServerInterceptor(tracing.GetTracer()),
				oauthInterceptor,
			),
			grpc_middleware.WithStreamServerChain(
				grpc_recovery.StreamServerInterceptor(recoveryOpts...),
				grpclogrus.StreamServerInterceptor(logger.Pre(), loggerOpts...),
			),
			grpc.MaxRecvMsgSize(math.MaxInt32),
			grpc.MaxSendMsgSize(math.MaxInt32),
			grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
				MinTime:             time.Nanosecond,
				PermitWithoutStream: true,
			}),
		)
	}
	registerGRPC(grpcServer)

	// Register reflection service on gRPC server.
	reflection.Register(grpcServer)
	logger.Pre().Infof("GRPC server start, listen at %s\n", port)
	go grpcServer.Serve(lis)
	return nil
}

func GrpcStop() {
	grpcServer.Stop()
	logger.Pre().Infoln("GRPC server stoped.")
}

// UnaryTimeoutInterceptor returns a func that sets timeout to incoming unary requests.
func UnaryTimeoutInterceptor(timeout time.Duration) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo,
		handler grpc.UnaryHandler) (any, error) {
		ctx, cancel := context.WithTimeout(ctx, timeout)
		defer cancel()

		var resp any
		var err error
		var lock sync.Mutex
		done := make(chan struct{})
		// create channel with buffer size 1 to avoid goroutine leak
		panicChan := make(chan any, 1)
		go func() {
			defer func() {
				if p := recover(); p != nil {
					// attach call stack to avoid missing in different goroutine
					panicChan <- fmt.Sprintf("%+v\n\n%s", p, strings.TrimSpace(string(debug.Stack())))
				}
			}()

			lock.Lock()
			defer lock.Unlock()
			resp, err = handler(ctx, req)
			close(done)
		}()

		select {
		case p := <-panicChan:
			panic(p)
		case <-done:
			lock.Lock()
			defer lock.Unlock()
			return resp, err
		case <-ctx.Done():
			err := ctx.Err()
			if errors.Is(err, context.Canceled) {
				err = status.Error(codes.Canceled, err.Error())
			} else if errors.Is(err, context.DeadlineExceeded) {
				err = status.Error(codes.DeadlineExceeded, err.Error())
			}
			return nil, err
		}
	}
}
