// Auto generate code. Help should mail to ['<EMAIL>']
package server

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/grpc-ecosystem/grpc-gateway/runtime"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	grpc_report "gitlab.hexcloud.cn/histore/sales-report/grpc/proto"
	"golang.org/x/net/context"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

func registProxy(ctx context.Context, mux *runtime.ServeMux, grpcPort string, opts []grpc.DialOption) {
	// TODO set yourself proxy protobuf server. @see registGRPC(gs *grpc.Server)
	grpc_report.RegisterSalesReportHandlerFromEndpoint(
		ctx, mux, grpcPort, opts,
	)
	// Example: schemaPB.RegisterSchemaServiceHandlerFromEndpoint(ctx, mux, port, opts)
}

// 运行grpc的代理服务
func RunProxy() error {
	port := config.DefaultConfig.Grpc.Proxy
	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	mux := runtime.NewServeMux(
		runtime.WithMetadata(func(ctx context.Context, req *http.Request) metadata.MD {
			tokens := req.Header["Authorization"]
			var token string
			if len(tokens) > 0 {
				token = tokens[0]
			}
			token = strings.TrimLeft(token, "Bearer ")
			return metadata.Pairs("token", token)
		}),
		runtime.WithMarshalerOption(
			runtime.MIMEWildcard, &runtime.JSONPb{OrigName: true, EmitDefaults: true},
		),
	)
	opts := []grpc.DialOption{grpc.WithInsecure()}

	grpcPort := fmt.Sprintf("localhost:%s", config.DefaultConfig.Grpc.Port)
	registProxy(ctx, mux, grpcPort, opts)

	return http.ListenAndServe(fmt.Sprintf(":%s", port), mux)
}
