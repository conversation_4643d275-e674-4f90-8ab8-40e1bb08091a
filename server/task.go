package server

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/mq_task"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"strconv"
)

func SalesTicketCompensation(ctx context.Context) (reTotal int64) {
	defer func() {
		if e := recover(); e != nil {
			logger.Pre().Error("扫描超时任务出现 panic 错误。", e)
		}
	}()
	// 查询所有的异常电子小票tasks
	tasks, total, err := repo.DefaultSalesDB.SearchTaskSales(
		ctx)
	if err != nil {
		logger.Pre().Error("扫描超时任务出现数据库错误。", err)
		return 0
	}
	var ids []int64
	for _, item := range tasks {
		ids = append(ids, item.Id)
	}
	err = repo.DefaultSalesDB.UpdateSalesInitBatch(ctx, ids)
	if err == nil {
		tracing.ModuleTracing(ctx, "补偿:SalesTicketCompensation", func(ctx2 context.Context) error {
			for _, item := range tasks {
				if err = mq_task.PublishMessage(ctx2, config.WaitCheckTopic, []byte(strconv.Itoa(int(item.Id)))); err == nil {
					logger.Pre().Infof("处理超时任务成功。ID: %d", item.Id)
				} else {
					logger.Pre().Infof("处理超时任务失败。ID: %d", item.Id)
				}
			}
			return nil
		})
	}
	return total
}

func AbnormalTicketCompensation(ctx context.Context) (reTotal int64) {
	defer func() {
		if e := recover(); e != nil {
			logger.Pre().Error("Abnormal 扫描超时任务出现 panic 错误。", e)
		}
	}()

	// 查询所有的异常电子小票tasks
	tasks, total, err := repo.DefaultAbnormalSalesDB.SearchTaskAbnormalSales(ctx)
	if err != nil {
		logger.Pre().Error("Abnormal 扫描超时任务出现数据库错误。", err)
		return 0
	}
	var ids []int64
	for _, item := range tasks {
		ids = append(ids, item.Id)
	}
	err = repo.DefaultAbnormalSalesDB.UpdateAbnormalInitSalesBatch(ctx, ids)
	if err == nil {
		tracing.ModuleTracing(ctx, "补偿:AbnormalTicketCompensation", func(ctx2 context.Context) error {
			for _, item := range tasks {
				if err = mq_task.PublishMessage(ctx2, config.ResolveAbnormalTicketTopic, []byte(strconv.Itoa(int(item.Id)))); err == nil {
					logger.Pre().Infof("Abnormal 处理超时任务成功。ID: %d", item.Id)
				} else {
					logger.Pre().Infof("Abnormal 处理超时任务失败。ID: %d", item.Id)
				}
			}
			return nil
		})

	}
	return total
}
