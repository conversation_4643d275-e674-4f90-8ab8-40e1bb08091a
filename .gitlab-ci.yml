variables:
  #DOCKER_REGISTRY_URL: "registry.hexcloud.cn"
  DOCKER_REGISTRY_URL: "registry.cn-shanghai.aliyuncs.com"
  NS: "hexsaas"
  RANCHER2_CLI_IMAGE: "devops/rancher-cli-2.0"
  CI_SCRIPT_URL: "http://hexgitlab:<EMAIL>/devops-deployment/ci.cd-script.git"
  CI_SCRIPT_FOLDER: "ci.cd-script"
  REPLICAS: "1"
  DEPLOY_SERVER: "http://************:31713/deploy"

stages:
  - build_container
#  - deploy

before_script:
  - "docker login -u hex_registry@hextech -p hex-registry123 registry.cn-shanghai.aliyuncs.com"
  - 'echo "Build fold : $PWD"'
  - 'echo "PROJECT NAME : $CI_PROJECT_NAME"'
  - 'echo "PROJECT ID : $CI_PROJECT_ID"'
  - 'echo "PROJECT URL : $CI_PROJECT_URL"'
  - 'echo "ENVIRONMENT URL : $CI_ENVIRONMENT_URL"'
  - 'echo "DOCKER REGISTRY URL : $DOCKER_REGISTRY_URL"'
  - "export COMMIT_ID_SHORT=${CI_COMMIT_SHA:0:8}"
  - "export PATH=$PATH:/usr/bin"

  #- 'DEPLOYMENT_CONTAINER="$DOCKER_REGISTRY_URL/hexcloud/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/$CI_COMMIT_REF_NAME"'
  - 'DEPLOYMENT_CONTAINER="$DOCKER_REGISTRY_URL/$NS/$CI_PROJECT_NAME:${CI_PROJECT_NAMESPACE//\//_}_${CI_COMMIT_REF_NAME//\//_}"'
  - 'DEPLOYMENT_CONTAINER=$(echo $DEPLOYMENT_CONTAINER|tr "[:upper:]" "[:lower:]")'
  - "echo DEPLOYMENT_CONTAINER: $DEPLOYMENT_CONTAINER"

  - "export IMAGE_REPO=$DEPLOYMENT_CONTAINER"

  #- "export IMAGE_TAG=$IMAGE_REPO:${CI_COMMIT_SHA:0:8}"
  #- "export IMAGE_TAG_LATEST=$IMAGE_REPO:latest"
  - "export IMAGE_TAG=${IMAGE_REPO}_${CI_COMMIT_SHA:0:8}"
  - "export IMAGE_TAG_LATEST=${IMAGE_REPO}_latest"
  - "echo IMAGE_TAG is :$IMAGE_TAG"
  - "GIT_PATH=$PWD"

build_container:
  stage: build_container
  image: docker:git
  services:
    - docker:18.09.7-dind
  when: manual
  allow_failure: false
  script:
    - 'echo "Job $CI_JOB_NAME triggered by $GITLAB_USER_NAME ($GITLAB_USER_ID)"'
    - 'echo "Build on ${CI_COMMIT_REF_NAME//\//_}"'
    - 'echo "HEAD commit SHA $CI_COMMIT_SHA"'

    # build go file
    - "docker build -t $IMAGE_TAG -t $IMAGE_TAG_LATEST ."
    - 'OLD_IMAGE_ID=$(docker images --filter="before=$IMAGE_TAG" $IMAGE_REPO -q)'
    - "[[ -z $OLD_IMAGE_ID ]] || docker rmi -f $OLD_IMAGE_ID"
    - "docker push $IMAGE_TAG"
    - "docker push $IMAGE_TAG_LATEST"
    - 'echo "The build is sucessful,The image is : $IMAGE_TAG"'

#deploy_stage:
#  stage: deploy
#  image: registry.cn-shanghai.aliyuncs.com/devops/rancher-deploy/master:env
#  when: manual
#  allow_failure: false
#  script:
#    - 'echo "$DEPLOY_SERVER?namespace=hws-pos&projectid=c-k7mz2:p-hxlqx&app=$CI_PROJECT_NAME&image=$IMAGE_TAG"'
#    - curl -v "$DEPLOY_SERVER?namespace=hws-pos&projectid=c-k7mz2:p-hxlqx&app=$CI_PROJECT_NAME&image=$IMAGE_TAG"
#    - curl -v "$DEPLOY_SERVER?namespace=seesaw-boh-test&projectid=c-8l98g:p-x5pkw&app=$CI_PROJECT_NAME&image=$IMAGE_TAG"
#    - curl -v "$DEPLOY_SERVER?namespace=godiva-pos-test&projectid=c-8l98g:p-fw2kc&app=$CI_PROJECT_NAME&image=$IMAGE_TAG"
#    - curl -v "$DEPLOY_SERVER?namespace=uncleq-pos-test&projectid=c-6fxq8:p-zmlct&app=$CI_PROJECT_NAME&image=$IMAGE_TAG"
