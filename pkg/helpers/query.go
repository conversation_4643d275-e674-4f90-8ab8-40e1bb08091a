package helpers

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"

	"github.com/spf13/cast"
)

func QueryDBWithChan(trackId int64, db *sql.DB, sqlStr string, ch chan []interface{}) {
	ch <- QueryDB(trackId, db, sqlStr)
}

// 查询数据库，并将结果映射为map对象
func QueryDB(trackId int64, db *sql.DB, sqlStr string) []interface{} {
	results := make([]interface{}, 0)

	r, err := db.Query(sqlStr)
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	defer r.Close()
	// 取表头字段切片
	cs, err := r.Columns()
	if err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
		return results
	}
	tmp := make(map[string]int64)
	// 遍历表头每一个表头字段，给初始值
	for i, item := range cs {
		// 判断字段是否已存在tmp中
		if v, _ := tmp[item]; v != 0 {
			// 如果已存在
			cs[i] = item + strconv.FormatInt(v, 10)
		}
		tmp[item] = tmp[item] + 1
	}
	// 取下一条记录
	for r.Next() {
		rs := make([]interface{}, len(cs))  // 存储查询到的数据值
		rst := make([]interface{}, len(cs)) // 用以存储rs每一项的指针
		for i := range rs {
			rst[i] = &rs[i]
		}
		// 映射为go结构体
		if err := r.Scan(rst...); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.QueryDB. Err: `%v`", trackId, err)
			return results
		}
		// 因为映射后的结构体只包含值，不包含键，所以需要单独映射为键值对
		m := make(map[string]interface{})
		// 遍历每一个表头，i是索引，k是键，rs是存储的值列表，组装成一个键值对map，形成一条数据记录
		for i, k := range cs {
			m[k] = cast.ToString(rs[i])
		}
		// 将数据存储到结果列表中
		results = append(results, m)
	}
	return results
}

func BatchSaveDB(trackId int64, db *sql.DB, insert, values string, data [][]interface{}) {
	/**
	insert : INSERT INTO test(a,b,c)
	values : ('%v','%v','%v')
	*/
	saveDB := func(sql string) {
		sql = strings.TrimRight(sql, ",")
		if _, err := db.Exec(sql); err != nil {
			logger.Pre().Errorf("[%d] pkg.helpers.BatchSaveDB. Err: `%v`", trackId, err)
		}
	}
	// 拼接SQL
	insert = insert + "VALUES"
	rawSQL := insert
	for i, d := range data {
		rawSQL += fmt.Sprintf(values, d...) + ","
		if i%1000 == 0 {
			saveDB(rawSQL)
			rawSQL = insert
		}
	}
	if rawSQL != insert {
		saveDB(rawSQL)
	}
}

func BatchDeleteDB(trackId int64, db *sql.DB, sql string) {
	if _, err := db.Exec(sql); err != nil {
		logger.Pre().Errorf("[%d] pkg.helpers.BatchDeleteDB. Err: `%v`", trackId, err)
	}
}

func NoBugs() {
	logger.Pre().Infof(" ......................阿弥陀佛......................\n" +
		"                       _oo0oo_                      \n" +
		"                      o8888888o                     \n" +
		"                      88\" . \"88                     \n" +
		"                      (| -_- |)                     \n" +
		"                      0\\  =  /0                     \n" +
		"                   ___/‘---’\\___                   \n" +
		"                  .' \\|       |/ '.                 \n" +
		"                 / \\\\|||  :  |||// \\                \n" +
		"                / _||||| -卍-|||||_ \\               \n" +
		"               |   | \\\\\\  -  /// |   |              \n" +
		"               | \\_|  ''\\---/''  |_/ |              \n" +
		"               \\  .-\\__  '-'  ___/-. /              \n" +
		"             ___'. .'  /--.--\\  '. .'___            \n" +
		"         .\"\" ‘<  ‘.___\\_<|>_/___.’>’ \"\".          \n" +
		"       | | :  ‘- \\‘.;‘\\ _ /’;.’/ - ’ : | |        \n" +
		"         \\  \\ ‘_.   \\_ __\\ /__ _/   .-’ /  /        \n" +
		"    =====‘-.____‘.___ \\_____/___.-’___.-’=====     \n" +
		"                       ‘=---=’                      \n" +
		"                                                    \n" +
		"....................佛祖保佑 ,永无BUG...................")
}
