package helpers

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

// 拼接int类型的切片为str
func JoinInt64(i []int64, sep string) string {
	s := make([]string, 0)
	for _, ii := range i {
		s = append(s, fmt.Sprintf("%v", ii))
	}
	return strings.Join(s, sep)
}

func JoinFloat64(i []float64, sep string) string {
	s := make([]string, 0)
	for _, ii := range i {
		s = append(s, fmt.Sprintf("%v", ii))
	}
	return strings.Join(s, sep)
}

func JoinString(i []string, sep string) string {
	s := make([]string, 0)
	for _, ii := range i {
		s = append(s, fmt.Sprintf("'%v'", ii))
	}
	return strings.Join(s, sep)
}

func Contains(items []string, val string) bool {
	for _, item := range items {
		if item == val {
			return true
		}
	}
	return false
}

// 判断值是否在数组中
func InArray(item interface{}, Array []interface{}) bool {
	var flag = false
	for _, value := range Array {
		if value == item {
			flag = true
		}
	}
	return flag
}

type Province struct {
	Value    string
	Label    string
	Children []City
}

type City struct {
	Value    string
	Label    string
	Children []County
}

type County struct { // 县
	Value    string
	Label    string
	Ssqename string
}

// 映射本地区域中文名称
func GetRegionName(regions []interface{}) (string, string, string, string, error) {
	pathCurrent, _ := os.Getwd()
	regionPath := filepath.Join(pathCurrent, "pkg/regions/region.json")
	filePtr, err := os.Open(regionPath)
	if err != nil {
		fmt.Printf("Open regions failed [Err:%s]", err.Error())
		return "", "", "", "", err
	}
	defer filePtr.Close()
	//fmt.Println(regionPath)
	// 读取文件内容
	fileByte, err := ioutil.ReadFile(regionPath)
	if err != nil {
		return "", "", "", "", err
	}
	var province []Province
	err = json.Unmarshal(fileByte, &province)
	if err != nil {
		return "", "", "", "", err
	}
	//fmt.Println("转换后的结构体是:", province)
	var regionName = make([]string, 0)
	var regionName1, regionName2, regionName3 string
	if len(regions) >= 1 {
		for _, pro := range province {
			if pro.Value == regions[0] {
				regionName1 = pro.Label
				regionName = append(regionName, pro.Label)
				if len(regions) >= 2 {
					for _, city := range pro.Children {
						if city.Value == regions[1] {
							regionName2 = city.Label
							regionName = append(regionName, city.Label)
							if len(regions) >= 3 {
								for _, cou := range city.Children {
									if cou.Value == regions[2] {
										regionName3 = cou.Label
										regionName = append(regionName, cou.Label)
									}
								}
							}
						}
					}
				}
			}
		}
	}
	regionRes := strings.Join(regionName, "")
	return regionRes, regionName1, regionName2, regionName3, nil
}

// 去重
func RemoveDuplicateElement(languages []int64) []int64 {
	result := make([]int64, 0, len(languages))
	temp := map[int64]struct{}{}
	for _, item := range languages {
		if _, ok := temp[item]; !ok { //如果字典中找不到元素，ok=false，!ok为true，就往切片中append元素。
			temp[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

// 合并map
func MergeMap(mObj ...map[int64]map[string]interface{}) map[int64]map[string]interface{} {
	newObj := map[int64]map[string]interface{}{}
	for _, m := range mObj {
		for k, v := range m {
			newObj[k] = v
		}
	}
	return newObj
}
