package helpers

import (
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"time"
)

// 增加空数据
func DddEmptySales(query *model.CommonRequest, resp *model.Response) {
	// 当查询门店没有数据时，就增加一个空数据进去
	searchIds := query.RegionSearchIds
	if query.TagType == "SUMMARY" {
		for _, storeId := range searchIds {
			var flag = false
			for _, row := range resp.Rows {
				r := row.(map[string]interface{})
				regionId := cast.ToString(r["region_id"])
				if storeId == regionId {
					flag = true
				}
			}
			if !flag {
				resp.Rows = append(resp.Rows, map[string]interface{}{
					"region_id": storeId,
				})
			}
			if len(resp.Rows) >= query.Limit {
				break
			}
		}
	} else {
		// 按照日期添加空数据
		var timeDurations = make([]string, 0)
		for true {
			if query.Start.Before(query.End) {
				timeDurations = append(timeDurations, query.Start.Format("2006-01-02"))
			} else {
				break
			}
			var timeStr = fmt.Sprintf("24h")
			d, _ := time.ParseDuration(timeStr)
			query.Start = query.Start.Add(d)
		}
		// 遍历门店和日期组合，是否能在结果中查询到
		for _, timeStr := range timeDurations {
			for _, storeId := range searchIds {
				var flag = false
				for _, row := range resp.Rows {
					r := row.(map[string]interface{})
					regionId := cast.ToString(r["region_id"])
					periodGroup := cast.ToString(r["period_group"])
					if storeId == regionId && periodGroup == timeStr {
						flag = true
					}
				}
				if !flag {
					resp.Rows = append(resp.Rows, map[string]interface{}{
						"region_id":    storeId,
						"period_group": timeStr,
					})
				}
			}
		}
	}
	summaryMap := cast.ToStringMap(resp.Summary)
	if summaryMap == nil {
		summaryMap = make(map[string]interface{})
	}
	//summaryMap["total"] = len(resp.Rows)
}
