package multilang

const (
	UnselectedDate        = "UnselectedDate"
	DateIntervalTooLong   = "DateIntervalTooLong"
	PermissionQueryFail   = "PermissionQueryFailure"
	InsufficientAuthority = "InsufficientAuthority"
)

var errorMessages = map[string]map[string]string{
	"zh-CN": {
		UnselectedDate:        "请选择日期",
		DateIntervalTooLong:   "日期间隔不能超过31天",
		PermissionQueryFail:   "查询数据权限失败",
		InsufficientAuthority: "数据权限校验失败，权限不足",
	},
	"en-US": {
		UnselectedDate:        "Please select a date",
		DateIntervalTooLong:   "The date interval cannot exceed 31 days",
		PermissionQueryFail:   "Failed to query the data permission.",
		InsufficientAuthority: "The data permission verification fails because the permission is insufficient",
	},
}

func GetErrorMessage(errType string, lang string) string {
	if lang == "" {
		lang = "zh-CN"
	}
	if messages, ok := errorMessages[lang]; ok {
		if message, ok := messages[errType]; ok {
			return message
		}
	}
	return "Unknown error"
}
