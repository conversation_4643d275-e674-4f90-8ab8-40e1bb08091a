package table_order

import (
	"context"
	"errors"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
)

func UpdateAllTableInfos(ctx context.Context, pids []string, sid, uid string) (err error) {
	for _, pid := range pids {
		err0 := updateTableInfos(ctx, pid, uid)
		if err0 != nil {
			logrus.WithFields(logrus.Fields{
				"partner_id": pid,
				"error":      err,
			}).Errorln("拉取桌位数据失败")
		}
		if err == nil {
			err = err0
		} else {
			// 合并两个错误
			err = errors.New(err.Error() + ";" + err0.Error())
		}
	}
	return err
}

func updateTableInfos(ctx context.Context, pid string, uid string) (error) {
	tableInfos, err := queryTableInfos(ctx, pid, uid)
	if err != nil {
		return err
	}

	tableCaches := make([]*model.TableCache, len(tableInfos))
	for i, info := range tableInfos {
		tableCaches[i] = info.toTableCache(pid)
	}

	// 存储数据
	if err = repo.DefaultMetadataCache.UpdateAllTable(ctx, cast.ToInt64(pid), tableCaches); err != nil {
		return err
	}
	return nil
}
