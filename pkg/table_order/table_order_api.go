package table_order

import (
    "bytes"
    "context"
    "encoding/json"
    "errors"
    "github.com/sirupsen/logrus"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "io/ioutil"
    "net/http"
    "time"
)

var client *http.Client

func InitClient() {
    client = &http.Client{
        Timeout: time.Duration(config.DefaultConfig.TableOrder.Timeout) * time.Second,
    }
}

func queryTableInfos(ctx context.Context, pid string, uid string) ([]*tableInfo, error) {
    req, err := http.NewRequest(http.MethodPost, config.DefaultConfig.TableOrder.ApiTableList, bytes.NewReader([]byte{}))
    if err != nil {
        return nil, err
    }
    req.Header.Add("partner_id", pid)
    req.Header.Add("user_id", uid)
    req.Header.Add("Content-Type", "application/json")
    resp, err := client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return nil, err
    }
    logrus.WithFields(logrus.Fields{
        "partner_id": pid,
        "response":   string(body),
    }).Infoln("拉取桌位数据接口响应")

    res := &tableInfoResponse{}
    err = json.Unmarshal(body, res)
    if err != nil {
        return nil, err
    }

    if res.StatusCode != 0 {
        return nil, errors.New(res.Description)
    }

    return res.Payload, nil
}


