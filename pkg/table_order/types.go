package table_order

import (
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
)

type tableInfoResponse struct {
	StatusCode  int32        `json:"status_code"`
	Description string       `json:"description"`
	Payload     []*tableInfo `json:"payload"`
}

type tableInfo struct {
	Name          string `json:"name"`          // 桌位名称
	Qty           int32  `json:"qty"`           // 桌位数
	StoreId       string `json:"storeId"`       // 门店id
	TableAreaId   int64  `json:"tableAreaId"`   // 区域id
	TableAreaName string `json:"tableAreaName"` // 区域名称
	TableId       int64  `json:"tableId"`       // 桌位id
	TableMode     int32  `json:"tableMode"`     // 桌位模式 是否培训桌位；0：否，1：是
}

func (t *tableInfo) toTableCache(partnerId string) *model.TableCache {
	return &model.TableCache{
		Id:        t.TableId,
		PartnerId: cast.ToInt64(partnerId),
		StoreId:   cast.ToInt64(t.StoreId),
		ZoneId:    t.TableAreaId,
		ZoneName:  t.TableAreaName,
		TableName: t.Name,
		TableSeat: t.Qty,
		TableMode: t.TableMode,
	}
}
