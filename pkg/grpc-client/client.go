package grpc_client

import (
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/saas/auth-golang/auth"
	authPb "gitlab.hexcloud.cn/saas/auth-golang/pb/auth"
	"log"
)

var HexAuth *auth.HexAuth

func InitHexAuth() {
	hexAuth, err := auth.NewHexAuth(config.DefaultConfig.HexAuth.Address)
	//log.Println("config.DefaultConfig.HexAuth.Address",config.DefaultConfig.HexAuth.Address)
	logrus.Infof("config.DefaultConfig.HexAuth.Address %v", config.DefaultConfig.HexAuth.Address)
	if err != nil {
		log.Fatal("connect to hexauth service error")
	}
	HexAuth = hexAuth
}

func QuerySubjectResource(ctx context.Context, domain string, partnerId int64, userId int64, schema string) (*authPb.SubjectResourceResponse, error) {
	ctx = context.WithValue(ctx, "partner_id", cast.ToUint64(partnerId))
	ctx = context.WithValue(ctx, "user_id", cast.ToUint64(userId))
	userID := fmt.Sprintf("user_id:%d", userId)
	logrus.Infof("QuerySubjectResource %v", HexAuth)
	in := &authPb.QuerySubjectResourceRequest{
		Subject:        userID,
		ResourceSchema: schema,
		Scope: &authPb.Scope{
			PartnerId: cast.ToUint64(partnerId),
			Domain:    domain,
		},
	}
	return HexAuth.QuerySubjectResource(ctx, in)
}
