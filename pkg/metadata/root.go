package entity

import (
	"context"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/helpers"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
)

func GetMaxDelayDays() int64 {
	return 62
}

// 获取区域信息map
func GetAllStoreMaps(ctx context.Context, query *model.CommonRequest) map[int64]map[string]interface{} {
	schema := "store"
	regionMaps, err := GetFieldMap(ctx, schema, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}
	// 通过region和本地的区域映射，增加中文地区名称
	var storeMaps = make(map[int64]interface{})
	for id, storeInfo := range regionMaps {
		stringMap := cast.ToStringMap(storeInfo)
		regions := cast.ToSlice(stringMap["region"])
		if regions != nil {
			regionName, _, _, _, _ := helpers.GetRegionName(regions)
			storeInfo["region_name"] = regionName
		}
		storeMaps[id] = storeInfo
	}
	return regionMaps
}


// 获取区域信息map
func GetAllMaps(ctx context.Context, query *model.CommonRequest, schema string) map[int64]map[string]interface{} {
	regionMaps, err := GetFieldMap(ctx, schema, query.Lan)
	if err != nil {
		logger.Pre().Error("从主档 GetIdsFieldMap 出错:", err)
	}

	return regionMaps
}
