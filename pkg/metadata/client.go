package entity

import (
	"context"
	"encoding/json"
	"errors"
	fmt "fmt"
	"github.com/golang/protobuf/jsonpb"
	"github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/cam"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/interceptor"
	otgrpc "gitlab.hexcloud.cn/histore/sales-report/pkg/tracing/go-grpc"
	"gitlab.hexcloud.cn/histore/sales-report/repo"
	"google.golang.org/protobuf/types/known/structpb"
	math "math"
	"strconv"
	"strings"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"

	"github.com/SmallTianTian/go-tools/slice"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
	grpc "google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

var DefaultClient EntityServiceClient

func Init() {
	// 设置超时时间2min
	timeout := 2 * time.Minute
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	conn, err := grpc.DialContext(ctx,
		config.DefaultConfig.ConnStr.Metadata,
		grpc.WithInsecure(),
		grpc.WithChainUnaryInterceptor(interceptor.TimeoutInterceptor(timeout)),
		grpc.WithUnaryInterceptor(
			otgrpc.OpenTracingClientInterceptor(),
		),
		grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(math.MaxInt32), grpc.MaxCallSendMsgSize(math.MaxInt32)))
	if err != nil {
		fmt.Println("Init report conn failed.")
		return
	}
	DefaultClient = NewEntityServiceClient(conn)
}

func GetRegionAndStoreIdsByType(ctx context.Context, _type string) (regions, stores []uint64, err error) {
	var info *oauth.UserAccount
	var resp *ListEntityResponse
	if info, err = oauth.GetUserinfo(ctx); err != nil {
		return
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))

	filters := map[string]interface{}{}
	filters["related_user_id__eq"] = cast.ToString(info.ID)
	filters["status__eq"] = "ENABLED"
	filtersPb, _ := utils.Map2Struct(filters)
	queryRequest := &ListEntityRequest{
		SchemaName:   "STAFF",
		ReturnFields: "id",
		Relation:     "store," + _type,
		Limit:        -1,
		Filters:      filtersPb,
	}
	// get staff by user id
	if resp, err = DefaultClient.ListEntity(ctx, queryRequest); err != nil {
		return
	}
	for _, item := range resp.Rows {
		if etContent, err := utils.Struct2Map(item.Fields); err == nil {
			stores = append(stores, utils.GetUint64ArrayFromJSON(utils.GetMapFromJSON(etContent, "relation"), "store")...)
			regions = append(regions, utils.GetUint64ArrayFromJSON(utils.GetMapFromJSON(etContent, "relation"), _type)...)
		}
	}
	if len(regions) > 0 {
		queryRequest = &ListEntityRequest{
			SchemaName: strings.ToUpper(_type),
			Limit:      -1,
		}
		if resp, err = DefaultClient.ListEntity(ctx, queryRequest); err != nil {
			return
		}
		p2ids := make(map[uint64][]uint64)
		for _, item := range resp.Rows {
			pid := cast.ToUint64(item.Fields.Fields["parnet_id"].GetStringValue())
			p2ids[pid] = append(p2ids[pid], item.Id)
		}
		// remove top region
		delete(p2ids, 0)
		for _, item := range regions[:] {
			regions = append(regions, p2ids[item]...)
		}
		regions = slice.DistinctUint64Slice(regions)

		regionIdsStr := make([]string, 0, len(regions))
		for _, region := range regions {
			regionIdsStr = append(regionIdsStr, cast.ToString(region))
		}
		relationFiltersPb, _ := utils.Map2Struct(map[string]interface{}{_type: regionIdsStr})
		queryRequest = &ListEntityRequest{
			SchemaName:      "STORE",
			ReturnFields:    "id",
			Limit:           -1,
			RelationFilters: relationFiltersPb,
		}
		if resp, err = DefaultClient.ListEntity(ctx, queryRequest); err != nil {
			return
		}
		for _, r := range resp.Rows {
			stores = append(stores, r.Id)
		}
	}
	stores = slice.DistinctUint64Slice(stores)
	return
}

func GetIdsFieldMap(ctx context.Context, schema string, ids []int64, lan string) (map[int64]map[string]interface{}, error) {
	result := make(map[int64]map[string]interface{})
	// 获取用户信息
	info, err := oauth.GetUserinfo(ctx)
	if err != nil || info == nil {
		return result, fmt.Errorf("Err: %v, Oauth info: %v", err, info)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))
	ids = slice.DistinctInt64Slice(ids)
	uids := make([]uint64, 0, len(ids))
	for _, id := range ids {
		uids = append(uids, uint64(id))
	}
	req := &ListEntityRequest{
		Ids:        uids,
		SchemaName: schema,
		State:      "all",
		Relation:   "all",
		Limit:      -1,
	}
	if lan != "" {
		req.Lan = lan
	}
	resp, err := DefaultClient.ListEntity(ctx, req)
	if err != nil {
		return result, err
	}
	for _, item := range resp.Rows {
		if result[int64(item.Id)], err = utils.Struct2Map(item.Fields); err != nil {
			return result, err
		}
	}
	return result, nil
}

func GetCodesFieldMap(ctx context.Context, schema string, codes []int64, lan string) (map[int64]map[string]interface{}, error) {
	result := make(map[int64]map[string]interface{})
	// 获取用户信息
	info, err := oauth.GetUserinfo(ctx)
	if err != nil || info == nil {
		return result, fmt.Errorf("Err: %v, Oauth info: %v", err, info)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))
	codes = slice.DistinctInt64Slice(codes)
	uids := make([]uint64, 0, len(codes))
	for _, id := range codes {
		uids = append(uids, uint64(id))
	}
	filters := map[string]interface{}{}
	filters["code__in"] = codes
	filtersPb, _ := utils.Map2Struct(filters)
	req := &ListEntityRequest{
		SchemaName: schema,
		Filters:    filtersPb,
		State:      "all",
		Relation:   "all",
		Limit:      -1,
	}
	if lan != "" {
		req.Lan = lan
	}
	resp, err := DefaultClient.ListEntity(ctx, req)
	if err != nil {
		return result, err
	}
	for _, item := range resp.Rows {
		if result[cast.ToInt64(item.Fields.Fields["code"].GetStringValue())], err = utils.Struct2Map(item.Fields); err != nil {
			return result, err
		}
	}
	return result, nil
}

func ParseListEntity(listEntityResp *ListEntityResponse) ([]map[string]interface{}, error) {
	entities := make([]map[string]interface{}, 0, len(listEntityResp.Rows))
	for _, entity := range listEntityResp.Rows {
		entity, err := ParseOneEntity(entity)
		if err != nil {
			return nil, err
		}
		entities = append(entities, entity)
	}

	return entities, nil
}

// 将entity数据打平，全部填充到entity.Fields.Fields中, 并转换为map
func ParseOneEntity(entity *Entity) (map[string]interface{}, error) {
	if entity.Fields == nil {
		logrus.WithFields(logrus.Fields{"entityId": entity.Id, "partnerId": entity.PartnerId, "schemaName": entity.SchemaName}).Warn("entity fields is nil")
		entity.Fields, _ = structpb.NewStruct(nil) // 参数为空，NewStruct不可能出错，可忽略错误
	}

	entity.Fields.Fields["id"] = structpb.NewStringValue(strconv.FormatUint(entity.Id, 10))
	entity.Fields.Fields["partner_id"] = structpb.NewStringValue(strconv.FormatUint(entity.PartnerId, 10))
	entity.Fields.Fields["created"] = structpb.NewStringValue(entity.Created)
	entity.Fields.Fields["created_by"] = structpb.NewStringValue(entity.CreatedBy)
	entity.Fields.Fields["updated"] = structpb.NewStringValue(entity.Updated)
	entity.Fields.Fields["updated_by"] = structpb.NewStringValue(entity.UpdatedBy)
	if entity.State != "" {
		entity.Fields.Fields["data_state"] = structpb.NewStringValue(entity.State)
	}
	if entity.ParentId != 0 {
		entity.Fields.Fields["parent_id"] = structpb.NewStringValue(strconv.FormatUint(entity.ParentId, 10))
	}
	if entity.Parent != nil {
		parent := make(map[string]interface{})
		ParseParent(entity, parent)
		parentValue, err := structpb.NewValue(parent)
		if err != nil {
			return nil, fmt.Errorf("解析parent失败:, partner_id:%d, entity_id:%d err:%v", entity.PartnerId, entity.Id, err)
		}
		entity.Fields.Fields["parent"] = parentValue
	}

	return entity.Fields.AsMap(), nil
}

// 递归解析父级
func ParseParent(entity *Entity, result map[string]interface{}) {
	if entity.Parent == nil {
		return
	}
	for key, value := range entity.Parent.Fields.AsMap() {
		result[key] = value
	}
	result["id"] = strconv.FormatUint(entity.Parent.Id, 10)
	if entity.Parent.Parent != nil {
		parent := make(map[string]interface{})
		result["parent"] = parent
		ParseParent(entity.Parent, parent)
	}
}

func GetProductCategory(ctx context.Context) ([]map[string]interface{}, error) {
	// 获取用户信息
	info, err := oauth.GetUserinfo(ctx)
	if err != nil || info == nil {
		return nil, fmt.Errorf("Err: %v, Oauth info: %v", err, info)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))
	req := &ListEntityRequest{
		SchemaName:     "product_category",
		Relation:       "all",
		IncludeParents: true,
		Limit:          -1,
	}
	resp, err := DefaultClient.ListEntity(ctx, req)
	if err != nil {
		return nil, err
	}
	data, _ := json.Marshal(&resp)
	logrus.Infof("GetProductCategory resp:%v", string(data))
	return ParseListEntity(resp)
}

func GetIdByCodes(ctx context.Context, schema string, codes []string, lan string) (map[string]int64, error) {
	result := make(map[string]int64)
	// 获取用户信息
	info, err := oauth.GetUserinfo(ctx)
	if err != nil || info == nil {
		return result, fmt.Errorf("Err: %v, Oauth info: %v", err, info)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))

	filters := map[string]interface{}{}
	filters["code__in"] = codes
	filtersPb, _ := utils.Map2Struct(filters)
	req := &ListEntityRequest{
		SchemaName: schema,
		Filters:    filtersPb,
		State:      "all",
		Limit:      -1,
	}
	if lan != "" {
		req.Lan = lan
	}
	resp, err := DefaultClient.ListEntity(ctx, req)
	logger.Pre().Infof("GetIdByCodes: %v", resp)
	if err != nil {
		return result, err
	}
	for _, item := range resp.Rows {
		if v, ok := item.Fields.Fields["code"]; ok && v != nil {
			result[v.GetStringValue()] = cast.ToInt64(item.Id)
		}
	}
	return result, nil
}

func GetFieldMap(ctx context.Context, schema string, lan string) (map[int64]map[string]interface{}, error) {
	result := make(map[int64]map[string]interface{})
	// 获取用户信息
	info, err := oauth.GetUserinfo(ctx)
	if err != nil || info == nil {
		return result, fmt.Errorf("Err: %v, Oauth info: %v", err, info)
	}
	ctx = metadata.AppendToOutgoingContext(ctx, "partner_id", strconv.FormatUint(info.PartnerID, 10), "scope_id", strconv.FormatUint(0, 10), "user_id", strconv.FormatUint(info.ID, 10))
	req := &ListEntityRequest{
		SchemaName: schema,
		State:      "all",
		Relation:   "all",
		Limit:      -1,
	}
	if lan != "" {
		req.Lan = lan
	}
	resp, err := DefaultClient.ListEntity(ctx, req)
	if err != nil {
		return result, err
	}
	for _, item := range resp.Rows {
		if result[int64(item.Id)], err = utils.Struct2Map(item.Fields); err != nil {
			return result, err
		}
	}
	return result, nil
}

func RefreshStartUpInfo(ctx context.Context, pids []string, sid, uid string) {
	if len(config.PartnerConfs) != 0 {
		return
	}
	mealSegmentMap := make(map[int64]map[string]struct{})
	for _, pid := range pids {
		// 修改查询主档门店信息，并更新门店缓存表store_caches实现逻辑
		md := metadata.Pairs("partner_id", pid, "scope_id", sid, "user_id", uid)
		ctx = metadata.NewOutgoingContext(ctx, md)
		resp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "store",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			logger.Pre().Errorf("RefreshStartUpInfo error: %v\n", err)
			continue
		}
		for _, item := range resp.Rows {
			putMealSegments(item, mealSegmentMap)
		}
		config.UpdateMealSegments(mealSegmentMap)
	}

	GetAllChannelInfo(ctx, pids, sid, uid)

	cam.UpdateCouponChannels(ctx, pids, sid, uid)
}

func GetAllStoreInfo(ctx context.Context, pids []string, sid, uid string) error {
	var rows = make([]*Entity, 0, 20)
	var geoMs = make(map[int64][]int64, 10)
	var branchMs = make(map[int64][]int64)
	// 增加加盟商区域   wangheng 2022-02-15---
	var franchiseeMs = make(map[int64][]int64)
	var idMap = make(map[uint64]bool)
	mealSegmentMap := make(map[int64]map[string]struct{})
	for _, pid := range pids {
		// 修改查询主档门店信息，并更新门店缓存表store_caches实现逻辑
		md := metadata.Pairs("partner_id", pid, "scope_id", sid, "user_id", uid)
		ctx = metadata.NewOutgoingContext(ctx, md)
		resp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "geo-region",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}
		geoM := entity2ParentMap(resp.Rows)
		for key, value := range geoM {
			geoMs[key] = value
		}
		if resp, err = DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "branch-region",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		}); err != nil {
			return err
		}
		branchM := entity2ParentMap(resp.Rows)
		for key, value := range branchM {
			branchMs[key] = value
		}
		// 加盟商区域
		if resp, err = DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "franchisee-region",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		}); err != nil {
			return err
		}
		franchiseeM := entity2ParentMap(resp.Rows)
		for key, value := range franchiseeM {
			franchiseeMs[key] = value
		}
		resp, err = DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "store",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}
		logger.Pre().Debugf("查到的主档门店ids: %d\n", len(resp.Rows))

		for _, store := range resp.Rows {
			if !idMap[store.Id] {
				rows = append(rows, store)
				idMap[store.Id] = true
			} else {
				logger.Pre().Debugf("重复的主档门店id: %d\n", store.Id)
			}
		}
	}
	var records []*model.StoreCache
	for _, item := range rows {
		putMealSegments(item, mealSegmentMap)
		records = append(records, entity2StoreCache(item, geoMs, branchMs, franchiseeMs))
	}
	config.UpdateMealSegments(mealSegmentMap)
	// 更新所有门店数据
	return repo.DefaultMetadataCache.UpdateAllStore(ctx, records)
}

func putMealSegments(en *Entity, m map[int64]map[string]struct{}) {
	if en.Fields == nil || en.Fields.Fields == nil {
		return
	}
	if en.Fields.Fields["meal_period"] == nil {
		return
	}
	mealPeriod := en.Fields.Fields["meal_period"]
	if mealPeriod.GetStructValue() == nil {
		return
	}
	mp := mealPeriod.GetStructValue()
	if mp.Fields == nil || mp.Fields["data"] == nil {
		return
	}
	data := mp.Fields["data"]
	if data.GetListValue() == nil {
		return
	}
	values := data.GetListValue().GetValues()

	for _, v := range values {
		if v.GetStructValue() == nil {
			continue
		}
		sv := v.GetStructValue()
		if sv.Fields == nil {
			continue
		}
		if sv.Fields["name"] == nil {
			continue
		}
		name := sv.Fields["name"].GetStringValue()
		if _, ok := m[int64(en.PartnerId)]; !ok {
			m[int64(en.PartnerId)] = make(map[string]struct{})
		}
		if name != "" {
			m[int64(en.PartnerId)][name] = struct{}{}
		}
	}
}

func entity2ParentMap(ens []*Entity) map[int64][]int64 {
	tmp := make(map[uint64]*Entity)
	for _, item := range ens {
		tmp[item.Id] = item
	}
	result := make(map[int64][]int64)
	for _, item := range ens {
		pid := item.ParentId
		if pid == 0 {
			continue
		}
		if v, in := result[int64(pid)]; in {
			result[int64(item.Id)] = append(v, int64(pid))
			continue
		}

		// 剩下的就懒得优化了
		pars := []int64{int64(pid)}
		for p := tmp[pid]; p != nil; p = tmp[pid] {
			if p.ParentId == 0 {
				break
			}
			pid = p.ParentId
			pars = append(pars, int64(p.ParentId))
		}
		for i, j := 0, len(pars)-1; i < j; i, j = i+1, j-1 {
			pars[i], pars[j] = pars[j], pars[i]
		}
		result[int64(item.Id)] = pars
	}
	return result
}

func entity2StoreCache(en *Entity, geoM, branchM, franchiseeM map[int64][]int64) (sc *model.StoreCache) {
	sc = &model.StoreCache{Id: int64(en.Id)}
	relation := en.Fields.Fields["relation"]
	openStatus := en.Fields.Fields["open_status"]
	storeName := en.Fields.Fields["name"]
	storeCode := en.Fields.Fields["code"]
	currency := en.Fields.Fields["currency"]
	timeZone := en.Fields.Fields["time_zone"]
	sc.PartnerId = cast.ToInt64(en.PartnerId)
	//logger.Pre().Debugf("get store info is `%s`", en)
	sc.StoreName = storeName.GetStringValue()
	sc.StoreCode = storeCode.GetStringValue()
	sc.OpenStatus = openStatus.GetStringValue()
	sc.TimeZone = timeZone.GetStringValue()
	if sc.TimeZone == "" {
		sc.TimeZone = "Asia/Hong_Kong"
	}
	if relation == nil || relation.GetStructValue() == nil {
		return
	}
	rf := relation.GetStructValue().Fields
	br := rf["branch_region"].GetStringValue()
	gr := rf["geo_region"].GetStringValue()
	// 加盟商区域
	fr := rf["franchisee_region"].GetStringValue()
	sc.StoreType = rf["store_type"].GetStringValue()
	pb, _ := strconv.ParseInt(br, 10, 64)
	pg, _ := strconv.ParseInt(gr, 10, 64)
	pf, _ := strconv.ParseInt(fr, 10, 64)
	sc.Branchs = append(branchM[pb], pb)
	sc.Geos = append(geoM[pg], pg)
	sc.Franchisees = append(franchiseeM[pf], pf)
	if len(sc.Branchs) > 0 {
		sc.Branch0 = sc.Branchs[0]
		if len(sc.Branchs) > 1 {
			sc.Branch1 = sc.Branchs[1]
			if len(sc.Branchs) > 2 {
				sc.Branch2 = sc.Branchs[2]
			}
		}
	}
	if len(sc.Geos) > 0 {
		sc.Geo0 = sc.Geos[0]
		if len(sc.Geos) > 1 {
			sc.Geo1 = sc.Geos[1]
			if len(sc.Geos) > 2 {
				sc.Geo2 = sc.Geos[2]
			}
		}
	}
	// 加盟商区域
	if len(sc.Franchisees) > 0 {
		sc.Franchisee0 = sc.Franchisees[0]
		if len(sc.Franchisees) > 1 {
			sc.Franchisee1 = sc.Franchisees[1]
			if len(sc.Franchisees) > 2 {
				sc.Franchisee2 = sc.Franchisees[2]
			}
		}
	}
	sc.Regions = make([]string, 0, 3)
	region := en.Fields.Fields["region"]
	if region != nil && region.GetListValue() != nil && region.GetListValue().GetValues() != nil {
		values := region.GetListValue().GetValues()
		for _, v := range values {
			sv := v.GetStringValue()
			if sv != "" {
				sc.Regions = append(sc.Regions, sv)
			}
		}
	}
	// 地理位置
	if len(sc.Regions) > 0 {
		sc.Region0 = sc.Regions[0]
		if len(sc.Regions) > 1 {
			sc.Region1 = sc.Regions[1]
			if len(sc.Regions) > 2 {
				sc.Region2 = sc.Regions[2]
			}
		}
	}
	// 取出门店所属公司 2021-08-03 -------------------wangheng
	company := rf["company_info"].GetStringValue()
	companyInfo, _ := strconv.ParseInt(company, 10, 64)
	sc.CompanyInfo = companyInfo
	sc.Currency = currency.GetStringValue()
	// entity to map
	//fieldsMap, _ := utils.Struct2Map(en.Fields)
	//bs, err := GetBusinessHour(fieldsMap, time.Now().Local().Format(config.DateFormat))
	//if err != nil {
	//	logger.Pre().Errorf("get business hour failed: %v", err)
	//}
	//sc.BusinessHours = bs
	return
}

func GetAllChannelInfo(ctx context.Context, pids []string, sid, uid string) error {
	m := make(map[int64][]config.PartnerChannel)
	for _, pid := range pids {
		md := metadata.Pairs("partner_id", pid, "scope_id", sid, "user_id", uid)
		ctx = metadata.NewOutgoingContext(ctx, md)
		resp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "channel_company",
			State:      "enabled",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}
		channelMaps := make(map[int64]map[string]interface{})
		for _, item := range resp.Rows {
			if channelMaps[int64(item.Id)], err = utils.Struct2Map(item.Fields); err != nil {
				return err
			}
		}
		channels := make([]config.PartnerChannel, 0)
		for id, v := range channelMaps {
			if v["name"] != nil && v["auth_status"] != nil && cast.ToString(v["auth_status"]) == "AUTHED" {
				channels = append(channels, config.PartnerChannel{ChannelId: id, ChannelName: cast.ToString(v["name"])})
			}
		}
		m[cast.ToInt64(pid)] = channels
	}
	config.UpdateChannels(m)
	return nil
}

func GetAllProductInfo(ctx context.Context, pids []string, sid, uid string) error {
	var rows = make([]*Entity, 0)
	var pcMs = make(map[int64][]int64)
	var productHasCup = make(map[uint64]bool)
	for _, pid := range pids {
		// 修改查询主档商品信息，并更新商品缓存表product_caches实现逻辑
		md := metadata.Pairs("partner_id", pid, "scope_id", sid, "user_id", uid)
		ctx = metadata.NewOutgoingContext(ctx, md)
		resp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "product-category",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}
		pcM := entity2ParentMap(resp.Rows)
		// 缓存parent数据
		for key, value := range pcM {
			pcMs[key] = value
		}
		if resp, err = DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "sku",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		}); err != nil {
			return err
		}
		//
		for _, pro := range resp.Rows {
			var flag = false
			for _, r := range rows {
				if pro.Id == r.Id {
					flag = true
					break
				}
			}
			if flag == false {
				rows = append(rows, pro)
			}
		}
		if resp, err = DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "product",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		}); err != nil {
			return err
		}
		for _, row := range resp.Rows {
			if row.Fields != nil && row.Fields.Fields != nil && row.Fields.Fields["channel_has_cup_counted"] != nil {
				productHasCup[row.Id] = row.Fields.Fields["channel_has_cup_counted"].GetBoolValue()
			}
		}
	}
	var records []*model.ProductCache
	for _, item := range rows {
		records = append(records, entity2ProductCache(item, pcMs, productHasCup))
	}
	return repo.DefaultMetadataCache.UpdateAllProduct(ctx, records)
}

func entity2ProductCache(en *Entity, pcM map[int64][]int64, productHasCup map[uint64]bool) (sc *model.ProductCache) {
	sc = &model.ProductCache{Id: int64(en.Id)}
	relation := en.Fields.Fields["relation"]
	if relation == nil || relation.GetStructValue() == nil {
		return
	}
	rf := relation.GetStructValue().Fields
	pc := rf["product_category"]
	if pc == nil {
		return
	}
	pci, _ := strconv.ParseInt(pc.GetStringValue(), 10, 64)
	sc.Category = pci
	sc.Categories = append(pcM[pci], pci)
	sc.PartnerId = cast.ToInt64(en.PartnerId)
	if len(sc.Categories) > 0 {
		sc.Category0 = sc.Categories[0]
		if len(sc.Categories) > 1 {
			sc.Category1 = sc.Categories[1]
			if len(sc.Categories) > 2 {
				sc.Category2 = sc.Categories[2]
			}
		}
	}
	if en.Fields != nil && en.Fields.Fields != nil && en.Fields.Fields["product_id"] != nil {
		sc.HasCup = productHasCup[cast.ToUint64(en.Fields.Fields["product_id"].GetStringValue())]
	}
	return
}

// 下面几个方法是通过code查主档取得名称
func ListEntity(ctx context.Context, schemaName string, filter map[string]interface{}) ([]*Entity, error) {
	filterObj, _ := decodeMapToPbStruct(filter)
	req := &ListEntityRequest{
		SchemaName: schemaName,
		Filters:    filterObj,
		Relation:   "all",
		Limit:      -1,
	}

	mCtx := getAuthCtx(ctx)
	res, err := DefaultClient.ListEntity(mCtx, req)
	if err != nil {
		return nil, err
	}
	return res.Rows, nil
}

func getAuthCtx(ctx context.Context) context.Context {
	c, _ := context.WithTimeout(context.Background(), 10*time.Second)
	partnerId := ctx.Value(`partner_id`)
	scopeId := ctx.Value(`scope_id`)
	useId := ctx.Value(`user_id`)
	md := metadata.New(map[string]string{
		"partner_id": cast.ToString(partnerId),
		"user_id":    cast.ToString(useId),
		"scope_id":   cast.ToString(scopeId),
	})
	ct := metadata.NewOutgoingContext(c, md)
	return ct
}

func decodeMapToPbStruct(content map[string]interface{}) (*structpb.Struct, error) {
	if content == nil {
		return nil, nil
	}
	contentByte, err := json.Marshal(content)
	if err != nil {
		return nil, errors.New("error while marshaling map")
	}
	s := &structpb.Struct{}
	errUnmar := jsonpb.UnmarshalString(string(contentByte), s)
	return s, errUnmar
}

type BusinessHour struct {
	OpenDay   int        `json:"open_day,omitempty"`  // 开始营业的星期几
	CloseDay  int        `json:"close_day,omitempty"` // 停止营业的星期几
	Times     []TimeSlot `json:"times,omitempty"`     // 多个营业时间段
	BeginTime string     `json:"beginTime,omitempty"` // 单个时间段的开始时间
	EndTime   string     `json:"endTime,omitempty"`   // 单个时间段的结束时间
}

type TimeSlot struct {
	OpenAt  string `json:"open_at,omitempty"`  // 开始时间
	CloseAt string `json:"close_at,omitempty"` // 结束时间
}

type BusinessHours struct {
	BusinessDay   []BusinessHour `json:"businessDay,omitempty"`   // 包含开关门日和时间段
	BusinessHours []BusinessHour `json:"businessHours,omitempty"` // 单纯的营业时间范围
}

func GetBusinessHour(en map[string]interface{}, date string) ([]int64, error) {
	var businessHours *BusinessHours
	if businessHoursStr, ok := en["business_hours"]; ok {
		bs, _ := json.Marshal(businessHoursStr)
		err := json.Unmarshal(bs, &businessHours)
		if err != nil {
			return nil, err
		}

	}

	parsedDate, err := time.Parse("2006-01-02", date)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %v", err)
	}

	opHours := []int64{}
	processed := make(map[int64]bool)
	weekday := int(parsedDate.Weekday()) // Sunday=0, Monday=1, ..., Saturday=6
	if weekday == 0 {
		weekday = 7 // Adjust Sunday to 7 to match OpenDay/CloseDay convention
	}
	// 没有设置的默认填充
	if businessHours == nil {
		businessHours = &BusinessHours{BusinessHours: []BusinessHour{
			BusinessHour{Times: []TimeSlot{{"00:00", "23:59"}}}}}
	}

	// 检查BusinessDay是否有设置
	for _, bh := range businessHours.BusinessDay {
		if bh.OpenDay <= weekday && weekday <= bh.CloseDay {
			for _, timeSlot := range bh.Times {
				start := ParseTime(timeSlot.OpenAt)
				end := ParseTime(timeSlot.CloseAt)
				if start != -1 && end != -1 {
					for i := start; i != end; i = (i + 1) % 24 {
						if !processed[i] {
							opHours = append(opHours, i)
							processed[i] = true
						}
					}
				}
			}
		}
	}
	for _, bh := range businessHours.BusinessHours {
		start := ParseTime(bh.BeginTime)
		end := ParseTime(bh.EndTime)
		if start != -1 && end != -1 {
			for i := start; i != end; i = (i + 1) % 24 {
				if !processed[i] {
					opHours = append(opHours, i)
					processed[i] = true
				}
			}
		}
		if end != -1 {
			opHours = append(opHours, end)
		}
	}

	return opHours, nil
}

func ParseTime(timeStr string) int64 {
	timeParts := strings.Split(timeStr, ":")
	if len(timeParts) != 2 {
		return -1 // Invalid time format
	}
	var hour int64
	fmt.Sscanf(timeParts[0], "%d", &hour)
	return hour
}

type baseInfo struct {
	code string
	name string
}

func GetAllPaymentChannelInfo(ctx context.Context, pids []string, sid, uid string) error {
	records := make([]*model.PaymentCache, 0)
	uniqueMap := make(map[int64]bool, 0)
	for _, pid := range pids {
		// 修改查询主档商品信息，并更新商品缓存表product_caches实现逻辑
		md := metadata.Pairs("partner_id", pid, "scope_id", sid, "user_id", uid)
		ctx = metadata.NewOutgoingContext(ctx, md)
		resp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "PAYMENT_CHANNEL",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}

		cResp, err := DefaultClient.ListEntity(ctx, &ListEntityRequest{
			SchemaName: "PAYMENT_CHANNEL_CATEGORY",
			State:      "all",
			Relation:   "all",
			Limit:      -1,
		})
		if err != nil {
			return err
		}
		categoryMap := make(map[int64]*baseInfo)

		for _, row := range cResp.Rows {
			m := row.Fields.AsMap()
			categoryMap[int64(row.Id)] = &baseInfo{
				code: cast.ToString(m["code"]),
				name: cast.ToString(m["name"]),
			}
		}
		for _, row := range resp.Rows {
			m := row.Fields.AsMap()
			record := &model.PaymentCache{
				Id:              int64(row.Id),
				PaymentMethod:   cast.ToString(m["payment_method"]),
				CooperationId:   cast.ToString(m["cooperation_id"]),
				CooperationCode: cast.ToString(m["cooperation_code"]),
				Name:            cast.ToString(m["name"]),
				Code:            cast.ToString(m["code"]),
			}
			if relation, ok := m["relation"]; ok {
				record.PaymentCategoryId = cast.ToInt64(relation.(map[string]interface{})["payment_channel_category"])
			}
			if b, ok := categoryMap[record.PaymentCategoryId]; ok {
				record.PaymentCategoryName = b.name
				record.PaymentCategoryCode = b.code
			}
			if !uniqueMap[record.Id] {
				records = append(records, record)
				uniqueMap[record.Id] = true
			}

		}

	}
	return repo.DefaultMetadataCache.UpdateAllPayment(ctx, records)
}
