// Code generated by protoc-gen-go. DO NOT EDIT.
// source: entity.proto

package entity

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_struct "github.com/golang/protobuf/ptypes/struct"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type AddEntityRequest struct {
	// schema 名称
	SchemaName string `protobuf:"bytes,1,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 创建之后enable数据
	AutoEnable bool `protobuf:"varint,2,opt,name=auto_enable,json=autoEnable,proto3" json:"auto_enable,omitempty"`
	// Entity字段json
	Fields *_struct.Struct `protobuf:"bytes,3,opt,name=fields,proto3" json:"fields,omitempty"`
	// 当前使用的语言
	Lan string `protobuf:"bytes,4,opt,name=lan,proto3" json:"lan,omitempty"`
	//为了导入数据的参数
	Id                   string   `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddEntityRequest) Reset()         { *m = AddEntityRequest{} }
func (m *AddEntityRequest) String() string { return proto.CompactTextString(m) }
func (*AddEntityRequest) ProtoMessage()    {}
func (*AddEntityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{0}
}

func (m *AddEntityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEntityRequest.Unmarshal(m, b)
}
func (m *AddEntityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEntityRequest.Marshal(b, m, deterministic)
}
func (m *AddEntityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEntityRequest.Merge(m, src)
}
func (m *AddEntityRequest) XXX_Size() int {
	return xxx_messageInfo_AddEntityRequest.Size(m)
}
func (m *AddEntityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEntityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddEntityRequest proto.InternalMessageInfo

func (m *AddEntityRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *AddEntityRequest) GetAutoEnable() bool {
	if m != nil {
		return m.AutoEnable
	}
	return false
}

func (m *AddEntityRequest) GetFields() *_struct.Struct {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *AddEntityRequest) GetLan() string {
	if m != nil {
		return m.Lan
	}
	return ""
}

func (m *AddEntityRequest) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type SyncEntityRequest struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 对数据状态是ENABLED的数据自动apply change
	AutoApply bool `protobuf:"varint,3,opt,name=auto_apply,json=autoApply,proto3" json:"auto_apply,omitempty"`
	// 创建之后enable数据
	AutoEnable bool `protobuf:"varint,4,opt,name=auto_enable,json=autoEnable,proto3" json:"auto_enable,omitempty"`
	// Entity字段json
	Fields *_struct.Struct `protobuf:"bytes,5,opt,name=fields,proto3" json:"fields,omitempty"`
	// 当前使用的语言
	Lan                  string   `protobuf:"bytes,6,opt,name=lan,proto3" json:"lan,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SyncEntityRequest) Reset()         { *m = SyncEntityRequest{} }
func (m *SyncEntityRequest) String() string { return proto.CompactTextString(m) }
func (*SyncEntityRequest) ProtoMessage()    {}
func (*SyncEntityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{1}
}

func (m *SyncEntityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SyncEntityRequest.Unmarshal(m, b)
}
func (m *SyncEntityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SyncEntityRequest.Marshal(b, m, deterministic)
}
func (m *SyncEntityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SyncEntityRequest.Merge(m, src)
}
func (m *SyncEntityRequest) XXX_Size() int {
	return xxx_messageInfo_SyncEntityRequest.Size(m)
}
func (m *SyncEntityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SyncEntityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SyncEntityRequest proto.InternalMessageInfo

func (m *SyncEntityRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SyncEntityRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *SyncEntityRequest) GetAutoApply() bool {
	if m != nil {
		return m.AutoApply
	}
	return false
}

func (m *SyncEntityRequest) GetAutoEnable() bool {
	if m != nil {
		return m.AutoEnable
	}
	return false
}

func (m *SyncEntityRequest) GetFields() *_struct.Struct {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *SyncEntityRequest) GetLan() string {
	if m != nil {
		return m.Lan
	}
	return ""
}

type UpdateEntityRequest struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 对数据状态是ENABLED的数据自动apply change
	AutoApply bool `protobuf:"varint,3,opt,name=auto_apply,json=autoApply,proto3" json:"auto_apply,omitempty"`
	// Entity字段json
	Fields *_struct.Struct `protobuf:"bytes,4,opt,name=fields,proto3" json:"fields,omitempty"`
	// 当前使用的语言
	Lan                  string   `protobuf:"bytes,5,opt,name=lan,proto3" json:"lan,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEntityRequest) Reset()         { *m = UpdateEntityRequest{} }
func (m *UpdateEntityRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEntityRequest) ProtoMessage()    {}
func (*UpdateEntityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{2}
}

func (m *UpdateEntityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntityRequest.Unmarshal(m, b)
}
func (m *UpdateEntityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntityRequest.Marshal(b, m, deterministic)
}
func (m *UpdateEntityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntityRequest.Merge(m, src)
}
func (m *UpdateEntityRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEntityRequest.Size(m)
}
func (m *UpdateEntityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntityRequest proto.InternalMessageInfo

func (m *UpdateEntityRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateEntityRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *UpdateEntityRequest) GetAutoApply() bool {
	if m != nil {
		return m.AutoApply
	}
	return false
}

func (m *UpdateEntityRequest) GetFields() *_struct.Struct {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *UpdateEntityRequest) GetLan() string {
	if m != nil {
		return m.Lan
	}
	return ""
}

type GetEntityByIdRequest struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 是否包含数据状态
	IncludeState bool `protobuf:"varint,3,opt,name=include_state,json=includeState,proto3" json:"include_state,omitempty"`
	// 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
	Code string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	// 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
	Relation string `protobuf:"bytes,5,opt,name=relation,proto3" json:"relation,omitempty"`
	// 除code和relation之外需要返回的字段, 多个以逗号隔开
	ReturnFields string `protobuf:"bytes,6,opt,name=return_fields,json=returnFields,proto3" json:"return_fields,omitempty"`
	// include_pending_changes=true时, 如果相关记录包含pending的修改属性,
	// 则会获取修改属性attach到返回记录中pengding_changes字段
	IncludePendingChanges bool `protobuf:"varint,7,opt,name=include_pending_changes,json=includePendingChanges,proto3" json:"include_pending_changes,omitempty"`
	// include_pending_record=true时, 如果相关记录包含pending的修改属性,
	// 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
	IncludePendingRecord bool `protobuf:"varint,8,opt,name=include_pending_record,json=includePendingRecord,proto3" json:"include_pending_record,omitempty"`
	// include_parents=true返回所有父级节点
	IncludeParents bool `protobuf:"varint,9,opt,name=include_parents,json=includeParents,proto3" json:"include_parents,omitempty"`
	// 当前使用的语言
	Lan string `protobuf:"bytes,10,opt,name=lan,proto3" json:"lan,omitempty"`
	// 是否包含所有本地化信息
	IncludeAllLocalizations bool     `protobuf:"varint,11,opt,name=include_all_localizations,json=includeAllLocalizations,proto3" json:"include_all_localizations,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GetEntityByIdRequest) Reset()         { *m = GetEntityByIdRequest{} }
func (m *GetEntityByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetEntityByIdRequest) ProtoMessage()    {}
func (*GetEntityByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{3}
}

func (m *GetEntityByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEntityByIdRequest.Unmarshal(m, b)
}
func (m *GetEntityByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEntityByIdRequest.Marshal(b, m, deterministic)
}
func (m *GetEntityByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEntityByIdRequest.Merge(m, src)
}
func (m *GetEntityByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetEntityByIdRequest.Size(m)
}
func (m *GetEntityByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEntityByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEntityByIdRequest proto.InternalMessageInfo

func (m *GetEntityByIdRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetEntityByIdRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *GetEntityByIdRequest) GetIncludeState() bool {
	if m != nil {
		return m.IncludeState
	}
	return false
}

func (m *GetEntityByIdRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *GetEntityByIdRequest) GetRelation() string {
	if m != nil {
		return m.Relation
	}
	return ""
}

func (m *GetEntityByIdRequest) GetReturnFields() string {
	if m != nil {
		return m.ReturnFields
	}
	return ""
}

func (m *GetEntityByIdRequest) GetIncludePendingChanges() bool {
	if m != nil {
		return m.IncludePendingChanges
	}
	return false
}

func (m *GetEntityByIdRequest) GetIncludePendingRecord() bool {
	if m != nil {
		return m.IncludePendingRecord
	}
	return false
}

func (m *GetEntityByIdRequest) GetIncludeParents() bool {
	if m != nil {
		return m.IncludeParents
	}
	return false
}

func (m *GetEntityByIdRequest) GetLan() string {
	if m != nil {
		return m.Lan
	}
	return ""
}

func (m *GetEntityByIdRequest) GetIncludeAllLocalizations() bool {
	if m != nil {
		return m.IncludeAllLocalizations
	}
	return false
}

type ListEntityRequest struct {
	// schema 名称
	SchemaName string `protobuf:"bytes,1,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
	// 如state=draft,disabled; state=all时返回所有数据状态的记录
	State string `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	// 是否包含数据状态
	IncludeState bool `protobuf:"varint,3,opt,name=include_state,json=includeState,proto3" json:"include_state,omitempty"`
	// 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
	Code string `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	// 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
	Relation string `protobuf:"bytes,5,opt,name=relation,proto3" json:"relation,omitempty"`
	// 除code和relation之外需要返回的字段, 多个以逗号隔开
	ReturnFields string `protobuf:"bytes,6,opt,name=return_fields,json=returnFields,proto3" json:"return_fields,omitempty"`
	// include_pending_changes=true时, 如果相关记录包含pending的修改属性,
	// 则会获取修改属性attach到返回记录中fields_pending字段
	IncludePendingChanges bool `protobuf:"varint,7,opt,name=include_pending_changes,json=includePendingChanges,proto3" json:"include_pending_changes,omitempty"`
	// include_pending_record=true时, 如果相关记录包含pending的修改属性,
	// 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
	IncludePendingRecord bool `protobuf:"varint,8,opt,name=include_pending_record,json=includePendingRecord,proto3" json:"include_pending_record,omitempty"`
	// include_parents=true返回所有父级节点
	IncludeParents bool `protobuf:"varint,9,opt,name=include_parents,json=includeParents,proto3" json:"include_parents,omitempty"`
	// 分页大小
	Limit int32 `protobuf:"varint,10,opt,name=limit,proto3" json:"limit,omitempty"`
	// 跳过行数
	Offset int32 `protobuf:"varint,11,opt,name=offset,proto3" json:"offset,omitempty"`
	// 排序字段
	Sort string `protobuf:"bytes,12,opt,name=sort,proto3" json:"sort,omitempty"`
	// 排序顺序
	Order string `protobuf:"bytes,13,opt,name=order,proto3" json:"order,omitempty"`
	// 返回总条数
	IncludeTotal bool `protobuf:"varint,14,opt,name=include_total,json=includeTotal,proto3" json:"include_total,omitempty"`
	// 要模糊查询的字符串
	Search string `protobuf:"bytes,15,opt,name=search,proto3" json:"search,omitempty"`
	// 要查询的字段, 多个逗号隔开;
	// 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
	// 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
	// 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
	SearchFields string `protobuf:"bytes,16,opt,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"`
	// 按id列表查询
	Ids []uint64 `protobuf:"varint,17,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 按字段过滤
	Filters *_struct.Struct `protobuf:"bytes,18,opt,name=filters,proto3" json:"filters,omitempty"`
	// 按关系深层次递归过滤(获取包含下级节点的数据)
	RelationFilters *_struct.Struct `protobuf:"bytes,19,opt,name=relation_filters,json=relationFilters,proto3" json:"relation_filters,omitempty"`
	// 当前使用的语言
	Lan string `protobuf:"bytes,20,opt,name=lan,proto3" json:"lan,omitempty"`
	// 是否包含所有本地化信息
	IncludeAllLocalizations bool `protobuf:"varint,21,opt,name=include_all_localizations,json=includeAllLocalizations,proto3" json:"include_all_localizations,omitempty"`
	// 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
	IsRequest            bool     `protobuf:"varint,22,opt,name=is_request,json=isRequest,proto3" json:"is_request,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListEntityRequest) Reset()         { *m = ListEntityRequest{} }
func (m *ListEntityRequest) String() string { return proto.CompactTextString(m) }
func (*ListEntityRequest) ProtoMessage()    {}
func (*ListEntityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{4}
}

func (m *ListEntityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEntityRequest.Unmarshal(m, b)
}
func (m *ListEntityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEntityRequest.Marshal(b, m, deterministic)
}
func (m *ListEntityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEntityRequest.Merge(m, src)
}
func (m *ListEntityRequest) XXX_Size() int {
	return xxx_messageInfo_ListEntityRequest.Size(m)
}
func (m *ListEntityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEntityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListEntityRequest proto.InternalMessageInfo

func (m *ListEntityRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *ListEntityRequest) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *ListEntityRequest) GetIncludeState() bool {
	if m != nil {
		return m.IncludeState
	}
	return false
}

func (m *ListEntityRequest) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ListEntityRequest) GetRelation() string {
	if m != nil {
		return m.Relation
	}
	return ""
}

func (m *ListEntityRequest) GetReturnFields() string {
	if m != nil {
		return m.ReturnFields
	}
	return ""
}

func (m *ListEntityRequest) GetIncludePendingChanges() bool {
	if m != nil {
		return m.IncludePendingChanges
	}
	return false
}

func (m *ListEntityRequest) GetIncludePendingRecord() bool {
	if m != nil {
		return m.IncludePendingRecord
	}
	return false
}

func (m *ListEntityRequest) GetIncludeParents() bool {
	if m != nil {
		return m.IncludeParents
	}
	return false
}

func (m *ListEntityRequest) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListEntityRequest) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ListEntityRequest) GetSort() string {
	if m != nil {
		return m.Sort
	}
	return ""
}

func (m *ListEntityRequest) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *ListEntityRequest) GetIncludeTotal() bool {
	if m != nil {
		return m.IncludeTotal
	}
	return false
}

func (m *ListEntityRequest) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *ListEntityRequest) GetSearchFields() string {
	if m != nil {
		return m.SearchFields
	}
	return ""
}

func (m *ListEntityRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *ListEntityRequest) GetFilters() *_struct.Struct {
	if m != nil {
		return m.Filters
	}
	return nil
}

func (m *ListEntityRequest) GetRelationFilters() *_struct.Struct {
	if m != nil {
		return m.RelationFilters
	}
	return nil
}

func (m *ListEntityRequest) GetLan() string {
	if m != nil {
		return m.Lan
	}
	return ""
}

func (m *ListEntityRequest) GetIncludeAllLocalizations() bool {
	if m != nil {
		return m.IncludeAllLocalizations
	}
	return false
}

func (m *ListEntityRequest) GetIsRequest() bool {
	if m != nil {
		return m.IsRequest
	}
	return false
}

type UpdateEntityStateRequest struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// state
	State                string   `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEntityStateRequest) Reset()         { *m = UpdateEntityStateRequest{} }
func (m *UpdateEntityStateRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEntityStateRequest) ProtoMessage()    {}
func (*UpdateEntityStateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{5}
}

func (m *UpdateEntityStateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntityStateRequest.Unmarshal(m, b)
}
func (m *UpdateEntityStateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntityStateRequest.Marshal(b, m, deterministic)
}
func (m *UpdateEntityStateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntityStateRequest.Merge(m, src)
}
func (m *UpdateEntityStateRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEntityStateRequest.Size(m)
}
func (m *UpdateEntityStateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntityStateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntityStateRequest proto.InternalMessageInfo

func (m *UpdateEntityStateRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateEntityStateRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *UpdateEntityStateRequest) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type ProcessEntityPendingChangesRequest struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// action ("apply" or "cancel")
	Action               string   `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcessEntityPendingChangesRequest) Reset()         { *m = ProcessEntityPendingChangesRequest{} }
func (m *ProcessEntityPendingChangesRequest) String() string { return proto.CompactTextString(m) }
func (*ProcessEntityPendingChangesRequest) ProtoMessage()    {}
func (*ProcessEntityPendingChangesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{6}
}

func (m *ProcessEntityPendingChangesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcessEntityPendingChangesRequest.Unmarshal(m, b)
}
func (m *ProcessEntityPendingChangesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcessEntityPendingChangesRequest.Marshal(b, m, deterministic)
}
func (m *ProcessEntityPendingChangesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcessEntityPendingChangesRequest.Merge(m, src)
}
func (m *ProcessEntityPendingChangesRequest) XXX_Size() int {
	return xxx_messageInfo_ProcessEntityPendingChangesRequest.Size(m)
}
func (m *ProcessEntityPendingChangesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcessEntityPendingChangesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ProcessEntityPendingChangesRequest proto.InternalMessageInfo

func (m *ProcessEntityPendingChangesRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ProcessEntityPendingChangesRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *ProcessEntityPendingChangesRequest) GetAction() string {
	if m != nil {
		return m.Action
	}
	return ""
}

type ListEntityResponse struct {
	Rows                 []*Entity `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	Total                int32     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ListEntityResponse) Reset()         { *m = ListEntityResponse{} }
func (m *ListEntityResponse) String() string { return proto.CompactTextString(m) }
func (*ListEntityResponse) ProtoMessage()    {}
func (*ListEntityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{7}
}

func (m *ListEntityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEntityResponse.Unmarshal(m, b)
}
func (m *ListEntityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEntityResponse.Marshal(b, m, deterministic)
}
func (m *ListEntityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEntityResponse.Merge(m, src)
}
func (m *ListEntityResponse) XXX_Size() int {
	return xxx_messageInfo_ListEntityResponse.Size(m)
}
func (m *ListEntityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEntityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListEntityResponse proto.InternalMessageInfo

func (m *ListEntityResponse) GetRows() []*Entity {
	if m != nil {
		return m.Rows
	}
	return nil
}

func (m *ListEntityResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type Entity struct {
	// 数据id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商户id
	PartnerId uint64 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// scope_id
	ScopeId uint64 `protobuf:"varint,3,opt,name=scope_id,json=scopeId,proto3" json:"scope_id,omitempty"`
	// 父级id
	ParentId uint64 `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// schema id
	SchemaId uint64 `protobuf:"varint,5,opt,name=schema_id,json=schemaId,proto3" json:"schema_id,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,6,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 数据状态
	State string `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	// 数据字段内容
	Fields *_struct.Struct `protobuf:"bytes,8,opt,name=fields,proto3" json:"fields,omitempty"`
	//string fields = 8;
	// 被更新的字段内容(未被接受的更新)
	// string fields_pending = 9;
	FieldsPending *_struct.Struct `protobuf:"bytes,9,opt,name=fields_pending,json=fieldsPending,proto3" json:"fields_pending,omitempty"`
	// fields合并了fields_pending
	// string record_pending = 10;
	RecordPending *_struct.Struct `protobuf:"bytes,10,opt,name=record_pending,json=recordPending,proto3" json:"record_pending,omitempty"`
	// 是否有被更新字段
	Pending bool `protobuf:"varint,11,opt,name=pending,proto3" json:"pending,omitempty"`
	// 创建时间
	Created string `protobuf:"bytes,12,opt,name=created,proto3" json:"created,omitempty"`
	// 最后一次修改时间
	Updated string `protobuf:"bytes,13,opt,name=updated,proto3" json:"updated,omitempty"`
	// 创建者
	CreatedBy string `protobuf:"bytes,14,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// 最后一次修改者
	UpdatedBy string `protobuf:"bytes,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	// 数据被批处理状态
	ProcessStatus string `protobuf:"bytes,16,opt,name=process_status,json=processStatus,proto3" json:"process_status,omitempty"`
	// 父级节点
	Parent               *Entity  `protobuf:"bytes,17,opt,name=parent,proto3" json:"parent,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Entity) Reset()         { *m = Entity{} }
func (m *Entity) String() string { return proto.CompactTextString(m) }
func (*Entity) ProtoMessage()    {}
func (*Entity) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{8}
}

func (m *Entity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Entity.Unmarshal(m, b)
}
func (m *Entity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Entity.Marshal(b, m, deterministic)
}
func (m *Entity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Entity.Merge(m, src)
}
func (m *Entity) XXX_Size() int {
	return xxx_messageInfo_Entity.Size(m)
}
func (m *Entity) XXX_DiscardUnknown() {
	xxx_messageInfo_Entity.DiscardUnknown(m)
}

var xxx_messageInfo_Entity proto.InternalMessageInfo

func (m *Entity) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Entity) GetPartnerId() uint64 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *Entity) GetScopeId() uint64 {
	if m != nil {
		return m.ScopeId
	}
	return 0
}

func (m *Entity) GetParentId() uint64 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *Entity) GetSchemaId() uint64 {
	if m != nil {
		return m.SchemaId
	}
	return 0
}

func (m *Entity) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *Entity) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *Entity) GetFields() *_struct.Struct {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *Entity) GetFieldsPending() *_struct.Struct {
	if m != nil {
		return m.FieldsPending
	}
	return nil
}

func (m *Entity) GetRecordPending() *_struct.Struct {
	if m != nil {
		return m.RecordPending
	}
	return nil
}

func (m *Entity) GetPending() bool {
	if m != nil {
		return m.Pending
	}
	return false
}

func (m *Entity) GetCreated() string {
	if m != nil {
		return m.Created
	}
	return ""
}

func (m *Entity) GetUpdated() string {
	if m != nil {
		return m.Updated
	}
	return ""
}

func (m *Entity) GetCreatedBy() string {
	if m != nil {
		return m.CreatedBy
	}
	return ""
}

func (m *Entity) GetUpdatedBy() string {
	if m != nil {
		return m.UpdatedBy
	}
	return ""
}

func (m *Entity) GetProcessStatus() string {
	if m != nil {
		return m.ProcessStatus
	}
	return ""
}

func (m *Entity) GetParent() *Entity {
	if m != nil {
		return m.Parent
	}
	return nil
}

type DefaultResponse struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DefaultResponse) Reset()         { *m = DefaultResponse{} }
func (m *DefaultResponse) String() string { return proto.CompactTextString(m) }
func (*DefaultResponse) ProtoMessage()    {}
func (*DefaultResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{9}
}

func (m *DefaultResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DefaultResponse.Unmarshal(m, b)
}
func (m *DefaultResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DefaultResponse.Marshal(b, m, deterministic)
}
func (m *DefaultResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DefaultResponse.Merge(m, src)
}
func (m *DefaultResponse) XXX_Size() int {
	return xxx_messageInfo_DefaultResponse.Size(m)
}
func (m *DefaultResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DefaultResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DefaultResponse proto.InternalMessageInfo

func (m *DefaultResponse) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type CreateEntityTaskFromPendingChangesRequest struct {
	// record_id
	RecordId   uint64 `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// task 名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 是否立即执行
	Immediate bool `protobuf:"varint,4,opt,name=immediate,proto3" json:"immediate,omitempty"`
	// 开始执行时间
	Start string `protobuf:"bytes,5,opt,name=start,proto3" json:"start,omitempty"`
	// 自动审核
	AutoApprove          bool     `protobuf:"varint,6,opt,name=auto_approve,json=autoApprove,proto3" json:"auto_approve,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEntityTaskFromPendingChangesRequest) Reset() {
	*m = CreateEntityTaskFromPendingChangesRequest{}
}
func (m *CreateEntityTaskFromPendingChangesRequest) String() string {
	return proto.CompactTextString(m)
}
func (*CreateEntityTaskFromPendingChangesRequest) ProtoMessage() {}
func (*CreateEntityTaskFromPendingChangesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{10}
}

func (m *CreateEntityTaskFromPendingChangesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest.Unmarshal(m, b)
}
func (m *CreateEntityTaskFromPendingChangesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest.Marshal(b, m, deterministic)
}
func (m *CreateEntityTaskFromPendingChangesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest.Merge(m, src)
}
func (m *CreateEntityTaskFromPendingChangesRequest) XXX_Size() int {
	return xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest.Size(m)
}
func (m *CreateEntityTaskFromPendingChangesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEntityTaskFromPendingChangesRequest proto.InternalMessageInfo

func (m *CreateEntityTaskFromPendingChangesRequest) GetRecordId() uint64 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *CreateEntityTaskFromPendingChangesRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *CreateEntityTaskFromPendingChangesRequest) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *CreateEntityTaskFromPendingChangesRequest) GetImmediate() bool {
	if m != nil {
		return m.Immediate
	}
	return false
}

func (m *CreateEntityTaskFromPendingChangesRequest) GetStart() string {
	if m != nil {
		return m.Start
	}
	return ""
}

func (m *CreateEntityTaskFromPendingChangesRequest) GetAutoApprove() bool {
	if m != nil {
		return m.AutoApprove
	}
	return false
}

type GetEntityTaskByIdRequest struct {
	// 数据id
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchemaName           string   `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEntityTaskByIdRequest) Reset()         { *m = GetEntityTaskByIdRequest{} }
func (m *GetEntityTaskByIdRequest) String() string { return proto.CompactTextString(m) }
func (*GetEntityTaskByIdRequest) ProtoMessage()    {}
func (*GetEntityTaskByIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{11}
}

func (m *GetEntityTaskByIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEntityTaskByIdRequest.Unmarshal(m, b)
}
func (m *GetEntityTaskByIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEntityTaskByIdRequest.Marshal(b, m, deterministic)
}
func (m *GetEntityTaskByIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEntityTaskByIdRequest.Merge(m, src)
}
func (m *GetEntityTaskByIdRequest) XXX_Size() int {
	return xxx_messageInfo_GetEntityTaskByIdRequest.Size(m)
}
func (m *GetEntityTaskByIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEntityTaskByIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetEntityTaskByIdRequest proto.InternalMessageInfo

func (m *GetEntityTaskByIdRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetEntityTaskByIdRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

type ListEntityTaskRequest struct {
	// 数据id
	RecordIds []uint64 `protobuf:"varint,1,rep,packed,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	// schema 名称
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// task 状态
	Status []string `protobuf:"bytes,3,rep,name=status,proto3" json:"status,omitempty"`
	// 批处理状态
	ProcessStatus []string `protobuf:"bytes,4,rep,name=process_status,json=processStatus,proto3" json:"process_status,omitempty"`
	// 分页大小
	Limit int32 `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	// 跳过行数
	Offset int32 `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	// 返回总条数
	IncludeTotal bool `protobuf:"varint,7,opt,name=include_total,json=includeTotal,proto3" json:"include_total,omitempty"`
	// 要模糊查询的字符串
	Search string `protobuf:"bytes,8,opt,name=search,proto3" json:"search,omitempty"`
	// 要查询的字段, 多个逗号隔开;
	SearchFields string `protobuf:"bytes,9,opt,name=search_fields,json=searchFields,proto3" json:"search_fields,omitempty"`
	// 按id列表查询
	Ids                  []uint64 `protobuf:"varint,10,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListEntityTaskRequest) Reset()         { *m = ListEntityTaskRequest{} }
func (m *ListEntityTaskRequest) String() string { return proto.CompactTextString(m) }
func (*ListEntityTaskRequest) ProtoMessage()    {}
func (*ListEntityTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{12}
}

func (m *ListEntityTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEntityTaskRequest.Unmarshal(m, b)
}
func (m *ListEntityTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEntityTaskRequest.Marshal(b, m, deterministic)
}
func (m *ListEntityTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEntityTaskRequest.Merge(m, src)
}
func (m *ListEntityTaskRequest) XXX_Size() int {
	return xxx_messageInfo_ListEntityTaskRequest.Size(m)
}
func (m *ListEntityTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEntityTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ListEntityTaskRequest proto.InternalMessageInfo

func (m *ListEntityTaskRequest) GetRecordIds() []uint64 {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

func (m *ListEntityTaskRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *ListEntityTaskRequest) GetStatus() []string {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *ListEntityTaskRequest) GetProcessStatus() []string {
	if m != nil {
		return m.ProcessStatus
	}
	return nil
}

func (m *ListEntityTaskRequest) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListEntityTaskRequest) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ListEntityTaskRequest) GetIncludeTotal() bool {
	if m != nil {
		return m.IncludeTotal
	}
	return false
}

func (m *ListEntityTaskRequest) GetSearch() string {
	if m != nil {
		return m.Search
	}
	return ""
}

func (m *ListEntityTaskRequest) GetSearchFields() string {
	if m != nil {
		return m.SearchFields
	}
	return ""
}

func (m *ListEntityTaskRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type UpdateEntityTaskRequest struct {
	// 数据id
	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// task 字段
	Fields               *_struct.Struct `protobuf:"bytes,3,opt,name=fields,proto3" json:"fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateEntityTaskRequest) Reset()         { *m = UpdateEntityTaskRequest{} }
func (m *UpdateEntityTaskRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEntityTaskRequest) ProtoMessage()    {}
func (*UpdateEntityTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{13}
}

func (m *UpdateEntityTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntityTaskRequest.Unmarshal(m, b)
}
func (m *UpdateEntityTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntityTaskRequest.Marshal(b, m, deterministic)
}
func (m *UpdateEntityTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntityTaskRequest.Merge(m, src)
}
func (m *UpdateEntityTaskRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEntityTaskRequest.Size(m)
}
func (m *UpdateEntityTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntityTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntityTaskRequest proto.InternalMessageInfo

func (m *UpdateEntityTaskRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateEntityTaskRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *UpdateEntityTaskRequest) GetFields() *_struct.Struct {
	if m != nil {
		return m.Fields
	}
	return nil
}

type DeleteEntityTaskRequest struct {
	// 数据id
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchemaName           string   `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEntityTaskRequest) Reset()         { *m = DeleteEntityTaskRequest{} }
func (m *DeleteEntityTaskRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteEntityTaskRequest) ProtoMessage()    {}
func (*DeleteEntityTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{14}
}

func (m *DeleteEntityTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEntityTaskRequest.Unmarshal(m, b)
}
func (m *DeleteEntityTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEntityTaskRequest.Marshal(b, m, deterministic)
}
func (m *DeleteEntityTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEntityTaskRequest.Merge(m, src)
}
func (m *DeleteEntityTaskRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteEntityTaskRequest.Size(m)
}
func (m *DeleteEntityTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEntityTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEntityTaskRequest proto.InternalMessageInfo

func (m *DeleteEntityTaskRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeleteEntityTaskRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

type UpdateEntityTaskStatusRequest struct {
	// 数据id
	Id         uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchemaName string `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// task 状态
	Status               string   `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEntityTaskStatusRequest) Reset()         { *m = UpdateEntityTaskStatusRequest{} }
func (m *UpdateEntityTaskStatusRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateEntityTaskStatusRequest) ProtoMessage()    {}
func (*UpdateEntityTaskStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{15}
}

func (m *UpdateEntityTaskStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEntityTaskStatusRequest.Unmarshal(m, b)
}
func (m *UpdateEntityTaskStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEntityTaskStatusRequest.Marshal(b, m, deterministic)
}
func (m *UpdateEntityTaskStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEntityTaskStatusRequest.Merge(m, src)
}
func (m *UpdateEntityTaskStatusRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateEntityTaskStatusRequest.Size(m)
}
func (m *UpdateEntityTaskStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEntityTaskStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEntityTaskStatusRequest proto.InternalMessageInfo

func (m *UpdateEntityTaskStatusRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateEntityTaskStatusRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *UpdateEntityTaskStatusRequest) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type RunEntityTaskRequest struct {
	// 数据id
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	SchemaName           string   `protobuf:"bytes,2,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RunEntityTaskRequest) Reset()         { *m = RunEntityTaskRequest{} }
func (m *RunEntityTaskRequest) String() string { return proto.CompactTextString(m) }
func (*RunEntityTaskRequest) ProtoMessage()    {}
func (*RunEntityTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{16}
}

func (m *RunEntityTaskRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RunEntityTaskRequest.Unmarshal(m, b)
}
func (m *RunEntityTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RunEntityTaskRequest.Marshal(b, m, deterministic)
}
func (m *RunEntityTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunEntityTaskRequest.Merge(m, src)
}
func (m *RunEntityTaskRequest) XXX_Size() int {
	return xxx_messageInfo_RunEntityTaskRequest.Size(m)
}
func (m *RunEntityTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RunEntityTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RunEntityTaskRequest proto.InternalMessageInfo

func (m *RunEntityTaskRequest) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RunEntityTaskRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

type ListEntityTaskResponse struct {
	Rows                 []*EntityTask `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	Total                int32         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ListEntityTaskResponse) Reset()         { *m = ListEntityTaskResponse{} }
func (m *ListEntityTaskResponse) String() string { return proto.CompactTextString(m) }
func (*ListEntityTaskResponse) ProtoMessage()    {}
func (*ListEntityTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{17}
}

func (m *ListEntityTaskResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListEntityTaskResponse.Unmarshal(m, b)
}
func (m *ListEntityTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListEntityTaskResponse.Marshal(b, m, deterministic)
}
func (m *ListEntityTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListEntityTaskResponse.Merge(m, src)
}
func (m *ListEntityTaskResponse) XXX_Size() int {
	return xxx_messageInfo_ListEntityTaskResponse.Size(m)
}
func (m *ListEntityTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ListEntityTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ListEntityTaskResponse proto.InternalMessageInfo

func (m *ListEntityTaskResponse) GetRows() []*EntityTask {
	if m != nil {
		return m.Rows
	}
	return nil
}

func (m *ListEntityTaskResponse) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type EntityTask struct {
	// task id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 商户id
	PartnerId uint64 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	// scope_id
	ScopeId uint64 `protobuf:"varint,3,opt,name=scope_id,json=scopeId,proto3" json:"scope_id,omitempty"`
	// job id
	JobId uint64 `protobuf:"varint,4,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	// schema type(entity, relation)
	SchemaType string `protobuf:"bytes,5,opt,name=schema_type,json=schemaType,proto3" json:"schema_type,omitempty"`
	// task 名称
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// 数据id
	RecordId uint64 `protobuf:"varint,7,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	// 数据字段内容
	Content *_struct.Struct `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`
	//数据原先的字段内容
	ContentFrom *_struct.Struct `protobuf:"bytes,21,opt,name=content_from,json=contentFrom,proto3" json:"content_from,omitempty"`
	// task 状态
	Status string `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`
	// 数据被批处理状态
	ProcessStatus string `protobuf:"bytes,10,opt,name=process_status,json=processStatus,proto3" json:"process_status,omitempty"`
	// task动作
	Action string `protobuf:"bytes,11,opt,name=action,proto3" json:"action,omitempty"`
	// 是否立即执行
	Immediate bool `protobuf:"varint,12,opt,name=immediate,proto3" json:"immediate,omitempty"`
	// 开始执行时间
	Start string `protobuf:"bytes,13,opt,name=start,proto3" json:"start,omitempty"`
	// 最后一次开始执行时间
	LastStart string `protobuf:"bytes,14,opt,name=last_start,json=lastStart,proto3" json:"last_start,omitempty"`
	// 最后一次结束时间
	LastEnd string `protobuf:"bytes,15,opt,name=last_end,json=lastEnd,proto3" json:"last_end,omitempty"`
	// 重试次数
	Retry int32 `protobuf:"varint,16,opt,name=retry,proto3" json:"retry,omitempty"`
	// 创建时间
	Created string `protobuf:"bytes,17,opt,name=created,proto3" json:"created,omitempty"`
	// 最后一次修改时间
	Updated string `protobuf:"bytes,18,opt,name=updated,proto3" json:"updated,omitempty"`
	// 创建者
	CreatedBy uint64 `protobuf:"varint,19,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	// 最后一次修改者
	UpdatedBy            uint64   `protobuf:"varint,20,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EntityTask) Reset()         { *m = EntityTask{} }
func (m *EntityTask) String() string { return proto.CompactTextString(m) }
func (*EntityTask) ProtoMessage()    {}
func (*EntityTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{18}
}

func (m *EntityTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EntityTask.Unmarshal(m, b)
}
func (m *EntityTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EntityTask.Marshal(b, m, deterministic)
}
func (m *EntityTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EntityTask.Merge(m, src)
}
func (m *EntityTask) XXX_Size() int {
	return xxx_messageInfo_EntityTask.Size(m)
}
func (m *EntityTask) XXX_DiscardUnknown() {
	xxx_messageInfo_EntityTask.DiscardUnknown(m)
}

var xxx_messageInfo_EntityTask proto.InternalMessageInfo

func (m *EntityTask) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *EntityTask) GetPartnerId() uint64 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *EntityTask) GetScopeId() uint64 {
	if m != nil {
		return m.ScopeId
	}
	return 0
}

func (m *EntityTask) GetJobId() uint64 {
	if m != nil {
		return m.JobId
	}
	return 0
}

func (m *EntityTask) GetSchemaType() string {
	if m != nil {
		return m.SchemaType
	}
	return ""
}

func (m *EntityTask) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *EntityTask) GetRecordId() uint64 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *EntityTask) GetContent() *_struct.Struct {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *EntityTask) GetContentFrom() *_struct.Struct {
	if m != nil {
		return m.ContentFrom
	}
	return nil
}

func (m *EntityTask) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *EntityTask) GetProcessStatus() string {
	if m != nil {
		return m.ProcessStatus
	}
	return ""
}

func (m *EntityTask) GetAction() string {
	if m != nil {
		return m.Action
	}
	return ""
}

func (m *EntityTask) GetImmediate() bool {
	if m != nil {
		return m.Immediate
	}
	return false
}

func (m *EntityTask) GetStart() string {
	if m != nil {
		return m.Start
	}
	return ""
}

func (m *EntityTask) GetLastStart() string {
	if m != nil {
		return m.LastStart
	}
	return ""
}

func (m *EntityTask) GetLastEnd() string {
	if m != nil {
		return m.LastEnd
	}
	return ""
}

func (m *EntityTask) GetRetry() int32 {
	if m != nil {
		return m.Retry
	}
	return 0
}

func (m *EntityTask) GetCreated() string {
	if m != nil {
		return m.Created
	}
	return ""
}

func (m *EntityTask) GetUpdated() string {
	if m != nil {
		return m.Updated
	}
	return ""
}

func (m *EntityTask) GetCreatedBy() uint64 {
	if m != nil {
		return m.CreatedBy
	}
	return 0
}

func (m *EntityTask) GetUpdatedBy() uint64 {
	if m != nil {
		return m.UpdatedBy
	}
	return 0
}

type GetChildrenEntityIdsRequest struct {
	// schema 名称
	SchemaName string `protobuf:"bytes,1,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 按id列表查询
	Ids                  []uint64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChildrenEntityIdsRequest) Reset()         { *m = GetChildrenEntityIdsRequest{} }
func (m *GetChildrenEntityIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetChildrenEntityIdsRequest) ProtoMessage()    {}
func (*GetChildrenEntityIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{19}
}

func (m *GetChildrenEntityIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChildrenEntityIdsRequest.Unmarshal(m, b)
}
func (m *GetChildrenEntityIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChildrenEntityIdsRequest.Marshal(b, m, deterministic)
}
func (m *GetChildrenEntityIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChildrenEntityIdsRequest.Merge(m, src)
}
func (m *GetChildrenEntityIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetChildrenEntityIdsRequest.Size(m)
}
func (m *GetChildrenEntityIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChildrenEntityIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetChildrenEntityIdsRequest proto.InternalMessageInfo

func (m *GetChildrenEntityIdsRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *GetChildrenEntityIdsRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetChildrenEntityIdsResponse struct {
	ChildrenIds          []uint64 `protobuf:"varint,1,rep,packed,name=children_ids,json=childrenIds,proto3" json:"children_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChildrenEntityIdsResponse) Reset()         { *m = GetChildrenEntityIdsResponse{} }
func (m *GetChildrenEntityIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetChildrenEntityIdsResponse) ProtoMessage()    {}
func (*GetChildrenEntityIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{20}
}

func (m *GetChildrenEntityIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChildrenEntityIdsResponse.Unmarshal(m, b)
}
func (m *GetChildrenEntityIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChildrenEntityIdsResponse.Marshal(b, m, deterministic)
}
func (m *GetChildrenEntityIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChildrenEntityIdsResponse.Merge(m, src)
}
func (m *GetChildrenEntityIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetChildrenEntityIdsResponse.Size(m)
}
func (m *GetChildrenEntityIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChildrenEntityIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetChildrenEntityIdsResponse proto.InternalMessageInfo

func (m *GetChildrenEntityIdsResponse) GetChildrenIds() []uint64 {
	if m != nil {
		return m.ChildrenIds
	}
	return nil
}

type GetParentEntityIdsRequest struct {
	// schema 名称
	SchemaName string `protobuf:"bytes,1,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	// 按id列表查询
	Ids                  []uint64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetParentEntityIdsRequest) Reset()         { *m = GetParentEntityIdsRequest{} }
func (m *GetParentEntityIdsRequest) String() string { return proto.CompactTextString(m) }
func (*GetParentEntityIdsRequest) ProtoMessage()    {}
func (*GetParentEntityIdsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{21}
}

func (m *GetParentEntityIdsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetParentEntityIdsRequest.Unmarshal(m, b)
}
func (m *GetParentEntityIdsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetParentEntityIdsRequest.Marshal(b, m, deterministic)
}
func (m *GetParentEntityIdsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetParentEntityIdsRequest.Merge(m, src)
}
func (m *GetParentEntityIdsRequest) XXX_Size() int {
	return xxx_messageInfo_GetParentEntityIdsRequest.Size(m)
}
func (m *GetParentEntityIdsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetParentEntityIdsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetParentEntityIdsRequest proto.InternalMessageInfo

func (m *GetParentEntityIdsRequest) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *GetParentEntityIdsRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type GetParentEntityIdsResponse struct {
	ParentIds            []uint64 `protobuf:"varint,1,rep,packed,name=parent_ids,json=parentIds,proto3" json:"parent_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetParentEntityIdsResponse) Reset()         { *m = GetParentEntityIdsResponse{} }
func (m *GetParentEntityIdsResponse) String() string { return proto.CompactTextString(m) }
func (*GetParentEntityIdsResponse) ProtoMessage()    {}
func (*GetParentEntityIdsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{22}
}

func (m *GetParentEntityIdsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetParentEntityIdsResponse.Unmarshal(m, b)
}
func (m *GetParentEntityIdsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetParentEntityIdsResponse.Marshal(b, m, deterministic)
}
func (m *GetParentEntityIdsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetParentEntityIdsResponse.Merge(m, src)
}
func (m *GetParentEntityIdsResponse) XXX_Size() int {
	return xxx_messageInfo_GetParentEntityIdsResponse.Size(m)
}
func (m *GetParentEntityIdsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetParentEntityIdsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetParentEntityIdsResponse proto.InternalMessageInfo

func (m *GetParentEntityIdsResponse) GetParentIds() []uint64 {
	if m != nil {
		return m.ParentIds
	}
	return nil
}

type GetCrossEntityReq struct {
	// schema 名称
	SchemaName string `protobuf:"bytes,1,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	//id
	OwnerId string `protobuf:"bytes,2,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 上次的最大id
	LastId uint64 `protobuf:"varint,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	// 分页条数
	Limit int32 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	// 是否立即执行
	Immediately          bool     `protobuf:"varint,5,opt,name=immediately,proto3" json:"immediately,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCrossEntityReq) Reset()         { *m = GetCrossEntityReq{} }
func (m *GetCrossEntityReq) String() string { return proto.CompactTextString(m) }
func (*GetCrossEntityReq) ProtoMessage()    {}
func (*GetCrossEntityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{23}
}

func (m *GetCrossEntityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCrossEntityReq.Unmarshal(m, b)
}
func (m *GetCrossEntityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCrossEntityReq.Marshal(b, m, deterministic)
}
func (m *GetCrossEntityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCrossEntityReq.Merge(m, src)
}
func (m *GetCrossEntityReq) XXX_Size() int {
	return xxx_messageInfo_GetCrossEntityReq.Size(m)
}
func (m *GetCrossEntityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCrossEntityReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCrossEntityReq proto.InternalMessageInfo

func (m *GetCrossEntityReq) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *GetCrossEntityReq) GetOwnerId() string {
	if m != nil {
		return m.OwnerId
	}
	return ""
}

func (m *GetCrossEntityReq) GetLastId() uint64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *GetCrossEntityReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetCrossEntityReq) GetImmediately() bool {
	if m != nil {
		return m.Immediately
	}
	return false
}

type GetCrossEntityRes struct {
	Rows                 []*CrossEntity `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	Total                int32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetCrossEntityRes) Reset()         { *m = GetCrossEntityRes{} }
func (m *GetCrossEntityRes) String() string { return proto.CompactTextString(m) }
func (*GetCrossEntityRes) ProtoMessage()    {}
func (*GetCrossEntityRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{24}
}

func (m *GetCrossEntityRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCrossEntityRes.Unmarshal(m, b)
}
func (m *GetCrossEntityRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCrossEntityRes.Marshal(b, m, deterministic)
}
func (m *GetCrossEntityRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCrossEntityRes.Merge(m, src)
}
func (m *GetCrossEntityRes) XXX_Size() int {
	return xxx_messageInfo_GetCrossEntityRes.Size(m)
}
func (m *GetCrossEntityRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCrossEntityRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetCrossEntityRes proto.InternalMessageInfo

func (m *GetCrossEntityRes) GetRows() []*CrossEntity {
	if m != nil {
		return m.Rows
	}
	return nil
}

func (m *GetCrossEntityRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type CrossEntity struct {
	//change_id in record
	Id        uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PartnerId uint64 `protobuf:"varint,2,opt,name=partner_id,json=partnerId,proto3" json:"partner_id,omitempty"`
	ScopeId   uint64 `protobuf:"varint,3,opt,name=scope_id,json=scopeId,proto3" json:"scope_id,omitempty"`
	// pro_id
	RecordId   uint64          `protobuf:"varint,4,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	SchemaName string          `protobuf:"bytes,5,opt,name=schema_name,json=schemaName,proto3" json:"schema_name,omitempty"`
	SchemaId   uint64          `protobuf:"varint,6,opt,name=schema_id,json=schemaId,proto3" json:"schema_id,omitempty"`
	State      string          `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	Content    *_struct.Struct `protobuf:"bytes,8,opt,name=content,proto3" json:"content,omitempty"`
	// store_id
	OwnerId              uint64   `protobuf:"varint,9,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	Created              string   `protobuf:"bytes,10,opt,name=created,proto3" json:"created,omitempty"`
	Updated              string   `protobuf:"bytes,11,opt,name=updated,proto3" json:"updated,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CrossEntity) Reset()         { *m = CrossEntity{} }
func (m *CrossEntity) String() string { return proto.CompactTextString(m) }
func (*CrossEntity) ProtoMessage()    {}
func (*CrossEntity) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{25}
}

func (m *CrossEntity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CrossEntity.Unmarshal(m, b)
}
func (m *CrossEntity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CrossEntity.Marshal(b, m, deterministic)
}
func (m *CrossEntity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CrossEntity.Merge(m, src)
}
func (m *CrossEntity) XXX_Size() int {
	return xxx_messageInfo_CrossEntity.Size(m)
}
func (m *CrossEntity) XXX_DiscardUnknown() {
	xxx_messageInfo_CrossEntity.DiscardUnknown(m)
}

var xxx_messageInfo_CrossEntity proto.InternalMessageInfo

func (m *CrossEntity) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CrossEntity) GetPartnerId() uint64 {
	if m != nil {
		return m.PartnerId
	}
	return 0
}

func (m *CrossEntity) GetScopeId() uint64 {
	if m != nil {
		return m.ScopeId
	}
	return 0
}

func (m *CrossEntity) GetRecordId() uint64 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *CrossEntity) GetSchemaName() string {
	if m != nil {
		return m.SchemaName
	}
	return ""
}

func (m *CrossEntity) GetSchemaId() uint64 {
	if m != nil {
		return m.SchemaId
	}
	return 0
}

func (m *CrossEntity) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

func (m *CrossEntity) GetContent() *_struct.Struct {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *CrossEntity) GetOwnerId() uint64 {
	if m != nil {
		return m.OwnerId
	}
	return 0
}

func (m *CrossEntity) GetCreated() string {
	if m != nil {
		return m.Created
	}
	return ""
}

func (m *CrossEntity) GetUpdated() string {
	if m != nil {
		return m.Updated
	}
	return ""
}

type GetRuleListReq struct {
	//store_id
	StoreId string `protobuf:"bytes,1,opt,name=store_id,json=storeId,proto3" json:"store_id,omitempty"`
	// product_id
	ProductId string `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	// 分页条数
	Limit                int32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               int32    `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRuleListReq) Reset()         { *m = GetRuleListReq{} }
func (m *GetRuleListReq) String() string { return proto.CompactTextString(m) }
func (*GetRuleListReq) ProtoMessage()    {}
func (*GetRuleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{26}
}

func (m *GetRuleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRuleListReq.Unmarshal(m, b)
}
func (m *GetRuleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRuleListReq.Marshal(b, m, deterministic)
}
func (m *GetRuleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRuleListReq.Merge(m, src)
}
func (m *GetRuleListReq) XXX_Size() int {
	return xxx_messageInfo_GetRuleListReq.Size(m)
}
func (m *GetRuleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRuleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRuleListReq proto.InternalMessageInfo

func (m *GetRuleListReq) GetStoreId() string {
	if m != nil {
		return m.StoreId
	}
	return ""
}

func (m *GetRuleListReq) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *GetRuleListReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetRuleListReq) GetOffset() int32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetRuleListRes struct {
	Rows                 []*CrossEntity `protobuf:"bytes,1,rep,name=rows,proto3" json:"rows,omitempty"`
	Total                int32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetRuleListRes) Reset()         { *m = GetRuleListRes{} }
func (m *GetRuleListRes) String() string { return proto.CompactTextString(m) }
func (*GetRuleListRes) ProtoMessage()    {}
func (*GetRuleListRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_cf50d946d740d100, []int{27}
}

func (m *GetRuleListRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRuleListRes.Unmarshal(m, b)
}
func (m *GetRuleListRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRuleListRes.Marshal(b, m, deterministic)
}
func (m *GetRuleListRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRuleListRes.Merge(m, src)
}
func (m *GetRuleListRes) XXX_Size() int {
	return xxx_messageInfo_GetRuleListRes.Size(m)
}
func (m *GetRuleListRes) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRuleListRes.DiscardUnknown(m)
}

var xxx_messageInfo_GetRuleListRes proto.InternalMessageInfo

func (m *GetRuleListRes) GetRows() []*CrossEntity {
	if m != nil {
		return m.Rows
	}
	return nil
}

func (m *GetRuleListRes) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*AddEntityRequest)(nil), "entity.AddEntityRequest")
	proto.RegisterType((*SyncEntityRequest)(nil), "entity.SyncEntityRequest")
	proto.RegisterType((*UpdateEntityRequest)(nil), "entity.UpdateEntityRequest")
	proto.RegisterType((*GetEntityByIdRequest)(nil), "entity.GetEntityByIdRequest")
	proto.RegisterType((*ListEntityRequest)(nil), "entity.ListEntityRequest")
	proto.RegisterType((*UpdateEntityStateRequest)(nil), "entity.UpdateEntityStateRequest")
	proto.RegisterType((*ProcessEntityPendingChangesRequest)(nil), "entity.ProcessEntityPendingChangesRequest")
	proto.RegisterType((*ListEntityResponse)(nil), "entity.ListEntityResponse")
	proto.RegisterType((*Entity)(nil), "entity.Entity")
	proto.RegisterType((*DefaultResponse)(nil), "entity.DefaultResponse")
	proto.RegisterType((*CreateEntityTaskFromPendingChangesRequest)(nil), "entity.CreateEntityTaskFromPendingChangesRequest")
	proto.RegisterType((*GetEntityTaskByIdRequest)(nil), "entity.GetEntityTaskByIdRequest")
	proto.RegisterType((*ListEntityTaskRequest)(nil), "entity.ListEntityTaskRequest")
	proto.RegisterType((*UpdateEntityTaskRequest)(nil), "entity.UpdateEntityTaskRequest")
	proto.RegisterType((*DeleteEntityTaskRequest)(nil), "entity.DeleteEntityTaskRequest")
	proto.RegisterType((*UpdateEntityTaskStatusRequest)(nil), "entity.UpdateEntityTaskStatusRequest")
	proto.RegisterType((*RunEntityTaskRequest)(nil), "entity.RunEntityTaskRequest")
	proto.RegisterType((*ListEntityTaskResponse)(nil), "entity.ListEntityTaskResponse")
	proto.RegisterType((*EntityTask)(nil), "entity.EntityTask")
	proto.RegisterType((*GetChildrenEntityIdsRequest)(nil), "entity.GetChildrenEntityIdsRequest")
	proto.RegisterType((*GetChildrenEntityIdsResponse)(nil), "entity.GetChildrenEntityIdsResponse")
	proto.RegisterType((*GetParentEntityIdsRequest)(nil), "entity.GetParentEntityIdsRequest")
	proto.RegisterType((*GetParentEntityIdsResponse)(nil), "entity.GetParentEntityIdsResponse")
	proto.RegisterType((*GetCrossEntityReq)(nil), "entity.GetCrossEntityReq")
	proto.RegisterType((*GetCrossEntityRes)(nil), "entity.GetCrossEntityRes")
	proto.RegisterType((*CrossEntity)(nil), "entity.CrossEntity")
	proto.RegisterType((*GetRuleListReq)(nil), "entity.GetRuleListReq")
	proto.RegisterType((*GetRuleListRes)(nil), "entity.GetRuleListRes")
}

func init() { proto.RegisterFile("entity.proto", fileDescriptor_cf50d946d740d100) }

var fileDescriptor_cf50d946d740d100 = []byte{
	// 2107 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4f, 0x73, 0x1b, 0x49,
	0x15, 0xaf, 0xb1, 0x46, 0xff, 0x9e, 0x6c, 0xc7, 0x6e, 0x3b, 0xf6, 0x58, 0x8e, 0x59, 0xa7, 0x97,
	0xdd, 0x75, 0xc2, 0x96, 0x15, 0x9b, 0x24, 0xbb, 0xe5, 0xb0, 0x14, 0x4e, 0x36, 0x71, 0x09, 0xb6,
	0x42, 0x6a, 0x1c, 0xb8, 0xaa, 0xc6, 0x33, 0xed, 0x78, 0x36, 0xa3, 0x19, 0x65, 0xa6, 0x95, 0x20,
	0x5c, 0xb9, 0x50, 0x40, 0x41, 0xc1, 0x8d, 0x03, 0x1c, 0xa8, 0x82, 0x13, 0x70, 0xe1, 0x4a, 0xf1,
	0x0d, 0xa8, 0xe2, 0xc2, 0x01, 0x3e, 0x02, 0xdf, 0x82, 0x0b, 0xd5, 0xff, 0x66, 0x5a, 0x9a, 0x91,
	0x2c, 0x39, 0xe6, 0xc6, 0x4d, 0xfd, 0xde, 0xeb, 0x7e, 0xbf, 0x7e, 0xff, 0xa7, 0x05, 0xf3, 0x24,
	0xa4, 0x3e, 0x1d, 0xec, 0xf6, 0xe2, 0x88, 0x46, 0xa8, 0x22, 0x56, 0xcd, 0x1b, 0x2f, 0xa2, 0xe8,
	0x45, 0x40, 0x5a, 0x4e, 0xcf, 0x6f, 0x39, 0x61, 0x18, 0x51, 0x87, 0xfa, 0x51, 0x98, 0x08, 0xa9,
	0x94, 0xcb, 0x57, 0x27, 0xfd, 0xd3, 0x56, 0x42, 0xe3, 0xbe, 0x4b, 0x05, 0x17, 0xff, 0xd1, 0x80,
	0xa5, 0x43, 0xcf, 0x7b, 0xcc, 0x4f, 0xb2, 0xc9, 0xab, 0x3e, 0x49, 0x28, 0x7a, 0x0f, 0x1a, 0x89,
	0x7b, 0x46, 0xba, 0x4e, 0x27, 0x74, 0xba, 0xc4, 0x32, 0xb6, 0x8d, 0x9d, 0xba, 0x0d, 0x82, 0xf4,
	0xd4, 0xe9, 0x12, 0x26, 0xe0, 0xf4, 0x69, 0xd4, 0x21, 0xa1, 0x73, 0x12, 0x10, 0x6b, 0x6e, 0xdb,
	0xd8, 0xa9, 0xd9, 0xc0, 0x48, 0x8f, 0x39, 0x05, 0xb5, 0xa0, 0x72, 0xea, 0x93, 0xc0, 0x4b, 0xac,
	0xd2, 0xb6, 0xb1, 0xd3, 0xd8, 0x5f, 0xdf, 0x15, 0x28, 0x76, 0x15, 0x8a, 0xdd, 0x63, 0x8e, 0xc2,
	0x96, 0x62, 0x68, 0x09, 0x4a, 0x81, 0x13, 0x5a, 0x26, 0x57, 0xc5, 0x7e, 0xa2, 0x45, 0x98, 0xf3,
	0x3d, 0xab, 0xcc, 0x09, 0x73, 0xbe, 0x87, 0xff, 0x66, 0xc0, 0xf2, 0xf1, 0x20, 0x74, 0x87, 0xa1,
	0x0a, 0x29, 0x86, 0xd0, 0x64, 0x52, 0xa3, 0xd0, 0xe7, 0x72, 0xd0, 0xb7, 0x80, 0xe3, 0xec, 0x38,
	0xbd, 0x5e, 0x30, 0xe0, 0xe8, 0x6a, 0x76, 0x9d, 0x51, 0x0e, 0x19, 0x61, 0xf4, 0x66, 0xe6, 0x84,
	0x9b, 0x95, 0x67, 0xba, 0x59, 0x25, 0xbd, 0x19, 0xfe, 0x93, 0x01, 0x2b, 0xdf, 0xeb, 0x79, 0x0e,
	0x25, 0xff, 0xdb, 0xbb, 0x64, 0x50, 0xcd, 0x99, 0xa0, 0x96, 0x33, 0xa8, 0x7f, 0x28, 0xc1, 0xea,
	0x11, 0xa1, 0x02, 0xe7, 0xc3, 0x41, 0xdb, 0xbb, 0x34, 0xd6, 0xf7, 0x61, 0xc1, 0x0f, 0xdd, 0xa0,
	0xef, 0x91, 0x4e, 0x42, 0x1d, 0x4a, 0x24, 0xdc, 0x79, 0x49, 0x3c, 0x66, 0x34, 0x84, 0xc0, 0x74,
	0x23, 0x8f, 0xc8, 0x30, 0xe0, 0xbf, 0x51, 0x13, 0x6a, 0x31, 0x09, 0x78, 0x48, 0x4b, 0x64, 0xe9,
	0x9a, 0x1d, 0x1a, 0x13, 0xda, 0x8f, 0xc3, 0x8e, 0xbc, 0xa8, 0xb0, 0xf2, 0xbc, 0x20, 0x3e, 0x11,
	0xb7, 0xba, 0x0f, 0xeb, 0x4a, 0x73, 0x8f, 0x84, 0x9e, 0x1f, 0xbe, 0xe8, 0xb8, 0x67, 0x4e, 0xf8,
	0x82, 0x24, 0x56, 0x95, 0x63, 0xb8, 0x2e, 0xd9, 0xcf, 0x04, 0xf7, 0x91, 0x60, 0xa2, 0xbb, 0xb0,
	0x36, 0xba, 0x2f, 0x26, 0x6e, 0x14, 0x7b, 0x56, 0x8d, 0x6f, 0x5b, 0x1d, 0xde, 0x66, 0x73, 0x1e,
	0xfa, 0x08, 0xae, 0xa5, 0xbb, 0x9c, 0x98, 0x84, 0x34, 0xb1, 0xea, 0x5c, 0x7c, 0x51, 0x89, 0x0b,
	0xaa, 0x32, 0x36, 0x64, 0x11, 0x7f, 0x00, 0x1b, 0x6a, 0xab, 0x13, 0x04, 0x9d, 0x20, 0x72, 0x9d,
	0xc0, 0xff, 0xa1, 0x48, 0x66, 0xab, 0xc1, 0x0f, 0x51, 0x37, 0x39, 0x0c, 0x82, 0x2f, 0x74, 0x36,
	0xfe, 0x45, 0x05, 0x96, 0xbf, 0xf0, 0x13, 0x3a, 0x63, 0x22, 0xaf, 0x42, 0x59, 0x78, 0x43, 0x38,
	0x4c, 0x2c, 0xfe, 0xef, 0xab, 0x51, 0x5f, 0xad, 0x42, 0x39, 0xf0, 0xbb, 0x3e, 0xe5, 0xde, 0x2a,
	0xdb, 0x62, 0x81, 0xd6, 0xa0, 0x12, 0x9d, 0x9e, 0x26, 0x84, 0x72, 0xe7, 0x94, 0x6d, 0xb9, 0x62,
	0x96, 0x49, 0xa2, 0x98, 0x5a, 0xf3, 0xc2, 0x32, 0xec, 0x37, 0x3b, 0x21, 0x8a, 0x3d, 0x12, 0x5b,
	0x0b, 0xc2, 0xd0, 0x7c, 0xa1, 0x1b, 0x9a, 0x46, 0xd4, 0x09, 0xac, 0xc5, 0x21, 0x43, 0x3f, 0x67,
	0x34, 0xa6, 0x26, 0x21, 0x4e, 0xec, 0x9e, 0x59, 0xd7, 0xf8, 0x5e, 0xb9, 0x62, 0x9b, 0xc5, 0x2f,
	0x65, 0xd0, 0x25, 0x61, 0x50, 0x41, 0x7c, 0x92, 0xa6, 0xb4, 0xef, 0x25, 0xd6, 0xf2, 0x76, 0x69,
	0xc7, 0xb4, 0xd9, 0x4f, 0xb4, 0x07, 0xd5, 0x53, 0x3f, 0xa0, 0x24, 0x4e, 0x2c, 0x34, 0xb9, 0x2c,
	0x28, 0x39, 0xf4, 0x10, 0x96, 0x94, 0x1b, 0x3b, 0x6a, 0xef, 0xca, 0xe4, 0xbd, 0xd7, 0xd4, 0x86,
	0x27, 0xf2, 0x0c, 0x19, 0xee, 0xab, 0x53, 0x86, 0xfb, 0xf5, 0x89, 0xe1, 0xce, 0x2a, 0x9f, 0x9f,
	0x74, 0x62, 0x11, 0xe6, 0xd6, 0x9a, 0xa8, 0x7c, 0x7e, 0x22, 0xe3, 0x1e, 0x3b, 0x60, 0xe9, 0x05,
	0x96, 0x07, 0xec, 0xa5, 0x2b, 0x57, 0x9a, 0x23, 0x25, 0x2d, 0x47, 0x70, 0x17, 0xf0, 0xb3, 0x38,
	0x72, 0x49, 0x92, 0x08, 0x1d, 0xc3, 0x01, 0x79, 0x69, 0x65, 0x6b, 0x50, 0x71, 0x5c, 0x9e, 0x3f,
	0x42, 0x9b, 0x5c, 0xe1, 0xa7, 0x80, 0xf4, 0xf4, 0x4e, 0x7a, 0x51, 0x98, 0x10, 0x84, 0xc1, 0x8c,
	0xa3, 0x37, 0x89, 0x65, 0x6c, 0x97, 0x76, 0x1a, 0xfb, 0x8b, 0xbb, 0x72, 0x3c, 0x90, 0x52, 0x9c,
	0xc7, 0xe0, 0x8b, 0xd8, 0x9a, 0x13, 0xb1, 0xcb, 0x17, 0xf8, 0x1f, 0x26, 0x54, 0x84, 0x58, 0x0e,
	0xe3, 0x16, 0x40, 0xcf, 0x89, 0x69, 0x48, 0xe2, 0x8e, 0xef, 0xf1, 0x5d, 0xa6, 0x5d, 0x97, 0x94,
	0xb6, 0x87, 0x36, 0xa0, 0x96, 0xb8, 0x51, 0x8f, 0x30, 0x66, 0x89, 0x33, 0xab, 0x7c, 0xdd, 0xf6,
	0xd0, 0x26, 0xd4, 0x45, 0x1e, 0x31, 0x9e, 0xc9, 0x79, 0x35, 0x41, 0x10, 0x4c, 0x79, 0x75, 0xd9,
	0xd6, 0x4d, 0xbb, 0x26, 0x08, 0xed, 0x9c, 0x5d, 0x2a, 0xe3, 0x9d, 0x50, 0xd5, 0x0b, 0x55, 0xd6,
	0xe1, 0x6a, 0xd3, 0x75, 0xb8, 0x6f, 0xc2, 0xa2, 0xf8, 0xa5, 0xca, 0x04, 0x4f, 0xf8, 0x09, 0x1b,
	0x17, 0x84, 0xb8, 0xf4, 0x2e, 0xdb, 0x2f, 0xea, 0x4a, 0xba, 0x1f, 0x2e, 0xd8, 0x2f, 0xc4, 0xd5,
	0x7e, 0x0b, 0xaa, 0x6a, 0xa3, 0x28, 0xe8, 0x6a, 0xc9, 0x38, 0x6e, 0x4c, 0x1c, 0x4a, 0x3c, 0x59,
	0x37, 0xd4, 0x92, 0x71, 0xfa, 0x3c, 0x98, 0x3d, 0x59, 0x3c, 0xd4, 0x92, 0x79, 0x4a, 0x0a, 0x75,
	0x4e, 0x06, 0xbc, 0x76, 0xd4, 0xed, 0xba, 0xa4, 0x3c, 0x1c, 0x30, 0xb6, 0x94, 0x64, 0x6c, 0x51,
	0x3c, 0xea, 0x92, 0xf2, 0x70, 0x80, 0x3e, 0x80, 0xc5, 0x9e, 0x88, 0x60, 0x5e, 0xe5, 0xfb, 0xaa,
	0x80, 0x2c, 0x48, 0xea, 0x31, 0x27, 0xa2, 0x0f, 0xa1, 0x22, 0x7c, 0x68, 0x2d, 0xf3, 0xab, 0x8e,
	0x46, 0x99, 0xe4, 0xe2, 0x5b, 0x70, 0xed, 0x73, 0x72, 0xea, 0xf4, 0x03, 0x9a, 0x86, 0xe7, 0x1a,
	0x54, 0x62, 0x92, 0xf4, 0x03, 0xca, 0xa3, 0xab, 0x66, 0xcb, 0x15, 0xfe, 0x97, 0x01, 0xb7, 0x1e,
	0x71, 0x98, 0xe2, 0x8c, 0xe7, 0x4e, 0xf2, 0xf2, 0x49, 0x1c, 0x75, 0x8b, 0x73, 0x68, 0x13, 0xea,
	0xd2, 0xe6, 0x69, 0x98, 0xd6, 0x04, 0xa1, 0x3d, 0x45, 0x42, 0x21, 0x30, 0x39, 0x47, 0xa4, 0x13,
	0xff, 0x8d, 0x6e, 0x40, 0xdd, 0xef, 0x76, 0x89, 0xe7, 0xb3, 0x80, 0x32, 0x65, 0xf1, 0x50, 0x04,
	0x19, 0x6a, 0x31, 0x95, 0x1d, 0x4c, 0x2c, 0xd0, 0x4d, 0x98, 0x57, 0xb3, 0x56, 0x1c, 0xbd, 0x16,
	0x21, 0x5a, 0xb3, 0x1b, 0x72, 0xda, 0x62, 0x24, 0xfc, 0x1d, 0xb0, 0xd2, 0x59, 0x89, 0x5d, 0xe9,
	0x5d, 0xe6, 0x25, 0xfc, 0xd7, 0x39, 0xb8, 0x9e, 0x65, 0x3c, 0x3b, 0x4e, 0x1d, 0xb5, 0x05, 0x90,
	0xda, 0x43, 0xa4, 0xbe, 0x69, 0xd7, 0x95, 0x41, 0x92, 0xa9, 0x4a, 0x8c, 0xf4, 0x77, 0x69, 0xbb,
	0xc4, 0xfb, 0x89, 0x70, 0x74, 0x3e, 0x1e, 0x4c, 0xce, 0x1f, 0x89, 0x87, 0xb4, 0x17, 0x96, 0x8b,
	0x7b, 0x61, 0x65, 0xa8, 0x17, 0xe6, 0x3a, 0x5c, 0x75, 0x62, 0x87, 0xab, 0x4d, 0xee, 0x70, 0xf5,
	0xf1, 0x1d, 0x0e, 0xd2, 0x0e, 0x87, 0xcf, 0x61, 0x5d, 0xaf, 0xfe, 0xba, 0xed, 0x66, 0xae, 0xc7,
	0xb3, 0x7e, 0xc8, 0xe0, 0x6f, 0xc3, 0xfa, 0xe7, 0x24, 0x20, 0x57, 0xa1, 0x1c, 0x9f, 0xc1, 0xd6,
	0xe8, 0x45, 0x84, 0x13, 0xde, 0xa5, 0xbd, 0xa4, 0xbe, 0x37, 0x32, 0xdf, 0xe3, 0x23, 0x58, 0xb5,
	0xfb, 0xe1, 0x15, 0x40, 0xfe, 0x3e, 0xac, 0x8d, 0x46, 0xad, 0x2c, 0x06, 0x1f, 0x0e, 0xf5, 0x2a,
	0x34, 0x5c, 0x45, 0xb8, 0xe4, 0xa4, 0x7e, 0xf5, 0x1f, 0x13, 0x20, 0x13, 0xbd, 0xc2, 0x9e, 0x75,
	0x1d, 0x2a, 0x5f, 0x46, 0x27, 0x59, 0xc3, 0x2a, 0x7f, 0x19, 0x9d, 0x0c, 0xd5, 0x15, 0x3a, 0xe8,
	0x11, 0x59, 0x0a, 0xe4, 0x45, 0x9f, 0x0f, 0x7a, 0x59, 0x5d, 0xa9, 0x68, 0x75, 0x65, 0xa8, 0x52,
	0x55, 0x47, 0x2a, 0xd5, 0x1e, 0x54, 0xdd, 0x28, 0xa4, 0xac, 0x90, 0x5e, 0xd0, 0xac, 0x94, 0x1c,
	0x3a, 0x80, 0x79, 0xf9, 0xb3, 0x73, 0x1a, 0x47, 0x5d, 0x3e, 0x14, 0x4d, 0xd8, 0xd7, 0x90, 0xc2,
	0xac, 0x94, 0x6a, 0x9e, 0xae, 0xeb, 0x9e, 0x2e, 0xc8, 0x72, 0x28, 0xaa, 0xfa, 0xd9, 0x1c, 0xd2,
	0xd0, 0xe7, 0x90, 0xe1, 0xd2, 0x39, 0x3f, 0xb6, 0x74, 0x2e, 0xe8, 0xa5, 0x73, 0x0b, 0x20, 0x70,
	0x12, 0xda, 0x11, 0x2c, 0xd9, 0xa6, 0x18, 0xe5, 0x98, 0xb3, 0x37, 0xa0, 0xc6, 0xd9, 0x24, 0xf4,
	0x64, 0x93, 0xaa, 0xb2, 0xf5, 0xe3, 0xd0, 0x63, 0xe7, 0xc5, 0x84, 0xc6, 0x03, 0xde, 0x99, 0xca,
	0xb6, 0x58, 0xe8, 0xad, 0x72, 0x79, 0x6c, 0xab, 0x44, 0x93, 0x5a, 0xe5, 0x8a, 0x08, 0x90, 0x71,
	0xad, 0x72, 0x55, 0xb0, 0xd3, 0x56, 0x89, 0x9f, 0xc1, 0xe6, 0x11, 0xa1, 0x8f, 0xce, 0xfc, 0xc0,
	0x8b, 0x89, 0x4c, 0x93, 0xb6, 0x97, 0x4c, 0xfd, 0x99, 0x25, 0x6b, 0xd4, 0x5c, 0x56, 0xa3, 0x0e,
	0xe1, 0x46, 0xf1, 0x89, 0x32, 0x5b, 0x6e, 0xc2, 0xbc, 0x2b, 0x99, 0x5a, 0x99, 0x6f, 0x28, 0x5a,
	0xdb, 0x4b, 0xf0, 0x53, 0xd8, 0x38, 0x22, 0x54, 0x7c, 0xa2, 0x5c, 0x05, 0xa4, 0x07, 0xd0, 0x2c,
	0x3a, 0x4f, 0x02, 0x12, 0x19, 0x26, 0x66, 0xbb, 0xb4, 0xeb, 0xa8, 0xe1, 0x2e, 0xc1, 0xbf, 0x37,
	0x60, 0x99, 0x5d, 0x28, 0x8e, 0xd4, 0x40, 0x6c, 0x93, 0x57, 0x17, 0xa3, 0xd8, 0x80, 0x5a, 0xf4,
	0x46, 0xcb, 0xda, 0xba, 0x5d, 0xe5, 0xeb, 0xb6, 0x87, 0xd6, 0x81, 0x87, 0x41, 0x96, 0xb2, 0x15,
	0xb6, 0x6c, 0x7b, 0x59, 0x03, 0x32, 0xf5, 0x06, 0xb4, 0x0d, 0x8d, 0x34, 0x0e, 0x83, 0x01, 0x4f,
	0xd8, 0x9a, 0xad, 0x93, 0xb0, 0x9d, 0x47, 0x98, 0xa0, 0x8f, 0x86, 0xaa, 0xd2, 0x8a, 0xaa, 0x4a,
	0xba, 0xd4, 0xa4, 0xb2, 0xf4, 0xf7, 0x39, 0x68, 0x68, 0xb2, 0x57, 0x3b, 0x4b, 0x67, 0xb5, 0xc4,
	0x9c, 0x3c, 0xf5, 0x94, 0x73, 0x76, 0x1d, 0x1a, 0xb6, 0x2b, 0x23, 0xc3, 0x76, 0xf1, 0x2c, 0x7d,
	0x89, 0xfa, 0xa4, 0x7b, 0xaf, 0x2e, 0xe0, 0x2b, 0xef, 0x69, 0x39, 0x0a, 0x63, 0x73, 0xb4, 0x31,
	0x94, 0xa3, 0xf8, 0x07, 0xb0, 0x78, 0x44, 0xa8, 0xdd, 0x0f, 0x08, 0x6b, 0x21, 0x2c, 0x7e, 0x98,
	0x7d, 0x68, 0x14, 0x13, 0x35, 0xf9, 0xd5, 0xed, 0x2a, 0x5f, 0xb7, 0x85, 0x65, 0xe3, 0xc8, 0xeb,
	0xbb, 0x34, 0x8b, 0x9d, 0xba, 0xa4, 0xe8, 0x41, 0x52, 0x2a, 0x9e, 0x52, 0x4c, 0x7d, 0x4a, 0xc1,
	0xdf, 0x1d, 0xd1, 0xfc, 0xae, 0x71, 0xb1, 0xff, 0x17, 0x04, 0x0b, 0xf2, 0xdb, 0x93, 0xc4, 0xaf,
	0x7d, 0x97, 0xa0, 0x97, 0x50, 0x4f, 0xdf, 0x59, 0x91, 0xa5, 0xce, 0x1b, 0x7d, 0x7a, 0x6d, 0x8e,
	0x4c, 0xd7, 0xf8, 0xee, 0x8f, 0xfe, 0xf9, 0xef, 0x5f, 0xcd, 0xed, 0xe2, 0x5b, 0xfc, 0x75, 0xf7,
	0xf5, 0x7e, 0xab, 0x4b, 0xa8, 0xe3, 0x39, 0xd4, 0x69, 0x09, 0xb9, 0xd6, 0xb9, 0x16, 0x08, 0x6f,
	0x5b, 0x8e, 0xe7, 0x1d, 0x18, 0xb7, 0x11, 0x85, 0x79, 0x7d, 0x70, 0x40, 0x9b, 0xea, 0xd4, 0x82,
	0x67, 0xc7, 0x9c, 0xca, 0x4f, 0xb8, 0xca, 0x3d, 0xfc, 0xf1, 0x74, 0x2a, 0x85, 0xf7, 0x98, 0xd6,
	0x73, 0x58, 0x3a, 0x1e, 0x84, 0xee, 0x90, 0xe6, 0x0d, 0x75, 0x78, 0xee, 0xe9, 0x36, 0xa7, 0xf7,
	0x1b, 0x5c, 0xef, 0x7d, 0xbc, 0x37, 0x9d, 0xde, 0x64, 0x10, 0xba, 0x9a, 0xf2, 0x37, 0xb0, 0x30,
	0xf4, 0x50, 0x89, 0x6e, 0xa8, 0xe3, 0x8b, 0xde, 0x2f, 0x73, 0xca, 0x3f, 0xe5, 0xca, 0xf7, 0xd1,
	0x9d, 0xe9, 0x94, 0x9f, 0x0c, 0x5a, 0xbe, 0xd7, 0x3a, 0xf7, 0xbd, 0xb7, 0xe8, 0x1c, 0x20, 0x9b,
	0x78, 0xb2, 0xfb, 0xe6, 0x1e, 0xe3, 0x9a, 0xcd, 0x22, 0x96, 0xa8, 0xae, 0xf8, 0x3e, 0x57, 0x7f,
	0x07, 0x7f, 0x6d, 0x3a, 0xf5, 0xaf, 0xfa, 0x24, 0x1e, 0xb0, 0x5b, 0xff, 0xd8, 0x80, 0xe5, 0xdc,
	0x4b, 0x07, 0xda, 0x2e, 0x72, 0xb7, 0xfe, 0x08, 0x92, 0xbb, 0xfe, 0x67, 0x5c, 0xff, 0x27, 0x78,
	0x7f, 0x4a, 0xdb, 0xb3, 0xb3, 0x34, 0xe3, 0xff, 0xd6, 0x80, 0xcd, 0x09, 0xaf, 0x21, 0xe8, 0xb6,
	0x52, 0x77, 0xf1, 0x93, 0x49, 0x0e, 0xda, 0x21, 0x87, 0xf6, 0x00, 0xdf, 0x9f, 0x0e, 0x9a, 0x7c,
	0x2e, 0x6c, 0x9d, 0x8b, 0x81, 0xe5, 0x2d, 0x83, 0xf7, 0x67, 0x03, 0xf0, 0xc5, 0xdf, 0x9b, 0x68,
	0x2f, 0xcb, 0xf2, 0x29, 0xbf, 0x4d, 0x9b, 0x05, 0x63, 0x2c, 0xfe, 0x16, 0x07, 0x7c, 0x80, 0xef,
	0xcd, 0x06, 0x98, 0x46, 0x2d, 0xea, 0x24, 0x2f, 0x19, 0xde, 0x9f, 0x18, 0xb0, 0x34, 0x3a, 0xf8,
	0xa3, 0xf7, 0x8a, 0x9c, 0xaa, 0xcd, 0xea, 0x85, 0x58, 0x66, 0xcc, 0x29, 0x06, 0x40, 0x73, 0xeb,
	0x6f, 0x0c, 0x58, 0x2b, 0xfe, 0x00, 0x41, 0x1f, 0x8c, 0x43, 0x33, 0xf4, 0x81, 0x52, 0x88, 0xe9,
	0x11, 0xc7, 0xf4, 0x19, 0xfe, 0x74, 0x66, 0x4c, 0x2d, 0x31, 0xb1, 0x32, 0x68, 0x3f, 0x35, 0x60,
	0x69, 0xf4, 0x3b, 0x2b, 0x33, 0xd1, 0x98, 0x2f, 0xb0, 0x42, 0x38, 0x32, 0xf4, 0x6f, 0xdf, 0x9b,
	0x01, 0x8e, 0x96, 0xfe, 0x3f, 0x13, 0x83, 0xcf, 0xf0, 0x57, 0x7f, 0x96, 0x81, 0xe3, 0x1e, 0x04,
	0x26, 0x41, 0x41, 0x97, 0x84, 0xf2, 0x4b, 0x03, 0x16, 0x87, 0x3f, 0xbe, 0xd0, 0x56, 0xbe, 0xe6,
	0xe8, 0xf6, 0xf8, 0xca, 0x38, 0xb6, 0x2c, 0x4b, 0x0f, 0x38, 0xa0, 0x7b, 0xf8, 0xce, 0x0c, 0x80,
	0xd2, 0xda, 0xf4, 0x3b, 0x83, 0xff, 0x77, 0x94, 0x9b, 0x71, 0xd1, 0xfb, 0x9a, 0x71, 0xc6, 0xcd,
	0xd4, 0xcd, 0xaf, 0x4e, 0x16, 0x92, 0x00, 0x67, 0x8c, 0x6f, 0x35, 0x3e, 0xfb, 0x1e, 0x0f, 0xa2,
	0x5f, 0x1b, 0x80, 0xf2, 0x23, 0x2f, 0xba, 0xa9, 0xa9, 0x2e, 0x1e, 0xaf, 0x9b, 0x78, 0x92, 0x88,
	0xc4, 0x76, 0xc0, 0xb1, 0xdd, 0xc5, 0xad, 0xe9, 0xb0, 0x89, 0x59, 0x5a, 0x22, 0xfb, 0xb9, 0x01,
	0x2b, 0x69, 0xe8, 0x88, 0x7f, 0x2b, 0x98, 0x8b, 0xb2, 0xf6, 0x92, 0x9b, 0xb5, 0x9b, 0x63, 0x59,
	0xc9, 0xac, 0x56, 0x12, 0x63, 0x66, 0x2b, 0xf0, 0x13, 0xca, 0xb0, 0xbc, 0x84, 0x86, 0x36, 0x1c,
	0xa1, 0x35, 0x4d, 0x8f, 0x36, 0xab, 0x35, 0x8b, 0xe9, 0x09, 0xfe, 0x98, 0x2b, 0xff, 0x10, 0xdf,
	0x1c, 0xa7, 0x3c, 0xee, 0x07, 0x44, 0x29, 0x3b, 0xa9, 0xf0, 0x61, 0xf3, 0xeb, 0xff, 0x0d, 0x00,
	0x00, 0xff, 0xff, 0xdb, 0x6f, 0xbe, 0xbd, 0xea, 0x1e, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EntityServiceClient is the client API for EntityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EntityServiceClient interface {
	// 添加Entity
	AddEntity(ctx context.Context, in *AddEntityRequest, opts ...grpc.CallOption) (*Entity, error)
	// 修改Entity
	UpdateEntity(ctx context.Context, in *UpdateEntityRequest, opts ...grpc.CallOption) (*Entity, error)
	// 同步Entity
	SyncUpdateEntity(ctx context.Context, in *SyncEntityRequest, opts ...grpc.CallOption) (*Entity, error)
	// 根据id获取Entity
	GetEntityById(ctx context.Context, in *GetEntityByIdRequest, opts ...grpc.CallOption) (*Entity, error)
	// 查询 Entity 列表
	ListEntity(ctx context.Context, in *ListEntityRequest, opts ...grpc.CallOption) (*ListEntityResponse, error)
	// 修改Entity数据状态
	UpdateEntityState(ctx context.Context, in *UpdateEntityStateRequest, opts ...grpc.CallOption) (*Entity, error)
	// Apply或者Cancel pending changes
	ProcessEntityPendingChanges(ctx context.Context, in *ProcessEntityPendingChangesRequest, opts ...grpc.CallOption) (*Entity, error)
	// 基于变更创建一个task
	CreateEntityTaskFromPendingChanges(ctx context.Context, in *CreateEntityTaskFromPendingChangesRequest, opts ...grpc.CallOption) (*EntityTask, error)
	// 修改Entity Task
	UpdateEntityTask(ctx context.Context, in *UpdateEntityTaskRequest, opts ...grpc.CallOption) (*EntityTask, error)
	// 修改Entity Task 状态
	UpdateEntityTaskStatus(ctx context.Context, in *UpdateEntityTaskStatusRequest, opts ...grpc.CallOption) (*EntityTask, error)
	// 删除Entity Task 状态
	DeleteEntityTask(ctx context.Context, in *DeleteEntityTaskRequest, opts ...grpc.CallOption) (*EntityTask, error)
	// 根据id获取Entity Task
	GetEntityTaskById(ctx context.Context, in *GetEntityTaskByIdRequest, opts ...grpc.CallOption) (*EntityTask, error)
	// 查询 Entity Task 列表
	ListEntityTask(ctx context.Context, in *ListEntityTaskRequest, opts ...grpc.CallOption) (*ListEntityTaskResponse, error)
	// 根据id列表获取所有id的子节点以及子孙节点
	GetChildrenEntityIds(ctx context.Context, in *GetChildrenEntityIdsRequest, opts ...grpc.CallOption) (*GetChildrenEntityIdsResponse, error)
	// 根据id列表获取所有id的上级节点
	GetParentEntityIds(ctx context.Context, in *GetParentEntityIdsRequest, opts ...grpc.CallOption) (*GetParentEntityIdsResponse, error)
	// 获取所有的变更记录
	GetEntityRecordList(ctx context.Context, in *GetCrossEntityReq, opts ...grpc.CallOption) (*GetCrossEntityRes, error)
	// 获取当前的订货规则
	GetRuleList(ctx context.Context, in *GetRuleListReq, opts ...grpc.CallOption) (*GetRuleListRes, error)
}

type entityServiceClient struct {
	cc *grpc.ClientConn
}

func NewEntityServiceClient(cc *grpc.ClientConn) EntityServiceClient {
	return &entityServiceClient{cc}
}

func (c *entityServiceClient) AddEntity(ctx context.Context, in *AddEntityRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/AddEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) UpdateEntity(ctx context.Context, in *UpdateEntityRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/UpdateEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) SyncUpdateEntity(ctx context.Context, in *SyncEntityRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/SyncUpdateEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetEntityById(ctx context.Context, in *GetEntityByIdRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetEntityById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ListEntity(ctx context.Context, in *ListEntityRequest, opts ...grpc.CallOption) (*ListEntityResponse, error) {
	out := new(ListEntityResponse)
	err := c.cc.Invoke(ctx, "/entity.EntityService/ListEntity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) UpdateEntityState(ctx context.Context, in *UpdateEntityStateRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/UpdateEntityState", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ProcessEntityPendingChanges(ctx context.Context, in *ProcessEntityPendingChangesRequest, opts ...grpc.CallOption) (*Entity, error) {
	out := new(Entity)
	err := c.cc.Invoke(ctx, "/entity.EntityService/ProcessEntityPendingChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) CreateEntityTaskFromPendingChanges(ctx context.Context, in *CreateEntityTaskFromPendingChangesRequest, opts ...grpc.CallOption) (*EntityTask, error) {
	out := new(EntityTask)
	err := c.cc.Invoke(ctx, "/entity.EntityService/CreateEntityTaskFromPendingChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) UpdateEntityTask(ctx context.Context, in *UpdateEntityTaskRequest, opts ...grpc.CallOption) (*EntityTask, error) {
	out := new(EntityTask)
	err := c.cc.Invoke(ctx, "/entity.EntityService/UpdateEntityTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) UpdateEntityTaskStatus(ctx context.Context, in *UpdateEntityTaskStatusRequest, opts ...grpc.CallOption) (*EntityTask, error) {
	out := new(EntityTask)
	err := c.cc.Invoke(ctx, "/entity.EntityService/UpdateEntityTaskStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) DeleteEntityTask(ctx context.Context, in *DeleteEntityTaskRequest, opts ...grpc.CallOption) (*EntityTask, error) {
	out := new(EntityTask)
	err := c.cc.Invoke(ctx, "/entity.EntityService/DeleteEntityTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetEntityTaskById(ctx context.Context, in *GetEntityTaskByIdRequest, opts ...grpc.CallOption) (*EntityTask, error) {
	out := new(EntityTask)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetEntityTaskById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ListEntityTask(ctx context.Context, in *ListEntityTaskRequest, opts ...grpc.CallOption) (*ListEntityTaskResponse, error) {
	out := new(ListEntityTaskResponse)
	err := c.cc.Invoke(ctx, "/entity.EntityService/ListEntityTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetChildrenEntityIds(ctx context.Context, in *GetChildrenEntityIdsRequest, opts ...grpc.CallOption) (*GetChildrenEntityIdsResponse, error) {
	out := new(GetChildrenEntityIdsResponse)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetChildrenEntityIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetParentEntityIds(ctx context.Context, in *GetParentEntityIdsRequest, opts ...grpc.CallOption) (*GetParentEntityIdsResponse, error) {
	out := new(GetParentEntityIdsResponse)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetParentEntityIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetEntityRecordList(ctx context.Context, in *GetCrossEntityReq, opts ...grpc.CallOption) (*GetCrossEntityRes, error) {
	out := new(GetCrossEntityRes)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetEntityRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetRuleList(ctx context.Context, in *GetRuleListReq, opts ...grpc.CallOption) (*GetRuleListRes, error) {
	out := new(GetRuleListRes)
	err := c.cc.Invoke(ctx, "/entity.EntityService/GetRuleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EntityServiceServer is the server API for EntityService service.
type EntityServiceServer interface {
	// 添加Entity
	AddEntity(context.Context, *AddEntityRequest) (*Entity, error)
	// 修改Entity
	UpdateEntity(context.Context, *UpdateEntityRequest) (*Entity, error)
	// 同步Entity
	SyncUpdateEntity(context.Context, *SyncEntityRequest) (*Entity, error)
	// 根据id获取Entity
	GetEntityById(context.Context, *GetEntityByIdRequest) (*Entity, error)
	// 查询 Entity 列表
	ListEntity(context.Context, *ListEntityRequest) (*ListEntityResponse, error)
	// 修改Entity数据状态
	UpdateEntityState(context.Context, *UpdateEntityStateRequest) (*Entity, error)
	// Apply或者Cancel pending changes
	ProcessEntityPendingChanges(context.Context, *ProcessEntityPendingChangesRequest) (*Entity, error)
	// 基于变更创建一个task
	CreateEntityTaskFromPendingChanges(context.Context, *CreateEntityTaskFromPendingChangesRequest) (*EntityTask, error)
	// 修改Entity Task
	UpdateEntityTask(context.Context, *UpdateEntityTaskRequest) (*EntityTask, error)
	// 修改Entity Task 状态
	UpdateEntityTaskStatus(context.Context, *UpdateEntityTaskStatusRequest) (*EntityTask, error)
	// 删除Entity Task 状态
	DeleteEntityTask(context.Context, *DeleteEntityTaskRequest) (*EntityTask, error)
	// 根据id获取Entity Task
	GetEntityTaskById(context.Context, *GetEntityTaskByIdRequest) (*EntityTask, error)
	// 查询 Entity Task 列表
	ListEntityTask(context.Context, *ListEntityTaskRequest) (*ListEntityTaskResponse, error)
	// 根据id列表获取所有id的子节点以及子孙节点
	GetChildrenEntityIds(context.Context, *GetChildrenEntityIdsRequest) (*GetChildrenEntityIdsResponse, error)
	// 根据id列表获取所有id的上级节点
	GetParentEntityIds(context.Context, *GetParentEntityIdsRequest) (*GetParentEntityIdsResponse, error)
	// 获取所有的变更记录
	GetEntityRecordList(context.Context, *GetCrossEntityReq) (*GetCrossEntityRes, error)
	// 获取当前的订货规则
	GetRuleList(context.Context, *GetRuleListReq) (*GetRuleListRes, error)
}

// UnimplementedEntityServiceServer can be embedded to have forward compatible implementations.
type UnimplementedEntityServiceServer struct {
}

func (*UnimplementedEntityServiceServer) AddEntity(ctx context.Context, req *AddEntityRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddEntity not implemented")
}
func (*UnimplementedEntityServiceServer) UpdateEntity(ctx context.Context, req *UpdateEntityRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntity not implemented")
}
func (*UnimplementedEntityServiceServer) SyncUpdateEntity(ctx context.Context, req *SyncEntityRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncUpdateEntity not implemented")
}
func (*UnimplementedEntityServiceServer) GetEntityById(ctx context.Context, req *GetEntityByIdRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityById not implemented")
}
func (*UnimplementedEntityServiceServer) ListEntity(ctx context.Context, req *ListEntityRequest) (*ListEntityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEntity not implemented")
}
func (*UnimplementedEntityServiceServer) UpdateEntityState(ctx context.Context, req *UpdateEntityStateRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntityState not implemented")
}
func (*UnimplementedEntityServiceServer) ProcessEntityPendingChanges(ctx context.Context, req *ProcessEntityPendingChangesRequest) (*Entity, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessEntityPendingChanges not implemented")
}
func (*UnimplementedEntityServiceServer) CreateEntityTaskFromPendingChanges(ctx context.Context, req *CreateEntityTaskFromPendingChangesRequest) (*EntityTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEntityTaskFromPendingChanges not implemented")
}
func (*UnimplementedEntityServiceServer) UpdateEntityTask(ctx context.Context, req *UpdateEntityTaskRequest) (*EntityTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntityTask not implemented")
}
func (*UnimplementedEntityServiceServer) UpdateEntityTaskStatus(ctx context.Context, req *UpdateEntityTaskStatusRequest) (*EntityTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEntityTaskStatus not implemented")
}
func (*UnimplementedEntityServiceServer) DeleteEntityTask(ctx context.Context, req *DeleteEntityTaskRequest) (*EntityTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEntityTask not implemented")
}
func (*UnimplementedEntityServiceServer) GetEntityTaskById(ctx context.Context, req *GetEntityTaskByIdRequest) (*EntityTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityTaskById not implemented")
}
func (*UnimplementedEntityServiceServer) ListEntityTask(ctx context.Context, req *ListEntityTaskRequest) (*ListEntityTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEntityTask not implemented")
}
func (*UnimplementedEntityServiceServer) GetChildrenEntityIds(ctx context.Context, req *GetChildrenEntityIdsRequest) (*GetChildrenEntityIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChildrenEntityIds not implemented")
}
func (*UnimplementedEntityServiceServer) GetParentEntityIds(ctx context.Context, req *GetParentEntityIdsRequest) (*GetParentEntityIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParentEntityIds not implemented")
}
func (*UnimplementedEntityServiceServer) GetEntityRecordList(ctx context.Context, req *GetCrossEntityReq) (*GetCrossEntityRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityRecordList not implemented")
}
func (*UnimplementedEntityServiceServer) GetRuleList(ctx context.Context, req *GetRuleListReq) (*GetRuleListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRuleList not implemented")
}

func RegisterEntityServiceServer(s *grpc.Server, srv EntityServiceServer) {
	s.RegisterService(&_EntityService_serviceDesc, srv)
}

func _EntityService_AddEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).AddEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/AddEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).AddEntity(ctx, req.(*AddEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_UpdateEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).UpdateEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/UpdateEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).UpdateEntity(ctx, req.(*UpdateEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_SyncUpdateEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).SyncUpdateEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/SyncUpdateEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).SyncUpdateEntity(ctx, req.(*SyncEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetEntityById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntityByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetEntityById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetEntityById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetEntityById(ctx, req.(*GetEntityByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ListEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ListEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/ListEntity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ListEntity(ctx, req.(*ListEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_UpdateEntityState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntityStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).UpdateEntityState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/UpdateEntityState",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).UpdateEntityState(ctx, req.(*UpdateEntityStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ProcessEntityPendingChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessEntityPendingChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ProcessEntityPendingChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/ProcessEntityPendingChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ProcessEntityPendingChanges(ctx, req.(*ProcessEntityPendingChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_CreateEntityTaskFromPendingChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEntityTaskFromPendingChangesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).CreateEntityTaskFromPendingChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/CreateEntityTaskFromPendingChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).CreateEntityTaskFromPendingChanges(ctx, req.(*CreateEntityTaskFromPendingChangesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_UpdateEntityTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntityTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).UpdateEntityTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/UpdateEntityTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).UpdateEntityTask(ctx, req.(*UpdateEntityTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_UpdateEntityTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEntityTaskStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).UpdateEntityTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/UpdateEntityTaskStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).UpdateEntityTaskStatus(ctx, req.(*UpdateEntityTaskStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_DeleteEntityTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEntityTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).DeleteEntityTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/DeleteEntityTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).DeleteEntityTask(ctx, req.(*DeleteEntityTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetEntityTaskById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntityTaskByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetEntityTaskById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetEntityTaskById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetEntityTaskById(ctx, req.(*GetEntityTaskByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ListEntityTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEntityTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ListEntityTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/ListEntityTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ListEntityTask(ctx, req.(*ListEntityTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetChildrenEntityIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChildrenEntityIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetChildrenEntityIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetChildrenEntityIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetChildrenEntityIds(ctx, req.(*GetChildrenEntityIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetParentEntityIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetParentEntityIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetParentEntityIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetParentEntityIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetParentEntityIds(ctx, req.(*GetParentEntityIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetEntityRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrossEntityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetEntityRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetEntityRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetEntityRecordList(ctx, req.(*GetCrossEntityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetRuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRuleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetRuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/entity.EntityService/GetRuleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetRuleList(ctx, req.(*GetRuleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _EntityService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "entity.EntityService",
	HandlerType: (*EntityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddEntity",
			Handler:    _EntityService_AddEntity_Handler,
		},
		{
			MethodName: "UpdateEntity",
			Handler:    _EntityService_UpdateEntity_Handler,
		},
		{
			MethodName: "SyncUpdateEntity",
			Handler:    _EntityService_SyncUpdateEntity_Handler,
		},
		{
			MethodName: "GetEntityById",
			Handler:    _EntityService_GetEntityById_Handler,
		},
		{
			MethodName: "ListEntity",
			Handler:    _EntityService_ListEntity_Handler,
		},
		{
			MethodName: "UpdateEntityState",
			Handler:    _EntityService_UpdateEntityState_Handler,
		},
		{
			MethodName: "ProcessEntityPendingChanges",
			Handler:    _EntityService_ProcessEntityPendingChanges_Handler,
		},
		{
			MethodName: "CreateEntityTaskFromPendingChanges",
			Handler:    _EntityService_CreateEntityTaskFromPendingChanges_Handler,
		},
		{
			MethodName: "UpdateEntityTask",
			Handler:    _EntityService_UpdateEntityTask_Handler,
		},
		{
			MethodName: "UpdateEntityTaskStatus",
			Handler:    _EntityService_UpdateEntityTaskStatus_Handler,
		},
		{
			MethodName: "DeleteEntityTask",
			Handler:    _EntityService_DeleteEntityTask_Handler,
		},
		{
			MethodName: "GetEntityTaskById",
			Handler:    _EntityService_GetEntityTaskById_Handler,
		},
		{
			MethodName: "ListEntityTask",
			Handler:    _EntityService_ListEntityTask_Handler,
		},
		{
			MethodName: "GetChildrenEntityIds",
			Handler:    _EntityService_GetChildrenEntityIds_Handler,
		},
		{
			MethodName: "GetParentEntityIds",
			Handler:    _EntityService_GetParentEntityIds_Handler,
		},
		{
			MethodName: "GetEntityRecordList",
			Handler:    _EntityService_GetEntityRecordList_Handler,
		},
		{
			MethodName: "GetRuleList",
			Handler:    _EntityService_GetRuleList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "entity.proto",
}
