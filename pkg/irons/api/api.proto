syntax = "proto3";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
//适配每行数据,以Struct代替map<string,string>
//import "google/protobuf/struct.proto";


package irons;
option go_package = "pkg/irons/api";
enum UploadType {
  XLSX = 0;
  CSV = 1;
  JSON = 2;
}
enum UiTaskStatus{
  //成功
  Succ = 0;
  //失败
  Fail =1;
  //处理中
  Processing =2;
  //待提交
  Pending= 3;
}

//UploadRequest 上传数据请求
message UploadRequest {
  //关于上传
  Scope scope=1;
  //上传种类
  UploadType type = 2;
  //导出文件名同请求名
  string name = 3;
  //上传文件的解析处理函数
  string uploadhelper = 4;
  //流式传输
  bytes data = 5;
}
message Scope{
  //上传者pid
  int64 partner_id=1;
  //域
  string domain=2;
  //上传处理的目标服务
  string service_topic=3;
  string service_channel=4;
}
message TaskIntro{
  //简介状态
  UiTaskStatus status = 1;
  string task_name = 2;
  //和后端交互用
  int64 task_id = 3;
  string upload_by =4;
  google.protobuf.Timestamp create_at = 5;
  google.protobuf.Timestamp complate_at = 6;
}
message UploadTaskItemAck{
  //供irons查找对应任务
  int64 task_id =1;
  repeated VerifyRows rows=2;
  bool isContinue =3;
}
message VerifyRows{
  //哪一行
  int64 row=1;
  //这一整行的信息
  repeated VerifyMsg info=2;
}
//TaskItemCell 上传的数据单元项
message TaskItemCell {
  //列中元素
  string value = 1;
  VerifyMsg verify = 2;
}
message VerifyMsg{
  // 第几列
  int64 index = 1;
  //验证后正确状态
  bool status=2;
  //验证信息
  string verify_msg = 3;
}
message UploadTaskItemM{
  int64 total_row =1;
  repeated TaskItemRow rows =2;
  bool isContinue = 3;
}
message TaskItemRow{
  //该行的所在的行序 -》第几行
  int64 row = 1;
  //改行所有列数据
  repeated TaskItemCell items=2;
}
message UploadResponse {
  int64 task_id = 1;
  google.protobuf.Timestamp created = 2;
}

message DownloadRequest{
  Scope scope=2;
  string template=3;
  //导出的文件名
  string name =4;
  //上传文件的解析处理函数
  string downloadhelper = 5;
  //导出字段及其限制
  string query=6;
}
message DownloadTask{
  int64 task_id=1;
  string url=2;
  google.protobuf.Timestamp completed_at=8;
}

message DownloadQueryResponse{
  int64 total=1;
  repeated DownloadTask tasks=2;
}
//第一次传输不带正文数据，即data字段为空
message DownloadTaskRequest{
  int64 task_id=1;
  //头部数据  仅第一次传输时进行解析  收发皆为map[string]interface{}
  bytes title = 2;
  //多行数据  收发皆为[]map[string]interface{}
  bytes data=3;
  string error =4;
}
//第一次传输不带正文数据，即data字段为空
message DownloadCustomRequest{
  int64 task_id=1;
  string shee_name = 2;
  //多行数据  若need_merge为true解析成[][]CustomData，否则[][]interface
  bytes data=3;
  //标识位,为true时会啊检查data解析后的CustomData并进行merge操作,false时不检查()
  bool need_merge = 4;

  string error =5;
}
message Error{
  string err =1;
}
message RespTaskStatus{
  UiTaskStatus status = 1;
}
message VerifyData{
  // 0 初始化  1
  int64 status =1;
}
message CommitStatus{
  int64 status =1;
  string description =2;
}
message ParseHelper{
  //上传？or下载？
  string parse_method = 1;
}
message AllParseHelper{
  int64 total =1;
  repeated ParseHelperStruct helper=2;
}
message ParseHelperStruct{
  string name = 1;
  string description =2;
}
message DownloadURL{
  string url =1;
}
message empty{}
//可加
message GetTaskInfor{
  int64 task_id =1;
}
message TaskInfo{
  string name =1;
  string upload_by =2;
  //文件大小
  string file_size = 3;
  google.protobuf.Timestamp create_at = 4;
  google.protobuf.Timestamp complate_at = 5;
  //文件状态
  string status_sign =6;
  //文件导出时的过滤规则
  string query = 7;
  //文件到处时下载的url
  string url =8;
}
message GetInfo{
  //page 从1开始,两个都为0时获取所有数据
  int64 page=1;
  int64 size=2;
  //上传or下载(为空全搜索)
  string  method =3;
}
message TaskIntros{
  repeated TaskIntro items = 1;
}
message TaskListTemp{
  repeated TaskItemsTemp items = 1;
}
message TaskItemsTemp{
  //简介状态
  UiTaskStatus status = 1;
  string task_com_url = 2;
  string task_name = 3;
  //和后端交互用
  int64 task_id = 4;
  string upload_by =5;
  google.protobuf.Timestamp create_at = 6;
  google.protobuf.Timestamp complate_at = 7;
}
message SqlPlan{
  string query_sql =1;
  string template_url = 2;
  string export_file_name = 3;
}
message SqlDownloadTask{
  int64 task_id=1;
  google.protobuf.Timestamp completed_at=8;
}

service UploadService {
  //Upload 上传任务
  rpc Upload (stream UploadRequest) returns (UploadResponse) {
    option (google.api.http) = {
      post:"/api/v1/common/upload"
      body:"*"
    };
  }

  // 验证业务结果
  rpc ProcessTaskValidatironstream(stream UploadTaskItemAck) returns(stream UploadTaskItemM){}
  //处理业务结果
  rpc ProcessTaskStream(stream UploadTaskItemAck) returns(stream UploadTaskItemM){}

}
service DownloadService{
  //提交下载任务(前端)
  rpc Download(DownloadRequest) returns(DownloadTask){
    option (google.api.http)={
      post:"/api/v1/common/download"
      body:"*"
    };
  }
  //开始下载文件生成
  rpc ProcessDownload(stream DownloadTaskRequest) returns(Error){}
  //业务报表(与自定义报表对接)
  rpc CustomDownload(stream DownloadCustomRequest) returns(Error){}

}
service Common{
  //获取文件下载地址
  rpc GetDownloadURL(GetTaskInfor) returns(DownloadURL) {
    option (google.api.http) = {
      get:"/api/v1/common/download/{task_id}"
    };
  }
  //获取Task详情
  rpc GetTaskInfo(GetTaskInfor) returns(TaskInfo) {
    option (google.api.http) = {
      get:"/api/v1/common/{task_id}"
    };
  }
  //GetCurrPageTask 获取当前页所有对应任务
  rpc GetCurrPageTask(GetInfo) returns(TaskIntros) {
    option (google.api.http) = {
      post:"/api/v1/common/upload/gettask"
      body:"*"
    };
  }
  //TODO GetCurrPageTask 获取当前页所有对应任务(临时方案)
  rpc GetCurrPageTaskTemp(GetInfo) returns(TaskListTemp) {
    option (google.api.http) = {
      post:"/api/v1/common/upload/gettask/temp"
      body:"*"
    };
  }
  //由参数获取解析函数及其描述(参数为)
  rpc GetAllParseHelper(ParseHelper) returns(AllParseHelper) {
    option (google.api.http) = {
      get:"/api/v1/common/gethelpers/{parse_method}"
    };
  }
  //确认提交
  rpc ConfirmCommit(GetTaskInfor) returns(CommitStatus) {
    option (google.api.http) = {
      get:"/api/v1/common/upload/commit/{task_id}"
    };
  }
  //TODO 此接口考虑存在必要
  rpc GetVerifyData(GetTaskInfor) returns(VerifyData) {
    option (google.api.http) = {
      get:"/api/v1/common/verify/{task_id}"
    };
  }
  //前端轮询任务状态
  rpc GetTaskStatus(GetTaskInfor) returns(RespTaskStatus) {
    option (google.api.http) = {
      get:"/api/v1/common/status/{task_id}"
    };
  }
}
service SqlQuery{
  rpc SqlExport(SqlPlan)returns (SqlDownloadTask){
    option (google.api.http) = {
      post:"/api/v1/query/querysql"
      body:"*"
    };
  }
}
