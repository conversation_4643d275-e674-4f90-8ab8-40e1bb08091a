package export

import (
	"bytes"
	"encoding/json"
	"fmt"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/irons/api"
	"net/http"
	"sort"
	"testing"
)

type Remark []string

func (a Remark) Len() int           { return len(a) }
func (a Remark) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a Remark) Less(i, j int) bool { return a[i] < a[j] }
func TestDownloadSrvClient_HandleMessage(t *testing.T) {
	//SkuRemark := []string{"bb", "cc", "aa", "cabde", "01", "091023"}
	//SkuRemark := make([]string, 0)
	//SkuRemark := new(Remark)
	var skuRemarks Remark
	// 将商品所有的sku属性code取出，并按字典序排序
	//for _, skuRemark := range SkuRemark { // 取出sku属性code
	//	skuRemarks = append(skuRemarks, skuRemark)
	//}
	fmt.Println("skuRemarks:", skuRemarks)
	sort.Sort(skuRemarks) // 排序
	fmt.Println("skuRemarks sorted:", skuRemarks)
	fmt.Println("end")

	//query := `{"query":{"is_pre":false,"is_today\":false,\"period_group_type\":\"DAY\",\"start\":\"2021-07-17T00:00:00+08:00\",\"end\":\"2021-08-15T23:59:59+08:00\",\"compare_start\":\"2021-08-16T00:00:00+08:00\",\"compare_end\":\"2021-08-16T23:59:59+08:00\",\"region_group_type\":\"STORE\",\"region_group_level\":0,\"region_search_type\":\"STORE\",\"region_search_ids\":[],\"tag_type\":\"DETAILED\",\"dateType\":\"MONTH\",\"lan\":\"zh-CN\",\"limit\":-1},\"table_name\":\"store_sale\"}`
	//query := `{"query":{"is_pre":false,"is_today":false,"period_group_type":"DAY","start":"2021-08-25T00:00:00+08:00","end":"2021-08-25T23:59:59+08:00","compare_start":"2021-08-25T00:00:00+08:00","compare_end":"2021-08-25T23:59:59+08:00","region_group_type":"STORE","region_group_level":0,"region_search_type":"STORE","region_search_ids":[],"store_type":"","open_status":"","refund_code":"","refund_side":0,"tag_type":"DETAILED","limit":10,"offset":0,"lan":"zh-CN"},"table_name":"refund_analysis_sale"}`
	query := `{"error_ticket_query":{"store_id":"4492575924352483328","status":"DATAERROR","bus_date":"2021-08-20T00%3A00%3A00Z%3B2021-08-26T23%3A59%3A59Z","lan":"zh-CN","limit":-1},"table_name":"abnormal_receipt"}`
	tempdata := api.DownloadRequest{
		Scope: &api.Scope{
			PartnerId:    111,
			Domain:       "hex",
			ServiceTopic: "sales_report_download",
		},
		Template:       "https://next-file.oss-cn-shanghai.aliyuncs.com/omnichannel-pos/stage/abnormal_receipt_report.xlsx",
		Name:           "下载示例",
		Downloadhelper: "",
		Query:          query,
	}
	reqdata, _ := json.Marshal(tempdata)

	req, err := http.NewRequest("POST", "http://localhost:8081/api/v1/common/download", bytes.NewReader(reqdata))
	if err != nil {
		fmt.Println(err)
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Cookie", "name=anny")
	client := &http.Client{}
	resp, err := client.Do(req)
	//fmt.Println("resp:",resp.Body)

	if err != nil {
		fmt.Println("err:", err)
		return
	}
	bs := make([]byte, 1000)
	n, err := resp.Body.Read(bs)
	//if err != nil {
	//	fmt.Println(err)
	//	return
	//}
	fmt.Println(n)
	fmt.Println("bs", string(bs))
	defer resp.Body.Close()
}
