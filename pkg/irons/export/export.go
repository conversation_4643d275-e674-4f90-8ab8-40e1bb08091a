package export

import (
	"context"
	"fmt"
)

const (
	STORE_SALE                 string = "store_sale"
	STORE_CHANNEL_SALE         string = "store_channel_sale"
	STORE_PERIOD_SALE          string = "store_period_sale"
	PRODUCT_SALE               string = "product_sale"
	PRODUCT_CHANNEL_SALE       string = "product_channel_sale"
	PRODUCT_PERIOD_SALE        string = "product_period_sale"
	DISCOUNT_SALE              string = "discount_sale"
	PAYMENT_SALE               string = "payment_sale"
	PAYMENT_PERIOD_SALE        string = "payment_period_sale"
	REFUND_ANALYSIS_SALE       string = "refund_analysis_sale"
	ABNORMAL_RECEIPT           string = "abnormal_receipt"
	PRODUCT_ATTRIBUTE_SALE     string = "product_attribute_sale"
	RECENT_SUMMARY             string = "recent_summary"
	PRODUCT_MAKE_TIME          string = "product_make_time"
	STORE_MAKE_TIME            string = "store_make_time"
	PRODUCT_TAX_SALE           string = "product_tax_sale"
	PRODUCT_SALES_SUMMARY      string = "product_sales_summary"
	STORE_PERFORMANCE_ANALYSIS string = "store_performance_analysis"
	PRODUCT_CATEGORY_SALE      string = "product_category_sale"
	// interface unmarshal interface 无法区分 int 和float 用特殊前缀表示int64 配合tpl使用
	//SplitKey string = "0xA1"
	SplitKey string = ""
)

var Tpl = func(a int64) string {
	return fmt.Sprintf(SplitKey+"%d", a)
}

type CustomData struct {
	Val       interface{} `json:"val"`
	MergeCell int         `json:"merge_cell"`
}

var exportInfo ExportInfo

type Export struct {
	ctx       context.Context
	request   Request
	startTime int
	endTime   int
	isBatch   bool
}

func (p *Export) SetExport(isBatch bool, startTime int, endTime int, tag string) {
	switch p.request.TableName {
	case STORE_SALE: // 门店销售
		exportInfo = new(StoreSales)
	case STORE_CHANNEL_SALE: // 门店渠道
		exportInfo = new(StoreChannelSales)
	case STORE_PERIOD_SALE: // 门店时段
		exportInfo = new(StorePeriodSales)
	case PRODUCT_SALE: // 单品销售
		p.isBatch = isBatch
		p.startTime = startTime
		p.endTime = endTime
		exportInfo = new(ProductSales)
	case PRODUCT_CHANNEL_SALE: // 单品渠道
		p.isBatch = isBatch
		p.startTime = startTime
		p.endTime = endTime
		exportInfo = new(ProductChannelSales)
	case PRODUCT_PERIOD_SALE: // 单品时段
		p.isBatch = isBatch
		p.startTime = startTime
		p.endTime = endTime
		exportInfo = new(ProductPeriodSales)
	case DISCOUNT_SALE: // 交易折扣
		p.isBatch = isBatch
		p.startTime = startTime
		p.endTime = endTime
		exportInfo = new(DiscountSales)
	case PAYMENT_SALE: // 支付统计
		exportInfo = new(PaymentStatistics)
	case PAYMENT_PERIOD_SALE: // 支付时段
		exportInfo = new(PaymentPeriod)
	case REFUND_ANALYSIS_SALE: // 退单分析
		exportInfo = new(RefundAnalysis)
	case ABNORMAL_RECEIPT: // 异常电子小票
		p.ctx = context.WithValue(p.ctx, "lan", p.request.ErrorTicketQuery.Lan)
		exportInfo = new(AbnormalReceipt)
	case PRODUCT_ATTRIBUTE_SALE: // 商品属性表
		p.isBatch = isBatch
		p.startTime = startTime
		p.endTime = endTime
		p.request.Query.TagTypes = tag
		exportInfo = new(ProductAttributeSales)
	case RECENT_SUMMARY: // 近期营业汇总
		exportInfo = new(ExportRecentSummary)
	case PRODUCT_MAKE_TIME: // 单杯出杯时间
		exportInfo = new(ExportProductMakeTime)
	case STORE_MAKE_TIME: // 订单完成时间
		exportInfo = new(ExportStoreMakeTime)
	case PRODUCT_TAX_SALE: // 商品计税表
		exportInfo = new(ProductTaxSales)
	case PRODUCT_SALES_SUMMARY: // 商品销售汇总表
		exportInfo = new(ProductSalesSummary)
	case PRODUCT_CATEGORY_SALE: // 商品类目销售表
		exportInfo = new(ProductCategorySale)
	}
}

func (p *Export) Response() ([]map[string]interface{}, error) {
	preStartTime := p.request.Query.Start
	preEndTime := p.request.Query.End
	if p.isBatch {
		p.request.Query.Start = preStartTime.AddDate(0, 0, p.startTime)
		p.request.Query.End = preStartTime.AddDate(0, 0, p.endTime)
	}
	result, err := exportInfo.PackagingQueryResponse(p.ctx, p.request)
	if p.isBatch {
		p.request.Query.Start = preStartTime
		p.request.Query.End = preEndTime
	}
	return result, err
}

func formatPercent(val float64) string {
	return fmt.Sprintf("%.2f%%", val*100)
}
