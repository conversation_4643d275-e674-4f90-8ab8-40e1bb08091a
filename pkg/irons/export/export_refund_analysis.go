package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report_v2"
)

type RefundAnalysis struct {
}

func (s *RefundAnalysis) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	results, err := report_v2.QueryRefundAnalysisV2(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export refund_analysis_sales_report:%v", err)
		return nil, err
	}

	return results, nil
}
