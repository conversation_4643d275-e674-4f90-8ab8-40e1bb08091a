package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report_v2"
)

type StorePeriodSales struct {
}

func (s *StorePeriodSales) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	results, err := report_v2.QueryStorePeriodSalesV2(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export store_period_sales_report error: %v", err)
		return nil, err
	}
	return results, nil
}
