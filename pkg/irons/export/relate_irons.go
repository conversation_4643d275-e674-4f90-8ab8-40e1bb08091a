package export

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	model2 "gitlab.hexcloud.cn/histore/sales-report/pkg/inavs/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/irons/api"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/oauth"
	"google.golang.org/grpc/keepalive"
	"math"
	"runtime"
	"time"

	"github.com/nsqio/go-nsq"
	"google.golang.org/grpc"
)

type (
	OperateStatus int
	U_Account     model2.UserAccount
	// Task 文件大小  失败原因 业务处理状态 验证状态  解析后总行数  过滤条件
	Task struct {
		//task自身id
		ID int64 `json:"id" gorm:"primaryKey"`
		//task自身name
		Name string `json:"name"`
		//该task状态
		Status OperateStatus `json:"status" gorm:"type:OperateStatus"`
		//过滤条件
		Query string `json:"query" gorm:"type:Query"`
		//文件格式
		Template string `json:"template"`
		//Task从属
		Method   string    `json:"method"` //上传or下载
		UserInfo U_Account `json:"user_info"`
		//帮助解析函数
		ParseHelper string `json:"parse_helper"`
		//用户域
		Domain string `json:"domain"`
		//提交任务到目标服务
		ServiceTopic string    `json:"service_topic"`
		CreatedAt    time.Time `json:"created_at"`
		UpdatedAt    time.Time `json:"updated_at"`
		FileName     string    `json:"file_name"`
		//文件在oss路径
		Url string `json:"url"`
		//文件大小
		ContentSize int64  `json:"content_size"`
		FailReason  string `json:"fail_reason"`
		Lan         string `json:"lan"`
	}

	DownloadSrvClient struct {
		cli api.DownloadServiceClient
		nsq.Handler
	}

	// 异常小票请求参数
	ErrorTicketRequest struct {
		StoreId      string `json:"store_id,omitempty"`
		Status       string `json:"status,omitempty"`
		BusDate      string `json:"bus_date,omitempty"`
		OrderDate    string `json:"order_date,omitempty"`
		CreateDate   string `json:"create_date,omitempty"`
		UpdateDate   string `json:"update_date,omitempty"`
		Limit        int    `json:"limit,omitempty"`  // 默认10, 0则转为10，-1则无限制
		Offset       int    `json:"offset,omitempty"` // 默认0
		IncludeTotal string `json:"include_total,omitempty"`
		Lan          string `json:"lan,omitempty"` // 语言
		ErrMsg       string `json:"err_msg,omitempty"`
		ChannelId    string `json:"channel_id,omitempty"`
	}

	Request struct {
		Query            model.CommonRequest `json:"query"`
		ErrorTicketQuery ErrorTicketRequest  `json:"error_ticket_query"`
		TableName        string              `json:"table_name"`
		Lan              string              `json:"lan"`
	}
)

const LEN int = 10

const BATCH_SIZE = 5

func Init() {
	// 设置超时
	var kacp = keepalive.ClientParameters{
		Time:                10 * time.Second,
		Timeout:             30 * time.Minute,
		PermitWithoutStream: true,
	}
	cc, err := grpc.Dial(config.DefaultConfig.ConnStr.Irons, grpc.WithInsecure(), grpc.WithKeepaliveParams(kacp),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(1024*1024*config.DefaultConfig.Grpc.Maxsendmsgsize)))
	if err != nil {
		logger.Pre().Errorf("irons连接错误:%v", err)
		return
	}
	//提供下载服务模块
	var downloadClient DownloadSrvClient
	//grpc client注册
	downloadClient.cli = api.NewDownloadServiceClient(cc)
	//监听nsq对应的topic
	err = ConnectNSQ(config.DefaultConfig.DownloadTopic, downloadClient)
	if err != nil {
		return
	}
	logger.Pre().Println("与irons连接成功")
}

// HandleMessage app对下载做数据发送
func (a DownloadSrvClient) HandleMessage(msg *nsq.Message) error {
	logger.Pre().WithFields(logrus.Fields{"msg": string(msg.Body)}).Infoln("收到下载任务")
	var (
		err     error
		stream  api.DownloadService_ProcessDownloadClient // 使用grpc流传输数据
		task    Task
		request Request
		export  *Export
	)
	msg.Finish()
	// 第一步、先解析队列消息
	if err := json.Unmarshal(msg.Body, &task); err != nil {
		logger.Pre().Errorf("task解析错误:%v", err)
		return err
	}
	logger.Pre().Printf("收到下载任务:%v,filename=%v\n", task.ID, task.Name)
	if err = json.Unmarshal([]byte(task.Query), &request); err != nil {
		logger.Pre().Errorf("task.Query解析失败:%v", err)
		return err
	}
	if request.Query.Lan == "" {
		request.Query.Lan = request.Lan
	}
	if !IsMyTask(request.TableName) {
		logger.Pre().Infoln("不是报表导出服务")
		return errors.New("不是报表导出服务")
	}
	uid := task.UserInfo.ID
	pid := task.UserInfo.PartnerID
	userInfo := task.UserInfo
	account := &oauth.UserAccount{
		ID:        userInfo.ID,
		PartnerID: userInfo.PartnerID,
		Status:    oauth.State(userInfo.Status),
		Nick:      userInfo.Nick,
		Lan:       oauth.Language(userInfo.Lan),
		Account:   userInfo.Account,
		Email:     userInfo.Email,
		Phone:     userInfo.Phone,
		Admin:     userInfo.Admin,
		CreatedAt: userInfo.CreatedAt,
	}
	ctx := context.WithValue(context.Background(), "user_id", uid)
	ctx = context.WithValue(ctx, "partner_id", pid)
	ctx = context.WithValue(ctx, "user_info", account)
	logger.Pre().Infof("开始查询数据 %v\n", ctx)
	start := time.Now()
	//分情况处理
	startTime := request.Query.Start
	endTime := request.Query.End
	export = &Export{
		ctx:     ctx,
		request: request,
	}
	switch request.TableName {
	case STORE_PERFORMANCE_ANALYSIS:
		exportStorePerformanceAnalysis(&a, export, &task)
		return nil
	}

	if (request.Query.TagType != "SUMMARY") &&
		(request.TableName == PRODUCT_CHANNEL_SALE || request.TableName == PRODUCT_PERIOD_SALE ||
			request.TableName == PRODUCT_SALE || request.TableName == PRODUCT_ATTRIBUTE_SALE ||
			request.TableName == DISCOUNT_SALE) {

		logger.Pre().Info("开始传输导出数据")
		// 使用策略模式来查询不同的报表数据
		timeSub := endTime.Sub(startTime)
		timeDay := timeSub.Hours()
		timeDay = timeDay / 24
		days := int(math.Round(timeDay))
		logger.Pre().Infof("天数: %v", days)

		// 查询结束后，再开始建立stream连接
		logger.Pre().Infof("开始建立tcp 连接")
		for i := 0; i < 3; i++ {
			stream, err = a.cli.ProcessDownload(context.Background())
			if err == nil {
				break
			}
		}
		if err != nil {
			logger.Pre().Errorf("stream创建错误:%v", err)
			return err
		}
		//// 传输查询时间
		startTimeString := request.Query.Start.Format("2006-01-02")
		endTimeString := request.Query.End.Format("2006-01-02")
		m := map[string]string{
			"start":       startTimeString,
			"end":         endTimeString,
			"export_time": time.Now().Format("2006-01-02 15:04:05"),
		}
		marshal, _ := json.Marshal(m)
		// 开始创建发送结构体
		sendData := &api.DownloadTaskRequest{
			TaskId: task.ID,
			Title:  marshal,
		}
		// 先发title(查询时间)
		err = stream.Send(sendData)
		if err != nil {
			logger.Pre().Errorf("传输头title出错:%v", err)
			return err
		}

		periods := make([][]int, 0)
		if request.TableName == PRODUCT_PERIOD_SALE {
			for s := 0; s < days; s += BATCH_SIZE {
				e := int(math.Min(float64(s+BATCH_SIZE-1), float64(days-1)))
				periods = append(periods, []int{s, e})
			}
		} else {
			// 倒序
			for e := days - 1; e >= 0; e -= BATCH_SIZE {
				s := int(math.Max(float64(e-BATCH_SIZE+1), 0))
				periods = append(periods, []int{s, e})
			}
		}

		var tags []string
		if request.TableName == PRODUCT_ATTRIBUTE_SALE {
			tags = []string{TASTE, ATTRIBUTE, FEED}
		} else {
			tags = []string{""}
		}
		for _, tag := range tags {
			logger.Pre().Infof("tag: %v\n", tag)
			for _, period := range periods {
				s := period[0]
				e := period[1]
				export.SetExport(true, s, e, tag)
				respMaps := make([]map[string]interface{}, 0) // 保存查询结果
				respMaps, err = export.Response()
				if err != nil {
					logger.Pre().Errorf("待导出数据查询失败:%v", err)
					return err
				}
				l := len(respMaps)
				logger.Pre().Infof("查询结束[%v,%v;%v]，查询到的导出数据大小:%d，耗费时间%v\n", s, e, days, len(respMaps), time.Since(start).Seconds())
				if l > 100 {
					runtime.GC()
					time.Sleep(time.Duration(200) * time.Millisecond)
				}
				logger.Pre().Infof("开始发送[%v,%v;%v]数据", s, e, days)
				if errSendMsg := SendMsg(err, stream, task, respMaps); errSendMsg != nil {
					return err
				}
			}
		}
	} else {
		respMaps := make([]map[string]interface{}, 0) // 保存查询结果
		// 第二步、使用策略模式来查询不同的报表数据
		export = &Export{
			ctx:     ctx,
			request: request,
		}
		export.SetExport(false, 0, 0, "")
		respMaps, err = export.Response()
		if err != nil {
			logger.Pre().Errorf("待导出数据查询失败:%v", err)
			return err
		}
		logger.Pre().Infof("查询结束，查询到的导出数据大小:%d，耗费时间%v\n", len(respMaps), time.Since(start).Seconds())
		//return nil
		// 第三步、查询结束后，再开始建立stream连接
		for i := 0; i < 3; i++ {
			stream, err = a.cli.ProcessDownload(context.Background())
			if err == nil {
				break
			}
		}
		if err != nil {
			logger.Pre().Errorf("stream创建错误:%v", err)
			return err
		}
		// 传输查询时间
		startTimeString := request.Query.Start.Format("2006-01-02")
		endTimeString := request.Query.End.Format("2006-01-02")
		m := map[string]string{
			"start":       startTimeString,
			"end":         endTimeString,
			"export_time": time.Now().Format("2006-01-02 15:04:05"),
		}
		marshal, _ := json.Marshal(m)
		// 第四步、开始创建发送结构体
		sendData := &api.DownloadTaskRequest{
			TaskId: task.ID,
			Title:  marshal,
		}
		// 先发title(查询时间)
		err = stream.Send(sendData)
		if err != nil {
			logger.Pre().Errorf("传输头title出错:%v", err)
			return err
		}
		logger.Pre().Info("开始传输导出数据")
		if errSendMsg := SendMsg(err, stream, task, respMaps); errSendMsg != nil {
			return err
		}
		logger.Pre().Infof("传输结束，总共发送%d行数据，总耗费时间%v\n", len(respMaps), time.Since(start).Seconds())
	}
	defer stream.CloseSend()
	ret, _ := stream.CloseAndRecv()
	logger.Pre().Infof("文件传输结果:%v，耗费时间:%v\n", ret, time.Since(start).Seconds())
	runtime.GC()
	time.Sleep(time.Duration(200) * time.Millisecond)
	return nil
}

func SendMsg(err error, stream api.DownloadService_ProcessDownloadClient, task Task, respMaps []map[string]interface{}) error {
	start := time.Now()
	//  一行一行发送数据
	for i := 0; i < len(respMaps); i += LEN {
		end := i + LEN
		if end > len(respMaps) {
			end = len(respMaps)
		}
		data, _ := json.Marshal(respMaps[i:end]) // 改为发送byte数组
		for j := 0; j < 3; j++ {
			err = stream.Send(&api.DownloadTaskRequest{
				TaskId: task.ID,
				Data:   data,
			})
			//logger.Pre().Infof("数据err %v",err)
			if err == nil {
				break
			}
		}
		if err != nil {
			logger.Pre().Errorf("发送错误:%v", err)
			ret, e := stream.CloseAndRecv()
			logger.Pre().Errorf("文件传输结果:%v, 异常原因：%v，耗费时间:%v\n", ret, e, time.Since(start).Seconds())
			return err
		}
	}
	logger.Pre().Infof("传输结束，单次一共发送%d行数据，单体耗费时间%v\n", len(respMaps), time.Since(start).Seconds())
	return nil
}

func IsMyTask(name string) bool {
	tableNames := []string{STORE_SALE, STORE_CHANNEL_SALE, STORE_PERIOD_SALE, PRODUCT_SALE, PRODUCT_CHANNEL_SALE,
		PRODUCT_PERIOD_SALE, REFUND_ANALYSIS_SALE, PAYMENT_SALE, PAYMENT_PERIOD_SALE, DISCOUNT_SALE, ABNORMAL_RECEIPT, PRODUCT_ATTRIBUTE_SALE,
		RECENT_SUMMARY, PRODUCT_MAKE_TIME, STORE_MAKE_TIME, PRODUCT_TAX_SALE, PRODUCT_SALES_SUMMARY, STORE_PERFORMANCE_ANALYSIS, PRODUCT_CATEGORY_SALE}
	for _, tableName := range tableNames {
		if tableName == name {
			return true
		}
	}
	return false
}

func ConnectNSQ(topic string, handler nsq.Handler) error {
	consumer, err := nsq.NewConsumer(topic, "info", nsq.NewConfig())
	if err != nil {
		logger.Pre().Errorf("与irons_nsq连接失败:%v", err)
		return err
	}
	consumer.IsStarved()
	consumer.AddHandler(handler)

	err = consumer.ConnectToNSQD(config.DefaultConfig.ConnStr.Ironsmq)
	//fmt.Println("irons_mq:", config.DefaultConfig.ConnStr.Ironsmq)
	if err != nil {
		logger.Pre().Errorf("与irons nsq连接失败:%v", err)
		return err
	}
	return nil
}
