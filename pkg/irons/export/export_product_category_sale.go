package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

type ProductCategorySale struct {
}

func (s *ProductCategorySale) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	resp, err := report.QueryCategorySales(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export product_category_sale error: %v", err)
		return nil, err
	}
	results := make([]map[string]interface{}, 0)
	for _, row := range resp.Rows {
		results = append(results, row.RowToMap())
	}
	return results, nil
}
