package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

type DiscountSales struct {
}

func (s *DiscountSales) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	resp, err := report.QueryDiscountSales(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export discount_sales_report error: %v", err)
		return nil, err
	}
	results := make([]map[string]interface{}, 0)
	for _, row := range resp.Rows {
		results = append(results, row.ToMap())
	}
	return results, nil
}
