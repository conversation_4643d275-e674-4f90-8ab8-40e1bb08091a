package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report_v2"
)

type PaymentPeriod struct {
}

func (s *PaymentPeriod) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	results, err := report_v2.QueryPaymentPeriodV2(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export payment_period_sales_report error: %v", err)
		return nil, err
	}
	//var results = make([]map[string]interface{}, 0, len(paymentResp.Rows))
	//for _, row := range paymentResp.Rows {
	//	results = append(results, row.ToMap())
	//}
	return results, nil

}
