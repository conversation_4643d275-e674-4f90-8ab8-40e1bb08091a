package export

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/irons/api"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
	"time"
)

var colNamesStorePerformanceAnalysis = []string{"流水", "收入", "数量"}

var baseTitlesStorePerformanceAnalysis = []string{"门店名称", "座位数目", "营业额", "碗数", "座位周转率", "平均日销售额", "单均销售额",
	//"Average bowl spending",
}

func exportStorePerformanceAnalysis(a *DownloadSrvClient, export *Export, task *Task) {
	customStream, err := a.cli.CustomDownload(context.Background())
	if err != nil {
		logger.Pre().Errorf("stream创建错误:%v", err)
	}
	defer customStream.CloseSend()

	sts := export.request.Query.Start.Format(config.DateFormat)
	ets := export.request.Query.End.Format(config.DateFormat)
	res := [][]interface{}{
		{"查询日期:" + sts + "--" + ets},
		{"导出日期:" + time.Now().Format(config.DateTimeFormat)},
		{},
	}
	meta, _ := json.Marshal(res)
	customStream.Send(&api.DownloadCustomRequest{
		TaskId: task.ID,
		Data: meta,
		SheeName: "门店表现分析",
	})
	isDetail := export.request.Query.TagType == "DETAIL"
	resp, err := report.StorePerformanceAnalysis(export.ctx, &export.request.Query)
	if err != nil {
		logger.Pre().Errorf("门店表现分析导出错误:%v", err)
		return
	}

	// 生成表头信息
	weekdays := make(map[string]int)
	weekends := make(map[string]int)
	titles := make([]string, 0)
	titles = append(titles, baseTitlesStorePerformanceAnalysis...)
	titles = append(titles, "平均日销售额")
	if isDetail {
		for _, r := range resp.Rows {
			for _, ms := range r.WeekdayOrderTypeMealSegmentTurnovers {
				if _, ok := weekdays[ms.Name]; !ok {
					titles = append(titles, ms.Name + "碗周转")
					weekdays[ms.Name] = len(titles) - 1
				}
			}
		}
	} else {
		for _, r := range resp.Rows {
			for _, ms := range r.WeekdayMealSegmentTurnovers {
				if _, ok := weekdays[ms.MealSegmentName]; !ok {
					titles = append(titles, ms.MealSegmentName + "碗周转")
					weekdays[ms.MealSegmentName] = len(titles) - 1
				}
			}
		}
		for _, r := range resp.Rows {
			for _, ch := range r.WeekdayChannelTurnovers {
				if _, ok := weekdays[ch.OrderTypeName]; !ok {
					titles = append(titles, ch.OrderTypeName + "碗周转")
					weekdays[ch.OrderTypeName] = len(titles) - 1
				}
			}
		}
	}
	titles = append(titles, "平均日销售额")
	weekendsStart := len(titles) - 1
	if isDetail {
		for _, r := range resp.Rows {
			for _, ms := range r.WeekendOrderTypeMealSegmentTurnovers {
				if _, ok := weekends[ms.Name]; !ok {
					titles = append(titles, ms.Name + "碗周转")
					weekends[ms.Name] = len(titles) - 1
				}
			}
		}
	} else {
		for _, r := range resp.Rows {
			for _, ms := range r.WeekendMealSegmentTurnovers {
				if _, ok := weekends[ms.MealSegmentName]; !ok {
					titles = append(titles, ms.MealSegmentName + "碗周转")
					weekends[ms.MealSegmentName] = len(titles) - 1
				}
			}
		}
		for _, r := range resp.Rows {
			for _, ch := range r.WeekendChannelTurnovers {
				if _, ok := weekends[ch.OrderTypeName]; !ok {
					titles = append(titles, ch.OrderTypeName + "碗周转")
					weekends[ch.OrderTypeName] = len(titles) - 1
				}
			}
		}
	}

	// 发送表头信息
	heads := []CustomData {
		{
			Val:       "整体表现",
			MergeCell: len(baseTitlesStorePerformanceAnalysis),
		},
		{
			Val:       "平日（周一到周五）",
			MergeCell: len(weekdays) + 1,
		},
		{
			Val:       "周末（周六、周日）",
			MergeCell: len(weekends) + 1,
		},
	}
	headsData := []interface{}{heads}
	headsBs , _ := json.Marshal(headsData)
	customStream.Send(&api.DownloadCustomRequest{
		TaskId: task.ID,
		Data: headsBs,
		NeedMerge: true,
	})
	logger.Pre().Infof("表头信息:%v \n", titles)
	titlesData := []interface{}{titles}
	titlesBs , _ := json.Marshal(titlesData)
	customStream.Send(&api.DownloadCustomRequest{
		TaskId: task.ID,
		Data: titlesBs,
	})

	// 发送数据信息
	for _, r := range resp.Rows {
		row := make([]interface{}, len(titles))
		var i = 0;
		row[i] = r.StoreName
		i++
		row[i] = r.SeatCount
		i++
		row[i] = r.BusinessAmount
		i++
		row[i] = r.BowlCount
		i++
		row[i] = formatPercent(r.SeatTurnoverRate)
		i++
		row[i] = r.AverageDailySales
		i++
		row[i] = r.AverageSalesPerOrder
		i++
		//row[i] = r.AverageBowlSpending
		//i++
		row[i] = r.WeekdayAverageDailySales
		if isDetail {
			for _, ms := range r.WeekdayOrderTypeMealSegmentTurnovers {
				row[weekdays[ms.Name]] = formatPercent(ms.BowlTurnover)
			}
		} else {
			for _, ms := range r.WeekdayMealSegmentTurnovers {
				row[weekdays[ms.MealSegmentName]] = formatPercent(ms.BowlTurnover)
			}
			for _, ch := range r.WeekdayChannelTurnovers {
				row[weekdays[ch.OrderTypeName]] = formatPercent(ch.BowlTurnover)
			}
		}
		row[weekendsStart] = r.WeekendAverageDailySales
		if isDetail {
			for _, ms := range r.WeekendOrderTypeMealSegmentTurnovers {
				row[weekends[ms.Name]] = formatPercent(ms.BowlTurnover)
			}
		} else {
			for _, ms := range r.WeekendMealSegmentTurnovers {
				row[weekends[ms.MealSegmentName]] = formatPercent(ms.BowlTurnover)
			}
			for _, ch := range r.WeekendChannelTurnovers {
				row[weekends[ch.OrderTypeName]] = formatPercent(ch.BowlTurnover)
			}
		}
		for q, _ := range row {
			if row[q] == nil {
				row[q] = 0
			}
		}
		dataBs , _ := json.Marshal([]interface{}{row})
		customStream.Send(&api.DownloadCustomRequest{
			TaskId: task.ID,
			Data: dataBs,
		})
	}

	ret, _ := customStream.CloseAndRecv()
	logger.Pre().Infof("stream关闭:%v", ret)
}
