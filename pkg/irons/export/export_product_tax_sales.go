package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

type ProductTaxSales struct {
}

func (s *ProductTaxSales) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	resp, err := report.QueryProductTaxSales(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export product_tax_sales_report error: %v", err)
		return nil, err
	}
	results := make([]map[string]interface{}, 0)
	for _, row := range resp.Rows {
		results = append(results, row.RowToMap())
	}
	for _, row := range resp.PackageFees {
		results = append(results, row.PackageToMap())
	}
	for _, row := range resp.SendFees {
		results = append(results, row.SendToMap())
	}
	return results, nil
}
