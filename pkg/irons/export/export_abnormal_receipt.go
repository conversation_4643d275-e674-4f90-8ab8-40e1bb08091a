package export

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/model"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services"
	"net/url"
	"strconv"
	"strings"
)

type AbnormalReceipt struct {
}

func (s *AbnormalReceipt) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	var (
		err error
	)
	// 修改异常电子小票按多门店查询
	storeIds := make([]int64, 0)
	if request.ErrorTicketQuery.StoreId != "" {
		if strings.Contains(request.ErrorTicketQuery.StoreId, ",") {
			arr := strings.Split(request.ErrorTicketQuery.StoreId, ",")
			for _, s := range arr {
				storeId, _ := strconv.ParseInt(s, 10, 64)
				storeIds = append(storeIds, storeId)
			}
		} else {
			storeId, _ := strconv.ParseInt(request.ErrorTicketQuery.StoreId, 10, 64)
			storeIds = append(storeIds, storeId)
		}
	}
	busDate, err := url.QueryUnescape(request.ErrorTicketQuery.BusDate)
	fmt.Printf("busdate:%v, err:%v\n", busDate, err)
	orderDate, err := url.QueryUnescape(request.ErrorTicketQuery.OrderDate)
	createDate, err := url.QueryUnescape(request.ErrorTicketQuery.CreateDate)
	updateDate, err := url.QueryUnescape(request.ErrorTicketQuery.UpdateDate)

	contents := make([]*model.SalesTicketContent, 0)

	if len(storeIds) > 0 {
		for _, storeId := range storeIds {
			// 组装异常电子小票查询条件
			var errTicketReq = &model.ErrorTicketRequest{
				StoreId:      storeId,
				Status:       model.NewSalesInfoStatus(request.ErrorTicketQuery.Status),
				BusDate:      busDate,
				OrderDate:    orderDate,
				CreateDate:   createDate,
				UpdateDate:   updateDate,
				Limit:        request.ErrorTicketQuery.Limit,
				Offset:       request.ErrorTicketQuery.Offset,
				IncludeTotal: request.ErrorTicketQuery.IncludeTotal,
				Lan:          request.ErrorTicketQuery.Lan,
				ErrMsg:       request.ErrorTicketQuery.ErrMsg,
				ChannelId:    request.ErrorTicketQuery.ChannelId,
			}
			result, _, err := services.QuerySalesInfo(ctx, errTicketReq)
			if err != nil && len(result) == 0 {
				logger.Pre().Errorf("异常电子小票查询失败:%v", err)
				return nil, err
			}
			for _, res := range result {
				contents = append(contents, res)
			}
		}
	} else {
		// 组装异常电子小票查询条件
		var errTicketReq = &model.ErrorTicketRequest{
			Status:       model.NewSalesInfoStatus(request.ErrorTicketQuery.Status),
			BusDate:      busDate,
			OrderDate:    orderDate,
			CreateDate:   createDate,
			UpdateDate:   updateDate,
			Limit:        request.ErrorTicketQuery.Limit,
			Offset:       request.ErrorTicketQuery.Offset,
			IncludeTotal: request.ErrorTicketQuery.IncludeTotal,
			Lan:          request.ErrorTicketQuery.Lan,
			ErrMsg:       request.ErrorTicketQuery.ErrMsg,
			ChannelId:    request.ErrorTicketQuery.ChannelId,
		}
		result, _, err := services.QuerySalesInfo(ctx, errTicketReq)
		if err != nil && len(result) == 0 {
			logger.Pre().Errorf("异常电子小票查询失败:%v", err)
			return nil, err
		}
		contents = append(contents, result...)
	}

	var results = make([]map[string]interface{}, 0)
	for _, row := range contents {
		contentModified := make(map[string]interface{})
		err := json.Unmarshal(*row.ContentModified, &contentModified)
		if err != nil {
			logger.Pre().Errorf("content_modified解析失败：%v", err)
			return nil, err
		}
		status := ""
		switch cast.ToString(contentModified["status"]) {
		case "REFUND":
			status = "退单"
		default:
			status = "销售单"
		}

		channelMap := cast.ToStringMap(contentModified["channel"])
		var channelName string
		channelName = cast.ToString(channelMap["source"])
		if channelName != "POS" {
			channelName = cast.ToString(channelMap["tpName"])
		}
		ticketUno := ""
		if channelName == "POS" {
			ticketUno = cast.ToString(contentModified["ticketUno"])
		} else {
			takeaway := cast.ToStringMap(contentModified["takeaway_info"])
			ticketUno = cast.ToString(takeaway["tp_order_id"])
		}
		channelName = ChannelName[strings.ToLower(channelName)]
		amounts := cast.ToStringMap(contentModified["amounts"])
		receivable := cast.ToFloat64(amounts["receivable"])
		payAmount := cast.ToFloat64(amounts["pay_amount"])
		errCodes := cast.ToString(contentModified["error_codes"])
		codes := ""
		for _, errCode := range strings.Split(errCodes, ";") {
			strs := strings.Split(errCode, ":")
			var code string
			if len(strs) >= 2 {
				code = strs[0] + ":" + strs[1]
			}
			codes += ErrorCode[code] + ";"
		}
		codes = strings.Trim(codes, ";") // 去除头尾的特殊字符
		results = append(results, map[string]interface{}{
			"bus_date":        row.BusDate.Format("2006-01-02"),   // 营业日期
			"order_time":      row.OrderTime.Format("2006-01-02"), // 下单时间
			"store_name":      row.StoreName,                      // 门店名称
			"abnormal_reason": codes,                              // 异常原因
			"order_type":      status,                             // 订单类型
			"channel_name":    channelName,                        // 渠道
			"ticketUno":       ticketUno,                          // 订单编号
			"receivable":      receivable,                         // 应付金额
			"pay_amount":      payAmount,                          // 实付金额
		})
	}
	return results, nil
}

var ErrorCode = map[string]string{
	"promotion_amount:ERROR":             "促销金额异常",
	"discount_amount:ERROR":              "折扣金额异常,discount_amount != 商品总折扣+加料总折扣",
	"real_amount:POSITIVE_ERROR":         "负单的实收金额小于或等于0",
	"real_amount:NEGATIVE_ERROR":         "正单的实收金额小于或等于0",
	"business_amount:ERROR":              "营业额异常",
	"expend_amount:ERROR":                "支出金额异常",
	"projected_income:ERROR":             "预计收入金额异常",
	"payment_transfer_amount:ERROR":      "支付转折扣合计金额异常",
	"payments_receivable:POSITIVE_ERROR": "应付金额异常",
	"fees_qty:POSITIVE_ERROR":            "负单用户额外支付的费用数量小于或等于零",
	"projected_amount:POSITIVE_ERROR":    "实收金额异常",
	"merchant_discount_amount:ERROR":     "商家优惠承担金额异常",
	"platform_discount_amount:ERROR":     "平台优惠承担金额异常",
	"store_discount_amount:ERROR":        "门店优惠承担金额异常",
	"discount_merchant_contribute:ERROR": "活动商家出资金额异常",
	"discount_platform_contribute:ERROR": "活动平台出资金额异常",
	"discount_buyer_contribute:ERROR":    "活动用户出资金额异常",
	"discount_other_contribute:ERROR":    "活动第三方出资金额异常",
	"pay_merchant_contribute:ERROR":      "实付商家出资金额异常",
	"pay_platform_contribute:ERROR":      "实付平台出资金额异常",
	"pay_buyer_contribute:ERROR":         "实付用户出资金额异常",
	"pay_other_contribute:ERROR":         "实付第三方出资金额异常",
	"receivable:ERROR":                   "应付金额异常",
	"change_amount:ERROR":                "找零金额异常",
	"overflow_amount:ERROR":              "溢收金额异常",
	"rounding:ERROR":                     "抹零金额异常",
	"delivery_fee：ERROR":                 "配送费总计金额异常",
	"send_fee：ERROR":                     "用户实际配送费异常",
	"discount:ERROR":                     "折扣金额异常",
	"merchant_discount:ERROR":            "活动商家优惠承担金额异常",
	"platform_discount:ERROR":            "活动平台优惠承担金额异常",
	"store_discount:ERROR":               "活动门店优惠承担金额异常",
	"cost:ERROR":                         "用户实际购买金额异常",
	"tp_allowance:ERROR":                 "第三方补贴金额异常",
	"merchant_allowance:ERROR":           "商家补贴金额异常",
	"platform_allowance:ERROR":           "平台补贴金额异常",
	"discount_transfer_amount:ERROR":     "折扣转支付金额异常",
	"real_amount:ERROR":                  "商家实收金额异常",
	"bus_date:EMPTY":                     "营业日期为空",
	"bus_date:MAXDELAY":                  "早于服务器时间减最大延迟天数再减1天",
	"content:TYPEERROR":                  "报文格式错误",
	"order_time:EMPTY":                   "订单时间为空",
	"order_time:MAXDELAY":                "早于服务器时间减最大延迟天数",
	"channel_id:EMPTY":                   "渠道id为空/0",
	"channel_id:UNKNOWN":                 "渠道id不存在",
	"pos_id:EMPTY":                       "POSid为空/0",
	"pos_id:UNKNOWN":                     "POSid不存在",
	"store_id:EMPTY":                     "门店id为空/0",
	"store_id:UNKNOWN":                   "门店id不存在",
	"product_id:EMPTY":                   "商品id为空/0",
	"product_id:UNKNOWN":                 "商品id不存在",
	"fee_id:EMPTY":                       "费用id为空/0",
	"fee_id:UNKNOWN":                     "费用id不存在",
	"tax_id:EMPTY":                       "税id为空/0",
	"tax_id:UNKNOWN":                     "税id不存在",
	"promotion_id:EMPTY":                 "促销id为空/0",
	"promotion_id:UNKNOWN":               "促销id不存在",
	"payment_id:EMPTY":                   "支付id为空/0",
	"payment_id:UNKNOWN":                 "支付id不存在",
	"fee_amount:ERROR":                   "费用金额异常:tip+package_fee+delivery_fee+serviceFee+other_fee != 费用合计",
	"tax_amount:ERROR":                   "税金额异常:taxAmount != 税金额合计",
	"gross_amount:ERROR":                 "交易额异常:gross_amount != 商品总金额",
	"net_amount:ERROR":                   "交易净额异常 != 交易金额 - 折扣金额",
	"pay_amount:ERROR":                   "实付金额异常,pay_amount != 交易净额 + 打包费 + 配送费 + 服务费 + 税费 - 抹零 + 溢出 + 找零",
}

var ChannelName = map[string]string{
	"ele":     "饿了么",
	"eleme":   "饿了么",
	"meituan": "美团",
	"pos":     "POS",
	"wechat":  "微信小程序",
	"app":     "小程序",
	"invlid":  "无需此信息",
	"koubei":  "口碑",
	"alipay":  "支付宝小程序",
}
