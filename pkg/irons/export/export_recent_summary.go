package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

type ExportRecentSummary struct {
}

func (s *ExportRecentSummary) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	request.Query.Limit = -1

	salesResp, err := report.QueryRecentSummary(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("近期营业汇总导出查询错误: %v", err)
		return nil, err
	}

	var results = make([]map[string]interface{}, 0, len(salesResp.Rows))
	for _, row := range salesResp.Rows {
		maps := row.ToMap();
		for _, m := range maps {
			results = append(results, *m)
		}
	}
	return results, nil
}
