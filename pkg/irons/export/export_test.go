package export

import (
	"fmt"
	"math"
	"testing"
)

const days = 0

func TestPeriod(t *testing.T) {
	// 倒序
	periods := make([][]int, 0)
	for e := days - 1; e >= 0; e -= BATCH_SIZE {
		s := int(math.Max(float64(e-BATCH_SIZE+1), 0))
		periods = append(periods, []int{s, e})
	}

	periods2 := make([][]int, 0)
	for s := 0; s < days; s += BATCH_SIZE {
		e := int(math.Min(float64(s+BATCH_SIZE-1), float64(days-1)))
		periods2 = append(periods2, []int{s, e})
	}

	fmt.Printf("%v\n", periods)
	fmt.Printf("%v\n", periods2)
}
