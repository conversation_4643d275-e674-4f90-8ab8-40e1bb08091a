package export

import (
	"context"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"gitlab.hexcloud.cn/histore/sales-report/services/report"
)

type ExportStoreMakeTime struct {
}

func (s *ExportStoreMakeTime) PackagingQueryResponse(ctx context.Context, request Request) ([]map[string]interface{}, error) {
	resp, err := report.QueryStoreMakeTime(ctx, &request.Query)
	if err != nil {
		logger.Pre().Errorf("export store_make_time error: %v", err)
		return nil, err
	}
	results := make([]map[string]interface{}, 0)
	for _, row := range resp.Rows {
		results = append(results, row.ToMap())
	}
	return results, nil
}
