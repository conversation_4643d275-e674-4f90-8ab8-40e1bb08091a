package pools_handler

import (
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/sirupsen/logrus"
)

type Pool struct {
	Key         string
	Concurrence int
	poolCh      chan bool
	handleFunc  func(*primitive.MessageExt) error
}

func NewPool(key string, concurrence int, handleFunc func(*primitive.MessageExt) error) *Pool {
	if concurrence <= 1 {
		concurrence = 1
	}
	pool := &Pool{
		Key:         key,
		poolCh:      make(chan bool, concurrence),
		handleFunc:  handleFunc,
		Concurrence: concurrence,
	}

	return pool
}

func (c *Pool) HandleFunc(data *primitive.MessageExt) error {
	if c.Concurrence <= 1 {
		return c.handleFunc(data)
	} else {
		c.poolCh <- true
		go func() {
			err := c.execInPool(data)
			if err != nil {
				logrus.Errorf("bindListener 消费出现异常 err=%v key=%v body=%v \n", err, c.Key, data.Body)
			}
		}()
	}
	return nil
}

func (c *Pool) execInPool(data *primitive.MessageExt) error {
	var err error
	defer func() {
		<-c.poolCh
		logrus.Infoln("execInPool 执行结束 ======= 当前 len(chan)= ", len(c.poolCh))
	}()
	logrus.Infoln("execInPool 执行开始 ======= 当前 len(chan)= ", len(c.poolCh))
	err = c.handleFunc(data)
	return err
}
