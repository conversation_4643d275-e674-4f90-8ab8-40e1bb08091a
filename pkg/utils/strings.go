package utils

import (
	"math/rand"
	"strconv"
	"sync"
	"time"
	"unsafe"
)

type source struct {
	sync.Mutex
	s rand.Source
}

func (s *source) SafeInt63() int64 {
	s.Lock()
	r := s.s.Int63()
	s.Unlock()
	return r
}

func (s *source) Int63() int64 {
	return s.s.Int63()
}

var src = &source{s: rand.NewSource(time.Now().UnixNano())}

const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
const (
	letterIdxBits = 6                    // 6 bits to represent a letter index
	letterIdxMask = 1<<letterIdxBits - 1 // All 1-bits, as many as letterIdxBits
	letterIdxMax  = 63 / letterIdxBits   // # of letter indices fitting in 63 bits
)

func RandString(n int) string {
	b := make([]byte, n)
	// A src.Int63() generates 63 random bits, enough for letterIdxMax characters!
	for i, cache, remain := n-1, src.SafeInt63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = src.SafeInt63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(letterBytes) {
			b[i] = letterBytes[idx]
			i--
		}
		cache >>= letterIdxBits
		remain--
	}

	return *(*string)(unsafe.Pointer(&b))
}

// 是否是手机号
func IsPhone(str string) bool {
	if len(str) != 11 {
		return false
	}
	if str[0] != '1' {
		return false
	}
	p, err := strconv.ParseUint(str, 10, 64)
	if err != nil {
		return false
	}
	return strconv.FormatUint(p, 10) == str
}
