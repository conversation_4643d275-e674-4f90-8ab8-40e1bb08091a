package utils

import (
	"fmt"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/pelletier/go-toml"
	"github.com/sirupsen/logrus"
	"golang.org/x/text/language"
	"os"
	"path/filepath"
	"sync"
)

var bundleOnce sync.Once
var bundle *i18n.Bundle
var localizerOnce sync.Once
var localizer *i18n.Localizer

func InitBundle() {
	bundle = i18n.NewBundle(language.SimplifiedChinese)
	bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)

	languages := []string{"en", "zh-Hans", "zh-Hant", "id", "ru"}
	for _, lang := range languages {
		pwd, _ := os.Getwd()
		i8nPath := filepath.Join(pwd, "i18n", fmt.Sprintf("active.%s.toml", lang))
		logrus.Infof("多语言载入语言 %v, 查找路径 %v", pwd, i8nPath)
		_, err := bundle.LoadMessageFile(i8nPath)
		if err != nil {
			logrus.Errorf("多语言载入语言 %v 失败, 查找路径 %v, err: %v", lang, i8nPath, err)
		}
	}
}

func getBundle() *i18n.Bundle {
	bundleOnce.Do(func() {
		InitBundle()
	})
	return bundle
}

func SetLocal(lang string) {
	if lang == "zh_CN" {
		lang = "zh-Hans"
	}
	localizer = i18n.NewLocalizer(getBundle(), lang)
}

func GetLocalizer() *i18n.Localizer {
	localizerOnce.Do(func() {
		if localizer == nil {
			localizer = i18n.NewLocalizer(getBundle(), language.SimplifiedChinese.String())
		}
	})
	return localizer
}

func GetI18nLabel(messageID string) string {
	res, err := GetLocalizer().Localize(&i18n.LocalizeConfig{
		DefaultMessage: &i18n.Message{
			ID: messageID,
		},
	})
	if err != nil {
		logrus.Errorf("没有找到翻译 %v: %v, 查找中文", messageID, err)
		// 如果找不到对应语言的，则使用中文
		res2, err2 := i18n.NewLocalizer(getBundle(), language.SimplifiedChinese.String()).Localize(&i18n.LocalizeConfig{
			DefaultMessage: &i18n.Message{
				ID: messageID,
			},
		})
		if err2 != nil {
			logrus.Errorf("没有找到翻译 %v: %v", messageID, err)
			// 如果中文还是没有, 则返回key
			return messageID
		}
		return res2
	}
	return res
}

func GetUnauthorizedErrLabel() string {
	return GetI18nLabel("UnauthorizedErr")
}

func GetAuthFailedErrLabel() string {
	return GetI18nLabel("AuthFailed")
}
