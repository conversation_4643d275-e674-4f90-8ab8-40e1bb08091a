package utils

import (
	"context"
	"encoding/binary"
	"github.com/gofrs/uuid"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/tracing"
	hexUUid "hexcloud.cn/histore/hex-pkg/uuid"
	"net"
	"strconv"

	"github.com/pkg/errors"

	"github.com/sirupsen/logrus"

	"sync/atomic"
)

const (
	kTryCount    = 10
	minPartnerId = 0x7FFFF
	midPartnerId = 0x3FFFFF
	maxPartnerId = 0xFFFFFF
)

func GetUUIdBatch(number int) []string {
	if number <= 0 {
		return nil
	}
	ids := make([]string, 0, number)
	for i := 0; i < number; i++ {
		ids = append(ids, GetUUId())
	}
	return ids
}

func GetUUId() string {
	// return uuid.Must(uuid.NewV1()).String()
	var err error
	uGen := uuidGen.Load().(*uuid.Gen)
	for n := 0; n < kTryCount; n++ {
		u, e := uGen.NewV1()
		if e == nil {
			return u.String()
		}
		err = e
	}
	panic(err)
}

var uuidGen atomic.Value

func init() {
	uuidGen.Store(uuid.DefaultGenerator)
}

func SetHWAddrFunc(partnerID, posCode string) error {
	if partnerID == "" || posCode == "" {
		return errors.New("PartnerID and  PosCode is required")
	}
	id, err := strconv.ParseUint(partnerID, 10, 64)
	if err != nil {
		return errors.Wrap(err, "parse partnerID")
	}
	code, err := strconv.ParseUint(posCode, 10, 64)
	if err != nil {
		return errors.Wrap(err, "parse posCode")
	}

	hwAddr, err := HubHWAddrFunc(id, code)
	if err != nil {
		return errors.Wrap(err, "HubHWAddrFunc")
	}

	hubGen := uuid.NewGenWithHWAF(func() (net.HardwareAddr, error) {
		return hwAddr, nil
	})
	uuidGen.Store(hubGen)
	logrus.Infof("uuidGen使用hwAddr=%s", hwAddr)
	return nil
}

// HubHWAddrFunc partnerType partnerID posCode 共计40bit
// +--------------------------------------------------------------------------------+
// | 0x01: 8 Bit | partnerType: 1~2 Bit | partnerID: 19~24 Bit | posCode: 14~20 Bit |
// +--------------------------------------------------------------------------------+
// partnerType   partnerID    posCode  (位宽)
//
//	1 			19 			20
//	2 			22 			16
//	2 			24 			14
func HubHWAddrFunc(partnerID, posCode uint64) (net.HardwareAddr, error) {
	if partnerID > maxPartnerId {
		return nil, errors.Errorf("partnerID不可超过%v,partnerID=%v", maxPartnerId, partnerID)
	}

	var u64 uint64
	if partnerID <= minPartnerId {
		// 0~0x7FFFF(524,287): 2.5B, 0bbb_bbbb bbbb_bbbb bbbb (pos_code: 2.5B)
		u64 = (partnerID << 20) | (posCode & 0xFFFFF)

	} else if partnerID <= midPartnerId {
		// 0x80000~0x3FFFFF(4,194,303): 3B, 10bb_bbbb bbbb_bbbb bbbb_bbbb (pos_code: 2B)
		u64 = (0b10 << 38) | (partnerID << 16) | (posCode & 0xFFFF)

	} else if partnerID <= maxPartnerId {
		// 0x400000~0xFFFFFF(16,777,215): 3.25B, 11bb_bbbb bbbb_bbbb bbbb_bbbb bb (pos_code: 1.75B, 16384)
		u64 = (0b11 << 38) | (partnerID << 14) | (posCode & 0x3FFF)
	}

	r := make([]byte, 8, 8)
	binary.BigEndian.PutUint64(r, u64)
	r[2] = 0x01
	return r[2:], nil

}

func GetHexUUid(ctx context.Context) (uid int64) {
	tracing.ModuleTracing(ctx, "GetUuid", func(ctx2 context.Context) error {
		uid = hexUUid.GetId()
		return nil
	})
	return
}
