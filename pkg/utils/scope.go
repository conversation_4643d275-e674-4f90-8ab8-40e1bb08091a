package utils

import (
	"context"
	"errors"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/grpc-client"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/multilang"
)

func DataScopeCheck(ctx context.Context, domain string, partnerId int64, userId int64, schema string, storeIds []int64, lan string) ([]int64, error) {
	resource, err := grpc_client.QuerySubjectResource(ctx, domain, partnerId, userId, schema)
	if err != nil {
		return nil, errors.New(multilang.GetErrorMessage(multilang.PermissionQueryFail, lan))
	}
	if resource.FullAccess {
		return storeIds, nil
	}
	if !resource.FullAccess && len(resource.Ids) == 0 {
		return nil, errors.New(multilang.GetErrorMessage(multilang.InsufficientAuthority, lan))
	}
	ids := make([]int64, 0, len(resource.Ids))
	for _, i := range resource.Ids {
		ids = append(ids, cast.ToInt64(i))
	}
	if len(storeIds) == 0 {
		return ids, nil
	}
	res := intersection(storeIds, ids)
	if len(res) == 0 {
		return nil, errors.New(multilang.GetErrorMessage(multilang.InsufficientAuthority, lan))
	}
	return res, nil
}

func intersection(first, second []int64) []int64 {
	m := make(map[int64]bool)
	for _, s := range first {
		m[s] = true
	}
	res := make([]int64, 0)
	for _, s := range second {
		if m[s] { // 过滤掉0
			res = append(res, s)
		}
	}
	return res
}
