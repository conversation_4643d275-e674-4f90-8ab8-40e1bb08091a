package utils

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"hexcloud.cn/histore/hexerror"
)

func ParseTimeWithLimit(timeStr string, limit time.Duration) (from, end *time.Time, err hexerror.HexError) {
	if timeStr == "" {
		return nil, nil, nil
	}
	eachTStr := strings.Split(timeStr, ";")
	if len(eachTStr) > 2 {
		return nil, nil, hexerror.InvalidData(fmt.Sprintf("More time param, expect < 2, now: %d.", len(eachTStr)))
	}
	times := make([]time.Time, 2)
	var e error
	for i, item := range eachTStr {
		if times[i], e = time.Parse(time.RFC3339, item); e != nil {
			return nil, nil, hexerror.InvalidData(fmt.Sprintf("`%s` not a RFC3339 time.", item))
		}
	}

	// 不能超过指定时间
	if offset := times[1].Sub(times[0]); offset > limit {
		return nil, nil, hexerror.InvalidData(fmt.Sprintf("From time: `%s`, End time: `%s`, Diff houre: `%fh` > Except houre: `%fh`", eachTStr[0], eachTStr[1], offset.Hours(), limit.Hours()))
	}
	return &times[0], &times[1], nil
}

func GetDayRound(s string) string {
	var y, c int
	var err error
	tmp := strings.Split(s, "-")
	if len(tmp) != 2 {
		return s
	}
	if y, err = strconv.Atoi(tmp[0]); err != nil {
		return s
	}
	if c, err = strconv.Atoi(tmp[1]); err != nil {
		return s
	}
	yearFirest := time.Date(y, time.January, 1, 0, 0, 0, 0, time.UTC)
	yearFirest = yearFirest.Add(24 * time.Hour * 7 * time.Duration(c-1))
	if yn, cn := yearFirest.ISOWeek(); yn != y || cn != c {
		return s
	}
	sunDay := yearFirest.Add(time.Duration(7-yearFirest.Weekday()) * 24 * time.Hour)
	monDay := sunDay.Add(-6 * 24 * time.Hour)
	return monDay.Format("2006-01-02") + "~" + sunDay.Format("2006-01-02")
}

// ConvertTimeToTimeZone 将给定的时间从北京时间转换到指定时区
func ConvertTimeToTimeZone(beijingTimeStr, timeZoneStr string) string {
	// 解析北京时间字符串
	beijingTime, err := time.ParseInLocation("2006-01-02 15:04:05", beijingTimeStr, time.FixedZone("CST", 8*3600))
	if err != nil {
		return ""
	}

	// 获取指定时区的位置
	timeZone, err := time.LoadLocation(timeZoneStr)
	if err != nil {
		return ""
	}

	// 将北京时间转换为指定时区的时间
	localTime := beijingTime.In(timeZone)

	// 格式化转换后的时间字符串
	localTimeStr := localTime.Format("2006-01-02 15:04:05")
	return localTimeStr
}
