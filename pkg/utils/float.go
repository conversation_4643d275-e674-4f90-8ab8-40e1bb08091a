package utils

import (
	"fmt"
	"github.com/spf13/cast"
	"math"
	"strconv"

	"github.com/shopspring/decimal"
)

const float64EqualityThreshold = 1e-9

func PayDecimalTwo(dotSize int, payAmount string) float64 {
	amount := cast.ToFloat64(payAmount)
	return cast.ToFloat64(DividePadding(2, amount, 100))
}

func Add(dotSize int, numbers ...float64) float64 {
	decimals := make([]decimal.Decimal, 0)
	for _, number := range numbers {
		d := decimal.NewFromFloat(number)
		decimals = append(decimals, d)
	}
	result := decimal.NewFromFloat(0)
	for _, d := range decimals {
		tmp := result.Add(d)
		result = tmp
	}
	f, _ := result.Truncate(int32(dotSize)).Float64()
	return f
}

func Trunc(dotSize int, number float64) float64 {
	f, _ := decimal.NewFromFloat(number).Truncate(int32(dotSize)).Float64()
	return f
}

// 将保留一位小数，但不采用四舍五入方式
func DecimalOne(value float64) float64 {
	str := fmt.Sprintf("%.3f", value)
	str = str[:len(str)-2]
	value, _ = strconv.ParseFloat(str, 64)
	return value
}

// 将保留两位小数，但不采用四舍五入方式
func DecimalTwo(value float64) float64 {
	str := fmt.Sprintf("%.4f", value)
	str = str[:len(str)-2]
	value, _ = strconv.ParseFloat(str, 64)
	return value
}

// 返回浮点数的符号
func Sign(f float64) int {
	d := decimal.NewFromFloat(f)

	return d.Sign()
}

func Sub(dotSize int, n1, n2 float64) float64 {
	dn1 := decimal.NewFromFloat(n1)
	dn2 := decimal.NewFromFloat(n2)
	result := dn1.Sub(dn2)
	f, _ := result.Truncate(int32(dotSize)).Float64()
	return f
}

func Divide(dotSize int32, a float64, b float64) float64 {
	if b == 0 {
		return 0
	}
	c := decimal.NewFromFloat(a)
	d := decimal.NewFromFloat(b)

	result, _ := c.DivRound(d, dotSize).Float64()
	return result
}

func DividePadding(dotSize int32, a float64, b float64) string {
	if b == 0 {
		return "0"
	}
	c := decimal.NewFromFloat(a)
	d := decimal.NewFromFloat(b)
	result := c.Div(d).StringFixedBank(dotSize)
	return result
}

func Mul(dotSize int, n1, n2 float64) float64 {
	dn1 := decimal.NewFromFloat(n1)
	dn2 := decimal.NewFromFloat(n2)
	result := dn1.Mul(dn2)
	f, _ := result.Truncate(int32(dotSize)).Float64()
	return f
}

func Equal(a, b float64) bool {
	return math.Abs(a-b) <= float64EqualityThreshold
}

// Abs returns the absolute value of x.
func Abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

// AbsInt64 returns the absolute value of x.
func AbsInt64(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

func AbsFloat64(a float64) float64 {
	return math.Abs(a)
}

func FloatFmt(value float64) float64 {
	value, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return value
}
