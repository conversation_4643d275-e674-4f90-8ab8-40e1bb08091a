package utils

import (
	"strconv"
	"strings"
)

func Int64Array2PGArrayValue(array []int64) string {
	var sb strings.Builder
	sb.WriteString("{")
	for i, item := range array {
		if i != 0 {
			sb.WriteString(",")
		}
		sb.WriteString(strconv.Itoa(int(item)))
	}
	sb.WriteString("}")
	return sb.String()
}

func StringArray2PGArrayValue(array []string) string {
	var sb strings.Builder
	sb.WriteString("{")
	for i, item := range array {
		if i != 0 {
			sb.WriteString(",")
		}
		sb.WriteString("\"" + item + "\"")
	}
	sb.WriteString("}")
	return sb.String()
}

func Int64Array2PGSearchArrayValue(array []int64) string {
	var sb strings.Builder
	sb.WriteString("(")
	for i, item := range array {
		if i != 0 {
			sb.WriteString(",")
		}
		sb.WriteString(strconv.Itoa(int(item)))
	}
	sb.WriteString(")")
	return sb.String()
}

func IN(id string, ids []string) bool {
	for _, v := range ids {
		if v == id {
			return true
		}
	}
	return false
}
