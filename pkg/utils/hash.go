package utils

import (
	"crypto/sha256"
	"encoding/base64"
	"math"
	"math/rand"

	"golang.org/x/crypto/pbkdf2"
)

const (
	saltLen  = 32
	hashIter = 2000
)

var (
	randLen = int(math.Ceil(float64(saltLen*6-5) / 8))
	b64     = base64.URLEncoding.WithPadding(base64.NoPadding)
)

// 随机盐
func RandomSalt() string {
	salt := make([]byte, randLen)
	if _, err := rand.Read(salt); err != nil {
		return RandString(saltLen)
	}
	return b64.EncodeToString(salt)
}

// 对密码进行哈希计算，盐必须使用 RandomSalt() 获取，否则会因为长度不一致无法计算。
// 固定算法，不可进行更改。
func PWDHash(pwd, salt string) string {
	if len(salt) != saltLen {
		return pwd
	}

	bs := pbkdf2.Key([]byte(pwd), []byte(salt), hashIter, saltLen, sha256.New)
	return b64.EncodeToString(bs)
}
