package rocket_mq

import (
    "context"
    "github.com/apache/rocketmq-client-go/v2/primitive"
    "gitlab.hexcloud.cn/histore/sales-report/config"
    "gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
    "gitlab.hexcloud.cn/histore/sales-report/services/ticket"
    "strconv"
)

var ticketGroupHandles = map[string]HandleFunc{
    TagCheckTicket:           handleWaitCheckTicket,
    TagDiscountApportion:     handleDiscountApportion,
    TagResolveTicket:         handleResolveTicket,
    TagResolveTicketAbnormal: handleResolveAbnormalTicket,
}

func handleGroupTicket(msg *primitive.MessageExt) error {
    tags := msg.GetTags()
    if h, ok := ticketGroupHandles[tags]; ok {
        return h(string(msg.Body))
    }
    return nil
}

// 校验电子小票合不合规则
func handleWaitCheckTicket(body string) error {
    id, err := strconv.Atoi(body)
    if err != nil {
        logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", body)
        logger.Pre().Errorf("From mq, wait check ticket id `%d` is not a int.", id)
        return nil
    }
    ctx := context.WithValue(context.Background(), config.TrackId, id)
    var succ bool
    // 校验电子小票是否合法
    if succ, err = ticket.ValidateTicket(ctx, int64(id)); err == nil && succ {
        PublishMessage(ctx, TagDiscountApportion, body)
    }
    return err
}

func handleDiscountApportion(body string) error {
    id, err := strconv.Atoi(body)
    if err != nil {
        logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
        return nil
    }
    ctx := context.WithValue(context.Background(), config.TrackId, id)
    if err = ticket.DiscountApportion(ctx, int64(id)); err == nil {
        PublishMessage(ctx, TagResolveTicket, body)
    }
    return err
}

func handleResolveTicket(body string) error {
    id, err := strconv.Atoi(body)
    if err != nil {
        logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
        return nil
    }
    ctx := context.WithValue(context.Background(), config.TrackId, id)
    err = ticket.ResolveTicket(ctx, int64(id))
    if err != nil {
        logger.Pre().Errorf("拆解数据存储异常:%v", err)
        return err
    }
    return nil
}

func handleResolveAbnormalTicket(body string) error {
    //id, err := strconv.Atoi(body)
    //if err != nil {
    //	logger.Pre().Errorf("From mq, wait check ticket id `%s` is not a int.", string(body))
    //	return nil
    //}
    //ctx := context.WithValue(context.Background(), config.TrackId, id)
    //err = ticket.ResolveTicketAbnormal(ctx, int64(id))
    //if err != nil {
    //	logger.Pre().Errorf("拆解异常小票数据存储异常:%v", err)
    //	return err
    //}
    return nil
}
