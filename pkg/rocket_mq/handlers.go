package rocket_mq

import (
	"github.com/apache/rocketmq-client-go/v2/primitive"
	log "github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/pools_handler"
)

var groupHandles = map[string]GroupHandleFunc{
	GroupIdTicket: handleGroupTicket,
	GroupIdLine:   handleGroupLine,
}

type GroupHandleFunc func(*primitive.MessageExt) error

type HandleFunc func(string) error

var handlersMap = make(map[string]GroupHandleFunc)

func registerHandler(key string, concurrence int) {
	realHandle := groupHandles[key]
	if realHandle == nil {
		return
	}

	if concurrence <= 1 {
		handlersMap[key] = realHandle
	}

	// 如果传递了并发值，就采用并发池包装下
	if concurrence > 1 {
		poolHandle := pools_handler.NewPool(key, concurrence, realHandle)
		handlersMap[key] = poolHandle.HandleFunc
	}

	log.Infof("[consumer handler] 注册 consumer listener handler key=%v \n", key)
}

func getHandler(key string) GroupHandleFunc {
	return handlersMap[key]
}
