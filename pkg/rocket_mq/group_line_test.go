package rocket_mq

import (
    "fmt"
    "hexcloud.cn/histore/hex-pkg/uuid"
    "testing"
)

func TestParse(t *testing.T) {
    uuid.InitDefaultRollBackIdGenerator(&uuid.RollBackOption{})
    ms, _ := parseProductMakeTime("{\"bus_date\":\"2023-06-20\",\"channel_tp_name\":\"pos\",\"order_time\":\"2023-06-20 10:48:03\",\"partner_id\":1016,\"products\":[{\"finish_time\":\"2023-06-20 10:48:39\",\"make_time\":36358,\"product_code\":\"5432\",\"product_id\":\"4558282622979047424\",\"sku_id\":\"4558282112901447680\"}],\"store_id\":\"4766619454543003648\",\"ticket_id\":\"dda6a144-0f14-11ee-a596-01003f99d897\"}")
    fmt.Printf("%+v\n", ms[0])
    fmt.Println(len(ms))
}
