package rocket_mq

import (
    "context"
    "encoding/json"
    "github.com/apache/rocketmq-client-go/v2/primitive"
    "gitlab.hexcloud.cn/histore/sales-report/model"
    utils "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"
    "gitlab.hexcloud.cn/histore/sales-report/repo"
    "hexcloud.cn/histore/hex-pkg/uuid"
    "time"
)

var lineGroupHandles = map[string]HandleFunc{
    TagProductMakeTime: handleProductMakeTime,
}

func handleGroupLine(msg *primitive.MessageExt) error {
    tags := msg.GetTags()
    if h, ok := lineGroupHandles[tags]; ok {
        return h(string(msg.Body))
    }
    return nil
}

func handleProductMakeTime(body string) error {
    models, err := parseProductMakeTime(body)
    if err != nil {
        return err
    }
    return repo.DefaultMakeTimeRepository.SaveBatch(context.Background(), models)
}

func parseProductMakeTime(body string) ([]*model.SalesProductMakeTime, error) {
    var v pmt
    err := json.Unmarshal([]byte(body), &v)
    if err != nil {
        return nil, err
    }
    models := make([]*model.SalesProductMakeTime, len(v.Products))
    now := time.Now()
    busDate, err := time.Parse("2006-01-02", v.BusDate)
    orderTime, err := time.Parse("2006-01-02 15:04:05", v.OrderTime)
    for i, product := range v.Products {
        finisTime, _ := time.Parse("2006-01-02 15:04:05", product.FinishTime)

        models[i] = &model.SalesProductMakeTime{
            Id:          uuid.GetId(),
            PartnerId:   int64(v.PartnerId),
            TicketId:    v.TicketId,
            StoreId:     utils.StringMust2Int(v.StoreId),
            ChannelCode: v.ChannelCode,
            ChannelName: v.ChannelTpName,
            BusDate:     time.Date(busDate.Year(), busDate.Month(), busDate.Day(), 0, 0, 0, 0, time.UTC),
            OrderTime:   orderTime,
            OrderHour:   int32(orderTime.Hour()),

            ProductId:   utils.StringMust2Int(product.SkuId),
            MakeTime:    int32(product.MakeTime / 1000),
            FinishTime:  finisTime,
            ProductDate: time.Date(finisTime.Year(), finisTime.Month(), finisTime.Day(), 0, 0, 0, 0, time.UTC),
            MakeHour:    int32(finisTime.Hour()),
            Deleted:     0,
            Created:     now,
            Updated:     now,
            UpdatedBy:   0,
        }
    }
    return models, nil
}

type pmt struct {
    TicketId      string       `json:"ticket_id"`
    PartnerId     int32        `json:"partner_id"`
    StoreId       string       `json:"store_id"`
    ChannelCode   string       `json:"channel_code"`
    ChannelTpName string       `json:"channel_tp_name"`
    BusDate       string       `json:"bus_date"`
    OrderTime     string       `json:"order_time"`
    Products      []pmtProduct `json:"products"`
}

type pmtProduct struct {
    ProductId   string `json:"product_id"`
    SkuId       string `json:"sku_id"`
    ProductCode string `json:"product_code"`
    MakeTime    int64  `json:"make_time"`
    FinishTime  string `json:"finish_time"`
}
