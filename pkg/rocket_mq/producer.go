package rocket_mq

import (
	"context"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

func PublishMessage(ctx context.Context, tag string, msg string) error {
	return PublishMessageTo(ctx, TopicSalesReport, tag, msg)
}

func PublishMessageTo(ctx context.Context, topic string, tag string, msg string) error {
	mqMsg := &primitive.Message{
		Topic: topic,
		Body:  []byte(msg),
	}
	mqMsg.WithTag(tag)

	resp, err := DefaultPublish(ctx, mqMsg)
	if err != nil {
		logrus.Warnln("PublishMessage 发送roceketMq 消息失败 ", err)
	} else {
		logrus.Warnln("PublishMessage 发送roceketMq 消息成功 ", resp)
	}
	return err
}

func DefaultPublish(ctx context.Context, rmqMsg *primitive.Message) (*primitive.SendResult, error) {
	if producerClient == nil {
		return nil, errors.New("producerClient 客户端未初始化")
	}
	return producerClient.SendSync(ctx, rmqMsg)
}
