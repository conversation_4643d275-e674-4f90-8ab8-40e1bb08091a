package rocket_mq

import (
	"context"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/sirupsen/logrus"
	"gitlab.hexcloud.cn/histore/sales-report/config"
)

func bindListener(consumerClient rocketmq.PushConsumer, myConsumer *config.RMQConsumerGroup) error {
	selector := consumer.MessageSelector{}
	if myConsumer.Expr != "" {
		selector = consumer.MessageSelector{
			Type:       consumer.TAG,
			Expression: myConsumer.Expr,
		}
	}
	handlekey := buildGroupHandlerKey(myConsumer)
	return consumerClient.Subscribe(myConsumer.Topic, selector, func(ctx context.Context,
		msgs ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		for i := range msgs {
			msgData := msgs[i]

			consumerHandler := getHandler(handlekey)
			// 消费消息
			if consumerHandler != nil {
				logrus.Infof("bindListener %v 收到消息，即将消费 ===== %v/%v  msg=%v \n", handlekey, i+1, len(msgs), msgData)
				err := consumerHandler(msgData)
				if err != nil {
					logrus.Errorf("bindListener 消费出现异常 err=%v key=%v body=%v \n", err, handlekey, msgData.Body)
				}
			} else {
				logrus.Warnf("bindListener 消费者未注册消费者处理函数 key=%v body=%v \n", handlekey, msgData.Body)
			}
		}
		return consumer.ConsumeSuccess, nil
	})
}
