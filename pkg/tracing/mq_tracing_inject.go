package tracing

import (
	"context"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/log"
	"github.com/sirupsen/logrus"
)

type TracingMQConsumer interface {
	Handle(ctx context.Context, data []byte) (bool, error)
}
type taskReaderWriter struct {
	MT map[string]string
}

func (w taskReaderWriter) Set(key, val string) {
	w.MT[key] = val
}

func (w taskReaderWriter) ForeachKey(handler func(key, val string) error) error {
	for k, val := range w.MT {
		if err := handler(k, val); err != nil {
			return err
		}
	}
	return nil
}

// InjectSpanToContextFromConsumer  从 消费者 中 获取 spanContext 并生成子span 注入到 context 中去
func InjectSpanToContextFromConsumer(ctx context.Context, topic string, mt map[string]string) (context.Context, opentracing.Span) {
	if mt == nil {
		return ctx, nil
	}
	tracer := GetTracer()
	spanCtx, err := tracer.Extract(opentracing.TextMap, taskReaderWriter{mt})
	if err != nil {
		return ctx, nil
	}

	var receiveSpan opentracing.Span
	if spanCtx != nil {
		receiveSpan = tracer.StartSpan("MQ.RECEIVER."+topic, opentracing.ChildOf(spanCtx))
	} else {
		receiveSpan = tracer.StartSpan("MQ.RECEIVER." + topic)
	}

	ctx = opentracing.ContextWithSpan(ctx, receiveSpan)
	return ctx, receiveSpan
}

// ExtractTracingMapFromCtx 从 context 中 获取 span 生成 map
func ExtractTracingMapFromCtx(ctx context.Context, topic, ticket_id string) map[string]string {
	parentSpan := opentracing.SpanFromContext(ctx)
	tracer := GetTracer()
	var mqSan opentracing.Span
	if parentSpan == nil {
		mqSan = tracer.StartSpan("MQ.PRODUCER." + topic)
	} else {
		mqSan = tracer.StartSpan("MQ.PRODUCER."+topic, opentracing.ChildOf(parentSpan.Context()))
	}
	mt := make(map[string]string)
	mtWriter := taskReaderWriter{mt}
	err := tracer.Inject(mqSan.Context(), opentracing.TextMap, mtWriter)
	if err != nil {
		mqSan.LogFields(log.String("event", "Tracer.Inject() failed"), log.Error(err))
	}

	mqSan.SetTag("ticket_id", ticket_id)

	mqSan.LogFields(log.Bool("producer", true))

	for key, val := range mt {
		logrus.WithField(key, val).Debugf("tracing  InjectSpanMQFromCtx inject====>")
	}

	mqSan.Finish()
	if parentSpan != nil {
		parentSpan.Finish()
	}
	return mt
}
