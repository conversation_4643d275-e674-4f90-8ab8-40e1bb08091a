package tracing

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"github.com/opentracing/opentracing-go/log"
	"github.com/spf13/cast"
	"net/http"
)

const TaceTagKeyTp = "tp_order_id"
const TaceTagKeyTk = "ticket_id"
const spanKey = "uber_id_gin"

func SetTraceSpanTagWithTpOrderId(ctx context.Context, tpOrderId string) {
	span := opentracing.SpanFromContext(ctx)
	if span != nil {
		span.SetTag(TaceTagKeyTp, tpOrderId)
	}
}

func SetTraceSpanTagWithTicketId(ctx context.Context, ticketId string) {
	span := opentracing.SpanFromContext(ctx)
	if span != nil {
		span.SetTag(TaceTagKeyTk, ticketId)
	}
}
func SetSpanTag(ctx context.Context, key, val string) {
	span := opentracing.SpanFromContext(ctx)
	if span != nil {
		span.SetTag(key, val)
	}
}

// 请求追踪中间件
func ModuleTracing(ctx context.Context, name string, handle func(ctx2 context.Context) error) error {
	var parentContext opentracing.SpanContext
	ctx = GetGinSpanCtx(ctx)
	tracer := opentracing.GlobalTracer()
	parentSpan := opentracing.SpanFromContext(ctx)
	if parentSpan != nil {
		parentContext = parentSpan.Context()
	}
	span := tracer.StartSpan(name, opentracing.ChildOf(parentContext))
	defer span.Finish()

	ctx = opentracing.ContextWithSpan(ctx, span)
	err := handle(ctx)

	if err != nil {
		span.SetTag("error", true)
		span.LogFields(log.Error(err))
		span.LogFields(log.String("event", "error"))
	}

	partnerId := cast.ToString(ctx.Value(`partner_id`))
	if partnerId != "" {
		span.SetTag(`partner_id`, partnerId)
	}

	return err
}

func GetSpan(ctx context.Context) opentracing.Span {
	return opentracing.SpanFromContext(ctx)
}

type HTTPHeadersCarrier struct {
	http.Header
}

// 为HTTPHeadersCarrier实现ForeachKey方法
func (c HTTPHeadersCarrier) ForeachKey(handler func(key, val string) error) error {
	for k, vals := range c.Header {
		for _, v := range vals {
			if err := handler(k, v); err != nil {
				return err
			}
		}
	}
	return nil
}

func TracerMiddle() gin.HandlerFunc {
	return func(c *gin.Context) {
		tracer := opentracing.GlobalTracer()
		head := opentracing.HTTPHeadersCarrier(c.Request.Header)

		// Extract the context from the headers
		spanCtx, err := tracer.Extract(opentracing.HTTPHeaders, head)
		if err != nil && err != opentracing.ErrSpanContextNotFound {
			// Log the error but continue without tracing
			log.Error(err)
		}

		// Start a new span
		span := tracer.StartSpan(c.Request.URL.String(), ext.RPCServerOption(spanCtx))
		defer span.Finish()

		// Set the span in the Gin context
		c.Set(spanKey, span)

		// Create a new context with the span and set it in the request
		ctx := opentracing.ContextWithSpan(c.Request.Context(), span)
		c.Request = c.Request.WithContext(ctx)

		// Continue processing the request
		c.Next()

		// Optionally, you can log additional information about the request
		span.LogFields(
			log.String("method", c.Request.Method),
			log.String("path", c.Request.URL.Path),
			log.Int("status", c.Writer.Status()),
		)
	}
}

func GetGinSpanCtx(ctx context.Context) context.Context {
	value := ctx.Value(spanKey)
	if span, ok := value.(opentracing.Span); ok {
		ctx = opentracing.ContextWithSpan(ctx, span)
		return ctx
	}
	return ctx
}
