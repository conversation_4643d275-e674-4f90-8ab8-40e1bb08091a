package otgrpc

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/opentracing/opentracing-go/mocktracer"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

const (
	firstCode = codes.OK
	lastCode  = codes.DataLoss
)

func TestSpanTags(t *testing.T) {
	tracer := mocktracer.New()
	for code := firstCode; code <= lastCode; code++ {
		// Client error
		tracer.Reset()
		span := tracer.StartSpan("test-trace-client")
		err := status.Error(code, "")
		SetSpanTags(span, err, true)
		span.Finish()

		// Assert added tags
		rawSpan := tracer.FinishedSpans()[0]
		expectedTags := map[string]interface{}{
			"response_code":  code,
			"response_class": ErrorClass(err),
		}
		if err != nil && ErrorClass(err) != Success {
			expectedTags["error"] = true
		}
		assert.Equal(t, expectedTags, rawSpan.Tags())

		// Server error
		tracer.Reset()
		span = tracer.StartSpan("test-trace-server")
		err = status.Error(code, "")
		SetSpanTags(span, err, false)
		span.Finish()

		// Assert added tags
		rawSpan = tracer.FinishedSpans()[0]
		expectedTags = map[string]interface{}{
			"response_code":  code,
			"response_class": ErrorClass(err),
		}
		if err != nil && ErrorClass(err) == ServerError {
			expectedTags["error"] = true
		}
		assert.Equal(t, expectedTags, rawSpan.Tags())
	}
}

func TestMQSpan(t *testing.T) {
	ctx := context.Background()
	receiveSpan := MockMQ2GrpcServerRootSpan(context.Background(), opentracing.GlobalTracer(), "mq_topic111")
	newCtx := opentracing.ContextWithSpan(ctx, receiveSpan)
	fmt.Println(newCtx)

	var parentCtx opentracing.SpanContext
	if parent := opentracing.SpanFromContext(newCtx); parent != nil {
		parentCtx = parent.Context()
	}
	fmt.Println(parentCtx)
}
