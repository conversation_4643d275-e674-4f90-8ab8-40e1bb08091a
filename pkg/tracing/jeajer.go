package tracing

import (
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/sirupsen/logrus"
	"github.com/uber/jaeger-client-go"
	"github.com/uber/jaeger-client-go/transport"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"time"
)

func GetTracer() opentracing.Tracer {
	return opentracing.GlobalTracer()
}

// Init 创建 tracer
func Init() opentracing.Tracer {
	traceCfg := config.DefaultConfig.Trace
	tracer := newJaegerTracerDirect(traceCfg)
	//tracer := NewJaegerTracerAgent(traceCfg.ServiceName)
	opentracing.SetGlobalTracer(tracer)
	return tracer
}

// NewJaegerTracerAgent 通过 Jaeger Agent 上报
func NewJaegerTracerAgent(service string) opentracing.Tracer {
	sender, err := jaeger.NewUDPTransport("", 0)

	if err != nil {
		panic(err)
	}

	tracer, _ := jaeger.NewTracer(service,
		jaeger.NewConstSampler(true),
		jaeger.NewRemoteReporter(sender, jaeger.ReporterOptions.Logger(jaeger.StdLogger)),
	)

	return tracer
}

func newJaegerTracerDirect(traceCfg config.TraceConfig) opentracing.Tracer {
	var err error
	var sample jaeger.Sampler

	// 采样设置
	value := traceCfg.SimpleValue
	switch traceCfg.SimpleType {
	case jaeger.SamplerTypeConst: // const
		value := value.(bool)
		sample = jaeger.NewConstSampler(value)
	case jaeger.SamplerTypeRateLimiting: // ratelimiting
		value := value.(float64)
		sample = jaeger.NewRateLimitingSampler(value)
	case jaeger.SamplerTypeProbabilistic: // probabilistic
		value := value.(float64)
		sample, err = jaeger.NewProbabilisticSampler(value)
		if err != nil {
			panic(err)
		}
	default:
		value := true
		sample = jaeger.NewConstSampler(value)
	}

	// 上报设置
	var reportOptions []jaeger.ReporterOption
	reportOptions = append(reportOptions, jaeger.ReporterOptions.Logger(jaeger.StdLogger))
	if traceCfg.ReportQueueSize > 0 {
		reportOptions = append(reportOptions, jaeger.ReporterOptions.QueueSize(traceCfg.ReportQueueSize))
	}
	interval := time.Second * 5
	if traceCfg.ReportBufferFlushInterval > 0 {
		interval = time.Second * time.Duration(traceCfg.ReportBufferFlushInterval)
	}
	reportOptions = append(reportOptions, jaeger.ReporterOptions.BufferFlushInterval(interval))

	fmt.Println(traceCfg, "traceCfg111")

	sender := transport.NewHTTPTransport(traceCfg.Address)
	tracer, _ := jaeger.NewTracer(traceCfg.ServiceName,
		sample,
		jaeger.NewRemoteReporter(sender, reportOptions...),
	)
	if err != nil {
		logrus.WithError(err).Errorln("tracing create failed")
	}
	return tracer
}
