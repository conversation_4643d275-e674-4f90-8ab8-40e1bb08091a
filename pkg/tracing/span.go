package tracing

import (
	"context"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/log"
)

type TraceSpan struct {
	name   string
	tracer opentracing.Tracer
	span   opentracing.Span
}

func NewTraceSpan(name string) *TraceSpan {
	tracer := GetTracer()
	ts := &TraceSpan{
		name:   name,
		tracer: tracer,
	}
	return ts
}

// DefaultMode 直接生成 根span
// 场景: cli 或 consumer 的接受者
func (ts *TraceSpan) DefaultMode() *TraceSpan {
	ts.span = ts.tracer.StartSpan(ts.name)
	return ts
}

// ParentMode 从父级span 中获取 生成 当前span
func (ts *TraceSpan) ParentMode(parentSpan *TraceSpan) *TraceSpan {
	ts.span = ts.tracer.StartSpan(ts.name, opentracing.ChildOf(parentSpan.context()))
	return ts
}

// CtxMode 从上下文中获取 parentSpan
// context 中的span 来源 -- grpc 拦截器注入的(从上个 grpc 服务获取的 或 单独生成的)
// 场景: grpc 服务的方法中，从 ctx 中 获取span
func (ts *TraceSpan) CtxMode(ctx context.Context) *TraceSpan {
	parent := opentracing.SpanFromContext(ctx)
	if parent != nil {
		ts.span = ts.tracer.StartSpan(ts.name, opentracing.ChildOf(parent.Context()))
		return ts
	}
	return ts.DefaultMode()
}

// SetTag 设置 标签
func (ts *TraceSpan) SetTag(key string, value interface{}) *TraceSpan {
	ts.span.SetTag(key, value)
	return ts
}

// Log 设置 log 标识
// 示例: span.Log(log.String(), log.Int64())
func (ts *TraceSpan) Log(fields ...log.Field) *TraceSpan {
	ts.span.LogFields(fields...)
	return ts
}

// LogError 错误log
// 示例: span.LogError("OSError", err)
func (ts *TraceSpan) LogError(errKind string, err error) *TraceSpan {
	ts.span.LogFields(log.Event("error"), log.String("error.kind", errKind), log.Error(err))
	return ts
}

// LogString 错误log
// 示例: span.LogString("key", "value")
func (ts *TraceSpan) LogString(key, value string) *TraceSpan {
	ts.span.LogFields(log.String(key, value))
	return ts
}

func (ts *TraceSpan) context() opentracing.SpanContext {
	return ts.span.Context()
}

// InjectCtx 把 当前 span 注入到 context 中
func (ts *TraceSpan) InjectCtx(ctx context.Context) context.Context {
	return opentracing.ContextWithSpan(ctx, ts.span)
}

// Finish 结束span
func (ts *TraceSpan) Finish() {
	ts.span.Finish()
}
