package tracing

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
)

func TracingBefore(ctx context.Context, opName string) opentracing.Span {
	spanName := fmt.Sprintf("%s", opName)
	// 自己喜欢的operationName
	span, _ := opentracing.StartSpanFromContext(ctx, spanName)
	return span
}

func TracingFinishSpan(span opentracing.Span, tagOpt ...string) {
	if span == nil {
		return
	}
	tagKey := ""
	for i := 0; i < len(tagOpt); i++ {
		if tagKey == "" {
			tagKey = tagOpt[i]
		} else {
			span.SetTag(tagKey, tagOpt[i])
			tagKey = ""
		}
	}
	span.Finish()
}
