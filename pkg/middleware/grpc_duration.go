// Auto generate code. Help should mail to ['<EMAIL>']
package middleware

import (
	"context"
	"fmt"
	"time"

	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

func UnaryLogDurationServerInterceptor(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	start := time.Now().UTC()
	resp, invokeErr := handler(ctx, req)
	duration := fmt.Sprintf("%d", int64(time.Since(start)/time.Millisecond))
	md := metadata.Pairs("duration", duration)
	err := grpc.SetHeader(ctx, md)
	if err != nil {
		logger.Pre().Error("set duration header fail")
	}

	logger.Pre().Info("duration: ", duration)

	return resp, invokeErr
}
