package oauth

import "gitlab.hexcloud.cn/histore/sales-report/pkg/utils"

// 用户账号信息，可共享
type UserAccount struct {
	ID        uint64 `json:"id,omitempty" gorm:"<-:create;primaryKey"`                    // 用户 ID
	PartnerID uint64 `json:"partner_id,omitempty" gorm:"<-:create;uniqueIndex:p_account"` // 用户租户 ID,uniqueIndex:p_email;uniqueIndex:p_phone;
	StoreID   uint64 `json:"store_id,omitempty"`

	Status  State    `json:"status,omitempty" gorm:"size:10"`                        // 用户状态
	Nick    string   `json:"nick,omitempty" gorm:"size:20"`                          // 用户昵称，最长 20 个字符
	Lan     Language `json:"lan,omitempty" gorm:"size:5"`                            // 用户语言
	Account string   `json:"account,omitempty" gorm:"size:20;uniqueIndex:p_account"` // 用户账号，最长 20 个字符
	Email   string   `json:"email,omitempty" gorm:"size:50"`                         // 用户邮箱，最长 50 个字符,uniqueIndex:p_email;
	Phone   string   `json:"phone,omitempty" gorm:"size:20"`                         // 用户手机号，最长 20 个字符,uniqueIndex:p_phone;
	Admin   bool     `json:"admin,omitempty"`                                        // 是否是管理员（相较于租户而言）

	CreatedAt uint64 `json:"created_at,omitempty" gorm:"<-:create;autoCreateTime"` // 用户创建时间
}

func (ua *UserAccount) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":         ua.ID,
		"partner_id": ua.PartnerID,
		"status":     ua.Status,
		"nick":       ua.Nick,
		"lan":        ua.Lan,
		"account":    ua.Account,
		"email":      ua.Email,
		"phone":      ua.Phone,
		"admin":      ua.Admin,
		"created_at": ua.CreatedAt,
	}
}

// 用户账号信息，不可共享
type UserProfile struct {
	Password string `json:"password,omitempty" gorm:"size:43"` // 用户密码，固定 43 个字符，基于 base64 编码规则固定
	Salt     string `json:"salt,omitempty" gorm:"size:32"`     // 密码盐，每次创建/重制密码都需要改变，固定 32 个字符
	Address  string `json:"address,omitempty"`                 // 用户地址

	UpdatedAt uint64 `json:"updated_at,omitempty" gorm:"autoCreateTime"` // 用户更新时间

	UserAccount
}

// 设置密码，用于新增/更新密码。
func (up *UserProfile) SetPwd(pwd string) {
	up.Password = pwd
	up.Salt = utils.RandomSalt()
	up.Password = utils.PWDHash(pwd, up.Salt)
}

// 验证密码是否正确。
func (up *UserProfile) CheckPwd(pwd string) bool {
	return utils.PWDHash(pwd, up.Salt) == up.Password
}
