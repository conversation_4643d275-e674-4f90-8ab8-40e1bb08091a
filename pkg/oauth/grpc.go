package oauth

import (
	"context"
	"encoding/json"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"io"
	"log"
	"strconv"

	"github.com/golang/protobuf/jsonpb"
	"github.com/grpc-ecosystem/go-grpc-middleware/util/metautils"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/jhump/protoreflect/dynamic"
	"github.com/spf13/cast"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"hexcloud.cn/histore/hexerror"
)

// NewDebugGrpcServerInterceptor 调试使用的 GRPC 服务端拦截器。
// 按正常方式取不到用户信息时，将使用用户传入的默认用户信息。
// ！！！！
// 生产不允许使用本方法，仅供本地连接不上授权服务时调试使用。
// 生产须使用 NewGrpcServerInterceptorAutoReject
// ！！！！
func NewDebugGrpcServerInterceptor(pID, uID uint64) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		md := metautils.ExtractIncoming(ctx)
		partnerID := md.Get("partner_id")
		userID := md.Get("user_id")
		user := &UserAccount{}
		user.PartnerID = cast.ToUint64(partnerID)
		user.ID = cast.ToUint64(userID)
		var pid, uid uint64
		if partnerID != "" && userID != "" {
			pid, _ = strconv.ParseUint(partnerID, 10, 64)
			uid, _ = strconv.ParseUint(userID, 10, 64)
		} else if token := md.Get("token"); token != "" {
			// 开始鉴权认证
			if user, err := Introspect(ctx, token); err == nil {
				pid = user.PartnerID
				uid = user.ID
			}
		}

		if pid == 0 || uid == 0 {
			pid = pID
			uid = uID
			log.Printf("使用调试数据：partner_id(%d), user_id(%d)，生产不允许此模式，可能会带来 panic.", pID, uID)
		}
		ctx = context.WithValue(ctx, PartnerID, pid)
		ctx = context.WithValue(ctx, UserID, uid)
		ctx = context.WithValue(ctx, UserInfo, user)
		return handler(ctx, req)
	}
}

// NewGrpcServerInterceptor 服务端拦截器，将设置 partner_id、user_id 到 context 中。
// 如果未包含授权信息，将直接返回错误，不再进行后续操作。
//
// 参数：methodWhiteList，部分方法需要没用户信息也能执行，忽略授权错误。
//                       例如健康检查，可以将方法加入到白名单。（健康检查默认已加入）
//
// 本地在连接不上授权服务的情况下，推荐使用 NewDebugGrpcServerInterceptor 进行调试，但不允许使用在生产上。
var posMethods = []string{"/sales_report.SalesReport/PosPromotion", "/sales_report.SalesReport/PosInboundReport"}

func NewGrpcServerInterceptor(methodWhiteList ...string) grpc.UnaryServerInterceptor {
	methodWhiteList = append(methodWhiteList, "/health_check.HealthCheck/Ping")

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		for _, method := range methodWhiteList {
			if method == info.FullMethod {
				return handler(ctx, req)
			}
		}

		// 需要store_id
		isPos := false
		for _, method := range posMethods {
			if method == info.FullMethod {
				isPos = true
				break
			}
		}

		md := metautils.ExtractIncoming(ctx)
		partnerID := md.Get("partner_id")
		userID := md.Get("user_id")
		storeIdStr := md.Get("store_id")
		userInfoStr := md.Get("user_info")
		var pid, uid, storeId uint64
		var err1, err2 error
		if !isPos && partnerID != "" && userID != "" {
			pid, err1 = strconv.ParseUint(partnerID, 10, 64)
			uid, err2 = strconv.ParseUint(userID, 10, 64)
			if err1 != nil || err2 != nil {
				return nil, hexerror.Unauthorized("user info invalid")
			}
		} else if isPos && partnerID != "" && userID != "" && storeIdStr != "" {
			pid, err1 = strconv.ParseUint(partnerID, 10, 64)
			uid, err2 = strconv.ParseUint(userID, 10, 64)
			if err1 != nil || err2 != nil {
				return nil, hexerror.Unauthorized("user info invalid")
			}
			storeId, err1 = strconv.ParseUint(storeIdStr, 10, 64)
			if err1 != nil {
				return nil, hexerror.Unauthorized("store id invalid")
			}
		} else if token := md.Get("token"); token != "" {
			// 开始鉴权认证
			token = token[len(Bearer):]
			if user, err := Introspect(ctx, token); err == nil {
				pid = user.PartnerID
				uid = user.ID
				storeId = user.StoreID
			} else {
				logger.Pre().Errorf("grpc request: %s, token introspect error: %v", info.FullMethod, err)
			}
		}
		if pid == 0 {
			return nil, NoAuth
		}
		// 防止别的系统校验userId不为0
		if uid == 0 {
			uid = 104
		}
		ua := &UserAccount{}
		json.Unmarshal([]byte(userInfoStr), ua)
		if ua.StoreID != 0 && ua.StoreID != storeId {
			storeId = ua.StoreID
		}
		ctx = context.WithValue(ctx, PartnerID, pid)
		ctx = context.WithValue(ctx, UserID, uid)
		ctx = context.WithValue(ctx, StoreID, storeId)
		ctx = context.WithValue(ctx, UserInfo, &UserAccount{
			ID:        uid,
			PartnerID: pid,
			StoreID:   storeId,
		})
		return handler(ctx, req)
	}
}

// GrpcClientInterceptor 客户端拦截器。
// 将 context 中的用户信息传到下一个服务。
func GrpcClientInterceptor(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	kvPair := []string{
		"partner_id", cast.ToString(ctx.Value(PartnerID)),
		"user_id", cast.ToString(ctx.Value(UserID)),
	}

	ctx = metadata.AppendToOutgoingContext(ctx, kvPair...)
	return invoker(ctx, method, req, reply, cc, opts...)
}

// GrpcGatewayInterceptor 网关的拦截器
// 取出 token 进行鉴权操作，将 partner_id 和 user_id 写入 context
// 如果没有授权信息，将返回错误。
func GrpcGatewayInterceptor(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) (err error) {
	token := cast.ToString(ctx.Value("Authorization"))
	if token == "" {
		token = cast.ToString(ctx.Value("authorization"))
	}
	if len(token) <= len(Bearer) {
		return NoAuth
	}
	var user *UserAccount
	if user, err = Introspect(ctx, token[len(Bearer):]); err != nil {
		return hexerror.Unauthorized(err.Error())
	}

	kvPair := []string{
		"partner_id", cast.ToString(user.PartnerID),
		"user_id", cast.ToString(user.ID),
	}
	ctx = metadata.AppendToOutgoingContext(ctx, kvPair...)
	return invoker(ctx, method, req, reply, cc, opts...)
}

type HttpResponse struct {
	StatusCode  int             `json:"status_code"`
	Code        string          `json:"code,omitempty"`
	Message     string          `json:"message,omitempty"`
	Source      string          `json:"source,omitempty"`
	Payload     json.RawMessage `json:"payload,omitempty"`
	Detail      interface{}     `json:"detail,omitempty"`
	Description interface{}     `json:"description"`
}

type HexMarshaler struct {
	marshaler runtime.Marshaler
}

func NewHexMarshaler() runtime.Marshaler {
	mm := &HexMarshaler{
		marshaler: &runtime.JSONPb{},
	}
	return mm
}

func (m *HexMarshaler) Marshal(v interface{}) ([]byte, error) {
	var r *HttpResponse
	switch rv := v.(type) {
	case hexerror.HexError:
		r = &HttpResponse{
			StatusCode:  1,
			Code:        rv.Code(),
			Source:      rv.Source(),
			Detail:      rv.Detail(),
			Description: rv.Description(),
		}
	case error:
		hexe := hexerror.HexErrorFromString(rv.Error())
		r = &HttpResponse{
			StatusCode:  1,
			Code:        hexe.Code(),
			Source:      hexe.Source(),
			Detail:      hexe.Detail(),
			Description: hexe.Description(),
		}
	case *dynamic.Message:
		if bs, err := rv.MarshalJSONPB(&jsonpb.Marshaler{OrigName: true}); err != nil {
			return nil, err
		} else {
			r = &HttpResponse{
				StatusCode: 0,
				Payload:    bs,
			}
		}
	}
	return json.MarshalIndent(r, "", "")
}

func (m *HexMarshaler) Unmarshal(data []byte, v interface{}) error {
	return m.marshaler.Unmarshal(data, v)
}

func (m *HexMarshaler) NewDecoder(r io.Reader) runtime.Decoder {
	return m.marshaler.NewDecoder(r)
}

func (m *HexMarshaler) NewEncoder(w io.Writer) runtime.Encoder {
	return m.marshaler.NewEncoder(w)
}

func (m *HexMarshaler) ContentType(v interface{}) string {
	return m.marshaler.ContentType(v)
}
