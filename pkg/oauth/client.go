package oauth

import (
	ianvs "gitlab.hexcloud.cn/histore/sales-report/pkg/inavs"
	"net/url"
	"time"

	"github.com/ory/hydra-client-go/client"
)

var (
	hydraClient *client.OryHydra
	timeout     time.Duration
)

func newClient(host, schema string) *client.OryHydra {
	return client.NewHTTPClientWithConfig(nil, &client.TransportConfig{
		Host:     host,
		BasePath: client.DefaultBasePath,
		Schemes:  []string{schema},
	})
}

//Init 根据指定 URL 设置 hydra 客户端
//目前只需要设置 admin 地址即可，需要带 host 及 scheme
func InitIanvs(uri, thirdParty string, tt time.Duration) (err error) {
	var u *url.URL
	if u, err = url.Parse(uri); err != nil {
		return
	}
	hydraClient = newClient(u.Host, u.Scheme)
	if tt > 0 {
		timeout = tt
	}
	if err = ianvs.InitAuthThirdPartyClient(thirdParty); err != nil {
		return
	}
	return nil
}
