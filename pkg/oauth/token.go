package oauth

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/ory/hydra-client-go/client/admin"
	"github.com/spf13/cast"
	"time"
)

func Introspect(ctx context.Context, token string) (user *UserAccount, err error) {
	req := (&admin.IntrospectOAuth2TokenParams{}).
		WithContext(ctx).
		WithToken(token).
		WithTimeout(timeout)
	var resp *admin.IntrospectOAuth2TokenOK
	if resp, err = hydraClient.Admin.IntrospectOAuth2Token(req); err != nil {
		return
	}
	if resp.Payload == nil {
		return nil, resp
	}
	if !*resp.Payload.Active || time.Now().Unix() > resp.Payload.Exp {
		return nil, errors.New("Invalid token: expired.")
	}

	user = &UserAccount{}
	ext, ok := resp.Payload.Ext.(map[string]interface{})
	if !ok {
		return
	}
	if p, ok := ext["partner_id"].(json.Number); ok {
		pid, _ := p.Int64()
		user.PartnerID = uint64(pid)
	} else {
		user.PartnerID = cast.ToUint64(ext["partner_id"])
	}
	if u, ok := ext["id"].(json.Number); ok {
		uid, _ := u.Int64()
		user.ID = uint64(uid)
	} else {
		user.ID = cast.ToUint64(ext["id"])
	}
	user.StoreID = cast.ToUint64(ext["store_id"])
	return
}
