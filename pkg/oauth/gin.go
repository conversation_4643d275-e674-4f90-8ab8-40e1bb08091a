package oauth

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	ianvs "gitlab.hexcloud.cn/histore/sales-report/pkg/inavs"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"hexcloud.cn/histore/hexerror"
	"log"
	"net/http"
	"strings"
	"time"
)

// NewGinMiddleware 服务端拦截器，将设置 partner_id、user_id 到 context 中。
// 如果未包含授权信息，将直接返回错误，不再进行后续操作。
//
// 参数：methodWhiteList，部分方法需要没用户信息也能执行，忽略授权错误。
//                       例如健康检查，可以将方法加入到白名单。（健康检查默认已加入）
//                       支持 Gin 路径匹配写法，参考 *gin.Context(c).FullPath()。
//
// 本地在连接不上授权服务的情况下，推荐使用 NewDebugGrpcServerInterceptor 进行调试，但不允许使用在生产上。
func NewGinMiddleware(methodWhiteList ...string) func(*gin.Context) {
	methodWhiteList = append(methodWhiteList, "/health_check")
	return func(c *gin.Context) {
		for _, method := range methodWhiteList {
			if method == c.Request.RequestURI {
				c.Next()
				return
			}
		}

		// 判断是否boh需要token调用app-intergration接口
		if strings.HasPrefix(c.FullPath(), "/sales-report/api/v1/adjust") {
			token := c.GetHeader("Authorization")
			if token == "" {
				c.AbortWithStatusJSON(200, newAuthErrorResponse(""))
				return
			}
			c.Set(string("Authorization"), token)
		}

		isPos := strings.HasPrefix(c.FullPath(), "/sales-report/api/v1/report/sales/pos")

		//修改租户信息拿取方式----------------wangdong
		pid := c.GetHeader("partner_id")
		uid := c.GetHeader("user_id")
		storeIdStr := c.GetHeader("store_id")
		if uid == "" {
			uid = "1"
		}
		if !isPos && pid != "" {
			user := &UserAccount{}
			logrus.WithField("partner_id", pid).Infof("从header中拉取到partner_id")
			c.Set(string(PartnerID), cast.ToUint64(pid))
			c.Set(string(UserID), cast.ToUint64(uid))
			user.PartnerID = cast.ToUint64(pid)
			user.ID = cast.ToUint64(uid)
			c.Set(string(UserInfo), user)
			c.Next()
			return
		}

		if isPos && pid != "" && storeIdStr != "" {
			user := &UserAccount{}
			logrus.WithField("partner_id", pid).Infof("从header中拉取到partner_id")
			c.Set(string(PartnerID), cast.ToUint64(pid))
			c.Set(string(UserID), cast.ToUint64(uid))
			c.Set(string(StoreID), cast.ToUint64(storeIdStr))
			user.PartnerID = cast.ToUint64(pid)
			user.ID = cast.ToUint64(uid)
			user.StoreID = cast.ToUint64(storeIdStr)
			c.Set(string(UserInfo), user)
			c.Next()
			return
		}
		//----------------------------------********
		var token string
		if token = c.GetHeader("Authorization"); len(token) > len(Bearer) {
			logger.Pre().Infoln("http using token:", token, "Introspect user")
			token = token[len(Bearer):]
			user, err := Introspect(c, token)
			if err != nil {
				logger.Pre().Infoln("new oauth err:", err.Error())
				c.AbortWithStatusJSON(200, newAuthErrorResponse(err.Error()))
				return
			}
			c.Set(string(PartnerID), user.PartnerID)
			c.Set(string(UserID), user.ID)
			c.Set(string(UserInfo), user)
			c.Set(string("Authorization"), c.GetHeader("Authorization"))
			c.Next()
		} else if token = c.GetHeader("app-secret"); len(token) > len(Bearer) {
			logger.Pre().Infoln("http using token:", token, "Introspect third party user")
			token = token[len(Bearer):]
			user, err := ianvs.IntrospectAuthThirdParty(c, token)
			if err != nil {
				logger.Pre().Infoln("new oauth err:", err.Error())
				c.AbortWithStatusJSON(200, newAuthErrorResponse(err.Error()))
				return
			}
			c.Set(string(PartnerID), user.PartnerID)
			// 防止别的系统校验userId不为0
			if user.ID == 0 {
				user.ID = 104
			}
			c.Set(string(UserID), user.ID)
			c.Set(string(UserInfo), &UserAccount{
				ID:        user.ID,
				PartnerID: user.PartnerID,
			})
			c.Next()
		} else {
			c.AbortWithStatusJSON(200, newAuthErrorResponse(""))
			return
		}
	}
}

// NewDebugGinMiddleware 调试使用的 Gin 服务端拦截器。
// 按正常方式取不到用户信息时，将使用用户传入的默认用户信息。
// ！！！！
// 生产不允许使用本方法，仅供本地连接不上授权服务时调试使用。
// 生产须使用 NewServerInterceptorAutoReject
// ！！！！
func NewDebugGinMiddleware(pID, uID uint64) func(*gin.Context) {
	return func(c *gin.Context) {
		var pid, uid uint64
		if token := c.GetHeader("Authorization"); len(token) > len(Bearer) {
			token = token[len(Bearer):]
			if user, err := Introspect(c, token); err == nil {
				pid = user.PartnerID
				uid = user.ID
			}
		}
		if pid == 0 || uid == 0 {
			pid = pID
			uid = uID
			log.Printf("使用调试数据：partner_id(%d), user_id(%d)，生产不允许此模式，可能会带来 panic.", pID, uID)
		}
		c.Set(string(PartnerID), pid)
		c.Set(string(UserID), uid)
		c.Set(string(UserInfo), &UserAccount{PartnerID: pid, ID: uid})
		c.Next()
	}
}

// WrapperAPI 对请求的结果进行包装。
// 错误将转换成特定的格式，状态码还是返回 200。
// 正确的结果也会转换。
func WrapAPI(viewFunc func(*gin.Context) (interface{}, hexerror.HexError)) func(c *gin.Context) {
	wrapperFunc := func(c *gin.Context) {
		st := time.Now()
		result, err := viewFunc(c)
		duration := time.Since(st)
		c.Header("Duration", fmt.Sprintf("%dms", duration.Milliseconds()))
		if err != nil {
			c.JSON(http.StatusOK, error2Response(err))
		} else {
			c.JSON(http.StatusOK, gin.H{
				"status_code": 0,
				"payload":     result,
			})
		}
	}
	return wrapperFunc
}

// WrapError 对于只返回错误的接口进行包装。
// 错误将转换成特定的格式，状态码还是返回 200。
func WrapError(viewFunc func(*gin.Context) hexerror.HexError) func(c *gin.Context) {
	wrapperFunc := func(c *gin.Context) {
		st := time.Now()
		e := viewFunc(c)
		duration := time.Since(st)
		c.Header("Duration", fmt.Sprintf("%dms", duration.Milliseconds()))
		if e == nil {
			c.JSON(http.StatusOK, gin.H{
				"status_code": 0,
				"payload":     "",
			})
		} else {
			c.JSON(http.StatusOK, error2Response(e))
		}
	}
	return wrapperFunc
}

func error2Response(e hexerror.HexError) *HttpResponse {
	return &HttpResponse{
		StatusCode:  1,
		Code:        e.Code(),
		Source:      e.Source(),
		Detail:      e.Detail(),
		Description: e.Description(),
	}
}

func newAuthErrorResponse(desc string) map[string]interface{} {
	if desc == "" {
		desc = NoAuth.Description()
	}
	return map[string]interface{}{
		"status_code": 1,
		"code":        NoAuth.Code(),
		"description": desc,
	}
}
