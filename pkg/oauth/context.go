package oauth

import (
	"context"
)

// GetUserinfo 获取用户信息，
// 包含用户不存在的错误，如果有错误，应当给客户端返回错误响应，不再继续执行下去
func GetUserinfo(c context.Context) (*UserAccount, error) {
	if u := c.Value(UserInfo); u != nil {
		if ru, ok := u.(*UserAccount); ok {
			return ru, nil
		}
	}
	return nil, NoAuth
}

// GetUserinfo 获取 pid 和 uid，
// 包含不存在的错误，如果有错误，应当给客户端返回错误响应，不再继续执行下去。
func GetPidAndUid(c context.Context) (pid, uid uint64, err error) {
	var ok bool
	p := c.Value(PartnerID)
	u := c.Value(UserID)
	if p == nil || u == nil {
		return 0, 0, NoAuth
	}

	if pid, ok = p.(uint64); !ok {
		return 0, 0, NoAuth
	}
	if uid, ok = u.(uint64); !ok {
		return 0, 0, <PERSON>A<PERSON>
	}
	return
}
