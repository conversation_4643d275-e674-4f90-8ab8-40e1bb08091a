package oauth

// max 10 char
type State string

const (
	Draft  State = "DRAFT"  // 新建未通过
	Normal State = "NORMAL" // 正常
	Lock   State = "LOCK"   // 锁定
	Delete State = "DELETE" // 被删除
)

type Language string

const (
	Zh_CN Language = "zh_CN" // 中文
	En_US Language = "en_US" // 英文
)

type ThreePartyCategory string

const (
	DingTalk ThreePartyCategory = "dingding" // 企业钉钉
)

type OAuthConst = string

const (
	PartnerID OAuthConst = "partner_id"
	StoreID   OAuthConst = "store_id"
	UserID    OAuthConst = "user_id"
	UserInfo  OAuthConst = "user_info"
	Bearer    OAuthConst = "Bearer "
)
