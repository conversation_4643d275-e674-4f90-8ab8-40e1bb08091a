package apollo

import (
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	"os"
	"os/signal"
	"testing"
)

func TestApollo(t *testing.T) {
	logger.InitLogger("info", "prod")
	config.DefaultConfig.Apollo = config.Apollo{
		AppID:          "sales-report",
		Cluster:        "default",
		Url:            "https://apolloconfig-qa.hexcloud.cn",
		NamespaceName:  "sales-report.yml",
		IsBackupConfig: true,
		Secret:         "12b1cc31d20141fbacb76443707276b1",
	}
	InitApollo()
	flag := make(chan os.Signal)
	signal.Notify(flag, os.Interrupt, os.Kill)
	<-flag
}
