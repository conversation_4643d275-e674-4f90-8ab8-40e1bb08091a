package apollo

import (
	"fmt"
	"github.com/apolloconfig/agollo/v4"
	apollo_config "github.com/apolloconfig/agollo/v4/env/config"
	"github.com/apolloconfig/agollo/v4/storage"
	"gitlab.hexcloud.cn/histore/sales-report/config"
)

type apolloClient struct {
	config *apollo_config.AppConfig
	Client agollo.Client
}

var ClientApollo *apolloClient

func InitApollo() {

	ClientApollo = new(apolloClient)

	c := &apollo_config.AppConfig{
		AppID:          config.DefaultConfig.Apollo.AppID,
		Cluster:        config.DefaultConfig.Apollo.Cluster,
		IP:             config.DefaultConfig.Apollo.Url,
		NamespaceName:  config.DefaultConfig.Apollo.NamespaceName,
		IsBackupConfig: config.DefaultConfig.Apollo.IsBackupConfig,
		Secret:         config.DefaultConfig.Apollo.Secret,
	}

	client, err := agollo.StartWithConfig(func() (*apollo_config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		fmt.Println("初始化Apollo配置失败", err)
		return
	}

	fmt.Println("初始化Apollo配置成功")

	ClientApollo.Client = client
	ClientApollo.config = c

	// 添加事件监听
	ClientApollo.Client.AddChangeListener(new(ApolloChangeListner))

	// 初始加载配置
	LoadApolloConfig()
}

type ApolloChangeListner struct {
}

func (r *ApolloChangeListner) OnChange(event *storage.ChangeEvent) {
	//fmt.Printf("OnChange Apollo配置变更：%v", event)
}
func (r *ApolloChangeListner) OnNewestChange(event *storage.FullChangeEvent) {
	fmt.Printf("OnNewestChange Apollo配置变更：%v\n", event)
	LoadApolloConfig()
}
