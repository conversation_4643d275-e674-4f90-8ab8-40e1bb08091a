package apollo

import (
	"context"
	"encoding/json"
	"github.com/spf13/cast"
	"gitlab.hexcloud.cn/histore/sales-report/config"
	"gitlab.hexcloud.cn/histore/sales-report/pkg/logger"
	entity "gitlab.hexcloud.cn/histore/sales-report/pkg/metadata"
)

type startUp struct {
	PartnerIDs []string `json:"partner_ids"`
	ScopeID    string   `json:"scope_id"`
	UserID     string   `json:"user_id"`
}

func LoadApolloConfig() {
	c := ClientApollo.Client.GetConfigAndInit(config.DefaultConfig.Apollo.NamespaceName)
	logger.Pre().Infof("LoadApolloConfig Start: \n`%s`", c.GetContent())
	loadDb(c)
	enabled := c.GetSliceValue("boh.enabled", nil)
	config.BohEnabled = make(map[int64]bool, len(enabled))
	for _, s := range enabled {
		config.BohEnabled[cast.ToInt64(s)] = true
	}
	disabled := c.GetSliceValue("boh.disabled", nil)
	config.BohDisabled = make(map[int64]bool, len(disabled))
	for _, s := range disabled {
		config.BohDisabled[cast.ToInt64(s)] = true
	}
	enableIncludeRealAmountZero := c.GetSliceValue("enable_include_real_amount_zero", nil)
	config.EnableIncludeRealAmountZero = make(map[int64]bool, len(enableIncludeRealAmountZero))
	for _, s := range enableIncludeRealAmountZero {
		config.EnableIncludeRealAmountZero[cast.ToInt64(s)] = true
	}
	config.SpicyCode = c.GetStringValue("spicy.code", "")
	SpicyValuesJson := c.GetStringValue("spicy.values", "[]")
	var spicyValues []string
	err := json.Unmarshal([]byte(SpicyValuesJson), &spicyValues)
	if err != nil {
		logger.Pre().Errorf("SpicyValuesJson Unmarshal Error: %v", err)
	}
	config.SpicyValues = make([]string, 0)
	config.SpicyMap = make(map[string]string)
	for i := 0; i < len(spicyValues); i += 2 {
		config.SpicyMap[spicyValues[i]] = spicyValues[i+1]
		config.SpicyValues = append(config.SpicyValues, spicyValues[i])
	}
	startUpVal := startUp{}
	startUpJson := c.GetStringValue("start_up", "{}")
	err = json.Unmarshal([]byte(startUpJson), &startUpVal)
	if err != nil {
		logger.Pre().Errorf("StartUpJson Unmarshal Error: %v", err)
	} else {
		entity.RefreshStartUpInfo(context.Background(), startUpVal.PartnerIDs, startUpVal.ScopeID, startUpVal.UserID)
	}

	// 餐段顺序
	config.MealSegmentSort = c.GetStringSliceValue("meal_segment_sort", config.MealSegmentSort)

	logger.Pre().Infof("config.SpicyCode: `%s`\n", config.SpicyCode)
	logger.Pre().Infof("config.SpicyValues: `%v`\n", config.SpicyValues)
	logger.Pre().Infof("config.SpicyMap: `%v`\n", config.SpicyMap)

	logger.Pre().Infof("LoadApolloConfig End: \n`%s`", c.GetContent())
}
