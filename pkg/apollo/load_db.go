package apollo

import (
	"github.com/apolloconfig/agollo/v4/storage"
	"gitlab.hexcloud.cn/histore/sales-report/db"
)

func loadDb(c *storage.Config) {
	allDb := c.GetStringValue("db.all", "")
	if allDb != "" {
		db.SwitchDBFromName(db.All, allDb)
	} else {
		serviceNames := db.GetServiceNames()
		for _, serviceName := range serviceNames {
			dbName := c.GetStringValue("db.service." + serviceName, "")
			if dbName != "" {
				db.SwitchDBFromName(serviceName, dbName)
			}
		}
	}
}
