// Auto generate code. Help should mail to ['<EMAIL>']
package logger

import (
	"github.com/sirupsen/logrus"
)

var me *logrus.Entry

func InitLogger(logLevel string, env string) {
	logEntry := logrus.New()
	//logEntry.Level = logrus.WarnLevel
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		logrus.WithError(err).Error("Error parsing log level, using: info")
		level = logrus.InfoLevel
	}
	logEntry.Level = level
	Formatter := new(logrus.TextFormatter)
	Formatter.TimestampFormat = "20060102T15:04:05.999"
	Formatter.FullTimestamp = true
	logEntry.SetFormatter(Formatter)
	me = logrus.NewEntry(logEntry).WithField("env", env)
}

// Pre is used to get a prepared logrus.Entry
func Pre() *logrus.Entry {
	return me
}
